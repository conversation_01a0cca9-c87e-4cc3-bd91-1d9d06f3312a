import { FullConfig, chromium } from '@playwright/test';
import { smartRunner } from './smart-test-runner';
import { AuthHelpers } from './auth-helpers';
import { setupTestEnv } from './setup-test-env';
import path from 'path';

async function globalSetup(config: FullConfig) {
  console.log('🎭 Setting up E2E tests with smart selection...');
  
  // Setup test environment
  setupTestEnv();
  
  // Initialize smart test runner
  const allTests = [
    { name: 'Import conversation flow - happy path', file: 'critical-flows.spec.ts' },
    { name: 'Homepage loads correctly', file: 'critical-flows.spec.ts' },
    { name: 'Import page loads and form works', file: 'critical-flows.spec.ts' },
    { name: 'Import page loads within performance budget', file: 'critical-flows.spec.ts' },
    { name: 'Error handling works correctly', file: 'critical-flows.spec.ts' },
    { name: 'Mobile responsive design works', file: 'critical-flows.spec.ts' },
    { name: 'End-to-end: Login → AI Import (ChatGPT) → Edit → Save Trip', file: 'full-journey.spec.ts' },
    { name: 'End-to-end: PDF Import → Preview → Create Trip', file: 'full-journey.spec.ts' },
    { name: 'Error handling: Invalid conversation format', file: 'full-journey.spec.ts' },
    { name: 'Multiple platform imports: Claude and Gemini', file: 'full-journey.spec.ts' },
    { name: 'Direct API call: AI conversation parsing', file: 'full-journey.spec.ts' },
    { name: 'Direct API call: PDF upload', file: 'full-journey.spec.ts' },
  ];

  const selectedTests = smartRunner.getPrioritizedTests(allTests);
  
  console.log(`📊 Smart selection: ${selectedTests.length}/${allTests.length} tests will run`);
  
  selectedTests.forEach((test, index) => {
    console.log(`  ${index + 1}. ${test.name} (${test.priority})`);
  });

  if (selectedTests.length < allTests.length) {
    const skipped = allTests.length - selectedTests.length;
    console.log(`⏭️  Skipping ${skipped} stable tests`);
  }

  // Set environment variable for test selection
  process.env.SELECTED_TESTS = JSON.stringify(selectedTests.map(t => t.name));
  
  // Setup authentication state if needed
  console.log('🔐 Setting up authentication state...');
  try {
    // Create a browser context to save auth state
    const browser = await chromium.launch();
    const context = await browser.newContext();
    const page = await context.newPage();
    
    // Perform authentication
    await AuthHelpers.setupAuthenticatedState(page);
    
    // Save storage state for reuse in tests
    const storageState = await context.storageState();
    await context.close();
    await browser.close();
    
    // Save to a file that tests can use
    const authFile = path.join(__dirname, '.auth', 'user.json');
    require('fs').mkdirSync(path.dirname(authFile), { recursive: true });
    require('fs').writeFileSync(authFile, JSON.stringify(storageState));
    
    console.log('✅ Authentication state saved');
  } catch (error) {
    console.error('⚠️ Failed to setup auth state:', error);
    console.log('Tests will handle authentication individually');
  }
  
  return Promise.resolve();
}

export default globalSetup;