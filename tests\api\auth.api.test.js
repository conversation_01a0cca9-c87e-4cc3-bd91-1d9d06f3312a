/**
 * Authentication API Tests
 * 
 * Perfect foundation test that demonstrates:
 * - Real login/signup flow with .env.local credentials
 * - Token persistence and reuse across tests
 * - Automatic token refresh when expired
 * - Clean token cleanup and renewal
 */

const ApiTestClient = require('./utils/api-client');
const apiConfig = require('./api.config');

// Test results tracking
const testResults = {
  passed: 0,
  failed: 0,
  tests: []
};

function logTest(name, result, details = {}) {
  const passed = result.success || result === true;
  testResults.tests.push({ name, passed, details });
  
  if (passed) {
    testResults.passed++;
    console.log(`✅ ${name}`);
  } else {
    testResults.failed++;
    console.log(`❌ ${name}`, details.error || result.error || '');
  }
}

async function testHealthCheck(client) {
  console.log('\n🏥 Testing Health Check...');
  
  try {
    const response = await client.healthCheck();
    logTest('Health Check', response, response);
    return response.success;
  } catch (error) {
    logTest('Health Check', false, { error: error.message });
    return false;
  }
}

async function testLogin(client) {
  console.log('\n🔐 Testing Login Flow...');
  
  try {
    // Clear any existing tokens first
    client.clearTokens();
    
    const response = await client.login(
      apiConfig.testData.user.email,
      apiConfig.testData.user.password
    );
    
    logTest('Login with valid credentials', response, response);
    
    if (response.success) {
      // Verify token was saved
      const token = client.getStoredToken();
      logTest('Token saved after login', !!token, { hasToken: !!token });
      
      // Verify user data is available
      const userData = client.tokens.user;
      logTest('User data saved', !!userData, { userData });
      
      return true;
    }
    
    return false;
  } catch (error) {
    logTest('Login Flow', false, { error: error.message });
    return false;
  }
}

async function testLoginWithInvalidCredentials(client) {
  console.log('\n🚫 Testing Login with Invalid Credentials...');
  
  try {
    const response = await client.login('<EMAIL>', 'wrongpassword');
    
    // Should fail
    logTest('Login with invalid credentials fails', !response.success, response);
    
    // Should not save token
    const token = client.getStoredToken();
    logTest('No token saved for invalid login', !token, { hasToken: !!token });
    
    return true;
  } catch (error) {
    logTest('Invalid Login Test', false, { error: error.message });
    return false;
  }
}

async function testGetMe(client) {
  console.log('\n👤 Testing Get Current User...');
  
  try {
    const response = await client.getMe();
    
    logTest('Get current user info', response, response);
    
    if (response.success) {
      const user = response.data.data;
      logTest('User email matches', user.email === apiConfig.testData.user.email, { 
        expected: apiConfig.testData.user.email,
        actual: user.email 
      });
      
      return true;
    }
    
    return false;
  } catch (error) {
    logTest('Get Me Test', false, { error: error.message });
    return false;
  }
}

async function testTokenReuse(client) {
  console.log('\n🔄 Testing Token Reuse...');
  
  try {
    // Make multiple authenticated requests to verify token reuse
    const response1 = await client.getMe();
    const response2 = await client.getMe();
    const response3 = await client.getMe();
    
    logTest('First authenticated request', response1, response1);
    logTest('Second authenticated request (token reuse)', response2, response2);
    logTest('Third authenticated request (token reuse)', response3, response3);
    
    // All should succeed with the same token
    const allSucceeded = response1.success && response2.success && response3.success;
    logTest('All requests succeeded with reused token', allSucceeded, {
      results: [response1.success, response2.success, response3.success]
    });
    
    return allSucceeded;
  } catch (error) {
    logTest('Token Reuse Test', false, { error: error.message });
    return false;
  }
}

async function testTokenRefresh(client) {
  console.log('\n🔄 Testing Token Refresh...');
  
  try {
    // Check if we have a refresh token
    if (!client.tokens.refreshToken) {
      console.log('ℹ️  No refresh token available - skipping refresh test');
      return true;
    }
    
    // Manually expire the token to test refresh
    const originalExpiresAt = client.tokens.expiresAt;
    client.tokens.expiresAt = Date.now() - 1000; // Expire 1 second ago
    
    // Make an authenticated request - should trigger refresh
    const response = await client.getMe();
    
    logTest('Request with expired token triggers refresh', response, response);
    
    // Verify token was refreshed
    const newExpiresAt = client.tokens.expiresAt;
    logTest('Token expiry time updated', newExpiresAt > originalExpiresAt, {
      original: originalExpiresAt,
      new: newExpiresAt
    });
    
    return response.success;
  } catch (error) {
    logTest('Token Refresh Test', false, { error: error.message });
    return false;
  }
}

async function testLogout(client) {
  console.log('\n🚪 Testing Logout...');
  
  try {
    const response = await client.logout();
    
    logTest('Logout request', response, response);
    
    // Verify tokens were cleared
    const token = client.getStoredToken();
    logTest('Tokens cleared after logout', !token, { hasToken: !!token });
    
    // Verify subsequent authenticated requests fail
    const meResponse = await client.getMe();
    logTest('Authenticated requests fail after logout', !meResponse.success, meResponse);
    
    return true;
  } catch (error) {
    logTest('Logout Test', false, { error: error.message });
    return false;
  }
}

async function testReloginAfterLogout(client) {
  console.log('\n🔄 Testing Re-login After Logout...');
  
  try {
    // Login again after logout
    const loginResponse = await client.login(
      apiConfig.testData.user.email,
      apiConfig.testData.user.password
    );
    
    logTest('Re-login after logout', loginResponse, loginResponse);
    
    if (loginResponse.success) {
      // Verify we can make authenticated requests again
      const meResponse = await client.getMe();
      logTest('Authenticated requests work after re-login', meResponse, meResponse);
      
      return meResponse.success;
    }
    
    return false;
  } catch (error) {
    logTest('Re-login Test', false, { error: error.message });
    return false;
  }
}

async function runTests() {
  console.log('🚀 Starting Authentication API Tests');
  console.log('====================================');
  console.log(`API Base URL: ${apiConfig.baseUrl}`);
  console.log(`Test User: ${apiConfig.testData.user.email}\n`);

  const client = new ApiTestClient();
  
  try {
    // Run all authentication tests in sequence
    const tests = [
      () => testHealthCheck(client),
      () => testLogin(client),
      () => testLoginWithInvalidCredentials(client),
      () => testGetMe(client),
      () => testTokenReuse(client),
      () => testTokenRefresh(client),
      () => testLogout(client),
      () => testReloginAfterLogout(client),
    ];

    for (const test of tests) {
      await test();
    }
    
  } catch (error) {
    console.error('\n💥 Test execution error:', error.message);
    testResults.failed++;
  }

  // Summary
  console.log('\n====================================');
  console.log('📊 Authentication Test Results');
  console.log('====================================');
  console.log(`✅ Passed: ${testResults.passed}`);
  console.log(`❌ Failed: ${testResults.failed}`);
  console.log(`📋 Total: ${testResults.tests.length}`);
  console.log(`📈 Success Rate: ${Math.round((testResults.passed / testResults.tests.length) * 100)}%`);

  // Detailed failures
  if (testResults.failed > 0) {
    console.log('\n❌ Failed Tests:');
    testResults.tests
      .filter(t => !t.passed)
      .forEach(t => {
        console.log(`\n- ${t.name}`);
        if (t.details.error) {
          console.log('  Error:', t.details.error);
        }
      });
  }

  return testResults;
}

// Export for use by test runner
module.exports = { runTests };

// Run directly if called as script
if (require.main === module) {
  runTests().catch(console.error);
}