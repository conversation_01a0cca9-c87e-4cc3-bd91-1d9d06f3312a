import request from 'supertest';
import { vi, describe, it, expect, beforeEach, afterEach } from 'vitest';
import express from 'express';
import { Router } from 'express';
import { createSuccessResponse, createErrorResponse } from '@travelviz/shared';

describe('Places Routes', () => {
  let app: express.Application;
  let mockPlacesService: any;

  beforeEach(() => {
    app = express();
    app.use(express.json());
    
    // Create mock service
    mockPlacesService = {
      searchPlaces: vi.fn(),
      getPlaceDetails: vi.fn(),
    };

    // Create routes manually to avoid module loading issues
    const router = Router();
    
    // GET /api/places/autocomplete
    router.get('/autocomplete', async (req, res) => {
      try {
        const query = typeof req.query.query === 'string' ? req.query.query.trim() : '';

        if (!query) {
          return res.status(400).json(createErrorResponse('Query parameter is required'));
        }

        const result = await mockPlacesService.searchPlaces(query);
        res.json(createSuccessResponse(result));
      } catch (error) {
        console.error('Places autocomplete error:', error);
        res.status(500).json(createErrorResponse('Failed to search places'));
      }
    });

    // GET /api/places/:placeId
    router.get('/:placeId', async (req, res) => {
      try {
        const { placeId } = req.params;

        const result = await mockPlacesService.getPlaceDetails(placeId);
        res.json(createSuccessResponse(result));
      } catch (error) {
        console.error('Place details error:', error);
        res.status(500).json(createErrorResponse('Failed to get place details'));
      }
    });
    
    app.use('/api/places', router);

    vi.clearAllMocks();
  });

  describe('GET /api/places/autocomplete', () => {
    it('should return autocomplete suggestions for valid query', async () => {
      // Arrange
      const mockSuggestions = {
        suggestions: [
          {
            description: 'Eiffel Tower, Paris, France',
            place_id: 'ChIJ123',
            main_text: 'Eiffel Tower',
            secondary_text: 'Paris, France',
          },
        ],
      };

      mockPlacesService.searchPlaces.mockResolvedValue(mockSuggestions);

      // Act
      const response = await request(app)
        .get('/api/places/autocomplete')
        .query({ query: 'Eiffel Tower' });

      // Assert
      expect(response.status).toBe(200);
      expect(response.body).toEqual({
        success: true,
        data: mockSuggestions,
      });
      expect(mockPlacesService.searchPlaces).toHaveBeenCalledWith('Eiffel Tower');
    });

    it('should return 400 for missing query parameter', async () => {
      // Act
      const response = await request(app)
        .get('/api/places/autocomplete');

      // Assert
      expect(response.status).toBe(400);
      expect(response.body).toEqual({
        success: false,
        error: 'Query parameter is required',
      });
    });

    it('should return 400 for empty query parameter', async () => {
      // Act
      const response = await request(app)
        .get('/api/places/autocomplete')
        .query({ query: '' });

      // Assert
      expect(response.status).toBe(400);
      expect(response.body).toEqual({
        success: false,
        error: 'Query parameter is required',
      });
    });

    it('should handle service errors gracefully', async () => {
      // Arrange
      mockPlacesService.searchPlaces.mockRejectedValue(
        new Error('Google API error')
      );

      // Act
      const response = await request(app)
        .get('/api/places/autocomplete')
        .query({ query: 'test' });

      // Assert
      expect(response.status).toBe(500);
      expect(response.body).toEqual({
        success: false,
        error: 'Failed to search places',
      });
    });

    it('should trim whitespace from query parameter', async () => {
      // Arrange
      const mockSuggestions = { suggestions: [] };
      mockPlacesService.searchPlaces.mockResolvedValue(mockSuggestions);

      // Act
      const response = await request(app)
        .get('/api/places/autocomplete')
        .query({ query: '  test query  ' });

      // Assert
      expect(response.status).toBe(200);
      expect(mockPlacesService.searchPlaces).toHaveBeenCalledWith('test query');
    });
  });

  describe('GET /api/places/:placeId', () => {
    it('should return place details for valid place ID', async () => {
      // Arrange
      const placeId = 'ChIJ123';
      const mockPlaceDetails = {
        place_id: placeId,
        name: 'Eiffel Tower',
        formatted_address: 'Paris, France',
        location_lat: 48.8584,
        location_lng: 2.2945,
        types: ['tourist_attraction'],
        rating: 4.6,
      };

      mockPlacesService.getPlaceDetails.mockResolvedValue(mockPlaceDetails);

      // Act
      const response = await request(app)
        .get(`/api/places/${placeId}`);

      // Assert
      expect(response.status).toBe(200);
      expect(response.body).toEqual({
        success: true,
        data: mockPlaceDetails,
      });
      expect(mockPlacesService.getPlaceDetails).toHaveBeenCalledWith(placeId);
    });

    it('should handle invalid place ID gracefully', async () => {
      // Arrange
      const placeId = 'invalid';
      mockPlacesService.getPlaceDetails.mockRejectedValue(
        new Error('Invalid place ID')
      );

      // Act
      const response = await request(app)
        .get(`/api/places/${placeId}`);

      // Assert
      expect(response.status).toBe(500);
      expect(response.body).toEqual({
        success: false,
        error: 'Failed to get place details',
      });
    });
  });
});