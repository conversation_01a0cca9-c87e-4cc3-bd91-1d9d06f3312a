{"compilerOptions": {"target": "ES2020", "lib": ["dom", "dom.iterable", "ES2020"], "allowJs": true, "skipLibCheck": true, "noUnusedLocals": false, "noUnusedParameters": false, "strict": true, "forceConsistentCasingInFileNames": true, "noImplicitAny": true, "noImplicitReturns": true, "exactOptionalPropertyTypes": false, "noEmit": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true, "composite": true, "plugins": [{"name": "next"}], "baseUrl": ".", "paths": {"@/*": ["./*", "./src/*"], "@/app/*": ["./app/*"], "@/components/*": ["./components/*", "./src/components/*"], "@/lib/*": ["./lib/*", "./src/lib/*"], "@/hooks/*": ["./hooks/*", "./src/hooks/*"], "@/stores/*": ["./stores/*"], "@/providers/*": ["./src/providers/*"], "@/contexts/*": ["./src/contexts/*"]}}, "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts"], "exclude": ["node_modules"], "references": [{"path": "../shared"}]}