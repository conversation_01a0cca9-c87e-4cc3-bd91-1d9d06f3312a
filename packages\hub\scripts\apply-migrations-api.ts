import { readFileSync } from 'fs';
import { join } from 'path';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config({ path: '.env.local' });

const SUPABASE_URL = process.env.SUPABASE_URL;
const SUPABASE_ACCESS_TOKEN = process.env.SUPABASE_ACCESS_TOKEN;

if (!SUPABASE_URL || !SUPABASE_ACCESS_TOKEN) {
  console.error('Missing Supabase credentials');
  process.exit(1);
}

// Extract project ref from URL
const projectRef = SUPABASE_URL.match(/https:\/\/([^.]+)\.supabase\.co/)?.[1];

if (!projectRef) {
  console.error('Could not extract project ref from Supabase URL');
  process.exit(1);
}

async function runSQL(sql: string) {
  const response = await fetch(`https://api.supabase.com/v1/projects/${projectRef}/database/query`, {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${SUPABASE_ACCESS_TOKEN}`,
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({ query: sql })
  });

  if (!response.ok) {
    const error = await response.text();
    throw new Error(`API Error: ${response.status} - ${error}`);
  }

  return response.json();
}

async function applyMigrations() {
  console.log('🚀 Applying database migrations via Supabase Management API...\n');
  console.log(`Project: ${projectRef}\n`);

  const migrations = [
    '001_initial_schema.sql',
    '002_row_level_security.sql', 
    '20250105_account_lockout.sql'
  ];

  for (const migrationFile of migrations) {
    console.log(`📝 Processing migration: ${migrationFile}`);
    
    try {
      const migrationPath = join(__dirname, '..', 'supabase', 'migrations', migrationFile);
      const sql = readFileSync(migrationPath, 'utf8');
      
      // Remove comments and split by semicolons
      const statements = sql
        .split('\n')
        .filter(line => !line.trim().startsWith('--'))
        .join('\n')
        .split(/;\s*(?=\S)/)
        .map(s => s.trim())
        .filter(s => s.length > 0);
      
      console.log(`   Found ${statements.length} SQL statements`);
      
      // Apply each statement
      for (let i = 0; i < statements.length; i++) {
        const statement = statements[i];
        if (!statement.endsWith(';')) {
          statements[i] = statement + ';';
        }
        
        console.log(`   Executing statement ${i + 1}/${statements.length}...`);
        
        try {
          await runSQL(statements[i]);
          console.log(`   ✅ Statement ${i + 1} executed successfully`);
        } catch (error: any) {
          console.error(`   ❌ Statement ${i + 1} failed:`, error.message);
          // Continue with next statement
        }
      }
      
      console.log(`✅ Migration ${migrationFile} processed\n`);
    } catch (error) {
      console.error(`❌ Failed to process ${migrationFile}:`, error);
      console.error('Continuing with next migration...\n');
    }
  }
  
  console.log('✨ Migration process completed!');
}

// Alternative: Manual SQL execution instructions
console.log('========================================');
console.log('ALTERNATIVE: Manual Migration Steps');
console.log('========================================\n');
console.log('If the automated migration fails, you can apply migrations manually:');
console.log('\n1. Go to your Supabase Dashboard:');
console.log(`   https://app.supabase.com/project/${projectRef}/editor`);
console.log('\n2. Open the SQL Editor');
console.log('\n3. Copy and paste each migration file content from:');
console.log('   packages/hub/supabase/migrations/');
console.log('\n4. Execute each migration in order:');
console.log('   - 001_initial_schema.sql');
console.log('   - 002_row_level_security.sql');
console.log('   - 20250105_account_lockout.sql');
console.log('\n========================================\n');

// Try automated approach
applyMigrations().catch(error => {
  console.error('\n❌ Automated migration failed:', error.message);
  console.log('\nPlease follow the manual steps above to apply migrations.');
});