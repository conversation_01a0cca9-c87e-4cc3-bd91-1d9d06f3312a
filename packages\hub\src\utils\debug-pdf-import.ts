/**
 * PDF Import Debugging Utilities
 * Systematic evidence collection for session 2479b9e7-4336-4b9c-9447-78f747ae26be
 */

import { getSupabaseClient } from '../lib/supabase';
import { redis } from '../config/redis';
import { logger } from './logger';
import { cacheService } from '../services/cache.service';

export interface DebuggingEvidence {
  sessionId: string;
  timestamp: Date;
  phase: DebugPhase;
  findings: Finding[];
  rawData: Record<string, any>;
}

export interface Finding {
  type: 'database' | 'cache' | 'api' | 'processing' | 'frontend';
  description: string;
  evidence: any;
  severity: 'critical' | 'warning' | 'info';
}

export enum DebugPhase {
  SESSION_STATE = 'session_state',
  API_CHAIN = 'api_chain',
  AI_PROCESSING = 'ai_processing',
  STATUS_UPDATE = 'status_update',
  FRONTEND_ERROR = 'frontend_error',
  ROOT_CAUSE_FIX = 'root_cause_fix',
  VALIDATION = 'validation'
}

export interface SessionStateInvestigation {
  databaseQuery: string;
  cacheQuery: string;
  expectedState: ImportStatus;
  actualState: ImportStatus;
  discrepancies: string[];
}

export interface ImportStatus {
  sessionId: string;
  status: 'pending' | 'processing' | 'complete' | 'failed';
  createdAt: Date;
  updatedAt: Date;
  parsedData?: any;
  errorMessage?: string;
}

/**
 * Debug Session Management
 */
export class PDFImportDebugger {
  private targetSessionId = '2479b9e7-4336-4b9c-9447-78f747ae26be';
  private evidence: DebuggingEvidence[] = [];

  constructor() {
    logger.info('PDFImportDebugger initialized', { 
      targetSessionId: this.targetSessionId,
      timestamp: new Date().toISOString()
    });
  }

  /**
   * Add evidence to the debugging session
   */
  private addEvidence(phase: DebugPhase, findings: Finding[], rawData: Record<string, any> = {}): void {
    const evidence: DebuggingEvidence = {
      sessionId: this.targetSessionId,
      timestamp: new Date(),
      phase,
      findings,
      rawData
    };

    this.evidence.push(evidence);
    
    logger.info('Debug evidence collected', {
      phase,
      findingsCount: findings.length,
      criticalFindings: findings.filter(f => f.severity === 'critical').length
    });
  }

  /**
   * Get all collected evidence
   */
  getEvidence(): DebuggingEvidence[] {
    return this.evidence;
  }

  /**
   * Generate debugging report
   */
  generateReport(): string {
    const report = {
      targetSession: this.targetSessionId,
      investigationStarted: this.evidence[0]?.timestamp,
      investigationCompleted: new Date(),
      phases: this.evidence.map(e => ({
        phase: e.phase,
        timestamp: e.timestamp,
        findings: e.findings.length,
        criticalIssues: e.findings.filter(f => f.severity === 'critical').length
      })),
      totalFindings: this.evidence.reduce((sum, e) => sum + e.findings.length, 0),
      criticalIssues: this.evidence.reduce((sum, e) => sum + e.findings.filter(f => f.severity === 'critical').length, 0)
    };

    return JSON.stringify(report, null, 2);
  }

  /**
   * Phase 1: Session State Verification
   * Requirement 1: Verify exact database and cache state
   */
  async investigateSessionState(): Promise<SessionStateInvestigation> {
    logger.info('Starting session state investigation', { sessionId: this.targetSessionId });

    const findings: Finding[] = [];
    let investigation: SessionStateInvestigation;

    try {
      // 1. Query database for session details
      const { data: dbSession, error: dbError } = await getSupabaseClient()
        .from('ai_import_logs')
        .select('*')
        .eq('id', this.targetSessionId)
        .single();

      if (dbError) {
        findings.push({
          type: 'database',
          description: 'Failed to query ai_import_logs table',
          evidence: { error: dbError.message, code: dbError.code },
          severity: 'critical'
        });
        throw new Error(`Database query failed: ${dbError.message}`);
      }

      if (!dbSession) {
        findings.push({
          type: 'database',
          description: 'Session not found in database',
          evidence: { sessionId: this.targetSessionId },
          severity: 'critical'
        });
        throw new Error('Session not found in database');
      }

      // 2. Check Redis cache state
      let cacheState = null;
      try {
        cacheState = await cacheService.get(`progress:${this.targetSessionId}`);
        findings.push({
          type: 'cache',
          description: 'Cache state retrieved',
          evidence: { cacheState, hasCache: !!cacheState },
          severity: 'info'
        });
      } catch (cacheError) {
        findings.push({
          type: 'cache',
          description: 'Failed to retrieve cache state',
          evidence: { error: cacheError },
          severity: 'warning'
        });
      }

      // 3. Analyze session state
      const actualState: ImportStatus = {
        sessionId: dbSession.id,
        status: dbSession.import_status,
        createdAt: new Date(dbSession.created_at),
        updatedAt: new Date(dbSession.updated_at),
        parsedData: dbSession.parsed_data,
        errorMessage: dbSession.error_message
      };

      // 4. Check for orphaned session (processing > 5 minutes)
      const sessionAge = Date.now() - actualState.createdAt.getTime();
      const maxProcessingTime = 5 * 60 * 1000; // 5 minutes
      
      if (actualState.status === 'processing' && sessionAge > maxProcessingTime) {
        findings.push({
          type: 'database',
          description: 'Orphaned session detected - processing too long',
          evidence: { 
            sessionAge: Math.round(sessionAge / 60000) + ' minutes',
            maxAllowed: '5 minutes',
            status: actualState.status
          },
          severity: 'critical'
        });
      }

      // 5. Validate state transitions
      const discrepancies: string[] = [];
      
      if (actualState.status === 'processing' && !actualState.updatedAt) {
        discrepancies.push('Processing status without updated_at timestamp');
      }
      
      if (actualState.status === 'processing' && actualState.parsedData) {
        discrepancies.push('Processing status but parsed_data exists');
      }
      
      if (actualState.status === 'failed' && !actualState.errorMessage) {
        discrepancies.push('Failed status without error message');
      }

      if (discrepancies.length > 0) {
        findings.push({
          type: 'database',
          description: 'State transition inconsistencies detected',
          evidence: { discrepancies },
          severity: 'critical'
        });
      }

      investigation = {
        databaseQuery: `SELECT * FROM ai_import_logs WHERE id = '${this.targetSessionId}'`,
        cacheQuery: `progress:${this.targetSessionId}`,
        expectedState: {
          sessionId: this.targetSessionId,
          status: 'complete', // Expected after processing
          createdAt: actualState.createdAt,
          updatedAt: actualState.updatedAt
        },
        actualState,
        discrepancies
      };

      // Add evidence to collection
      this.addEvidence(DebugPhase.SESSION_STATE, findings, {
        dbSession,
        cacheState,
        investigation,
        sessionAge: Math.round(sessionAge / 60000)
      });

      logger.info('Session state investigation completed', {
        sessionId: this.targetSessionId,
        status: actualState.status,
        sessionAgeMinutes: Math.round(sessionAge / 60000),
        findingsCount: findings.length,
        criticalIssues: findings.filter(f => f.severity === 'critical').length
      });

      return investigation;

    } catch (error) {
      findings.push({
        type: 'database',
        description: 'Session state investigation failed',
        evidence: { error: error instanceof Error ? error.message : String(error) },
        severity: 'critical'
      });

      this.addEvidence(DebugPhase.SESSION_STATE, findings);
      throw error;
    }
  }

  /**
   * Phase 2: API Response Chain Analysis
   * Requirement 2: Trace complete API response chain for status checking
   */
  async investigateAPIResponseChain(): Promise<any> {
    logger.info('Starting API response chain investigation', { sessionId: this.targetSessionId });

    const findings: Finding[] = [];

    try {
      // This will be implemented in the next phase
      // For now, we'll focus on the database investigation first
      
      findings.push({
        type: 'api',
        description: 'API response chain investigation placeholder',
        evidence: { note: 'To be implemented after session state analysis' },
        severity: 'info'
      });

      this.addEvidence(DebugPhase.API_CHAIN, findings);
      
      return { status: 'pending_implementation' };

    } catch (error) {
      findings.push({
        type: 'api',
        description: 'API response chain investigation failed',
        evidence: { error: error instanceof Error ? error.message : String(error) },
        severity: 'critical'
      });

      this.addEvidence(DebugPhase.API_CHAIN, findings);
      throw error;
    }
  }
}

/**
 * Utility functions for evidence collection
 */
export const debugUtils = {
  /**
   * Format database query results for evidence
   */
  formatDatabaseEvidence(data: any): Record<string, any> {
    return {
      timestamp: new Date().toISOString(),
      data: JSON.parse(JSON.stringify(data, null, 2)),
      recordCount: Array.isArray(data) ? data.length : 1
    };
  },

  /**
   * Format cache evidence
   */
  formatCacheEvidence(key: string, value: any): Record<string, any> {
    return {
      timestamp: new Date().toISOString(),
      key,
      value,
      hasValue: value !== null && value !== undefined,
      type: typeof value
    };
  },

  /**
   * Create evidence summary
   */
  createEvidenceSummary(evidence: DebuggingEvidence[]): Record<string, any> {
    return {
      totalPhases: evidence.length,
      totalFindings: evidence.reduce((sum, e) => sum + e.findings.length, 0),
      criticalIssues: evidence.reduce((sum, e) => sum + e.findings.filter(f => f.severity === 'critical').length, 0),
      warningIssues: evidence.reduce((sum, e) => sum + e.findings.filter(f => f.severity === 'warning').length, 0),
      phases: evidence.map(e => e.phase),
      investigationDuration: evidence.length > 0 ? 
        new Date(evidence[evidence.length - 1].timestamp).getTime() - new Date(evidence[0].timestamp).getTime() : 0
    };
  }
};

// Export singleton instance
export const pdfImportDebugger = new PDFImportDebugger();