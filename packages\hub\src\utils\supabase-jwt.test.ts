// packages/hub/src/utils/supabase-jwt.test.ts
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { verifySupabaseJWT, SupabaseJWTError } from './supabase-jwt';
import { getSupabaseClient } from '../lib/supabase';
import { logger } from './logger';
import jwt from 'jsonwebtoken';

// Mock external dependencies to isolate the function under test.
vi.mock('../lib/supabase');
vi.mock('./logger');
vi.mock('jsonwebtoken');

// Create typed mocks for better autocompletion and type safety.
const mockedGetSupabaseClient = vi.mocked(getSupabaseClient);
const mockedLoggerError = vi.mocked(logger.error);
const mockedJwtDecode = vi.mocked(jwt.decode);

// Set up a mock for the Supabase client's 'getUser' method.
const mockGetUser = vi.fn();
mockedGetSupabaseClient.mockReturnValue({
  auth: {
    getUser: mockGetUser,
  },
} as any);

describe('verifySupabaseJWT', () => {
  const validToken = 'valid-jwt';
  const validUser = {
    id: 'user-123',
    email: '<EMAIL>',
    user_metadata: { name: 'Test User' },
    app_metadata: {},
    role: 'authenticated',
  };

  beforeEach(() => {
    // Reset mocks before each test to ensure isolation.
    vi.clearAllMocks();
  });

  // Test case for the happy path where the token is valid and Supabase confirms the user.
  it('should return a user when token is valid and verified by Supabase', async () => {
    // Arrange: Mock a valid, non-expired token payload and a successful Supabase response.
    mockedJwtDecode.mockReturnValue({
      sub: 'user-123',
      email: '<EMAIL>',
      exp: Date.now() / 1000 + 3600, // Expires in 1 hour
      aud: 'authenticated',
      iat: Date.now() / 1000 - 300, // Issued 5 minutes ago
      iss: 'https://example.supabase.co/auth/v1',
    });
    mockGetUser.mockResolvedValue({ data: { user: validUser }, error: null });

    // Act: Call the function with the valid token.
    const result = await verifySupabaseJWT(validToken);

    // Assert: The function should return the correctly formatted user object.
    expect(result).toEqual({
      id: 'user-123',
      email: '<EMAIL>',
      name: 'Test User',
      app_metadata: {},
      user_metadata: { name: 'Test User' },
      role: 'authenticated',
    });
    expect(mockGetUser).toHaveBeenCalledWith(validToken);
    expect(mockedLoggerError).not.toHaveBeenCalled();
  });

  // Test case for when Supabase's auth.getUser returns an error. This was a key failure point.
  it('should throw SupabaseJWTError when auth.getUser returns an error', async () => {
    // Arrange: Mock a valid token structure but a failure response from Supabase.
    mockedJwtDecode.mockReturnValue({
      sub: 'user-123',
      email: '<EMAIL>',
      exp: Date.now() / 1000 + 3600,
      aud: 'authenticated',
      iat: Date.now() / 1000 - 300,
      iss: 'https://example.supabase.co/auth/v1',
    });
    const supabaseError = new Error('Invalid token provided');
    mockGetUser.mockResolvedValue({ data: { user: null }, error: supabaseError });

    // Act & Assert: The promise should reject with a specific error.
    await expect(verifySupabaseJWT(validToken)).rejects.toThrow(
      new SupabaseJWTError('Invalid token')
    );
    expect(mockedLoggerError).toHaveBeenCalledWith('Supabase auth.getUser failed:', { error: supabaseError });
  });

  // Test case for when Supabase's auth.getUser returns no user and no error.
  it('should throw SupabaseJWTError when auth.getUser returns no user and no error', async () => {
    // Arrange: Mock a scenario where Supabase returns an empty success response.
    mockedJwtDecode.mockReturnValue({
      sub: 'user-123',
      email: '<EMAIL>',
      exp: Date.now() / 1000 + 3600,
      aud: 'authenticated',
      iat: Date.now() / 1000 - 300,
      iss: 'https://example.supabase.co/auth/v1',
    });
    mockGetUser.mockResolvedValue({ data: { user: null }, error: null });

    // Act & Assert: The function should still treat this as an invalid token.
    await expect(verifySupabaseJWT(validToken)).rejects.toThrow(
      new SupabaseJWTError('Invalid token')
    );
    expect(mockedLoggerError).toHaveBeenCalledWith('Supabase auth.getUser failed:', { error: null });
  });

  // Test case for when the token has expired.
  it('should throw SupabaseJWTError for an expired token', async () => {
    // Arrange: Mock a token payload with an expiration date in the past.
    mockedJwtDecode.mockReturnValue({
      sub: 'user-123',
      email: '<EMAIL>',
      exp: Date.now() / 1000 - 3600, // Expired 1 hour ago
      aud: 'authenticated',
      iat: Date.now() / 1000 - 7200,
      iss: 'https://example.supabase.co/auth/v1',
    });

    // Act & Assert: validateTokenStructure will detect expired token and throw 'Invalid token structure'.
    await expect(verifySupabaseJWT(validToken)).rejects.toThrow(
      new SupabaseJWTError('Invalid token structure')
    );
    expect(mockGetUser).not.toHaveBeenCalled();
  });

  // Test case for a malformed token that lacks required fields.
  it('should throw SupabaseJWTError for a token with invalid structure', async () => {
    // Arrange: Mock a decoded payload that is missing the 'sub' (subject) field.
    mockedJwtDecode.mockReturnValue({
      email: '<EMAIL>',
      exp: Date.now() / 1000 + 3600,
    });

    // Act & Assert: The function should reject before calling Supabase.
    await expect(verifySupabaseJWT('malformed-token')).rejects.toThrow(
      new SupabaseJWTError('Invalid token structure')
    );
    expect(mockGetUser).not.toHaveBeenCalled();
  });

  // Test case for when the token is not a valid JWT and jwt.decode fails.
  it('should throw a generic verification error if jwt.decode fails', async () => {
    // Arrange: Mock jwt.decode to throw an error, simulating a completely invalid token string.
    mockedJwtDecode.mockImplementation(() => {
      throw new Error('Invalid token format');
    });

    // Act & Assert: The function should catch the error in validateTokenStructure and throw 'Invalid token structure'.
    await expect(verifySupabaseJWT('not-a-jwt')).rejects.toThrow(
      new SupabaseJWTError('Invalid token structure')
    );
    expect(mockGetUser).not.toHaveBeenCalled();
  });

  // Test case for when the Supabase client itself throws an exception (e.g., network error).
  it('should throw SupabaseJWTError if auth.getUser throws an exception', async () => {
    // Arrange: Mock a valid token, but make the Supabase client reject its promise.
    mockedJwtDecode.mockReturnValue({
      sub: 'user-123',
      email: '<EMAIL>',
      exp: Date.now() / 1000 + 3600,
      aud: 'authenticated',
      iat: Date.now() / 1000 - 300,
      iss: 'https://example.supabase.co/auth/v1',
    });
    const networkError = new Error('Network request failed');
    mockGetUser.mockRejectedValue(networkError);

    // Act & Assert: The function should catch the exception and reject with the correct error.
    await expect(verifySupabaseJWT(validToken)).rejects.toThrow(
      new SupabaseJWTError('Invalid token')
    );
    expect(mockedLoggerError).toHaveBeenCalledWith('Supabase authentication failed:', { error: networkError });
  });

  // Additional edge cases for comprehensive coverage
  describe('Edge Cases and Rate Limiting', () => {
    it('should handle Supabase rate limit errors gracefully', async () => {
      // Arrange: Mock a valid token but rate limit error from Supabase
      mockedJwtDecode.mockReturnValue({
        sub: 'user-123',
        email: '<EMAIL>',
        exp: Date.now() / 1000 + 3600,
        aud: 'authenticated',
        iat: Date.now() / 1000 - 300,
        iss: 'https://example.supabase.co/auth/v1',
      });
      const rateLimitError = new Error('Too many requests');
      (rateLimitError as any).status = 429;
      mockGetUser.mockRejectedValue(rateLimitError);

      // Act & Assert: Should throw SupabaseJWTError with appropriate logging
      await expect(verifySupabaseJWT(validToken)).rejects.toThrow(
        new SupabaseJWTError('Invalid token')
      );
      expect(mockedLoggerError).toHaveBeenCalledWith('Supabase authentication failed:', { error: rateLimitError });
    });

    it('should handle missing token gracefully', async () => {
      // Mock jwt.decode to return null for empty string
      mockedJwtDecode.mockReturnValue(null);
      
      // Act & Assert: Should throw with appropriate error message
      await expect(verifySupabaseJWT('')).rejects.toThrow(
        new SupabaseJWTError('Invalid token structure')
      );
      expect(mockGetUser).not.toHaveBeenCalled();
    });

    it('should handle token with missing exp field', async () => {
      // Arrange: Mock a token without expiration
      mockedJwtDecode.mockReturnValue({
        sub: 'user-123',
        email: '<EMAIL>',
        // exp field missing
      });

      // Act & Assert: Should reject as invalid structure
      await expect(verifySupabaseJWT(validToken)).rejects.toThrow(
        new SupabaseJWTError('Invalid token structure')
      );
      expect(mockGetUser).not.toHaveBeenCalled();
    });

    it('should handle token expiring exactly now', async () => {
      // Arrange: Mock a token that expires right at the current time
      // Note: isTokenExpired has a 5-second buffer, so we need to go back further
      const nowInSeconds = Math.floor(Date.now() / 1000);
      mockedJwtDecode.mockReturnValue({
        sub: 'user-123',
        email: '<EMAIL>',
        exp: nowInSeconds - 10, // Expired 10 seconds ago to bypass the 5-second buffer
        aud: 'authenticated',
        iat: nowInSeconds - 3610,
        iss: 'https://example.supabase.co/auth/v1',
      });

      // Act & Assert: validateTokenStructure will detect expired token
      await expect(verifySupabaseJWT(validToken)).rejects.toThrow(
        new SupabaseJWTError('Invalid token structure')
      );
      expect(mockGetUser).not.toHaveBeenCalled();
    });

    it('should handle Supabase returning user with missing required fields', async () => {
      // Arrange: Mock a successful response but with incomplete user data
      mockedJwtDecode.mockReturnValue({
        sub: 'user-123',
        email: '<EMAIL>',
        exp: Date.now() / 1000 + 3600,
        aud: 'authenticated',
        iat: Date.now() / 1000 - 300,
        iss: 'https://example.supabase.co/auth/v1',
      });
      mockGetUser.mockResolvedValue({ 
        data: { 
          user: { 
            id: 'user-123',
            // email field missing
            user_metadata: {},
            app_metadata: {},
          } 
        }, 
        error: null 
      });

      // Act: Call the function
      const result = await verifySupabaseJWT(validToken);

      // Assert: Should still process the user but with empty string for missing email
      expect(result.id).toBe('user-123');
      expect(result.email).toBe(''); // The code returns empty string, not undefined
    });
  });

  describe('Performance and Resilience', () => {
    it('should handle concurrent verification requests independently', async () => {
      // Arrange: Mock successful responses with slight delays
      mockedJwtDecode.mockReturnValue({
        sub: 'user-123',
        email: '<EMAIL>',
        exp: Date.now() / 1000 + 3600,
        aud: 'authenticated',
        iat: Date.now() / 1000 - 300,
        iss: 'https://example.supabase.co/auth/v1',
      });
      
      mockGetUser.mockImplementation(async (token) => {
        // Extract token number from the token parameter to ensure proper ordering
        const tokenNum = token.match(/token-(\d+)/)?.[1] || '0';
        await new Promise(resolve => setTimeout(resolve, 10));
        return { 
          data: { 
            user: { ...validUser, id: `user-${parseInt(tokenNum) + 1}` } 
          }, 
          error: null 
        };
      });

      // Act: Make multiple concurrent requests
      const promises = Array(3).fill(null).map((_, i) => 
        verifySupabaseJWT(`token-${i}`)
      );
      const results = await Promise.all(promises);

      // Assert: Each request should be handled independently
      expect(results).toHaveLength(3);
      expect(mockGetUser).toHaveBeenCalledTimes(3);
      results.forEach((result, i) => {
        expect(result.id).toBe(`user-${i + 1}`);
      });
    });
  });
});