{"name": "@travelviz/hub", "version": "1.0.0", "private": true, "scripts": {"dev": "npx tsx watch src/index.ts", "build": "tsc", "start": "node dist/index.js", "test": "vitest run", "test:watch": "vitest", "test:unit": "vitest run --config vitest.config.ts --reporter=verbose", "test:integration": "vitest run tests/integration/ --config vitest.config.ts --reporter=verbose", "test:integration:smoke": "vitest run tests/integration/flows/ai-import-simple.test.ts tests/integration/flows/auth-flow.test.ts --config vitest.config.ts --reporter=verbose", "test:integration:full": "vitest run tests/integration/flows/ --config vitest.config.ts --reporter=verbose", "test:performance": "vitest run tests/integration/flows/performance-load.test.ts --config vitest.config.ts --reporter=verbose", "test:ci": "vitest run --config vitest.config.ts --reporter=verbose --coverage", "test:validate": "vitest run test/runtime-validation.test.ts", "test:debug": "node scripts/debug-imports.js", "test:logs": "node scripts/view-test-logs.js -- --errors", "lint": "eslint src --ext .ts,.tsx", "type-check": "tsc --noEmit", "validate:imports": "node ../../scripts/validate-imports.js"}, "dependencies": {"@googlemaps/google-maps-services-js": "^3.4.2", "@supabase/supabase-js": "^2.50.2", "@travelviz/shared": "workspace:*", "@types/compression": "^1.8.1", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/jsdom": "^21.1.7", "@types/jsonwebtoken": "^9.0.10", "@types/morgan": "^1.9.9", "@types/multer": "^2.0.0", "@types/node": "^20.14.0", "@types/pdf-parse": "^1.1.5", "@upstash/redis": "^1.35.1", "axios": "^1.10.0", "compression": "^1.8.0", "cors": "^2.8.5", "dompurify": "^3.2.6", "dotenv": "^16.4.5", "express": "^4.19.2", "express-rate-limit": "^7.5.1", "helmet": "^7.1.0", "isomorphic-dompurify": "^2.26.0", "jsdom": "^26.1.0", "jsonwebtoken": "^9.0.2", "lru-cache": "^10.1.0", "morgan": "^1.10.0", "multer": "^2.0.1", "nanoid": "^5.1.5", "pdf-parse": "^1.1.1", "uuid": "^11.1.0", "zod": "^4.0.5"}, "devDependencies": {"@playwright/test": "^1.53.2", "@types/eventsource": "^1.1.15", "@types/supertest": "^6.0.2", "@types/uuid": "^10.0.0", "eventsource": "^2.0.2", "supertest": "^7.0.0"}}