import { AnalyticsProvider, AnalyticsConfig } from '@travelviz/shared';
import posthog from 'posthog-js';

/**
 * PostHog implementation of the AnalyticsProvider interface
 */
export class PostHogAnalyticsProvider implements AnalyticsProvider {
  private initialized = false;
  private config: AnalyticsConfig;

  constructor(config: AnalyticsConfig) {
    this.config = config;
    if (!config.disabled && config.apiKey && typeof window !== 'undefined') {
      this.initialize();
    }
  }

  private initialize(): void {
    if (this.initialized || !this.config.apiKey) return;

    try {
      posthog.init(this.config.apiKey, {
        api_host: this.config.apiHost || 'https://app.posthog.com',
        disable_session_recording: true,
        capture_pageview: false,
        persistence: 'localStorage',
        loaded: (posthog) => {
          if (this.config.debug) {
            console.log('[Analytics] PostHog initialized');
          }
        },
      });
      this.initialized = true;
    } catch (error) {
      console.error('[Analytics] Failed to initialize PostHog:', error);
    }
  }

  track(event: string, properties?: Record<string, any>): void {
    if (this.config.disabled || !this.initialized) {
      if (this.config.debug) {
        console.log('[Analytics] Track event (disabled):', event, properties);
      }
      return;
    }

    try {
      posthog.capture(event, properties);
      if (this.config.debug) {
        console.log('[Analytics] Track event:', event, properties);
      }
    } catch (error) {
      console.error('[Analytics] Failed to track event:', error);
    }
  }

  identify(userId: string, traits?: Record<string, any>): void {
    if (this.config.disabled || !this.initialized) {
      if (this.config.debug) {
        console.log('[Analytics] Identify user (disabled):', userId, traits);
      }
      return;
    }

    try {
      posthog.identify(userId, traits);
      if (this.config.debug) {
        console.log('[Analytics] Identify user:', userId, traits);
      }
    } catch (error) {
      console.error('[Analytics] Failed to identify user:', error);
    }
  }

  reset(): void {
    if (this.config.disabled || !this.initialized) {
      if (this.config.debug) {
        console.log('[Analytics] Reset (disabled)');
      }
      return;
    }

    try {
      posthog.reset();
      if (this.config.debug) {
        console.log('[Analytics] Reset');
      }
    } catch (error) {
      console.error('[Analytics] Failed to reset:', error);
    }
  }

  setSuperProperties(properties: Record<string, any>): void {
    if (this.config.disabled || !this.initialized) {
      if (this.config.debug) {
        console.log('[Analytics] Set super properties (disabled):', properties);
      }
      return;
    }

    try {
      posthog.register(properties);
      if (this.config.debug) {
        console.log('[Analytics] Set super properties:', properties);
      }
    } catch (error) {
      console.error('[Analytics] Failed to set super properties:', error);
    }
  }

  getDistinctId(): string | undefined {
    if (this.config.disabled || !this.initialized) {
      return undefined;
    }

    try {
      return posthog.get_distinct_id();
    } catch (error) {
      console.error('[Analytics] Failed to get distinct ID:', error);
      return undefined;
    }
  }
}