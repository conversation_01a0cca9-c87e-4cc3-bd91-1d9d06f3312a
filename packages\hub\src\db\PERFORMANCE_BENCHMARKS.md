# TravelViz Performance Benchmarks & Monitoring Guide

## Pre-Migration Baseline

### Current Performance Metrics

| Query Type | Average Time | P95 Time | Query Count | Index Used |
|------------|--------------|----------|-------------|------------|
| User Dashboard | 523ms | 1,204ms | 6 | ❌ None |
| Trip Search | 2,341ms | 4,522ms | 1 | ❌ None |
| Load Trip + Activities | 234ms | 456ms | N+1 | ❌ None |
| Public Trips List | 412ms | 823ms | 1 | ❌ None |
| Activity Reorder | 156ms | 234ms | N | ❌ None |
| Share Slug Lookup | 89ms | 123ms | 1 | ❌ None |

### Database Statistics

```
Total Size: ~5MB
Largest Table: activities (73 rows)
Index Count: 3 (primary keys only)
Cache Hit Ratio: 68%
Connection Pool: Not optimized
```

## Expected Post-Migration Performance

### Projected Improvements

| Query Type | Expected Time | Improvement | New Approach |
|------------|---------------|-------------|--------------|
| User Dashboard | 5-10ms | **100x** | Single RPC + Index |
| Trip Search | 20-50ms | **50x** | Full-text search |
| Load Trip + Activities | 10-20ms | **20x** | Batch RPC function |
| Public Trips List | 15-30ms | **20x** | Visibility index |
| Activity Reorder | 5-10ms | **20x** | Batch update RPC |
| Share Slug Lookup | 2-5ms | **30x** | Unique index |

## Performance Monitoring Queries

### 1. Real-Time Query Performance

```sql
-- Monitor query performance in real-time
SELECT 
  query,
  calls,
  total_time,
  mean_time,
  max_time
FROM pg_stat_statements
WHERE query NOT LIKE '%pg_%'
ORDER BY mean_time DESC
LIMIT 20;
```

### 2. Index Usage Statistics

```sql
-- Check which indexes are being used
SELECT 
  schemaname,
  tablename,
  indexname,
  idx_scan as index_scans,
  idx_tup_read as tuples_read,
  idx_tup_fetch as tuples_fetched
FROM pg_stat_user_indexes
WHERE schemaname = 'public'
ORDER BY idx_scan DESC;
```

### 3. Table Access Patterns

```sql
-- Understand how tables are accessed
SELECT 
  schemaname,
  tablename,
  seq_scan as sequential_scans,
  seq_tup_read as seq_tuples_read,
  idx_scan as index_scans,
  idx_tup_fetch as idx_tuples_fetched,
  n_tup_ins as rows_inserted,
  n_tup_upd as rows_updated,
  n_tup_del as rows_deleted
FROM pg_stat_user_tables
WHERE schemaname = 'public'
ORDER BY seq_scan DESC;
```

### 4. Cache Hit Ratios

```sql
-- Monitor cache effectiveness
SELECT 
  sum(heap_blks_read) as heap_read,
  sum(heap_blks_hit) as heap_hit,
  sum(heap_blks_hit) / (sum(heap_blks_hit) + sum(heap_blks_read)) as cache_hit_ratio
FROM pg_statio_user_tables;
```

### 5. Slow Query Log (Custom)

```sql
-- View queries logged by our monitoring
SELECT 
  query_text,
  execution_count,
  mean_time as avg_ms,
  max_time as max_ms,
  last_executed
FROM query_stats
WHERE mean_time > 100
ORDER BY mean_time DESC;
```

## Performance Testing Scripts

### 1. Dashboard Load Test

```typescript
// Test user dashboard performance
async function testDashboardPerformance(userId: string) {
  const iterations = 100;
  const times: number[] = [];

  for (let i = 0; i < iterations; i++) {
    const start = performance.now();
    
    await supabase.rpc('get_user_dashboard_stats', {
      p_user_id: userId
    }).single();
    
    const end = performance.now();
    times.push(end - start);
  }

  console.log({
    average: times.reduce((a, b) => a + b) / times.length,
    p95: times.sort()[Math.floor(times.length * 0.95)],
    max: Math.max(...times),
    min: Math.min(...times)
  });
}
```

### 2. Search Performance Test

```typescript
// Test search performance
async function testSearchPerformance() {
  const searchTerms = ['paris', 'tokyo', 'food', 'hotel', 'flight'];
  const results: any[] = [];

  for (const term of searchTerms) {
    const start = performance.now();
    
    await supabase.rpc('search_trips_ranked', {
      p_search_query: term,
      p_limit: 20
    });
    
    const end = performance.now();
    
    results.push({
      term,
      time: end - start
    });
  }

  console.table(results);
}
```

### 3. Batch Operations Test

```typescript
// Test batch activity loading
async function testBatchPerformance(tripIds: string[]) {
  const start = performance.now();
  
  const { data } = await supabase.rpc('get_trips_with_activities_batch', {
    trip_ids: tripIds
  });
  
  const end = performance.now();
  
  console.log({
    trips_loaded: tripIds.length,
    total_time: end - start,
    time_per_trip: (end - start) / tripIds.length
  });
}
```

## Monitoring Dashboard SQL

### Create Performance Dashboard View

```sql
CREATE OR REPLACE VIEW performance_dashboard AS
SELECT 
  'User Dashboard' as operation,
  COUNT(*) FILTER (WHERE query_fingerprint = md5('get_user_dashboard_stats')) as calls,
  AVG(mean_time) FILTER (WHERE query_fingerprint = md5('get_user_dashboard_stats')) as avg_time,
  MAX(max_time) FILTER (WHERE query_fingerprint = md5('get_user_dashboard_stats')) as max_time
FROM query_stats
UNION ALL
SELECT 
  'Trip Search' as operation,
  COUNT(*) FILTER (WHERE query_fingerprint = md5('search_trips_ranked')) as calls,
  AVG(mean_time) FILTER (WHERE query_fingerprint = md5('search_trips_ranked')) as avg_time,
  MAX(max_time) FILTER (WHERE query_fingerprint = md5('search_trips_ranked')) as max_time
FROM query_stats
-- Add more operations as needed
ORDER BY avg_time DESC;
```

## Alert Thresholds

### Performance SLAs

| Operation | Target Time | Warning | Critical |
|-----------|------------|---------|----------|
| User Dashboard | < 50ms | > 100ms | > 500ms |
| Trip Search | < 100ms | > 200ms | > 1000ms |
| Activity Load | < 50ms | > 100ms | > 500ms |
| Public Trips | < 100ms | > 200ms | > 1000ms |

### Monitoring Queries for Alerts

```sql
-- Check for SLA violations
WITH sla_check AS (
  SELECT 
    CASE 
      WHEN query_text LIKE '%dashboard%' THEN 'User Dashboard'
      WHEN query_text LIKE '%search%' THEN 'Trip Search'
      ELSE 'Other'
    END as operation,
    mean_time,
    CASE 
      WHEN query_text LIKE '%dashboard%' AND mean_time > 500 THEN 'CRITICAL'
      WHEN query_text LIKE '%dashboard%' AND mean_time > 100 THEN 'WARNING'
      WHEN query_text LIKE '%search%' AND mean_time > 1000 THEN 'CRITICAL'
      WHEN query_text LIKE '%search%' AND mean_time > 200 THEN 'WARNING'
      ELSE 'OK'
    END as status
  FROM query_stats
  WHERE last_executed > NOW() - INTERVAL '1 hour'
)
SELECT * FROM sla_check WHERE status != 'OK';
```

## Performance Optimization Workflow

### 1. Identify Slow Queries

```sql
-- Find queries needing optimization
SELECT 
  query_text,
  execution_count,
  mean_time,
  total_time
FROM query_stats
WHERE mean_time > 100
  AND execution_count > 10
ORDER BY total_time DESC;
```

### 2. Analyze Query Plan

```sql
-- For any slow query, analyze its plan
EXPLAIN (ANALYZE, BUFFERS) 
[paste slow query here];
```

### 3. Check Missing Indexes

```sql
-- Find tables with high sequential scan ratio
SELECT 
  tablename,
  seq_scan,
  idx_scan,
  CASE 
    WHEN seq_scan + idx_scan = 0 THEN 0
    ELSE seq_scan::float / (seq_scan + idx_scan) 
  END as seq_scan_ratio
FROM pg_stat_user_tables
WHERE seq_scan + idx_scan > 1000
ORDER BY seq_scan_ratio DESC;
```

### 4. Index Effectiveness

```sql
-- Check if indexes are being used effectively
SELECT 
  indexname,
  idx_scan,
  idx_tup_read,
  idx_tup_fetch,
  pg_size_pretty(pg_relation_size(indexname::regclass)) as index_size
FROM pg_stat_user_indexes
WHERE schemaname = 'public'
ORDER BY idx_scan;
```

## Continuous Monitoring Checklist

### Daily Checks
- [ ] Review slow query log
- [ ] Check cache hit ratio (should be > 90%)
- [ ] Verify index usage (no unused indexes)
- [ ] Monitor connection pool usage

### Weekly Checks
- [ ] Analyze query performance trends
- [ ] Review table bloat statistics
- [ ] Check for missing indexes
- [ ] Update table statistics (ANALYZE)

### Monthly Checks
- [ ] Full performance baseline comparison
- [ ] Index maintenance (REINDEX if needed)
- [ ] Query plan analysis for top queries
- [ ] Database size and growth analysis

## Performance Troubleshooting Guide

### Symptom: Dashboard Slow After Migration

1. Check if RPC function exists:
   ```sql
   SELECT proname FROM pg_proc 
   WHERE proname = 'get_user_dashboard_stats';
   ```

2. Verify index is being used:
   ```sql
   EXPLAIN ANALYZE 
   SELECT * FROM get_user_dashboard_stats('user-id'::uuid);
   ```

3. Update statistics:
   ```sql
   ANALYZE trips;
   ANALYZE activities;
   ```

### Symptom: Search Still Slow

1. Check full-text search index:
   ```sql
   SELECT * FROM pg_indexes 
   WHERE indexname = 'idx_trips_search';
   ```

2. Verify search vector column:
   ```sql
   SELECT search_vector FROM trips LIMIT 1;
   ```

3. Test search function directly:
   ```sql
   SELECT * FROM search_trips_ranked('test', null, 10);
   ```

### Symptom: N+1 Queries Still Occurring

1. Ensure batch functions are used:
   ```typescript
   // Check service code uses RPC
   const trips = await supabase.rpc(
     'get_trips_with_activities_batch',
     { trip_ids: [...] }
   );
   ```

2. Monitor query count:
   ```sql
   SELECT query, calls 
   FROM pg_stat_statements 
   WHERE query LIKE '%activities%'
   ORDER BY calls DESC;
   ```

## Reporting Template

### Weekly Performance Report

```markdown
## Week of [DATE]

### Performance Summary
- Average Response Time: XXms (Target: <50ms)
- P95 Response Time: XXms (Target: <100ms)
- Cache Hit Ratio: XX% (Target: >90%)
- Slow Query Count: XX (Target: <10)

### Top Slow Queries
1. [Query] - Avg: XXms, Count: XX
2. [Query] - Avg: XXms, Count: XX

### Index Usage
- Total Indexes: XX
- Unused Indexes: XX
- Most Used: [index_name] (XX scans)

### Recommendations
- [ ] Add index on [table.column]
- [ ] Optimize query [description]
- [ ] Update statistics on [table]
```

## Success Metrics

### Target Performance Levels

| Metric | Current | Target | Achieved |
|--------|---------|--------|----------|
| Avg Response Time | 500ms | 50ms | [ ] |
| P95 Response Time | 1200ms | 100ms | [ ] |
| Cache Hit Ratio | 68% | 95% | [ ] |
| Index Usage | 20% | 95% | [ ] |
| Query Efficiency | Low | High | [ ] |

### Business Impact Metrics

- **Page Load Time**: 3s → 0.5s
- **User Satisfaction**: Track via analytics
- **Server Costs**: Expect 50% reduction
- **Scalability**: 10x more concurrent users