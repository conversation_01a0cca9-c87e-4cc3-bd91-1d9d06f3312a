import { z } from 'zod';
import { ActivityType } from './activity';

// AI Parser specific schemas

export const ParsedLocationSchema = z.object({
  address: z.string(),
  lat: z.number().optional(),
  lng: z.number().optional(),
  confidence: z.number().min(0).max(1).default(1),
});

export const ParsedActivitySchema = z.object({
  name: z.string().min(1),
  description: z.string().optional(),
  type: z.nativeEnum(ActivityType),
  startTime: z.string(), // ISO string
  endTime: z.string().optional(), // ISO string
  location: ParsedLocationSchema.optional(),
  price: z.number().min(0).optional(),
  currency: z.string().regex(/^[A-Z]{3}$/).default('USD'),
  bookingUrl: z.string().url().optional(),
  confidence: z.number().min(0).max(1).default(1),
  dayNumber: z.number().int().min(1),
  originalText: z.string().optional(), // Relevant excerpt from conversation
});

export const ParseMetadataSchema = z.object({
  source: z.enum(['chatgpt', 'claude', 'gemini', 'unknown']),
  confidence: z.number().min(0).max(1),
  warnings: z.array(z.string()).default([]),
  parseDate: z.string(),
  version: z.string().default('1.0'),
  // AI Model Optimization tracking fields
  modelUsed: z.string().optional(),
  inputTokens: z.number().optional(),
  outputTokens: z.number().optional(),
  cost: z.number().optional(),
  fallbackAttempts: z.number().optional(),
});

export const ParsedTripSchema = z.object({
  title: z.string().min(1),
  description: z.string().optional(),
  startDate: z.string(), // YYYY-MM-DD
  endDate: z.string(), // YYYY-MM-DD
  destination: z.string(),
  activities: z.array(ParsedActivitySchema),
  metadata: ParseMetadataSchema,
});

// Parse session schema for tracking progress
export const ParseSessionSchema = z.object({
  id: z.string().uuid(),
  status: z.enum(['pending', 'processing', 'complete', 'error']),
  progress: z.number().min(0).max(100),
  currentStep: z.string(),
  result: ParsedTripSchema.optional(),
  error: z.string().optional(),
  startedAt: z.date(),
  completedAt: z.date().optional(),
});

// Export types
export type ParsedLocation = z.infer<typeof ParsedLocationSchema>;
export type ParsedActivity = z.infer<typeof ParsedActivitySchema>;
export type ParseMetadata = z.infer<typeof ParseMetadataSchema>;
export type ParsedTrip = z.infer<typeof ParsedTripSchema>;
export type ParseSession = z.infer<typeof ParseSessionSchema>;

// Helper to convert parsed data to database format
export function parsedTripToDbFormat(parsed: ParsedTrip, userId: string) {
  return {
    user_id: userId,
    title: parsed.title,
    description: parsed.description,
    destination: parsed.destination,
    start_date: parsed.startDate,
    end_date: parsed.endDate,
    metadata: {
      importSource: parsed.metadata.source,
      importDate: parsed.metadata.parseDate,
      importConfidence: parsed.metadata.confidence,
      importWarnings: parsed.metadata.warnings,
    },
  };
}

export function parsedActivityToDbFormat(activity: ParsedActivity, tripId: string, position: number) {
  return {
    trip_id: tripId,
    title: activity.name,
    description: activity.description,
    type: activity.type,
    start_time: activity.startTime,
    end_time: activity.endTime,
    location: activity.location?.address,
    location_lat: activity.location?.lat,
    location_lng: activity.location?.lng,
    price: activity.price,
    currency: activity.currency,
    booking_url: activity.bookingUrl,
    position,
    metadata: {
      dayNumber: activity.dayNumber,
      confidence: activity.confidence,
      originalText: activity.originalText,
    },
  };
}