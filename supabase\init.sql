-- TravelViz Database Initialization Script
-- This script creates all necessary tables, indexes, functions, and RLS policies

-- Enable required extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- <PERSON><PERSON> custom types
CREATE TYPE trip_status AS ENUM ('draft', 'planning', 'confirmed', 'in_progress', 'completed', 'cancelled');
CREATE TYPE activity_type AS ENUM ('flight', 'accommodation', 'transport', 'dining', 'activity', 'shopping', 'other');
CREATE TYPE trip_visibility AS ENUM ('private', 'unlisted', 'public');
CREATE TYPE share_type AS ENUM ('read_only', 'can_comment', 'can_edit');

-- Create profiles table (extends Supabase auth.users)
CREATE TABLE IF NOT EXISTS profiles (
    id UUID PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
    email TEXT UNIQUE NOT NULL,
    name TEXT,
    avatar_url TEXT,
    bio TEXT,
    preferences JSONB DEFAULT '{}',
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create trips table
CREATE TABLE IF NOT EXISTS trips (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    title TEXT NOT NULL,
    description TEXT,
    destination TEXT,
    start_date DATE,
    end_date DATE,
    status trip_status DEFAULT 'draft',
    visibility trip_visibility DEFAULT 'private',
    cover_image TEXT,
    metadata JSONB DEFAULT '{}',
    tags TEXT[] DEFAULT '{}',
    budget_amount DECIMAL(10, 2),
    budget_currency VARCHAR(3) DEFAULT 'USD',
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    deleted_at TIMESTAMPTZ,
    CONSTRAINT valid_dates CHECK (end_date >= start_date)
);

-- Create activities table
CREATE TABLE IF NOT EXISTS activities (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    trip_id UUID NOT NULL REFERENCES trips(id) ON DELETE CASCADE,
    title TEXT NOT NULL,
    description TEXT,
    type activity_type DEFAULT 'activity',
    start_time TIMESTAMPTZ,
    end_time TIMESTAMPTZ,
    location TEXT,
    location_lat DECIMAL(10, 8),
    location_lng DECIMAL(11, 8),
    price DECIMAL(10, 2),
    currency VARCHAR(3) DEFAULT 'USD',
    booking_reference TEXT,
    booking_url TEXT,
    notes TEXT,
    metadata JSONB DEFAULT '{}',
    attachments TEXT[] DEFAULT '{}',
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    CONSTRAINT valid_times CHECK (end_time >= start_time)
);

-- Create trip_shares table for collaboration
CREATE TABLE IF NOT EXISTS trip_shares (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    trip_id UUID NOT NULL REFERENCES trips(id) ON DELETE CASCADE,
    shared_by UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    shared_with UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    email TEXT,
    share_type share_type DEFAULT 'read_only',
    share_token TEXT UNIQUE DEFAULT gen_random_uuid()::TEXT,
    accepted_at TIMESTAMPTZ,
    expires_at TIMESTAMPTZ,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    CONSTRAINT shared_with_someone CHECK (shared_with IS NOT NULL OR email IS NOT NULL)
);

-- Create trip_templates table for sharing trip structures
CREATE TABLE IF NOT EXISTS trip_templates (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    created_by UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    title TEXT NOT NULL,
    description TEXT,
    template_data JSONB NOT NULL,
    tags TEXT[] DEFAULT '{}',
    is_public BOOLEAN DEFAULT false,
    use_count INTEGER DEFAULT 0,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create ai_import_logs table for tracking AI conversation imports
CREATE TABLE IF NOT EXISTS ai_import_logs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    trip_id UUID REFERENCES trips(id) ON DELETE SET NULL,
    ai_platform TEXT NOT NULL,
    import_status TEXT NOT NULL,
    raw_conversation TEXT,
    parsed_data JSONB,
    error_message TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create trip_collaborators view for easier querying
CREATE OR REPLACE VIEW trip_collaborators AS
SELECT 
    ts.trip_id,
    ts.shared_with as user_id,
    ts.share_type,
    ts.accepted_at,
    p.name,
    p.email,
    p.avatar_url
FROM trip_shares ts
JOIN profiles p ON ts.shared_with = p.id
WHERE ts.accepted_at IS NOT NULL;

-- Security tables for Phase 2 features
CREATE TABLE IF NOT EXISTS auth_failed_attempts (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    email TEXT NOT NULL,
    ip_address INET,
    user_agent TEXT,
    attempted_at TIMESTAMPTZ DEFAULT NOW(),
    created_at TIMESTAMPTZ DEFAULT NOW()
);

CREATE TABLE IF NOT EXISTS auth_account_lockouts (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    email TEXT NOT NULL UNIQUE,
    locked_until TIMESTAMPTZ NOT NULL,
    reason TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Audit log table
CREATE TABLE IF NOT EXISTS audit_logs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
    event_type TEXT NOT NULL,
    event_data JSONB DEFAULT '{}',
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create indexes for performance
CREATE INDEX idx_trips_user_id ON trips(user_id) WHERE deleted_at IS NULL;
CREATE INDEX idx_trips_status ON trips(status) WHERE deleted_at IS NULL;
CREATE INDEX idx_trips_dates ON trips(start_date, end_date) WHERE deleted_at IS NULL;
CREATE INDEX idx_activities_trip_id ON activities(trip_id);
CREATE INDEX idx_activities_start_time ON activities(start_time);
CREATE INDEX idx_trip_shares_trip_id ON trip_shares(trip_id);
CREATE INDEX idx_trip_shares_shared_with ON trip_shares(shared_with);
CREATE INDEX idx_trip_shares_token ON trip_shares(share_token);
CREATE INDEX idx_auth_failed_attempts_email ON auth_failed_attempts(email);
CREATE INDEX idx_auth_failed_attempts_ip ON auth_failed_attempts(ip_address);
CREATE INDEX idx_audit_logs_user_id ON audit_logs(user_id);
CREATE INDEX idx_audit_logs_event_type ON audit_logs(event_type);

-- Create updated_at trigger function
CREATE OR REPLACE FUNCTION update_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Apply updated_at triggers
CREATE TRIGGER update_profiles_updated_at BEFORE UPDATE ON profiles
    FOR EACH ROW EXECUTE FUNCTION update_updated_at();

CREATE TRIGGER update_trips_updated_at BEFORE UPDATE ON trips
    FOR EACH ROW EXECUTE FUNCTION update_updated_at();

CREATE TRIGGER update_activities_updated_at BEFORE UPDATE ON activities
    FOR EACH ROW EXECUTE FUNCTION update_updated_at();

CREATE TRIGGER update_trip_templates_updated_at BEFORE UPDATE ON trip_templates
    FOR EACH ROW EXECUTE FUNCTION update_updated_at();

CREATE TRIGGER update_ai_import_logs_updated_at BEFORE UPDATE ON ai_import_logs
    FOR EACH ROW EXECUTE FUNCTION update_updated_at();

-- Create function to automatically create profile on user signup
CREATE OR REPLACE FUNCTION handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO profiles (id, email, name)
    VALUES (
        NEW.id,
        NEW.email,
        COALESCE(NEW.raw_user_meta_data->>'name', NEW.raw_user_meta_data->>'full_name')
    );
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger for new user profiles
CREATE OR REPLACE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW
    EXECUTE FUNCTION handle_new_user();

-- Row Level Security (RLS) Policies
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE trips ENABLE ROW LEVEL SECURITY;
ALTER TABLE activities ENABLE ROW LEVEL SECURITY;
ALTER TABLE trip_shares ENABLE ROW LEVEL SECURITY;
ALTER TABLE trip_templates ENABLE ROW LEVEL SECURITY;
ALTER TABLE ai_import_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE auth_failed_attempts ENABLE ROW LEVEL SECURITY;
ALTER TABLE auth_account_lockouts ENABLE ROW LEVEL SECURITY;
ALTER TABLE audit_logs ENABLE ROW LEVEL SECURITY;

-- Profiles policies
CREATE POLICY "Users can view their own profile"
    ON profiles FOR SELECT
    USING (auth.uid() = id);

CREATE POLICY "Users can update their own profile"
    ON profiles FOR UPDATE
    USING (auth.uid() = id);

-- Trips policies
CREATE POLICY "Users can view their own trips"
    ON trips FOR SELECT
    USING (auth.uid() = user_id AND deleted_at IS NULL);

CREATE POLICY "Users can view shared trips"
    ON trips FOR SELECT
    USING (
        EXISTS (
            SELECT 1 FROM trip_shares
            WHERE trip_shares.trip_id = trips.id
            AND trip_shares.shared_with = auth.uid()
            AND trip_shares.accepted_at IS NOT NULL
        )
        AND deleted_at IS NULL
    );

CREATE POLICY "Users can create their own trips"
    ON trips FOR INSERT
    WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own trips"
    ON trips FOR UPDATE
    USING (auth.uid() = user_id AND deleted_at IS NULL);

CREATE POLICY "Users can update shared trips with edit permission"
    ON trips FOR UPDATE
    USING (
        EXISTS (
            SELECT 1 FROM trip_shares
            WHERE trip_shares.trip_id = trips.id
            AND trip_shares.shared_with = auth.uid()
            AND trip_shares.share_type = 'can_edit'
            AND trip_shares.accepted_at IS NOT NULL
        )
        AND deleted_at IS NULL
    );

CREATE POLICY "Users can soft delete their own trips"
    ON trips FOR UPDATE
    USING (auth.uid() = user_id AND deleted_at IS NULL)
    WITH CHECK (auth.uid() = user_id);

-- Activities policies
CREATE POLICY "Users can view activities for their trips"
    ON activities FOR SELECT
    USING (
        EXISTS (
            SELECT 1 FROM trips
            WHERE trips.id = activities.trip_id
            AND trips.user_id = auth.uid()
            AND trips.deleted_at IS NULL
        )
    );

CREATE POLICY "Users can view activities for shared trips"
    ON activities FOR SELECT
    USING (
        EXISTS (
            SELECT 1 FROM trip_shares
            WHERE trip_shares.trip_id = activities.trip_id
            AND trip_shares.shared_with = auth.uid()
            AND trip_shares.accepted_at IS NOT NULL
        )
    );

CREATE POLICY "Users can create activities for their trips"
    ON activities FOR INSERT
    WITH CHECK (
        EXISTS (
            SELECT 1 FROM trips
            WHERE trips.id = activities.trip_id
            AND trips.user_id = auth.uid()
            AND trips.deleted_at IS NULL
        )
    );

CREATE POLICY "Users can update activities for their trips"
    ON activities FOR UPDATE
    USING (
        EXISTS (
            SELECT 1 FROM trips
            WHERE trips.id = activities.trip_id
            AND trips.user_id = auth.uid()
            AND trips.deleted_at IS NULL
        )
    );

CREATE POLICY "Users can delete activities for their trips"
    ON activities FOR DELETE
    USING (
        EXISTS (
            SELECT 1 FROM trips
            WHERE trips.id = activities.trip_id
            AND trips.user_id = auth.uid()
            AND trips.deleted_at IS NULL
        )
    );

-- Trip shares policies
CREATE POLICY "Users can view shares for their trips"
    ON trip_shares FOR SELECT
    USING (
        EXISTS (
            SELECT 1 FROM trips
            WHERE trips.id = trip_shares.trip_id
            AND trips.user_id = auth.uid()
        )
        OR shared_with = auth.uid()
    );

CREATE POLICY "Users can create shares for their trips"
    ON trip_shares FOR INSERT
    WITH CHECK (
        EXISTS (
            SELECT 1 FROM trips
            WHERE trips.id = trip_shares.trip_id
            AND trips.user_id = auth.uid()
        )
        AND shared_by = auth.uid()
    );

-- AI import logs policies
CREATE POLICY "Users can view their own import logs"
    ON ai_import_logs FOR SELECT
    USING (auth.uid() = user_id);

CREATE POLICY "Users can create their own import logs"
    ON ai_import_logs FOR INSERT
    WITH CHECK (auth.uid() = user_id);

-- Audit logs policies (service role only for writing)
CREATE POLICY "Users can view their own audit logs"
    ON audit_logs FOR SELECT
    USING (auth.uid() = user_id);

-- Grant permissions
GRANT ALL ON ALL TABLES IN SCHEMA public TO service_role;
GRANT ALL ON ALL SEQUENCES IN SCHEMA public TO service_role;
GRANT ALL ON ALL FUNCTIONS IN SCHEMA public TO service_role;

-- Create useful functions for the application
CREATE OR REPLACE FUNCTION get_user_trip_stats(p_user_id UUID)
RETURNS TABLE (
    total_trips BIGINT,
    upcoming_trips BIGINT,
    in_progress_trips BIGINT,
    completed_trips BIGINT,
    total_activities BIGINT
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        COUNT(DISTINCT t.id) as total_trips,
        COUNT(DISTINCT CASE WHEN t.start_date > CURRENT_DATE THEN t.id END) as upcoming_trips,
        COUNT(DISTINCT CASE WHEN t.start_date <= CURRENT_DATE AND t.end_date >= CURRENT_DATE THEN t.id END) as in_progress_trips,
        COUNT(DISTINCT CASE WHEN t.end_date < CURRENT_DATE THEN t.id END) as completed_trips,
        COUNT(DISTINCT a.id) as total_activities
    FROM trips t
    LEFT JOIN activities a ON t.id = a.trip_id
    WHERE t.user_id = p_user_id
    AND t.deleted_at IS NULL;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Add check for email confirmation settings
ALTER TABLE auth.users REPLICA IDENTITY FULL;

COMMIT;