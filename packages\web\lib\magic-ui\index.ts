/**
 * Magic UI Components - Organized animation library
 * Consolidates all Magic UI components with consistent API
 */

// Core animation components
export { ShimmerButton } from '@/components/magic-ui/shimmer-button';
export { BorderBeam, BorderBeamSubtle } from '@/components/magic-ui/border-beam';
export { AnimatedShinyText } from '@/components/magic-ui/animated-shiny-text';

// Interactive components
export { MagicCard, activityGlowColors } from '@/components/magic-ui/magic-card';
export { AnimatedBeam, AnimatedBeamSimple } from '@/components/magic-ui/animated-beam';
export { AnimatedList } from '@/components/magic-ui/animated-list';

// Data display
export { NumberTicker } from '@/components/magic-ui/number-ticker';
export { DynamicNumberTicker } from '@/components/magic-ui/number-ticker-dynamic';
export { MorphingText } from '@/components/magic-ui/morphing-text';

// Layout components
export { BentoGrid, BentoCard, BentoCardSizes, BentoCardSkeleton } from '@/components/magic-ui/bento-grid';

// 3D components
export { Globe } from '@/components/magic-ui/globe';
export { GlobeDynamic } from '@/components/magic-ui/globe-dynamic';

// Export types for 3D components
export type { GlobeCity, GlobeArc } from '@/components/magic-ui/globe';

// Animation presets for common use cases
export const magicAnimations = {
  // Button animations
  shimmerButton: {
    backgroundImage: 'linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent)',
    backgroundSize: '200% 100%',
    animation: 'shimmer 2s infinite',
  },
  
  // Text animations
  shinyText: {
    backgroundImage: 'linear-gradient(120deg, rgba(120, 119, 198, 0.3) 0%, rgba(255, 255, 255, 0.3) 50%, rgba(120, 119, 198, 0.3) 100%)',
    backgroundSize: '200% 100%',
    animation: 'shine 2s infinite',
  },
  
  // Border animations
  borderBeam: {
    position: 'relative',
    overflow: 'hidden',
  },
  
  // Card animations
  magicCard: {
    background: 'linear-gradient(45deg, transparent, rgba(255,255,255,0.1), transparent)',
    backgroundSize: '200% 200%',
    animation: 'gradient 3s ease infinite',
  },
} as const;

// Animation keyframes that need to be added to CSS
export const magicKeyframes = `
@keyframes shimmer {
  0% { background-position: 200% 0; }
  100% { background-position: -200% 0; }
}

@keyframes shine {
  0% { background-position: 200% 0; }
  100% { background-position: -200% 0; }
}

@keyframes gradient {
  0%, 100% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

@keyframes pulse-ring {
  0% { transform: scale(0.33); }
  80%, 100% { opacity: 0; }
}

@keyframes pulse-dot {
  0% { transform: scale(0.8); }
  50% { transform: scale(1.0); }
  100% { transform: scale(0.8); }
}
`;

// Common animation configurations
export const magicConfig = {
  // Duration presets
  durations: {
    fast: '0.3s',
    normal: '0.5s',
    slow: '1s',
    slower: '2s',
  },
  
  // Easing presets
  easings: {
    smooth: 'cubic-bezier(0.4, 0, 0.2, 1)',
    bounce: 'cubic-bezier(0.68, -0.55, 0.265, 1.55)',
    elastic: 'cubic-bezier(0.25, 0.46, 0.45, 0.94)',
  },
  
  // Color presets for consistency
  colors: {
    shimmer: 'rgba(255, 255, 255, 0.2)',
    glow: 'rgba(59, 130, 246, 0.5)',
    success: 'rgba(34, 197, 94, 0.5)',
    warning: 'rgba(251, 191, 36, 0.5)',
    error: 'rgba(239, 68, 68, 0.5)',
  },
} as const;