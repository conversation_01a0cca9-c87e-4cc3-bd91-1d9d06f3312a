# Claude AI Assistant Guide

This file provides essential guidance for the Claude AI assistant when working in the TravelViz repository.

---

### 🔮 **1. Core Project Understanding**

- **Project Goal:** TravelViz transforms AI-generated travel chats into visual, shareable, and bookable itineraries.
- **Primary Documentation:**
  - For a complete product overview, user personas, and feature requirements, **read `travelviz-prd-v2.md`**.
  - For setup and a general project overview, **refer to the root `README.md`**.
- **Core Principle:** Your primary goal is to maintain and extend the application's functionality while strictly adhering to its existing architecture and conventions.

---

### 🏛️ **2. Architecture & Technology**

- **Hub-Centric Architecture:** The system is a monorepo with a central API hub. **All business logic and data access MUST go through the `@travelviz/hub` package.** Clients (`web`, `mobile`) do not access the database or external services directly.
- **Key Packages:**
  - `@travelviz/hub`: The brain. **Express.js** API. All logic lives here.
  - `@travelviz/web`: The UI. **Next.js 14** with App Router.
  - `@travelviz/shared`: The glue. Shared **TypeScript types, Zod schemas,** and utils.
- **Type Safety:** Always import shared types, interfaces, and validation schemas from `@travelviz/shared` to ensure consistency.

---

### 🛠️ **3. Development Workflow & Commands**

- **Package Manager:** This project uses `pnpm`. Use `pnpm` for all package management and script execution.
- **Primary Commands:**
  - `pnpm dev`: Runs `web` and `hub` services concurrently.
  - `pnpm build`: Builds all workspace packages.
  - `pnpm lint`: Lints all packages.
  - `pnpm type-check`: Runs TypeScript checks across the workspace.
  - `pnpm test`: Runs all Vitest tests.
- **Commits:** Use **Conventional Commits**. (`feat:`, `fix:`, `docs:`, `chore:`, etc.). This is enforced by `commitlint`.

---

### ✅ **4. Testing & Quality (MANDATORY)**

- **Pre-Commit Checks:** Before committing, you **MUST** ensure your changes pass all local CI checks. Run the following commands:
  1. `pnpm lint`
  2. `pnpm type-check`
  3. `pnpm test`
- **NEVER Use --no-verify:** The `--no-verify` flag is **STRICTLY FORBIDDEN** for any git commits. All pre-commit hooks must pass. If there are errors, fix them before committing. This ensures code quality and prevents broken code from entering the repository.
- **Create Tests:** For any new feature or bug fix, you must add or update corresponding tests. Use the test generation script for new files: `pnpm test:create <path_to_component_or_file>`.

---

### 📜 **5. Code Style & Patterns**

- **Follow Existing Patterns:** Mimic the style, structure, and naming conventions of the existing code in the package you are working in.
- **Error Handling:** Use the shared response utilities (`createSuccessResponse`, `createErrorResponse`) in the `@travelviz/hub` for consistent API responses.
- **Validation:** Use the **Zod schemas** from `@travelviz/shared` for all data validation (API inputs, forms, etc.).
- **Environment Variables:** Do not commit secrets. Use the `.env.example` files as templates. All necessary development credentials are in `.env.local` files in each package directory.
  - **Test environments automatically load `.env.local` files**, so tests will use the same credentials as development.
  - The test setup files (`test/setup.ts`) handle this loading automatically.

---

### 🧠 **6. AI Behavior Rules**

- **Analyze First:** Before writing code, always read the relevant files to understand the context.
- **No Assumptions:** Do not assume a library or framework is available. Verify its usage in `package.json` or existing files.
- **Ask Questions:** If requirements in the PRD or user request are unclear, ask for clarification before proceeding.
- **Verify Your Work:** After making changes, always run the relevant tests and checks to confirm you have not introduced any regressions.
- **Focus on the Task:** Adhere to the specific task requested. Do not refactor or modify unrelated code.

---

### 🔒 **7. STRICT TECH STACK CONSTRAINTS**

This project uses a **CAREFULLY SELECTED, MINIMAL** tech stack. **DO NOT suggest additional services or infrastructure.**

#### **Approved Services Only:**

- **Frontend:** Vercel (includes edge functions, analytics, security headers)
- **Backend:** Render (Node.js hosting with built-in metrics)
- **Database:** Supabase (PostgreSQL with RLS, real-time, auth)
- **Redis:** Upstash (serverless Redis with connection pooling)
- **AI:** OpenRouter and Google Gemini (already optimized for $0/month)
- **Maps:** Google Maps and Mapbox APIs

#### **AI Model Selection Flow (Finalized):**

The project uses a cascading fallback system to ensure $0/month AI costs:

1. **Primary Model:** Gemini Flash 2.0 via Google API (Free)
   - Model ID: `gemini-2.0-flash-exp`
   - Direct Google API integration for maximum free usage
   - Fast, high quality, and completely free

2. **First Fallback:** DeepSeek Chat v3 via OpenRouter (Free tier)
   - Model ID: `deepseek/deepseek-chat-v3-0324:free`
   - Free tier on OpenRouter when Google API quota is exhausted
   - Good quality and reasonable speed

3. **Second Fallback:** Gemini Flash 2.0 via OpenRouter (Very cheap)
   - Model ID: `google/gemini-2.0-flash-001`
   - Only $0.05 per million tokens as last resort
   - Used when both free options are maxed out

**Note:** No testing of model comparison is needed - this flow is finalized and optimized for cost.

#### **Security Implementation:**

- Use Vercel's built-in security headers
- Leverage Supabase RLS for data access control
- Implement input sanitization with DOMPurify (npm package)
- Use platform-native monitoring tools

#### **Why These Constraints:**

1. Avoids infrastructure complexity
2. Reduces operational overhead
3. Maintains $0/month AI costs
4. Each service is best-in-class for its purpose
5. All have generous free tiers

#### **DO NOT SUGGEST:**

- Service meshes (Istio, Linkerd)
- Container orchestration (Kubernetes, Docker Swarm)
- Monitoring stacks (Prometheus, Grafana)
- Security tools (Falco, RASP)
- Edge computing platforms (Cloudflare Workers)
- Additional databases or caching layers
- Any service not listed in "Approved Services Only"

**When fixing issues, use the capabilities of existing services first.**
