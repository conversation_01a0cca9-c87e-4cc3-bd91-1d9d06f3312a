import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { 
  isValidUUID, 
  formatDate, 
  parseDate, 
  slugify, 
  capitalize,
  ErrorLogger 
} from './utils';

describe('Utility Functions', () => {
  describe('isValidUUID', () => {
    it('should validate correct UUIDs', () => {
      expect(isValidUUID('550e8400-e29b-41d4-a716-************')).toBe(true);
      expect(isValidUUID('6ba7b810-9dad-11d1-80b4-00c04fd430c8')).toBe(true);
      expect(isValidUUID('6ba7b811-9dad-11d1-80b4-00c04fd430c8')).toBe(true);
      expect(isValidUUID('6ba7b812-9dad-11d1-80b4-00c04fd430c8')).toBe(true);
      expect(isValidUUID('6ba7b814-9dad-11d1-80b4-00c04fd430c8')).toBe(true);
    });

    it('should reject invalid UUIDs', () => {
      expect(isValidUUID('invalid-uuid')).toBe(false);
      expect(isValidUUID('550e8400-e29b-41d4-a716-44665544000g')).toBe(false); // invalid character
      expect(isValidUUID('550e8400-e29b-41d4-a716-4466554400')).toBe(false); // too short
      expect(isValidUUID('550e8400-e29b-41d4-a716-************00')).toBe(false); // too long
      expect(isValidUUID('')).toBe(false);
    });

    it('should handle case insensitive UUIDs', () => {
      expect(isValidUUID('550E8400-E29B-41D4-A716-************')).toBe(true);
      expect(isValidUUID('550e8400-E29B-41d4-A716-************')).toBe(true);
    });
  });

  describe('formatDate', () => {
    it('should format date to ISO string', () => {
      const date = new Date('2024-01-15T10:30:00.000Z');
      expect(formatDate(date)).toBe('2024-01-15T10:30:00.000Z');
    });

    it('should handle different dates', () => {
      const date1 = new Date(0); // Unix epoch
      expect(formatDate(date1)).toBe('1970-01-01T00:00:00.000Z');

      const date2 = new Date('2023-12-31T23:59:59.999Z');
      expect(formatDate(date2)).toBe('2023-12-31T23:59:59.999Z');
    });
  });

  describe('parseDate', () => {
    it('should parse ISO date string', () => {
      const dateString = '2024-01-15T10:30:00.000Z';
      const date = parseDate(dateString);
      expect(date.toISOString()).toBe(dateString);
    });

    it('should handle various date formats', () => {
      const date1 = parseDate('2024-01-15');
      expect(date1.getFullYear()).toBe(2024);
      expect(date1.getMonth()).toBe(0); // January is 0

      const date2 = parseDate('2024-12-31T23:59:59.999Z');
      expect(date2.getFullYear()).toBe(2024);
      expect(date2.getMonth()).toBe(11); // December is 11
    });
  });

  describe('slugify', () => {
    it('should convert text to slug', () => {
      expect(slugify('Hello World')).toBe('hello-world');
      expect(slugify('This is a Test')).toBe('this-is-a-test');
    });

    it('should remove special characters', () => {
      expect(slugify('Hello! World?')).toBe('hello-world');
      expect(slugify('Test@#$%^&*()')).toBe('test');
    });

    it('should handle multiple spaces and dashes', () => {
      expect(slugify('Hello   World')).toBe('hello-world');
      expect(slugify('Test--Case')).toBe('test-case');
    });

    it('should trim dashes from start and end', () => {
      expect(slugify('-Hello World-')).toBe('hello-world');
      expect(slugify('---Test---')).toBe('test');
    });

    it('should handle empty and edge cases', () => {
      expect(slugify('')).toBe('');
      expect(slugify('123')).toBe('123');
      expect(slugify('CamelCase')).toBe('camelcase');
    });
  });

  describe('capitalize', () => {
    it('should capitalize first letter', () => {
      expect(capitalize('hello')).toBe('Hello');
      expect(capitalize('world')).toBe('World');
    });

    it('should handle already capitalized strings', () => {
      expect(capitalize('Hello')).toBe('Hello');
      expect(capitalize('HELLO')).toBe('HELLO');
    });

    it('should handle edge cases', () => {
      expect(capitalize('')).toBe('');
      expect(capitalize('a')).toBe('A');
      expect(capitalize('123')).toBe('123');
    });
  });

  describe('ErrorLogger', () => {
    let logger: ErrorLogger;
    let consoleErrorSpy: import('vitest').MockInstance<typeof console.error>;
    let consoleWarnSpy: import('vitest').MockInstance<typeof console.warn>;
    let consoleInfoSpy: import('vitest').MockInstance<typeof console.info>;

    beforeEach(() => {
      // Reset singleton instance
      (ErrorLogger as unknown as { instance: undefined }).instance = undefined;
      logger = ErrorLogger.getInstance();
      
      // Spy on console methods
      consoleErrorSpy = vi.spyOn(console, 'error').mockImplementation(() => {});
      consoleWarnSpy = vi.spyOn(console, 'warn').mockImplementation(() => {});
      consoleInfoSpy = vi.spyOn(console, 'info').mockImplementation(() => {});
      
      // Set NODE_ENV to development for testing
      process.env.NODE_ENV = 'development';
    });

    afterEach(() => {
      vi.restoreAllMocks();
    });

    it('should be a singleton', () => {
      const logger1 = ErrorLogger.getInstance();
      const logger2 = ErrorLogger.getInstance();
      expect(logger1).toBe(logger2);
    });

    it('should log errors with context', () => {
      const error = new Error('Test error');
      const context = { userId: '123', action: 'test' };
      
      logger.logError(error, context, 'TestComponent');
      
      expect(consoleErrorSpy).toHaveBeenCalledWith(
        '[ErrorLogger]',
        expect.objectContaining({
          message: 'Test error',
          level: 'error',
          component: 'TestComponent',
          context
        })
      );
    });

    it('should log warnings', () => {
      const message = 'Test warning';
      const context = { severity: 'low' };
      
      logger.logWarning(message, context, 'TestComponent');
      
      expect(consoleWarnSpy).toHaveBeenCalledWith(
        '[ErrorLogger]',
        expect.objectContaining({
          message: 'Test warning',
          level: 'warn',
          component: 'TestComponent',
          context
        })
      );
    });

    it('should log info messages', () => {
      const message = 'Test info';
      const context = { detail: 'some detail' };
      
      logger.logInfo(message, context, 'TestComponent');
      
      expect(consoleInfoSpy).toHaveBeenCalledWith(
        '[ErrorLogger]',
        expect.objectContaining({
          message: 'Test info',
          level: 'info',
          component: 'TestComponent',
          context
        })
      );
    });

    it('should get recent logs', () => {
      logger.logError(new Error('Error 1'));
      logger.logWarning('Warning 1');
      logger.logInfo('Info 1');
      
      const logs = logger.getRecentLogs();
      expect(logs).toHaveLength(3);
      expect(logs[0].message).toBe('Error 1');
      expect(logs[1].message).toBe('Warning 1');
      expect(logs[2].message).toBe('Info 1');
    });

    it('should get specific number of recent logs', () => {
      logger.logError(new Error('Error 1'));
      logger.logWarning('Warning 1');
      logger.logInfo('Info 1');
      logger.logError(new Error('Error 2'));
      logger.logInfo('Info 2');
      
      const recentLogs = logger.getRecentLogs(3);
      expect(recentLogs).toHaveLength(3);
      // Should get the 3 most recent logs
      expect(recentLogs[0].message).toBe('Info 1');
      expect(recentLogs[1].message).toBe('Error 2');
      expect(recentLogs[2].message).toBe('Info 2');
    });

    it('should clear logs', () => {
      logger.logError(new Error('Error 1'));
      logger.logWarning('Warning 1');
      
      logger.clearLogs();
      
      const logs = logger.getRecentLogs();
      expect(logs).toHaveLength(0);
    });

    it('should not log in production mode', () => {
      process.env.NODE_ENV = 'production';
      
      logger.logError(new Error('Production error'));
      
      expect(consoleErrorSpy).not.toHaveBeenCalled();
    });

    it('should maintain max logs limit', () => {
      // The maxLogs is set to 100 in the ErrorLogger class
      for (let i = 0; i < 150; i++) {
        logger.logInfo(`Log ${i}`);
      }
      
      const logs = logger.getRecentLogs();
      expect(logs.length).toBeLessThanOrEqual(100);
    });
  });
});