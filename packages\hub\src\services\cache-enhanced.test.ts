import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { CacheService } from './cache.service';
import { redisConnectionPool } from './redis-connection-pool.service';
import { logger } from '../utils/logger';
import { 
  advanceTimeAndFlushPromises,
  createMockRedisClient
} from '../../test/utils/test-helpers';

// Mock dependencies
vi.mock('./redis-connection-pool.service', () => ({
  redisConnectionPool: {
    execute: vi.fn(),
    healthCheck: vi.fn().mockResolvedValue(true),
  }
}));
vi.mock('../utils/logger', () => ({
  logger: {
    error: vi.fn(),
    warn: vi.fn(),
    info: vi.fn(),
    debug: vi.fn(),
  }
}));

describe('CacheService - Enhanced Tests', () => {
  let cacheService: CacheService;
  let mockRedis: any;

  beforeEach(async () => {
    vi.clearAllMocks();
    vi.useFakeTimers();
    
    // Set up environment
    process.env.UPSTASH_REDIS_URL = 'https://test-redis.upstash.io';
    process.env.UPSTASH_REDIS_TOKEN = 'test-token';
    
    // Reset singleton
    (CacheService as any).instance = undefined;
    
    // Mock Redis client
    mockRedis = createMockRedisClient();
    vi.mocked(redisConnectionPool.execute).mockImplementation(async (fn) => {
      return fn(mockRedis);
    });

    cacheService = CacheService.getInstance();
    
    // Wait for initialization
    await new Promise(resolve => setImmediate(resolve));
  });

  afterEach(() => {
    vi.useRealTimers();
    delete process.env.UPSTASH_REDIS_URL;
    delete process.env.UPSTASH_REDIS_TOKEN;
  });

  describe('TTL Expiration', () => {
    it('should respect TTL when setting cache values', async () => {
      const key = 'test-key';
      const value = { data: 'test-value' };
      const ttl = 3600; // 1 hour

      await cacheService.set(key, value, { ttl });

      expect(mockRedis.set).toHaveBeenCalledWith(
        'travelviz:test-key',
        JSON.stringify(value),
        { ex: ttl }
      );
    });

    it('should use default TTL when not specified', async () => {
      const key = 'test-key';
      const value = 'test-value';

      await cacheService.set(key, value);

      expect(mockRedis.set).toHaveBeenCalledWith(
        'travelviz:test-key',
        JSON.stringify(value),
        { ex: 3600 } // Default 1 hour
      );
    });

    it('should handle expired cache gracefully', async () => {
      const key = 'test-key';
      const value = 'test-value';
      
      // Set value with short TTL
      await cacheService.set(key, value, { ttl: 1 });
      
      // Mock Redis returning null (expired)
      mockRedis.get.mockResolvedValueOnce(null);
      
      const result = await cacheService.get(key);
      
      expect(result).toBeNull();
      expect(logger.debug).toHaveBeenCalledWith('[Cache] Miss', { keyLength: expect.any(Number) });
    });

    it('should support custom TTL per namespace', async () => {
      const testCases = [
        { namespace: 'parser', ttl: 86400 },    // 24 hours
        { namespace: 'usage', ttl: 3600 },      // 1 hour
        { namespace: 'session', ttl: 300 }      // 5 minutes
      ];

      for (const testCase of testCases) {
        await cacheService.set('key', 'value', {
          namespace: testCase.namespace,
          ttl: testCase.ttl
        });

        expect(mockRedis.set).toHaveBeenCalledWith(
          `travelviz:${testCase.namespace}:key`,
          JSON.stringify('value'),
          { ex: testCase.ttl }
        );
      }
    });
  });

  describe('Memory Pressure Handling', () => {
    it('should handle large content without caching', async () => {
      const largeContent = 'x'.repeat(10000); // 10KB content
      const key = 'large-content';
      
      // Simulate memory pressure by making set fail
      mockRedis.set.mockRejectedValueOnce(new Error('OOM command not allowed'));
      
      const result = await cacheService.set(key, largeContent);
      
      expect(result).toBe(false);
      expect(logger.error).toHaveBeenCalledWith(
        '[Cache] Set error',
        expect.objectContaining({
          error: expect.any(Error),
          keyLength: expect.any(Number)
        })
      );
    });

    it('should implement cache size limits', async () => {
      const stats = await cacheService.getStats();
      
      // Verify stats tracking
      expect(stats).toHaveProperty('hits');
      expect(stats).toHaveProperty('misses');
      expect(stats).toHaveProperty('errors');
    });

    it('should gracefully degrade under memory pressure', async () => {
      // Simulate multiple set failures
      mockRedis.set
        .mockRejectedValueOnce(new Error('Memory limit exceeded'))
        .mockRejectedValueOnce(new Error('Memory limit exceeded'))
        .mockResolvedValueOnce('OK');

      // First two attempts should fail
      expect(await cacheService.set('key1', 'value1')).toBe(false);
      expect(await cacheService.set('key2', 'value2')).toBe(false);
      
      // Third attempt succeeds (simulating memory freed)
      expect(await cacheService.set('key3', 'value3')).toBe(true);
      
      // Verify error logging
      expect(logger.error).toHaveBeenCalledTimes(2);
    });
  });

  describe('Connection Pool Integration', () => {
    it('should use connection pool for all operations', async () => {
      await cacheService.get('test-key');
      await cacheService.set('test-key', 'value');
      await cacheService.del('test-key');

      expect(redisConnectionPool.execute).toHaveBeenCalledTimes(3);
    });

    it('should handle connection pool failures', async () => {
      vi.mocked(redisConnectionPool.execute).mockRejectedValueOnce(
        new Error('Connection pool exhausted')
      );

      const result = await cacheService.get('test-key');
      
      expect(result).toBeNull();
      expect(logger.error).toHaveBeenCalledWith(
        '[Cache] Get error',
        expect.objectContaining({
          error: expect.any(Error)
        })
      );
    });

    it('should disable cache when pool health check fails', async () => {
      // Reset instance
      (CacheService as any).instance = undefined;
      
      // Mock failed health check
      vi.mocked(redisConnectionPool.healthCheck).mockResolvedValueOnce(false);
      
      const newCacheService = CacheService.getInstance();
      await new Promise(resolve => setImmediate(resolve));
      
      // Cache should be disabled
      expect(await newCacheService.get('any-key')).toBeNull();
      expect(await newCacheService.set('any-key', 'value')).toBe(false);
      
      expect(logger.error).toHaveBeenCalledWith(
        '[Cache] Redis connection pool health check failed'
      );
    });
  });

  describe('Namespace Management', () => {
    it('should properly namespace cache keys', async () => {
      const testCases = [
        { key: 'user:123', namespace: 'session', expected: 'travelviz:session:user:123' },
        { key: 'trip:456', namespace: 'parser', expected: 'travelviz:parser:trip:456' },
        { key: 'stats', namespace: undefined, expected: 'travelviz:stats' }
      ];

      for (const testCase of testCases) {
        await cacheService.get(testCase.key, { namespace: testCase.namespace });
        
        expect(mockRedis.get).toHaveBeenCalledWith(testCase.expected);
      }
    });

    it('should handle namespace collisions', async () => {
      // Set values in different namespaces with same key
      await cacheService.set('config', { type: 'user' }, { namespace: 'user' });
      await cacheService.set('config', { type: 'system' }, { namespace: 'system' });
      
      // Verify separate storage
      expect(mockRedis.set).toHaveBeenCalledWith(
        'travelviz:user:config',
        JSON.stringify({ type: 'user' }),
        expect.any(Object)
      );
      expect(mockRedis.set).toHaveBeenCalledWith(
        'travelviz:system:config',
        JSON.stringify({ type: 'system' }),
        expect.any(Object)
      );
    });
  });

  describe('Cache Statistics', () => {
    it('should track cache hits accurately', async () => {
      mockRedis.get.mockResolvedValue(JSON.stringify('cached-value'));
      
      const initialStats = await cacheService.getStats();
      const initialHits = initialStats.hits;
      
      // Generate hits
      await cacheService.get('key1');
      await cacheService.get('key2');
      await cacheService.get('key3');
      
      const finalStats = await cacheService.getStats();
      expect(finalStats.hits).toBe(initialHits + 3);
    });

    it('should track cache misses accurately', async () => {
      mockRedis.get.mockResolvedValue(null);
      
      const initialStats = await cacheService.getStats();
      const initialMisses = initialStats.misses;
      
      // Generate misses
      await cacheService.get('missing1');
      await cacheService.get('missing2');
      
      const finalStats = await cacheService.getStats();
      expect(finalStats.misses).toBe(initialMisses + 2);
    });

    it('should track errors accurately', async () => {
      vi.mocked(redisConnectionPool.execute).mockRejectedValue(new Error('Redis error'));
      
      const initialStats = await cacheService.getStats();
      const initialErrors = initialStats.errors;
      
      // Generate errors
      await cacheService.get('key');
      await cacheService.set('key', 'value');
      await cacheService.del('key');
      
      const finalStats = await cacheService.getStats();
      expect(finalStats.errors).toBe(initialErrors + 3);
    });
  });

  describe('Delete Operations', () => {
    it('should delete keys with namespace support', async () => {
      await cacheService.del('user:123', 'session');
      
      expect(mockRedis.del).toHaveBeenCalledWith('travelviz:session:user:123');
    });

    it('should handle delete failures gracefully', async () => {
      mockRedis.del.mockRejectedValueOnce(new Error('Delete failed'));
      
      const result = await cacheService.del('key');
      
      expect(result).toBe(false);
      expect(logger.error).toHaveBeenCalledWith(
        '[Cache] Delete error',
        expect.objectContaining({
          error: expect.any(Error)
        })
      );
    });

    it('should support pattern-based deletion', async () => {
      // Clear all keys with a specific pattern
      const pattern = 'session:*';
      await cacheService.clear(pattern);
      
      expect(logger.debug).toHaveBeenCalledWith(
        '[Cache] Pattern clear',
        expect.objectContaining({
          pattern,
          namespace: undefined
        })
      );
    });
  });

  describe('Complex Data Types', () => {
    it('should handle nested objects', async () => {
      const complexData = {
        user: { id: '123', name: 'Test User' },
        trips: [
          { id: 'trip1', title: 'Paris Trip' },
          { id: 'trip2', title: 'Tokyo Trip' }
        ],
        metadata: {
          created: new Date().toISOString(),
          version: 2
        }
      };

      await cacheService.set('complex', complexData);
      
      expect(mockRedis.set).toHaveBeenCalledWith(
        'travelviz:complex',
        JSON.stringify(complexData),
        expect.any(Object)
      );
    });

    it('should handle circular references gracefully', async () => {
      const obj: any = { name: 'test' };
      obj.circular = obj; // Create circular reference
      
      // Should handle circular reference error
      await cacheService.set('circular', obj);
      
      expect(logger.error).toHaveBeenCalledWith(
        '[Cache] Set error',
        expect.objectContaining({
          error: expect.any(Error)
        })
      );
    });
  });

  describe('Edge Cases', () => {
    it('should handle empty or null values', async () => {
      const testCases = [
        { key: 'null-value', value: null },
        { key: 'undefined-value', value: undefined },
        { key: 'empty-string', value: '' },
        { key: 'empty-object', value: {} },
        { key: 'empty-array', value: [] }
      ];

      for (const testCase of testCases) {
        const result = await cacheService.set(testCase.key, testCase.value);
        expect(result).toBe(true);
      }
    });

    it('should handle very long keys', async () => {
      const longKey = 'x'.repeat(1000);
      
      await cacheService.set(longKey, 'value');
      
      // Should truncate or hash long keys
      const call = mockRedis.set.mock.calls[0];
      expect(call[0].length).toBeLessThan(1100); // Reasonable key length
    });

    it('should recover from Redis restart', async () => {
      // Simulate Redis connection lost
      vi.mocked(redisConnectionPool.execute)
        .mockRejectedValueOnce(new Error('Connection refused'))
        .mockImplementation(async (fn) => fn(mockRedis));
      
      // First call fails
      expect(await cacheService.get('key')).toBeNull();
      
      // Second call succeeds (connection restored)
      mockRedis.get.mockResolvedValueOnce(JSON.stringify('value'));
      expect(await cacheService.get('key')).toBe('value');
    });
  });
});