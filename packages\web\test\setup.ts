import '@testing-library/jest-dom';
import { expect, afterEach, vi, beforeAll, afterAll } from 'vitest';
import { cleanup } from '@testing-library/react';
import * as matchers from '@testing-library/jest-dom/matchers';
import { server } from './mocks/server';
import { config } from 'dotenv';
import { resolve } from 'path';

// Type declarations for global test variables
declare global {
  interface Window {
    posthog?: unknown;
  }
  var posthog: unknown;
}

// Load environment variables from .env.local if it exists
config({ path: resolve(__dirname, '../.env.local') });

// Extend Vitest's expect with jest-dom matchers
expect.extend(matchers);

// Setup MSW
beforeAll(() => {
  server.listen({ onUnhandledRequest: 'error' });
});

afterEach(() => {
  cleanup();
  server.resetHandlers();
  // Clear all mocks to free memory
  vi.clearAllMocks();
  vi.resetModules();
  // Force garbage collection if available (V8 only)
  if (global.gc) {
    global.gc();
  }
});

afterAll(() => {
  server.close();
  // Final cleanup
  vi.clearAllMocks();
  vi.resetModules();
  if (global.gc) {
    global.gc();
  }
});

// Mock Next.js router
vi.mock('next/navigation', () => ({
  useRouter() {
    return {
      push: vi.fn(),
      replace: vi.fn(),
      prefetch: vi.fn(),
      back: vi.fn(),
      forward: vi.fn(),
      refresh: vi.fn(),
    };
  },
  useSearchParams() {
    return new URLSearchParams();
  },
  usePathname() {
    return '/';
  },
  useParams() {
    return {};
  },
}));

// Mock environment variables - use values from .env.local if available
process.env = {
  ...process.env,
  NEXT_PUBLIC_SUPABASE_URL: process.env.NEXT_PUBLIC_SUPABASE_URL || 'http://localhost:54321',
  NEXT_PUBLIC_SUPABASE_ANON_KEY: process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || 'test-anon-key',
  NEXT_PUBLIC_API_URL: process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001',
  NEXT_PUBLIC_MAPBOX_ACCESS_TOKEN: process.env.NEXT_PUBLIC_MAPBOX_ACCESS_TOKEN || 'test_mapbox_not_real',
};

// Global test utilities
global.ResizeObserver = vi.fn().mockImplementation(() => ({
  observe: vi.fn(),
  unobserve: vi.fn(),
  disconnect: vi.fn(),
}));

global.IntersectionObserver = vi.fn().mockImplementation(() => ({
  observe: vi.fn(),
  unobserve: vi.fn(),
  disconnect: vi.fn(),
}));

// Add polyfills for Radix UI compatibility with happy-dom
if (!Element.prototype.hasPointerCapture) {
  Element.prototype.hasPointerCapture = vi.fn().mockReturnValue(false);
}

if (!Element.prototype.setPointerCapture) {
  Element.prototype.setPointerCapture = vi.fn();
}

if (!Element.prototype.releasePointerCapture) {
  Element.prototype.releasePointerCapture = vi.fn();
}

// Setup global document for happy-dom
if (typeof document === 'undefined') {
  // @ts-ignore
  global.document = window.document;
}

// Setup document.hidden property for tests
Object.defineProperty(document, 'hidden', {
  configurable: true,
  writable: true,
  value: false,
});

// Mock global posthog for tests to prevent analytics in test environment
(global as any).posthog = {
  init: vi.fn(),
  capture: vi.fn(),
  identify: vi.fn(),
  reset: vi.fn(),
  register: vi.fn(),
  get_distinct_id: vi.fn(() => 'test-distinct-id'),
};

// Also add to window for client-side tests
if (typeof window !== 'undefined') {
  window.posthog = global.posthog;
}

// Suppress console errors in tests
const originalError = console.error;
beforeAll(() => {
  console.error = (...args: unknown[]) => {
    if (
      typeof args[0] === 'string' &&
      args[0].includes('Warning: ReactDOM.render')
    ) {
      return;
    }
    originalError.call(console, ...args);
  };
});

afterAll(() => {
  console.error = originalError;
});