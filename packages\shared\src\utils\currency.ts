/**
 * Currency utilities for handling multi-currency calculations
 */

export interface CurrencyAmount {
  currency: string;
  amount: number;
}

/**
 * Groups prices by currency and calculates totals
 * @param items - Array of items with price and currency
 * @returns Array of currency totals
 */
export function groupByCurrency<T extends { price?: number; currency?: string }>(
  items: T[]
): CurrencyAmount[] {
  const currencyMap = new Map<string, number>();

  for (const item of items) {
    if (item.price && item.currency) {
      const currentAmount = currencyMap.get(item.currency) || 0;
      currencyMap.set(item.currency, currentAmount + item.price);
    }
  }

  // Convert to array and sort by currency code
  return Array.from(currencyMap.entries())
    .map(([currency, amount]) => ({ currency, amount }))
    .sort((a, b) => a.currency.localeCompare(b.currency));
}

/**
 * Formats currency amounts for display
 * @param amounts - Array of currency amounts
 * @returns Formatted string like "$500 USD, €300 EUR"
 */
export function formatCurrencyAmounts(amounts: CurrencyAmount[]): string {
  if (amounts.length === 0) {
    return '$0';
  }

  return amounts
    .map(({ currency, amount }) => {
      // Get currency symbol (basic mapping, can be expanded)
      const symbol = getCurrencySymbol(currency);
      return `${symbol}${amount.toFixed(0)} ${currency}`;
    })
    .join(', ');
}

/**
 * Gets currency symbol for common currencies
 * @param currency - Currency code (e.g., 'USD', 'EUR')
 * @returns Currency symbol
 */
export function getCurrencySymbol(currency: string): string {
  const symbols: Record<string, string> = {
    USD: '$',
    EUR: '€',
    GBP: '£',
    JPY: '¥',
    CNY: '¥',
    AUD: 'A$',
    CAD: 'C$',
    CHF: 'Fr',
    SEK: 'kr',
    NZD: 'NZ$',
    INR: '₹',
    SGD: 'S$',
    HKD: 'HK$',
    NOK: 'kr',
    MXN: '$',
    ZAR: 'R',
    BRL: 'R$',
    RUB: '₽',
    KRW: '₩',
    THB: '฿',
  };

  return symbols[currency.toUpperCase()] || currency + ' ';
}

/**
 * Checks if all amounts are in the same currency
 * @param amounts - Array of currency amounts
 * @returns True if all amounts are in the same currency
 */
export function isSingleCurrency(amounts: CurrencyAmount[]): boolean {
  if (amounts.length <= 1) return true;
  const firstCurrency = amounts[0].currency;
  return amounts.every(amount => amount.currency === firstCurrency);
}