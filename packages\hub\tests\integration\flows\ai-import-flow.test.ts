import { describe, it, expect, beforeAll, afterAll, afterEach } from 'vitest';
import { BaseIntegrationTest } from '../utils/base-integration-test';

/**
 * Integration test for AI Import Flow
 * Tests the complete journey from conversation import to trip creation
 * 
 * Critical Flow #1: AI Conversation Import
 * - User pastes ChatGPT/Claude/Gemini conversation
 * - System parses conversation with AI
 * - Creates structured trip with activities
 * - User can view and modify the result
 */
class AIImportFlowTest extends BaseIntegrationTest {
  
  async testCompleteImportFlow() {
    console.log('🤖 Testing complete AI import flow...');
    
    // Step 1: Import conversation
    const importResponse = await this.importConversation(
      this.fixtures.conversations.paris2Day, 
      'chatgpt'
    );

    this.expectResponsePattern(importResponse, this.fixtures.expectedResponses.successfulImport);
    
    const importId = importResponse.body.data.importId;
    console.log(`✅ Import started with ID: ${importId}`);

    // Step 2: Wait for AI processing
    await this.waitForProcessing(3000);

    // Step 3: Check import status
    const statusResponse = await this.server.get(`/api/v1/import/parse-simple/${importId}`);
    expect(statusResponse.status).toBe(200);
    
    console.log('📊 Import status:', statusResponse.body);
    
    // Should have trip data if processing completed
    if (statusResponse.body.data?.trip) {
      const tripId = statusResponse.body.data.trip.id;
      this.cleanup.trackTestData('trips', tripId);
      
      // Step 4: Verify trip was created with activities
      const tripResponse = await this.server.get(`/api/v1/trips/${tripId}`);
      expect(tripResponse.status).toBe(200);
      
      const trip = tripResponse.body.data.trip;
      expect(trip).toHaveProperty('title');
      expect(trip).toHaveProperty('destination');
      expect(trip.title).toContain('Paris');
      
      // Step 5: Check activities were created
      const activitiesResponse = await this.server.get(`/api/v1/trips/${tripId}/activities`);
      expect(activitiesResponse.status).toBe(200);
      
      const activities = activitiesResponse.body.data.activities;
      expect(Array.isArray(activities)).toBe(true);
      expect(activities.length).toBeGreaterThan(0);
      
      // Verify activity structure
      const firstActivity = activities[0];
      expect(firstActivity).toHaveProperty('title');
      expect(firstActivity).toHaveProperty('description');
      expect(firstActivity).toHaveProperty('startTime');
      
      console.log(`✅ Trip created with ${activities.length} activities`);
      return { trip, activities, importId };
    } else {
      console.log('⏳ Import still processing or failed');
      return { importId };
    }
  }

  async testMultipleConversationSources() {
    console.log('🔄 Testing multiple conversation sources...');
    
    const sources = [
      { source: 'chatgpt', conversation: this.fixtures.conversations.paris2Day },
      { source: 'claude', conversation: this.fixtures.conversations.rome3Day },
      { source: 'gemini', conversation: this.fixtures.conversations.tokyo5Day }
    ];

    const results = [];
    
    for (const { source, conversation } of sources) {
      console.log(`Testing ${source} import...`);
      
      const response = await this.importConversation(conversation, source);
      this.expectResponsePattern(response, this.fixtures.expectedResponses.successfulImport);
      
      results.push({
        source,
        importId: response.body.data.importId,
        success: true
      });
    }

    expect(results).toHaveLength(3);
    expect(results.every(r => r.success)).toBe(true);
    
    console.log('✅ All conversation sources supported');
    return results;
  }

  async testErrorHandling() {
    console.log('❌ Testing import error scenarios...');
    
    // Test invalid conversation
    const invalidResponse = await this.importConversation(
      this.fixtures.errorScenarios.invalidConversation,
      'chatgpt'
    );
    
    // Should either succeed with warning or fail gracefully
    expect([200, 400, 422]).toContain(invalidResponse.status);
    
    // Test empty content
    const emptyResponse = await this.server.post('/api/v1/import/parse-simple', {
      content: this.fixtures.errorScenarios.emptyContent,
      source: 'chatgpt'
    });
    
    expect(emptyResponse.status).toBe(400);
    expect(emptyResponse.body.success).toBe(false);
    
    console.log('✅ Error handling works correctly');
  }

  async testAuthenticationRequired() {
    console.log('🔒 Testing authentication requirements...');
    
    await this.testAuthRequired('/api/v1/import/parse-simple', 'POST', {
      content: this.fixtures.conversations.paris2Day,
      source: 'chatgpt'
    });
    
    await this.testAuthRequired('/api/v1/import/parse-simple/fake-id', 'GET');
    
    console.log('✅ Authentication properly enforced');
  }

  async testPerformanceWithLargeConversation() {
    console.log('⚡ Testing performance with large conversation...');
    
    const startTime = Date.now();
    
    const response = await this.importConversation(
      this.fixtures.performance.largeConversation,
      'chatgpt'
    );
    
    const importTime = Date.now() - startTime;
    
    this.expectResponsePattern(response, this.fixtures.expectedResponses.successfulImport);
    
    // Import should complete within reasonable time (30 seconds)
    expect(importTime).toBeLessThan(30000);
    
    console.log(`✅ Large conversation imported in ${importTime}ms`);
  }
}

describe('AI Import Flow - Integration Tests', () => {
  let test: AIImportFlowTest;

  beforeAll(async () => {
    test = new AIImportFlowTest();
    await test.setupTest();
  });

  afterAll(async () => {
    await test.teardownTest();
  });

  afterEach(async () => {
    await test.cleanupBetweenTests();
  });

  it('should complete full AI import flow from conversation to trip', async () => {
    const result = await test.testCompleteImportFlow();
    expect(result.importId).toBeDefined();
  });

  it('should support multiple conversation sources (ChatGPT, Claude, Gemini)', async () => {
    const results = await test.testMultipleConversationSources();
    expect(results).toHaveLength(3);
  });

  it('should handle import errors gracefully', async () => {
    await test.testErrorHandling();
  });

  it('should require authentication for all import endpoints', async () => {
    await test.testAuthenticationRequired();
  });

  it('should handle large conversations within performance limits', async () => {
    await test.testPerformanceWithLargeConversation();
  });

  it('should preserve conversation source metadata', async () => {
    const response = await test.importConversation(
      test.fixtures.conversations.paris2Day,
      'chatgpt'
    );
    
    test.expectResponsePattern(response, test.fixtures.expectedResponses.successfulImport);
    
    // Check that source information is preserved
    const importId = response.body.data.importId;
    await test.waitForProcessing(1000);
    
    const statusResponse = await test.server.get(`/api/v1/import/parse-simple/${importId}`);
    expect(statusResponse.status).toBe(200);
    
    // Verify source is tracked
    if (statusResponse.body.data?.import) {
      expect(statusResponse.body.data.import.source).toBe('chatgpt');
    }
  });

  it('should handle concurrent imports from same user', async () => {
    console.log('🔄 Testing concurrent imports...');
    
    const conversations = [
      test.fixtures.conversations.paris2Day,
      test.fixtures.conversations.rome3Day,
      test.fixtures.conversations.shortWeekend
    ];

    // Start multiple imports simultaneously
    const importPromises = conversations.map(conversation =>
      test.importConversation(conversation, 'chatgpt')
    );

    const responses = await Promise.all(importPromises);
    
    // All should succeed
    responses.forEach(response => {
      test.expectResponsePattern(response, test.fixtures.expectedResponses.successfulImport);
    });

    console.log(`✅ ${responses.length} concurrent imports completed`);
  });

  it('should validate conversation content before processing', async () => {
    const malformedResponse = await test.server.post('/api/v1/import/parse-simple', {
      content: test.fixtures.errorScenarios.malformedJson,
      source: 'chatgpt'
    });

    // Should handle malformed content gracefully
    expect([200, 400, 422]).toContain(malformedResponse.status);
    
    if (malformedResponse.status !== 200) {
      expect(malformedResponse.body.success).toBe(false);
      expect(malformedResponse.body.error).toBeDefined();
    }
  });
});