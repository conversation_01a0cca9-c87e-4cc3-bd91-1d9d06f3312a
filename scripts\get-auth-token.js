const axios = require('axios');

async function createTestUser() {
  try {
    console.log('👤 Creating test user...');

    const response = await axios.post('http://localhost:3001/api/v1/auth/signup', {
      email: '<EMAIL>',
      password: 'TestPassword123!',
      name: 'Test User'
    });

    if (response.data.success) {
      console.log('✅ Test user created successfully');
      console.log('Signup response:', JSON.stringify(response.data, null, 2));
      return response.data.data?.access_token;
    } else {
      console.log('ℹ️ User might already exist:', response.data.message);
      return null;
    }
  } catch (error) {
    console.log('ℹ️ User creation failed (might already exist):', error.response?.data?.message || error.message);
    return null;
  }
}

async function getAuthToken() {
  try {
    console.log('🔐 Getting auth token...');

    const response = await axios.post('http://localhost:3001/api/v1/auth/login', {
      email: '<EMAIL>',
      password: 'TestPassword123!'
    });

    if (response.data.success) {
      const token = response.data.data.access_token;
      console.log('✅ Auth token obtained');
      console.log('Token:', token);
      return token;
    } else {
      console.error('❌ Login failed:', response.data);
      return null;
    }
  } catch (error) {
    console.error('❌ Error getting auth token:', error.response?.data || error.message);
    return null;
  }
}

async function main() {
  // Try to create user first (will fail if exists)
  let token = await createTestUser();

  // If user creation didn't return a token, try login
  if (!token) {
    token = await getAuthToken();
  }

  if (token) {
    console.log('\n🎯 Use this token in debug script:');
    console.log(token);
  }

  return token;
}

main().catch(console.error);
