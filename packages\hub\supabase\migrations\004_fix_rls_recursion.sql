-- Fix RLS infinite recursion issue
-- Date: 2025-07-07

-- Drop existing policies that might cause recursion
DROP POLICY IF EXISTS "Users can view their own trips" ON public.trips;
DROP POLICY IF EXISTS "Users can insert their own trips" ON public.trips;
DROP POLICY IF EXISTS "Users can update their own trips" ON public.trips;
DROP POLICY IF EXISTS "Users can delete their own trips" ON public.trips;
DROP POLICY IF EXISTS "Anyone can view public trips" ON public.trips;

-- Recreate policies without recursion
-- For trips table
CREATE POLICY "Users can view their own trips"
  ON public.trips
  FOR SELECT
  USING (
    auth.uid() = user_id 
    OR visibility = 'public'
    OR (visibility = 'unlisted' AND share_slug IS NOT NULL)
  );

CREATE POLICY "Users can insert their own trips"
  ON public.trips
  FOR INSERT
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own trips"
  ON public.trips
  FOR UPDATE
  USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own trips"
  ON public.trips
  FOR DELETE
  USING (auth.uid() = user_id);

-- For activities table
DROP POLICY IF EXISTS "Users can view activities from their trips" ON public.activities;
DROP POLICY IF EXISTS "Users can insert activities to their trips" ON public.activities;
DROP POLICY IF EXISTS "Users can update activities from their trips" ON public.activities;
DROP POLICY IF EXISTS "Users can delete activities from their trips" ON public.activities;

CREATE POLICY "Users can view activities from their trips"
  ON public.activities
  FOR SELECT
  USING (
    EXISTS (
      SELECT 1 FROM public.trips
      WHERE trips.id = activities.trip_id
      AND (
        trips.user_id = auth.uid() 
        OR trips.visibility = 'public'
        OR (trips.visibility = 'unlisted' AND trips.share_slug IS NOT NULL)
      )
    )
  );

CREATE POLICY "Users can insert activities to their trips"
  ON public.activities
  FOR INSERT
  WITH CHECK (
    EXISTS (
      SELECT 1 FROM public.trips
      WHERE trips.id = activities.trip_id
      AND trips.user_id = auth.uid()
    )
  );

CREATE POLICY "Users can update activities from their trips"
  ON public.activities
  FOR UPDATE
  USING (
    EXISTS (
      SELECT 1 FROM public.trips
      WHERE trips.id = activities.trip_id
      AND trips.user_id = auth.uid()
    )
  );

CREATE POLICY "Users can delete activities from their trips"
  ON public.activities
  FOR DELETE
  USING (
    EXISTS (
      SELECT 1 FROM public.trips
      WHERE trips.id = activities.trip_id
      AND trips.user_id = auth.uid()
    )
  );