# Simplified E2E Testing Guide

This guide replaces the complex `smart-test-runner.ts` with native Playwright features for better performance and maintainability.

## Why Replace Smart Test Runner?

The smart test runner had several issues:
- 302 lines of custom logic duplicating Playwright features
- Synchronous file I/O causing blocking operations
- Complex metadata management requiring maintenance
- Reinvented test filtering and prioritization

## Native Playwright Solutions

### Test Prioritization with Tags

Instead of custom priority logic, use <PERSON><PERSON>'s built-in tagging:

```typescript
// Critical tests
test('user login flow @critical', async ({ page }) => {
  // Critical path test
});

// High priority tests  
test('import functionality @high', async ({ page }) => {
  // High priority test
});

// Medium priority tests
test('dashboard analytics @medium', async ({ page }) => {
  // Medium priority test
});

// Low priority tests
test('visual consistency @low @visual', async ({ page }) => {
  // Low priority test
});
```

### Running Tests by Priority

```bash
# Run only critical tests
npx playwright test --grep @critical

# Run critical and high priority
npx playwright test --grep "@critical|@high"

# Run all except low priority
npx playwright test --grep-invert @low

# Run tests by specific tags
npx playwright test --grep @smoke
npx playwright test --grep @regression
```

### Test Categories

Organize tests with descriptive tags:

```typescript
// Smoke tests (quick validation)
test('homepage loads @smoke', async ({ page }) => {
  await page.goto('/');
  await expect(page.locator('h1')).toBeVisible();
});

// Regression tests (prevent bugs)
test('import parsing handles edge cases @regression', async ({ page }) => {
  // Test for specific bugs
});

// Performance tests
test('timeline renders within 2 seconds @performance', async ({ page }) => {
  const start = Date.now();
  await page.goto('/timeline');
  await page.waitForLoadState('networkidle');
  const duration = Date.now() - start;
  expect(duration).toBeLessThan(2000);
});

// Visual tests
test('dashboard layout consistency @visual', async ({ page }) => {
  await page.goto('/dashboard');
  await expect(page).toHaveScreenshot('dashboard.png');
});
```

### Configuration-Based Test Selection

Use `playwright.config.ts` for smart test selection:

```typescript
// playwright.config.ts
export default defineConfig({
  projects: [
    {
      name: 'critical',
      grep: /@critical/,
      retries: 2,
    },
    {
      name: 'smoke',
      grep: /@smoke/,
      retries: 1,
    },
    {
      name: 'regression',
      grep: /@regression/,
      retries: 0,
    },
  ],
});
```

### Running Different Test Suites

```bash
# Development workflow
npm run test:smoke    # Quick validation
npm run test:critical # Core functionality
npm run test:full     # Complete test suite

# CI/CD pipeline
npm run test:smoke    # PR validation
npm run test:critical # Pre-merge
npm run test:full     # Post-merge
```

### Package.json Scripts

```json
{
  "scripts": {
    "test:e2e": "playwright test",
    "test:smoke": "playwright test --grep @smoke",
    "test:critical": "playwright test --grep @critical",
    "test:regression": "playwright test --grep @regression",
    "test:visual": "playwright test --grep @visual",
    "test:performance": "playwright test --grep @performance",
    "test:ci": "playwright test --grep '@critical|@smoke'",
    "test:full": "playwright test --grep-invert @slow"
  }
}
```

### Test Dependencies with Playwright

Instead of custom dependency tracking, use Playwright's built-in features:

```typescript
// Sequential tests that depend on each other
test.describe.serial('User workflow', () => {
  test('create account @critical', async ({ page }) => {
    // Setup test
  });

  test('login with new account @critical', async ({ page }) => {
    // Depends on previous test
  });

  test('create first trip @critical', async ({ page }) => {
    // Depends on login
  });
});

// Parallel tests with shared setup
test.describe.parallel('Import tests', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/import');
    await page.waitForLoadState('networkidle');
  });

  test('import ChatGPT conversation @high', async ({ page }) => {
    // Test implementation
  });

  test('import Claude conversation @high', async ({ page }) => {
    // Test implementation
  });
});
```

### Test Timing and Retries

Configure retries and timeouts based on test importance:

```typescript
// playwright.config.ts
export default defineConfig({
  projects: [
    {
      name: 'critical',
      grep: /@critical/,
      retries: 3,
      timeout: 30000,
    },
    {
      name: 'standard',
      grep: /@medium|@high/,
      retries: 1,
      timeout: 15000,
    },
    {
      name: 'visual',
      grep: /@visual/,
      retries: 0,
      timeout: 10000,
    },
  ],
});
```

## Migration from Smart Test Runner

1. **Remove custom runner**: Delete `smart-test-runner.ts` and related files
2. **Tag existing tests**: Add appropriate tags to all test descriptions
3. **Update package.json**: Add the new script commands
4. **Configure Playwright**: Set up projects for different test types
5. **Update CI/CD**: Use native Playwright commands

## Benefits of This Approach

✅ **Simpler**: No custom logic to maintain
✅ **Faster**: No file I/O operations blocking tests  
✅ **Native**: Uses Playwright's optimized test selection
✅ **Flexible**: Easy to add new test categories
✅ **Standard**: Follows Playwright best practices
✅ **Maintainable**: Less custom code to debug

## Example Test File

```typescript
import { test, expect } from '@playwright/test';

test.describe('TravelViz Core Features', () => {
  test('homepage loads quickly @smoke @critical', async ({ page }) => {
    await page.goto('/');
    await expect(page.locator('h1')).toContainText('TravelViz');
    await expect(page.locator('[data-testid="hero-section"]')).toBeVisible();
  });

  test('import flow works end-to-end @critical @regression', async ({ page }) => {
    await page.goto('/import');
    
    // Upload conversation file
    await page.setInputFiles('[data-testid="file-input"]', './test-data/sample-conversation.txt');
    
    // Wait for parsing
    await expect(page.locator('[data-testid="parsing-status"]')).toContainText('Complete');
    
    // Preview should show activities
    await expect(page.locator('[data-testid="activity-preview"]')).toHaveCount.greaterThan(0);
    
    // Create trip
    await page.click('[data-testid="create-trip-button"]');
    await expect(page).toHaveURL(/\/trip\/[\w-]+/);
  });

  test('timeline is visually consistent @visual @low', async ({ page }) => {
    await page.goto('/trip/demo');
    await page.waitForLoadState('networkidle');
    await expect(page.locator('[data-testid="timeline"]')).toHaveScreenshot('timeline.png');
  });
});
```

This approach eliminates 302 lines of custom code while providing better test organization and performance.