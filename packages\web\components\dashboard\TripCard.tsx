"use client";

import { useState } from 'react';
import Link from 'next/link';
import { motion, AnimatePresence } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { Badge } from '@/components/ui/badge';
import { CalendarDays, MapPin, Eye, MoreHorizontal, Edit, Share2, Copy, Trash2, ExternalLink } from 'lucide-react';
import { format, differenceInDays } from 'date-fns';

interface Trip {
  id: string;
  title: string;
  destination: string;
  startDate: string;
  endDate: string;
  status: 'upcoming' | 'in-progress' | 'completed';
  imageUrl: string;
  viewCount: number;
  activities: number;
}

interface TripCardProps {
  trip: Trip;
  isSelected: boolean;
  onSelect: () => void;
}

export function TripCard({ trip, isSelected, onSelect }: TripCardProps) {
  const [isImageLoaded, setIsImageLoaded] = useState(false);
  const duration = differenceInDays(new Date(trip.endDate), new Date(trip.startDate)) + 1;

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'upcoming': return 'bg-blue-100 text-blue-800';
      case 'in-progress': return 'bg-orange-100 text-orange-800';
      case 'completed': return 'bg-green-100 text-green-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusLabel = (status: string) => {
    switch (status) {
      case 'upcoming': return 'Upcoming';
      case 'in-progress': return 'In Progress';
      case 'completed': return 'Completed';
      default: return status;
    }
  };

  return (
    <motion.div
      layout
      className={`group relative bg-white rounded-xl shadow-sm hover:shadow-xl transition-all duration-300 overflow-hidden ${
        isSelected ? 'ring-2 ring-orange-500 shadow-xl' : ''
      }`}
      whileHover={{ y: -4 }}
    >
      {/* Image */}
      <div className="relative h-48 overflow-hidden">
        <img
          src={trip.imageUrl}
          alt={trip.title}
          className={`w-full h-full object-cover transition-all duration-300 group-hover:scale-105 ${
            isImageLoaded ? 'opacity-100' : 'opacity-0'
          }`}
          onLoad={() => setIsImageLoaded(true)}
        />
        {!isImageLoaded && (
          <div className="absolute inset-0 bg-gray-200 animate-pulse"></div>
        )}
        
        {/* Overlay */}
        <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
        
        {/* Status Badge */}
        <div className="absolute top-3 right-3">
          <Badge className={`${getStatusColor(trip.status)} text-xs font-medium`}>
            {getStatusLabel(trip.status)}
          </Badge>
        </div>

        {/* Quick Actions (on hover) */}
        <AnimatePresence>
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: 10 }}
            className="absolute bottom-3 left-3 right-3 flex space-x-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300"
          >
            <Link href={`/plan/${trip.id}`} className="flex-1">
              <Button size="sm" className="w-full bg-white/90 text-gray-900 hover:bg-white">
                <Edit className="h-3 w-3 mr-1" />
                Edit
              </Button>
            </Link>
            <Button size="sm" variant="outline" className="bg-white/90 border-white hover:bg-white">
              <Share2 className="h-3 w-3" />
            </Button>
          </motion.div>
        </AnimatePresence>
      </div>

      {/* Content */}
      <div className="p-6">
        <div className="flex items-start justify-between mb-3">
          <div className="flex-1 min-w-0">
            <h3 className="text-lg font-semibold text-gray-900 truncate">
              {trip.title}
            </h3>
            <div className="flex items-center text-sm text-gray-600 mt-1">
              <MapPin className="h-3 w-3 mr-1 flex-shrink-0" />
              <span className="truncate">{trip.destination}</span>
            </div>
          </div>
          
          {/* More Actions */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem>
                <ExternalLink className="mr-2 h-4 w-4" />
                View Public Link
              </DropdownMenuItem>
              <DropdownMenuItem>
                <Copy className="mr-2 h-4 w-4" />
                Duplicate
              </DropdownMenuItem>
              <DropdownMenuItem className="text-red-600">
                <Trash2 className="mr-2 h-4 w-4" />
                Delete
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>

        {/* Trip Details */}
        <div className="space-y-3">
          <div className="flex items-center text-sm text-gray-600">
            <CalendarDays className="h-3 w-3 mr-2 flex-shrink-0" />
            <span>
              {format(new Date(trip.startDate), 'MMM d')} - {format(new Date(trip.endDate), 'MMM d, yyyy')}
            </span>
          </div>

          {/* Stats */}
          <div className="flex items-center justify-between pt-3 border-t border-gray-100">
            <div className="flex items-center space-x-4 text-xs text-gray-500">
              <span>{duration} day{duration !== 1 ? 's' : ''}</span>
              <span>•</span>
              <span>{trip.activities} activities</span>
            </div>
            <div className="flex items-center text-xs text-gray-500">
              <Eye className="h-3 w-3 mr-1" />
              <span>{trip.viewCount}</span>
            </div>
          </div>
        </div>
      </div>
    </motion.div>
  );
}