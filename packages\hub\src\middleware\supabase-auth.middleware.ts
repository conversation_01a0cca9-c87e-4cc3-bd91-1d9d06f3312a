import { Request, Response, NextFunction } from 'express';
import { createErrorResponse, HTTP_STATUS } from '@travelviz/shared';
import { verifySupabaseJWT, extractBearerToken, SupabaseUser, SupabaseJWTError } from '../utils/supabase-jwt';
import { logger } from '../utils/logger';

export interface SupabaseAuthenticatedRequest extends Request {
  user?: SupabaseUser;
}

/**
 * Middleware to verify Supabase JWT access token
 * This middleware validates tokens issued by Supabase Auth
 */
export const authenticateSupabaseUser = async (
  req: SupabaseAuthenticatedRequest,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const token = extractBearerToken(req.headers.authorization);

    logger.debug('Authentication attempt', {
      hasToken: !!token,
      tokenLength: token?.length,
      userAgent: req.headers['user-agent'],
      path: req.path,
      method: req.method
    });

    if (!token) {
      logger.warn('Authentication failed - no token provided', {
        authHeader: req.headers.authorization ? 'present' : 'missing',
        path: req.path
      });
      res.status(HTTP_STATUS.UNAUTHORIZED).json(
        createErrorResponse('Unauthorized', 'Missing or invalid authorization header')
      );
      return;
    }

    // Use Supabase JWT verification exclusively
    logger.debug('Using Supabase JWT verification');
    const user = await verifySupabaseJWT(token);
    req.user = user;
    logger.debug('Authentication successful', { userId: user.id, userEmail: user.email });
    next();
  } catch (error) {
    logger.error('Authentication error occurred', {
      errorType: error instanceof Error ? error.constructor.name : 'Unknown',
      errorMessage: error instanceof Error ? error.message : String(error),
      path: req.path,
      method: req.method,
      hasToken: !!extractBearerToken(req.headers.authorization)
    });

    if (error instanceof SupabaseJWTError) {
      const statusCode = (error as { statusCode?: number }).statusCode || HTTP_STATUS.UNAUTHORIZED;
      const message = (error as Error).message;
      res.status(statusCode).json(
        createErrorResponse('Unauthorized', message)
      );
      return;
    }

    res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(
      createErrorResponse('Authentication error', 'An error occurred during authentication')
    );
  }
};

/**
 * Optional Supabase authentication middleware
 * Adds user to request if valid token is provided, but doesn't fail if missing
 */
export const optionalSupabaseAuth = async (
  req: SupabaseAuthenticatedRequest,
  res: Response,
  next: NextFunction
): Promise<void> => {
  const token = extractBearerToken(req.headers.authorization);
  
  if (!token) {
    // No token provided, continue without user data
    return next();
  }

  try {
    const user = await verifySupabaseJWT(token);
    req.user = user;
  } catch (error) {
    // Token invalid but continue anyway for optional auth
    // Optionally log the error for debugging
    if (process.env.NODE_ENV === 'development') {
      logger.debug('Optional auth token verification failed:', { error });
    }
  }

  next();
};

/**
 * Middleware to check if user has specific role
 * Must be used after authenticateSupabaseUser
 */
export const requireRole = (
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  role: string
) => {
  return (req: SupabaseAuthenticatedRequest, res: Response, next: NextFunction) => {
    if (!req.user) {
      res.status(HTTP_STATUS.UNAUTHORIZED).json(
        createErrorResponse('Unauthorized', 'Authentication required')
      );
      return;
    }

    // For MVP, we don't have role checking implemented
    // This is a placeholder for future implementation
    next();
  };
};