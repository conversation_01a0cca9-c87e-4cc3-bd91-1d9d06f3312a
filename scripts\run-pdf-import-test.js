#!/usr/bin/env node

/**
 * Quick PDF Import Test Runner
 * Executes the comprehensive PDF import test and provides immediate feedback
 */

const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');

// Configuration
const CONFIG = {
  TEST_SCRIPT: path.join(__dirname, '../tests/integration/pdf-import-comprehensive-test.js'),
  BACKEND_CHECK_URL: 'http://localhost:3001/health',
  FRONTEND_CHECK_URL: 'http://localhost:3000',
  PDF_FILE_PATH: 'C:\\Users\\<USER>\\Travelviz\\Travelviz\\Updated 15-Day European Travel Itinerary_ London, Madrid, Lisbon and Porto.pdf'
};

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

function colorLog(color, message) {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

// Pre-flight checks
async function preflightChecks() {
  colorLog('blue', '🔍 Running pre-flight checks...');
  
  const checks = [];
  
  // Check if test script exists
  if (fs.existsSync(CONFIG.TEST_SCRIPT)) {
    colorLog('green', '✅ Test script found');
    checks.push(true);
  } else {
    colorLog('red', '❌ Test script not found: ' + CONFIG.TEST_SCRIPT);
    checks.push(false);
  }
  
  // Check if PDF file exists
  if (fs.existsSync(CONFIG.PDF_FILE_PATH)) {
    const stats = fs.statSync(CONFIG.PDF_FILE_PATH);
    colorLog('green', `✅ PDF file found (${Math.round(stats.size / 1024)}KB)`);
    checks.push(true);
  } else {
    colorLog('red', '❌ PDF file not found: ' + CONFIG.PDF_FILE_PATH);
    colorLog('yellow', '   Please update the PDF_FILE_PATH in the test configuration');
    checks.push(false);
  }
  
  // Check backend server
  try {
    const axios = require('axios');
    await axios.get(CONFIG.BACKEND_CHECK_URL, { timeout: 5000 });
    colorLog('green', '✅ Backend server is running');
    checks.push(true);
  } catch (error) {
    colorLog('red', '❌ Backend server not accessible');
    colorLog('yellow', '   Please start the backend server: cd packages/hub && npm run dev');
    checks.push(false);
  }
  
  // Check environment variables
  const requiredEnvVars = ['OPENROUTER_API_KEY', 'GOOGLE_GEMINI_API_KEY'];
  let envChecks = 0;
  
  requiredEnvVars.forEach(envVar => {
    if (process.env[envVar]) {
      colorLog('green', `✅ ${envVar} is set`);
      envChecks++;
    } else {
      colorLog('yellow', `⚠️  ${envVar} not set (may affect AI functionality)`);
    }
  });
  
  checks.push(envChecks > 0); // At least one AI API key should be set
  
  return checks.every(check => check);
}

// Run the test
async function runTest() {
  colorLog('blue', '🚀 Starting PDF Import Test...');
  
  return new Promise((resolve, reject) => {
    const testProcess = spawn('node', [CONFIG.TEST_SCRIPT], {
      stdio: 'inherit',
      env: { ...process.env }
    });
    
    testProcess.on('close', (code) => {
      if (code === 0) {
        colorLog('green', '✅ Test completed successfully');
        resolve(true);
      } else {
        colorLog('red', `❌ Test failed with exit code ${code}`);
        resolve(false);
      }
    });
    
    testProcess.on('error', (error) => {
      colorLog('red', `❌ Test execution error: ${error.message}`);
      reject(error);
    });
  });
}

// Display help information
function showHelp() {
  console.log(`
${colors.cyan}PDF Import Test Runner${colors.reset}

This script runs a comprehensive test of the PDF import functionality.

${colors.yellow}Prerequisites:${colors.reset}
1. Backend server running on http://localhost:3001
2. Test PDF file available at the configured path
3. Environment variables set (OPENROUTER_API_KEY, etc.)
4. Test account credentials configured

${colors.yellow}Usage:${colors.reset}
  node scripts/run-pdf-import-test.js [options]

${colors.yellow}Options:${colors.reset}
  --help, -h     Show this help message
  --check, -c    Run pre-flight checks only
  --force, -f    Skip pre-flight checks and run test

${colors.yellow}Test Configuration:${colors.reset}
  Test Script: ${CONFIG.TEST_SCRIPT}
  PDF File: ${CONFIG.PDF_FILE_PATH}
  Backend URL: ${CONFIG.BACKEND_CHECK_URL}

${colors.yellow}What the test does:${colors.reset}
1. Authenticates with test account (<EMAIL>)
2. Uploads the specified PDF file
3. Monitors the parsing process with detailed logging
4. Tracks model selection, timeouts, and performance
5. Generates a comprehensive test report

${colors.yellow}Expected Results:${colors.reset}
- Authentication: < 2 seconds
- Upload: < 30 seconds  
- Parsing: < 3 minutes
- Overall success rate: > 90%

${colors.yellow}Troubleshooting:${colors.reset}
- If backend check fails: Start with 'cd packages/hub && npm run dev'
- If PDF not found: Update PDF_FILE_PATH in test configuration
- If auth fails: Check test credentials in .env.local
- If parsing hangs: Check backend logs for model selection and timeouts
`);
}

// Main execution
async function main() {
  const args = process.argv.slice(2);
  
  // Handle command line arguments
  if (args.includes('--help') || args.includes('-h')) {
    showHelp();
    return;
  }
  
  if (args.includes('--check') || args.includes('-c')) {
    const checksPass = await preflightChecks();
    process.exit(checksPass ? 0 : 1);
  }
  
  const skipChecks = args.includes('--force') || args.includes('-f');
  
  try {
    colorLog('magenta', '🧪 PDF Import Comprehensive Test Runner');
    colorLog('magenta', '=====================================');
    
    // Run pre-flight checks unless skipped
    if (!skipChecks) {
      const checksPass = await preflightChecks();
      if (!checksPass) {
        colorLog('red', '\n❌ Pre-flight checks failed. Use --force to skip checks.');
        colorLog('yellow', 'Run with --help for more information.');
        process.exit(1);
      }
      colorLog('green', '\n✅ All pre-flight checks passed');
    }
    
    // Run the test
    const testSuccess = await runTest();
    
    // Final summary
    console.log('\n' + '='.repeat(50));
    if (testSuccess) {
      colorLog('green', '🎉 PDF Import Test PASSED');
      colorLog('green', 'The recent fixes appear to be working correctly.');
    } else {
      colorLog('red', '💥 PDF Import Test FAILED');
      colorLog('yellow', 'Check the test report for detailed analysis.');
      colorLog('yellow', 'Review backend logs for additional debugging information.');
    }
    console.log('='.repeat(50));
    
    process.exit(testSuccess ? 0 : 1);
    
  } catch (error) {
    colorLog('red', `💥 Test runner error: ${error.message}`);
    process.exit(1);
  }
}

// Handle unhandled rejections
process.on('unhandledRejection', (reason, promise) => {
  colorLog('red', `Unhandled Rejection at: ${promise}, reason: ${reason}`);
  process.exit(1);
});

// Run if this file is executed directly
if (require.main === module) {
  main();
}

module.exports = { main, preflightChecks, runTest };
