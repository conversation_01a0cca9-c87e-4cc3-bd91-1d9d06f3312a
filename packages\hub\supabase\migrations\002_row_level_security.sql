-- Row Level Security (RLS) policies for TravelViz Hub
-- This migration sets up security policies to ensure users can only access their own data

-- Enable RLS on all tables
ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.trips ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.activities ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.trip_shares ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.affiliate_clicks ENABLE ROW LEVEL SECURITY;

-- Profiles policies
-- Users can view their own profile
CREATE POLICY "Users can view own profile" ON public.profiles
  FOR SELECT USING (auth.uid() = id);

-- Users can update their own profile
CREATE POLICY "Users can update own profile" ON public.profiles
  FOR UPDATE USING (auth.uid() = id);

-- Service role can do anything (for admin operations)
CREATE POLICY "Service role has full access to profiles" ON public.profiles
  FOR ALL USING (auth.jwt()->>'role' = 'service_role');

-- Trips policies
-- Users can view their own trips
CREATE POLICY "Users can view own trips" ON public.trips
  FOR SELECT USING (auth.uid() = user_id);

-- Users can view public trips
CREATE POLICY "Anyone can view public trips" ON public.trips
  FOR SELECT USING (is_public = true);

-- Users can insert their own trips
CREATE POLICY "Users can create own trips" ON public.trips
  FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Users can update their own trips
CREATE POLICY "Users can update own trips" ON public.trips
  FOR UPDATE USING (auth.uid() = user_id);

-- Users can delete their own trips
CREATE POLICY "Users can delete own trips" ON public.trips
  FOR DELETE USING (auth.uid() = user_id);

-- Service role has full access
CREATE POLICY "Service role has full access to trips" ON public.trips
  FOR ALL USING (auth.jwt()->>'role' = 'service_role');

-- Activities policies
-- Users can view activities for trips they own
CREATE POLICY "Users can view activities for own trips" ON public.activities
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM public.trips 
      WHERE trips.id = activities.trip_id 
      AND trips.user_id = auth.uid()
    )
  );

-- Users can view activities for public trips
CREATE POLICY "Anyone can view activities for public trips" ON public.activities
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM public.trips 
      WHERE trips.id = activities.trip_id 
      AND trips.is_public = true
    )
  );

-- Users can create activities for their own trips
CREATE POLICY "Users can create activities for own trips" ON public.activities
  FOR INSERT WITH CHECK (
    EXISTS (
      SELECT 1 FROM public.trips 
      WHERE trips.id = activities.trip_id 
      AND trips.user_id = auth.uid()
    )
  );

-- Users can update activities for their own trips
CREATE POLICY "Users can update activities for own trips" ON public.activities
  FOR UPDATE USING (
    EXISTS (
      SELECT 1 FROM public.trips 
      WHERE trips.id = activities.trip_id 
      AND trips.user_id = auth.uid()
    )
  );

-- Users can delete activities for their own trips
CREATE POLICY "Users can delete activities for own trips" ON public.activities
  FOR DELETE USING (
    EXISTS (
      SELECT 1 FROM public.trips 
      WHERE trips.id = activities.trip_id 
      AND trips.user_id = auth.uid()
    )
  );

-- Service role has full access
CREATE POLICY "Service role has full access to activities" ON public.activities
  FOR ALL USING (auth.jwt()->>'role' = 'service_role');

-- Trip shares policies
-- Anyone can view trip share records for public trips
CREATE POLICY "Anyone can view shares for public trips" ON public.trip_shares
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM public.trips 
      WHERE trips.id = trip_shares.trip_id 
      AND trips.is_public = true
    )
  );

-- Users can create share records for their own trips
CREATE POLICY "Users can create shares for own trips" ON public.trip_shares
  FOR INSERT WITH CHECK (
    EXISTS (
      SELECT 1 FROM public.trips 
      WHERE trips.id = trip_shares.trip_id 
      AND trips.user_id = auth.uid()
    )
  );

-- Service role has full access
CREATE POLICY "Service role has full access to trip shares" ON public.trip_shares
  FOR ALL USING (auth.jwt()->>'role' = 'service_role');

-- Affiliate clicks policies
-- Anyone can insert affiliate clicks (for tracking)
CREATE POLICY "Anyone can create affiliate clicks" ON public.affiliate_clicks
  FOR INSERT WITH CHECK (true);

-- Users can view their own affiliate clicks
CREATE POLICY "Users can view own affiliate clicks" ON public.affiliate_clicks
  FOR SELECT USING (auth.uid() = user_id);

-- Service role has full access
CREATE POLICY "Service role has full access to affiliate clicks" ON public.affiliate_clicks
  FOR ALL USING (auth.jwt()->>'role' = 'service_role');

-- Create helper function to check if user owns a trip
CREATE OR REPLACE FUNCTION user_owns_trip(trip_id UUID)
RETURNS BOOLEAN AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1 FROM public.trips 
    WHERE id = trip_id 
    AND user_id = auth.uid()
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create helper function to check if trip is public
CREATE OR REPLACE FUNCTION trip_is_public(trip_id UUID)
RETURNS BOOLEAN AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1 FROM public.trips 
    WHERE id = trip_id 
    AND is_public = true
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;