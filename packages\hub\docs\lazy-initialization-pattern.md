# Lazy Initialization Pattern for Redis Connection Pool

## Problem

When running tests, the Redis connection pool singleton was initializing at module import time, before test environment variables were loaded from `.env.test`. This caused connection failures with "Exhausted all retries" errors.

## Root Cause

The issue occurred because:
1. Module imports happen before test setup runs
2. Singletons were instantiated during module evaluation
3. Redis credentials weren't available until test setup loaded `.env.test`
4. Additionally, `global.fetch` was being mocked in test setup, breaking Upstash Redis HTTP requests

## Solution

Implemented lazy initialization pattern:

### 1. Lazy-initialized singleton in `redis-connection-pool.service.ts`:

```typescript
// Lazy-initialized singleton instance
let _redisConnectionPool: RedisConnectionPool | null = null;

export function getRedisConnectionPool(): RedisConnectionPool {
  if (!_redisConnectionPool) {
    _redisConnectionPool = new RedisConnectionPool(defaultConfig);
  }
  return _redisConnectionPool;
}

// Export a proxy object for backward compatibility
export const redisConnectionPool = {
  execute: async <T>(operation: (redis: Redis) => Promise<T>): Promise<T> => {
    return getRedisConnectionPool().execute(operation);
  },
  // ... other methods
};
```

### 2. Delayed initialization in `cache.service.ts`:

```typescript
private initialized = false;

constructor() {
  // Delay initialization until first use
}

private async ensureInitialized(): Promise<void> {
  if (!this.initialized) {
    await this.initialize();
    this.initialized = true;
  }
}

// Call ensureInitialized() in every public method before operations
```

### 3. Fixed test setup in `test/setup.ts`:

```typescript
// Don't mock fetch globally - it breaks Upstash Redis HTTP requests
// Individual tests that need fetch mocked should do it themselves
```

## Benefits

1. **Test Compatibility**: Tests can load environment variables before Redis connects
2. **Backward Compatibility**: Existing code using `redisConnectionPool` continues to work
3. **Clean Architecture**: Separation of instance creation from module loading
4. **Flexibility**: Tests can control when initialization happens

## Testing

With this pattern, real integration tests now work correctly:
- Tests connect to actual Redis using credentials from `.env.test`
- No more mock-based testing for Redis operations
- Better confidence in production behavior