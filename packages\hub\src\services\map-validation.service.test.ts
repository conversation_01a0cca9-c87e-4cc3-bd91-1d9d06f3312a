import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { MapValidationService } from './map-validation.service';
import axios from 'axios';

vi.mock('axios');
vi.mock('../../utils/logger', () => ({
  logger: {
    error: vi.fn(),
    warn: vi.fn(),
    info: vi.fn(),
    debug: vi.fn(),
  },
}));

describe('MapValidationService', () => {
  let service: MapValidationService;
  const originalEnv = process.env;

  beforeEach(() => {
    vi.clearAllMocks();
    process.env = { ...originalEnv };
    service = MapValidationService.getInstance();
  });

  afterEach(() => {
    process.env = originalEnv;
  });

  describe('validateMapConfig', () => {
    it('should validate both Mapbox and Google Maps configurations', async () => {
      process.env.MAPBOX_ACCESS_TOKEN = 'test-mapbox-token';
      process.env.GOOGLE_MAPS_API_KEY = 'test-google-key';

      vi.mocked(axios.get).mockImplementation((url: string) => {
        if (url.includes('mapbox')) {
          return Promise.resolve({ status: 200, data: {} });
        }
        if (url.includes('googleapis')) {
          return Promise.resolve({ data: { status: 'OK' } });
        }
        return Promise.reject(new Error('Unknown URL'));
      });

      const result = await service.validateMapConfig();

      expect(result.mapbox.isValid).toBe(true);
      expect(result.google.isValid).toBe(true);
      expect(result.allValid).toBe(true);
    });

    it('should handle missing API keys', async () => {
      delete process.env.MAPBOX_ACCESS_TOKEN;
      delete process.env.GOOGLE_MAPS_API_KEY;

      const result = await service.validateMapConfig();

      expect(result.mapbox.isValid).toBe(false);
      expect(result.mapbox.error).toContain('not configured');
      expect(result.google.isValid).toBe(false);
      expect(result.google.error).toContain('not configured');
      expect(result.allValid).toBe(false);
    });

    it('should handle invalid Mapbox token', async () => {
      process.env.MAPBOX_ACCESS_TOKEN = 'invalid-token';
      process.env.GOOGLE_MAPS_API_KEY = 'test-google-key';

      vi.mocked(axios.get).mockImplementation((url: string) => {
        if (url.includes('mapbox')) {
          const error: any = new Error('Unauthorized');
          error.response = { status: 401 };
          error.isAxiosError = true;
          return Promise.reject(error);
        }
        return Promise.resolve({ data: { status: 'OK' } });
      });

      const result = await service.validateMapConfig();

      expect(result.mapbox.isValid).toBe(false);
      expect(result.mapbox.error).toContain('Invalid Mapbox access token');
    });

    it('should handle invalid Google Maps key', async () => {
      process.env.MAPBOX_ACCESS_TOKEN = 'test-mapbox-token';
      process.env.GOOGLE_MAPS_API_KEY = 'invalid-key';

      vi.mocked(axios.get).mockImplementation((url: string) => {
        if (url.includes('mapbox')) {
          return Promise.resolve({ status: 200, data: {} });
        }
        if (url.includes('googleapis')) {
          return Promise.resolve({ data: { status: 'REQUEST_DENIED' } });
        }
        return Promise.reject(new Error('Unknown URL'));
      });

      const result = await service.validateMapConfig();

      expect(result.google.isValid).toBe(false);
      expect(result.google.error).toContain('invalid or lacks required permissions');
    });
  });

  describe('geocodeWithFallback', () => {
    it('should use Mapbox when available', async () => {
      process.env.MAPBOX_ACCESS_TOKEN = 'test-mapbox-token';
      process.env.GOOGLE_MAPS_API_KEY = 'test-google-key';

      vi.mocked(axios.get).mockResolvedValueOnce({
        data: {
          features: [{
            center: [139.6917, 35.6895]
          }]
        }
      });

      const result = await service.geocodeWithFallback('Tokyo');

      expect(result).toEqual({
        lat: 35.6895,
        lng: 139.6917,
        provider: 'mapbox'
      });
    });

    it('should fallback to Google when Mapbox fails', async () => {
      process.env.MAPBOX_ACCESS_TOKEN = 'test-mapbox-token';
      process.env.GOOGLE_MAPS_API_KEY = 'test-google-key';

      vi.mocked(axios.get)
        .mockRejectedValueOnce(new Error('Mapbox error'))
        .mockResolvedValueOnce({
          data: {
            results: [{
              geometry: {
                location: {
                  lat: 35.6762,
                  lng: 139.6503
                }
              }
            }]
          }
        });

      const result = await service.geocodeWithFallback('Tokyo');

      expect(result).toEqual({
        lat: 35.6762,
        lng: 139.6503,
        provider: 'google'
      });
    });

    it('should return null when both providers fail', async () => {
      process.env.MAPBOX_ACCESS_TOKEN = 'test-mapbox-token';
      process.env.GOOGLE_MAPS_API_KEY = 'test-google-key';

      vi.mocked(axios.get)
        .mockRejectedValueOnce(new Error('Mapbox error'))
        .mockRejectedValueOnce(new Error('Google error'));

      const result = await service.geocodeWithFallback('Unknown Place');

      expect(result).toBeNull();
    });
  });

  describe('getStaticMapUrl', () => {
    it('should prefer Google Static Maps when available', () => {
      process.env.GOOGLE_MAPS_API_KEY = 'test-google-key';
      process.env.MAPBOX_ACCESS_TOKEN = 'test-mapbox-token';

      const url = service.getStaticMapUrl(35.6762, 139.6503);

      expect(url).toContain('maps.googleapis.com');
      expect(url).toContain('35.6762,139.6503');
      expect(url).toContain('test-google-key');
    });

    it('should fallback to Mapbox when Google is not available', () => {
      delete process.env.GOOGLE_MAPS_API_KEY;
      process.env.MAPBOX_ACCESS_TOKEN = 'test-mapbox-token';

      const url = service.getStaticMapUrl(35.6762, 139.6503);

      expect(url).toContain('api.mapbox.com');
      expect(url).toContain('139.6503,35.6762');
      expect(url).toContain('test-mapbox-token');
    });

    it('should return null when no API keys are configured', () => {
      delete process.env.GOOGLE_MAPS_API_KEY;
      delete process.env.MAPBOX_ACCESS_TOKEN;

      const url = service.getStaticMapUrl(35.6762, 139.6503);

      expect(url).toBeNull();
    });
  });
});