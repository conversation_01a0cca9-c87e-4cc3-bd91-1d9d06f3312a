'use client';

import { useState, useCallback } from 'react';
import { useImport } from '@/contexts/ImportContext';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Tabs, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { useDropzone } from 'react-dropzone';
import { Upload, FileText, Link, Sparkles, ArrowRight, Info } from 'lucide-react';
import { cn } from '@/lib/utils';
import { importApi } from '@/lib/api/import';
import { useAnalytics, useInteractionTracking } from '@/hooks/useAnalytics';
import { ShimmerButton } from '@/components/magic-ui/shimmer-button';

// Import sample conversations from fixtures
import sampleData from '@travelviz/shared/test/fixtures/sample-conversations.json';

const SAMPLE_CONVERSATIONS = sampleData.conversations.slice(0, 3); // Use first 3 simple examples

export function InputStep() {
  const { source, content, setContent, setSource, setStep, setError, setImportId, setSseUrl, setParseProgress } = useImport();
  const [isProcessing, setIsProcessing] = useState(false);
  const [characterCount, setCharacterCount] = useState(content.length);
  const { trackImportEvent, trackError } = useAnalytics();
  const { trackClick } = useInteractionTracking();

  const handleContentChange = (value: string) => {
    setContent(value);
    setCharacterCount(value.length);
  };

  const handleSampleSelect = (sample: typeof SAMPLE_CONVERSATIONS[0]) => {
    setContent(sample.content);
    setCharacterCount(sample.content.length);
    setSource('paste');
    trackImportEvent('input', { 
      sample_used: sample.id,
      content_length: sample.content.length 
    });
  };

  const detectAISource = (text: string): 'chatgpt' | 'claude' | 'gemini' | 'unknown' => {
    if (text.includes('ChatGPT') || text.includes('GPT-4')) return 'chatgpt';
    if (text.includes('Claude') || text.includes('Anthropic')) return 'claude';
    if (text.includes('Gemini') || text.includes('Bard')) return 'gemini';
    return 'unknown';
  };

  const handleParse = async () => {
    if (!content.trim() || content.length < 100) {
      setError('Please paste a longer conversation with trip details');
      return;
    }

    setIsProcessing(true);
    setError(null);

    try {
      // Track import start
      trackImportEvent('input', { 
        source,
        content_length: content.length,
        ai_source: detectAISource(content)
      });
      
      // Start parsing
      setStep('parsing');
      
      // Create import session
      const response = await importApi.parseText(content, detectAISource(content));
      setImportId(response.importId);

      // Will continue in ParsingStep with SSE
    } catch (error) {
      const errorMessage = 'Failed to start import. Please try again.';
      setError(errorMessage);
      setStep('input');
      
      // Track error
      trackError(error as Error, { 
        step: 'input',
        source,
        content_length: characterCount,
        ai_source: detectAISource(content)
      });
    } finally {
      setIsProcessing(false);
    }
  };

  const onDrop = useCallback(async (acceptedFiles: File[]) => {
    const file = acceptedFiles[0];
    if (file && file.type === 'application/pdf') {
      setIsProcessing(true);
      setError(null);
      
      try {
        // Track import start
        trackImportEvent('input', { 
          source: 'file',
          file_size: file.size,
          file_name: file.name
        });
        
        // Start parsing
        setStep('parsing');
        
        // Upload PDF and create import session
        const response = await importApi.uploadPDF(file);
        setImportId(response.sessionId);
        setSseUrl(response.sseUrl); // Store SSE URL for real-time progress
        
        // Track detected source
        trackImportEvent('input', {
          source: response.source,
          page_count: response.metadata.pageCount,
          input_type: 'pdf_uploaded'
        });
        
        // Will continue in ParsingStep with polling
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Failed to upload PDF. Please try again.';
        setError(errorMessage);
        setStep('input');
        
        // Track error
        trackError(error as Error, { 
          step: 'input',
          source: 'file',
          file_size: file.size
        });
      } finally {
        setIsProcessing(false);
      }
    }
  }, [setError, setStep, setImportId, trackImportEvent, trackError]);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'application/pdf': ['.pdf']
    },
    maxSize: 10 * 1024 * 1024, // 10MB
    multiple: false
  });

  return (
    <div className="space-y-6">
      {/* Source Tabs */}
      <Tabs value={source} onValueChange={(v) => setSource(v as any)}>
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="paste">
            <FileText className="w-4 h-4 mr-2" />
            Paste Text
          </TabsTrigger>
          <TabsTrigger value="file">
            <Upload className="w-4 h-4 mr-2" />
            Upload PDF
          </TabsTrigger>
          <TabsTrigger value="url" disabled>
            <Link className="w-4 h-4 mr-2" />
            From URL
          </TabsTrigger>
        </TabsList>

        {/* Paste Text */}
        <TabsContent value="paste" className="space-y-4">
          {/* Tips */}
          <Alert className="bg-orange-50 border-orange-200">
            <Info className="h-4 w-4 text-orange-600" />
            <AlertDescription className="text-orange-800">
              <strong>Pro Tips for Best Results:</strong>
              <ul className="mt-2 space-y-1 text-sm">
                <li>• Include specific dates and times in your conversation</li>
                <li>• Mention hotel names, flight numbers, or restaurant names</li>
                <li>• Copy the entire conversation, not just the final itinerary</li>
                <li>• Include price information for better budget tracking</li>
              </ul>
            </AlertDescription>
          </Alert>

          {/* Textarea */}
          <div className="relative">
            <label htmlFor="conversation-input" className="sr-only">
              Paste your AI conversation
            </label>
            <Textarea
              id="conversation-input"
              placeholder="Paste your entire ChatGPT, Claude, or Gemini conversation here..."
              className="min-h-[300px] font-mono text-sm resize-none"
              value={content}
              onChange={(e) => handleContentChange(e.target.value)}
              aria-label="AI conversation input"
              aria-describedby="character-count"
              aria-required="true"
            />
            <div 
              id="character-count"
              className="absolute bottom-2 right-2 text-xs text-gray-400"
              aria-live="polite"
              aria-atomic="true"
            >
              {characterCount} characters
            </div>
          </div>

          {/* Sample Conversations */}
          <div className="space-y-3">
            <p className="text-sm font-medium text-gray-700" id="sample-conversations-label">
              Try a sample conversation:
            </p>
            <div 
              className="grid grid-cols-1 md:grid-cols-2 gap-3"
              role="group"
              aria-labelledby="sample-conversations-label"
            >
              {SAMPLE_CONVERSATIONS.map((sample) => (
                <button
                  key={sample.id}
                  onClick={() => handleSampleSelect(sample)}
                  className="text-left p-4 border rounded-lg hover:border-orange-500 hover:bg-orange-50 transition-colors group focus:outline-none focus:ring-2 focus:ring-orange-500 focus:ring-offset-2"
                  aria-label={`Load sample: ${sample.title} - ${sample.description}`}
                >
                  <h4 className="font-medium text-gray-900">{sample.title}</h4>
                  <p className="text-sm text-gray-600 mt-1">{sample.description}</p>
                  <p className="text-xs text-gray-500 mt-2 line-clamp-2" aria-hidden="true">
                    {sample.preview}
                  </p>
                </button>
              ))}
            </div>
          </div>
        </TabsContent>

        {/* Upload PDF */}
        <TabsContent value="file">
          <div
            {...getRootProps()}
            className={cn(
              "border-2 border-dashed rounded-lg p-12 text-center cursor-pointer transition-all focus:outline-none focus:ring-2 focus:ring-orange-500 focus:ring-offset-2",
              isDragActive 
                ? "border-orange-500 bg-orange-50" 
                : "border-gray-300 hover:border-orange-400 hover:bg-orange-50/30"
            )}
            role="button"
            tabIndex={0}
            aria-label="Upload PDF file"
            aria-describedby="upload-description"
          >
            <input {...getInputProps()} aria-label="File input" />
            <Upload className="mx-auto h-12 w-12 text-gray-400 mb-4" aria-hidden="true" />
            <p className="text-lg font-medium text-gray-700">
              {isDragActive 
                ? "Drop your PDF here" 
                : "Drag & drop your conversation PDF"}
            </p>
            <p className="text-sm text-gray-500 mt-2" id="upload-description">
              or click to browse (max 10MB)
            </p>
            <p className="text-xs text-gray-400 mt-4">
              Supports: ChatGPT, Claude, and Gemini PDF exports
            </p>
          </div>
        </TabsContent>
      </Tabs>

      {/* Action Button */}
      <div className="flex justify-end">
        <ShimmerButton
          onClick={handleParse}
          disabled={!content.trim() || characterCount < 100 || isProcessing}
          className="min-w-[200px]"
        >
          {isProcessing ? (
            <>
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
              Processing...
            </>
          ) : (
            <>
              Start Import
              <ArrowRight className="ml-2 h-4 w-4" />
            </>
          )}
        </ShimmerButton>
      </div>
    </div>
  );
}