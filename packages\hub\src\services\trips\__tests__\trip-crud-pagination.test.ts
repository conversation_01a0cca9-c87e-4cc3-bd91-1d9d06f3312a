import { describe, it, expect, vi, beforeEach } from 'vitest';
import { TripCrudService } from './trip-crud.service';
import { getSupabaseClient } from '../../../lib/supabase';
import { PaginationQuery } from '@travelviz/shared';

// Mock the Supabase client
vi.mock('../../../lib/supabase', () => ({
  getSupabaseClient: vi.fn(),
  handleSupabaseError: vi.fn((error) => ({ message: error?.message || 'Unknown error' })),
  validateDatabaseResponse: vi.fn((schema, data) => data),
  TABLES: {
    TRIPS: 'trips',
    ACTIVITIES: 'activities'
  },
  TripSchema: {},
  ActivitySchema: {}
}));

describe('TripCrudService - Pagination', () => {
  let tripCrudService: TripCrudService;
  let mockSupabaseClient: any;

  beforeEach(() => {
    // Create mock Supabase client
    mockSupabaseClient = {
      from: vi.fn().mockReturnThis(),
      select: vi.fn().mockReturnThis(),
      eq: vi.fn().mockReturnThis(),
      order: vi.fn().mockReturnThis(),
      range: vi.fn().mockReturnThis(),
      single: vi.fn()
    };

    // Return the mock client when getSupabaseClient is called
    vi.mocked(getSupabaseClient).mockReturnValue(mockSupabaseClient);

    tripCrudService = new TripCrudService();
  });

  describe('getUserTripsPaginated', () => {
    it('should return paginated trips with correct metadata', async () => {
      const userId = 'test-user-123';
      const pagination: PaginationQuery = {
        page: 1,
        limit: 10,
        sortBy: 'created_at',
        sortOrder: 'desc'
      };

      const mockTrips = [
        {
          id: 'trip-1',
          user_id: userId,
          title: 'Trip to Paris',
          status: 'draft',
          visibility: 'private',
          activities: []
        },
        {
          id: 'trip-2',
          user_id: userId,
          title: 'Trip to Tokyo',
          status: 'confirmed',
          visibility: 'public',
          activities: []
        }
      ];

      // Mock count query
      mockSupabaseClient.select.mockReturnValueOnce({
        eq: vi.fn().mockResolvedValueOnce({
          count: 15,
          error: null
        })
      });

      // Mock trips query
      mockSupabaseClient.range.mockResolvedValueOnce({
        data: mockTrips,
        error: null
      });

      const result = await tripCrudService.getUserTripsPaginated(userId, pagination);

      // Verify pagination metadata
      expect(result).toMatchObject({
        page: 1,
        limit: 10,
        total: 15,
        totalPages: 2,
        hasNextPage: true,
        hasPrevPage: false
      });

      // Verify trips are included
      expect(result.trips).toHaveLength(2);
      expect(result.trips[0].title).toBe('Trip to Paris');
      expect(result.trips[1].title).toBe('Trip to Tokyo');

      // Verify correct Supabase calls
      expect(mockSupabaseClient.from).toHaveBeenCalledWith('trips');
      expect(mockSupabaseClient.order).toHaveBeenCalledWith('created_at', { ascending: false });
      expect(mockSupabaseClient.range).toHaveBeenCalledWith(0, 9);
    });

    it('should handle different pages correctly', async () => {
      const userId = 'test-user-123';
      const pagination: PaginationQuery = {
        page: 2,
        limit: 5,
        sortBy: 'title',
        sortOrder: 'asc'
      };

      // Mock count query
      mockSupabaseClient.select.mockReturnValueOnce({
        eq: vi.fn().mockResolvedValueOnce({
          count: 23,
          error: null
        })
      });

      // Mock trips query
      mockSupabaseClient.range.mockResolvedValueOnce({
        data: [],
        error: null
      });

      const result = await tripCrudService.getUserTripsPaginated(userId, pagination);

      // Verify pagination metadata for page 2
      expect(result).toMatchObject({
        page: 2,
        limit: 5,
        total: 23,
        totalPages: 5,
        hasNextPage: true,
        hasPrevPage: true
      });

      // Verify correct offset calculation
      expect(mockSupabaseClient.range).toHaveBeenCalledWith(5, 9);
      expect(mockSupabaseClient.order).toHaveBeenCalledWith('title', { ascending: true });
    });

    it('should handle last page correctly', async () => {
      const userId = 'test-user-123';
      const pagination: PaginationQuery = {
        page: 3,
        limit: 10,
        sortBy: 'created_at',
        sortOrder: 'desc'
      };

      // Mock count query
      mockSupabaseClient.select.mockReturnValueOnce({
        eq: vi.fn().mockResolvedValueOnce({
          count: 25,
          error: null
        })
      });

      // Mock trips query
      mockSupabaseClient.range.mockResolvedValueOnce({
        data: [],
        error: null
      });

      const result = await tripCrudService.getUserTripsPaginated(userId, pagination);

      // Verify pagination metadata for last page
      expect(result).toMatchObject({
        page: 3,
        limit: 10,
        total: 25,
        totalPages: 3,
        hasNextPage: false,
        hasPrevPage: true
      });
    });

    it('should handle empty results', async () => {
      const userId = 'test-user-123';
      const pagination: PaginationQuery = {
        page: 1,
        limit: 20,
        sortBy: 'created_at',
        sortOrder: 'desc'
      };

      // Mock count query
      mockSupabaseClient.select.mockReturnValueOnce({
        eq: vi.fn().mockResolvedValueOnce({
          count: 0,
          error: null
        })
      });

      // Mock trips query
      mockSupabaseClient.range.mockResolvedValueOnce({
        data: [],
        error: null
      });

      const result = await tripCrudService.getUserTripsPaginated(userId, pagination);

      expect(result).toMatchObject({
        trips: [],
        page: 1,
        limit: 20,
        total: 0,
        totalPages: 0,
        hasNextPage: false,
        hasPrevPage: false
      });
    });

    it('should handle errors correctly', async () => {
      const userId = 'test-user-123';
      const pagination: PaginationQuery = {
        page: 1,
        limit: 10,
        sortBy: 'created_at',
        sortOrder: 'desc'
      };

      // Mock count query with error
      mockSupabaseClient.select.mockReturnValueOnce({
        eq: vi.fn().mockResolvedValueOnce({
          count: null,
          error: { message: 'Database connection failed' }
        })
      });

      await expect(
        tripCrudService.getUserTripsPaginated(userId, pagination)
      ).rejects.toThrow('Failed to count trips: Database connection failed');
    });
  });
});