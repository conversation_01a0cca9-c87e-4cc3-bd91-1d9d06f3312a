# TravelViz Monitoring Guide

This guide explains how to monitor TravelViz using native platform monitoring tools. No additional monitoring services or infrastructure are required.

## 1. Vercel Analytics (Frontend)

### Setup

1. Go to your Vercel dashboard: https://vercel.com/dashboard
2. Select your TravelViz project
3. Navigate to the "Analytics" tab
4. Enable Web Vitals monitoring (free tier available)

### What to Monitor

- **Performance Metrics**:
  - First Contentful Paint (FCP)
  - Largest Contentful Paint (LCP)
  - Time to Interactive (TTI)
  - Cumulative Layout Shift (CLS)
- **Security Headers**: Verify headers are being applied correctly
- **Edge Function Performance**: Monitor API route latencies
- **Error Rates**: Track 4xx and 5xx responses

### Alerts

- Set up email notifications for performance degradation
- Configure alerts for error rate spikes

## 2. Render Metrics (Backend)

### Access

1. Log in to Render dashboard: https://dashboard.render.com
2. Select your hub service
3. Click on "Metrics" tab

### Key Metrics

- **Memory Usage**: Monitor for memory leaks (should be stable after fixes)
- **CPU Usage**: Track spikes during AI parsing operations
- **Response Times**: Monitor API endpoint latencies
- **Health Checks**: Ensure service is responding to health endpoint
- **Deploy Status**: Track deployment success/failure

### Built-in Features

- Automatic health checks every 30 seconds
- Memory and CPU graphs over time
- Request count and error rates
- Deployment history and rollback options

## 3. Supabase Dashboard (Database)

### Access

1. Go to Supabase dashboard: https://app.supabase.com
2. Select your TravelViz project
3. Use the monitoring tabs

### Monitoring Areas

#### Database

- **Query Performance**: SQL Editor → Performance tab
- **Slow Queries**: Identify queries taking >100ms
- **RLS Violations**: Monitor failed access attempts
- **Connection Pool**: Track active connections

#### Security

- **RLS Policy Violations**: Authentication → Logs
- **Failed Login Attempts**: Check auth_failed_attempts table
- **API Rate Limiting**: API → Usage tab

#### Storage

- **Bandwidth Usage**: Storage → Usage
- **File Upload Errors**: Storage → Logs

### SQL Queries for Monitoring

```sql
-- Check RLS violations (last 24 hours)
SELECT
  timestamp,
  event_message,
  metadata
FROM auth.audit_log_entries
WHERE timestamp > NOW() - INTERVAL '24 hours'
  AND event_message LIKE '%policy%'
ORDER BY timestamp DESC;

-- Monitor slow queries
SELECT
  query,
  mean_exec_time,
  calls
FROM pg_stat_statements
WHERE mean_exec_time > 100
ORDER BY mean_exec_time DESC
LIMIT 20;

-- Check table sizes
SELECT
  schemaname,
  tablename,
  pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) AS size
FROM pg_tables
WHERE schemaname = 'public'
ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC;
```

## 4. Upstash Console (Redis)

### Access

1. Visit Upstash console: https://console.upstash.com
2. Select your Redis database
3. Navigate to monitoring sections

### Key Metrics

- **Connection Count**: Monitor connection pool efficiency
- **Memory Usage**: Track Redis memory consumption
- **Command Statistics**: Most used commands
- **Latency**: Average response times
- **Request Rate**: Requests per second

### What to Watch

- Connection pool utilization (should be <80%)
- Memory usage trends
- Command latencies (should be <10ms)
- Error rates on Redis operations

## 5. Application-Level Monitoring

### Error Tracking

Use the SecurityUtils error logging in the application:

```typescript
// Errors are logged internally with context
SecurityUtils.sanitizeError(error, {
  userId: req.user?.id,
  endpoint: req.path,
  method: req.method,
});
```

### Circuit Breaker Status

Monitor AI circuit breaker state:

```typescript
// Check circuit breaker status
const status = aiParserService.getCircuitBreakerStatus();
// Log or expose via health endpoint
```

### Health Endpoints

#### Hub Service

```bash
GET /health
```

Returns:

- Redis connection status
- Database connection status
- AI service availability
- Circuit breaker states

#### Frontend

Use Vercel's built-in health checks

## 6. Monitoring Checklist

### Daily

- [ ] Check Vercel Analytics for performance issues
- [ ] Review Render memory/CPU graphs
- [ ] Monitor Supabase RLS violations
- [ ] Check Upstash connection counts

### Weekly

- [ ] Review slow query logs in Supabase
- [ ] Analyze Redis command patterns
- [ ] Check error rates across all services
- [ ] Review security headers effectiveness

### Monthly

- [ ] Analyze cost trends across platforms
- [ ] Review and optimize slow queries
- [ ] Check for unused indexes
- [ ] Audit security logs

## 7. Incident Response

### Performance Degradation

1. Check Vercel Analytics for frontend issues
2. Review Render metrics for backend problems
3. Analyze Supabase slow query logs
4. Check Redis latencies

### Security Incidents

1. Review RLS violation logs in Supabase
2. Check auth_failed_attempts table
3. Analyze security headers in Vercel
4. Review audit logs

### AI Service Issues

1. Check circuit breaker status
2. Monitor OpenRouter rate limits
3. Review AI parsing error logs
4. Check deduplication cache hit rates

## 8. Cost Monitoring

### Vercel

- Monitor bandwidth usage
- Track serverless function invocations
- Review Analytics usage

### Render

- Track memory/CPU usage vs plan limits
- Monitor bandwidth consumption

### Supabase

- Database size and row counts
- API request counts
- Storage bandwidth

### Upstash

- Request count vs plan limits
- Memory usage
- Bandwidth consumption

## Best Practices

1. **Set Up Alerts**: Configure email/webhook alerts for critical metrics
2. **Regular Reviews**: Weekly monitoring review meetings
3. **Document Incidents**: Keep incident logs with root cause analysis
4. **Baseline Metrics**: Establish normal operating ranges
5. **Capacity Planning**: Monitor growth trends for scaling decisions

## Useful Links

- Vercel Status: https://www.vercel-status.com/
- Render Status: https://status.render.com/
- Supabase Status: https://status.supabase.com/
- Upstash Status: https://status.upstash.com/

Remember: All monitoring uses platform-native tools. No additional monitoring infrastructure needed!
