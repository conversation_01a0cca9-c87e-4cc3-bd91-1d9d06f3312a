#!/usr/bin/env node

/**
 * Final Test System Validation
 * 
 * Comprehensive validation that all test components work together
 * and the system is ready for production use
 */

const fs = require('fs');
const path = require('path');
const { spawn } = require('child_process');

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function logHeader(message) {
  log('\n' + '='.repeat(60), 'cyan');
  log(`  ${message}`, 'bright');
  log('='.repeat(60), 'cyan');
}

function logSuccess(message) {
  log(`✅ ${message}`, 'green');
}

function logError(message) {
  log(`❌ ${message}`, 'red');
}

function logWarning(message) {
  log(`⚠️  ${message}`, 'yellow');
}

function logInfo(message) {
  log(`ℹ️  ${message}`, 'blue');
}

// Test results tracking
const validationResults = {
  passed: 0,
  failed: 0,
  warnings: 0,
  tests: []
};

function logTest(name, result, details = {}) {
  const passed = result === true || (typeof result === 'object' && result.success);
  validationResults.tests.push({ name, passed, details });
  
  if (passed) {
    validationResults.passed++;
    logSuccess(name);
  } else {
    validationResults.failed++;
    logError(`${name} - ${details.error || 'Failed'}`);
  }
}

function logWarningTest(name, details = {}) {
  validationResults.warnings++;
  validationResults.tests.push({ name, passed: true, warning: true, details });
  logWarning(`${name} - ${details.warning || 'Warning'}`);
}

async function runCommand(command, args, options = {}) {
  return new Promise((resolve) => {
    const child = spawn(command, args, {
      stdio: 'pipe',
      shell: true,
      timeout: options.timeout || 60000,
      ...options
    });

    let stdout = '';
    let stderr = '';

    child.stdout?.on('data', (data) => {
      stdout += data.toString();
    });

    child.stderr?.on('data', (data) => {
      stderr += data.toString();
    });

    child.on('close', (code) => {
      resolve({ stdout, stderr, code, success: code === 0 });
    });

    child.on('error', (error) => {
      resolve({ stdout, stderr, code: -1, success: false, error: error.message });
    });
  });
}

async function validateTestStructure() {
  logHeader('Test Structure Validation');

  // Check all required test files exist
  const requiredFiles = [
    'tests/test.config.js',
    'tests/api/api.config.js',
    'tests/api/run-api-tests.js',
    'tests/api/utils/api-client.js',
    'tests/api/auth.api.test.js',
    'tests/api/trips.api.test.js',
    'tests/api/import.api.test.js',
    'tests/api/places.api.test.js',
    'tests/api/public.api.test.js',
    'tests/e2e/utils/page-objects.js',
    'tests/e2e/auth-flow.spec.js',
    'tests/e2e/user-journey.spec.js',
    'tests/e2e/import-flow.spec.js',
    'tests/e2e/trip-display.spec.js',
    'tests/ci/health-check.test.js',
    'tests/ci/env-validation.test.js',
    'tests/ci/startup.test.js',
    'playwright.config.js',
    'TESTING.md'
  ];

  requiredFiles.forEach(file => {
    const filePath = path.join(__dirname, '..', file);
    const exists = fs.existsSync(filePath);
    logTest(`Required file: ${file}`, exists, {
      error: exists ? null : `File ${file} is missing`
    });
  });

  // Check test results directory
  const testResultsDir = path.join(__dirname, '../test-results');
  if (!fs.existsSync(testResultsDir)) {
    fs.mkdirSync(testResultsDir, { recursive: true });
    logSuccess('Created test-results directory');
  } else {
    logSuccess('Test results directory exists');
  }

  return true;
}

async function validatePackageJsonScripts() {
  logHeader('Package.json Scripts Validation');

  const packageJsonPath = path.join(__dirname, '../package.json');
  const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
  
  const requiredScripts = [
    'test:api',
    'test:api:auth',
    'test:api:trips',
    'test:api:import',
    'test:api:places',
    'test:api:public',
    'test:e2e',
    'test:e2e:headed',
    'test:e2e:auth',
    'test:e2e:journey',
    'test:e2e:import',
    'test:e2e:display',
    'test:ci',
    'test:ci:env',
    'test:ci:startup',
    'test:validate',
    'test:all'
  ];

  requiredScripts.forEach(script => {
    const exists = !!packageJson.scripts[script];
    logTest(`NPM script: ${script}`, exists, {
      error: exists ? null : `Script ${script} is missing from package.json`
    });
  });

  return true;
}

async function validateTestConfiguration() {
  logHeader('Test Configuration Validation');

  // Test main config
  try {
    const testConfig = require('./test.config.js');
    logTest('Main test configuration loads', true, {
      apiBaseUrl: testConfig.api.baseUrl,
      e2eBaseUrl: testConfig.e2e.baseUrl
    });
  } catch (error) {
    logTest('Main test configuration loads', false, { error: error.message });
  }

  // Test API config
  try {
    const apiConfig = require('./api/api.config.js');
    logTest('API test configuration loads', true, {
      baseUrl: apiConfig.baseUrl
    });
  } catch (error) {
    logTest('API test configuration loads', false, { error: error.message });
  }

  // Test Playwright config
  try {
    const playwrightConfig = require('../playwright.config.js');
    logTest('Playwright configuration loads', true, {
      testDir: playwrightConfig.testDir
    });
  } catch (error) {
    logTest('Playwright configuration loads', false, { error: error.message });
  }

  return true;
}

async function validateCITests() {
  logHeader('CI/CD Tests Validation');

  // Run health check
  logInfo('Running CI health check...');
  const healthResult = await runCommand('node', ['tests/ci/health-check.test.js'], {
    cwd: path.join(__dirname, '..')
  });

  logTest('CI health check passes', healthResult.success, {
    error: healthResult.success ? null : 'Health check failed - see output above'
  });

  // Run environment validation
  logInfo('Running environment validation...');
  const envResult = await runCommand('node', ['tests/ci/env-validation.test.js'], {
    cwd: path.join(__dirname, '..')
  });

  if (envResult.success) {
    logTest('Environment validation passes', true);
  } else {
    logWarningTest('Environment validation has issues', {
      warning: 'Some environment variables may be missing or invalid'
    });
  }

  return healthResult.success;
}

async function validateAPITestFoundation() {
  logHeader('API Test Foundation Validation');

  // Check if we can run the foundation validator
  logInfo('Running foundation validator...');
  const foundationResult = await runCommand('node', ['tests/validate-foundation.js'], {
    cwd: path.join(__dirname, '..')
  });

  if (foundationResult.success) {
    logTest('Test foundation validation passes', true);
  } else {
    logWarningTest('Test foundation validation has issues', {
      warning: 'Foundation tests may not be fully ready - check servers are running'
    });
  }

  // Check if API client can be instantiated
  try {
    const ApiTestClient = require('./api/utils/api-client.js');
    const client = new ApiTestClient();
    logTest('API client instantiates correctly', true);
  } catch (error) {
    logTest('API client instantiates correctly', false, { error: error.message });
  }

  return true;
}

async function validateE2ETestFoundation() {
  logHeader('E2E Test Foundation Validation');

  // Check if page objects can be loaded
  try {
    const pageObjects = require('./e2e/utils/page-objects.js');
    const hasRequiredObjects = !!(pageObjects.HomePage && pageObjects.LoginPage && pageObjects.DashboardPage);
    logTest('Page objects load correctly', hasRequiredObjects, {
      error: hasRequiredObjects ? null : 'Required page objects are missing'
    });
  } catch (error) {
    logTest('Page objects load correctly', false, { error: error.message });
  }

  // Check if Playwright is available
  try {
    const playwrightResult = await runCommand('npx', ['playwright', '--version']);
    logTest('Playwright is available', playwrightResult.success, {
      version: playwrightResult.stdout.trim(),
      error: playwrightResult.success ? null : 'Playwright not installed or not working'
    });
  } catch (error) {
    logTest('Playwright is available', false, { error: error.message });
  }

  return true;
}

async function validateDocumentation() {
  logHeader('Documentation Validation');

  // Check if TESTING.md exists and has content
  const testingMdPath = path.join(__dirname, '../TESTING.md');
  const testingMdExists = fs.existsSync(testingMdPath);
  logTest('TESTING.md exists', testingMdExists);

  if (testingMdExists) {
    const content = fs.readFileSync(testingMdPath, 'utf8');
    const hasRequiredSections = content.includes('## API Integration Tests') && 
                               content.includes('## E2E Tests') && 
                               content.includes('## CI/CD Validation Tests');
    logTest('TESTING.md has required sections', hasRequiredSections);
  }

  // Check if TEST_PATTERNS.md was generated
  const testPatternsPath = path.join(__dirname, '../TEST_PATTERNS.md');
  const testPatternsExists = fs.existsSync(testPatternsPath);
  if (testPatternsExists) {
    logSuccess('TEST_PATTERNS.md exists (generated by foundation validator)');
  } else {
    logInfo('TEST_PATTERNS.md not found (will be generated by foundation validator)');
  }

  return true;
}

async function generateFinalReport() {
  logHeader('Generating Final Validation Report');

  const report = {
    timestamp: new Date().toISOString(),
    environment: process.env.NODE_ENV || 'development',
    ci: !!process.env.CI,
    platform: process.platform,
    nodeVersion: process.version,
    validation: {
      total: validationResults.tests.length,
      passed: validationResults.passed,
      failed: validationResults.failed,
      warnings: validationResults.warnings,
      successRate: Math.round((validationResults.passed / validationResults.tests.length) * 100)
    },
    testSystem: {
      apiTests: 5, // auth, trips, import, places, public
      e2eTests: 4, // auth-flow, user-journey, import-flow, trip-display
      ciTests: 3,  // health-check, env-validation, startup
      totalTestFiles: 12
    },
    capabilities: [
      'Real API integration testing with token management',
      'Complete user journey E2E testing',
      'AI import functionality testing',
      'Trip visualization and interaction testing',
      'Environment and startup validation',
      'Cross-browser testing support',
      'Screenshot capture on failures',
      'Comprehensive error reporting'
    ],
    tests: validationResults.tests
  };

  const reportPath = path.join(__dirname, '../test-results/final-validation-report.json');
  
  // Ensure directory exists
  const reportDir = path.dirname(reportPath);
  if (!fs.existsSync(reportDir)) {
    fs.mkdirSync(reportDir, { recursive: true });
  }

  fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
  logSuccess(`Final validation report generated: ${reportPath}`);

  return report;
}

async function main() {
  logHeader('TravelViz Final Test System Validation');
  
  log('This script performs a comprehensive validation of the entire test system', 'bright');
  log('to ensure everything is ready for production use.\n');

  try {
    // Run all validation steps
    await validateTestStructure();
    await validatePackageJsonScripts();
    await validateTestConfiguration();
    const ciPassed = await validateCITests();
    await validateAPITestFoundation();
    await validateE2ETestFoundation();
    await validateDocumentation();
    
    // Generate final report
    const report = await generateFinalReport();
    
    // Summary
    logHeader('Final Validation Results');
    
    if (validationResults.failed === 0) {
      logSuccess('🎉 All validations passed! Test system is ready for production!');
      
      if (validationResults.warnings > 0) {
        logWarning(`⚠️  ${validationResults.warnings} warnings found - review recommended`);
      }
      
      log('\n🚀 Test System Capabilities:', 'bright');
      report.capabilities.forEach(capability => {
        log(`• ${capability}`, 'green');
      });
      
      log('\n📋 Available Test Commands:', 'bright');
      log('• pnpm test:all          - Run all tests (CI + API + E2E)');
      log('• pnpm test:api          - Run all API integration tests');
      log('• pnpm test:e2e          - Run all E2E tests');
      log('• pnpm test:ci           - Run CI/CD validation tests');
      log('• pnpm test:validate     - Validate test foundation');
      
      log('\n📚 Documentation:', 'bright');
      log('• TESTING.md             - Comprehensive testing guide');
      log('• test-results/          - Test reports and screenshots');
      
      log('\n🎯 Next Steps:', 'bright');
      log('1. Start development servers: pnpm dev');
      log('2. Run foundation validation: pnpm test:validate');
      log('3. Run API tests: pnpm test:api');
      log('4. Run E2E tests: pnpm test:e2e:headed');
      log('5. Set up CI/CD with: pnpm test:ci');
      
      process.exit(0);
    } else {
      logError(`❌ ${validationResults.failed} validations failed`);
      
      if (validationResults.warnings > 0) {
        logWarning(`⚠️  ${validationResults.warnings} warnings also found`);
      }
      
      log('\nFailed validations:', 'bright');
      validationResults.tests
        .filter(t => !t.passed)
        .forEach(t => {
          log(`• ${t.name}: ${t.details.error || 'Unknown error'}`);
        });
      
      log('\nTroubleshooting:', 'bright');
      log('• Check all required files are present');
      log('• Verify package.json scripts are correct');
      log('• Ensure test configurations load properly');
      log('• Run individual validation steps to isolate issues');
      
      process.exit(1);
    }
    
  } catch (error) {
    logError(`Fatal error: ${error.message}`);
    console.error(error.stack);
    process.exit(1);
  }
}

// Handle process termination
process.on('SIGINT', () => {
  log('\nFinal validation interrupted by user', 'yellow');
  process.exit(1);
});

// Run the final validation
main();