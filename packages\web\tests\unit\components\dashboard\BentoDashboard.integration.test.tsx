import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, screen, waitFor } from '@testing-library/react';
import { BentoDashboard } from '@/components/dashboard/BentoDashboard';
import { useAuthStore } from '@/stores/auth.store';
import { useTripStore } from '@/stores/trip.store';
import { useRouter } from 'next/navigation';

// Mock dependencies
vi.mock('next/navigation');
vi.mock('@/stores/auth.store');
vi.mock('@/stores/trip.store');

// Mock framer-motion to simplify testing
vi.mock('framer-motion', async () => {
  const actual = await vi.importActual('framer-motion');
  return {
    ...actual,
    motion: {
      div: vi.fn(({ children, ...props }: any) => {
        return <div {...props}>{children}</div>;
      }),
    },
  };
});

// Mock number ticker to simplify output
vi.mock('@/components/magic-ui/number-ticker-dynamic', () => ({
  DynamicNumberTicker: ({ value }: { value: number }) => <span>{value}</span>,
}));

describe('BentoDashboard Integration - Authentication Cascade Prevention', () => {
  const mockRouter = { push: vi.fn() };
  const mockUser = { id: 'user-1', email: '<EMAIL>', name: 'Test User' };

  beforeEach(() => {
    vi.clearAllMocks();
    vi.mocked(useRouter).mockReturnValue(mockRouter as any);
  });

  describe('Dashboard Mount Authentication Flow', () => {
    it('should handle authentication failure gracefully on mount', async () => {
      // Simulate auth failure scenario
      const fetchTripsPaginated = vi.fn().mockRejectedValue(new Error('Unauthorized'));
      
      vi.mocked(useAuthStore).mockReturnValue({
        user: mockUser,
      } as any);
      
      vi.mocked(useTripStore).mockReturnValue({
        trips: [],
        fetchTripsPaginated,
        isLoading: false,
      } as any);

      render(<BentoDashboard />);

      await waitFor(() => {
        // Should call fetch once, not repeatedly
        expect(fetchTripsPaginated).toHaveBeenCalledTimes(1);
        expect(fetchTripsPaginated).toHaveBeenCalledWith(1, 20);
      });

      // Dashboard should still render with empty state
      expect(screen.getByText(/Welcome back/i)).toBeInTheDocument();
      expect(screen.getByText(/No trips yet/i)).toBeInTheDocument();
    });

    it('should not retry failed requests on subsequent renders', async () => {
      const fetchTripsPaginated = vi.fn().mockRejectedValue(new Error('401'));
      
      vi.mocked(useAuthStore).mockReturnValue({ user: mockUser } as any);
      vi.mocked(useTripStore).mockReturnValue({
        trips: [],
        fetchTripsPaginated,
        isLoading: false,
      } as any);

      const { rerender } = render(<BentoDashboard />);

      await waitFor(() => {
        expect(fetchTripsPaginated).toHaveBeenCalledTimes(1);
      });

      // Re-render should not trigger another fetch
      rerender(<BentoDashboard />);

      await waitFor(() => {
        expect(fetchTripsPaginated).toHaveBeenCalledTimes(1);
      });
    });

    it('should display loading state during authentication', () => {
      vi.mocked(useAuthStore).mockReturnValue({ user: mockUser } as any);
      vi.mocked(useTripStore).mockReturnValue({
        trips: [],
        fetchTripsPaginated: vi.fn(),
        isLoading: true,
      } as any);

      render(<BentoDashboard />);

      // Should show welcome message even while loading
      expect(screen.getByText(/Welcome back, Test/i)).toBeInTheDocument();
    });
  });

  describe('Dashboard Data Display', () => {
    it('should handle successful data load after auth', async () => {
      const mockTrips = [
        { id: 'trip-1', title: 'Paris Trip', destination: 'Paris', status: 'planning' },
        { id: 'trip-2', title: 'Tokyo Trip', destination: 'Tokyo', status: 'completed' },
      ];

      const fetchTripsPaginated = vi.fn().mockResolvedValue(undefined);
      
      vi.mocked(useAuthStore).mockReturnValue({ user: mockUser } as any);
      vi.mocked(useTripStore).mockReturnValue({
        trips: mockTrips,
        fetchTripsPaginated,
        isLoading: false,
      } as any);

      render(<BentoDashboard />);

      await waitFor(() => {
        expect(screen.getByText('Paris Trip')).toBeInTheDocument();
        expect(screen.getByText('Tokyo Trip')).toBeInTheDocument();
      });

      // Stats should be calculated correctly
      expect(screen.getByText('2')).toBeInTheDocument(); // Total trips
    });

    it('should handle empty state without cascading API calls', () => {
      vi.mocked(useAuthStore).mockReturnValue({ user: mockUser } as any);
      vi.mocked(useTripStore).mockReturnValue({
        trips: [],
        fetchTripsPaginated: vi.fn(),
        isLoading: false,
      } as any);

      render(<BentoDashboard />);

      expect(screen.getByText(/No trips yet/i)).toBeInTheDocument();
      expect(screen.getByText(/Start planning your first adventure/i)).toBeInTheDocument();
    });
  });

  describe('User Interactions', () => {
    it('should navigate to import page when clicking Import Trip', async () => {
      vi.mocked(useAuthStore).mockReturnValue({ user: mockUser } as any);
      vi.mocked(useTripStore).mockReturnValue({
        trips: [],
        fetchTripsPaginated: vi.fn(),
        isLoading: false,
      } as any);

      render(<BentoDashboard />);

      const importButton = screen.getByText('Import Trip');
      importButton.click();

      expect(mockRouter.push).toHaveBeenCalledWith('/import');
    });

    it('should navigate to new trip page when clicking Create Trip', () => {
      vi.mocked(useAuthStore).mockReturnValue({ user: mockUser } as any);
      vi.mocked(useTripStore).mockReturnValue({
        trips: [],
        fetchTripsPaginated: vi.fn(),
        isLoading: false,
      } as any);

      render(<BentoDashboard />);

      const createButton = screen.getByText('Create Trip');
      createButton.click();

      expect(mockRouter.push).toHaveBeenCalledWith('/plan/new');
    });
  });

  describe('Critical Auth Cascade Prevention Tests', () => {
    it('should only attempt to fetch trips once on mount, even with 401 errors', async () => {
      // This is THE critical test - prevents infinite retry loops
      const fetchTripsPaginated = vi.fn().mockRejectedValue(new Error('401 Unauthorized'));
      
      vi.mocked(useAuthStore).mockReturnValue({ user: mockUser } as any);
      vi.mocked(useTripStore).mockReturnValue({
        trips: [],
        fetchTripsPaginated,
        isLoading: false,
      } as any);

      render(<BentoDashboard />);

      // Wait for initial fetch attempt
      await waitFor(() => {
        expect(fetchTripsPaginated).toHaveBeenCalledTimes(1);
      });

      // Wait a bit more to ensure no retry attempts
      await new Promise(resolve => setTimeout(resolve, 100));

      // Critical assertion: exactly one call, no retries
      expect(fetchTripsPaginated).toHaveBeenCalledTimes(1);
      expect(fetchTripsPaginated).toHaveBeenCalledWith(1, 20);
    });

    it('should not create dependency loops between auth and trip stores', async () => {
      const fetchTripsPaginated = vi.fn().mockRejectedValue(new Error('401'));
      let authStoreCallCount = 0;
      let tripStoreCallCount = 0;

      // Track store access
      vi.mocked(useAuthStore).mockImplementation(() => {
        authStoreCallCount++;
        return { user: mockUser } as any;
      });

      vi.mocked(useTripStore).mockImplementation(() => {
        tripStoreCallCount++;
        return {
          trips: [],
          fetchTripsPaginated,
          isLoading: false,
        } as any;
      });

      const { rerender } = render(<BentoDashboard />);

      await waitFor(() => {
        expect(fetchTripsPaginated).toHaveBeenCalledTimes(1);
      });

      const initialAuthCalls = authStoreCallCount;
      const initialTripCalls = tripStoreCallCount;

      // Force re-render
      rerender(<BentoDashboard />);

      // Store hooks should be called again on re-render, but not excessively
      expect(authStoreCallCount).toBeGreaterThan(initialAuthCalls);
      expect(tripStoreCallCount).toBeGreaterThan(initialTripCalls);
      
      // But fetch should NOT be called again
      expect(fetchTripsPaginated).toHaveBeenCalledTimes(1);
    });

    it('should handle network errors without cascading retries', async () => {
      const networkError = new Error('Network request failed');
      const fetchTripsPaginated = vi.fn().mockRejectedValue(networkError);
      
      vi.mocked(useAuthStore).mockReturnValue({ user: mockUser } as any);
      vi.mocked(useTripStore).mockReturnValue({
        trips: [],
        fetchTripsPaginated,
        isLoading: false,
      } as any);

      render(<BentoDashboard />);

      await waitFor(() => {
        expect(fetchTripsPaginated).toHaveBeenCalledTimes(1);
      });

      // Component should still be rendered and stable
      expect(screen.getByText(/Welcome back/i)).toBeInTheDocument();
      expect(screen.getByText(/No trips yet/i)).toBeInTheDocument();
    });

    it('should handle rate limit errors (429) without retrying', async () => {
      const rateLimitError = new Error('Rate limit exceeded');
      (rateLimitError as any).status = 429;
      const fetchTripsPaginated = vi.fn().mockRejectedValue(rateLimitError);
      
      vi.mocked(useAuthStore).mockReturnValue({ user: mockUser } as any);
      vi.mocked(useTripStore).mockReturnValue({
        trips: [],
        fetchTripsPaginated,
        isLoading: false,
      } as any);

      render(<BentoDashboard />);

      await waitFor(() => {
        expect(fetchTripsPaginated).toHaveBeenCalledTimes(1);
      });

      // No retries should occur
      await new Promise(resolve => setTimeout(resolve, 200));
      expect(fetchTripsPaginated).toHaveBeenCalledTimes(1);
    });
  });

  describe('Component Stability and Memory Management', () => {
    it('should cleanup properly when unmounting during active fetch', async () => {
      const fetchTripsPaginated = vi.fn().mockImplementation(
        () => new Promise(resolve => setTimeout(resolve, 1000))
      );
      
      vi.mocked(useAuthStore).mockReturnValue({ user: mockUser } as any);
      vi.mocked(useTripStore).mockReturnValue({
        trips: [],
        fetchTripsPaginated,
        isLoading: true,
      } as any);

      const { unmount } = render(<BentoDashboard />);

      // Verify fetch was initiated
      expect(fetchTripsPaginated).toHaveBeenCalledTimes(1);

      // Unmount before fetch completes
      unmount();

      // No errors should occur (would fail if state updates happen after unmount)
    });

    it('should handle rapid mount/unmount cycles without issues', async () => {
      const fetchTripsPaginated = vi.fn().mockResolvedValue(undefined);
      
      vi.mocked(useAuthStore).mockReturnValue({ user: mockUser } as any);
      vi.mocked(useTripStore).mockReturnValue({
        trips: [],
        fetchTripsPaginated,
        isLoading: false,
      } as any);

      // Rapid mount/unmount cycle
      for (let i = 0; i < 3; i++) {
        const { unmount } = render(<BentoDashboard />);
        await new Promise(resolve => setTimeout(resolve, 10));
        unmount();
      }

      // Should have been called 3 times (once per mount)
      expect(fetchTripsPaginated).toHaveBeenCalledTimes(3);
    });
  });

  describe('Error Recovery and Graceful Degradation', () => {
    it('should show appropriate UI when trips fail to load', async () => {
      const fetchTripsPaginated = vi.fn().mockRejectedValue(new Error('Server error'));
      
      vi.mocked(useAuthStore).mockReturnValue({ user: mockUser } as any);
      vi.mocked(useTripStore).mockReturnValue({
        trips: [],
        fetchTripsPaginated,
        isLoading: false,
        error: 'Failed to load trips',
      } as any);

      render(<BentoDashboard />);

      await waitFor(() => {
        expect(fetchTripsPaginated).toHaveBeenCalledTimes(1);
      });

      // Should show empty state, not error state
      expect(screen.getByText(/No trips yet/i)).toBeInTheDocument();
      
      // Stats should show zeros
      const zeroStats = screen.getAllByText('0');
      expect(zeroStats.length).toBeGreaterThan(1);
    });

    it('should handle malformed trip data gracefully', () => {
      const malformedTrips = [
        { id: 'trip-1', title: null, destination: undefined }, // Missing required fields
        { id: 'trip-2' }, // Severely incomplete
      ];
      
      vi.mocked(useAuthStore).mockReturnValue({ user: mockUser } as any);
      vi.mocked(useTripStore).mockReturnValue({
        trips: malformedTrips as any,
        fetchTripsPaginated: vi.fn(),
        isLoading: false,
      } as any);

      // Should not crash when rendering malformed data
      expect(() => render(<BentoDashboard />)).not.toThrow();
    });
  });

  describe('Performance and Optimization', () => {
    it('should not re-fetch when switching between loading states', async () => {
      const fetchTripsPaginated = vi.fn().mockResolvedValue(undefined);
      
      vi.mocked(useAuthStore).mockReturnValue({ user: mockUser } as any);
      
      const { rerender } = render(
        <BentoDashboard />,
        {
          wrapper: ({ children }) => {
            vi.mocked(useTripStore).mockReturnValue({
              trips: [],
              fetchTripsPaginated,
              isLoading: true,
            } as any);
            return <>{children}</>;
          }
        }
      );

      expect(fetchTripsPaginated).toHaveBeenCalledTimes(1);

      // Change to not loading
      vi.mocked(useTripStore).mockReturnValue({
        trips: [],
        fetchTripsPaginated,
        isLoading: false,
      } as any);

      rerender(<BentoDashboard />);

      // Should not trigger another fetch
      expect(fetchTripsPaginated).toHaveBeenCalledTimes(1);
    });

    it('should debounce rapid state changes', async () => {
      const fetchTripsPaginated = vi.fn().mockResolvedValue(undefined);
      
      vi.mocked(useAuthStore).mockReturnValue({ user: mockUser } as any);
      vi.mocked(useTripStore).mockReturnValue({
        trips: [],
        fetchTripsPaginated,
        isLoading: false,
      } as any);

      const { rerender } = render(<BentoDashboard />);

      // Rapid re-renders
      for (let i = 0; i < 5; i++) {
        rerender(<BentoDashboard />);
      }

      await waitFor(() => {
        expect(fetchTripsPaginated).toHaveBeenCalledTimes(1);
      });

      // Still only one fetch despite multiple renders
      expect(fetchTripsPaginated).toHaveBeenCalledTimes(1);
    });
  });
});