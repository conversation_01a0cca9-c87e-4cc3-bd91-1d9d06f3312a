/**
 * Trip Display E2E Test
 * 
 * Tests trip visualization, timeline, and map functionality
 * Uses the proven authentication pattern for setup
 */

const { test, expect } = require('@playwright/test');
const { LoginPage, DashboardPage } = require('./utils/page-objects');
const testConfig = require('../test.config');

// Test configuration
const TEST_USER = {
  email: testConfig.auth.testUserEmail,
  password: testConfig.auth.testUserPassword
};

class TripDisplayPage {
  constructor(page) {
    this.page = page;
  }

  async expectTripLoaded() {
    try {
      // Wait for trip-specific elements
      const tripSelectors = [
        'h1, h2', // Trip title
        '.trip-title',
        '[data-testid="trip-title"]',
        '.timeline',
        '[data-testid="timeline"]',
        '.activities',
        '[data-testid="activities"]'
      ];

      for (const selector of tripSelectors) {
        try {
          await this.page.waitForSelector(selector, { timeout: 5000 });
          return true;
        } catch (error) {
          // Continue to next selector
        }
      }

      return false;
    } catch (error) {
      return false;
    }
  }

  async expectTimelineVisible() {
    try {
      const timelineSelectors = [
        '.timeline',
        '[data-testid="timeline"]',
        '.trip-timeline',
        '.itinerary',
        '[data-testid="itinerary"]'
      ];

      for (const selector of timelineSelectors) {
        try {
          const element = await this.page.locator(selector).first();
          if (await element.isVisible({ timeout: 5000 })) {
            return true;
          }
        } catch (error) {
          // Continue to next selector
        }
      }

      return false;
    } catch (error) {
      return false;
    }
  }

  async expectMapVisible() {
    try {
      const mapSelectors = [
        '.map',
        '[data-testid="map"]',
        '.mapbox-gl-map',
        '#map',
        'canvas[class*="mapbox"]'
      ];

      for (const selector of mapSelectors) {
        try {
          const element = await this.page.locator(selector).first();
          if (await element.isVisible({ timeout: 10000 })) {
            return true;
          }
        } catch (error) {
          // Continue to next selector
        }
      }

      return false;
    } catch (error) {
      return false;
    }
  }

  async expectActivitiesCount(expectedCount) {
    try {
      const activitySelectors = [
        '.activity',
        '[data-testid="activity"]',
        '.trip-activity',
        '.activity-item',
        '[data-testid="activity-item"]'
      ];

      for (const selector of activitySelectors) {
        try {
          const elements = await this.page.locator(selector).all();
          if (elements.length >= expectedCount) {
            return elements.length;
          }
        } catch (error) {
          // Continue to next selector
        }
      }

      return 0;
    } catch (error) {
      return 0;
    }
  }

  async dragActivity(fromIndex, toIndex) {
    try {
      const activitySelectors = [
        '.activity',
        '[data-testid="activity"]',
        '.trip-activity'
      ];

      for (const selector of activitySelectors) {
        try {
          const activities = await this.page.locator(selector).all();
          if (activities.length > Math.max(fromIndex, toIndex)) {
            const fromElement = activities[fromIndex];
            const toElement = activities[toIndex];
            
            await fromElement.dragTo(toElement);
            return true;
          }
        } catch (error) {
          // Continue to next selector
        }
      }

      return false;
    } catch (error) {
      return false;
    }
  }

  async getTripTitle() {
    try {
      const titleSelectors = [
        'h1',
        'h2',
        '.trip-title',
        '[data-testid="trip-title"]'
      ];

      for (const selector of titleSelectors) {
        try {
          const element = await this.page.locator(selector).first();
          if (await element.isVisible({ timeout: 5000 })) {
            return await element.textContent();
          }
        } catch (error) {
          // Continue to next selector
        }
      }

      return null;
    } catch (error) {
      return null;
    }
  }
}

test.describe('Trip Display and Interaction', () => {
  test.beforeEach(async ({ page }) => {
    // Set up test isolation
    await page.context().clearCookies();
    await page.context().clearPermissions();
    
    // Login before each test
    const loginPage = new LoginPage(page);
    await loginPage.navigate();
    await loginPage.login(TEST_USER.email, TEST_USER.password);
    const loginSuccess = await loginPage.expectLoginSuccess();
    expect(loginSuccess).toBe(true);
    
    // Create test results directory
    const fs = require('fs');
    const path = require('path');
    const resultsDir = path.join(__dirname, '../../test-results');
    if (!fs.existsSync(resultsDir)) {
      fs.mkdirSync(resultsDir, { recursive: true });
    }
  });

  test('should display trip timeline and activities', async ({ page }) => {
    console.log('📅 Testing trip timeline display...');
    
    // Navigate to dashboard to find existing trips
    const dashboardPage = new DashboardPage(page);
    await dashboardPage.navigate();
    expect(await dashboardPage.isLoaded()).toBe(true);
    
    await page.screenshot({ 
      path: 'test-results/trip-display-01-dashboard.png',
      fullPage: true 
    });
    
    // Look for existing trips
    const tripsCount = await dashboardPage.getTripsCount();
    console.log(`   Found ${tripsCount} trips on dashboard`);
    
    if (tripsCount > 0) {
      // Click on first trip
      const tripSelectors = [
        '.trip-card:first-child',
        '[data-testid="trip-card"]:first-child',
        '.trip-item:first-child',
        'a[href*="/plan/"]:first-child',
        'a[href*="/trip/"]:first-child'
      ];

      let tripClicked = false;
      for (const selector of tripSelectors) {
        try {
          const element = await page.locator(selector).first();
          if (await element.isVisible({ timeout: 5000 })) {
            await element.click();
            tripClicked = true;
            console.log('   ✅ Clicked on first trip');
            break;
          }
        } catch (error) {
          // Continue to next selector
        }
      }

      if (tripClicked) {
        // Wait for trip page to load
        await page.waitForLoadState('networkidle');
        
        const tripDisplayPage = new TripDisplayPage(page);
        
        // Test trip display elements
        const tripLoaded = await tripDisplayPage.expectTripLoaded();
        expect(tripLoaded).toBe(true);
        console.log('   ✅ Trip page loaded');
        
        await page.screenshot({ 
          path: 'test-results/trip-display-02-trip-page.png',
          fullPage: true 
        });
        
        // Test timeline visibility
        const timelineVisible = await tripDisplayPage.expectTimelineVisible();
        if (timelineVisible) {
          console.log('   ✅ Timeline is visible');
        } else {
          console.log('   ℹ️  Timeline not immediately visible');
        }
        
        // Test activities count
        const activitiesCount = await tripDisplayPage.expectActivitiesCount(1);
        console.log(`   Found ${activitiesCount} activities`);
        expect(activitiesCount).toBeGreaterThanOrEqual(0);
        
        // Get trip title
        const tripTitle = await tripDisplayPage.getTripTitle();
        if (tripTitle) {
          console.log(`   Trip title: ${tripTitle}`);
        }
        
        await page.screenshot({ 
          path: 'test-results/trip-display-03-timeline.png',
          fullPage: true 
        });
        
      } else {
        console.log('   ⚠️  Could not click on trip - testing with current page');
      }
    } else {
      console.log('   ℹ️  No trips found - skipping trip display test');
      console.log('   Create a trip first by running the import flow test');
    }
    
    console.log('🎉 Trip timeline test completed');
  });

  test('should display map integration', async ({ page }) => {
    console.log('🗺️ Testing map integration...');
    
    // Navigate to dashboard
    const dashboardPage = new DashboardPage(page);
    await dashboardPage.navigate();
    
    const tripsCount = await dashboardPage.getTripsCount();
    
    if (tripsCount > 0) {
      // Click on first trip
      const tripLink = await page.locator('.trip-card, [data-testid="trip-card"], a[href*="/plan/"]').first();
      if (await tripLink.isVisible({ timeout: 5000 })) {
        await tripLink.click();
        await page.waitForLoadState('networkidle');
        
        const tripDisplayPage = new TripDisplayPage(page);
        
        // Wait a bit for map to load
        await page.waitForTimeout(5000);
        
        // Test map visibility
        const mapVisible = await tripDisplayPage.expectMapVisible();
        if (mapVisible) {
          console.log('   ✅ Map is visible');
        } else {
          console.log('   ℹ️  Map not immediately visible');
        }
        
        await page.screenshot({ 
          path: 'test-results/trip-display-04-map.png',
          fullPage: true 
        });
        
        // Look for map controls
        const mapControls = await page.locator('.mapbox-ctrl, .map-control, [class*="map-control"]').count();
        if (mapControls > 0) {
          console.log(`   ✅ Found ${mapControls} map controls`);
        }
        
      }
    } else {
      console.log('   ℹ️  No trips found for map testing');
    }
    
    console.log('🎉 Map integration test completed');
  });

  test('should handle trip interaction features', async ({ page }) => {
    console.log('🎯 Testing trip interaction features...');
    
    // Navigate to dashboard
    const dashboardPage = new DashboardPage(page);
    await dashboardPage.navigate();
    
    const tripsCount = await dashboardPage.getTripsCount();
    
    if (tripsCount > 0) {
      // Click on first trip
      const tripLink = await page.locator('.trip-card, [data-testid="trip-card"], a[href*="/plan/"]').first();
      if (await tripLink.isVisible({ timeout: 5000 })) {
        await tripLink.click();
        await page.waitForLoadState('networkidle');
        
        const tripDisplayPage = new TripDisplayPage(page);
        
        await page.screenshot({ 
          path: 'test-results/trip-display-05-before-interaction.png',
          fullPage: true 
        });
        
        // Test drag and drop if activities exist
        const activitiesCount = await tripDisplayPage.expectActivitiesCount(2);
        if (activitiesCount >= 2) {
          console.log('   Testing drag and drop...');
          const dragSuccess = await tripDisplayPage.dragActivity(0, 1);
          if (dragSuccess) {
            console.log('   ✅ Drag and drop interaction successful');
          } else {
            console.log('   ℹ️  Drag and drop not available or failed');
          }
          
          await page.screenshot({ 
            path: 'test-results/trip-display-06-after-drag.png',
            fullPage: true 
          });
        } else {
          console.log('   ℹ️  Not enough activities for drag and drop test');
        }
        
        // Test edit functionality
        const editSelectors = [
          'button:has-text("Edit")',
          '[data-testid="edit-button"]',
          '.edit-button',
          'button[class*="edit"]'
        ];

        for (const selector of editSelectors) {
          try {
            const editButton = await page.locator(selector).first();
            if (await editButton.isVisible({ timeout: 3000 })) {
              await editButton.click();
              console.log('   ✅ Edit button clicked');
              
              await page.waitForTimeout(2000);
              await page.screenshot({ 
                path: 'test-results/trip-display-07-edit-mode.png',
                fullPage: true 
              });
              break;
            }
          } catch (error) {
            // Continue to next selector
          }
        }
        
      }
    } else {
      console.log('   ℹ️  No trips found for interaction testing');
    }
    
    console.log('🎉 Trip interaction test completed');
  });

  test('should handle responsive design', async ({ page }) => {
    console.log('📱 Testing responsive design...');
    
    // Test mobile viewport
    await page.setViewportSize({ width: 375, height: 667 });
    
    const dashboardPage = new DashboardPage(page);
    await dashboardPage.navigate();
    
    await page.screenshot({ 
      path: 'test-results/trip-display-08-mobile-dashboard.png',
      fullPage: true 
    });
    
    const tripsCount = await dashboardPage.getTripsCount();
    
    if (tripsCount > 0) {
      // Click on first trip in mobile view
      const tripLink = await page.locator('.trip-card, [data-testid="trip-card"], a[href*="/plan/"]').first();
      if (await tripLink.isVisible({ timeout: 5000 })) {
        await tripLink.click();
        await page.waitForLoadState('networkidle');
        
        await page.screenshot({ 
          path: 'test-results/trip-display-09-mobile-trip.png',
          fullPage: true 
        });
        
        console.log('   ✅ Mobile view renders correctly');
      }
    }
    
    // Test tablet viewport
    await page.setViewportSize({ width: 768, height: 1024 });
    await page.reload();
    
    await page.screenshot({ 
      path: 'test-results/trip-display-10-tablet.png',
      fullPage: true 
    });
    
    console.log('   ✅ Tablet view renders correctly');
    
    // Reset to desktop
    await page.setViewportSize({ width: 1280, height: 720 });
    
    console.log('🎉 Responsive design test completed');
  });

  test.afterEach(async ({ page }, testInfo) => {
    // Take screenshot on failure
    if (testInfo.status !== testInfo.expectedStatus) {
      const screenshotPath = `test-results/trip-display-failure-${testInfo.title.replace(/\s+/g, '-')}-${Date.now()}.png`;
      await page.screenshot({ 
        path: screenshotPath,
        fullPage: true 
      });
      console.log(`📸 Failure screenshot saved: ${screenshotPath}`);
    }
  });
});

// Global setup
test.beforeAll(async () => {
  console.log('🚀 E2E Trip Display Tests Starting');
  console.log('==================================');
  console.log(`Base URL: ${testConfig.e2e.baseUrl}`);
  console.log(`Test User: ${TEST_USER.email}`);
  console.log('Testing trip visualization, timeline, and map functionality');
  console.log('');
});