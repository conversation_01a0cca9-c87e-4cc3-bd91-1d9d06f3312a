# AI Import Testing Guide with Logging

## Overview

This directory contains comprehensive integration tests for AI import, PDF import, and chat itinerary features. All tests now include detailed logging to help debug issues.

## Test Files

1. **`ai-import-fixed.test.ts`** - Fixed test suite with proper error handling and logging
2. **`ai-import-enhanced.test.ts`** - Comprehensive tests with detailed assertions
3. **`pdf-import.test.ts`** - PDF upload and processing tests
4. **`ai-chat-itinerary.test.ts`** - Chat-based itinerary import tests
5. **`test-logger.ts`** - Logging utility for all tests

## Running Tests

### Run all tests with logging:
```bash
cd packages/hub
pnpm test:ai-import
```

### Run tests in watch mode:
```bash
pnpm test:ai-import:watch
```

### Run with coverage:
```bash
pnpm test:ai-import:coverage
```

## Viewing Test Logs

### View complete test log:
```bash
pnpm test:logs
```

### View only errors:
```bash
pnpm test:logs:errors
```

### View last 50 lines:
```bash
pnpm test:logs -- --tail 50
```

### View test summary:
```bash
pnpm test:summary
```

## Log Files Location

All logs are saved in `packages/hub/logs/` directory:
- `test-run-{timestamp}.log` - Complete test execution log
- `test-errors-{timestamp}.log` - Errors only

## Common Issues and Solutions

### 1. Rate Limiting (429 errors)
**Problem**: Too many requests to API endpoints
**Solution**: Tests now include delays between requests (2 seconds)

### 2. Parse Timeouts
**Problem**: AI parsing takes longer than expected
**Solution**: 
- Increased timeout to 60 attempts
- Added exponential backoff
- Tests accept "in progress" as valid state

### 3. Missing Environment Variables
**Problem**: Tests fail with auth errors
**Solution**: Ensure `.env.local` has:
```
SUPABASE_SERVICE_ROLE_KEY=your-key
OPENROUTER_API_KEY=your-key
UPSTASH_REDIS_URL=your-url
UPSTASH_REDIS_TOKEN=your-token
```

### 4. Module Resolution Errors
**Problem**: Cannot find module errors
**Solution**: Fixed - now imports from correct paths (`../../server`)

## Test Structure

Each test follows this pattern:
```typescript
const logger = setupTestLogging('test-name');
logger.log('Starting test...');

// Make request
const response = await request(server)...
logger.response(response);

// Log important data
logger.debug('Response data', response.body);

// Track duration
logger.duration();
```

## Debugging Failed Tests

1. Run the specific test file:
```bash
pnpm vitest run src/integration/__tests__/ai-import-fixed.test.ts
```

2. Check the logs:
```bash
pnpm test:logs
```

3. Look for specific errors:
```bash
pnpm test:logs:errors
```

4. View summary:
```bash
pnpm test:summary
```

## Test Account

Use these credentials for testing:
- Email: `<EMAIL>`
- Password: `Flaremmk123!`

## Adding New Tests

1. Import the logger:
```typescript
import { TestLogger, setupTestLogging } from './test-logger';
```

2. Create logger for each test:
```typescript
const logger = setupTestLogging('my-test-name');
```

3. Log all important events:
```typescript
logger.log('Test event');
logger.error('Error occurred', error);
logger.response(httpResponse);
logger.debug('Data', object);
logger.duration();
```

## CI/CD Integration

Tests run automatically in CI with:
- Logging enabled
- Environment variables from GitHub secrets
- Coverage reporting to Codecov

For local CI simulation:
```bash
CI=true pnpm test:ai-import
```