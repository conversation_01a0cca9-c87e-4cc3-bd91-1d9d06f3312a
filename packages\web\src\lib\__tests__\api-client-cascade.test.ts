import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { ApiClient } from '@/lib/api-client';
import { useAuthStore } from '@/stores/auth.store';

// Mock the auth store
vi.mock('@/stores/auth.store', () => ({
  useAuthStore: {
    getState: vi.fn(() => ({
      accessToken: 'test-token',
      refreshSession: vi.fn(),
      clearAuth: vi.fn()
    }))
  }
}));

// Mock fetch
global.fetch = vi.fn();

describe('API Client - Authentication Cascade Prevention', () => {
  let apiClient: ApiClient;
  let mockFetch: any;
  let consoleLogSpy: any;

  beforeEach(() => {
    mockFetch = global.fetch as any;
    vi.clearAllMocks();
    
    apiClient = new ApiClient({
      baseURL: 'http://localhost:3001',
      onUnauthorized: vi.fn(),
      onError: vi.fn()
    });

    // Spy on console.log to verify circuit breaker behavior
    consoleLogSpy = vi.spyOn(console, 'log').mockImplementation(() => {});
  });

  afterEach(() => {
    consoleLogSpy.mockRestore();
  });

  it('should prevent infinite retry loops on 401 errors', async () => {
    const authStore = useAuthStore.getState();
    
    // Mock all requests to return 401
    mockFetch.mockResolvedValue({
      ok: false,
      status: 401,
      headers: new Headers({ 'content-type': 'application/json' }),
      json: async () => ({ error: 'Unauthorized' })
    });

    // Mock refresh to also fail
    (authStore.refreshSession as any).mockRejectedValue(new Error('Refresh failed'));

    // First request should trigger refresh attempt
    try {
      await apiClient.get('/api/test');
    } catch (error) {
      expect(error).toBeDefined();
    }

    // Verify only 2 fetch calls were made (1 original + 1 retry)
    expect(mockFetch).toHaveBeenCalledTimes(2);
    
    // Verify refresh was attempted only once
    expect(authStore.refreshSession).toHaveBeenCalledTimes(1);
    
    // Verify auth was cleared
    expect(authStore.clearAuth).toHaveBeenCalledTimes(1);
  });

  it('should open circuit breaker after 3 failures', async () => {
    // Mock all requests to fail
    mockFetch.mockResolvedValue({
      ok: false,
      status: 500,
      headers: new Headers({ 'content-type': 'application/json' }),
      json: async () => ({ error: 'Server error' })
    });

    // Make 3 failed requests
    for (let i = 0; i < 3; i++) {
      try {
        await apiClient.get('/api/test');
      } catch (error) {
        // Expected to fail
      }
    }

    // 4th request should fail immediately due to circuit breaker
    try {
      await apiClient.get('/api/test');
    } catch (error: any) {
      expect(error.message).toBe('Service temporarily unavailable');
      expect(error.status).toBe(503);
    }

    // Verify fetch was only called 3 times (circuit breaker prevented 4th)
    expect(mockFetch).toHaveBeenCalledTimes(3);
  });

  it('should handle 429 rate limiting gracefully', async () => {
    const onError = vi.fn();
    apiClient = new ApiClient({
      baseURL: 'http://localhost:3001',
      onError
    });

    // Mock rate limit response
    mockFetch.mockResolvedValue({
      ok: false,
      status: 429,
      headers: new Headers({ 
        'content-type': 'application/json',
        'Retry-After': '60'
      }),
      json: async () => ({ error: 'Rate limit exceeded' })
    });

    try {
      await apiClient.get('/api/test');
    } catch (error: any) {
      expect(error.message).toContain('Rate limit exceeded');
      expect(error.status).toBe(429);
    }

    // Verify error handler was called
    expect(onError).toHaveBeenCalledWith(
      expect.objectContaining({
        status: 429,
        message: expect.stringContaining('Rate limit exceeded')
      })
    );

    // Verify circuit breaker state was updated
    const status = apiClient.getCircuitBreakerStatus();
    expect(status.failures).toBe(1);
  });

  it('should reset circuit breaker after successful request', async () => {
    // First, trigger some failures
    mockFetch.mockResolvedValue({
      ok: false,
      status: 500,
      headers: new Headers({ 'content-type': 'application/json' }),
      json: async () => ({ error: 'Server error' })
    });

    // Make 2 failed requests
    for (let i = 0; i < 2; i++) {
      try {
        await apiClient.get('/api/test');
      } catch (error) {
        // Expected to fail
      }
    }

    // Verify failures were recorded
    let status = apiClient.getCircuitBreakerStatus();
    expect(status.failures).toBe(2);

    // Now make a successful request
    mockFetch.mockResolvedValue({
      ok: true,
      status: 200,
      headers: new Headers({ 'content-type': 'application/json' }),
      json: async () => ({ success: true, data: { test: 'data' } })
    });

    const result = await apiClient.get('/api/test');
    expect(result).toEqual({ test: 'data' });

    // Verify circuit breaker was reset
    status = apiClient.getCircuitBreakerStatus();
    expect(status.failures).toBe(0);
    expect(status.isOpen).toBe(false);
  });

  it('should prevent concurrent refresh attempts', async () => {
    const authStore = useAuthStore.getState();
    
    // Mock first request to return 401
    mockFetch.mockResolvedValueOnce({
      ok: false,
      status: 401,
      headers: new Headers({ 'content-type': 'application/json' }),
      json: async () => ({ error: 'Unauthorized' })
    });

    // Mock refresh to take some time
    let refreshResolve: any;
    (authStore.refreshSession as any).mockReturnValue(
      new Promise((resolve) => {
        refreshResolve = resolve;
      })
    );

    // Make multiple concurrent requests
    const promise1 = apiClient.get('/api/test1').catch(() => {});
    const promise2 = apiClient.get('/api/test2').catch(() => {});
    const promise3 = apiClient.get('/api/test3').catch(() => {});

    // Wait a bit for all requests to hit the 401
    await new Promise(resolve => setTimeout(resolve, 10));

    // Verify refresh was only called once despite multiple 401s
    expect(authStore.refreshSession).toHaveBeenCalledTimes(1);

    // Resolve the refresh
    refreshResolve();

    // Wait for all promises to complete
    await Promise.all([promise1, promise2, promise3]);
  });

  it('should apply exponential backoff on retries', async () => {
    const authStore = useAuthStore.getState();
    
    // Mock successful refresh
    (authStore.refreshSession as any).mockResolvedValue(true);

    // First call returns 401, second call succeeds
    mockFetch
      .mockResolvedValueOnce({
        ok: false,
        status: 401,
        headers: new Headers({ 'content-type': 'application/json' }),
        json: async () => ({ error: 'Unauthorized' })
      })
      .mockResolvedValueOnce({
        ok: true,
        status: 200,
        headers: new Headers({ 'content-type': 'application/json' }),
        json: async () => ({ success: true, data: { test: 'data' } })
      });

    const startTime = Date.now();
    const result = await apiClient.get('/api/test');
    const endTime = Date.now();

    expect(result).toEqual({ test: 'data' });
    
    // Verify retry was delayed (at least 1000ms for first retry)
    expect(endTime - startTime).toBeGreaterThanOrEqual(1000);
  });
});