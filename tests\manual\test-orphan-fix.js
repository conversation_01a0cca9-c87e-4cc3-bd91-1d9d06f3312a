// Test the orphaned session fix
const axios = require('axios');

async function testOrphanedSessionFix() {
  console.log('🔧 TESTING ORPHANED SESSION FIX');
  console.log('Time:', new Date().toISOString());
  console.log('');

  try {
    // Test the problematic session that should now be detected as orphaned
    console.log('1. Testing problematic session 58df67d3-99d7-4384-8071-6ac4f63607f4...');
    
    const response = await axios.get(
      'http://localhost:3001/api/import/parse-simple/58df67d3-99d7-4384-8071-6ac4f63607f4',
      {
        headers: {
          'Authorization': 'Bearer test-token' // This will fail auth but we want to see if the fix works
        }
      }
    );

    console.log('✅ Response received:', response.data);

  } catch (error) {
    if (error.response?.status === 401) {
      console.log('ℹ️ Got 401 as expected (no auth), but server is responding');
    } else {
      console.log('❌ Error:', error.response?.data || error.message);
    }
  }

  // Test the other orphaned session too
  try {
    console.log('\n2. Testing other orphaned session 68d0d194-c743-4fa5-be43-3d5b832e02ee...');
    
    const response = await axios.get(
      'http://localhost:3001/api/import/parse-simple/68d0d194-c743-4fa5-be43-3d5b832e02ee',
      {
        headers: {
          'Authorization': 'Bearer test-token'
        }
      }
    );

    console.log('✅ Response received:', response.data);

  } catch (error) {
    if (error.response?.status === 401) {
      console.log('ℹ️ Got 401 as expected (no auth), but server is responding');
    } else {
      console.log('❌ Error:', error.response?.data || error.message);
    }
  }

  // Check database to see if sessions were marked as failed
  console.log('\n3. Checking database to verify sessions were marked as failed...');
  
  const { createClient } = require('@supabase/supabase-js');
  
  // Load environment variables
  require('dotenv').config({ path: '../../.env.local' });
  
  const supabaseUrl = process.env.SUPABASE_URL || 'https://your-project.supabase.co';
  const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY || 'your-service-key-here';
  
  const supabase = createClient(supabaseUrl, supabaseServiceKey);

  const { data: sessions, error } = await supabase
    .from('ai_import_logs')
    .select('id, import_status, error_message, created_at, updated_at')
    .in('id', [
      '58df67d3-99d7-4384-8071-6ac4f63607f4',
      '68d0d194-c743-4fa5-be43-3d5b832e02ee'
    ]);

  if (error) {
    console.log('❌ Database error:', error);
  } else {
    console.log('📊 Session Status After Fix:');
    sessions.forEach(session => {
      console.log(`  ${session.id.substring(0, 8)}... | ${session.import_status} | ${session.error_message || 'No error'}`);
      console.log(`    Created: ${session.created_at}`);
      console.log(`    Updated: ${session.updated_at}`);
    });
  }

  console.log('\n🎯 FIX VERIFICATION:');
  console.log('If sessions are now marked as "failed" with timeout error, the fix works!');
  console.log('Frontend should now receive error status instead of infinite polling.');

} catch (error) {
  console.error('❌ Test error:', error.message);
}

// Run the test
testOrphanedSessionFix().catch(console.error);
