{"name": "@travelviz/shared", "version": "1.0.0", "private": true, "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc", "dev": "tsc --watch", "lint": "eslint src --ext .ts,.tsx", "type-check": "tsc --noEmit", "test": "vitest run", "test:watch": "vitest", "test:coverage": "vitest run --coverage"}, "dependencies": {"isomorphic-dompurify": "^2.26.0", "zod": "^4.0.5"}, "devDependencies": {"@faker-js/faker": "^9.9.0"}}