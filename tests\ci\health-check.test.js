#!/usr/bin/env node

/**
 * CI/CD Health Check Test
 * 
 * Simple validation test for CI environments that checks:
 * - Environment variables are present
 * - Services can start successfully
 * - Basic health checks pass
 */

const fs = require('fs');
const path = require('path');
const { spawn } = require('child_process');

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function logHeader(message) {
  log('\n' + '='.repeat(50), 'cyan');
  log(`  ${message}`, 'bright');
  log('='.repeat(50), 'cyan');
}

function logSuccess(message) {
  log(`✅ ${message}`, 'green');
}

function logError(message) {
  log(`❌ ${message}`, 'red');
}

function logWarning(message) {
  log(`⚠️  ${message}`, 'yellow');
}

function logInfo(message) {
  log(`ℹ️  ${message}`, 'blue');
}

// Test results tracking
const testResults = {
  passed: 0,
  failed: 0,
  tests: []
};

function logTest(name, result, details = {}) {
  const passed = result === true || (typeof result === 'object' && result.success);
  testResults.tests.push({ name, passed, details });
  
  if (passed) {
    testResults.passed++;
    logSuccess(name);
  } else {
    testResults.failed++;
    logError(`${name} - ${details.error || 'Failed'}`);
  }
}

async function validateEnvironmentVariables() {
  logHeader('Environment Variable Validation');
  
  // Load environment variables from .env.local files
  function loadEnvFile(filePath) {
    if (fs.existsSync(filePath)) {
      const envContent = fs.readFileSync(filePath, 'utf8');
      const envVars = {};
      
      envContent.split('\n').forEach(line => {
        const trimmed = line.trim();
        if (trimmed && !trimmed.startsWith('#')) {
          const [key, ...valueParts] = trimmed.split('=');
          if (key && valueParts.length > 0) {
            envVars[key.trim()] = valueParts.join('=').trim().replace(/^["']|["']$/g, '');
          }
        }
      });
      
      return envVars;
    }
    return {};
  }

  // Check for .env.local files
  const hubEnvPath = path.join(__dirname, '../../packages/hub/.env.local');
  const webEnvPath = path.join(__dirname, '../../packages/web/.env.local');
  
  const hubEnvExists = fs.existsSync(hubEnvPath);
  const webEnvExists = fs.existsSync(webEnvPath);
  
  logTest('Hub .env.local file exists', hubEnvExists, { 
    path: hubEnvPath,
    error: hubEnvExists ? null : 'File not found'
  });
  
  logTest('Web .env.local file exists', webEnvExists, { 
    path: webEnvPath,
    error: webEnvExists ? null : 'File not found'
  });

  // Load and validate critical environment variables
  const hubEnv = hubEnvExists ? loadEnvFile(hubEnvPath) : {};
  const webEnv = webEnvExists ? loadEnvFile(webEnvPath) : {};
  const allEnv = { ...process.env, ...hubEnv, ...webEnv };

  // Critical environment variables for the application
  const criticalVars = [
    'SUPABASE_URL',
    'SUPABASE_ANON_KEY',
    'JWT_SECRET'
  ];

  // Optional but recommended variables
  const optionalVars = [
    'GOOGLE_PLACES_API_KEY',
    'OPENAI_API_KEY',
    'NEXT_PUBLIC_APP_URL',
    'API_BASE_URL'
  ];

  let criticalMissing = 0;
  let optionalMissing = 0;

  // Check critical variables
  criticalVars.forEach(varName => {
    const exists = !!allEnv[varName];
    logTest(`Critical env var: ${varName}`, exists, {
      error: exists ? null : 'Missing critical environment variable'
    });
    if (!exists) criticalMissing++;
  });

  // Check optional variables
  optionalVars.forEach(varName => {
    const exists = !!allEnv[varName];
    if (exists) {
      logSuccess(`Optional env var: ${varName} - Present`);
    } else {
      logWarning(`Optional env var: ${varName} - Missing (not critical)`);
      optionalMissing++;
    }
  });

  // Summary
  logTest('All critical environment variables present', criticalMissing === 0, {
    missing: criticalMissing,
    total: criticalVars.length,
    error: criticalMissing > 0 ? `${criticalMissing} critical variables missing` : null
  });

  logInfo(`Optional variables: ${optionalVars.length - optionalMissing}/${optionalVars.length} present`);

  return criticalMissing === 0;
}

async function validatePackageStructure() {
  logHeader('Package Structure Validation');

  // Check workspace structure
  const workspaceFiles = [
    'package.json',
    'pnpm-workspace.yaml',
    'packages/hub/package.json',
    'packages/web/package.json',
    'packages/shared/package.json'
  ];

  workspaceFiles.forEach(file => {
    const exists = fs.existsSync(path.join(__dirname, '../..', file));
    logTest(`Workspace file: ${file}`, exists, {
      error: exists ? null : 'Required workspace file missing'
    });
  });

  // Check if shared package is built
  const sharedDistPath = path.join(__dirname, '../../packages/shared/dist');
  const sharedBuilt = fs.existsSync(sharedDistPath);
  
  if (!sharedBuilt) {
    logWarning('Shared package not built - this may cause issues');
    logInfo('Run: pnpm --filter @travelviz/shared build');
  } else {
    logSuccess('Shared package is built');
  }

  return true;
}

async function runCommand(command, args, options = {}) {
  return new Promise((resolve) => {
    const child = spawn(command, args, {
      stdio: 'pipe',
      shell: true,
      timeout: 30000, // 30 second timeout
      ...options
    });

    let stdout = '';
    let stderr = '';

    child.stdout?.on('data', (data) => {
      stdout += data.toString();
    });

    child.stderr?.on('data', (data) => {
      stderr += data.toString();
    });

    child.on('close', (code) => {
      resolve({ stdout, stderr, code, success: code === 0 });
    });

    child.on('error', (error) => {
      resolve({ stdout, stderr, code: -1, success: false, error: error.message });
    });

    // Handle timeout
    setTimeout(() => {
      child.kill();
      resolve({ stdout, stderr, code: -1, success: false, error: 'Command timeout' });
    }, 30000);
  });
}

async function validateServiceStartup() {
  logHeader('Service Startup Validation');

  // Check if we can install dependencies
  logInfo('Checking dependency installation...');
  const installResult = await runCommand('pnpm', ['install', '--frozen-lockfile'], {
    cwd: path.join(__dirname, '../..')
  });

  logTest('Dependencies install successfully', installResult.success, {
    error: installResult.success ? null : `Install failed: ${installResult.stderr || installResult.error}`
  });

  // Check if we can build the shared package
  logInfo('Checking shared package build...');
  const buildResult = await runCommand('pnpm', ['--filter', '@travelviz/shared', 'build'], {
    cwd: path.join(__dirname, '../..')
  });

  logTest('Shared package builds successfully', buildResult.success, {
    error: buildResult.success ? null : `Build failed: ${buildResult.stderr || buildResult.error}`
  });

  // Check if we can run type checking
  logInfo('Checking TypeScript compilation...');
  const typeCheckResult = await runCommand('pnpm', ['--filter', '@travelviz/hub', 'type-check'], {
    cwd: path.join(__dirname, '../..')
  });

  logTest('TypeScript type checking passes', typeCheckResult.success, {
    error: typeCheckResult.success ? null : `Type check failed: ${typeCheckResult.stderr || typeCheckResult.error}`
  });

  // In CI, we don't start the actual servers, just validate they can be prepared
  logInfo('Service startup validation completed (servers not started in CI)');
  
  return true;
}

async function validateTestConfiguration() {
  logHeader('Test Configuration Validation');

  // Check test configuration files
  const testConfigFiles = [
    'tests/test.config.js',
    'tests/api/api.config.js',
    'playwright.config.js'
  ];

  testConfigFiles.forEach(file => {
    const filePath = path.join(__dirname, '../..', file);
    const exists = fs.existsSync(filePath);
    logTest(`Test config: ${file}`, exists, {
      error: exists ? null : 'Test configuration file missing'
    });

    if (exists) {
      try {
        require(filePath);
        logSuccess(`  ${file} - Valid configuration`);
      } catch (error) {
        logError(`  ${file} - Configuration error: ${error.message}`);
      }
    }
  });

  // Check test directories
  const testDirs = [
    'tests/api',
    'tests/e2e',
    'tests/ci'
  ];

  testDirs.forEach(dir => {
    const dirPath = path.join(__dirname, '../..', dir);
    const exists = fs.existsSync(dirPath);
    logTest(`Test directory: ${dir}`, exists, {
      error: exists ? null : 'Test directory missing'
    });
  });

  return true;
}

async function generateCIReport() {
  logHeader('Generating CI Report');

  const report = {
    timestamp: new Date().toISOString(),
    environment: process.env.NODE_ENV || 'development',
    ci: !!process.env.CI,
    nodeVersion: process.version,
    platform: process.platform,
    testResults: {
      total: testResults.tests.length,
      passed: testResults.passed,
      failed: testResults.failed,
      successRate: Math.round((testResults.passed / testResults.tests.length) * 100)
    },
    tests: testResults.tests
  };

  const reportPath = path.join(__dirname, '../../test-results/ci-health-report.json');
  
  // Ensure directory exists
  const reportDir = path.dirname(reportPath);
  if (!fs.existsSync(reportDir)) {
    fs.mkdirSync(reportDir, { recursive: true });
  }

  fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
  logSuccess(`CI report generated: ${reportPath}`);

  return report;
}

async function main() {
  logHeader('TravelViz CI/CD Health Check');
  
  log('This script validates the environment and basic setup for CI/CD', 'bright');
  log('It does NOT start servers or run full tests - just validates setup\n');

  try {
    // Run all validation steps
    await validateEnvironmentVariables();
    await validatePackageStructure();
    await validateServiceStartup();
    await validateTestConfiguration();
    
    // Generate report
    const report = await generateCIReport();
    
    // Summary
    logHeader('Health Check Results');
    
    if (testResults.failed === 0) {
      logSuccess('🎉 All health checks passed!');
      log('\nEnvironment is ready for:', 'bright');
      log('• Running API tests with: pnpm test:api');
      log('• Running E2E tests with: pnpm test:e2e');
      log('• Deploying to production');
      
      process.exit(0);
    } else {
      logError(`❌ ${testResults.failed} health checks failed`);
      log('\nFailed checks:', 'bright');
      testResults.tests
        .filter(t => !t.passed)
        .forEach(t => {
          log(`• ${t.name}: ${t.details.error || 'Unknown error'}`);
        });
      
      log('\nTroubleshooting:', 'bright');
      log('• Check environment variables in .env.local files');
      log('• Run: pnpm install');
      log('• Run: pnpm --filter @travelviz/shared build');
      log('• Verify Supabase configuration');
      
      process.exit(1);
    }
    
  } catch (error) {
    logError(`Fatal error: ${error.message}`);
    console.error(error.stack);
    process.exit(1);
  }
}

// Handle process termination
process.on('SIGINT', () => {
  log('\nHealth check interrupted by user', 'yellow');
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  logError(`Unhandled Rejection: ${reason}`);
  process.exit(1);
});

// Run the health check
main();