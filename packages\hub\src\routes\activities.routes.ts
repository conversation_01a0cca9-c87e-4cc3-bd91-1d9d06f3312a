import { Router } from 'express';
import { TripsController } from '../controllers/trips.controller';
import { ActivitiesController } from '../controllers/activities.controller';
import { authenticateSupabaseUser } from '../middleware/supabase-auth.middleware';
import { validateRequest } from '../middleware/validation.middleware';
import { apiRateLimit } from '../middleware/rate-limit.middleware';
import { verifyActivityOwnership } from '../middleware/ownership.middleware';
import {
  updateActivitySchema,
  activityIdOnlyParamSchema,
} from '../schemas/trip.schema';

const router: Router = Router();
const tripsController = new TripsController();
const activitiesController = new ActivitiesController();

// Apply rate limiting to all routes
router.use(apiRateLimit);

// All activity routes require authentication
router.use(authenticateSupabaseUser);

// Add activity from Google Place
router.post('/add-from-place',
  activitiesController.addFromPlace.bind(activitiesController)
);

// Activity operations by ID only (REST compliant)
router.put('/:id',
  validateRequest(activityIdOnlyParamSchema, 'params'),
  validateRequest(updateActivitySchema),
  verifyActivityOwnership,
  tripsController.updateActivity.bind(tripsController)
);

router.delete('/:id',
  validateRequest(activityIdOnlyParamSchema, 'params'),
  verifyActivityOwnership,
  tripsController.deleteActivity.bind(tripsController)
);

export { router as activitiesRoutes };
export default router;