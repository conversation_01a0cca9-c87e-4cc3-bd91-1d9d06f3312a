import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { tripsAPI, activitiesAPI } from '@/lib/api';
import { queryKeys } from '@/providers/QueryProvider';
import { Trip, Activity, ActivityType } from '@travelviz/shared';
import { toast } from 'sonner';

// Trip create/update types
interface TripCreateInput {
  title: string;
  description?: string;
  destination?: string;
  start_date?: string;
  end_date?: string;
  budget_amount?: number;
  budget_currency?: string;
  tags?: string[];
  visibility?: 'private' | 'unlisted' | 'public';
}

interface TripUpdateInput extends Partial<TripCreateInput> {
  status?: 'draft' | 'planning' | 'confirmed' | 'in_progress' | 'completed' | 'cancelled';
}

interface ActivityCreateInput {
  title: string;
  description?: string;
  type?: ActivityType;
  start_time?: string;
  end_time?: string;
  location?: string;
  location_lat?: number;
  location_lng?: number;
  price?: number;
  currency?: string;
  booking_reference?: string;
  booking_url?: string;
  notes?: string;
  attachments?: string[];
}

interface ActivityUpdateInput extends Partial<ActivityCreateInput> {
  position?: number;
}

// Hook for fetching all trips with caching
export function useTrips() {
  return useQuery({
    queryKey: queryKeys.trips.lists(),
    queryFn: async () => {
      const response = await tripsAPI.list();
      if (!response.success) {
        throw new Error(response.error || 'Failed to fetch trips');
      }
      return response.data || [];
    },
    staleTime: 2 * 60 * 1000, // 2 minutes - trips don't change frequently
    gcTime: 10 * 60 * 1000, // 10 minutes
  });
}

// Hook for fetching a single trip with activities
export function useTrip(tripId: string) {
  return useQuery({
    queryKey: queryKeys.trips.detail(tripId),
    queryFn: async () => {
      const response = await tripsAPI.get(tripId);
      if (!response.success) {
        throw new Error(response.error || 'Failed to fetch trip');
      }
      return response.data;
    },
    enabled: !!tripId,
    staleTime: 5 * 60 * 1000, // 5 minutes - trip details change less frequently
    gcTime: 15 * 60 * 1000, // 15 minutes
  });
}

// Hook for creating a new trip
export function useCreateTrip() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (tripData: TripCreateInput) => {
      const response = await tripsAPI.create(tripData);
      if (!response.success) {
        throw new Error(response.error || 'Failed to create trip');
      }
      return response.data;
    },
    onSuccess: (newTrip) => {
      // Invalidate trips list to refetch
      queryClient.invalidateQueries({ queryKey: queryKeys.trips.lists() });
      
      // Optimistically add the new trip to cache
      queryClient.setQueryData(queryKeys.trips.lists(), (old: Trip[] | undefined) => {
        return old ? [newTrip, ...old] : [newTrip];
      });
      
      toast.success('Trip created successfully!');
    },
    onError: (error: Error) => {
      toast.error(error.message || 'Failed to create trip');
    },
  });
}

// Hook for updating a trip
export function useUpdateTrip() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ id, ...tripData }: TripUpdateInput & { id: string }) => {
      const response = await tripsAPI.update(id, tripData);
      if (!response.success) {
        throw new Error(response.error || 'Failed to update trip');
      }
      return response.data;
    },
    onSuccess: (updatedTrip) => {
      // Update the specific trip in cache
      queryClient.setQueryData(queryKeys.trips.detail(updatedTrip!.id), updatedTrip);
      
      // Update the trip in the list cache
      queryClient.setQueryData(queryKeys.trips.lists(), (old: Trip[] | undefined) => {
        return old?.map(trip => trip.id === updatedTrip!.id ? updatedTrip! : trip);
      });
      
      toast.success('Trip updated successfully!');
    },
    onError: (error: Error) => {
      toast.error(error.message || 'Failed to update trip');
    },
  });
}

// Hook for deleting a trip
export function useDeleteTrip() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (tripId: string) => {
      const response = await tripsAPI.delete(tripId);
      if (!response.success) {
        throw new Error(response.error || 'Failed to delete trip');
      }
      return tripId;
    },
    onSuccess: (deletedTripId) => {
      // Remove trip from list cache
      queryClient.setQueryData(queryKeys.trips.lists(), (old: Trip[] | undefined) => {
        return old?.filter(trip => trip.id !== deletedTripId);
      });
      
      // Remove trip detail from cache
      queryClient.removeQueries({ queryKey: queryKeys.trips.detail(deletedTripId) });
      
      toast.success('Trip deleted successfully!');
    },
    onError: (error: Error) => {
      toast.error(error.message || 'Failed to delete trip');
    },
  });
}

// Hook for adding an activity to a trip
export function useAddActivity() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ tripId, ...activityData }: ActivityCreateInput & { tripId: string }) => {
      const response = await tripsAPI.addActivity(tripId, activityData);
      if (!response.success) {
        throw new Error(response.error || 'Failed to add activity');
      }
      return { activity: response.data, tripId };
    },
    onSuccess: ({ activity, tripId }) => {
      // Invalidate trip details to refetch with new activity
      queryClient.invalidateQueries({ queryKey: queryKeys.trips.detail(tripId) });
      
      // Optimistically update trip cache if it exists
      queryClient.setQueryData(queryKeys.trips.detail(tripId), (old: Trip | undefined) => {
        if (old && old.activities) {
          return {
            ...old,
            activities: [...old.activities, activity!]
          };
        }
        return old;
      });
      
      toast.success('Activity added successfully!');
    },
    onError: (error: Error) => {
      toast.error(error.message || 'Failed to add activity');
    },
  });
}

// Hook for updating an activity
export function useUpdateActivity() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ id, tripId, ...activityData }: ActivityUpdateInput & { id: string; tripId: string }) => {
      const response = await activitiesAPI.update(id, activityData);
      if (!response.success) {
        throw new Error(response.error || 'Failed to update activity');
      }
      return { activity: response.data, tripId };
    },
    onSuccess: ({ activity, tripId }) => {
      // Update activity in trip cache
      queryClient.setQueryData(queryKeys.trips.detail(tripId), (old: Trip | undefined) => {
        if (old && old.activities) {
          return {
            ...old,
            activities: old.activities.map(a => a.id === activity!.id ? activity! : a)
          };
        }
        return old;
      });
      
      toast.success('Activity updated successfully!');
    },
    onError: (error: Error) => {
      toast.error(error.message || 'Failed to update activity');
    },
  });
}

// Hook for deleting an activity
export function useDeleteActivity() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ id, tripId }: { id: string; tripId: string }) => {
      const response = await activitiesAPI.delete(id);
      if (!response.success) {
        throw new Error(response.error || 'Failed to delete activity');
      }
      return { activityId: id, tripId };
    },
    onSuccess: ({ activityId, tripId }) => {
      // Remove activity from trip cache
      queryClient.setQueryData(queryKeys.trips.detail(tripId), (old: Trip | undefined) => {
        if (old && old.activities) {
          return {
            ...old,
            activities: old.activities.filter(a => a.id !== activityId)
          };
        }
        return old;
      });
      
      toast.success('Activity deleted successfully!');
    },
    onError: (error: Error) => {
      toast.error(error.message || 'Failed to delete activity');
    },
  });
}

// Helper hook for prefetching trip data
export function usePrefetchTrip() {
  const queryClient = useQueryClient();

  return (tripId: string) => {
    queryClient.prefetchQuery({
      queryKey: queryKeys.trips.detail(tripId),
      queryFn: async () => {
        const response = await tripsAPI.get(tripId);
        if (!response.success) {
          throw new Error(response.error || 'Failed to fetch trip');
        }
        return response.data;
      },
      staleTime: 5 * 60 * 1000, // 5 minutes
    });
  };
}

// Hook for optimistic updates
export function useOptimisticTrips() {
  const queryClient = useQueryClient();

  const optimisticCreateTrip = (tripData: TripCreateInput) => {
    const tempId = `temp-${Date.now()}`;
    const optimisticTrip: Trip = {
      id: tempId,
      user_id: 'current-user', // This should come from auth context
      title: tripData.title,
      description: tripData.description || '',
      destination: tripData.destination || '',
      start_date: tripData.start_date || '',
      end_date: tripData.end_date || '',
      budget_amount: tripData.budget_amount || 0,
      budget_currency: tripData.budget_currency || 'USD',
      tags: tripData.tags || [],
      visibility: tripData.visibility || 'private',
      status: 'draft',
      cover_image: '',
      metadata: {},
      views: 0,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      deleted_at: null,
      activities: [],
    };

    // Add optimistically to cache
    queryClient.setQueryData(queryKeys.trips.lists(), (old: Trip[] | undefined) => {
      return old ? [optimisticTrip, ...old] : [optimisticTrip];
    });

    return tempId;
  };

  const rollbackOptimisticCreate = (tempId: string) => {
    queryClient.setQueryData(queryKeys.trips.lists(), (old: Trip[] | undefined) => {
      return old?.filter(trip => trip.id !== tempId);
    });
  };

  return {
    optimisticCreateTrip,
    rollbackOptimisticCreate,
  };
}