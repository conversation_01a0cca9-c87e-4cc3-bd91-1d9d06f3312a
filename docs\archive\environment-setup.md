# TravelViz Environment Setup Guide

This guide will walk you through setting up all the necessary accounts and environment variables for TravelViz.

## Overview

TravelViz requires several third-party services to function properly:

- **Supabase**: Database and authentication
- **Mapbox**: Map visualization
- **OpenRouter**: AI model access (supports multiple providers)
- **Render**: Deployment platform for the Hub API
- **Vercel**: Deployment platform for the Next.js web app

## Step 1: Supabase Setup

### 1.1 Create Supabase Account

1. Go to [https://supabase.com](https://supabase.com)
2. Click "Start your project"
3. Sign up with GitHub or email
4. Create a new organization (e.g., "travelviz")

### 1.2 Create New Project

1. Click "New project"
2. Fill in project details:
   - **Name**: `travelviz-prod` (or `travelviz-dev` for development)
   - **Database Password**: Generate a strong password and save it
   - **Region**: Choose closest to your users
   - **Pricing Plan**: Free tier is fine to start
3. Click "Create new project" and wait for setup (~2 minutes)

### 1.3 Get API Keys

1. Once project is ready, go to Settings → API
2. Copy these values:
   - **Project URL**: This is your `NEXT_PUBLIC_SUPABASE_URL`
   - **anon/public key**: This is your `NEXT_PUBLIC_SUPABASE_ANON_KEY`
   - **service_role key**: This is your `SUPABASE_SERVICE_ROLE_KEY` (keep secret!)

### 1.4 Set Up Database Tables

1. Go to SQL Editor
2. Create a new query and run this SQL:

```sql
-- Create waitlist table
CREATE TABLE waitlist (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  email VARCHAR(255) UNIQUE NOT NULL,
  name VARCHAR(255),
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  source VARCHAR(50) DEFAULT 'web',
  referral_code VARCHAR(50),
  metadata JSONB DEFAULT '{}',
  status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'invited', 'converted'))
);

-- Create index for email lookups
CREATE INDEX idx_waitlist_email ON waitlist(email);
CREATE INDEX idx_waitlist_status ON waitlist(status);
CREATE INDEX idx_waitlist_created_at ON waitlist(created_at DESC);

-- Enable Row Level Security
ALTER TABLE waitlist ENABLE ROW LEVEL SECURITY;

-- Create policy for public access (optional, for waitlist)
CREATE POLICY "Allow public to insert into waitlist" ON waitlist
  FOR INSERT TO anon WITH CHECK (true);

-- Create updated_at trigger
CREATE OR REPLACE FUNCTION update_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_waitlist_updated_at
  BEFORE UPDATE ON waitlist
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at();
```

## Step 2: Mapbox Setup

### 2.1 Create Mapbox Account

1. Go to [https://mapbox.com](https://mapbox.com)
2. Click "Sign up" and create account
3. Verify your email

### 2.2 Get Access Token

1. Go to Account → Tokens
2. Copy your default public token
3. This is your `NEXT_PUBLIC_MAPBOX_TOKEN`

### 2.3 Create Secret Token for Backend

1. Click "Create a token"
2. Give it a name: `travelviz-hub-secret`
3. Select scopes:
   - ✅ `styles:read`
   - ✅ `geocoding:read` (for address lookup)
   - ✅ `directions:read` (for route calculations)
   - ✅ `matrix:read` (for distance calculations)
4. Keep this token secret - it's for server-side use only
5. This is your `MAPBOX_ACCESS_TOKEN` for the Hub

### 2.4 Configure Public Token (Optional)

1. For the default public token used in web
2. Add URL restrictions for production (e.g., `https://yourdomain.com`)
3. This token is already public, used for map display only

## Step 3: OpenRouter Setup

### 3.1 Create OpenRouter Account

1. Go to [https://openrouter.ai](https://openrouter.ai)
2. Click "Sign up" and create account with Google or email
3. Verify your email

### 3.2 Get API Key

1. Go to [https://openrouter.ai/keys](https://openrouter.ai/keys)
2. Click "Create Key"
3. Name it: `travelviz-prod`
4. Copy the key immediately
5. This is your `OPENROUTER_API_KEY`

### 3.3 Add Credits

1. Go to [https://openrouter.ai/credits](https://openrouter.ai/credits)
2. Add credits ($5-10 for development)
3. OpenRouter supports multiple AI providers:
   - OpenAI (GPT-4, GPT-3.5)
   - Anthropic (Claude)
   - Google (Gemini)
   - Meta (Llama)
   - And many more!

### 3.4 Benefits of OpenRouter

- Single API for multiple AI providers
- Automatic fallbacks if one provider fails
- Often cheaper than direct provider access
- No need for multiple API keys

## Step 4: Environment Variables Setup

### 4.1 Create `.env.local` file

In your project root (`packages/web/`), create `.env.local`:

```bash
# Supabase
NEXT_PUBLIC_SUPABASE_URL=https://your-project-id.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-anon-key-here
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key-here

# Mapbox
NEXT_PUBLIC_MAPBOX_TOKEN=pk.eyXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX

# OpenRouter (for AI features)
OPENROUTER_API_KEY=sk-or-v1-XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX

# App Settings
NEXT_PUBLIC_APP_URL=http://localhost:3000
NEXT_PUBLIC_HUB_URL=http://localhost:3001
```

### 4.2 Create `.env.local` for Hub

In `packages/hub/`, create `.env.local`:

```bash
# Port
PORT=3001

# Supabase (for direct database access)
SUPABASE_URL=https://your-project-id.supabase.co
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key-here

# OpenRouter (hub handles AI requests)
OPENROUTER_API_KEY=sk-or-v1-XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX

# Mapbox (for geocoding and distance calculations)
MAPBOX_ACCESS_TOKEN=sk.eyXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX

# Security
JWT_SECRET=your-random-jwt-secret-minimum-32-characters
```

## Step 5: Render Deployment Setup (Hub API)

### 5.1 Create Render Account

1. Go to [https://render.com](https://render.com)
2. Sign up with GitHub (recommended) or email
3. Verify your email

### 5.2 Deploy Hub API

1. Click "New +" → "Web Service"
2. Connect your GitHub repository
3. Configure service:
   - **Name**: `travelviz-hub`
   - **Region**: Choose closest to your users
   - **Branch**: `main` or `dev`
   - **Root Directory**: `packages/hub`
   - **Runtime**: Node
   - **Build Command**: `npm install -g pnpm && pnpm install && pnpm --filter @travelviz/hub build`
   - **Start Command**: `pnpm start`
   - **Instance Type**: Free (for development)

### 5.3 Add Environment Variables

1. Go to Environment tab
2. Add all hub environment variables:
   - `PORT=3001`
   - `SUPABASE_URL`
   - `SUPABASE_SERVICE_ROLE_KEY`
   - `OPENROUTER_API_KEY`
   - `JWT_SECRET`
   - `NODE_ENV=production`
   - `MAPBOX_ACCESS_TOKEN` (for geocoding/distance calculations)

### 5.4 Get Deployment URL

After deployment, you'll get a URL like:

- `https://travelviz-hub.onrender.com`

Save this URL for the web app configuration.

## Step 6: Vercel Deployment Setup (Web App)

### 6.1 Connect Repository

1. Go to [https://vercel.com](https://vercel.com)
2. Sign up/in with GitHub
3. Click "New Project"
4. Import your GitHub repository
5. Select the `dev` or `main` branch

### 6.2 Configure Build Settings

1. **Framework Preset**: Next.js
2. **Root Directory**: `packages/web`
3. **Build Command**: Leave default or use custom from vercel.json
4. **Output Directory**: Leave default

### 6.3 Add Environment Variables

1. Go to Settings → Environment Variables
2. Add all variables from `.env.local`
3. **Important**: Update the Hub URL to your Render deployment:
   - `NEXT_PUBLIC_HUB_URL=https://travelviz-hub.onrender.com`
4. Select appropriate environments:
   - ✅ Production
   - ✅ Preview
   - ✅ Development

## Step 6: Additional Services (Optional)

### 6.1 Resend (Email Service)

1. Sign up at [https://resend.com](https://resend.com)
2. Get API key from dashboard
3. Add `RESEND_API_KEY` to environment

### 6.2 Google Analytics

1. Create property at [https://analytics.google.com](https://analytics.google.com)
2. Get Measurement ID (G-XXXXXXXXXX)
3. Add `NEXT_PUBLIC_GA_ID` to environment

### 6.3 Sentry (Error Tracking)

1. Sign up at [https://sentry.io](https://sentry.io)
2. Create new project → Next.js
3. Get DSN from project settings
4. Add `NEXT_PUBLIC_SENTRY_DSN` to environment

## Verification Checklist

After setup, verify everything works:

- [ ] Run `pnpm dev` - both web and hub start without errors
- [ ] Visit http://localhost:3000 - page loads
- [ ] Test waitlist signup - email saves to Supabase
- [ ] Check map components - Mapbox tiles load
- [ ] Test AI features - OpenAI responds (if implemented)

## Security Best Practices

1. **Never commit `.env.local` files** - they're in `.gitignore`
2. **Use different API keys** for development and production
3. **Rotate keys regularly** - every 3-6 months
4. **Set spending limits** on paid services
5. **Use environment-specific keys** in Vercel

## Troubleshooting

### Supabase Connection Issues

- Check if project is paused (free tier pauses after 1 week inactive)
- Verify API keys are correct (no extra spaces)
- Check Row Level Security policies

### Mapbox Not Loading

- Verify token has correct scopes
- Check browser console for CORS errors
- Ensure token isn't restricted to specific URLs

### OpenRouter Issues

- Check credits balance at https://openrouter.ai/credits
- Verify API key starts with `sk-or-v1-`
- Check model availability at https://openrouter.ai/models
- Monitor usage at https://openrouter.ai/activity

### Render Deployment Issues

- Free tier spins down after 15 minutes of inactivity
- First request after spin-down takes ~30 seconds
- Check logs in Render dashboard
- Verify all environment variables are set
- Ensure build command includes `pnpm install`

### Vercel Build Failures

- Ensure all `NEXT_PUBLIC_*` variables are set in Vercel
- Check build logs for specific missing variables
- Verify Root Directory is set to `packages/web`
- Update `NEXT_PUBLIC_HUB_URL` to your Render URL

### Hub-Web Communication Issues

- Verify CORS is configured in hub (`packages/hub/src/index.ts`)
- Check that `NEXT_PUBLIC_HUB_URL` matches your Render deployment
- Ensure both services are running (locally or deployed)
- Check network tab in browser for failed requests

## Support Resources

- **Supabase Discord**: [https://discord.supabase.com](https://discord.supabase.com)
- **Mapbox Documentation**: [https://docs.mapbox.com](https://docs.mapbox.com)
- **OpenRouter Discord**: [https://discord.gg/openrouter](https://discord.gg/openrouter)
- **OpenRouter Docs**: [https://openrouter.ai/docs](https://openrouter.ai/docs)
- **Render Community**: [https://community.render.com](https://community.render.com)
- **Render Docs**: [https://render.com/docs](https://render.com/docs)
- **Vercel Support**: [https://vercel.com/support](https://vercel.com/support)
