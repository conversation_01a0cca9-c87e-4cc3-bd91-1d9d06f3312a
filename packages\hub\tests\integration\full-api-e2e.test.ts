import { describe, it, expect, beforeAll, afterAll, beforeEach } from 'vitest';
import request from 'supertest';
import { getSupabaseClient } from '../../src/lib/supabase';
import { createServer } from '../../src/server';
import { Express } from 'express';
import path from 'path';
import fs from 'fs';

// Load environment variables from .env.local
import dotenv from 'dotenv';
dotenv.config({ path: path.join(__dirname, '../../../.env.local') });

// Test configuration
const TEST_USER_EMAIL = '<EMAIL>';
const TEST_USER_PASSWORD = 'Flaremmk123!';
const API_TIMEOUT = 60000;

// Skip these tests in CI if needed
const isCI = process.env.CI === 'true';
const describeIntegration = isCI ? describe.skip : describe;

// Test data
const TEST_CONVERSATIONS = {
  chatgpt: `User: I'm planning a 7-day trip to Japan in April 2025. I want to visit Tokyo, Kyoto, and Osaka. My budget is around $3000 including flights from New York.

Assistant: I'd be happy to help you plan your 7-day Japan trip! Here's a suggested itinerary:

Day 1-3: Tokyo
- Day 1: Arrive at Narita/Haneda, check into hotel in Shinjuku or Shibuya. Visit Senso-ji Temple and explore Asakusa.
- Day 2: Tsukiji Outer Market for breakfast, Imperial Palace, Ginza shopping, Tokyo Tower
- Day 3: Meiji Shrine, Harajuku, Shibuya Crossing, teamLab Borderless

Day 4-5: Kyoto
- Day 4: Take shinkansen to Kyoto (2.5 hours). Visit Fushimi Inari Shrine, Kiyomizu-dera Temple
- Day 5: Arashiyama Bamboo Grove, Golden Pavilion, Gion district for geisha spotting

Day 6-7: Osaka
- Day 6: Travel to Osaka (30 mins from Kyoto). Osaka Castle, Dotonbori district for street food
- Day 7: Day trip to Nara (1 hour) to see deer park and Todai-ji Temple. Return to Osaka for flight

Budget Breakdown:
- Flights (NYC-Tokyo): $1000-1200
- Hotels (6 nights): $600-800
- JR Pass (7 days): $280
- Food: $350-450
- Activities/Entrance fees: $200
- Local transport: $100
Total: ~$2700-3000`,

  claude: `Human: I need a 5-day itinerary for London and Edinburgh with a focus on history and culture. Budget is £2000.

Assistant: I'll create a historical and cultural itinerary for London and Edinburgh within your £2000 budget.

**Day 1-3: London**

Day 1: Central London History
- Morning: Tower of London (£29.90) - See Crown Jewels and medieval history
- Afternoon: Walk along Thames to Shakespeare's Globe Theatre
- Evening: West End show (£50-80)

Day 2: Museums & Royalty
- Morning: British Museum (free) - World history and artifacts
- Afternoon: Buckingham Palace State Rooms (£30) or Westminster Abbey (£27)
- Evening: Pub dinner in Covent Garden

Day 3: Art & Culture
- Morning: National Gallery (free) and Trafalgar Square
- Afternoon: Tate Modern (free) and walk across Millennium Bridge
- Evening: Travel to Edinburgh by train (£50-120)

**Day 4-5: Edinburgh**

Day 4: Old Town Edinburgh
- Morning: Edinburgh Castle (£19.50) - Scottish crown jewels and history
- Afternoon: Royal Mile walk and St Giles' Cathedral
- Evening: Ghost tour (£15)

Day 5: Culture & Departure
- Morning: National Museum of Scotland (free)
- Afternoon: Palace of Holyroodhouse (£19) or Arthur's Seat hike
- Evening: Traditional Scottish dinner

**Budget Summary:**
- Transport: £150-250
- Accommodation (4 nights): £400-600
- Attractions: £200-250
- Food: £400-500
- Activities/Shopping: £300-400
Total: £1450-2000`,
  
  gemini: `User: Plan a 4-day Barcelona and Madrid trip focusing on architecture and food. €1500 budget.

Gemini: Here's your 4-day Spanish adventure focusing on architecture and gastronomy:

🏛️ **Barcelona (Day 1-2)**

*Day 1: Gaudí & Gothic Quarter*
- 9 AM: Sagrada Família (book ahead: €26)
- 11 AM: Walk through Eixample to see Casa Batlló and La Pedrera from outside
- 1 PM: Lunch at Mercat de la Boqueria
- 3 PM: Explore Gothic Quarter and Barcelona Cathedral
- 8 PM: Tapas crawl in El Born

*Day 2: Park Güell & Beach*
- 9 AM: Park Güell (€10)
- 11 AM: Gràcia neighborhood exploration
- 1 PM: Paella at Barceloneta Beach
- 4 PM: Cable car to Montjuïc
- 8 PM: Dinner at Can Culleretes (oldest restaurant)

✈️ **Travel to Madrid** (Day 2 evening)
- 9 PM: High-speed train to Madrid (€35-60)

🏛️ **Madrid (Day 3-4)**

*Day 3: Royal Madrid*
- 10 AM: Royal Palace (€12)
- 12 PM: Almudena Cathedral
- 2 PM: Lunch at Mercado San Miguel
- 4 PM: Prado Museum (€15)
- 8 PM: Tapas in La Latina

*Day 4: Modern Madrid*
- 10 AM: Retiro Park and Crystal Palace
- 12 PM: Reina Sofía Museum (€12)
- 2 PM: Cocido madrileño lunch
- 4 PM: Gran Vía shopping
- 8 PM: Farewell dinner in Malasaña

💰 **Budget Breakdown:**
- Transport: €150
- Hotels (3 nights): €300-450
- Attractions: €100
- Food: €400-500
- Reserve: €400
Total: €1350-1500`
};

// Helper function to create test PDF
function createTestPDF(): Buffer {
  const pdfContent = `%PDF-1.4
1 0 obj
<< /Type /Catalog /Pages 2 0 R >>
endobj
2 0 obj
<< /Type /Pages /Kids [3 0 R] /Count 1 >>
endobj
3 0 obj
<< /Type /Page /Parent 2 0 R /Resources << /Font << /F1 << /Type /Font /Subtype /Type1 /BaseFont /Arial >> >> >> /MediaBox [0 0 612 792] /Contents 4 0 R >>
endobj
4 0 obj
<< /Length 280 >>
stream
BT
/F1 12 Tf
50 750 Td
(Paris Itinerary - 3 Days) Tj
0 -20 Td
(Day 1: Eiffel Tower, Louvre Museum, Latin Quarter) Tj
0 -20 Td  
(Day 2: Versailles Palace, Champs-Elysees, Arc de Triomphe) Tj
0 -20 Td
(Day 3: Montmartre, Sacre-Coeur, Seine River Cruise) Tj
0 -20 Td
(Budget: $1500 per person) Tj
ET
endstream
endobj
xref
0 5
0000000000 65535 f 
0000000009 00000 n 
0000000058 00000 n 
0000000115 00000 n 
0000000229 00000 n 
trailer
<< /Size 5 /Root 1 0 R >>
startxref
560
%%EOF`;

  return Buffer.from(pdfContent);
}

// Helper class for managing auth and making authenticated requests
class AuthenticatedTest {
  private static token: string | null = null;
  private static tokenExpiry: number = 0;
  private static supabase = getSupabaseClient();

  static async getToken(): Promise<string> {
    // Return cached token if still valid
    if (this.token && Date.now() < this.tokenExpiry) {
      return this.token;
    }

    // Get new token
    const { data, error } = await this.supabase.auth.signInWithPassword({
      email: TEST_USER_EMAIL,
      password: TEST_USER_PASSWORD
    });

    if (error || !data.session) {
      throw new Error(`Authentication failed: ${error?.message || 'No session returned'}`);
    }

    this.token = data.session.access_token;
    this.tokenExpiry = Date.now() + (data.session.expires_in! * 1000) - 60000; // Refresh 1 min early
    
    return this.token;
  }

  static async makeRequest(app: Express, method: string, url: string) {
    const token = await this.getToken();
    const req = request(app)[method.toLowerCase()](url);
    req.set('Authorization', `Bearer ${token}`);
    return req;
  }

  static async makeAuthenticatedRequest(app: Express) {
    const token = await this.getToken();
    return {
      get: (url: string) => request(app).get(url).set('Authorization', `Bearer ${token}`),
      post: (url: string) => request(app).post(url).set('Authorization', `Bearer ${token}`),
      put: (url: string) => request(app).put(url).set('Authorization', `Bearer ${token}`),
      delete: (url: string) => request(app).delete(url).set('Authorization', `Bearer ${token}`)
    };
  }

  static async logout() {
    await this.supabase.auth.signOut();
    this.token = null;
    this.tokenExpiry = 0;
  }
}

describeIntegration('Complete API E2E Tests', () => {
  let app: Express;
  let server: any;

  beforeAll(async () => {
    console.log('🚀 Starting API E2E tests...');
    app = createServer();
    server = app.listen(0); // Random port
  }, API_TIMEOUT);

  afterAll(async () => {
    await AuthenticatedTest.logout();
    server?.close();
  });

  describe('Authentication API', () => {
    it('should successfully authenticate with valid credentials', async () => {
      const response = await request(app)
        .post('/api/auth/login')
        .send({
          email: TEST_USER_EMAIL,
          password: TEST_USER_PASSWORD
        });

      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('success', true);
      expect(response.body.data).toHaveProperty('token');
      expect(response.body.data).toHaveProperty('user');
      expect(response.body.data.user.email).toBe(TEST_USER_EMAIL);
    });

    it('should reject invalid credentials', async () => {
      const response = await request(app)
        .post('/api/auth/login')
        .send({
          email: TEST_USER_EMAIL,
          password: 'wrongpassword'
        });

      expect(response.status).toBe(401);
      expect(response.body.success).toBe(false);
      expect(response.body.error).toContain('Invalid');
    });

    it('should validate email format', async () => {
      const response = await request(app)
        .post('/api/auth/login')
        .send({
          email: 'notanemail',
          password: TEST_USER_PASSWORD
        });

      expect(response.status).toBe(400);
      expect(response.body.success).toBe(false);
    });

    it('should require both email and password', async () => {
      const response = await request(app)
        .post('/api/auth/login')
        .send({
          email: TEST_USER_EMAIL
        });

      expect(response.status).toBe(400);
      expect(response.body.success).toBe(false);
    });
  });

  describe('AI Conversation Import API', () => {
    it('should import ChatGPT conversation successfully', async () => {
      const response = await AuthenticatedTest.makeRequest(app, 'POST', '/api/import/conversation')
        .send({
          content: TEST_CONVERSATIONS.chatgpt,
          source: 'chatgpt'
        });

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveProperty('tripId');
      expect(response.body.data).toHaveProperty('tripData');
      expect(response.body.data.tripData).toHaveProperty('title');
      expect(response.body.data.tripData.title).toContain('Japan');
      expect(response.body.data.tripData.days).toHaveLength(7);
    }, API_TIMEOUT);

    it('should import Claude conversation successfully', async () => {
      const response = await AuthenticatedTest.makeRequest(app, 'POST', '/api/import/conversation')
        .send({
          content: TEST_CONVERSATIONS.claude,
          source: 'claude'
        });

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveProperty('tripId');
      expect(response.body.data.tripData.title).toMatch(/London|Edinburgh/i);
      expect(response.body.data.tripData.days).toHaveLength(5);
    }, API_TIMEOUT);

    it('should import Gemini conversation successfully', async () => {
      const response = await AuthenticatedTest.makeRequest(app, 'POST', '/api/import/conversation')
        .send({
          content: TEST_CONVERSATIONS.gemini,
          source: 'gemini'
        });

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveProperty('tripId');
      expect(response.body.data.tripData.title).toMatch(/Barcelona|Madrid/i);
      expect(response.body.data.tripData.days).toHaveLength(4);
    }, API_TIMEOUT);

    it('should reject empty conversation', async () => {
      const response = await AuthenticatedTest.makeRequest(app, 'POST', '/api/import/conversation')
        .send({
          content: '',
          source: 'chatgpt'
        });

      expect(response.status).toBe(400);
      expect(response.body.success).toBe(false);
      expect(response.body.error).toContain('empty');
    });

    it('should reject invalid source platform', async () => {
      const response = await AuthenticatedTest.makeRequest(app, 'POST', '/api/import/conversation')
        .send({
          content: 'Some content',
          source: 'invalid-platform'
        });

      expect(response.status).toBe(400);
      expect(response.body.success).toBe(false);
    });

    it('should require authentication', async () => {
      const response = await request(app)
        .post('/api/import/conversation')
        .send({
          content: TEST_CONVERSATIONS.chatgpt,
          source: 'chatgpt'
        });

      expect(response.status).toBe(401);
      expect(response.body.success).toBe(false);
    });
  });

  describe('PDF Import API', () => {
    it('should upload PDF successfully', async () => {
      const pdfBuffer = createTestPDF();
      
      const response = await (await AuthenticatedTest.makeRequest(app))
        .post('/api/v1/import/upload')
        .attach('file', pdfBuffer, 'test-itinerary.pdf')
        .field('source', 'pdf');

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveProperty('importId');
      expect(response.body.data).toHaveProperty('message');
    });

    it('should reject non-PDF files', async () => {
      const textBuffer = Buffer.from('This is not a PDF');
      
      const response = await (await AuthenticatedTest.makeRequest(app))
        .post('/api/v1/import/upload')
        .attach('file', textBuffer, 'test.txt')
        .field('source', 'pdf');

      expect(response.status).toBe(400);
      expect(response.body.success).toBe(false);
      expect(response.body.error).toContain('PDF');
    });

    it('should enforce file size limits', async () => {
      // Create 11MB buffer (exceeds 10MB limit)
      const largeBuffer = Buffer.alloc(11 * 1024 * 1024, 'a');
      
      const response = await (await AuthenticatedTest.makeRequest(app))
        .post('/api/v1/import/upload')
        .attach('file', largeBuffer, 'large.pdf')
        .field('source', 'pdf');

      expect(response.status).toBe(413);
      expect(response.body.success).toBe(false);
    });

    it('should require authentication for PDF upload', async () => {
      const pdfBuffer = createTestPDF();
      
      const response = await request(app)
        .post('/api/v1/import/upload')
        .attach('file', pdfBuffer, 'test.pdf')
        .field('source', 'pdf');

      expect(response.status).toBe(401);
      expect(response.body.success).toBe(false);
    });
  });

  describe('Trip Management API', () => {
    let createdTripId: string;

    it('should create a trip from imported conversation', async () => {
      // First import a conversation
      const importResponse = await (await AuthenticatedTest.makeRequest(app))
        .post('/api/import/conversation')
        .send({
          content: TEST_CONVERSATIONS.chatgpt,
          source: 'chatgpt'
        });

      expect(importResponse.status).toBe(200);
      createdTripId = importResponse.body.data.tripId;
    }, API_TIMEOUT);

    it('should retrieve created trip', async () => {
      const response = await (await AuthenticatedTest.makeRequest(app))
        .get(`/api/trips/${createdTripId}`);

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveProperty('id', createdTripId);
      expect(response.body.data).toHaveProperty('title');
      expect(response.body.data).toHaveProperty('days');
    });

    it('should list user trips', async () => {
      const response = await (await AuthenticatedTest.makeRequest(app))
        .get('/api/trips');

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveProperty('trips');
      expect(Array.isArray(response.body.data.trips)).toBe(true);
      
      // Should include our created trip
      const ourTrip = response.body.data.trips.find((t: any) => t.id === createdTripId);
      expect(ourTrip).toBeDefined();
    });

    it('should update trip details', async () => {
      const updateData = {
        title: 'Updated Japan Adventure 2025',
        description: 'An amazing journey through Japan'
      };

      const response = await (await AuthenticatedTest.makeRequest(app))
        .put(`/api/trips/${createdTripId}`)
        .send(updateData);

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data.title).toBe(updateData.title);
      expect(response.body.data.description).toBe(updateData.description);
    });

    it('should not allow unauthorized access to other user trips', async () => {
      // Try to access a non-existent/other user's trip
      const response = await (await AuthenticatedTest.makeRequest(app))
        .get('/api/trips/non-existent-trip-id');

      expect(response.status).toBe(404);
      expect(response.body.success).toBe(false);
    });

    it('should delete trip', async () => {
      const response = await (await AuthenticatedTest.makeRequest(app))
        .delete(`/api/trips/${createdTripId}`);

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);

      // Verify deletion
      const getResponse = await (await AuthenticatedTest.makeRequest(app))
        .get(`/api/trips/${createdTripId}`);
      
      expect(getResponse.status).toBe(404);
    });
  });

  describe('Error Handling and Edge Cases', () => {
    it('should handle malformed JSON gracefully', async () => {
      const response = await AuthenticatedTest.makeRequest(app, 'POST', '/api/import/conversation')
        .set('Content-Type', 'application/json')
        .send('{ invalid json');

      expect(response.status).toBe(400);
      expect(response.body.success).toBe(false);
    });

    it('should handle very long conversations', async () => {
      // Create a very long conversation (but within reasonable limits)
      const longConversation = `User: Plan a trip\n` + 
        'Assistant: '.padEnd(5000, 'Here is a detailed itinerary... ');

      const response = await AuthenticatedTest.makeRequest(app, 'POST', '/api/import/conversation')
        .send({
          content: longConversation,
          source: 'chatgpt'
        });

      // Should either succeed or fail gracefully with appropriate error
      expect([200, 400, 413]).toContain(response.status);
      expect(response.body).toHaveProperty('success');
    });

    it('should handle rate limiting appropriately', async () => {
      // Make multiple rapid requests
      const promises = Array(10).fill(null).map(() => 
        AuthenticatedTest.makeRequest(app).then(req => 
          req.get('/api/trips')
        )
      );

      const responses = await Promise.all(promises);
      
      // Check if any were rate limited
      const rateLimited = responses.filter(r => r.status === 429);
      
      // Rate limiting should work if configured
      if (rateLimited.length > 0) {
        expect(rateLimited[0].body.error).toContain('rate');
      }
    });

    it('should validate trip data formats', async () => {
      const invalidData = {
        title: '', // Empty title
        days: 'not-an-array', // Invalid days format
        startDate: 'invalid-date' // Invalid date
      };

      const response = await (await AuthenticatedTest.makeRequest(app))
        .post('/api/trips')
        .send(invalidData);

      expect(response.status).toBe(400);
      expect(response.body.success).toBe(false);
      expect(response.body.error).toBeDefined();
    });
  });

  describe('Integration Flow: Complete User Journey', () => {
    it('should complete full flow: login → import → edit → retrieve', async () => {
      // Step 1: Login
      const loginResponse = await request(app)
        .post('/api/auth/login')
        .send({
          email: TEST_USER_EMAIL,
          password: TEST_USER_PASSWORD
        });

      expect(loginResponse.status).toBe(200);
      const token = loginResponse.body.data.token;

      // Step 2: Import conversation
      const importResponse = await request(app)
        .post('/api/import/conversation')
        .set('Authorization', `Bearer ${token}`)
        .send({
          content: TEST_CONVERSATIONS.gemini,
          source: 'gemini'
        });

      expect(importResponse.status).toBe(200);
      const tripId = importResponse.body.data.tripId;

      // Step 3: Edit trip
      const editResponse = await request(app)
        .put(`/api/trips/${tripId}`)
        .set('Authorization', `Bearer ${token}`)
        .send({
          title: 'My Spanish Architecture Tour',
          tags: ['architecture', 'food', 'culture']
        });

      expect(editResponse.status).toBe(200);

      // Step 4: Retrieve final trip
      const getResponse = await request(app)
        .get(`/api/trips/${tripId}`)
        .set('Authorization', `Bearer ${token}`);

      expect(getResponse.status).toBe(200);
      expect(getResponse.body.data.title).toBe('My Spanish Architecture Tour');
      expect(getResponse.body.data.tags).toContain('architecture');

      // Cleanup
      await request(app)
        .delete(`/api/trips/${tripId}`)
        .set('Authorization', `Bearer ${token}`);
    }, API_TIMEOUT);
  });
});