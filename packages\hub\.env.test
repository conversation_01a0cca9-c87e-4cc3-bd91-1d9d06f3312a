# Test Environment Configuration
# Use placeholder credentials for security - replace with real values locally

# Upstash Redis (Test instance)
UPSTASH_REDIS_REST_URL=https://your-test-redis-endpoint.upstash.io
UPSTASH_REDIS_REST_TOKEN=your-test-redis-rest-token-here
# Also provide the alternate naming for compatibility
UPSTASH_REDIS_URL=https://your-test-redis-endpoint.upstash.io
UPSTASH_REDIS_TOKEN=your-test-redis-token-here

# Supabase (Test project)
SUPABASE_URL=https://your-test-project.supabase.co
SUPABASE_ANON_KEY=your-test-supabase-anon-key-here
SUPABASE_SERVICE_ROLE_KEY=your-test-supabase-service-role-key-here

# Other services (use test-specific keys when available)
OPENROUTER_API_KEY=sk-or-v1-your-test-openrouter-api-key-here
GOOGLE_PLACES_API_KEY=your-test-google-places-api-key-here

# Test user credentials
TEST_USER_EMAIL=<EMAIL>
TEST_USER_PASSWORD=Flaremmk123!

# JWT Secret for tests
JWT_SECRET=test-jwt-secret-key-with-at-least-32-characters-for-tests

# Test data isolation prefix
TEST_DATA_PREFIX=test_