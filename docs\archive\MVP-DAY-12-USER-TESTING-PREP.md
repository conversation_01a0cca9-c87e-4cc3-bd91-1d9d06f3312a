# MVP Day 12: User Testing Prep

**Date**: [Execute Date]  
**Goal**: Create impressive demo content and prepare for user feedback  
**Duration**: 8 hours  
**Critical Path**: YES - First impressions determine product success

## Context & Testing Strategy

### Why This Matters

- Users judge products in <10 seconds
- Demo content showcases possibilities
- FAQ reduces support burden
- Feedback loop drives improvement
- Social proof builds trust

### Success Metrics

- 5 stunning demo trips ready
- 2-minute demo video recorded
- FAQ covers 90% of questions
- Feedback system captures insights
- Support response <2 hours

## Testing Infrastructure Setup

### Session Recording & Analytics Implementation

**File**: `packages/web/src/services/analytics.service.ts`

```typescript
import { FullStory, init as initFullStory } from '@fullstory/browser';
import mixpanel from 'mixpanel-browser';
import { PostHog } from 'posthog-js';
import * as Sentry from '@sentry/nextjs';

interface AnalyticsConfig {
  fullstory?: { orgId: string };
  mixpanel?: { token: string };
  posthog?: { apiKey: string; host: string };
  hotjar?: { siteId: number };
  googleAnalytics?: { measurementId: string };
}

class AnalyticsService {
  private initialized = false;
  private userId: string | null = null;
  private consent: 'pending' | 'granted' | 'denied' = 'pending';

  async initialize(config: AnalyticsConfig) {
    if (this.initialized) return;

    // Check for user consent (GDPR compliance)
    this.consent = await this.checkConsent();

    if (this.consent === 'denied') {
      console.log('Analytics disabled due to user preference');
      return;
    }

    // FullStory for session recording
    if (config.fullstory && process.env.NODE_ENV === 'production') {
      initFullStory({ orgId: config.fullstory.orgId });

      // Configure privacy settings
      FullStory('setUserVars', {
        privacyMode: true, // Mask sensitive data
        recordOnlyPublicPages: true,
      });
    }

    // Mixpanel for product analytics
    if (config.mixpanel) {
      mixpanel.init(config.mixpanel.token, {
        debug: process.env.NODE_ENV === 'development',
        track_pageview: true,
        persistence: 'localStorage',
        ip: false, // Don't track IP for privacy
        property_blacklist: ['$email', '$phone'], // GDPR compliance
      });
    }

    // PostHog for feature flags & experiments
    if (config.posthog) {
      const posthog = new PostHog(config.posthog.apiKey, {
        api_host: config.posthog.host,
        person_profiles: 'identified_only',
        capture_pageview: false, // Manual control
        capture_pageleave: true,
        autocapture: {
          dom_event_allowlist: ['click', 'submit'], // Limit autocapture
          element_allowlist: ['button', 'a', 'form'],
        },
      });
    }

    // Hotjar for heatmaps
    if (config.hotjar && typeof window !== 'undefined') {
      (function (h, o, t, j, a, r) {
        h.hj =
          h.hj ||
          function () {
            (h.hj.q = h.hj.q || []).push(arguments);
          };
        h._hjSettings = { hjid: config.hotjar.siteId, hjsv: 6 };
        a = o.getElementsByTagName('head')[0];
        r = o.createElement('script');
        r.async = 1;
        r.src = t + h._hjSettings.hjid + j + h._hjSettings.hjsv;
        a.appendChild(r);
      })(window, document, 'https://static.hotjar.com/c/hotjar-', '.js?sv=');
    }

    this.initialized = true;
  }

  // GDPR-compliant consent check
  private async checkConsent(): Promise<'pending' | 'granted' | 'denied'> {
    const stored = localStorage.getItem('analytics_consent');
    if (stored) return stored as any;

    // Show consent banner if not set
    return 'pending';
  }

  // User identification (after login)
  identify(userId: string, traits?: Record<string, any>) {
    this.userId = userId;

    // Anonymize PII data
    const safeTraits = this.sanitizeTraits(traits);

    if (typeof FullStory !== 'undefined') {
      FullStory('identify', userId, safeTraits);
    }

    if (typeof mixpanel !== 'undefined') {
      mixpanel.identify(userId);
      mixpanel.people.set(safeTraits);
    }

    // Add to error tracking
    Sentry.setUser({ id: userId, ...safeTraits });
  }

  // Track events with context
  track(event: string, properties?: Record<string, any>) {
    if (this.consent !== 'granted') return;

    const enrichedProps = {
      ...properties,
      timestamp: new Date().toISOString(),
      session_id: this.getSessionId(),
      page_url: window.location.href,
      user_agent: navigator.userAgent,
    };

    if (typeof mixpanel !== 'undefined') {
      mixpanel.track(event, enrichedProps);
    }

    // Also send to backend for custom analytics
    this.sendToBackend(event, enrichedProps);
  }

  // A/B test tracking
  trackExperiment(experimentId: string, variant: string) {
    this.track('experiment_viewed', {
      experiment_id: experimentId,
      variant,
      timestamp: Date.now(),
    });
  }

  // Performance tracking
  trackPerformance() {
    if (typeof window === 'undefined') return;

    // Core Web Vitals
    if ('PerformanceObserver' in window) {
      try {
        // LCP (Largest Contentful Paint)
        const lcpObserver = new PerformanceObserver(list => {
          const entries = list.getEntries();
          const lastEntry = entries[entries.length - 1];
          this.track('web_vitals', {
            metric: 'LCP',
            value: lastEntry.startTime,
            rating: this.getRating('LCP', lastEntry.startTime),
          });
        });
        lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] });

        // FID (First Input Delay)
        const fidObserver = new PerformanceObserver(list => {
          for (const entry of list.getEntries()) {
            if (entry.entryType === 'first-input') {
              const delay = entry.processingStart - entry.startTime;
              this.track('web_vitals', {
                metric: 'FID',
                value: delay,
                rating: this.getRating('FID', delay),
              });
            }
          }
        });
        fidObserver.observe({ entryTypes: ['first-input'] });

        // CLS (Cumulative Layout Shift)
        let clsValue = 0;
        const clsObserver = new PerformanceObserver(list => {
          for (const entry of list.getEntries()) {
            if (!entry.hadRecentInput) {
              clsValue += entry.value;
            }
          }
        });
        clsObserver.observe({ entryTypes: ['layout-shift'] });

        // Report CLS on page unload
        window.addEventListener('beforeunload', () => {
          this.track('web_vitals', {
            metric: 'CLS',
            value: clsValue,
            rating: this.getRating('CLS', clsValue),
          });
        });
      } catch (e) {
        console.error('Performance tracking error:', e);
      }
    }
  }

  // Helper to rate Core Web Vitals
  private getRating(metric: string, value: number): 'good' | 'needs-improvement' | 'poor' {
    const thresholds = {
      LCP: { good: 2500, poor: 4000 },
      FID: { good: 100, poor: 300 },
      CLS: { good: 0.1, poor: 0.25 },
    };

    const threshold = thresholds[metric];
    if (!threshold) return 'needs-improvement';

    if (value <= threshold.good) return 'good';
    if (value <= threshold.poor) return 'needs-improvement';
    return 'poor';
  }

  // Session management
  private getSessionId(): string {
    let sessionId = sessionStorage.getItem('analytics_session_id');
    if (!sessionId) {
      sessionId = `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
      sessionStorage.setItem('analytics_session_id', sessionId);
    }
    return sessionId;
  }

  // Sanitize PII data
  private sanitizeTraits(traits?: Record<string, any>): Record<string, any> {
    if (!traits) return {};

    const sensitive = ['email', 'phone', 'ssn', 'credit_card'];
    const sanitized = { ...traits };

    sensitive.forEach(key => {
      if (sanitized[key]) {
        sanitized[key] = '[REDACTED]';
      }
    });

    return sanitized;
  }

  // Send to backend for custom processing
  private async sendToBackend(event: string, properties: Record<string, any>) {
    try {
      await fetch('/api/v1/analytics/track', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ event, properties, userId: this.userId }),
      });
    } catch (e) {
      // Fail silently
    }
  }
}

export const analytics = new AnalyticsService();
```

### Real-time Feedback Collection System

**File**: `packages/web/src/components/feedback/FeedbackPrioritizer.tsx`

```typescript
import { useState, useEffect } from 'react';
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { supabase } from '@/lib/supabase';

interface FeedbackItem {
  id: string;
  type: 'bug' | 'feature' | 'praise' | 'other';
  message: string;
  email?: string;
  userId?: string;
  metadata: {
    page: string;
    userAgent: string;
    timestamp: string;
    sessionId?: string;
  };
  status: 'new' | 'triaged' | 'in-progress' | 'resolved';
  priority?: 'critical' | 'high' | 'medium' | 'low';
  impactScore?: number;
  effortScore?: number;
  tags?: string[];
  assignee?: string;
  resolution?: string;
  createdAt: string;
}

interface PriorityMatrix {
  critical: FeedbackItem[];
  highValue: FeedbackItem[];
  quickWins: FeedbackItem[];
  backlog: FeedbackItem[];
}

export function FeedbackPrioritizer() {
  const [feedback, setFeedback] = useState<FeedbackItem[]>([]);
  const [matrix, setMatrix] = useState<PriorityMatrix>({
    critical: [],
    highValue: [],
    quickWins: [],
    backlog: []
  });
  const [filters, setFilters] = useState({
    type: 'all',
    status: 'new',
    timeframe: '24h'
  });

  useEffect(() => {
    // Real-time subscription to feedback
    const subscription = supabase
      .channel('feedback-changes')
      .on('postgres_changes', {
        event: '*',
        schema: 'public',
        table: 'feedback'
      }, handleFeedbackChange)
      .subscribe();

    loadFeedback();

    return () => {
      subscription.unsubscribe();
    };
  }, [filters]);

  const handleFeedbackChange = (payload: any) => {
    if (payload.eventType === 'INSERT') {
      // New feedback - prioritize immediately
      const newItem = payload.new as FeedbackItem;
      prioritizeFeedback(newItem);
      setFeedback(prev => [newItem, ...prev]);
    } else if (payload.eventType === 'UPDATE') {
      setFeedback(prev => prev.map(item =>
        item.id === payload.new.id ? payload.new : item
      ));
    }
  };

  const prioritizeFeedback = async (item: FeedbackItem) => {
    // Auto-prioritization logic
    let priority: 'critical' | 'high' | 'medium' | 'low' = 'medium';
    let impactScore = 5;
    let effortScore = 5;

    // Critical bugs
    if (item.type === 'bug' && item.message.match(/can't|crash|error|broken/i)) {
      priority = 'critical';
      impactScore = 10;
    }

    // Payment issues
    if (item.message.match(/payment|billing|charge|refund/i)) {
      priority = 'critical';
      impactScore = 10;
    }

    // Feature requests from paying users
    if (item.type === 'feature' && item.userId) {
      const user = await getUser(item.userId);
      if (user?.subscription === 'pro') {
        priority = 'high';
        impactScore = 8;
      }
    }

    // Quick wins (small text changes, UI tweaks)
    if (item.message.match(/typo|color|spacing|text/i)) {
      effortScore = 2;
    }

    // Update item with scores
    await supabase
      .from('feedback')
      .update({
        priority,
        impactScore,
        effortScore
      })
      .eq('id', item.id);
  };

  const loadFeedback = async () => {
    let query = supabase
      .from('feedback')
      .select('*')
      .order('created_at', { ascending: false });

    // Apply filters
    if (filters.type !== 'all') {
      query = query.eq('type', filters.type);
    }
    if (filters.status !== 'all') {
      query = query.eq('status', filters.status);
    }

    // Timeframe filter
    const timeframes = {
      '1h': 1,
      '24h': 24,
      '7d': 168,
      '30d': 720
    };
    if (filters.timeframe in timeframes) {
      const hoursAgo = new Date();
      hoursAgo.setHours(hoursAgo.getHours() - timeframes[filters.timeframe]);
      query = query.gte('created_at', hoursAgo.toISOString());
    }

    const { data, error } = await query;
    if (!error && data) {
      setFeedback(data);
      categorizeFeedback(data);
    }
  };

  const categorizeFeedback = (items: FeedbackItem[]) => {
    const categorized: PriorityMatrix = {
      critical: [],
      highValue: [],
      quickWins: [],
      backlog: []
    };

    items.forEach(item => {
      const impact = item.impactScore || 5;
      const effort = item.effortScore || 5;

      if (item.priority === 'critical') {
        categorized.critical.push(item);
      } else if (impact >= 7 && effort <= 3) {
        categorized.quickWins.push(item);
      } else if (impact >= 7) {
        categorized.highValue.push(item);
      } else {
        categorized.backlog.push(item);
      }
    });

    setMatrix(categorized);
  };

  const getUser = async (userId: string) => {
    const { data } = await supabase
      .from('users')
      .select('subscription')
      .eq('id', userId)
      .single();
    return data;
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Feedback Priority Matrix</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 gap-4">
            {/* High Impact, Low Effort - Quick Wins */}
            <div className="border-2 border-green-500 rounded-lg p-4">
              <h3 className="font-semibold text-green-700 mb-2">
                Quick Wins ({matrix.quickWins.length})
              </h3>
              <p className="text-sm text-gray-600 mb-3">High Impact, Low Effort</p>
              <div className="space-y-2">
                {matrix.quickWins.slice(0, 3).map(item => (
                  <FeedbackCard key={item.id} item={item} />
                ))}
              </div>
            </div>

            {/* High Impact, High Effort - Strategic */}
            <div className="border-2 border-blue-500 rounded-lg p-4">
              <h3 className="font-semibold text-blue-700 mb-2">
                High Value ({matrix.highValue.length})
              </h3>
              <p className="text-sm text-gray-600 mb-3">High Impact, High Effort</p>
              <div className="space-y-2">
                {matrix.highValue.slice(0, 3).map(item => (
                  <FeedbackCard key={item.id} item={item} />
                ))}
              </div>
            </div>

            {/* Critical Issues */}
            <div className="border-2 border-red-500 rounded-lg p-4 col-span-2">
              <h3 className="font-semibold text-red-700 mb-2">
                Critical Issues ({matrix.critical.length})
              </h3>
              <p className="text-sm text-gray-600 mb-3">Immediate Attention Required</p>
              <div className="space-y-2">
                {matrix.critical.map(item => (
                  <FeedbackCard key={item.id} item={item} critical />
                ))}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Detailed Feedback List */}
      <Card>
        <CardHeader>
          <CardTitle>All Feedback</CardTitle>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="new">
            <TabsList>
              <TabsTrigger value="new">New ({feedback.filter(f => f.status === 'new').length})</TabsTrigger>
              <TabsTrigger value="triaged">Triaged</TabsTrigger>
              <TabsTrigger value="in-progress">In Progress</TabsTrigger>
              <TabsTrigger value="resolved">Resolved</TabsTrigger>
            </TabsList>

            <TabsContent value="new" className="space-y-4">
              {feedback
                .filter(f => f.status === 'new')
                .map(item => (
                  <DetailedFeedbackCard key={item.id} item={item} />
                ))}
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
}

function FeedbackCard({ item, critical = false }: { item: FeedbackItem; critical?: boolean }) {
  return (
    <div className={`p-3 rounded ${critical ? 'bg-red-50' : 'bg-gray-50'}`}>
      <div className="flex items-start justify-between">
        <div className="flex-1">
          <p className="text-sm font-medium line-clamp-2">{item.message}</p>
          <div className="flex items-center gap-2 mt-1">
            <Badge variant={item.type === 'bug' ? 'destructive' : 'default'} className="text-xs">
              {item.type}
            </Badge>
            <span className="text-xs text-gray-500">
              {new Date(item.createdAt).toRelaleString()}
            </span>
          </div>
        </div>
      </div>
    </div>
  );
}

function DetailedFeedbackCard({ item }: { item: FeedbackItem }) {
  const [isExpanded, setIsExpanded] = useState(false);

  return (
    <Card>
      <CardContent className="p-4">
        <div className="space-y-3">
          <div className="flex items-start justify-between">
            <div className="flex-1">
              <p className="font-medium">{item.message}</p>
              <div className="flex items-center gap-2 mt-2">
                <Badge variant={item.type === 'bug' ? 'destructive' : 'default'}>
                  {item.type}
                </Badge>
                {item.priority && (
                  <Badge variant={item.priority === 'critical' ? 'destructive' : 'outline'}>
                    {item.priority}
                  </Badge>
                )}
                <span className="text-sm text-gray-500">
                  {item.email || 'Anonymous'}
                </span>
              </div>
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setIsExpanded(!isExpanded)}
            >
              {isExpanded ? 'Less' : 'More'}
            </Button>
          </div>

          {isExpanded && (
            <div className="pt-3 border-t space-y-2">
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <p className="text-gray-500">Impact Score</p>
                  <p className="font-medium">{item.impactScore || 'Not rated'}</p>
                </div>
                <div>
                  <p className="text-gray-500">Effort Score</p>
                  <p className="font-medium">{item.effortScore || 'Not rated'}</p>
                </div>
                <div>
                  <p className="text-gray-500">Page</p>
                  <p className="font-medium">{item.metadata.page}</p>
                </div>
                <div>
                  <p className="text-gray-500">Time</p>
                  <p className="font-medium">
                    {new Date(item.metadata.timestamp).toLocaleString()}
                  </p>
                </div>
              </div>

              <div className="flex gap-2">
                <Button size="sm" variant="outline">Assign</Button>
                <Button size="sm" variant="outline">Tag</Button>
                <Button size="sm">Start Work</Button>
              </div>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
```

### A/B Testing Framework

**File**: `packages/web/src/services/experiment.service.ts`

```typescript
import { analytics } from './analytics.service';
import { supabase } from '@/lib/supabase';

interface Experiment {
  id: string;
  name: string;
  description: string;
  variants: Variant[];
  targetingRules?: TargetingRule[];
  trafficAllocation: number; // 0-100
  status: 'draft' | 'running' | 'paused' | 'completed';
  startDate?: string;
  endDate?: string;
  primaryMetric: string;
  secondaryMetrics?: string[];
  minimumSampleSize?: number;
  statisticalSignificance?: number; // Default 95%
}

interface Variant {
  id: string;
  name: string;
  weight: number; // Traffic percentage
  config: Record<string, any>;
  metrics?: VariantMetrics;
}

interface VariantMetrics {
  participants: number;
  conversions: number;
  conversionRate: number;
  confidence?: number;
  uplift?: number;
}

interface TargetingRule {
  type: 'user_property' | 'behavior' | 'technology' | 'location';
  operator: 'equals' | 'contains' | 'greater_than' | 'less_than';
  value: any;
}

class ExperimentService {
  private experiments: Map<string, Experiment> = new Map();
  private userAssignments: Map<string, Map<string, string>> = new Map();
  private localStorage = typeof window !== 'undefined' ? window.localStorage : null;

  async initialize(userId?: string) {
    // Load active experiments
    const { data: experiments } = await supabase
      .from('experiments')
      .select('*')
      .eq('status', 'running');

    if (experiments) {
      experiments.forEach(exp => {
        this.experiments.set(exp.id, exp);
      });
    }

    // Load user's existing assignments
    if (userId) {
      const { data: assignments } = await supabase
        .from('experiment_assignments')
        .select('*')
        .eq('user_id', userId);

      if (assignments) {
        const userMap = new Map<string, string>();
        assignments.forEach(a => {
          userMap.set(a.experiment_id, a.variant_id);
        });
        this.userAssignments.set(userId, userMap);
      }
    }

    // Also check local storage for anonymous users
    if (this.localStorage) {
      const stored = this.localStorage.getItem('experiment_assignments');
      if (stored) {
        try {
          const parsed = JSON.parse(stored);
          Object.entries(parsed).forEach(([expId, variantId]) => {
            this.assignAnonymousUser(expId, variantId as string);
          });
        } catch (e) {
          // Invalid data, ignore
        }
      }
    }
  }

  getVariant(experimentId: string, userId?: string): string | null {
    const experiment = this.experiments.get(experimentId);
    if (!experiment || experiment.status !== 'running') {
      return null;
    }

    // Check if user is already assigned
    const existingAssignment = this.getUserAssignment(experimentId, userId);
    if (existingAssignment) {
      this.trackExposure(experimentId, existingAssignment, userId);
      return existingAssignment;
    }

    // Check targeting rules
    if (!this.isEligible(experiment, userId)) {
      return null;
    }

    // Check traffic allocation
    if (Math.random() * 100 > experiment.trafficAllocation) {
      return null; // User not in experiment
    }

    // Assign variant
    const variant = this.selectVariant(experiment);
    if (variant) {
      this.assignUser(experimentId, variant.id, userId);
      this.trackExposure(experimentId, variant.id, userId);
    }

    return variant?.id || null;
  }

  private getUserAssignment(experimentId: string, userId?: string): string | null {
    if (userId) {
      const userMap = this.userAssignments.get(userId);
      if (userMap?.has(experimentId)) {
        return userMap.get(experimentId)!;
      }
    }

    // Check anonymous assignments
    const anonMap = this.userAssignments.get('anonymous');
    return anonMap?.get(experimentId) || null;
  }

  private isEligible(experiment: Experiment, userId?: string): boolean {
    if (!experiment.targetingRules || experiment.targetingRules.length === 0) {
      return true;
    }

    // TODO: Implement targeting rule evaluation
    // For now, include everyone
    return true;
  }

  private selectVariant(experiment: Experiment): Variant | null {
    const random = Math.random() * 100;
    let cumulative = 0;

    for (const variant of experiment.variants) {
      cumulative += variant.weight;
      if (random <= cumulative) {
        return variant;
      }
    }

    return experiment.variants[0]; // Fallback
  }

  private assignUser(experimentId: string, variantId: string, userId?: string) {
    if (userId) {
      // Persist to database
      supabase
        .from('experiment_assignments')
        .upsert({
          user_id: userId,
          experiment_id: experimentId,
          variant_id: variantId,
          assigned_at: new Date().toISOString(),
        })
        .then(() => {
          // Update local cache
          const userMap = this.userAssignments.get(userId) || new Map();
          userMap.set(experimentId, variantId);
          this.userAssignments.set(userId, userMap);
        });
    } else {
      // Anonymous user - store locally
      this.assignAnonymousUser(experimentId, variantId);
    }
  }

  private assignAnonymousUser(experimentId: string, variantId: string) {
    const anonMap = this.userAssignments.get('anonymous') || new Map();
    anonMap.set(experimentId, variantId);
    this.userAssignments.set('anonymous', anonMap);

    // Persist to localStorage
    if (this.localStorage) {
      const existing = JSON.parse(this.localStorage.getItem('experiment_assignments') || '{}');
      existing[experimentId] = variantId;
      this.localStorage.setItem('experiment_assignments', JSON.stringify(existing));
    }
  }

  private trackExposure(experimentId: string, variantId: string, userId?: string) {
    analytics.trackExperiment(experimentId, variantId);

    // Also track to backend for analysis
    supabase.from('experiment_events').insert({
      experiment_id: experimentId,
      variant_id: variantId,
      user_id: userId,
      event_type: 'exposure',
      timestamp: new Date().toISOString(),
    });
  }

  // Track conversion events
  trackConversion(experimentId: string, metricName: string, value?: number, userId?: string) {
    const variantId = this.getUserAssignment(experimentId, userId);
    if (!variantId) return;

    analytics.track(`experiment_conversion`, {
      experiment_id: experimentId,
      variant_id: variantId,
      metric: metricName,
      value,
    });

    supabase.from('experiment_events').insert({
      experiment_id: experimentId,
      variant_id: variantId,
      user_id: userId,
      event_type: 'conversion',
      metric_name: metricName,
      metric_value: value,
      timestamp: new Date().toISOString(),
    });
  }

  // Get experiment results with statistical analysis
  async getResults(experimentId: string): Promise<ExperimentResults> {
    const experiment = this.experiments.get(experimentId);
    if (!experiment) {
      throw new Error('Experiment not found');
    }

    // Fetch aggregated metrics
    const { data: metrics } = await supabase.rpc('get_experiment_results', {
      experiment_id: experimentId,
    });

    // Calculate statistical significance
    const results: ExperimentResults = {
      experiment,
      variants: [],
      winner: null,
      confidence: 0,
      recommendedAction: '',
    };

    if (metrics && metrics.length > 0) {
      const control = metrics.find(m => m.variant_name === 'control') || metrics[0];

      results.variants = metrics.map(variant => {
        const stats = this.calculateStats(
          control.conversions,
          control.participants,
          variant.conversions,
          variant.participants
        );

        return {
          ...variant,
          conversionRate:
            variant.participants > 0 ? (variant.conversions / variant.participants) * 100 : 0,
          uplift: stats.uplift,
          confidence: stats.confidence,
          isSignificant: stats.isSignificant,
        };
      });

      // Determine winner
      const significantVariants = results.variants.filter(
        v => v.isSignificant && v.uplift > 0 && v.variant_name !== 'control'
      );

      if (significantVariants.length > 0) {
        results.winner = significantVariants.reduce((best, current) =>
          current.uplift > best.uplift ? current : best
        );
        results.confidence = results.winner.confidence;
      }

      // Recommendation
      results.recommendedAction = this.getRecommendation(results);
    }

    return results;
  }

  private calculateStats(
    controlConversions: number,
    controlTotal: number,
    variantConversions: number,
    variantTotal: number
  ) {
    const controlRate = controlConversions / controlTotal;
    const variantRate = variantConversions / variantTotal;

    // Calculate uplift
    const uplift = ((variantRate - controlRate) / controlRate) * 100;

    // Calculate standard error
    const controlSE = Math.sqrt((controlRate * (1 - controlRate)) / controlTotal);
    const variantSE = Math.sqrt((variantRate * (1 - variantRate)) / variantTotal);
    const pooledSE = Math.sqrt(controlSE ** 2 + variantSE ** 2);

    // Calculate z-score
    const zScore = Math.abs(variantRate - controlRate) / pooledSE;

    // Calculate confidence (two-tailed test)
    const confidence = this.zScoreToConfidence(zScore);

    return {
      uplift,
      confidence,
      isSignificant: confidence >= (this.experiments.get('').statisticalSignificance || 95),
    };
  }

  private zScoreToConfidence(zScore: number): number {
    // Simplified conversion - use proper statistics library in production
    if (zScore >= 2.58) return 99;
    if (zScore >= 1.96) return 95;
    if (zScore >= 1.64) return 90;
    return Math.min(zScore * 50, 89);
  }

  private getRecommendation(results: ExperimentResults): string {
    if (!results.winner) {
      const totalParticipants = results.variants.reduce((sum, v) => sum + v.participants, 0);
      if (totalParticipants < (results.experiment.minimumSampleSize || 1000)) {
        return 'Continue running - insufficient sample size';
      }
      return 'No significant difference detected - consider stopping';
    }

    if (results.confidence >= 95) {
      return `Deploy ${results.winner.variant_name} - ${results.winner.uplift.toFixed(1)}% uplift with ${results.confidence}% confidence`;
    }

    return `${results.winner.variant_name} trending positive - continue monitoring`;
  }
}

export const experimentService = new ExperimentService();

// React hook for easy A/B testing
export function useExperiment(experimentId: string) {
  const [variant, setVariant] = useState<string | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const loadVariant = async () => {
      // Get user ID if logged in
      const {
        data: { user },
      } = await supabase.auth.getUser();
      const variantId = experimentService.getVariant(experimentId, user?.id);
      setVariant(variantId);
      setLoading(false);
    };

    loadVariant();
  }, [experimentId]);

  const trackConversion = (metricName: string, value?: number) => {
    experimentService.trackConversion(experimentId, metricName, value);
  };

  return { variant, loading, trackConversion };
}

interface ExperimentResults {
  experiment: Experiment;
  variants: Array<{
    variant_id: string;
    variant_name: string;
    participants: number;
    conversions: number;
    conversionRate: number;
    uplift: number;
    confidence: number;
    isSignificant: boolean;
  }>;
  winner: any | null;
  confidence: number;
  recommendedAction: string;
}
```

### Performance Under Load Testing

**File**: `packages/hub/src/tests/load/surge-simulation.ts`

```typescript
import { spawn } from 'child_process';
import axios from 'axios';
import pLimit from 'p-limit';
import { performance } from 'perf_hooks';

interface SurgeTestConfig {
  baseUrl: string;
  stages: Array<{
    duration: number; // seconds
    users: number;
    rampUp?: number; // seconds to reach target users
  }>;
  scenarios: Array<{
    name: string;
    weight: number;
    steps: Array<{
      method: 'GET' | 'POST' | 'PUT' | 'DELETE';
      path: string;
      body?: any;
      think?: number; // seconds between requests
    }>;
  }>;
}

class SurgeSimulator {
  private metrics: {
    requests: number;
    errors: number;
    responseTime: number[];
    throughput: number[];
    concurrentUsers: number;
  } = {
    requests: 0,
    errors: 0,
    responseTime: [],
    throughput: [],
    concurrentUsers: 0,
  };

  async runSurgeTest(config: SurgeTestConfig) {
    console.log('🌊 Starting User Surge Simulation...\n');

    for (const stage of config.stages) {
      await this.runStage(stage, config);
    }

    this.printResults();
  }

  private async runStage(
    stage: { duration: number; users: number; rampUp?: number },
    config: SurgeTestConfig
  ) {
    console.log(`📈 Stage: ${stage.users} users for ${stage.duration}s`);

    const rampUpTime = stage.rampUp || 0;
    const steadyTime = stage.duration - rampUpTime;
    const usersPerSecond = stage.users / (rampUpTime || 1);

    // Ramp up phase
    if (rampUpTime > 0) {
      for (let i = 0; i < rampUpTime; i++) {
        const currentUsers = Math.floor(usersPerSecond * (i + 1));
        this.metrics.concurrentUsers = currentUsers;

        await this.spawnUsers(currentUsers - this.metrics.concurrentUsers, config);

        await this.sleep(1000);
      }
    } else {
      // Immediate load
      await this.spawnUsers(stage.users, config);
      this.metrics.concurrentUsers = stage.users;
    }

    // Steady state
    await this.sleep(steadyTime * 1000);
  }

  private async spawnUsers(count: number, config: SurgeTestConfig) {
    const limit = pLimit(50); // Limit concurrent promises
    const promises = [];

    for (let i = 0; i < count; i++) {
      promises.push(limit(() => this.simulateUser(config)));
    }

    await Promise.all(promises);
  }

  private async simulateUser(config: SurgeTestConfig) {
    // Pick random scenario based on weights
    const scenario = this.selectScenario(config.scenarios);

    for (const step of scenario.steps) {
      const start = performance.now();

      try {
        const response = await axios({
          method: step.method,
          url: `${config.baseUrl}${step.path}`,
          data: step.body,
          timeout: 30000,
          validateStatus: () => true, // Don't throw on non-2xx
        });

        const duration = performance.now() - start;
        this.metrics.requests++;
        this.metrics.responseTime.push(duration);

        if (response.status >= 400) {
          this.metrics.errors++;
          console.error(`❌ ${step.method} ${step.path}: ${response.status}`);
        }

        // Think time
        if (step.think) {
          await this.sleep(step.think * 1000);
        }
      } catch (error) {
        this.metrics.errors++;
        console.error(`💥 Request failed: ${error.message}`);
      }
    }
  }

  private selectScenario(scenarios: any[]) {
    const random = Math.random() * 100;
    let cumulative = 0;

    for (const scenario of scenarios) {
      cumulative += scenario.weight;
      if (random <= cumulative) {
        return scenario;
      }
    }

    return scenarios[0];
  }

  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  private printResults() {
    const sortedResponseTimes = [...this.metrics.responseTime].sort((a, b) => a - b);
    const p50 = sortedResponseTimes[Math.floor(sortedResponseTimes.length * 0.5)];
    const p95 = sortedResponseTimes[Math.floor(sortedResponseTimes.length * 0.95)];
    const p99 = sortedResponseTimes[Math.floor(sortedResponseTimes.length * 0.99)];

    console.log('\n📊 Surge Test Results:');
    console.log('====================');
    console.log(`Total Requests: ${this.metrics.requests}`);
    console.log(
      `Errors: ${this.metrics.errors} (${((this.metrics.errors / this.metrics.requests) * 100).toFixed(2)}%)`
    );
    console.log(`\nResponse Times:`);
    console.log(`  P50: ${p50?.toFixed(0)}ms`);
    console.log(`  P95: ${p95?.toFixed(0)}ms`);
    console.log(`  P99: ${p99?.toFixed(0)}ms`);
    console.log(`  Max: ${Math.max(...this.metrics.responseTime).toFixed(0)}ms`);

    if (this.metrics.errors > this.metrics.requests * 0.01) {
      console.log('\n⚠️  HIGH ERROR RATE DETECTED - System may not handle surge well');
    } else if (p95 > 2000) {
      console.log('\n⚠️  HIGH RESPONSE TIMES - Consider optimization');
    } else {
      console.log('\n✅ System handled surge successfully!');
    }
  }
}

// Run surge test
const surgeTest = new SurgeSimulator();

surgeTest.runSurgeTest({
  baseUrl: process.env.API_URL || 'http://localhost:3001',
  stages: [
    { duration: 60, users: 10, rampUp: 10 }, // Warm up
    { duration: 120, users: 100, rampUp: 30 }, // Normal load
    { duration: 60, users: 500, rampUp: 0 }, // Sudden spike!
    { duration: 120, users: 200, rampUp: 30 }, // Recovery
  ],
  scenarios: [
    {
      name: 'Browse and Import',
      weight: 60,
      steps: [
        { method: 'GET', path: '/api/trips/public', think: 2 },
        {
          method: 'POST',
          path: '/api/auth/login',
          body: {
            /* ... */
          },
          think: 1,
        },
        {
          method: 'POST',
          path: '/api/trips/import',
          body: {
            /* ... */
          },
          think: 5,
        },
      ],
    },
    {
      name: 'Quick Browse',
      weight: 30,
      steps: [
        { method: 'GET', path: '/', think: 3 },
        { method: 'GET', path: '/api/trips/public', think: 2 },
      ],
    },
    {
      name: 'API Stress',
      weight: 10,
      steps: [
        { method: 'GET', path: '/api/health' },
        { method: 'GET', path: '/api/health' },
        { method: 'GET', path: '/api/health' },
      ],
    },
  ],
});
```

### Data Privacy & GDPR Compliance

**File**: `packages/web/src/components/privacy/ConsentManager.tsx`

```typescript
import { useState, useEffect } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Switch } from '@/components/ui/switch';
import { Shield, Cookie, Camera, BarChart } from 'lucide-react';
import { analytics } from '@/services/analytics.service';

interface ConsentSettings {
  necessary: boolean; // Always true
  analytics: boolean;
  marketing: boolean;
  recording: boolean;
}

interface ConsentManagerProps {
  onConsentUpdate?: (settings: ConsentSettings) => void;
}

export function ConsentManager({ onConsentUpdate }: ConsentManagerProps) {
  const [isVisible, setIsVisible] = useState(false);
  const [isDetailView, setIsDetailView] = useState(false);
  const [consent, setConsent] = useState<ConsentSettings>({
    necessary: true,
    analytics: false,
    marketing: false,
    recording: false
  });

  useEffect(() => {
    // Check for existing consent
    const stored = localStorage.getItem('privacy_consent');
    if (!stored) {
      // Show consent banner after 2 seconds
      setTimeout(() => setIsVisible(true), 2000);
    } else {
      try {
        const parsed = JSON.parse(stored);
        setConsent(parsed);
        applyConsent(parsed);
      } catch (e) {
        setIsVisible(true);
      }
    }
  }, []);

  const applyConsent = (settings: ConsentSettings) => {
    // Apply to analytics services
    if (settings.analytics) {
      analytics.enable();
    } else {
      analytics.disable();
    }

    // Apply to session recording
    if (settings.recording && typeof FullStory !== 'undefined') {
      FullStory('consent', true);
    } else if (typeof FullStory !== 'undefined') {
      FullStory('consent', false);
      FullStory('shutdown');
    }

    // Update GTM consent
    if (typeof window !== 'undefined' && window.dataLayer) {
      window.dataLayer.push({
        event: 'consent_update',
        consent: {
          analytics_storage: settings.analytics ? 'granted' : 'denied',
          ad_storage: settings.marketing ? 'granted' : 'denied',
          functionality_storage: 'granted',
          personalization_storage: settings.marketing ? 'granted' : 'denied',
          security_storage: 'granted'
        }
      });
    }

    onConsentUpdate?.(settings);
  };

  const saveConsent = (settings: ConsentSettings) => {
    localStorage.setItem('privacy_consent', JSON.stringify(settings));
    localStorage.setItem('privacy_consent_date', new Date().toISOString());
    applyConsent(settings);
    setIsVisible(false);

    // Track consent (only if analytics allowed)
    if (settings.analytics) {
      analytics.track('privacy_consent_updated', {
        analytics: settings.analytics,
        marketing: settings.marketing,
        recording: settings.recording
      });
    }
  };

  const acceptAll = () => {
    const allConsent = {
      necessary: true,
      analytics: true,
      marketing: true,
      recording: true
    };
    setConsent(allConsent);
    saveConsent(allConsent);
  };

  const rejectAll = () => {
    const minimalConsent = {
      necessary: true,
      analytics: false,
      marketing: false,
      recording: false
    };
    setConsent(minimalConsent);
    saveConsent(minimalConsent);
  };

  if (!isVisible) return null;

  return (
    <>
      {/* Overlay */}
      <div className="fixed inset-0 bg-black/50 z-50" />

      {/* Consent Modal */}
      <div className="fixed inset-x-4 bottom-4 max-w-2xl mx-auto z-50 md:inset-x-auto md:bottom-8">
        <Card className="shadow-2xl">
          <CardContent className="p-6">
            {!isDetailView ? (
              // Simple View
              <div className="space-y-4">
                <div className="flex items-start gap-3">
                  <Shield className="w-6 h-6 text-blue-600 flex-shrink-0 mt-1" />
                  <div className="flex-1">
                    <h3 className="text-lg font-semibold mb-2">
                      Your Privacy Matters to Us
                    </h3>
                    <p className="text-sm text-gray-600">
                      We use cookies and similar technologies to improve your experience,
                      analyze site traffic, and show relevant content. You can customize
                      your preferences or accept all.
                    </p>
                  </div>
                </div>

                <div className="flex flex-col sm:flex-row gap-3">
                  <Button
                    variant="outline"
                    onClick={() => setIsDetailView(true)}
                    className="flex-1"
                  >
                    Manage Preferences
                  </Button>
                  <Button
                    variant="outline"
                    onClick={rejectAll}
                    className="flex-1"
                  >
                    Reject All
                  </Button>
                  <Button
                    onClick={acceptAll}
                    className="flex-1 bg-blue-600 hover:bg-blue-700"
                  >
                    Accept All
                  </Button>
                </div>

                <p className="text-xs text-center text-gray-500">
                  By clicking "Accept All", you agree to our{' '}
                  <a href="/privacy" className="underline">Privacy Policy</a> and{' '}
                  <a href="/cookies" className="underline">Cookie Policy</a>
                </p>
              </div>
            ) : (
              // Detailed View
              <div className="space-y-4">
                <h3 className="text-lg font-semibold">Privacy Preferences</h3>

                <div className="space-y-4">
                  {/* Necessary Cookies */}
                  <div className="flex items-start gap-3 p-3 bg-gray-50 rounded-lg">
                    <Cookie className="w-5 h-5 text-gray-600 mt-0.5" />
                    <div className="flex-1">
                      <div className="flex items-center justify-between mb-1">
                        <h4 className="font-medium">Necessary Cookies</h4>
                        <Switch checked disabled />
                      </div>
                      <p className="text-sm text-gray-600">
                        Essential for the website to function. These cannot be disabled.
                      </p>
                    </div>
                  </div>

                  {/* Analytics Cookies */}
                  <div className="flex items-start gap-3 p-3 rounded-lg border">
                    <BarChart className="w-5 h-5 text-gray-600 mt-0.5" />
                    <div className="flex-1">
                      <div className="flex items-center justify-between mb-1">
                        <h4 className="font-medium">Analytics Cookies</h4>
                        <Switch
                          checked={consent.analytics}
                          onCheckedChange={(checked) =>
                            setConsent({ ...consent, analytics: checked })
                          }
                        />
                      </div>
                      <p className="text-sm text-gray-600">
                        Help us understand how visitors interact with our website
                        to improve user experience.
                      </p>
                      <p className="text-xs text-gray-500 mt-1">
                        Includes: Google Analytics, Mixpanel
                      </p>
                    </div>
                  </div>

                  {/* Marketing Cookies */}
                  <div className="flex items-start gap-3 p-3 rounded-lg border">
                    <Cookie className="w-5 h-5 text-gray-600 mt-0.5" />
                    <div className="flex-1">
                      <div className="flex items-center justify-between mb-1">
                        <h4 className="font-medium">Marketing Cookies</h4>
                        <Switch
                          checked={consent.marketing}
                          onCheckedChange={(checked) =>
                            setConsent({ ...consent, marketing: checked })
                          }
                        />
                      </div>
                      <p className="text-sm text-gray-600">
                        Used to show relevant advertisements and measure campaign effectiveness.
                      </p>
                      <p className="text-xs text-gray-500 mt-1">
                        Includes: Facebook Pixel, Google Ads
                      </p>
                    </div>
                  </div>

                  {/* Session Recording */}
                  <div className="flex items-start gap-3 p-3 rounded-lg border">
                    <Camera className="w-5 h-5 text-gray-600 mt-0.5" />
                    <div className="flex-1">
                      <div className="flex items-center justify-between mb-1">
                        <h4 className="font-medium">Session Recording</h4>
                        <Switch
                          checked={consent.recording}
                          onCheckedChange={(checked) =>
                            setConsent({ ...consent, recording: checked })
                          }
                        />
                      </div>
                      <p className="text-sm text-gray-600">
                        Records your session to help us identify usability issues.
                        Sensitive data is automatically masked.
                      </p>
                      <p className="text-xs text-gray-500 mt-1">
                        Includes: FullStory, Hotjar recordings
                      </p>
                    </div>
                  </div>
                </div>

                <div className="flex gap-3 pt-2">
                  <Button
                    variant="outline"
                    onClick={() => setIsDetailView(false)}
                    className="flex-1"
                  >
                    Back
                  </Button>
                  <Button
                    onClick={() => saveConsent(consent)}
                    className="flex-1 bg-blue-600 hover:bg-blue-700"
                  >
                    Save Preferences
                  </Button>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </>
  );
}

// Privacy controls component for settings page
export function PrivacyControls() {
  const [consent, setConsent] = useState<ConsentSettings | null>(null);
  const [dataRequested, setDataRequested] = useState(false);

  useEffect(() => {
    const stored = localStorage.getItem('privacy_consent');
    if (stored) {
      setConsent(JSON.parse(stored));
    }
  }, []);

  const requestDataExport = async () => {
    try {
      const response = await fetch('/api/v1/privacy/export', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      if (response.ok) {
        setDataRequested(true);
        analytics.track('privacy_data_export_requested');
      }
    } catch (error) {
      console.error('Failed to request data export:', error);
    }
  };

  const requestDataDeletion = async () => {
    if (!confirm('Are you sure? This will permanently delete your account and all associated data.')) {
      return;
    }

    try {
      const response = await fetch('/api/v1/privacy/delete', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      if (response.ok) {
        analytics.track('privacy_data_deletion_requested');
        // Log out and redirect
        window.location.href = '/';
      }
    } catch (error) {
      console.error('Failed to request data deletion:', error);
    }
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardContent className="p-6">
          <h3 className="text-lg font-semibold mb-4">Privacy Settings</h3>

          {consent && (
            <div className="space-y-3">
              <div className="flex items-center justify-between py-2">
                <span>Analytics</span>
                <Switch checked={consent.analytics} disabled />
              </div>
              <div className="flex items-center justify-between py-2">
                <span>Marketing</span>
                <Switch checked={consent.marketing} disabled />
              </div>
              <div className="flex items-center justify-between py-2">
                <span>Session Recording</span>
                <Switch checked={consent.recording} disabled />
              </div>
            </div>
          )}

          <Button
            variant="outline"
            className="mt-4"
            onClick={() => {
              localStorage.removeItem('privacy_consent');
              window.location.reload();
            }}
          >
            Update Consent Preferences
          </Button>
        </CardContent>
      </Card>

      <Card>
        <CardContent className="p-6">
          <h3 className="text-lg font-semibold mb-4">Your Data</h3>

          <div className="space-y-4">
            <div>
              <h4 className="font-medium mb-2">Export Your Data</h4>
              <p className="text-sm text-gray-600 mb-3">
                Download all your personal data in a machine-readable format (JSON).
              </p>
              <Button
                variant="outline"
                onClick={requestDataExport}
                disabled={dataRequested}
              >
                {dataRequested ? 'Export Requested' : 'Request Data Export'}
              </Button>
            </div>

            <div className="pt-4 border-t">
              <h4 className="font-medium mb-2 text-red-600">Delete Your Account</h4>
              <p className="text-sm text-gray-600 mb-3">
                Permanently delete your account and all associated data. This action cannot be undone.
              </p>
              <Button
                variant="destructive"
                onClick={requestDataDeletion}
              >
                Delete My Account
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
```

## Morning: Demo Content Creation (4 hours)

### Task 1: Create Showcase Trips (2 hours)

**File**: `packages/hub/src/scripts/create-demo-trips.ts`

```typescript
import { supabase } from '../config/supabase';
import { v4 as uuidv4 } from 'uuid';

interface DemoTrip {
  title: string;
  description: string;
  destination: string;
  theme: string;
  days: number;
  budget: number;
  highlights: string[];
  activities: DemoActivity[];
}

interface DemoActivity {
  name: string;
  description: string;
  type: ActivityType;
  day: number;
  time: string;
  duration: number; // hours
  location: {
    address: string;
    lat: number;
    lng: number;
  };
  price?: number;
  bookingUrl?: string;
  imageUrl?: string;
  tips?: string[];
}

const DEMO_TRIPS: DemoTrip[] = [
  {
    title: 'Paris & Rome: Classic European Romance',
    description:
      "Experience the timeless elegance of Paris and the ancient wonders of Rome in this perfectly crafted 7-day journey through Europe's most romantic cities.",
    destination: 'Paris & Rome',
    theme: 'romance',
    days: 7,
    budget: 2500,
    highlights: [
      'Sunset at the Eiffel Tower',
      'Private Louvre tour',
      'Colosseum underground access',
      'Authentic cooking class in Rome',
      'Seine river dinner cruise',
    ],
    activities: [
      {
        name: 'Arrival & Eiffel Tower Magic',
        description:
          "Check into the charming Hotel Malte Opera and experience your first Parisian sunset from the Eiffel Tower's summit.",
        type: 'activity',
        day: 1,
        time: '16:00',
        duration: 3,
        location: {
          address: 'Eiffel Tower, Champ de Mars, 5 Avenue Anatole France, 75007 Paris',
          lat: 48.8584,
          lng: 2.2945,
        },
        price: 28,
        bookingUrl: 'https://www.toureiffel.paris/en',
        imageUrl: 'https://images.unsplash.com/photo-1543349689-9a4d426bee8e',
        tips: [
          'Book skip-the-line tickets in advance',
          'Visit at sunset for the best photos',
          'The summit can be windy - bring a jacket',
        ],
      },
      {
        name: 'Louvre Museum VIP Experience',
        description:
          "Skip the lines and explore the world's largest art museum with an expert guide, including exclusive access to lesser-known masterpieces.",
        type: 'activity',
        day: 2,
        time: '09:00',
        duration: 4,
        location: {
          address: 'Louvre Museum, Rue de Rivoli, 75001 Paris',
          lat: 48.8606,
          lng: 2.3376,
        },
        price: 85,
        bookingUrl: 'https://www.louvre.fr/en',
        imageUrl: 'https://images.unsplash.com/photo-1505682614136-0a7b2c4f7c93',
      },
      // Add 15-20 more activities across 7 days...
    ],
  },
  {
    title: 'Tokyo Adventure: Modern Meets Traditional',
    description:
      'Immerse yourself in the fascinating contrasts of Tokyo, from ancient temples to neon-lit streets, in this action-packed 5-day urban adventure.',
    destination: 'Tokyo',
    theme: 'adventure',
    days: 5,
    budget: 1800,
    highlights: [
      'Sunrise at Tsukiji Market',
      'Robot Restaurant show',
      'Traditional tea ceremony',
      'Mount Fuji day trip',
      'Shibuya crossing experience',
    ],
    activities: [
      // Add complete activities...
    ],
  },
  {
    title: 'Bali Wellness Retreat: Restore & Recharge',
    description:
      'Find your inner peace with yoga, meditation, and spa treatments in the tropical paradise of Bali. A transformative 6-day wellness journey.',
    destination: 'Bali',
    theme: 'wellness',
    days: 6,
    budget: 1500,
    highlights: [
      'Daily sunrise yoga',
      'Traditional Balinese spa',
      'Rice terrace meditation',
      'Healthy cooking workshop',
      'Sacred water temple visit',
    ],
    activities: [
      // Add complete activities...
    ],
  },
  {
    title: 'American Road Trip: California Dreaming',
    description:
      'Drive the iconic Pacific Coast Highway from San Francisco to Los Angeles, discovering hidden beaches, charming towns, and breathtaking vistas.',
    destination: 'California Coast',
    theme: 'road-trip',
    days: 8,
    budget: 2200,
    highlights: [
      'Golden Gate Bridge views',
      'Big Sur scenic drive',
      'Wine tasting in Paso Robles',
      'Hearst Castle tour',
      'Santa Monica Pier sunset',
    ],
    activities: [
      // Add complete activities...
    ],
  },
  {
    title: 'Greek Island Hopping: Aegean Paradise',
    description:
      'Sail through the crystal-clear waters of the Aegean, exploring the white-washed villages of Santorini, the beaches of Mykonos, and the history of Athens.',
    destination: 'Greek Islands',
    theme: 'island',
    days: 10,
    budget: 3000,
    highlights: [
      'Santorini sunset in Oia',
      'Mykonos beach clubs',
      'Acropolis guided tour',
      'Traditional Greek cooking',
      'Private catamaran cruise',
    ],
    activities: [
      // Add complete activities...
    ],
  },
];

async function createDemoTrips() {
  console.log('🌟 Creating demo trips...');

  // Create demo user if doesn't exist
  let demoUserId = process.env.DEMO_USER_ID;

  if (!demoUserId) {
    const { data: demoUser, error } = await supabase.auth.admin.createUser({
      email: '<EMAIL>',
      password: 'DemoUser123!',
      email_confirm: true,
      user_metadata: {
        name: 'TravelViz Demo',
        is_demo: true,
      },
    });

    if (error) {
      console.error('Failed to create demo user:', error);
      return;
    }

    demoUserId = demoUser.user.id;
    console.log('Created demo user:', demoUserId);
  }

  // Create each demo trip
  for (const demoTrip of DEMO_TRIPS) {
    console.log(`Creating trip: ${demoTrip.title}`);

    const tripId = uuidv4();
    const startDate = new Date();
    startDate.setDate(startDate.getDate() + 30); // Start 30 days from now

    // Create trip
    const { error: tripError } = await supabase.from('trips').insert({
      id: tripId,
      user_id: demoUserId,
      title: demoTrip.title,
      description: demoTrip.description,
      destination: demoTrip.destination,
      start_date: startDate.toISOString(),
      end_date: new Date(
        startDate.getTime() + (demoTrip.days - 1) * 24 * 60 * 60 * 1000
      ).toISOString(),
      is_public: true,
      published_at: new Date().toISOString(),
      metadata: {
        theme: demoTrip.theme,
        highlights: demoTrip.highlights,
        estimated_budget: demoTrip.budget,
        is_demo: true,
        featured: true,
      },
    });

    if (tripError) {
      console.error(`Failed to create trip ${demoTrip.title}:`, tripError);
      continue;
    }

    // Create activities
    const activities = demoTrip.activities.map((activity, index) => {
      const activityDate = new Date(startDate);
      activityDate.setDate(activityDate.getDate() + activity.day - 1);

      const [hours, minutes] = activity.time.split(':').map(Number);
      activityDate.setHours(hours, minutes, 0, 0);

      return {
        id: uuidv4(),
        trip_id: tripId,
        type: activity.type,
        name: activity.name,
        description: activity.description,
        start_time: activityDate.toISOString(),
        end_time: new Date(
          activityDate.getTime() + activity.duration * 60 * 60 * 1000
        ).toISOString(),
        location: activity.location,
        price: activity.price,
        currency: 'USD',
        booking_url: activity.bookingUrl,
        position: index,
        day_number: activity.day,
        metadata: {
          image_url: activity.imageUrl,
          tips: activity.tips,
        },
      };
    });

    const { error: activitiesError } = await supabase.from('activities').insert(activities);

    if (activitiesError) {
      console.error(`Failed to create activities for ${demoTrip.title}:`, activitiesError);
    } else {
      console.log(`✅ Created ${demoTrip.title} with ${activities.length} activities`);
    }

    // Generate some fake engagement
    await generateDemoEngagement(tripId);
  }

  console.log('✨ Demo trips created successfully!');
}

async function generateDemoEngagement(tripId: string) {
  // Add random views (100-500)
  const views = Math.floor(Math.random() * 400) + 100;

  // Add random copies (10-50)
  const copies = Math.floor(Math.random() * 40) + 10;

  await supabase.from('trip_analytics').insert({
    trip_id: tripId,
    views,
    copies,
    shares: Math.floor(copies * 2.5),
    unique_viewers: Math.floor(views * 0.7),
  });

  console.log(`  📊 Added engagement: ${views} views, ${copies} copies`);
}

// Run the script
if (require.main === module) {
  createDemoTrips()
    .then(() => process.exit(0))
    .catch(error => {
      console.error('Script failed:', error);
      process.exit(1);
    });
}
```

### Task 2: Demo Landing Page (2 hours)

**File**: `packages/web/src/app/demo/page.tsx`

```typescript
'use client';

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Sparkles,
  Map,
  Calendar,
  Users,
  TrendingUp,
  Play,
  ChevronRight,
  Star
} from 'lucide-react';
import { DemoTripCard } from '@/components/demo/DemoTripCard';
import { VideoModal } from '@/components/modals/VideoModal';

export default function DemoPage() {
  const [demoTrips, setDemoTrips] = useState<DemoTrip[]>([]);
  const [showVideo, setShowVideo] = useState(false);
  const [email, setEmail] = useState('');
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchDemoTrips();
  }, []);

  const fetchDemoTrips = async () => {
    try {
      const response = await fetch('/api/v1/trips/demo');
      const data = await response.json();
      setDemoTrips(data.trips);
    } catch (error) {
      console.error('Failed to load demo trips:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleEmailSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Track interest
    await fetch('/api/v1/analytics/track', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        event: 'demo_email_captured',
        properties: { email, source: 'demo_page' }
      })
    });

    // Redirect to import
    window.location.href = '/import';
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50">
      {/* Hero Section */}
      <section className="relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-blue-600/5 to-purple-600/5" />

        <div className="relative container mx-auto px-4 py-16 md:py-24">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="text-center max-w-4xl mx-auto"
          >
            {/* Badge */}
            <motion.div
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              transition={{ delay: 0.2 }}
              className="inline-flex items-center gap-2 px-4 py-2 bg-purple-100 rounded-full text-purple-700 font-medium mb-6"
            >
              <Sparkles className="w-4 h-4" />
              AI-Powered Trip Planning
            </motion.div>

            {/* Headline */}
            <h1 className="text-5xl md:text-6xl font-bold mb-6">
              Turn AI Conversations Into
              <span className="block text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-purple-600">
                Beautiful Travel Itineraries
              </span>
            </h1>

            <p className="text-xl text-gray-600 mb-8">
              Import your ChatGPT, Claude, or Gemini travel plans and watch them transform
              into stunning visual itineraries with maps, timelines, and booking links.
            </p>

            {/* CTA */}
            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
              <Button
                size="lg"
                className="min-w-[200px] bg-gradient-to-r from-blue-600 to-purple-600"
                onClick={() => setShowVideo(true)}
              >
                <Play className="w-4 h-4 mr-2" />
                Watch 2-Min Demo
              </Button>
              <Button
                size="lg"
                variant="outline"
                className="min-w-[200px]"
                onClick={() => window.location.href = '/import'}
              >
                Try It Free
                <ChevronRight className="w-4 h-4 ml-2" />
              </Button>
            </div>

            {/* Social Proof */}
            <div className="flex items-center justify-center gap-8 mt-8 text-sm text-gray-600">
              <div className="flex items-center gap-2">
                <Users className="w-4 h-4" />
                <span>2,847 happy travelers</span>
              </div>
              <div className="flex items-center gap-2">
                <TrendingUp className="w-4 h-4" />
                <span>15,432 trips created</span>
              </div>
              <div className="flex items-center gap-1">
                {[...Array(5)].map((_, i) => (
                  <Star key={i} className="w-4 h-4 text-yellow-500 fill-current" />
                ))}
                <span className="ml-1">4.9/5</span>
              </div>
            </div>
          </motion.div>
        </div>
      </section>

      {/* How It Works */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0 }}
            whileInView={{ opacity: 1 }}
            viewport={{ once: true }}
            className="text-center mb-12"
          >
            <h2 className="text-3xl font-bold mb-4">
              How It Works
            </h2>
            <p className="text-gray-600">
              Three simple steps to transform your AI travel conversations
            </p>
          </motion.div>

          <div className="grid md:grid-cols-3 gap-8 max-w-4xl mx-auto">
            {[
              {
                step: 1,
                title: "Paste Your Conversation",
                description: "Copy your ChatGPT, Claude, or Gemini travel planning chat",
                icon: "📋"
              },
              {
                step: 2,
                title: "AI Extracts Details",
                description: "Our AI identifies dates, locations, and activities automatically",
                icon: "🤖"
              },
              {
                step: 3,
                title: "Get Visual Itinerary",
                description: "See your trip on a timeline and map, ready to share",
                icon: "✨"
              }
            ].map((item, index) => (
              <motion.div
                key={item.step}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
                viewport={{ once: true }}
                className="text-center"
              >
                <div className="text-4xl mb-4">{item.icon}</div>
                <div className="inline-flex items-center justify-center w-8 h-8 bg-blue-600 text-white rounded-full text-sm font-bold mb-3">
                  {item.step}
                </div>
                <h3 className="font-semibold mb-2">{item.title}</h3>
                <p className="text-sm text-gray-600">{item.description}</p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Demo Trips */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0 }}
            whileInView={{ opacity: 1 }}
            viewport={{ once: true }}
            className="text-center mb-12"
          >
            <h2 className="text-3xl font-bold mb-4">
              Explore Demo Itineraries
            </h2>
            <p className="text-gray-600">
              See what's possible with TravelViz
            </p>
          </motion.div>

          {loading ? (
            <div className="flex justify-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600" />
            </div>
          ) : (
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6 max-w-6xl mx-auto">
              {demoTrips.map((trip, index) => (
                <motion.div
                  key={trip.id}
                  initial={{ opacity: 0, scale: 0.9 }}
                  whileInView={{ opacity: 1, scale: 1 }}
                  transition={{ delay: index * 0.1 }}
                  viewport={{ once: true }}
                >
                  <DemoTripCard trip={trip} />
                </motion.div>
              ))}
            </div>
          )}
        </div>
      </section>

      {/* Email Capture */}
      <section className="py-16 bg-gradient-to-r from-blue-600 to-purple-600 text-white">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            className="text-center max-w-2xl mx-auto"
          >
            <h2 className="text-3xl font-bold mb-4">
              Ready to Create Your First Trip?
            </h2>
            <p className="mb-8 opacity-90">
              Import your AI conversation and see the magic happen
            </p>

            <form onSubmit={handleEmailSubmit} className="flex gap-3 max-w-md mx-auto">
              <Input
                type="email"
                placeholder="Enter your email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                required
                className="bg-white/10 border-white/20 text-white placeholder:text-white/60"
              />
              <Button
                type="submit"
                variant="secondary"
                size="lg"
              >
                Get Started
              </Button>
            </form>

            <p className="text-sm mt-4 opacity-75">
              Free to use • No credit card required
            </p>
          </motion.div>
        </div>
      </section>

      {/* Video Modal */}
      {showVideo && (
        <VideoModal
          videoUrl="https://www.youtube.com/embed/YOUR_VIDEO_ID"
          onClose={() => setShowVideo(false)}
        />
      )}
    </div>
  );
}
```

## Afternoon: Documentation & Feedback (4 hours)

### Task 3: FAQ System (2 hours)

**File**: `packages/web/src/app/help/faq/page.tsx`

```typescript
'use client';

import { useState, useMemo } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import {
  Search,
  ChevronDown,
  MessageCircle,
  FileText,
  Sparkles,
  CreditCard,
  Globe,
  Shield,
  Zap
} from 'lucide-react';

interface FAQItem {
  id: string;
  category: string;
  question: string;
  answer: string;
  related?: string[];
  helpful?: number;
  icon: any;
}

const FAQ_DATA: FAQItem[] = [
  {
    id: 'what-is-travelviz',
    category: 'Getting Started',
    question: 'What is TravelViz?',
    answer: 'TravelViz is an AI-powered tool that transforms your travel conversations from ChatGPT, Claude, or Gemini into beautiful visual itineraries. Simply paste your chat, and we'll create an interactive timeline and map of your trip.',
    related: ['how-import-works', 'supported-ai-tools'],
    icon: Sparkles
  },
  {
    id: 'how-import-works',
    category: 'Getting Started',
    question: 'How does the AI import work?',
    answer: 'Our AI analyzes your conversation to extract trip details like dates, locations, activities, and times. It then organizes this information into a structured itinerary with a visual timeline and interactive map. The process takes less than 30 seconds!',
    related: ['supported-ai-tools', 'import-accuracy'],
    icon: FileText
  },
  {
    id: 'supported-ai-tools',
    category: 'Getting Started',
    question: 'Which AI tools are supported?',
    answer: 'We support conversations from ChatGPT (GPT-3.5 and GPT-4), Claude (all versions), Google Gemini, and most other AI assistants. The key is that your conversation contains trip details like dates, locations, and activities.',
    related: ['how-import-works'],
    icon: MessageCircle
  },
  {
    id: 'is-it-free',
    category: 'Pricing',
    question: 'Is TravelViz free to use?',
    answer: 'Yes! TravelViz offers a free plan that includes 3 AI imports per month, basic timeline and map views, and public sharing. For unlimited imports and advanced features, you can upgrade to Pro for $9/month.',
    related: ['pro-features', 'payment-methods'],
    icon: CreditCard
  },
  {
    id: 'pro-features',
    category: 'Pricing',
    question: 'What do I get with Pro?',
    answer: 'Pro includes unlimited AI imports, priority processing, PDF and calendar exports, custom branding removal, advanced customization options, and priority support. You also get early access to new features!',
    related: ['is-it-free', 'cancel-subscription'],
    icon: Zap
  },
  {
    id: 'cancel-subscription',
    category: 'Pricing',
    question: 'Can I cancel my Pro subscription anytime?',
    answer: 'Absolutely! You can cancel your Pro subscription at any time from your account settings. You'll continue to have Pro features until the end of your billing period, and all your trips will remain accessible.',
    related: ['pro-features', 'refund-policy'],
    icon: CreditCard
  },
  {
    id: 'share-trips',
    category: 'Sharing',
    question: 'How do I share my trips?',
    answer: 'Click the "Share" button on any trip to get a unique link. You can make trips public (anyone with the link can view) or keep them private. Public trips can be copied by others to their own accounts.',
    related: ['privacy-settings', 'copy-trips'],
    icon: Globe
  },
  {
    id: 'copy-trips',
    category: 'Sharing',
    question: 'Can others copy my shared trips?',
    answer: 'If you make a trip public and enable copying, anyone can copy it to their account and customize it. You can disable this option in your share settings. When someone copies your trip, you'll see it in your analytics.',
    related: ['share-trips', 'privacy-settings'],
    icon: Globe
  },
  {
    id: 'privacy-settings',
    category: 'Privacy',
    question: 'Is my data private and secure?',
    answer: 'Yes! We take privacy seriously. Your trips are private by default, and we use bank-level encryption. We never share your personal data, and you can delete your account and all data at any time.',
    related: ['share-trips', 'delete-account'],
    icon: Shield
  },
  {
    id: 'import-accuracy',
    category: 'Troubleshooting',
    question: 'Why didn't my import work perfectly?',
    answer: 'AI parsing works best when your conversation includes specific dates, times, and locations. Vague references like "that famous tower" or "sometime in spring" are harder to parse. You can always edit the imported trip to fix any issues.',
    related: ['improve-import', 'edit-trips'],
    icon: MessageCircle
  },
  {
    id: 'improve-import',
    category: 'Troubleshooting',
    question: 'How can I improve import accuracy?',
    answer: 'For best results: 1) Include specific dates and times, 2) Use full location names and addresses, 3) Copy the entire conversation, not just summaries, 4) Include prices if you want budget tracking.',
    related: ['import-accuracy', 'supported-ai-tools'],
    icon: Sparkles
  },
  {
    id: 'affiliate-links',
    category: 'Monetization',
    question: 'What are the booking links in my itinerary?',
    answer: 'We partner with booking platforms to show relevant hotels, flights, and activities. These are affiliate links - we earn a small commission if you book through them, at no extra cost to you. This helps keep TravelViz free!',
    related: ['disable-affiliate', 'booking-partners'],
    icon: CreditCard
  }
];

const CATEGORIES = [
  { name: 'All', icon: FileText },
  { name: 'Getting Started', icon: Sparkles },
  { name: 'Pricing', icon: CreditCard },
  { name: 'Sharing', icon: Globe },
  { name: 'Privacy', icon: Shield },
  { name: 'Troubleshooting', icon: MessageCircle }
];

export default function FAQPage() {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('All');
  const [expandedItems, setExpandedItems] = useState<Set<string>>(new Set());
  const [helpfulItems, setHelpfulItems] = useState<Record<string, boolean>>({});

  const filteredFAQs = useMemo(() => {
    return FAQ_DATA.filter(faq => {
      const matchesSearch = searchQuery === '' ||
        faq.question.toLowerCase().includes(searchQuery.toLowerCase()) ||
        faq.answer.toLowerCase().includes(searchQuery.toLowerCase());

      const matchesCategory = selectedCategory === 'All' ||
        faq.category === selectedCategory;

      return matchesSearch && matchesCategory;
    });
  }, [searchQuery, selectedCategory]);

  const toggleExpanded = (id: string) => {
    const newExpanded = new Set(expandedItems);
    if (newExpanded.has(id)) {
      newExpanded.delete(id);
    } else {
      newExpanded.add(id);
    }
    setExpandedItems(newExpanded);
  };

  const markHelpful = async (id: string, helpful: boolean) => {
    setHelpfulItems({ ...helpfulItems, [id]: helpful });

    // Track feedback
    await fetch('/api/v1/analytics/track', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        event: 'faq_feedback',
        properties: { faqId: id, helpful }
      })
    });
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto px-4 py-12">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center mb-12"
        >
          <h1 className="text-4xl font-bold mb-4">
            Frequently Asked Questions
          </h1>
          <p className="text-gray-600 mb-8">
            Find answers to common questions about TravelViz
          </p>

          {/* Search */}
          <div className="max-w-2xl mx-auto relative">
            <Search className="absolute left-3 top-3 w-5 h-5 text-gray-400" />
            <Input
              type="text"
              placeholder="Search FAQs..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>
        </motion.div>

        {/* Categories */}
        <div className="flex flex-wrap justify-center gap-2 mb-8">
          {CATEGORIES.map((category) => {
            const Icon = category.icon;
            return (
              <Button
                key={category.name}
                variant={selectedCategory === category.name ? 'default' : 'outline'}
                size="sm"
                onClick={() => setSelectedCategory(category.name)}
                className="gap-2"
              >
                <Icon className="w-4 h-4" />
                {category.name}
              </Button>
            );
          })}
        </div>

        {/* FAQ Items */}
        <div className="max-w-4xl mx-auto space-y-4">
          {filteredFAQs.length === 0 ? (
            <div className="text-center py-12">
              <p className="text-gray-500">No FAQs found matching your search.</p>
              <Button
                variant="link"
                onClick={() => {
                  setSearchQuery('');
                  setSelectedCategory('All');
                }}
                className="mt-2"
              >
                Clear filters
              </Button>
            </div>
          ) : (
            filteredFAQs.map((faq, index) => {
              const Icon = faq.icon;
              const isExpanded = expandedItems.has(faq.id);

              return (
                <motion.div
                  key={faq.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.05 }}
                  className="bg-white rounded-lg shadow-sm"
                >
                  <button
                    onClick={() => toggleExpanded(faq.id)}
                    className="w-full p-6 text-left flex items-start gap-4 hover:bg-gray-50 transition-colors"
                  >
                    <Icon className="w-5 h-5 text-gray-600 mt-0.5" />
                    <div className="flex-1">
                      <h3 className="font-medium text-gray-900">
                        {faq.question}
                      </h3>
                      {!isExpanded && (
                        <p className="text-sm text-gray-500 mt-1 line-clamp-2">
                          {faq.answer}
                        </p>
                      )}
                    </div>
                    <motion.div
                      animate={{ rotate: isExpanded ? 180 : 0 }}
                      transition={{ duration: 0.2 }}
                    >
                      <ChevronDown className="w-5 h-5 text-gray-400" />
                    </motion.div>
                  </button>

                  <AnimatePresence>
                    {isExpanded && (
                      <motion.div
                        initial={{ height: 0 }}
                        animate={{ height: 'auto' }}
                        exit={{ height: 0 }}
                        transition={{ duration: 0.2 }}
                        className="overflow-hidden"
                      >
                        <div className="px-6 pb-6">
                          <p className="text-gray-700 whitespace-pre-wrap">
                            {faq.answer}
                          </p>

                          {/* Related FAQs */}
                          {faq.related && faq.related.length > 0 && (
                            <div className="mt-4 pt-4 border-t">
                              <p className="text-sm font-medium text-gray-600 mb-2">
                                Related questions:
                              </p>
                              <div className="flex flex-wrap gap-2">
                                {faq.related.map(relatedId => {
                                  const relatedFaq = FAQ_DATA.find(f => f.id === relatedId);
                                  if (!relatedFaq) return null;

                                  return (
                                    <Button
                                      key={relatedId}
                                      variant="outline"
                                      size="sm"
                                      onClick={() => {
                                        setExpandedItems(new Set([relatedId]));
                                        document.getElementById(relatedId)?.scrollIntoView({
                                          behavior: 'smooth',
                                          block: 'center'
                                        });
                                      }}
                                    >
                                      {relatedFaq.question}
                                    </Button>
                                  );
                                })}
                              </div>
                            </div>
                          )}

                          {/* Helpful? */}
                          <div className="mt-4 pt-4 border-t flex items-center justify-between">
                            <p className="text-sm text-gray-600">
                              Was this helpful?
                            </p>
                            <div className="flex gap-2">
                              <Button
                                variant={helpfulItems[faq.id] === true ? 'default' : 'outline'}
                                size="sm"
                                onClick={() => markHelpful(faq.id, true)}
                              >
                                Yes
                              </Button>
                              <Button
                                variant={helpfulItems[faq.id] === false ? 'default' : 'outline'}
                                size="sm"
                                onClick={() => markHelpful(faq.id, false)}
                              >
                                No
                              </Button>
                            </div>
                          </div>
                        </div>
                      </motion.div>
                    )}
                  </AnimatePresence>
                </motion.div>
              );
            })
          )}
        </div>

        {/* Still need help? */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.5 }}
          className="text-center mt-12 p-8 bg-white rounded-lg shadow-sm max-w-2xl mx-auto"
        >
          <h2 className="text-xl font-semibold mb-2">
            Still have questions?
          </h2>
          <p className="text-gray-600 mb-4">
            We're here to help! Reach out to our support team.
          </p>
          <Button onClick={() => window.location.href = '/help/contact'}>
            <MessageCircle className="w-4 h-4 mr-2" />
            Contact Support
          </Button>
        </motion.div>
      </div>
    </div>
  );
}
```

### Task 4: Feedback System (2 hours)

**File**: `packages/web/src/components/feedback/FeedbackWidget.tsx`

```typescript
'use client';

import { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Label } from '@/components/ui/label';
import {
  MessageSquare,
  X,
  Send,
  Bug,
  Lightbulb,
  Heart,
  AlertCircle
} from 'lucide-react';
import { toast } from '@/components/ui/use-toast';
import { useAuth } from '@/contexts/AuthContext';

type FeedbackType = 'bug' | 'feature' | 'praise' | 'other';

interface FeedbackData {
  type: FeedbackType;
  message: string;
  email?: string;
  metadata?: {
    page: string;
    userAgent: string;
    timestamp: string;
  };
}

export function FeedbackWidget() {
  const { user } = useAuth();
  const [isOpen, setIsOpen] = useState(false);
  const [feedbackType, setFeedbackType] = useState<FeedbackType>('feature');
  const [message, setMessage] = useState('');
  const [email, setEmail] = useState(user?.email || '');
  const [submitting, setSubmitting] = useState(false);
  const [submitted, setSubmitted] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!message.trim()) {
      toast({
        title: 'Please enter your feedback',
        variant: 'destructive'
      });
      return;
    }

    setSubmitting(true);

    try {
      const feedbackData: FeedbackData = {
        type: feedbackType,
        message,
        email: email || undefined,
        metadata: {
          page: window.location.pathname,
          userAgent: navigator.userAgent,
          timestamp: new Date().toISOString()
        }
      };

      const response = await fetch('/api/v1/feedback', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          ...(user && { 'Authorization': `Bearer ${localStorage.getItem('token')}` })
        },
        body: JSON.stringify(feedbackData)
      });

      if (response.ok) {
        setSubmitted(true);

        // Reset after delay
        setTimeout(() => {
          setIsOpen(false);
          setSubmitted(false);
          setMessage('');
          setFeedbackType('feature');
        }, 3000);
      } else {
        throw new Error('Failed to submit feedback');
      }
    } catch (error) {
      toast({
        title: 'Failed to send feedback',
        description: 'Please try again later',
        variant: 'destructive'
      });
    } finally {
      setSubmitting(false);
    }
  };

  const feedbackTypes = [
    { value: 'bug', label: 'Report a Bug', icon: Bug, color: 'text-red-600' },
    { value: 'feature', label: 'Feature Request', icon: Lightbulb, color: 'text-yellow-600' },
    { value: 'praise', label: 'Share Praise', icon: Heart, color: 'text-pink-600' },
    { value: 'other', label: 'Other', icon: AlertCircle, color: 'text-gray-600' }
  ];

  return (
    <>
      {/* Floating Button */}
      <motion.button
        initial={{ scale: 0 }}
        animate={{ scale: 1 }}
        whileHover={{ scale: 1.1 }}
        whileTap={{ scale: 0.9 }}
        onClick={() => setIsOpen(true)}
        className="fixed bottom-6 right-6 w-14 h-14 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-full shadow-lg flex items-center justify-center z-40"
      >
        <MessageSquare className="w-6 h-6" />
      </motion.button>

      {/* Feedback Modal */}
      <AnimatePresence>
        {isOpen && (
          <>
            {/* Backdrop */}
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              onClick={() => setIsOpen(false)}
              className="fixed inset-0 bg-black/50 z-40"
            />

            {/* Modal */}
            <motion.div
              initial={{ opacity: 0, scale: 0.9, y: 20 }}
              animate={{ opacity: 1, scale: 1, y: 0 }}
              exit={{ opacity: 0, scale: 0.9, y: 20 }}
              className="fixed bottom-24 right-6 w-96 bg-white rounded-lg shadow-xl z-50"
            >
              <div className="p-6">
                {/* Header */}
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-semibold">Send Feedback</h3>
                  <button
                    onClick={() => setIsOpen(false)}
                    className="text-gray-400 hover:text-gray-600"
                  >
                    <X className="w-5 h-5" />
                  </button>
                </div>

                {submitted ? (
                  // Success State
                  <motion.div
                    initial={{ opacity: 0, scale: 0.8 }}
                    animate={{ opacity: 1, scale: 1 }}
                    className="text-center py-8"
                  >
                    <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                      <Send className="w-8 h-8 text-green-600" />
                    </div>
                    <h4 className="font-semibold mb-2">Thank you!</h4>
                    <p className="text-sm text-gray-600">
                      Your feedback helps us improve TravelViz
                    </p>
                  </motion.div>
                ) : (
                  // Form
                  <form onSubmit={handleSubmit} className="space-y-4">
                    {/* Feedback Type */}
                    <div>
                      <Label className="text-sm font-medium mb-2">
                        What's on your mind?
                      </Label>
                      <RadioGroup
                        value={feedbackType}
                        onValueChange={(value) => setFeedbackType(value as FeedbackType)}
                        className="grid grid-cols-2 gap-2"
                      >
                        {feedbackTypes.map((type) => {
                          const Icon = type.icon;
                          return (
                            <div key={type.value}>
                              <RadioGroupItem
                                value={type.value}
                                id={type.value}
                                className="peer sr-only"
                              />
                              <Label
                                htmlFor={type.value}
                                className="flex items-center gap-2 p-3 border rounded-lg cursor-pointer hover:bg-gray-50 peer-checked:border-blue-600 peer-checked:bg-blue-50"
                              >
                                <Icon className={`w-4 h-4 ${type.color}`} />
                                <span className="text-sm">{type.label}</span>
                              </Label>
                            </div>
                          );
                        })}
                      </RadioGroup>
                    </div>

                    {/* Message */}
                    <div>
                      <Label htmlFor="message" className="text-sm font-medium mb-2">
                        Your feedback
                      </Label>
                      <Textarea
                        id="message"
                        value={message}
                        onChange={(e) => setMessage(e.target.value)}
                        placeholder={
                          feedbackType === 'bug'
                            ? "Describe the issue you're experiencing..."
                            : feedbackType === 'feature'
                            ? "What feature would you like to see?"
                            : feedbackType === 'praise'
                            ? "What do you love about TravelViz?"
                            : "Share your thoughts..."
                        }
                        rows={4}
                        required
                      />
                    </div>

                    {/* Email (if not logged in) */}
                    {!user && (
                      <div>
                        <Label htmlFor="email" className="text-sm font-medium mb-2">
                          Email (optional)
                        </Label>
                        <Input
                          id="email"
                          type="email"
                          value={email}
                          onChange={(e) => setEmail(e.target.value)}
                          placeholder="<EMAIL>"
                        />
                        <p className="text-xs text-gray-500 mt-1">
                          Include if you'd like a response
                        </p>
                      </div>
                    )}

                    {/* Submit */}
                    <Button
                      type="submit"
                      className="w-full"
                      disabled={submitting}
                    >
                      {submitting ? (
                        <>
                          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                          Sending...
                        </>
                      ) : (
                        <>
                          <Send className="w-4 h-4 mr-2" />
                          Send Feedback
                        </>
                      )}
                    </Button>
                  </form>
                )}
              </div>
            </motion.div>
          </>
        )}
      </AnimatePresence>
    </>
  );
}

// Feedback Dashboard for Admin
export function FeedbackDashboard() {
  // Admin view to see all feedback
  // Implement sorting, filtering, and response functionality
}
```

## Testing Checklist

### Demo Content

- [ ] 5 showcase trips created
- [ ] Each trip has 15+ activities
- [ ] All activity types represented
- [ ] Booking links work
- [ ] Maps display correctly
- [ ] Timelines are beautiful

### Demo Video

- [ ] 2-minute video recorded
- [ ] Shows full import flow
- [ ] Highlights key features
- [ ] Includes call-to-action
- [ ] Uploaded to YouTube/Vimeo

### FAQ System

- [ ] Covers common questions
- [ ] Search works properly
- [ ] Categories filter correctly
- [ ] Related questions link
- [ ] Feedback tracking works

### Feedback Widget

- [ ] Appears on all pages
- [ ] Submits successfully
- [ ] Email notifications work
- [ ] Admin can view feedback
- [ ] Response time <2 hours

## Extended Thinking Prompts

For demo content:

```
Target user persona: [describe]
Their travel style: [preferences]
Pain points to address: [list]
What demo would make them say "wow"?
```

## Definition of Done

✅ Demo Content Ready:

- 5 stunning trips live
- Each showcases different use case
- Public links work
- Copy feature demonstrated

✅ Support Systems:

- FAQ answers 90% of questions
- Feedback widget on all pages
- Demo video compelling
- Support response fast

## Next Days Preview

Days 13-14: Scale Preparation

- Monitoring setup
- Performance testing
- Cache optimization
- Error tracking

## Notes

- First impressions are everything
- Demo content must inspire
- FAQs reduce support burden
- Feedback drives product direction
- Make it easy to get help
- Show don't tell in demos
