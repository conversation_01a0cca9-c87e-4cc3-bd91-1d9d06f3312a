import { describe, it, expect, beforeEach, vi } from 'vitest';
import { PDFParserService } from './pdf-parser.service';

// Mock dynamic import of pdf-parse
const mockPdfParse = vi.fn((buffer) => {
  // Check if it's a valid PDF header
  const header = buffer.toString('utf8', 0, 5);
  if (header !== '%PDF-') {
    throw new Error('Invalid PDF structure');
  }
  
  // Return mock data based on buffer content
  const content = buffer.toString();
  if (content.includes('Day 1: London')) {
    return Promise.resolve({
      numpages: 1,
      text: `Day 1: London
Morning: Visit Tower Bridge
Afternoon: British Museum
Evening: West End Show`
    });
  }
  
  // Default empty PDF
  return Promise.resolve({
    numpages: 0,
    text: ''
  });
});

// Mock the dynamic import
vi.mock('pdf-parse', () => ({
  __esModule: true,
  default: mockPdfParse
}));

describe('PDFParserService', () => {
  let pdfParser: PDFParserService;

  beforeEach(() => {
    pdfParser = new PDFParserService();
  });

  describe('extractText', () => {
    it('should extract text from a valid PDF buffer', async () => {
      // Create a test PDF with some travel content
      const pdfContent = `%PDF-1.4
1 0 obj
<< /Type /Catalog /Pages 2 0 R >>
endobj
2 0 obj
<< /Type /Pages /Kids [3 0 R] /Count 1 >>
endobj
3 0 obj
<< /Type /Page /Parent 2 0 R /Resources << /Font << /F1 4 0 R >> >> /MediaBox [0 0 612 792] /Contents 5 0 R >>
endobj
4 0 obj
<< /Type /Font /Subtype /Type1 /BaseFont /Helvetica >>
endobj
5 0 obj
<< /Length 200 >>
stream
BT
/F1 12 Tf
50 750 Td
(Day 1: London) Tj
0 -20 Td
(Morning: Visit Tower Bridge) Tj
0 -20 Td
(Afternoon: British Museum) Tj
0 -20 Td
(Evening: West End Show) Tj
ET
endstream
endobj
xref
0 6
0000000000 65535 f
0000000009 00000 n
0000000058 00000 n
0000000115 00000 n
0000000262 00000 n
0000000341 00000 n
trailer
<< /Size 6 /Root 1 0 R >>
startxref
591
%%EOF`;

      const pdfBuffer = Buffer.from(pdfContent);
      const extractedText = await pdfParser.extractText(pdfBuffer);

      expect(extractedText).toBeDefined();
      expect(extractedText).toContain('Day 1: London');
      expect(extractedText).toContain('Morning: Visit Tower Bridge');
      expect(extractedText).toContain('British Museum');
    });

    it('should handle empty PDF', async () => {
      const emptyPdf = Buffer.from('%PDF-1.4\n%%EOF');
      const extractedText = await pdfParser.extractText(emptyPdf);

      expect(extractedText).toBe('');
    });

    it('should throw error for invalid PDF', async () => {
      const invalidPdf = Buffer.from('This is not a PDF');

      await expect(pdfParser.extractText(invalidPdf)).rejects.toThrow('Invalid PDF');
    });

    it('should handle multi-page PDFs', async () => {
      // Mock pdf-parse to return multi-page content
      const pdfParse = await import('pdf-parse');
      (pdfParse.default as any).mockImplementationOnce(() => Promise.resolve({
        numpages: 3,
        text: `Day 1: London
Morning: Tower Bridge
Afternoon: British Museum

Day 2: Paris
Morning: Eiffel Tower
Afternoon: Louvre Museum

Day 3: Rome
Morning: Colosseum
Afternoon: Vatican City`
      }));

      const validPdf = Buffer.from('%PDF-1.4\n%%EOF');
      const result = await pdfParser.extractText(validPdf);
      
      expect(result).toContain('Day 1: London');
      expect(result).toContain('Day 2: Paris');
      expect(result).toContain('Day 3: Rome');
    });

    it('should clean and format extracted text', async () => {
      // Mock pdf-parse to return messy text
      const pdfParse = await import('pdf-parse');
      (pdfParse.default as any).mockImplementationOnce(() => Promise.resolve({
        numpages: 1,
        text: `Day    1:    London


      Morning:     Tower    Bridge
      
      
      Afternoon:    British     Museum`
      }));

      const validPdf = Buffer.from('%PDF-1.4\n%%EOF');
      const result = await pdfParser.extractText(validPdf);
      
      // Should clean up extra whitespace
      expect(result).not.toMatch(/\s{3,}/); // No triple+ spaces
      expect(result).not.toMatch(/\n{3,}/); // No triple+ newlines
    });
  });

  describe('validatePDF', () => {
    it('should return true for valid PDF buffer', () => {
      const validPdf = Buffer.from('%PDF-1.4\nsome content\n%%EOF');
      expect(pdfParser.validatePDF(validPdf)).toBe(true);
    });

    it('should return false for non-PDF buffer', () => {
      const invalidPdf = Buffer.from('Not a PDF file');
      expect(pdfParser.validatePDF(invalidPdf)).toBe(false);
    });

    it('should return false for empty buffer', () => {
      const emptyBuffer = Buffer.alloc(0);
      expect(pdfParser.validatePDF(emptyBuffer)).toBe(false);
    });
  });

  describe('extractTravelItinerary', () => {
    it('should extract structured itinerary from PDF text', async () => {
      const travelText = `
        15-Day European Travel Itinerary
        
        Day 1-3: London, UK
        - Day 1: Arrival, Tower Bridge, Tower of London
        - Day 2: British Museum, Buckingham Palace, West End show
        - Day 3: Day trip to Windsor Castle
        
        Day 4-6: Paris, France
        - Day 4: Eurostar to Paris, Eiffel Tower
        - Day 5: Louvre Museum, Notre-Dame Cathedral
        - Day 6: Versailles day trip
        
        Day 7-9: Barcelona, Spain
        - Day 7: Flight to Barcelona, Las Ramblas
        - Day 8: Sagrada Familia, Park Güell
        - Day 9: Beach day at Barceloneta
      `;

      // Mock pdf-parse to return travel text
      const pdfParse = await import('pdf-parse');
      (pdfParse.default as any).mockImplementationOnce(() => Promise.resolve({
        numpages: 1,
        text: travelText
      }));

      const validPdf = Buffer.from('%PDF-1.4\n%%EOF');
      const itinerary = await pdfParser.extractTravelItinerary(validPdf);

      expect(itinerary).toHaveProperty('title');
      expect(itinerary.title).toContain('European Travel Itinerary');
      expect(itinerary).toHaveProperty('days');
      expect(itinerary.days).toBeGreaterThan(0);
      expect(itinerary).toHaveProperty('locations');
      expect(itinerary.locations).toContain('London');
      expect(itinerary.locations).toContain('Paris');
      expect(itinerary.locations).toContain('Barcelona');
    });
  });
});