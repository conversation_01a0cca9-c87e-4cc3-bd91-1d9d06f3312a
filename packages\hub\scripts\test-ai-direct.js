#!/usr/bin/env node

const { config } = require('dotenv');
const { resolve } = require('path');
const axios = require('axios');

// Load environment variables
config({ path: resolve(__dirname, '../.env.local') });

const OPENROUTER_API_KEY = process.env.OPENROUTER_TEST_API_KEY || process.env.OPENROUTER_API_KEY;

if (!OPENROUTER_API_KEY) {
  console.error('❌ No OpenRouter API key found');
  process.exit(1);
}

console.log('🔑 Using API Key:', OPENROUTER_API_KEY.substring(0, 15) + '...');

async function testDirectAPI() {
  console.log('\n📡 Testing Direct OpenRouter API Call...\n');

  const testPrompt = `Extract structured trip data from this conversation:

User: I need a 3-day Tokyo trip.
Assistant: Here's your Tokyo itinerary:
Day 1: Arrive at Narita, check into Shinjuku hotel, visit Senso-ji Temple
Day 2: TeamLab museum, Shibuya crossing, Tokyo Tower
Day 3: Meiji Shrine, shopping in Ginza, depart

Return JSON with title, destination, and activities array.`;

  try {
    console.log('🚀 Making API request to OpenRouter...');
    const response = await axios.post(
      'https://openrouter.ai/api/v1/chat/completions',
      {
        model: 'anthropic/claude-3-haiku',
        messages: [
          {
            role: 'user',
            content: testPrompt
          }
        ],
        temperature: 0.3,
        max_tokens: 2000
      },
      {
        headers: {
          'Authorization': `Bearer ${OPENROUTER_API_KEY}`,
          'Content-Type': 'application/json',
          'HTTP-Referer': 'https://travelviz.app',
          'X-Title': 'TravelViz Test'
        },
        timeout: 30000
      }
    );

    console.log('✅ API Response received!');
    console.log('📊 Usage:', response.data.usage);
    
    const content = response.data.choices[0]?.message?.content;
    if (content) {
      console.log('\n📝 Raw Response:');
      console.log(content);
      
      // Try to parse as JSON
      try {
        const jsonMatch = content.match(/\{[\s\S]*\}/);
        if (jsonMatch) {
          const parsed = JSON.parse(jsonMatch[0]);
          console.log('\n✨ Parsed JSON:');
          console.log(JSON.stringify(parsed, null, 2));
          
          console.log('\n📈 Statistics:');
          console.log(`- Title: ${parsed.title || 'N/A'}`);
          console.log(`- Destination: ${parsed.destination || 'N/A'}`);
          console.log(`- Activities: ${parsed.activities?.length || 0}`);
        }
      } catch (e) {
        console.log('⚠️  Could not parse JSON from response');
      }
    }

  } catch (error) {
    console.error('\n❌ API Error:', error.response?.data || error.message);
    if (error.response?.status === 401) {
      console.error('🔐 Authentication failed - check your API key');
    } else if (error.response?.status === 429) {
      console.error('⏱️  Rate limit exceeded');
    }
  }
}

// Run the test
testDirectAPI().then(() => {
  console.log('\n✅ Test completed');
}).catch(err => {
  console.error('\n❌ Test failed:', err);
  process.exit(1);
});