# Phase Analysis: Implementation Status & Requirements

This document provides a detailed analysis of the current implementation status against the PROJECT_PHASES.md requirements, identifying what needs to be added for each phase.

## Phase 1: Core Experience - Building a Usable Foundation

### Epic 1.1: Interactive Map & Timeline

#### Current Status

- ✅ **Map Component**: TripMap component exists with Mapbox integration
- ✅ **Timeline Component**: TripTimeline component displays activities by day
- ⚠️ **Mapbox Token**: Configured but using placeholder value
- ❌ **Drag-and-Drop**: Not implemented
- ❌ **Activity Reordering**: No backend support

#### What Needs to Be Added

1. **Database Migration**

   ```sql
   ALTER TABLE activities ADD COLUMN position INTEGER;
   ```

   - Add position column to activities table
   - Update existing activities with initial position values based on start_time

2. **Install Dependencies**

   ```bash
   cd packages/web
   pnpm add @dnd-kit/core @dnd-kit/sortable @dnd-kit/utilities
   ```

3. **Backend API Endpoint**
   - Create `PATCH /api/trips/:tripId/activities/reorder`
   - Request body: `{ "orderedIds": ["activity_id_3", "activity_id_1", "activity_id_2"] }`
   - Update position field for each activity

4. **Frontend Implementation**
   - Refactor TripTimeline.tsx to use DndContext and SortableContext
   - Create SortableActivityItem component
   - Implement onDragEnd handler with optimistic updates
   - Add visual feedback (lifting animation, drop shadow, ghost element)

5. **Map-Timeline Sync**
   - Update useTripStore to handle reordering
   - Ensure TripMap re-renders pins with updated numbering
   - Implement smooth pan/zoom to selected activity

6. **Error Handling**
   - Revert optimistic updates on failure
   - Display toast notifications for errors

### Epic 1.2: UX Polish for Core Interactions

#### What Needs to Be Added

1. **Drag Visual States**
   - Lifting animation with transform and scale
   - Drop shadow on dragged item
   - Ghost/placeholder element showing drop position
   - Smooth transitions

2. **Map Interactions**
   - Click activity in timeline → map flyTo animation
   - Selected pin visual state (larger, different color)
   - Hover states for both timeline and map items

## Phase 2: The "Magic" Input - Effortless Activity Creation

### Epic 2.1: AI-Powered Place Search

#### Current Status

- ❌ **One-Box Input**: Not implemented (using multi-field modal)
- ❌ **Place Search**: No autocomplete functionality
- ❌ **Google Places API**: Not integrated

#### What Needs to Be Added

1. **Backend Endpoints**

   a. **Places Autocomplete Endpoint**

   ```typescript
   GET /api/places/autocomplete?query=[search_term]
   Response: {
     suggestions: [{
       description: "Eiffel Tower, Paris, France",
       place_id: "ChIJ..."
     }]
   }
   ```

   - Integrate Google Places Autocomplete API
   - Implement rate limiting
   - Add caching layer

   b. **Add from Place Endpoint**

   ```typescript
   POST /api/activities/add-from-place
   Body: { place_id: "ChIJ...", tripId: "..." }
   Response: {
     id: "...",
     title: "Eiffel Tower",
     location: "Champ de Mars, 5 Avenue Anatole France, 75007 Paris",
     location_lat: 48.8584,
     location_lng: 2.2945,
     type: "attraction",
     // ... other activity fields
   }
   ```

   - Call Google Places Details API
   - Extract relevant information
   - Create activity with enriched data

2. **Frontend Component**

   Create `AddActivityInput.tsx`:

   ```typescript
   - Single input field with search icon
   - Debounced search (300ms)
   - Dropdown with place suggestions
   - Loading states
   - Keyboard navigation support
   - Mobile-optimized
   ```

3. **Integration**
   - Replace "Add Activity" button with new input
   - Update useTripStore for optimistic updates
   - Show success animation on add

### Epic 2.2: Seamless AI Onboarding

#### Current Status

- ❌ **AI Model Selection**: Still visible in upload flow
- ❌ **Backend AI Router**: Not implemented

#### What Needs to Be Added

1. **Remove AI Model Selection**
   - Remove model dropdown from upload/page.tsx
   - Remove model selection from any other UI components

2. **Backend AI Router Service**

   ```typescript
   // services/aiRouter.ts
   class AIRouter {
     selectModel(task: 'itinerary' | 'parse' | 'suggest') {
       // Logic to select appropriate model
       // Start with simple defaults
       // Future: load balancing, cost optimization
     }
   }
   ```

3. **Update Import Flow**
   - Use AI router instead of user-selected model
   - Hide technical details from users

## Phase 3: Visual Overhaul & First-Time Experience

### Epic 3.1: Modern Three-Column Layout

#### Current Status

- ⚠️ **Layout**: Using tabs instead of three columns
- ✅ **Visual Design**: Modern UI with Tailwind/shadcn

#### What Needs to Be Added

1. **Three-Column Layout Component**

   ```typescript
   // components/TripPlanLayout.tsx
   - Left: Collapsible day selector (w-64)
   - Center: Timeline with activities (flex-1)
   - Right: Map view (w-96 lg:w-1/3)
   - Responsive breakpoints
   - Collapsible panels for mobile
   ```

2. **Day Selector Sidebar**
   - List of trip days with counts
   - Current day highlighting
   - Quick navigation between days
   - Add day functionality

3. **Layout Polish**
   - Increased whitespace
   - Simplified activity cards
   - Progressive disclosure patterns
   - Subtle animations

### Epic 3.2: Onboarding & Empty States

#### Current Status

- ✅ **Templates**: 30 AI templates available
- ✅ **Empty States**: Well-designed empty states
- ❌ **Onboarding Flow**: Not implemented

#### What Needs to Be Added

1. **Onboarding Flow**

   ```typescript
   // components/onboarding/
   -WelcomeModal.tsx - FeatureTour.tsx - FirstTripGuide.tsx;
   ```

   Features:
   - First-time user detection
   - Interactive feature highlights
   - "Try with a template" prompt
   - Skip option
   - Progress indicators

2. **Guided First Trip**
   - Tooltip prompts for key actions
   - Highlight import options
   - Show Magic Input capabilities
   - Celebrate first activity added

3. **Loading States Enhancement**
   - AI generation progress indicators
   - Fun facts or tips during loading
   - Estimated time remaining

## Implementation Priority

### High Priority (Week 1-2)

1. **Drag-and-Drop Timeline** (Phase 1.1)
   - Database migration for position
   - DnD implementation
   - Reorder API endpoint

2. **Magic Input** (Phase 2.1)
   - Places API integration
   - One-box component
   - Backend endpoints

### Medium Priority (Week 3-4)

3. **Three-Column Layout** (Phase 3.1)
   - Layout refactor
   - Day selector
   - Responsive design

4. **AI Router** (Phase 2.2)
   - Remove model selection
   - Backend router service

### Lower Priority (Week 5+)

5. **Onboarding Flow** (Phase 3.2)
   - Welcome experience
   - Feature tour
   - Guided actions

6. **Polish & Animations** (Phase 1.2 & 3.1)
   - Interaction states
   - Smooth transitions
   - Visual feedback

## Technical Requirements

### Environment Variables Needed

```bash
# packages/web/.env.local
NEXT_PUBLIC_MAPBOX_ACCESS_TOKEN=<valid_token>
NEXT_PUBLIC_GOOGLE_PLACES_API_KEY=<valid_key>

# packages/hub/.env.local
GOOGLE_PLACES_API_KEY=<valid_key>
```

### Dependencies to Add

```json
{
  "@dnd-kit/core": "^6.x",
  "@dnd-kit/sortable": "^8.x",
  "@dnd-kit/utilities": "^3.x",
  "@googlemaps/google-maps-services-js": "^3.x"
}
```

### Testing Requirements

- Unit tests for reordering logic
- Integration tests for new endpoints
- E2E tests for drag-and-drop
- E2E tests for Magic Input flow

## Success Metrics

- Phase 1: User can reorder activities in < 2 seconds
- Phase 2: Add activity in < 5 seconds with one input
- Phase 3: New user creates first trip in < 3 minutes

## Phase 0: TDD Workflow Enforcement - Quality Foundation

### Goal

Implement an automated Test-Driven Development workflow that prevents bugs and ensures code quality through Claude Code hooks and structured prompting.

### Current Status

- ❌ **TDD Enforcement**: No automated test-first workflow
- ❌ **Hook Configuration**: No Claude Code hooks configured
- ❌ **Prompt Templates**: No standardized AI prompts for TDD
- ✅ **Testing Infrastructure**: Jest/Vitest ready but underutilized

### What Needs to Be Added

#### 1. Claude Code Hook Configuration

Create `.claude/hooks/config.json`:

```json
{
  "hooks": {
    "preToolUse": {
      "edit": "./hooks/pre-edit.js",
      "write": "./hooks/pre-write.js"
    },
    "postToolUse": {
      "edit": "./hooks/post-edit.js",
      "write": "./hooks/post-write.js"
    }
  }
}
```

#### 2. Pre-Edit Hook (RED Phase Enforcement)

`.claude/hooks/pre-edit.js`:

```javascript
// Validates tests exist before allowing implementation changes
module.exports = async context => {
  const { filePath } = context;

  // Skip for test files themselves
  if (filePath.includes('.test.') || filePath.includes('.spec.')) {
    return { allow: true };
  }

  // Check for corresponding test file
  const testPath = deriveTestPath(filePath);
  if (!(await fileExists(testPath))) {
    return {
      allow: false,
      message:
        `❌ TDD Violation: No test file found at ${testPath}\n` +
        `Please create failing tests first.`,
    };
  }

  // Verify tests are failing (RED phase)
  const testResults = await runTests(testPath);
  if (testResults.passing > 0 && testResults.failing === 0) {
    return {
      allow: false,
      message:
        `⚠️ TDD Warning: All tests are passing.\n` +
        `In TDD, write failing tests first (RED phase).`,
    };
  }

  return { allow: true };
};
```

#### 3. Post-Edit Hook (GREEN/REFACTOR Phase Automation)

`.claude/hooks/post-edit.js`:

```javascript
// Automatically runs quality checks after code changes
module.exports = async context => {
  const { filePath } = context;

  // Format code
  await exec(`pnpm prettier --write ${filePath}`);

  // Lint code
  const lintResult = await exec(`pnpm eslint ${filePath}`);
  if (lintResult.exitCode !== 0) {
    console.warn(`⚠️ Linting issues found in ${filePath}`);
  }

  // Type check
  const typeResult = await exec(`pnpm tsc --noEmit ${filePath}`);
  if (typeResult.exitCode !== 0) {
    console.error(`❌ Type errors in ${filePath}`);
  }

  // Run related tests
  const testPath = deriveTestPath(filePath);
  if (await fileExists(testPath)) {
    const testResult = await exec(`pnpm test ${testPath}`);
    console.log(testResult.passing ? '✅ Tests passing' : '❌ Tests failing');
  }

  // Security scan
  await exec(`pnpm audit`);
};
```

#### 4. TDD Prompt Templates

Create `PROMPT_TEMPLATES.md`:

```markdown
# TDD Prompt Templates

## RED Phase - Write Failing Tests

<context>
Project: TravelViz
Feature: [FEATURE_NAME]
Component: [COMPONENT_PATH]
Testing Framework: Vitest + Testing Library
</context>

<requirements>
1. [REQUIREMENT_1]
2. [REQUIREMENT_2]
3. [REQUIREMENT_3]
</requirements>

<task>
Write comprehensive failing tests for the requirements above.
- Tests must fail initially (RED phase)
- Use descriptive test names following "should [expected behavior] when [condition]"
- Include edge cases and error scenarios
- Mock external dependencies
</task>

## GREEN Phase - Minimal Implementation

<context>
Failing tests have been written for [FEATURE_NAME].
Now implement the minimal code to make tests pass.
</context>

<constraints>
- Write ONLY enough code to pass the tests
- Do not add features not covered by tests
- Keep functions under 20 lines
- All functions must have JSDoc comments
</constraints>

## REFACTOR Phase - Optimize Quality

<context>
All tests are passing for [FEATURE_NAME].
Now refactor for quality without changing behavior.
</context>

<checklist>
- [ ] Remove code duplication
- [ ] Improve naming clarity
- [ ] Extract complex logic to separate functions
- [ ] Add performance optimizations if needed
- [ ] Ensure accessibility compliance
- [ ] Verify no tests break during refactoring
</checklist>
```

#### 5. Integration with Existing Workflow

Update package.json scripts:

```json
{
  "scripts": {
    "test:watch": "vitest watch",
    "test:coverage": "vitest run --coverage",
    "tdd": "concurrently \"pnpm test:watch\" \"pnpm dev\"",
    "pre-commit": "lint-staged && pnpm test:coverage"
  }
}
```

Configure lint-staged for TDD:

```json
{
  "*.{ts,tsx}": ["eslint --fix", "prettier --write", "vitest related --run"]
}
```

### Implementation Timeline

#### Week 0 (Before Phase 1)

1. Set up Claude Code hooks
2. Create prompt templates
3. Configure automated testing
4. Train team on TDD workflow

### Success Metrics

- 90%+ code coverage on new features
- Zero production bugs from new code
- 50% reduction in debugging time
- All PRs include tests before implementation
