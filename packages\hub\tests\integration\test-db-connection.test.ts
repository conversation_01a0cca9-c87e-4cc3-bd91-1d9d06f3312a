import { describe, it, expect } from 'vitest';
import { getSupabaseClient, testDatabaseConnection } from '../../src/lib/supabase';
import dotenv from 'dotenv';
import path from 'path';

// Load environment variables from .env.local
dotenv.config({ path: path.join(__dirname, '../../../.env.local') });

describe('Database Connection Test', () => {
  it('should have valid Supabase environment variables', () => {
    console.log('SUPABASE_URL:', process.env.SUPABASE_URL?.substring(0, 30) + '...');
    console.log('SUPABASE_SERVICE_ROLE_KEY:', process.env.SUPABASE_SERVICE_ROLE_KEY?.substring(0, 20) + '...');
    
    expect(process.env.SUPABASE_URL).toBeDefined();
    expect(process.env.SUPABASE_SERVICE_ROLE_KEY).toBeDefined();
    expect(process.env.SUPABASE_URL).not.toBe('https://test.supabase.co');
    expect(process.env.SUPABASE_SERVICE_ROLE_KEY).not.toBe('test-service-role-key');
  });

  it('should connect to database', async () => {
    const isConnected = await testDatabaseConnection();
    expect(isConnected).toBe(true);
  });

  it('should be able to query trips table', async () => {
    const supabase = getSupabaseClient();
    const { count, error } = await supabase
      .from('trips')
      .select('*', { count: 'exact', head: true });
    
    console.log('Count query result:', { count, error });
    
    // Even if no trips exist, the query should succeed
    expect(error).toBeNull();
    expect(typeof count).toBe('number');
  });
});