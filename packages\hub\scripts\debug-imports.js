#!/usr/bin/env node

/**
 * Debug imports helper
 * Quickly test module resolution to debug import issues
 */

const { resolve } = require('path');
const { existsSync } = require('fs');

console.log('\n🔍 Module Resolution Debugger\n');

// Check shared package dist
const sharedDistPath = resolve(__dirname, '../../shared/dist');
console.log('📦 Shared package dist:', existsSync(sharedDistPath) ? '✅ Found' : '❌ Missing');

if (!existsSync(sharedDistPath)) {
  console.log('\n⚠️  Shared package not built!');
  console.log('   Run: pnpm build (from root)');
  process.exit(1);
}

// Test different import patterns
const testImports = [
  {
    name: 'Package root import',
    code: `import { validateDateRange } from '@travelviz/shared'`,
    test: () => {
      try {
        require('@travelviz/shared');
        return '✅ Works';
      } catch (e) {
        return `❌ Failed: ${e.message}`;
      }
    }
  },
  {
    name: 'Subpath import (should fail)',
    code: `import { validateDateRange } from '@travelviz/shared/utils/date-validation'`,
    test: () => {
      try {
        require('@travelviz/shared/utils/date-validation');
        return '⚠️  Unexpectedly works (should fail)';
      } catch (e) {
        return '✅ Correctly fails (as expected)';
      }
    }
  },
  {
    name: 'Check exports',
    code: `Checking what's exported from @travelviz/shared`,
    test: () => {
      try {
        const shared = require('@travelviz/shared');
        const exports = Object.keys(shared).slice(0, 10);
        return `✅ Exports: ${exports.join(', ')}${exports.length > 10 ? '...' : ''}`;
      } catch (e) {
        return `❌ Failed: ${e.message}`;
      }
    }
  }
];

console.log('Testing import patterns:\n');

testImports.forEach(({ name, code, test }) => {
  console.log(`📋 ${name}`);
  console.log(`   Code: ${code}`);
  console.log(`   Result: ${test()}\n`);
});

// Check for common issues
console.log('🔍 Common Issues Check:\n');

// Check node_modules
const nodeModulesPath = resolve(__dirname, '../node_modules/@travelviz/shared');
console.log('node_modules/@travelviz/shared:', existsSync(nodeModulesPath) ? '✅ Found' : '❌ Missing');

if (!existsSync(nodeModulesPath)) {
  console.log('\n⚠️  Workspace link missing!');
  console.log('   Run: pnpm install (from root)');
}

// Check package.json exports
try {
  const sharedPkg = require(resolve(__dirname, '../../shared/package.json'));
  console.log('\nShared package exports:', sharedPkg.exports ? '✅ Configured' : '❌ Not configured');
  
  if (sharedPkg.exports) {
    console.log('Export config:', JSON.stringify(sharedPkg.exports, null, 2));
  }
} catch (e) {
  console.log('❌ Could not read shared package.json');
}

console.log('\n💡 Tips:');
console.log('- Always use package root imports: import { x } from "@travelviz/shared"');
console.log('- Run "pnpm build" in root to build shared package');
console.log('- Run "pnpm validate:imports" to check all imports');
console.log('- Run "pnpm test" to test with runtime-accurate resolution\n');