// Common types and utilities shared across packages
export * from './types';
export * from './utils';
export * from './constants';

// Export activity types specifically
export { ActivityType, normalizeActivityType } from './types/activity';
export type { ActivityTypeWithAliases } from './types/activity';

// Export database models and schemas
export * from './types/models';

// Export DTOs and validation schemas
export * from './dto';

// Explicitly export date validation utilities
export { validateDateRange, validateNotInPast, DateValidationError } from './utils/date-validation';

// Re-export zod for validation
export { z } from 'zod'; 