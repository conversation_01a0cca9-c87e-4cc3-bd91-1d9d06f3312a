import { describe, it, expect, beforeAll, afterAll, beforeEach } from 'vitest';
import request from 'supertest';
import { app } from '../index';
import { getSupabaseClient } from '../lib/supabase';
import jwt from 'jsonwebtoken';

// Mock Supabase client
vi.mock('../lib/supabase');

describe('E2E Authentication Cascade Prevention', () => {
  let server: any;
  const mockSupabaseClient = {
    auth: {
      getUser: vi.fn(),
    },
    from: vi.fn().mockReturnThis(),
    select: vi.fn().mockReturnThis(),
    eq: vi.fn().mockReturnThis(),
    single: vi.fn(),
  };

  beforeAll(() => {
    vi.mocked(getSupabaseClient).mockReturnValue(mockSupabaseClient as any);
    server = app.listen(0); // Random port
  });

  afterAll((done) => {
    server.close(done);
  });

  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('Complete Authentication Flow', () => {
    it('should prevent cascade when JWT verification fails', async () => {
      // Simulate invalid JWT that will fail verification
      mockSupabaseClient.auth.getUser.mockResolvedValueOnce({
        data: { user: null },
        error: { message: 'Invalid token' },
      });

      const response = await request(server)
        .get('/api/v1/trips')
        .set('Authorization', 'Bearer invalid-jwt-token');

      // Should return 401 without cascading
      expect(response.status).toBe(401);
      expect(response.body).toEqual({
        success: false,
        error: expect.any(String),
        message: expect.any(String),
      });

      // Should only call getUser once (no retries)
      expect(mockSupabaseClient.auth.getUser).toHaveBeenCalledTimes(1);
    });

    it('should handle successful authentication flow', async () => {
      const validToken = jwt.sign(
        { sub: 'user-123', email: '<EMAIL>' },
        'secret'
      );

      mockSupabaseClient.auth.getUser.mockResolvedValueOnce({
        data: {
          user: {
            id: 'user-123',
            email: '<EMAIL>',
            user_metadata: { name: 'Test User' },
          },
        },
        error: null,
      });

      // Mock trips data
      mockSupabaseClient.single.mockResolvedValueOnce({
        data: { trips: [], total: 0 },
        error: null,
      });

      const response = await request(server)
        .get('/api/v1/trips')
        .set('Authorization', `Bearer ${validToken}`);

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
    });

    it('should rate limit excessive requests', async () => {
      // Make multiple rapid requests
      const requests = Array(20).fill(null).map(() =>
        request(server)
          .get('/api/v1/trips')
          .set('Authorization', 'Bearer invalid-token')
      );

      const responses = await Promise.all(requests);
      
      // Some requests should be rate limited
      const rateLimited = responses.filter(r => r.status === 429);
      expect(rateLimited.length).toBeGreaterThan(0);
    });
  });

  describe('Token Refresh Flow', () => {
    it('should handle refresh token flow without cascade', async () => {
      const expiredToken = jwt.sign(
        { sub: 'user-123', exp: Math.floor(Date.now() / 1000) - 3600 },
        'secret'
      );

      // First request with expired token
      const response1 = await request(server)
        .get('/api/v1/trips')
        .set('Authorization', `Bearer ${expiredToken}`);

      expect(response1.status).toBe(401);

      // Attempt refresh (should also fail in this test)
      const refreshResponse = await request(server)
        .post('/api/v1/auth/refresh')
        .send({ refresh_token: 'invalid-refresh' });

      expect(refreshResponse.status).toBe(401);

      // Should not cause system overload
      expect(mockSupabaseClient.auth.getUser).toHaveBeenCalledTimes(0);
    });
  });

  describe('Dashboard API Integration', () => {
    it('should handle paginated trips request with auth failure', async () => {
      mockSupabaseClient.auth.getUser.mockResolvedValueOnce({
        data: { user: null },
        error: { message: 'Session expired' },
      });

      const response = await request(server)
        .get('/api/v1/trips?page=1&limit=20')
        .set('Authorization', 'Bearer expired-token');

      expect(response.status).toBe(401);
      expect(response.body.success).toBe(false);
    });

    it('should successfully load dashboard data with valid auth', async () => {
      const validToken = jwt.sign(
        { sub: 'user-123', email: '<EMAIL>' },
        'secret'
      );

      mockSupabaseClient.auth.getUser.mockResolvedValueOnce({
        data: {
          user: {
            id: 'user-123',
            email: '<EMAIL>',
          },
        },
        error: null,
      });

      // Mock paginated response
      mockSupabaseClient.single.mockResolvedValueOnce({
        data: {
          trips: [
            { id: 'trip-1', title: 'Test Trip', user_id: 'user-123' },
          ],
          total: 1,
        },
        error: null,
      });

      const response = await request(server)
        .get('/api/v1/trips?page=1&limit=20')
        .set('Authorization', `Bearer ${validToken}`);

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data.pagination).toEqual({
        page: 1,
        limit: 20,
        total: 1,
        totalPages: 1,
        hasNextPage: false,
        hasPrevPage: false,
        nextPage: null,
        prevPage: null,
      });
    });
  });

  describe('Concurrent Request Handling', () => {
    it('should handle concurrent auth failures without cascade', async () => {
      mockSupabaseClient.auth.getUser.mockResolvedValue({
        data: { user: null },
        error: { message: 'Invalid token' },
      });

      // Simulate dashboard making multiple concurrent API calls
      const concurrentRequests = [
        request(server).get('/api/v1/trips').set('Authorization', 'Bearer bad-token'),
        request(server).get('/api/v1/profile').set('Authorization', 'Bearer bad-token'),
        request(server).get('/api/v1/activities').set('Authorization', 'Bearer bad-token'),
      ];

      const responses = await Promise.all(concurrentRequests);

      // All should fail with 401
      responses.forEach(response => {
        expect(response.status).toBe(401);
      });

      // Should not cause excessive auth checks
      expect(mockSupabaseClient.auth.getUser.mock.calls.length).toBeLessThanOrEqual(3);
    });
  });
});