import { Router } from 'express';
import { getSupabaseClient } from '../lib/supabase';
import { getErrorMessage } from '../utils/error-handler';
import { logger } from '../utils/logger';

const router: Router = Router();

// Test endpoint to check Supabase connectivity
router.get('/supabase-status', async (_req, res) => {
  try {
    const supabase = getSupabaseClient();
    
    // Test database connection
    const { count: profileCount, error: profileError } = await supabase
      .from('profiles')
      .select('*', { count: 'exact', head: true });
      
    // Test auth service
    const { data: { users }, error: userError } = await supabase.auth.admin.listUsers({
      page: 1,
      perPage: 1
    });
    
    res.json({
      success: true,
      data: {
        database: {
          connected: !profileError,
          profileCount: profileCount || 0,
          error: profileError?.message
        },
        auth: {
          connected: !userError,
          userCount: users?.length || 0,
          error: userError?.message
        }
      }
    });
  } catch (error: unknown) {
    res.status(500).json({
      success: false,
      error: 'Failed to check Supabase status',
      message: getErrorMessage(error)
    });
  }
});

// Create a test user manually
router.post('/create-test-user', async (req, res) => {
  try {
    const supabase = getSupabaseClient();
    const { email = '<EMAIL>', password = 'Test123@' } = req.body;
    
    // First, try to delete any existing user with this email
    const { data: existingUsers } = await supabase.auth.admin.listUsers();
    const existingUser = existingUsers?.users?.find(u => u.email === email);
    
    if (existingUser) {
      await supabase.auth.admin.deleteUser(existingUser.id);
    }
    
    // Create user with admin API (bypasses triggers)
    const { data, error } = await supabase.auth.admin.createUser({
      email,
      password,
      email_confirm: true
    });
    
    if (error) {
      throw error;
    }
    
    // Manually create profile
    const { error: profileError } = await supabase
      .from('profiles')
      .insert({
        id: data.user.id,
        email: data.user.email,
        name: 'Test User'
      });
      
    if (profileError && !profileError.message?.includes('duplicate')) {
      logger.error('Profile creation error:', { error: profileError });
    }
    
    // Sign in to get session
    const { data: signInData, error: signInError } = await supabase.auth.signInWithPassword({
      email,
      password
    });
    
    res.json({
      success: true,
      data: {
        user: data.user,
        session: signInData?.session,
        signInError: signInError?.message
      }
    });
  } catch (error: unknown) {
    res.status(500).json({
      success: false,
      error: 'Failed to create test user',
      message: getErrorMessage(error)
    });
  }
});

export default router;