#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

/**
 * Reorganize tests to follow colocated pattern
 * Move tests from __tests__ folders to be next to their source files
 */

const packages = ['hub', 'web', 'shared'];
const rootDir = path.join(__dirname, '..');

// Track all moves for summary
const moves = [];
const errors = [];

function findTestFiles(dir) {
  const results = [];
  
  try {
    const files = fs.readdirSync(dir);
    
    for (const file of files) {
      const fullPath = path.join(dir, file);
      const stat = fs.statSync(fullPath);
      
      if (stat.isDirectory()) {
        if (file === 'node_modules' || file === 'dist' || file === 'coverage') {
          continue;
        }
        results.push(...findTestFiles(fullPath));
      } else if (file.endsWith('.test.ts') || file.endsWith('.test.tsx') || 
                 file.endsWith('.test.js') || file.endsWith('.test.jsx')) {
        results.push(fullPath);
      }
    }
  } catch (err) {
    console.error(`Error reading directory ${dir}:`, err.message);
  }
  
  return results;
}

function getSourceFileForTest(testFile) {
  const testFileName = path.basename(testFile);
  const testDir = path.dirname(testFile);
  
  // Remove .test.ts/tsx/js/jsx extension
  const sourceFileName = testFileName.replace(/\.test\.(ts|tsx|js|jsx)$/, '');
  
  // Check if this is in a __tests__ directory
  if (testDir.endsWith('__tests__')) {
    const parentDir = path.dirname(testDir);
    
    // Try to find the source file in the parent directory
    const possibleExtensions = ['.ts', '.tsx', '.js', '.jsx'];
    for (const ext of possibleExtensions) {
      const sourceFile = path.join(parentDir, sourceFileName + ext);
      if (fs.existsSync(sourceFile)) {
        return sourceFile;
      }
    }
    
    // For controller/service tests, the source might be in the same directory
    // e.g., controllers/__tests__/activities.controller.test.ts -> controllers/activities.controller.ts
    const grandParentDir = path.dirname(parentDir);
    for (const ext of possibleExtensions) {
      const sourceFile = path.join(grandParentDir, sourceFileName + ext);
      if (fs.existsSync(sourceFile)) {
        return sourceFile;
      }
    }
  }
  
  return null;
}

function moveTestToColocated(testFile) {
  const sourceFile = getSourceFileForTest(testFile);
  
  if (!sourceFile) {
    console.log(`⚠️  No source file found for test: ${testFile}`);
    return false;
  }
  
  const sourceDir = path.dirname(sourceFile);
  const testFileName = path.basename(testFile);
  const newTestPath = path.join(sourceDir, testFileName);
  
  if (testFile === newTestPath) {
    console.log(`✓ Already colocated: ${testFile}`);
    return true;
  }
  
  try {
    // Create directory if needed
    if (!fs.existsSync(sourceDir)) {
      fs.mkdirSync(sourceDir, { recursive: true });
    }
    
    // Move the file
    fs.renameSync(testFile, newTestPath);
    moves.push({ from: testFile, to: newTestPath });
    console.log(`✓ Moved: ${path.relative(rootDir, testFile)} → ${path.relative(rootDir, newTestPath)}`);
    
    return true;
  } catch (err) {
    errors.push({ file: testFile, error: err.message });
    console.error(`✗ Failed to move ${testFile}: ${err.message}`);
    return false;
  }
}

function cleanupEmptyTestDirs(dir) {
  try {
    const files = fs.readdirSync(dir);
    
    // Recursively clean subdirectories first
    for (const file of files) {
      const fullPath = path.join(dir, file);
      const stat = fs.statSync(fullPath);
      
      if (stat.isDirectory() && file === '__tests__') {
        cleanupEmptyTestDirs(fullPath);
      }
    }
    
    // Check if this __tests__ directory is empty
    if (path.basename(dir) === '__tests__') {
      const contents = fs.readdirSync(dir);
      if (contents.length === 0) {
        fs.rmdirSync(dir);
        console.log(`✓ Removed empty directory: ${path.relative(rootDir, dir)}`);
      }
    }
  } catch (err) {
    console.error(`Error cleaning directory ${dir}:`, err.message);
  }
}

// Main execution
console.log('🔄 Reorganizing tests to colocated pattern...\n');

for (const pkg of packages) {
  const packageDir = path.join(rootDir, 'packages', pkg, 'src');
  
  if (!fs.existsSync(packageDir)) {
    console.log(`⚠️  Package directory not found: ${packageDir}`);
    continue;
  }
  
  console.log(`\n📦 Processing ${pkg} package...`);
  
  // Find all test files
  const testFiles = findTestFiles(packageDir);
  console.log(`Found ${testFiles.length} test files`);
  
  // Move tests to colocated positions
  for (const testFile of testFiles) {
    // Skip if already colocated (not in __tests__ directory)
    if (!testFile.includes('__tests__')) {
      console.log(`✓ Already colocated: ${path.relative(rootDir, testFile)}`);
      continue;
    }
    
    moveTestToColocated(testFile);
  }
  
  // Clean up empty __tests__ directories
  cleanupEmptyTestDirs(packageDir);
}

// Summary
console.log('\n📊 Summary:');
console.log(`✓ Moved ${moves.length} test files`);
console.log(`✗ Failed to move ${errors.length} files`);

if (errors.length > 0) {
  console.log('\n❌ Errors:');
  errors.forEach(({ file, error }) => {
    console.log(`  - ${path.relative(rootDir, file)}: ${error}`);
  });
}

console.log('\n✅ Test reorganization complete!');

// Update imports in moved test files
if (moves.length > 0) {
  console.log('\n🔧 Updating imports in moved test files...');
  
  for (const { to } of moves) {
    try {
      let content = fs.readFileSync(to, 'utf-8');
      
      // Update relative imports that might have changed
      // This is a simple heuristic - may need manual adjustment
      content = content.replace(/from ['"]\.\.\/\.\.\/([^'"]+)['"]/g, (match, importPath) => {
        // Adjust the relative path based on new location
        return `from '../${importPath}'`;
      });
      
      fs.writeFileSync(to, content);
    } catch (err) {
      console.error(`Failed to update imports in ${to}: ${err.message}`);
    }
  }
}

console.log('\n💡 Next steps:');
console.log('1. Run "pnpm test" to verify all tests still pass');
console.log('2. Fix any import errors that may have occurred');
console.log('3. Commit the reorganized test structure');