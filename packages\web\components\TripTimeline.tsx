'use client';

import { useMemo, useCallback, useRef, useEffect } from 'react';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { getSafeUrl, groupByCurrency, formatCurrencyAmounts } from '@travelviz/shared';
import { 
  Calendar, 
  Clock, 
  MapPin, 
  DollarSign, 
  Plane, 
  Hotel, 
  Car, 
  Utensils, 
  Activity as ActivityIcon,
  ShoppingBag,
  MoreHorizontal,
  Link,
  FileText,
  Map,
  Eye,
  Theater
} from 'lucide-react';
import { format, isSameDay, parseISO } from 'date-fns';
import { motion } from 'framer-motion';
import type { Activity } from '@/stores/trip.store';
import dynamic from 'next/dynamic';
import { MagicCard, activityGlowColors } from '@/components/magic-ui/magic-card';

// Lazy load the drag-and-drop version to reduce bundle size
const TripTimelineDnd = dynamic(() => import('./TripTimelineDnd'), {
  ssr: false,
  loading: () => (
    <div className="animate-pulse">
      <div className="h-32 bg-gray-200 rounded-lg mb-4"></div>
      <div className="h-32 bg-gray-200 rounded-lg mb-4"></div>
      <div className="h-32 bg-gray-200 rounded-lg"></div>
    </div>
  ),
});

interface TripTimelineProps {
  activities: Activity[];
  tripId?: string;
  enableDragAndDrop?: boolean;
  onReorder?: (orderedIds: string[]) => void;
  onReorderError?: (error: Error) => void;
}

const activityIcons: Record<string, any> = {
  flight: Plane,
  accommodation: Hotel,
  transport: Car,
  dining: Utensils,
  activity: ActivityIcon,
  shopping: ShoppingBag,
  car_rental: Car,
  tour: Map,
  sightseeing: Eye,
  entertainment: Theater,
  other: MoreHorizontal
};

const activityColors: Record<string, string> = {
  flight: 'bg-blue-500',
  accommodation: 'bg-green-500',
  transport: 'bg-purple-500',
  dining: 'bg-yellow-500',
  activity: 'bg-red-500',
  shopping: 'bg-pink-500',
  car_rental: 'bg-indigo-500',
  tour: 'bg-teal-500',
  sightseeing: 'bg-orange-500',
  entertainment: 'bg-violet-500',
  other: 'bg-gray-500'
};

interface GroupedActivities {
  date: string;
  dayNumber: number;
  activities: Activity[];
}

export default function TripTimeline({ 
  activities, 
  tripId,
  enableDragAndDrop = true,
  onReorder,
  onReorderError
}: TripTimelineProps) {
  // Use a ref to track if component is mounted to prevent state updates on unmounted component
  const isMountedRef = useRef(true);
  
  // Clean up on unmount to prevent memory leaks
  useEffect(() => {
    return () => {
      isMountedRef.current = false;
    };
  }, []);

  // Group activities by day
  const groupedActivities = useMemo(() => {
    // Separate scheduled and unscheduled activities
    const scheduled: Activity[] = [];
    const unscheduled: Activity[] = [];
    
    activities.forEach(activity => {
      if (activity.start_time) {
        scheduled.push(activity);
      } else {
        unscheduled.push(activity);
      }
    });
    
    // Sort scheduled activities by start time
    scheduled.sort((a, b) => {
      return new Date(a.start_time!).getTime() - new Date(b.start_time!).getTime();
    });

    // Group scheduled activities by day
    const groups: GroupedActivities[] = [];
    let currentGroup: GroupedActivities | null = null;
    let dayNumber = 1;
    let lastDate: Date | null = null;

    scheduled.forEach(activity => {
      const activityDate = parseISO(activity.start_time!);
      
      if (!currentGroup || (lastDate && !isSameDay(activityDate, lastDate))) {
        // Start new group
        currentGroup = {
          date: format(activityDate, 'EEEE, MMMM d, yyyy'),
          dayNumber: dayNumber++,
          activities: []
        };
        groups.push(currentGroup);
        lastDate = activityDate;
      }
      
      currentGroup.activities.push(activity);
    });

    // Group unscheduled activities by type
    if (unscheduled.length > 0) {
      // First, sort unscheduled activities by type and then by title
      unscheduled.sort((a, b) => {
        if (a.type !== b.type) {
          return a.type.localeCompare(b.type);
        }
        return a.title.localeCompare(b.title);
      });
      
      // Group by activity type
      const typeGroups: Record<string, Activity[]> = {};
      unscheduled.forEach(activity => {
        const type = activity.type;
        if (!typeGroups[type]) {
          typeGroups[type] = [];
        }
        typeGroups[type].push(activity);
      });
      
      // Create groups for each type
      Object.entries(typeGroups).forEach(([type, activities]) => {
        const typeLabel = type.charAt(0).toUpperCase() + type.slice(1);
        groups.push({
          date: `Unscheduled ${typeLabel}s`,
          dayNumber: 0,
          activities
        });
      });
    }

    return groups;
  }, [activities]);

  const formatTime = useCallback((datetime: string | undefined) => {
    if (!datetime) return undefined;
    try {
      return format(parseISO(datetime), 'h:mm a');
    } catch {
      return undefined;
    }
  }, []);

  const formatDuration = useCallback((start: string | undefined, end: string | undefined) => {
    if (!start || !end) return undefined;
    try {
      const startTime = parseISO(start);
      const endTime = parseISO(end);
      const durationMs = endTime.getTime() - startTime.getTime();
      const hours = Math.floor(durationMs / (1000 * 60 * 60));
      const minutes = Math.floor((durationMs % (1000 * 60 * 60)) / (1000 * 60));
      
      if (hours > 0 && minutes > 0) return `${hours}h ${minutes}m`;
      if (hours > 0) return `${hours}h`;
      if (minutes > 0) return `${minutes}m`;
      return undefined;
    } catch {
      return undefined;
    }
  }, []);

  // Memoize summary statistics
  const summaryStats = useMemo(() => ({
    totalActivities: activities.length,
    daysPlanned: groupedActivities.filter(g => g.dayNumber > 0).length,
    uniqueLocations: new Set(activities.map(a => a.location).filter(Boolean)).size,
    totalBudget: formatCurrencyAmounts(groupByCurrency(activities.map(a => ({ 
      price: a.price || undefined, 
      currency: a.currency || undefined 
    }))))
  }), [activities, groupedActivities]);

  // If drag and drop is enabled and we have a tripId, use the DnD version
  if (enableDragAndDrop && tripId) {
    return (
      <TripTimelineDnd 
        activities={activities} 
        tripId={tripId}
        onReorder={onReorder}
        onReorderError={onReorderError}
      />
    );
  }

  if (activities.length === 0) {
    return (
      <Card className="p-8 text-center" role="region" aria-label="Timeline area">
        <Calendar className="h-12 w-12 text-gray-400 mx-auto mb-4" aria-hidden="true" />
        <h3 className="text-lg font-semibold text-gray-900 mb-2">No Activities Yet</h3>
        <p className="text-gray-600">Start adding activities to see your timeline.</p>
      </Card>
    );
  }

  return (
    <div className="space-y-8" role="region" aria-label="Trip timeline">
      {groupedActivities.map((group, groupIndex) => (
        <motion.div
          key={group.date}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: groupIndex * 0.1 }}
        >
          {/* Day Header */}
          <div className="flex items-center space-x-4 mb-6">
            <div className="flex-shrink-0">
              {group.dayNumber > 0 ? (
                <div className="w-12 h-12 bg-orange-500 rounded-full flex items-center justify-center text-white font-bold" aria-label={`Day ${group.dayNumber}`}>
                  {group.dayNumber}
                </div>
              ) : (
                <div className="w-12 h-12 bg-gray-400 rounded-full flex items-center justify-center" aria-label="Unscheduled activities">
                  <Calendar className="h-6 w-6 text-white" aria-hidden="true" />
                </div>
              )}
            </div>
            <div>
              <h3 className="text-xl font-bold text-gray-900">
                {group.dayNumber > 0 ? `Day ${group.dayNumber}` : group.date}
              </h3>
              {group.dayNumber > 0 && (
                <p className="text-gray-600">{group.date}</p>
              )}
              {group.dayNumber === 0 && (
                <p className="text-gray-600">{group.activities.length} {group.activities.length === 1 ? 'activity' : 'activities'}</p>
              )}
            </div>
          </div>

          {/* Activities for this day */}
          <div className="ml-6 border-l-2 border-gray-200 pl-10 space-y-6">
            {group.activities.map((activity, activityIndex) => {
              const Icon = activityIcons[activity.type];
              const colorClass = activityColors[activity.type];
              const time = formatTime(activity.start_time || undefined);
              const duration = formatDuration(activity.start_time || undefined, activity.end_time || undefined);

              return (
                <motion.div
                  key={activity.id}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: groupIndex * 0.1 + activityIndex * 0.05 }}
                  className="relative"
                >
                  {/* Timeline dot */}
                  <div className={`absolute -left-11 w-5 h-5 ${colorClass} rounded-full border-4 border-white shadow-sm`} aria-hidden="true" />
                  
                  {/* Activity card with Magic Card effect */}
                  <MagicCard 
                    glowColor={activityGlowColors[activity.type as keyof typeof activityGlowColors] || activityGlowColors.other}
                    className="rounded-lg"
                  >
                    <Card className="p-4 transition-shadow" role="article" aria-label={`${activity.type} activity: ${activity.title}`}>
                    <div className="flex items-start justify-between mb-2">
                      <div className="flex items-start space-x-3">
                        <div className={`p-2 ${colorClass} rounded-lg`} aria-hidden="true">
                          <Icon className="h-5 w-5 text-white" aria-hidden="true" />
                        </div>
                        <div className="flex-1">
                          <h4 className="font-semibold text-gray-900">{activity.title}</h4>
                          {activity.description && (
                            <p className="text-sm text-gray-600 mt-1">{activity.description}</p>
                          )}
                        </div>
                      </div>
                      <Badge variant="outline" className="ml-2">
                        {activity.type}
                      </Badge>
                    </div>

                    {/* Activity details */}
                    <div className="ml-11 space-y-1">
                      {time && (
                        <div className="flex items-center text-sm text-gray-600">
                          <Clock className="h-3 w-3 mr-1" aria-hidden="true" />
                          <span aria-label={`Scheduled time: ${time}${duration ? `, duration: ${duration}` : ''}`}>
                            {time}
                            {duration && <span className="text-gray-400 ml-1">({duration})</span>}
                          </span>
                        </div>
                      )}

                      {activity.location && (
                        <div className="flex items-center text-sm text-gray-600">
                          <MapPin className="h-3 w-3 mr-1" aria-hidden="true" />
                          <span aria-label={`Location: ${activity.location}`}>{activity.location}</span>
                        </div>
                      )}

                      {activity.price && (
                        <div className="flex items-center text-sm text-gray-600">
                          <DollarSign className="h-3 w-3 mr-1" aria-hidden="true" />
                          <span aria-label={`Price: ${activity.price} ${activity.currency}`}>
                            {activity.price} {activity.currency}
                          </span>
                        </div>
                      )}

                      {activity.booking_reference && (
                        <div className="flex items-center text-sm text-gray-600">
                          <FileText className="h-3 w-3 mr-1" aria-hidden="true" />
                          <span aria-label={`Booking reference: ${activity.booking_reference}`}>
                            Booking: {activity.booking_reference}
                          </span>
                        </div>
                      )}

                      {activity.booking_url && getSafeUrl(activity.booking_url) !== '#' && (
                        <a
                          href={getSafeUrl(activity.booking_url)}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="inline-flex items-center text-sm text-blue-600 hover:text-blue-800 mt-2"
                          aria-label={`View booking for ${activity.title} (opens in new tab)`}
                        >
                          <Link className="h-3 w-3 mr-1" aria-hidden="true" />
                          View Booking
                        </a>
                      )}
                    </div>

                    {/* Notes */}
                    {activity.notes && (
                      <div className="ml-11 mt-3 p-3 bg-gray-50 rounded-lg">
                        <p className="text-sm text-gray-700">{activity.notes}</p>
                      </div>
                    )}
                  </Card>
                  </MagicCard>
                </motion.div>
              );
            })}
          </div>
        </motion.div>
      ))}

      {/* Summary Stats */}
      <Card className="p-6 bg-gray-50" role="region" aria-label="Trip summary statistics">
        <h3 className="font-semibold text-gray-900 mb-4">Trip Summary</h3>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="text-center">
            <div className="text-2xl font-bold text-gray-900" aria-label={`${summaryStats.totalActivities} total activities`}>{summaryStats.totalActivities}</div>
            <div className="text-sm text-gray-600">Total Activities</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-gray-900" aria-label={`${summaryStats.daysPlanned} days planned`}>{summaryStats.daysPlanned}</div>
            <div className="text-sm text-gray-600">Days Planned</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-gray-900" aria-label={`${summaryStats.uniqueLocations} unique locations`}>{summaryStats.uniqueLocations}</div>
            <div className="text-sm text-gray-600">Unique Locations</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-gray-900" aria-label={`Total budget: ${summaryStats.totalBudget}`}>{summaryStats.totalBudget}</div>
            <div className="text-sm text-gray-600">Total Budget</div>
          </div>
        </div>
      </Card>
    </div>
  );
}