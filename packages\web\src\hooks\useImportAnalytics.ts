import { useCallback, useEffect, useRef } from 'react';
import { useAnalytics } from '../contexts/analytics.context';
import { AnalyticsEvent } from '@travelviz/shared';

interface ImportAnalyticsEvent {
  event: string;
  properties?: Record<string, any>;
  timestamp: number;
}

interface ImportAnalytics {
  trackEvent: (event: string, properties?: Record<string, any>) => void;
  trackProgress: (step: string, progress: number) => void;
  trackError: (error: string, context?: Record<string, any>) => void;
  trackSuccess: (tripId: string, stats: Record<string, any>) => void;
  getSessionMetrics: () => ImportSessionMetrics;
}

interface ImportSessionMetrics {
  sessionId: string;
  startTime: number;
  events: ImportAnalyticsEvent[];
  parseTime?: number;
  totalTime?: number;
  errorCount: number;
  lastError?: string;
  source?: string;
  activitiesCount?: number;
}

export function useImportAnalytics(sessionId: string, source?: string): ImportAnalytics {
  const analytics = useAnalytics();
  const metricsRef = useRef<ImportSessionMetrics>({
    sessionId,
    startTime: Date.now(),
    events: [],
    errorCount: 0,
    source,
  });

  const trackEvent = useCallback((event: string, properties?: Record<string, any>) => {
    const analyticsEvent: ImportAnalyticsEvent = {
      event,
      properties,
      timestamp: Date.now(),
    };

    metricsRef.current.events.push(analyticsEvent);

    // Log to console in development
    if (process.env.NODE_ENV === 'development') {
      console.log('[Import Analytics]', event, properties);
    }

    // Send to analytics service using the injected provider
    analytics.track(event, {
      ...properties,
      sessionId,
      source,
    });
  }, [analytics, sessionId, source]);

  const trackProgress = useCallback((step: string, progress: number) => {
    trackEvent(AnalyticsEvent.IMPORT_PROGRESS, {
      step,
      progress,
      elapsed: Date.now() - metricsRef.current.startTime,
    });

    // Track parse completion time
    if (step === 'complete' && !metricsRef.current.parseTime) {
      metricsRef.current.parseTime = Date.now() - metricsRef.current.startTime;
    }
  }, [trackEvent]);

  const trackError = useCallback((error: string, context?: Record<string, any>) => {
    metricsRef.current.errorCount++;
    metricsRef.current.lastError = error;

    trackEvent(AnalyticsEvent.IMPORT_FAILED, {
      error,
      context,
      errorCount: metricsRef.current.errorCount,
    });
  }, [trackEvent]);

  const trackSuccess = useCallback((tripId: string, stats: Record<string, any>) => {
    metricsRef.current.totalTime = Date.now() - metricsRef.current.startTime;
    metricsRef.current.activitiesCount = stats.totalActivities;

    trackEvent(AnalyticsEvent.IMPORT_COMPLETED, {
      tripId,
      ...stats,
      parseTime: metricsRef.current.parseTime,
      totalTime: metricsRef.current.totalTime,
      errorCount: metricsRef.current.errorCount,
    });

    // Track performance metrics
    if (metricsRef.current.parseTime) {
      trackEvent(AnalyticsEvent.FEATURE_USED, {
        featureName: 'import_performance',
        parseTime: metricsRef.current.parseTime,
        totalTime: metricsRef.current.totalTime,
        activitiesPerSecond: stats.totalActivities / (metricsRef.current.parseTime / 1000),
      });
    }
  }, [trackEvent]);

  const getSessionMetrics = useCallback(() => {
    return { ...metricsRef.current };
  }, []);

  // Track session start
  useEffect(() => {
    trackEvent(AnalyticsEvent.IMPORT_STARTED, { source });

    // Track session end on unmount
    return () => {
      const totalTime = Date.now() - metricsRef.current.startTime;
      trackEvent(AnalyticsEvent.FEATURE_USED, {
        featureName: 'import_session_end',
        totalTime,
        eventCount: metricsRef.current.events.length,
        errorCount: metricsRef.current.errorCount,
        completed: !!metricsRef.current.parseTime,
      });
    };
  }, [source, trackEvent]);

  // Track page visibility changes
  useEffect(() => {
    const handleVisibilityChange = () => {
      trackEvent(AnalyticsEvent.FEATURE_USED, {
        featureName: 'import_visibility_change',
        hidden: document.hidden,
        elapsed: Date.now() - metricsRef.current.startTime,
      });
    };

    if (typeof document !== 'undefined') {
      document.addEventListener('visibilitychange', handleVisibilityChange);
      return () => {
        document.removeEventListener('visibilitychange', handleVisibilityChange);
      };
    }
    return undefined;
  }, [trackEvent]);

  return {
    trackEvent,
    trackProgress,
    trackError,
    trackSuccess,
    getSessionMetrics,
  };
}