export enum ActivityType {
  flight = 'flight',
  accommodation = 'accommodation',
  transport = 'transport',
  dining = 'dining',
  activity = 'activity',
  shopping = 'shopping',
  car_rental = 'car_rental',
  tour = 'tour',
  sightseeing = 'sightseeing',
  entertainment = 'entertainment',
  other = 'other'
}

// Type alias for backwards compatibility
export type ActivityTypeWithAliases = ActivityType | 'hotel' | 'food';

// Helper function to normalize activity types
export function normalizeActivityType(type: ActivityTypeWithAliases): ActivityType {
  if (type === 'hotel') return ActivityType.accommodation;
  if (type === 'food') return ActivityType.dining;
  return type as ActivityType;
}