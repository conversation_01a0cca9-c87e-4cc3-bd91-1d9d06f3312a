import { logger } from '../utils/logger';
import { CacheService } from './cache.service';
import axios from 'axios';

export interface UsageEvent {
  service: string;
  timestamp: Date;
  model?: string;
  tokensUsed?: number;
  responseTime?: number;
  cacheHit?: boolean;
  error?: string;
}

export interface UsageAlert {
  service: string;
  level: 'warning' | 'critical';
  usage: number;
  limit: number;
  percentage: number;
  message: string;
  timestamp: Date;
}

export interface UsageMetrics {
  service: string;
  daily: number;
  hourly: number;
  costEstimate: number;
  quotaRemaining: number;
  quotaPercentage: number;
}

export class MonitoringService {
  private static instance: MonitoringService;
  private cacheService: CacheService;
  private alertThresholds = {
    warning: 80,   // 80% usage triggers warning
    critical: 95   // 95% usage triggers critical alert
  };
  
  // Webhook URL for alerts (could be Slack, Discord, etc.)
  private alertWebhook: string | undefined;
  
  private constructor() {
    this.cacheService = CacheService.getInstance();
    this.alertWebhook = process.env.ALERT_WEBHOOK_URL;
  }
  
  static getInstance(): MonitoringService {
    if (!MonitoringService.instance) {
      MonitoringService.instance = new MonitoringService();
    }
    return MonitoringService.instance;
  }
  
  /**
   * Check usage and trigger alerts if needed
   */
  async checkUsageAlerts(): Promise<UsageAlert[]> {
    const alerts: UsageAlert[] = [];
    
    // Check DeepSeek usage
    const deepseekUsage = await this.getServiceUsage('deepseek');
    const deepseekLimit = 1000;
    const deepseekPercentage = (deepseekUsage / deepseekLimit) * 100;
    
    if (deepseekPercentage >= this.alertThresholds.critical) {
      alerts.push({
        service: 'DeepSeek',
        level: 'critical',
        usage: deepseekUsage,
        limit: deepseekLimit,
        percentage: deepseekPercentage,
        message: `DeepSeek usage critical: ${deepseekUsage}/${deepseekLimit} (${deepseekPercentage.toFixed(1)}%)`,
        timestamp: new Date()
      });
    } else if (deepseekPercentage >= this.alertThresholds.warning) {
      alerts.push({
        service: 'DeepSeek',
        level: 'warning',
        usage: deepseekUsage,
        limit: deepseekLimit,
        percentage: deepseekPercentage,
        message: `DeepSeek usage warning: ${deepseekUsage}/${deepseekLimit} (${deepseekPercentage.toFixed(1)}%)`,
        timestamp: new Date()
      });
    }
    
    // Check Gemini usage (no hard limit, but track for monitoring)
    const geminiUsage = await this.getServiceUsage('gemini');
    if (geminiUsage > 5000) { // Soft warning at 5000 requests
      alerts.push({
        service: 'Gemini',
        level: 'warning',
        usage: geminiUsage,
        limit: 10000, // Soft limit for monitoring
        percentage: (geminiUsage / 10000) * 100,
        message: `Gemini usage high: ${geminiUsage} requests today`,
        timestamp: new Date()
      });
    }
    
    // Send alerts
    for (const alert of alerts) {
      await this.sendAlert(alert);
    }
    
    return alerts;
  }
  
  /**
   * Get usage metrics for all services
   */
  async getUsageMetrics(): Promise<UsageMetrics[]> {
    const metrics: UsageMetrics[] = [];
    
    // DeepSeek metrics
    const deepseekDaily = await this.getServiceUsage('deepseek');
    const deepseekHourly = await this.getHourlyUsage('deepseek');
    metrics.push({
      service: 'DeepSeek',
      daily: deepseekDaily,
      hourly: deepseekHourly,
      costEstimate: 0, // Free tier
      quotaRemaining: Math.max(0, 1000 - deepseekDaily),
      quotaPercentage: (deepseekDaily / 1000) * 100
    });
    
    // Gemini metrics
    const geminiDaily = await this.getServiceUsage('gemini');
    const geminiHourly = await this.getHourlyUsage('gemini');
    metrics.push({
      service: 'Gemini',
      daily: geminiDaily,
      hourly: geminiHourly,
      costEstimate: 0, // Free tier
      quotaRemaining: -1, // No hard limit
      quotaPercentage: 0 // No quota
    });
    
    // OpenRouter metrics (fallback)
    const openrouterDaily = await this.getServiceUsage('openrouter');
    const openrouterHourly = await this.getHourlyUsage('openrouter');
    const openrouterCost = openrouterDaily * 0.00014; // $0.14 per 1K tokens estimate
    metrics.push({
      service: 'OpenRouter',
      daily: openrouterDaily,
      hourly: openrouterHourly,
      costEstimate: openrouterCost,
      quotaRemaining: -1, // Pay as you go
      quotaPercentage: 0
    });
    
    return metrics;
  }
  
  /**
   * Log usage event with metadata
   */
  async logUsage(service: string, metadata: {
    model?: string;
    tokensUsed?: number;
    responseTime?: number;
    cacheHit?: boolean;
    error?: string;
  }): Promise<void> {
    const event = {
      service,
      timestamp: new Date(),
      ...metadata
    };
    
    // Store in cache for analytics
    const key = `usage_events_${new Date().toISOString().split('T')[0]}`;
    const events = await this.cacheService.get<UsageEvent[]>(key, { namespace: 'monitoring' }) || [];
    events.push(event);
    
    await this.cacheService.set(key, events, { 
      namespace: 'monitoring', 
      ttl: 604800 // 7 days
    });
    
    // Log for immediate visibility
    logger.info('Usage event', event);
  }
  
  /**
   * Get performance metrics
   */
  async getPerformanceMetrics(): Promise<{
    avgResponseTime: number;
    cacheHitRate: number;
    errorRate: number;
    successRate: number;
  }> {
    const today = new Date().toISOString().split('T')[0];
    const events = await this.cacheService.get<UsageEvent[]>(
      `usage_events_${today}`, 
      { namespace: 'monitoring' }
    ) || [];
    
    if (events.length === 0) {
      return {
        avgResponseTime: 0,
        cacheHitRate: 0,
        errorRate: 0,
        successRate: 100
      };
    }
    
    // Calculate metrics
    const responseTimes = events
      .filter(e => e.responseTime !== undefined)
      .map(e => e.responseTime as number);
    
    const avgResponseTime = responseTimes.length > 0
      ? responseTimes.reduce((a, b) => a + b, 0) / responseTimes.length
      : 0;
    
    const cacheHits = events.filter(e => e.cacheHit).length;
    const cacheHitRate = (cacheHits / events.length) * 100;
    
    const errors = events.filter(e => e.error).length;
    const errorRate = (errors / events.length) * 100;
    const successRate = 100 - errorRate;
    
    return {
      avgResponseTime: Math.round(avgResponseTime),
      cacheHitRate: Math.round(cacheHitRate),
      errorRate: Math.round(errorRate * 10) / 10,
      successRate: Math.round(successRate * 10) / 10
    };
  }
  
  /**
   * Send alert via webhook or log
   */
  private async sendAlert(alert: UsageAlert): Promise<void> {
    // Always log the alert
    if (alert.level === 'critical') {
      logger.error('Usage alert', { ...alert });
    } else {
      logger.warn('Usage alert', { ...alert });
    }
    
    // Send to webhook if configured
    if (this.alertWebhook) {
      try {
        await axios.post(this.alertWebhook, {
          text: alert.message,
          level: alert.level,
          service: alert.service,
          usage: alert.usage,
          limit: alert.limit,
          timestamp: alert.timestamp
        });
      } catch (error) {
        logger.error('Failed to send alert webhook', { error, alert });
      }
    }
  }
  
  /**
   * Get service usage for today
   */
  private async getServiceUsage(service: string): Promise<number> {
    const today = new Date().toISOString().split('T')[0];
    const key = `${service}_usage_${today}`;
    return await this.cacheService.get<number>(key, { namespace: 'usage' }) || 0;
  }
  
  /**
   * Get hourly usage for a service
   */
  private async getHourlyUsage(service: string): Promise<number> {
    const hour = new Date().toISOString().slice(0, 13);
    const key = `${service}_usage_hourly_${hour}`;
    return await this.cacheService.get<number>(key, { namespace: 'usage' }) || 0;
  }
  
  /**
   * Reset daily counters (for testing)
   */
  async resetDailyCounters(): Promise<void> {
    const today = new Date().toISOString().split('T')[0];
    await this.cacheService.del(`deepseek_usage_${today}`, 'usage');
    await this.cacheService.del(`gemini_usage_${today}`, 'usage');
    await this.cacheService.del(`openrouter_usage_${today}`, 'usage');
    logger.info('Daily usage counters reset');
  }
}