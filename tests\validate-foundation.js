#!/usr/bin/env node

/**
 * Foundation Test Validator
 * 
 * Validates that our foundation tests work flawlessly and provides
 * optimization recommendations and pattern documentation.
 */

const fs = require('fs');
const path = require('path');
const { spawn } = require('child_process');

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function logHeader(message) {
  log('\n' + '='.repeat(60), 'cyan');
  log(`  ${message}`, 'bright');
  log('='.repeat(60), 'cyan');
}

function logSuccess(message) {
  log(`✅ ${message}`, 'green');
}

function logError(message) {
  log(`❌ ${message}`, 'red');
}

function logWarning(message) {
  log(`⚠️  ${message}`, 'yellow');
}

function logInfo(message) {
  log(`ℹ️  ${message}`, 'blue');
}

async function runCommand(command, args, options = {}) {
  return new Promise((resolve, reject) => {
    const child = spawn(command, args, {
      stdio: 'pipe',
      shell: true,
      ...options
    });

    let stdout = '';
    let stderr = '';

    child.stdout?.on('data', (data) => {
      stdout += data.toString();
    });

    child.stderr?.on('data', (data) => {
      stderr += data.toString();
    });

    child.on('close', (code) => {
      resolve({ stdout, stderr, code });
    });

    child.on('error', (error) => {
      reject({ error, stdout, stderr });
    });
  });
}

async function checkServerRunning() {
  logInfo('Checking if development server is running...');
  
  try {
    const response = await fetch('http://localhost:3001/api/v1/health');
    if (response.ok) {
      logSuccess('Hub server is running on port 3001');
      return true;
    } else {
      logError('Hub server health check failed');
      return false;
    }
  } catch (error) {
    logError('Hub server is not running');
    logInfo('Please start the development server: pnpm dev');
    return false;
  }
}

async function checkWebServerRunning() {
  logInfo('Checking if web server is running...');
  
  try {
    const response = await fetch('http://localhost:3000');
    if (response.ok) {
      logSuccess('Web server is running on port 3000');
      return true;
    } else {
      logError('Web server is not responding');
      return false;
    }
  } catch (error) {
    logError('Web server is not running');
    logInfo('Please start the development server: pnpm dev');
    return false;
  }
}

async function validateEnvironmentConfig() {
  logHeader('Validating Environment Configuration');
  
  let allValid = true;
  
  // Check test config file
  const testConfigPath = path.join(__dirname, 'test.config.js');
  if (fs.existsSync(testConfigPath)) {
    logSuccess('Test configuration file exists');
    
    try {
      const testConfig = require('./test.config');
      
      // Validate API config
      if (testConfig.api && testConfig.api.baseUrl) {
        logSuccess(`API base URL configured: ${testConfig.api.baseUrl}`);
      } else {
        logError('API base URL not configured');
        allValid = false;
      }
      
      // Validate E2E config
      if (testConfig.e2e && testConfig.e2e.baseUrl) {
        logSuccess(`E2E base URL configured: ${testConfig.e2e.baseUrl}`);
      } else {
        logError('E2E base URL not configured');
        allValid = false;
      }
      
      // Validate auth config
      if (testConfig.auth && testConfig.auth.testUserEmail && testConfig.auth.testUserPassword) {
        logSuccess(`Test user configured: ${testConfig.auth.testUserEmail}`);
      } else {
        logError('Test user credentials not configured');
        allValid = false;
      }
      
    } catch (error) {
      logError(`Test configuration error: ${error.message}`);
      allValid = false;
    }
  } else {
    logError('Test configuration file missing');
    allValid = false;
  }
  
  // Check .env.local files
  const hubEnvPath = path.join(__dirname, '../packages/hub/.env.local');
  const webEnvPath = path.join(__dirname, '../packages/web/.env.local');
  
  if (fs.existsSync(hubEnvPath)) {
    logSuccess('Hub .env.local file exists');
  } else {
    logWarning('Hub .env.local file missing');
  }
  
  if (fs.existsSync(webEnvPath)) {
    logSuccess('Web .env.local file exists');
  } else {
    logWarning('Web .env.local file missing');
  }
  
  return allValid;
}

async function validateApiFoundation() {
  logHeader('Validating API Foundation Tests');
  
  // Check if API test files exist
  const authTestPath = path.join(__dirname, 'api/auth.api.test.js');
  const tripsTestPath = path.join(__dirname, 'api/trips.api.test.js');
  const apiClientPath = path.join(__dirname, 'api/utils/api-client.js');
  
  if (!fs.existsSync(authTestPath)) {
    logError('Authentication API test missing');
    return false;
  }
  logSuccess('Authentication API test exists');
  
  if (!fs.existsSync(tripsTestPath)) {
    logError('Trips API test missing');
    return false;
  }
  logSuccess('Trips API test exists');
  
  if (!fs.existsSync(apiClientPath)) {
    logError('API client missing');
    return false;
  }
  logSuccess('API client exists');
  
  // Try to run the authentication test
  logInfo('Running authentication API test...');
  try {
    const result = await runCommand('node', [authTestPath], {
      cwd: path.join(__dirname, '..')
    });
    
    if (result.code === 0) {
      logSuccess('Authentication API test passed');
      
      // Check if tokens were saved
      const tokenPath = path.join(__dirname, 'api/.test-tokens.json');
      if (fs.existsSync(tokenPath)) {
        logSuccess('Authentication tokens saved successfully');
      } else {
        logWarning('Authentication tokens not saved');
      }
      
      return true;
    } else {
      logError('Authentication API test failed');
      console.log(result.stdout);
      console.log(result.stderr);
      return false;
    }
  } catch (error) {
    logError(`Authentication API test error: ${error.message || error}`);
    return false;
  }
}

async function validateE2eFoundation() {
  logHeader('Validating E2E Foundation Tests');
  
  // Check if E2E test files exist
  const authFlowTestPath = path.join(__dirname, 'e2e/auth-flow.spec.js');
  const pageObjectsPath = path.join(__dirname, 'e2e/utils/page-objects.js');
  const playwrightConfigPath = path.join(__dirname, '../playwright.config.js');
  
  if (!fs.existsSync(authFlowTestPath)) {
    logError('Authentication flow E2E test missing');
    return false;
  }
  logSuccess('Authentication flow E2E test exists');
  
  if (!fs.existsSync(pageObjectsPath)) {
    logError('Page objects missing');
    return false;
  }
  logSuccess('Page objects exist');
  
  if (!fs.existsSync(playwrightConfigPath)) {
    logError('Playwright configuration missing');
    return false;
  }
  logSuccess('Playwright configuration exists');
  
  // Check if Playwright is installed
  try {
    const result = await runCommand('npx', ['playwright', '--version']);
    if (result.code === 0) {
      logSuccess('Playwright is installed');
    } else {
      logWarning('Playwright may not be installed properly');
    }
  } catch (error) {
    logWarning('Could not check Playwright installation');
  }
  
  logInfo('E2E tests require manual validation with running servers');
  logInfo('To test E2E foundation:');
  logInfo('1. Start development servers: pnpm dev');
  logInfo('2. Run E2E tests: pnpm test:e2e:headed');
  
  return true;
}

async function generatePatternDocumentation() {
  logHeader('Generating Pattern Documentation');
  
  const documentation = `# Test Foundation Patterns

## API Test Pattern

### Authentication Pattern
\`\`\`javascript
const ApiTestClient = require('./utils/api-client');
const client = new ApiTestClient();

// Login and save tokens
const loginResponse = await client.login(email, password);
// Tokens are automatically saved and reused

// Make authenticated requests
const response = await client.authenticatedRequest('GET', '/api/v1/trips');
// Token refresh is automatic if expired
\`\`\`

### Test Structure Pattern
\`\`\`javascript
async function runTests() {
  const testResults = { passed: 0, failed: 0, tests: [] };
  
  function logTest(name, result, details = {}) {
    const passed = result.success || result === true;
    testResults.tests.push({ name, passed, details });
    
    if (passed) {
      testResults.passed++;
      console.log(\`✅ \${name}\`);
    } else {
      testResults.failed++;
      console.log(\`❌ \${name}\`, details.error || '');
    }
  }
  
  // Run tests...
  
  return testResults;
}
\`\`\`

## E2E Test Pattern

### Page Object Pattern
\`\`\`javascript
const { HomePage, LoginPage } = require('./utils/page-objects');

test('user flow', async ({ page }) => {
  const homePage = new HomePage(page);
  await homePage.navigate();
  await homePage.clickLogin();
  
  const loginPage = new LoginPage(page);
  await loginPage.login(email, password);
  await loginPage.expectLoginSuccess();
});
\`\`\`

### Screenshot Pattern
\`\`\`javascript
test.afterEach(async ({ page }, testInfo) => {
  if (testInfo.status !== testInfo.expectedStatus) {
    const screenshotPath = \`test-results/failure-\${testInfo.title.replace(/\\s+/g, '-')}-\${Date.now()}.png\`;
    await page.screenshot({ path: screenshotPath, fullPage: true });
  }
});
\`\`\`

## Configuration Pattern

### Environment Loading
\`\`\`javascript
// Load from .env.local files
const hubEnv = loadEnvFile('../packages/hub/.env.local');
const webEnv = loadEnvFile('../packages/web/.env.local');
const testEnv = { ...hubEnv, ...webEnv };
\`\`\`

### Test Config Structure
\`\`\`javascript
const testConfig = {
  api: { baseUrl: 'http://localhost:3001', timeout: 30000 },
  e2e: { baseUrl: 'http://localhost:3000', headless: false },
  auth: { testUserEmail: '...', testUserPassword: '...' }
};
\`\`\`

## Usage Instructions

### Running API Tests
1. Start development server: \`pnpm dev\`
2. Run API tests: \`pnpm test:api\`

### Running E2E Tests
1. Start development server: \`pnpm dev\`
2. Run E2E tests: \`pnpm test:e2e:headed\`

### Creating New Tests
1. Copy the pattern from foundation tests
2. Follow the same structure and error handling
3. Use the established page objects and API client
4. Add proper cleanup and isolation
`;

  const docPath = path.join(__dirname, '../TEST_PATTERNS.md');
  fs.writeFileSync(docPath, documentation);
  logSuccess(`Pattern documentation generated: ${docPath}`);
  
  return true;
}

async function main() {
  logHeader('TravelViz Test Foundation Validator');
  
  log('This script validates that our foundation tests work flawlessly', 'bright');
  log('and provides optimization recommendations and pattern documentation.\n');
  
  let allValid = true;
  
  // Step 1: Check servers
  const hubRunning = await checkServerRunning();
  const webRunning = await checkWebServerRunning();
  
  if (!hubRunning || !webRunning) {
    logWarning('Servers not running - some validations will be skipped');
    logInfo('To run full validation:');
    logInfo('1. Start development servers: pnpm dev');
    logInfo('2. Run this validator again: node tests/validate-foundation.js');
  }
  
  // Step 2: Validate environment
  const envValid = await validateEnvironmentConfig();
  if (!envValid) allValid = false;
  
  // Step 3: Validate API foundation
  if (hubRunning) {
    const apiValid = await validateApiFoundation();
    if (!apiValid) allValid = false;
  } else {
    logWarning('Skipping API foundation validation - hub server not running');
  }
  
  // Step 4: Validate E2E foundation
  const e2eValid = await validateE2eFoundation();
  if (!e2eValid) allValid = false;
  
  // Step 5: Generate documentation
  await generatePatternDocumentation();
  
  // Summary
  logHeader('Validation Results');
  
  if (allValid) {
    logSuccess('🎉 All foundation tests are ready!');
    log('\nNext steps:', 'bright');
    log('• Copy these patterns to create comprehensive test suites');
    log('• Use the API client for all API endpoint tests');
    log('• Use the page objects for all E2E user flow tests');
    log('• Follow the established error handling and reporting patterns');
    
    if (hubRunning && webRunning) {
      log('\n🚀 Ready to scale the patterns!', 'green');
    } else {
      log('\n⚠️  Start servers to complete validation', 'yellow');
    }
  } else {
    logError('❌ Some foundation tests need fixes');
    log('\nTroubleshooting:', 'bright');
    log('• Check environment variables in .env.local files');
    log('• Ensure test user credentials are correct');
    log('• Verify Supabase configuration');
    log('• Make sure development servers are running');
  }
}

// Handle process termination
process.on('SIGINT', () => {
  log('\nValidation interrupted by user', 'yellow');
  process.exit(1);
});

// Run the validator
main().catch((error) => {
  logError(`Fatal error: ${error.message}`);
  process.exit(1);
});