-- Migration to add missing fields identified in QA report
-- Date: 2025-07-06

-- Add missing fields to trips table
ALTER TABLE public.trips
ADD COLUMN IF NOT EXISTS status TEXT DEFAULT 'draft' 
    CHECK (status IN ('draft', 'planning', 'confirmed', 'in_progress', 'completed', 'cancelled')),
ADD COLUMN IF NOT EXISTS visibility TEXT DEFAULT 'private' 
    CHECK (visibility IN ('private', 'unlisted', 'public')),
ADD COLUMN IF NOT EXISTS cover_image TEXT,
ADD COLUMN IF NOT EXISTS tags TEXT[] DEFAULT '{}',
ADD COLUMN IF NOT EXISTS budget_amount DECIMAL(10, 2) CHECK (budget_amount >= 0),
ADD COLUMN IF NOT EXISTS budget_currency CHAR(3) DEFAULT 'USD' CHECK (budget_currency ~ '^[A-Z]{3}$'),
ADD COLUMN IF NOT EXISTS metadata JSONB DEFAULT '{}',
ADD COLUMN IF NOT EXISTS views INTEGER DEFAULT 0;

-- Update is_public column to match new visibility field
-- First, update existing data
UPDATE public.trips 
SET visibility = CASE 
    WHEN is_public = true THEN 'public'
    ELSE 'private'
END;

-- Then drop the old column
ALTER TABLE public.trips DROP COLUMN IF EXISTS is_public;

-- Add missing fields to activities table
ALTER TABLE public.activities
ADD COLUMN IF NOT EXISTS location_lat DECIMAL(10, 8) CHECK (location_lat >= -90 AND location_lat <= 90),
ADD COLUMN IF NOT EXISTS location_lng DECIMAL(11, 8) CHECK (location_lng >= -180 AND location_lng <= 180),
ADD COLUMN IF NOT EXISTS notes TEXT,
ADD COLUMN IF NOT EXISTS attachments TEXT[] DEFAULT '{}',
ADD COLUMN IF NOT EXISTS booking_reference TEXT,
ADD COLUMN IF NOT EXISTS metadata JSONB DEFAULT '{}';

-- Update type column to include more activity types
ALTER TABLE public.activities DROP CONSTRAINT IF EXISTS activities_type_check;
ALTER TABLE public.activities 
ADD CONSTRAINT activities_type_check 
CHECK (type IN ('flight', 'accommodation', 'transport', 'dining', 'activity', 'shopping', 'other'));

-- Create indexes for new fields
CREATE INDEX IF NOT EXISTS idx_trips_status ON public.trips(status);
CREATE INDEX IF NOT EXISTS idx_trips_visibility ON public.trips(visibility);
CREATE INDEX IF NOT EXISTS idx_trips_tags ON public.trips USING gin(tags);
CREATE INDEX IF NOT EXISTS idx_activities_location ON public.activities(location_lat, location_lng) 
    WHERE location_lat IS NOT NULL AND location_lng IS NOT NULL;

-- Add comments for documentation
COMMENT ON COLUMN public.trips.status IS 'Current status of the trip';
COMMENT ON COLUMN public.trips.visibility IS 'Visibility setting: private (only owner), unlisted (shareable via link), public (discoverable)';
COMMENT ON COLUMN public.trips.cover_image IS 'URL to cover image for the trip';
COMMENT ON COLUMN public.trips.tags IS 'Array of tags for categorization';
COMMENT ON COLUMN public.trips.budget_amount IS 'Total budget for the trip';
COMMENT ON COLUMN public.trips.budget_currency IS 'ISO 4217 currency code';
COMMENT ON COLUMN public.trips.metadata IS 'Additional flexible data storage';
COMMENT ON COLUMN public.trips.views IS 'Number of times the trip has been viewed';

COMMENT ON COLUMN public.activities.location_lat IS 'Latitude coordinate';
COMMENT ON COLUMN public.activities.location_lng IS 'Longitude coordinate';
COMMENT ON COLUMN public.activities.notes IS 'Additional notes for the activity';
COMMENT ON COLUMN public.activities.attachments IS 'Array of attachment URLs';
COMMENT ON COLUMN public.activities.booking_reference IS 'Reference number for bookings';
COMMENT ON COLUMN public.activities.metadata IS 'Additional flexible data storage';

-- Update RLS policies to handle new visibility field
-- Drop old policies that reference is_public
DROP POLICY IF EXISTS "Trips are viewable by owner or if public" ON public.trips;
DROP POLICY IF EXISTS "Trip shares viewable if trip is public" ON public.trip_shares;

-- Create new policies with visibility field
CREATE POLICY "Trips are viewable by owner or if public/unlisted with link" 
ON public.trips FOR SELECT 
USING (
    auth.uid() = user_id 
    OR visibility = 'public'
    OR (visibility = 'unlisted' AND share_slug IS NOT NULL)
);

CREATE POLICY "Trip shares viewable if trip is public or unlisted" 
ON public.trip_shares FOR SELECT 
USING (
    EXISTS (
        SELECT 1 FROM public.trips 
        WHERE trips.id = trip_shares.trip_id 
        AND (trips.visibility IN ('public', 'unlisted'))
    )
);

-- Create RPC function for incrementing trip views
CREATE OR REPLACE FUNCTION public.increment_trip_views(trip_id UUID)
RETURNS void AS $$
BEGIN
    UPDATE public.trips 
    SET views = COALESCE(views, 0) + 1 
    WHERE id = trip_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION public.increment_trip_views(UUID) TO authenticated;