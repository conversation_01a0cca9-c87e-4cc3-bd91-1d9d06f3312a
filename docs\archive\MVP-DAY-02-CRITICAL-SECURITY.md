# MVP Day 02: Critical Security

**Date**: [Execute Date]  
**Goal**: Fix critical security vulnerabilities before they become disasters  
**Duration**: 8 hours  
**Critical Path**: YES - Security holes = company-ending breaches

## Context & Current Vulnerabilities

### Critical Issues

1. **JWT Secret Hardcoded** - Anyone can forge tokens
2. **Foreign Keys Broken** - Data integrity compromised
3. **RLS Disabled** - Users can access all data
4. **No Ownership Checks** - API exposes everything

### Risk Assessment

```
Current State: ANY user can:
- Access ALL trips from ALL users
- Modify ANY user's data
- Delete ANYON<PERSON>'s trips
- Forge authentication tokens
```

## Security Threat Model (STRIDE Analysis)

### 1. Spoofing Identity

**Threats**:

- JWT token forgery with hardcoded secret
- Session hijacking without proper token rotation
- Missing user impersonation protection

**Mitigations**:

- Dynamic JWT secrets with rotation
- Short-lived access tokens (15 min) + refresh tokens
- Device fingerprinting for session validation

### 2. Tampering with Data

**Threats**:

- Direct database manipulation without RLS
- API parameter pollution
- Missing input validation on critical fields

**Mitigations**:

- Row Level Security policies enforced
- Zod schema validation on all inputs
- Immutable audit logs for critical changes

### 3. Repudiation

**Threats**:

- No audit trail for user actions
- Missing transaction logs
- Unverifiable state changes

**Mitigations**:

- Comprehensive audit logging system
- Signed API responses
- Database triggers for change tracking

### 4. Information Disclosure

**Threats**:

- User data leakage through broken access control
- Sensitive data in error messages
- Missing data classification

**Mitigations**:

- Strict ownership middleware
- Generic error responses
- Field-level encryption for PII

### 5. Denial of Service

**Threats**:

- No rate limiting on API endpoints
- Resource exhaustion attacks
- Unbounded database queries

**Mitigations**:

- Rate limiting per user/IP
- Query pagination enforcement
- Resource quotas per tenant

### 6. Elevation of Privilege

**Threats**:

- Missing role-based access control
- Privilege escalation through API
- Admin endpoints exposed

**Mitigations**:

- RBAC implementation
- Separate admin API with MFA
- Principle of least privilege

## Zero-Trust Architecture Implementation

### Core Principles

1. **Never Trust, Always Verify** - Every request authenticated
2. **Least Privilege Access** - Minimal permissions by default
3. **Assume Breach** - Design for compromised components
4. **Verify Explicitly** - Multi-factor validation

### Implementation for MVP

```typescript
// packages/hub/src/middleware/zero-trust.middleware.ts
export interface SecurityContext {
  userId: string;
  sessionId: string;
  deviceFingerprint: string;
  ipAddress: string;
  requestTime: Date;
  riskScore: number;
}

export class ZeroTrustValidator {
  async validateRequest(req: Request): Promise<SecurityContext> {
    // 1. Token validation
    const token = await this.validateToken(req.headers.authorization);

    // 2. Session validation
    const session = await this.validateSession(token.sessionId);

    // 3. Device fingerprinting
    const fingerprint = this.calculateFingerprint(req);

    // 4. Risk scoring
    const riskScore = await this.calculateRiskScore({
      ip: req.ip,
      userAgent: req.headers['user-agent'],
      sessionAge: session.age,
      locationChange: await this.detectLocationChange(req.ip, session.lastIp),
    });

    // 5. Enforce policies
    if (riskScore > 0.7) {
      throw new SecurityException('High risk request - additional verification required');
    }

    return {
      userId: token.userId,
      sessionId: token.sessionId,
      deviceFingerprint: fingerprint,
      ipAddress: req.ip,
      requestTime: new Date(),
      riskScore,
    };
  }
}
```

### Network Segmentation

```yaml
# Zero-trust network zones
zones:
  public:
    - web_frontend (3000)
    - public_api_gateway

  private:
    - api_hub (3001)
    - internal_services

  restricted:
    - database
    - secrets_vault
    - admin_services

policies:
  - from: public.web_frontend
    to: private.api_hub
    allow: ['GET', 'POST', 'PUT', 'DELETE']
    require: ['valid_token', 'rate_limit']

  - from: private.api_hub
    to: restricted.database
    allow: ['query']
    require: ['service_account', 'encryption']
```

## Secrets Management with Vault/KMS

### Development Phase - Environment Variables

```bash
# Generate secure secrets
openssl rand -base64 32 > jwt_secret.key
openssl rand -base64 32 > encryption_key.key
openssl rand -base64 16 > api_key_salt.key

# Store in .env.local (NEVER commit)
JWT_SECRET=$(cat jwt_secret.key)
ENCRYPTION_KEY=$(cat encryption_key.key)
API_KEY_SALT=$(cat api_key_salt.key)
```

### Production Phase - HashiCorp Vault Integration

```typescript
// packages/hub/src/config/vault.config.ts
import { VaultClient } from 'node-vault';

export class SecretsManager {
  private vault: VaultClient;
  private cache: Map<string, { value: string; expires: Date }> = new Map();

  constructor() {
    this.vault = new VaultClient({
      endpoint: process.env.VAULT_ENDPOINT,
      token: process.env.VAULT_TOKEN,
    });
  }

  async getSecret(key: string): Promise<string> {
    // Check cache first
    const cached = this.cache.get(key);
    if (cached && cached.expires > new Date()) {
      return cached.value;
    }

    // Fetch from Vault
    const response = await this.vault.read(`secret/data/travelviz/${key}`);
    const value = response.data.data[key];

    // Cache with TTL
    this.cache.set(key, {
      value,
      expires: new Date(Date.now() + 300000), // 5 min TTL
    });

    return value;
  }

  async rotateSecret(key: string): Promise<void> {
    const newValue = crypto.randomBytes(32).toString('base64');
    await this.vault.write(`secret/data/travelviz/${key}`, {
      data: { [key]: newValue },
    });
    this.cache.delete(key);
  }
}

// Usage in application
const secrets = new SecretsManager();
const JWT_SECRET = await secrets.getSecret('jwt_secret');
```

### AWS KMS Alternative

```typescript
// packages/hub/src/config/kms.config.ts
import { KMSClient, DecryptCommand, GenerateDataKeyCommand } from '@aws-sdk/client-kms';

export class KMSSecretsManager {
  private kms: KMSClient;
  private keyId: string;

  constructor() {
    this.kms = new KMSClient({ region: process.env.AWS_REGION });
    this.keyId = process.env.KMS_KEY_ID!;
  }

  async decrypt(encryptedSecret: string): Promise<string> {
    const command = new DecryptCommand({
      CiphertextBlob: Buffer.from(encryptedSecret, 'base64'),
      KeyId: this.keyId,
    });

    const response = await this.kms.send(command);
    return Buffer.from(response.Plaintext!).toString('utf-8');
  }

  async generateDataKey(): Promise<{ plaintext: string; encrypted: string }> {
    const command = new GenerateDataKeyCommand({
      KeyId: this.keyId,
      KeySpec: 'AES_256',
    });

    const response = await this.kms.send(command);
    return {
      plaintext: Buffer.from(response.Plaintext!).toString('base64'),
      encrypted: Buffer.from(response.CiphertextBlob!).toString('base64'),
    };
  }
}
```

## OAuth2/OIDC Authentication Flow

### Implementation Architecture

```typescript
// packages/hub/src/auth/oauth2.config.ts
export interface OAuth2Config {
  provider: 'google' | 'github' | 'apple';
  clientId: string;
  clientSecret: string;
  redirectUri: string;
  scope: string[];
  authorizationEndpoint: string;
  tokenEndpoint: string;
  userInfoEndpoint: string;
}

export class OAuth2Handler {
  private providers: Map<string, OAuth2Config> = new Map();

  constructor(private secrets: SecretsManager) {
    this.initializeProviders();
  }

  private async initializeProviders() {
    // Google OAuth2
    this.providers.set('google', {
      provider: 'google',
      clientId: await this.secrets.getSecret('google_client_id'),
      clientSecret: await this.secrets.getSecret('google_client_secret'),
      redirectUri: `${process.env.API_URL}/auth/callback/google`,
      scope: ['openid', 'email', 'profile'],
      authorizationEndpoint: 'https://accounts.google.com/o/oauth2/v2/auth',
      tokenEndpoint: 'https://oauth2.googleapis.com/token',
      userInfoEndpoint: 'https://www.googleapis.com/oauth2/v2/userinfo',
    });
  }

  // PKCE implementation for mobile/SPA
  generateCodeChallenge(): { verifier: string; challenge: string } {
    const verifier = crypto.randomBytes(32).toString('base64url');
    const challenge = crypto.createHash('sha256').update(verifier).digest('base64url');

    return { verifier, challenge };
  }

  async exchangeCodeForToken(
    provider: string,
    code: string,
    codeVerifier?: string
  ): Promise<TokenResponse> {
    const config = this.providers.get(provider);
    if (!config) throw new Error('Invalid provider');

    const params = new URLSearchParams({
      grant_type: 'authorization_code',
      code,
      redirect_uri: config.redirectUri,
      client_id: config.clientId,
      client_secret: config.clientSecret,
    });

    // Add PKCE verifier if provided
    if (codeVerifier) {
      params.append('code_verifier', codeVerifier);
    }

    const response = await fetch(config.tokenEndpoint, {
      method: 'POST',
      headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
      body: params.toString(),
    });

    if (!response.ok) {
      throw new Error('Token exchange failed');
    }

    return response.json();
  }
}
```

### Secure Session Management

```typescript
// packages/hub/src/auth/session.manager.ts
export class SecureSessionManager {
  private redis: RedisClient;
  private readonly SESSION_TTL = 15 * 60; // 15 minutes
  private readonly REFRESH_TTL = 7 * 24 * 60 * 60; // 7 days

  async createSession(userId: string, metadata: SessionMetadata): Promise<SessionTokens> {
    const sessionId = crypto.randomUUID();
    const accessToken = await this.generateAccessToken(userId, sessionId);
    const refreshToken = await this.generateRefreshToken(userId, sessionId);

    // Store session with metadata
    await this.redis.setex(
      `session:${sessionId}`,
      this.SESSION_TTL,
      JSON.stringify({
        userId,
        createdAt: new Date(),
        lastActivity: new Date(),
        deviceFingerprint: metadata.deviceFingerprint,
        ipAddress: metadata.ipAddress,
        userAgent: metadata.userAgent,
      })
    );

    // Store refresh token mapping
    await this.redis.setex(`refresh:${refreshToken}`, this.REFRESH_TTL, sessionId);

    return { accessToken, refreshToken, expiresIn: this.SESSION_TTL };
  }

  async refreshSession(refreshToken: string): Promise<SessionTokens> {
    const sessionId = await this.redis.get(`refresh:${refreshToken}`);
    if (!sessionId) {
      throw new UnauthorizedError('Invalid refresh token');
    }

    const session = await this.redis.get(`session:${sessionId}`);
    if (!session) {
      throw new UnauthorizedError('Session expired');
    }

    const sessionData = JSON.parse(session);

    // Rotate refresh token for security
    await this.redis.del(`refresh:${refreshToken}`);

    return this.createSession(sessionData.userId, {
      deviceFingerprint: sessionData.deviceFingerprint,
      ipAddress: sessionData.ipAddress,
      userAgent: sessionData.userAgent,
    });
  }
}
```

## Row-Level Security (RLS) Best Practices

### Multi-Tenant Architecture Patterns

```sql
-- Base pattern for multi-tenant RLS
CREATE POLICY "tenant_isolation" ON trips
  USING (
    -- Direct ownership
    user_id = auth.uid()
    OR
    -- Team membership (future feature)
    EXISTS (
      SELECT 1 FROM team_members tm
      JOIN teams t ON tm.team_id = t.id
      WHERE tm.user_id = auth.uid()
      AND t.id = trips.team_id
    )
    OR
    -- Public access with conditions
    (is_public = true AND published_at IS NOT NULL AND published_at < NOW())
  );

-- Performance-optimized RLS with security definer functions
CREATE OR REPLACE FUNCTION user_trips(user_uuid UUID)
RETURNS SETOF trips
LANGUAGE sql
SECURITY DEFINER
STABLE
AS $$
  SELECT * FROM trips
  WHERE user_id = user_uuid
  OR EXISTS (
    SELECT 1 FROM trip_collaborators tc
    WHERE tc.trip_id = trips.id
    AND tc.user_id = user_uuid
    AND tc.status = 'active'
  );
$$;
```

### RLS Performance Optimization

```sql
-- Create security indexes
CREATE INDEX idx_trips_user_id ON trips(user_id);
CREATE INDEX idx_trips_public_access ON trips(is_public, published_at)
  WHERE is_public = true AND published_at IS NOT NULL;
CREATE INDEX idx_team_members_user_team ON team_members(user_id, team_id);

-- Materialized view for complex permissions
CREATE MATERIALIZED VIEW user_accessible_trips AS
SELECT DISTINCT
  t.id,
  t.user_id,
  up.user_id as accessible_by_user_id
FROM trips t
LEFT JOIN user_permissions up ON t.id = up.trip_id
WITH DATA;

-- Refresh on permission changes
CREATE OR REPLACE FUNCTION refresh_user_trips()
RETURNS TRIGGER AS $$
BEGIN
  REFRESH MATERIALIZED VIEW CONCURRENTLY user_accessible_trips;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;
```

### Security Headers Configuration

```typescript
// packages/hub/src/middleware/security-headers.middleware.ts
import helmet from 'helmet';

export const securityHeaders = helmet({
  // Content Security Policy
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      scriptSrc: ["'self'", "'unsafe-inline'", 'https://maps.googleapis.com'],
      styleSrc: ["'self'", "'unsafe-inline'", 'https://fonts.googleapis.com'],
      imgSrc: ["'self'", 'data:', 'https:', 'blob:'],
      connectSrc: ["'self'", 'https://api.mapbox.com', 'wss://travelviz.com'],
      fontSrc: ["'self'", 'https://fonts.gstatic.com'],
      objectSrc: ["'none'"],
      mediaSrc: ["'self'"],
      frameSrc: ["'none'"],
      upgradeInsecureRequests: [],
    },
  },

  // Strict Transport Security
  hsts: {
    maxAge: 31536000,
    includeSubDomains: true,
    preload: true,
  },

  // Additional security headers
  referrerPolicy: { policy: 'strict-origin-when-cross-origin' },
  noSniff: true,
  xssFilter: true,
  ieNoOpen: true,
  frameguard: { action: 'deny' },
  permittedCrossDomainPolicies: false,
});

// Custom headers for API security
export const apiSecurityHeaders = (req: Request, res: Response, next: NextFunction) => {
  // API version headers
  res.setHeader('X-API-Version', '1.0.0');

  // Request ID for tracing
  res.setHeader('X-Request-ID', req.id || crypto.randomUUID());

  // Prevent caching of sensitive data
  if (req.path.includes('/auth') || req.path.includes('/user')) {
    res.setHeader('Cache-Control', 'no-store, no-cache, must-revalidate, private');
    res.setHeader('Pragma', 'no-cache');
    res.setHeader('Expires', '0');
  }

  // CORS with credentials
  res.setHeader('Access-Control-Allow-Credentials', 'true');

  next();
};
```

## API Rate Limiting & DDoS Protection

```typescript
// packages/hub/src/middleware/rate-limit.middleware.ts
import rateLimit from 'express-rate-limit';
import RedisStore from 'rate-limit-redis';
import { createClient } from 'redis';

const redisClient = createClient({
  url: process.env.REDIS_URL,
});

// Base rate limiter
export const apiLimiter = rateLimit({
  store: new RedisStore({
    client: redisClient,
    prefix: 'rl:api:',
  }),
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // 100 requests per window
  message: 'Too many requests, please try again later.',
  standardHeaders: true,
  legacyHeaders: false,
  skip: req => {
    // Skip rate limiting for health checks
    return req.path === '/health';
  },
});

// Strict limiter for auth endpoints
export const authLimiter = rateLimit({
  store: new RedisStore({
    client: redisClient,
    prefix: 'rl:auth:',
  }),
  windowMs: 15 * 60 * 1000,
  max: 5, // Only 5 auth attempts per 15 minutes
  skipSuccessfulRequests: true, // Don't count successful logins
});

// AI endpoint limiter (expensive operations)
export const aiLimiter = rateLimit({
  store: new RedisStore({
    client: redisClient,
    prefix: 'rl:ai:',
  }),
  windowMs: 60 * 60 * 1000, // 1 hour
  max: 10, // 10 AI requests per hour
  skip: req => {
    // Premium users get higher limits
    return req.user?.subscription === 'premium';
  },
});

// DDoS protection with dynamic blocking
export class DDoSProtection {
  private blacklist: Set<string> = new Set();
  private requestCounts: Map<string, number[]> = new Map();

  middleware() {
    return (req: Request, res: Response, next: NextFunction) => {
      const ip = req.ip;

      // Check blacklist
      if (this.blacklist.has(ip)) {
        return res.status(429).json({ error: 'IP temporarily blocked' });
      }

      // Track request patterns
      const now = Date.now();
      const requests = this.requestCounts.get(ip) || [];

      // Remove old requests (older than 1 minute)
      const recentRequests = requests.filter(time => now - time < 60000);

      // Detect spike patterns (more than 100 requests in 10 seconds)
      const last10Seconds = recentRequests.filter(time => now - time < 10000);
      if (last10Seconds.length > 100) {
        this.blacklist.add(ip);
        setTimeout(() => this.blacklist.delete(ip), 300000); // 5 min ban
        return res.status(429).json({ error: 'Rate limit exceeded' });
      }

      recentRequests.push(now);
      this.requestCounts.set(ip, recentRequests);

      next();
    };
  }
}
```

## Audit Logging System

```typescript
// packages/hub/src/services/audit.service.ts
export interface AuditLog {
  id: string;
  timestamp: Date;
  userId: string;
  action: string;
  resource: string;
  resourceId: string;
  ipAddress: string;
  userAgent: string;
  result: 'success' | 'failure';
  metadata?: Record<string, any>;
}

export class AuditService {
  private readonly RETENTION_DAYS = 90;

  async log(event: Omit<AuditLog, 'id' | 'timestamp'>): Promise<void> {
    const auditLog: AuditLog = {
      id: crypto.randomUUID(),
      timestamp: new Date(),
      ...event,
    };

    // Store in database
    await supabase.from('audit_logs').insert(auditLog);

    // Alert on suspicious patterns
    if (event.result === 'failure') {
      await this.checkFailurePatterns(event.userId, event.action);
    }
  }

  private async checkFailurePatterns(userId: string, action: string) {
    const recentFailures = await supabase
      .from('audit_logs')
      .select('*')
      .eq('userId', userId)
      .eq('result', 'failure')
      .gte('timestamp', new Date(Date.now() - 300000)); // Last 5 minutes

    if (recentFailures.data?.length > 5) {
      // Trigger security alert
      await this.sendSecurityAlert({
        type: 'MULTIPLE_FAILURES',
        userId,
        action,
        count: recentFailures.data.length,
      });
    }
  }
}

// Audit middleware
export const auditMiddleware = (action: string) => {
  return async (req: AuthRequest, res: Response, next: NextFunction) => {
    const startTime = Date.now();

    // Capture original end function
    const originalEnd = res.end;

    res.end = function (...args: any[]) {
      const result = res.statusCode < 400 ? 'success' : 'failure';

      // Log the action
      auditService
        .log({
          userId: req.user?.id || 'anonymous',
          action,
          resource: req.path.split('/')[3], // e.g., 'trips', 'activities'
          resourceId: req.params.id || req.params.tripId || 'n/a',
          ipAddress: req.ip,
          userAgent: req.headers['user-agent'] || 'unknown',
          result,
          metadata: {
            method: req.method,
            statusCode: res.statusCode,
            duration: Date.now() - startTime,
            body: result === 'failure' ? req.body : undefined,
          },
        })
        .catch(console.error);

      // Call original end
      originalEnd.apply(res, args);
    };

    next();
  };
};
```

## Morning Tasks (4 hours)

### Task 1: Remove Hardcoded JWT Secret (1 hour)

**File**: `packages/hub/src/utils/tokens.ts`

**Current VULNERABLE Code**:

```typescript
const JWT_SECRET = process.env.JWT_SECRET || 'fallback-secret'; // CRITICAL: Remove fallback!
```

**Secure Implementation**:

```typescript
// packages/hub/src/utils/tokens.ts
const JWT_SECRET = process.env.JWT_SECRET;

if (!JWT_SECRET) {
  console.error('CRITICAL: JWT_SECRET environment variable is not set');
  console.error('Generate one with: openssl rand -base64 32');
  process.exit(1); // Stop the server - this is non-negotiable
}

// Add validation
if (JWT_SECRET.length < 32) {
  console.error('CRITICAL: JWT_SECRET must be at least 32 characters');
  process.exit(1);
}

export function generateToken(userId: string): string {
  return jwt.sign(
    {
      userId,
      iat: Math.floor(Date.now() / 1000),
      exp: Math.floor(Date.now() / 1000) + 60 * 60 * 24 * 7, // 7 days
    },
    JWT_SECRET,
    { algorithm: 'HS256' }
  );
}

export function verifyToken(token: string): { userId: string } {
  try {
    const decoded = jwt.verify(token, JWT_SECRET, {
      algorithms: ['HS256'],
      maxAge: '7d',
    }) as any;

    if (!decoded.userId) {
      throw new Error('Invalid token structure');
    }

    return { userId: decoded.userId };
  } catch (error) {
    throw new Error('Invalid or expired token');
  }
}
```

**Environment Setup**:

```bash
# Generate secure secret
echo "JWT_SECRET=$(openssl rand -base64 32)" >> packages/hub/.env.local

# Verify it's set
grep JWT_SECRET packages/hub/.env.local
```

**Verification**:

```bash
# Start hub without JWT_SECRET
unset JWT_SECRET && cd packages/hub && pnpm dev
# Should exit with error

# Start with JWT_SECRET
cd packages/hub && pnpm dev
# Should start successfully
```

### Task 2: Fix Foreign Key Constraints (1.5 hours)

**Create Migration**: `packages/hub/src/migrations/010_fix_foreign_keys.sql`

```sql
-- Fix foreign key constraints
BEGIN;

-- Drop existing broken constraints
ALTER TABLE IF EXISTS activities
  DROP CONSTRAINT IF EXISTS activities_trip_id_fkey;

ALTER TABLE IF EXISTS search_history
  DROP CONSTRAINT IF EXISTS search_history_user_id_fkey;

ALTER TABLE IF EXISTS trip_clones
  DROP CONSTRAINT IF EXISTS trip_clones_original_trip_id_fkey,
  DROP CONSTRAINT IF EXISTS trip_clones_cloned_trip_id_fkey,
  DROP CONSTRAINT IF EXISTS trip_clones_user_id_fkey;

-- Add proper foreign keys with CASCADE
ALTER TABLE activities
  ADD CONSTRAINT activities_trip_id_fkey
  FOREIGN KEY (trip_id) REFERENCES trips(id)
  ON DELETE CASCADE;

ALTER TABLE search_history
  ADD CONSTRAINT search_history_user_id_fkey
  FOREIGN KEY (user_id) REFERENCES auth.users(id)
  ON DELETE CASCADE;

ALTER TABLE trip_clones
  ADD CONSTRAINT trip_clones_original_trip_id_fkey
  FOREIGN KEY (original_trip_id) REFERENCES trips(id)
  ON DELETE CASCADE,
  ADD CONSTRAINT trip_clones_cloned_trip_id_fkey
  FOREIGN KEY (cloned_trip_id) REFERENCES trips(id)
  ON DELETE CASCADE,
  ADD CONSTRAINT trip_clones_user_id_fkey
  FOREIGN KEY (user_id) REFERENCES auth.users(id)
  ON DELETE CASCADE;

-- Add indexes for foreign keys (performance)
CREATE INDEX IF NOT EXISTS idx_activities_trip_id ON activities(trip_id);
CREATE INDEX IF NOT EXISTS idx_search_history_user_id ON search_history(user_id);
CREATE INDEX IF NOT EXISTS idx_trip_clones_user_id ON trip_clones(user_id);

COMMIT;
```

**Run Migration**:

```bash
# Apply through Supabase dashboard or CLI
npx supabase db push
```

**Test Integrity**:

```sql
-- Test: This should fail now (good!)
INSERT INTO activities (trip_id, name) VALUES ('non-existent-id', 'Test');

-- Test: Cascading delete should work
DELETE FROM trips WHERE id = 'some-trip-id';
-- Should also delete all related activities
```

### Task 3: Re-enable Row Level Security (1.5 hours)

**Create Migration**: `packages/hub/src/migrations/011_enable_rls_with_policies.sql`

```sql
-- Enable RLS on all tables
BEGIN;

-- Enable RLS
ALTER TABLE trips ENABLE ROW LEVEL SECURITY;
ALTER TABLE activities ENABLE ROW LEVEL SECURITY;
ALTER TABLE search_history ENABLE ROW LEVEL SECURITY;
ALTER TABLE trip_clones ENABLE ROW LEVEL SECURITY;

-- Drop all existing policies (clean slate)
DROP POLICY IF EXISTS "Users can view own trips" ON trips;
DROP POLICY IF EXISTS "Users can create own trips" ON trips;
DROP POLICY IF EXISTS "Users can update own trips" ON trips;
DROP POLICY IF EXISTS "Users can delete own trips" ON trips;
DROP POLICY IF EXISTS "Public trips are viewable" ON trips;

-- Trips policies
CREATE POLICY "Users can view own trips" ON trips
  FOR SELECT USING (
    auth.uid() = user_id OR
    (is_public = true AND published_at IS NOT NULL)
  );

CREATE POLICY "Users can create own trips" ON trips
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own trips" ON trips
  FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete own trips" ON trips
  FOR DELETE USING (auth.uid() = user_id);

-- Activities policies (through trip ownership)
CREATE POLICY "Users can view activities" ON activities
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM trips
      WHERE trips.id = activities.trip_id
      AND (trips.user_id = auth.uid() OR (trips.is_public = true AND trips.published_at IS NOT NULL))
    )
  );

CREATE POLICY "Users can manage own activities" ON activities
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM trips
      WHERE trips.id = activities.trip_id
      AND trips.user_id = auth.uid()
    )
  );

-- Search history (private to user)
CREATE POLICY "Users can manage own search history" ON search_history
  FOR ALL USING (auth.uid() = user_id);

-- Trip clones (track who cloned what)
CREATE POLICY "Users can view own clones" ON trip_clones
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can create clones" ON trip_clones
  FOR INSERT WITH CHECK (auth.uid() = user_id);

COMMIT;
```

**Test RLS**:

```typescript
// Test file: test-rls.ts
import { createClient } from '@supabase/supabase-js';

const supabase1 = createClient(url, key, { auth: { persistSession: false } });
const supabase2 = createClient(url, key, { auth: { persistSession: false } });

// Login as different users
await supabase1.auth.signInWithPassword({ email: '<EMAIL>', password });
await supabase2.auth.signInWithPassword({ email: '<EMAIL>', password });

// User 1 creates a trip
const { data: trip } = await supabase1.from('trips').insert({ title: 'My Trip' }).select().single();

// User 2 tries to access it (should fail)
const { data, error } = await supabase2.from('trips').select().eq('id', trip.id);
console.assert(error || !data?.length, 'RLS not working!');
```

## Afternoon Tasks (4 hours)

### Task 4: Ownership Middleware (2 hours)

**Create Middleware**: `packages/hub/src/middleware/ownership.middleware.ts`

```typescript
import { Request, Response, NextFunction } from 'express';
import { supabase } from '../config/supabase';

interface AuthRequest extends Request {
  user?: { id: string };
  trip?: any;
}

/**
 * Verifies user owns the trip they're trying to access
 */
export async function verifyTripOwnership(req: AuthRequest, res: Response, next: NextFunction) {
  try {
    const { tripId } = req.params;
    const userId = req.user?.id;

    if (!userId) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    if (!tripId) {
      return next(); // No trip to verify
    }

    // Check ownership
    const { data: trip, error } = await supabase
      .from('trips')
      .select('id, user_id, is_public')
      .eq('id', tripId)
      .single();

    if (error || !trip) {
      return res.status(404).json({ error: 'Trip not found' });
    }

    // Check permissions
    const isOwner = trip.user_id === userId;
    const isPublicRead = trip.is_public && req.method === 'GET';

    if (!isOwner && !isPublicRead) {
      return res.status(403).json({
        error: 'Forbidden',
        message: 'You do not have permission to access this trip',
      });
    }

    // Attach trip to request for downstream use
    req.trip = trip;
    next();
  } catch (error) {
    console.error('Ownership check failed:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
}

/**
 * Ensures user can only access their own resources
 */
export async function verifyResourceOwnership(
  resourceType: 'trip' | 'activity' | 'search_history'
) {
  return async (req: AuthRequest, res: Response, next: NextFunction) => {
    const userId = req.user?.id;
    const resourceId = req.params.id || req.params[`${resourceType}Id`];

    if (!userId) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    try {
      let query = supabase.from(resourceType + 's').select('*');

      // Add user filter based on resource type
      switch (resourceType) {
        case 'trip':
          query = query.eq('user_id', userId);
          break;
        case 'activity':
          // Activities are owned through trips
          query = supabase
            .from('activities')
            .select('*, trip:trips!inner(user_id)')
            .eq('trips.user_id', userId);
          break;
        case 'search_history':
          query = query.eq('user_id', userId);
          break;
      }

      if (resourceId) {
        query = query.eq('id', resourceId).single();
      }

      const { data, error } = await query;

      if (error || !data) {
        return res.status(404).json({ error: `${resourceType} not found` });
      }

      next();
    } catch (error) {
      console.error('Resource ownership check failed:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  };
}
```

**Apply to Routes**: `packages/hub/src/routes/trips.routes.ts`

```typescript
import { verifyTripOwnership } from '../middleware/ownership.middleware';

// Apply to all trip-specific routes
router.get('/trips/:tripId', authenticate, verifyTripOwnership, getTrip);
router.put('/trips/:tripId', authenticate, verifyTripOwnership, updateTrip);
router.delete('/trips/:tripId', authenticate, verifyTripOwnership, deleteTrip);

// Apply to activity routes
router.get('/trips/:tripId/activities', authenticate, verifyTripOwnership, getActivities);
router.post('/trips/:tripId/activities', authenticate, verifyTripOwnership, createActivity);
router.put(
  '/trips/:tripId/activities/:activityId',
  authenticate,
  verifyTripOwnership,
  updateActivity
);
router.delete(
  '/trips/:tripId/activities/:activityId',
  authenticate,
  verifyTripOwnership,
  deleteActivity
);
```

### Task 5: Security Testing Suite (2 hours)

**Create Test File**: `packages/hub/src/tests/security.test.ts`

```typescript
import request from 'supertest';
import { app } from '../server';
import { generateToken } from '../utils/tokens';

describe('Security Tests', () => {
  let user1Token: string;
  let user2Token: string;
  let user1TripId: string;

  beforeAll(async () => {
    // Setup test users
    user1Token = generateToken('user1-id');
    user2Token = generateToken('user2-id');

    // User 1 creates a trip
    const res = await request(app)
      .post('/api/v1/trips')
      .set('Authorization', `Bearer ${user1Token}`)
      .send({ title: 'User 1 Trip' });

    user1TripId = res.body.data.id;
  });

  describe('JWT Security', () => {
    it('should reject requests without token', async () => {
      const res = await request(app).get('/api/v1/trips');
      expect(res.status).toBe(401);
    });

    it('should reject invalid tokens', async () => {
      const res = await request(app)
        .get('/api/v1/trips')
        .set('Authorization', 'Bearer invalid-token');
      expect(res.status).toBe(401);
    });

    it('should reject expired tokens', async () => {
      const expiredToken = jwt.sign(
        { userId: 'test', exp: Math.floor(Date.now() / 1000) - 3600 },
        process.env.JWT_SECRET!
      );
      const res = await request(app)
        .get('/api/v1/trips')
        .set('Authorization', `Bearer ${expiredToken}`);
      expect(res.status).toBe(401);
    });
  });

  describe('Ownership Security', () => {
    it('should prevent user 2 from accessing user 1 private trip', async () => {
      const res = await request(app)
        .get(`/api/v1/trips/${user1TripId}`)
        .set('Authorization', `Bearer ${user2Token}`);
      expect(res.status).toBe(403);
    });

    it('should prevent user 2 from updating user 1 trip', async () => {
      const res = await request(app)
        .put(`/api/v1/trips/${user1TripId}`)
        .set('Authorization', `Bearer ${user2Token}`)
        .send({ title: 'Hacked!' });
      expect(res.status).toBe(403);
    });

    it('should prevent user 2 from deleting user 1 trip', async () => {
      const res = await request(app)
        .delete(`/api/v1/trips/${user1TripId}`)
        .set('Authorization', `Bearer ${user2Token}`);
      expect(res.status).toBe(403);
    });

    it('should allow user 2 to access public trips', async () => {
      // Make trip public
      await request(app)
        .put(`/api/v1/trips/${user1TripId}`)
        .set('Authorization', `Bearer ${user1Token}`)
        .send({ is_public: true, published_at: new Date() });

      // User 2 can now read it
      const res = await request(app)
        .get(`/api/v1/trips/${user1TripId}`)
        .set('Authorization', `Bearer ${user2Token}`);
      expect(res.status).toBe(200);

      // But still can't modify
      const updateRes = await request(app)
        .put(`/api/v1/trips/${user1TripId}`)
        .set('Authorization', `Bearer ${user2Token}`)
        .send({ title: 'Still trying to hack' });
      expect(updateRes.status).toBe(403);
    });
  });

  describe('SQL Injection Protection', () => {
    it('should sanitize malicious input', async () => {
      const maliciousInput = "'; DROP TABLE trips; --";
      const res = await request(app)
        .post('/api/v1/trips')
        .set('Authorization', `Bearer ${user1Token}`)
        .send({ title: maliciousInput });

      // Should create trip safely
      expect(res.status).toBe(201);
      expect(res.body.data.title).toBe(maliciousInput);

      // Tables should still exist
      const checkRes = await request(app)
        .get('/api/v1/trips')
        .set('Authorization', `Bearer ${user1Token}`);
      expect(checkRes.status).toBe(200);
    });
  });
});
```

**Run Security Tests**:

```bash
cd packages/hub
pnpm test security.test.ts
```

## Extended Thinking Prompts

For complex security decisions:

```
The security requirement is: [describe requirement]
Current implementation: [current code]
Potential attack vectors: [list concerns]
What's the most secure approach that maintains usability?
```

## Security Checklist

### Environment Security

- [ ] JWT_SECRET removed from code
- [ ] JWT_SECRET in .env.local only
- [ ] Server fails to start without JWT_SECRET
- [ ] All secrets in environment variables
- [ ] .env.local in .gitignore

### Database Security

- [ ] Foreign keys properly constrained
- [ ] Cascading deletes work correctly
- [ ] RLS enabled on all tables
- [ ] RLS policies test correctly
- [ ] No direct database access from frontend

### API Security

- [ ] All routes require authentication
- [ ] Ownership middleware on all routes
- [ ] 403 returned for unauthorized access
- [ ] Public routes explicitly marked
- [ ] Rate limiting considered

### Testing

- [ ] Security test suite passes
- [ ] Cross-user access blocked
- [ ] Token validation working
- [ ] SQL injection protected
- [ ] XSS prevention in place

## Definition of Done

✅ Security Commands Pass:

```bash
# No hardcoded secrets
grep -r "fallback" packages/hub/src --include="*.ts" | grep -i secret
# Should return nothing

# RLS enabled
echo "SELECT tablename, rowsecurity FROM pg_tables WHERE schemaname = 'public';" | psql
# All tables should show rowsecurity = true

# Security tests pass
cd packages/hub && pnpm test security.test.ts
# All tests green
```

✅ Manual Security Verification:

```bash
# As User A, create a trip
curl -X POST http://localhost:3001/api/v1/trips \
  -H "Authorization: Bearer $USER_A_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"title": "My Secret Trip"}'

# As User B, try to access it (should fail with 403)
curl -X GET http://localhost:3001/api/v1/trips/$TRIP_ID \
  -H "Authorization: Bearer $USER_B_TOKEN"
```

## Production Readiness

After today, the app has:

- ✅ Secure authentication
- ✅ Data isolation between users
- ✅ Protected API endpoints
- ✅ Database integrity
- ✅ Basic security test coverage

Still needed (future):

- Rate limiting
- CORS configuration
- Input sanitization middleware
- Security headers
- Audit logging

## Next Day Preview

Day 3 will prevent performance disasters:

- Add database indexes
- Implement pagination
- Optimize queries
- Add caching layer
- Load testing

## Penetration Test Plan

### Phase 1: Reconnaissance (1 hour)

```bash
# 1. API Discovery
curl -X OPTIONS http://localhost:3001/api/v1/
nmap -p 3000,3001 localhost

# 2. Endpoint enumeration
ffuf -w /usr/share/wordlists/common-api-endpoints.txt \
  -u http://localhost:3001/api/v1/FUZZ

# 3. Technology fingerprinting
whatweb http://localhost:3000
wafw00f http://localhost:3001
```

### Phase 2: Authentication Testing (2 hours)

```typescript
// Test JWT vulnerabilities
const tests = [
  // Test 1: Algorithm confusion attack
  {
    name: 'JWT None Algorithm',
    token: jwt.sign({ userId: 'admin' }, '', { algorithm: 'none' }),
    expected: 401,
  },

  // Test 2: Weak secret brute force
  {
    name: 'Common JWT Secrets',
    secrets: ['secret', '123456', 'password', 'admin'],
    expected: 401,
  },

  // Test 3: Token expiration bypass
  {
    name: 'Expired Token',
    token: generateExpiredToken(),
    expected: 401,
  },
];

// Test OAuth2 flows
const oauthTests = [
  // CSRF in OAuth flow
  { name: 'Missing state parameter', expected: 400 },

  // Redirect URI manipulation
  { name: 'Open redirect', redirect: 'https://evil.com', expected: 400 },

  // Token leakage
  { name: 'Referrer leakage', checkHeaders: true },
];
```

### Phase 3: Authorization Testing (2 hours)

```bash
# Horizontal privilege escalation
curl -H "Authorization: Bearer $USER_A_TOKEN" \
  http://localhost:3001/api/v1/users/$USER_B_ID/trips

# Vertical privilege escalation
curl -X POST -H "Authorization: Bearer $USER_TOKEN" \
  http://localhost:3001/api/v1/admin/users

# IDOR vulnerabilities
for i in {1..100}; do
  curl -H "Authorization: Bearer $TOKEN" \
    http://localhost:3001/api/v1/trips/$i
done
```

### Phase 4: Input Validation (1 hour)

```python
# SQL Injection payloads
payloads = [
    "' OR '1'='1",
    "1; DROP TABLE trips; --",
    "1' UNION SELECT * FROM users--",
    "admin'--",
    "' OR 1=1--"
]

# XSS payloads
xss_payloads = [
    "<script>alert('XSS')</script>",
    "<img src=x onerror=alert('XSS')>",
    "javascript:alert('XSS')",
    "<svg onload=alert('XSS')>"
]

# Command injection
cmd_payloads = [
    "; ls -la",
    "| whoami",
    "&& cat /etc/passwd",
    "`id`"
]
```

### Phase 5: Business Logic Testing (1 hour)

```typescript
// Race condition testing
async function testRaceCondition() {
  const promises = [];

  // Try to use same resource simultaneously
  for (let i = 0; i < 10; i++) {
    promises.push(
      fetch('/api/v1/trips/clone', {
        method: 'POST',
        headers: { Authorization: `Bearer ${token}` },
        body: JSON.stringify({ tripId: 'same-trip-id' }),
      })
    );
  }

  const results = await Promise.all(promises);
  // Check if multiple clones were created
}

// Price manipulation
async function testPriceManipulation() {
  // Attempt negative prices
  await createActivity({ price: -100 });

  // Attempt price overflow
  await createActivity({ price: Number.MAX_SAFE_INTEGER + 1 });
}
```

### Automated Security Scanning

```yaml
# OWASP ZAP configuration
zap:
  target: http://localhost:3001
  scan_type: full
  authentication:
    type: bearer
    token: ${TEST_TOKEN}

  policies:
    - sql_injection: high
    - xss: high
    - csrf: medium
    - xxe: medium
    - security_headers: high
```

## OWASP Top 10 Compliance Checklist

### A01:2021 – Broken Access Control ✓

- [x] RLS policies enforced on all tables
- [x] Ownership middleware on all routes
- [x] RBAC implementation planned
- [x] Session validation on every request
- [x] CORS properly configured

### A02:2021 – Cryptographic Failures ✓

- [x] JWT secrets in environment variables
- [x] Strong secret generation (32+ bytes)
- [x] HTTPS enforced in production
- [x] Sensitive data encryption at rest
- [x] No hardcoded secrets in code

### A03:2021 – Injection ✓

- [x] Parameterized queries (Supabase)
- [x] Input validation with Zod schemas
- [x] Output encoding for XSS prevention
- [x] Command injection prevention
- [x] NoSQL injection protection

### A04:2021 – Insecure Design ✓

- [x] Threat modeling completed (STRIDE)
- [x] Security requirements defined
- [x] Secure design patterns used
- [x] Defense in depth approach
- [x] Fail securely principle

### A05:2021 – Security Misconfiguration ✓

- [x] Security headers configured
- [x] Error messages sanitized
- [x] Default credentials removed
- [x] Unnecessary features disabled
- [x] Security patches automated

### A06:2021 – Vulnerable Components ✓

- [x] Dependency scanning in CI/CD
- [x] Regular updates scheduled
- [x] Component inventory maintained
- [x] License compliance checked
- [x] Known vulnerabilities monitored

### A07:2021 – Authentication Failures ✓

- [x] Strong password requirements
- [x] Account lockout mechanisms
- [x] Session timeout implemented
- [x] MFA support planned
- [x] Secure password recovery

### A08:2021 – Software and Data Integrity ✓

- [x] Code signing in CI/CD
- [x] Dependency integrity checks
- [x] Audit logging implemented
- [x] Data validation at boundaries
- [x] Rollback capabilities

### A09:2021 – Security Logging & Monitoring ✓

- [x] Comprehensive audit logs
- [x] Security event monitoring
- [x] Alert thresholds defined
- [x] Log retention policies
- [x] Incident response plan

### A10:2021 – Server-Side Request Forgery ✓

- [x] URL validation implemented
- [x] Network segmentation
- [x] Allowlist for external calls
- [x] Response validation
- [x] Timeout controls

## Security Implementation Checklist

### Immediate Actions (Day 2)

- [x] Remove hardcoded secrets
- [x] Enable RLS on all tables
- [x] Implement ownership checks
- [x] Add security headers
- [x] Basic rate limiting

### Short-term (Week 1)

- [ ] Full OAuth2 implementation
- [ ] Advanced rate limiting
- [ ] Penetration testing
- [ ] Security monitoring
- [ ] Incident response procedures

### Long-term (Month 1)

- [ ] SOC2 preparation
- [ ] Bug bounty program
- [ ] Security training
- [ ] Compliance audits
- [ ] Advanced threat detection

## Notes

- Security is NOT optional
- Every shortcut today = breach tomorrow
- Test everything twice
- Document security decisions
- If unsure, choose the more secure option
- Get security right BEFORE users arrive
- Security is a continuous process, not a destination
