#!/usr/bin/env node

/**
 * Comprehensive AI System Test
 * Tests PDF import, model selection, prompt optimization, and end-to-end functionality
 */

// Load environment variables first
import { loadEnvironment } from '../utils/env-loader';
loadEnvironment();

import { enhancedAIRouterService } from '../services/enhanced-ai-router.service';
import { usageTrackingService } from '../services/usage-tracking.service';
import { modelSelectorService } from '../services/model-selector.service';
import { promptManager } from '../services/prompt-manager.service';
import { PDFImportDebugger } from '../utils/debug-pdf-import';
import { getSupabaseClient } from '../lib/supabase';
import { logger } from '../utils/logger';

// Test data for different complexity levels
const TEST_CONTENT = {
  simple: `
    Day 1: Paris
    - Visit Eiffel Tower at 10:00 AM
    - Lunch at Café de Flore at 1:00 PM
  `,
  medium: `
    Day 1: Paris, France
    - 9:00 AM: Check into Hotel Le Meurice (228 Rue de Rivoli)
    - 10:30 AM: Visit Eiffel Tower, take elevator to top (€29.40)
    - 1:00 PM: Lunch at Café de Flore (172 Boulevard Saint-Germain)
    - 3:00 PM: Louvre Museum, see Mona Lisa (€17)
    - 7:00 PM: Seine River cruise (€15)
    
    Day 2: Rome, Italy
    - Flight: Paris CDG to Rome FCO at 8:00 AM (€120)
    - 11:00 AM: Colosseum tour with guide (€35)
    - 2:00 PM: Vatican Museums and Sistine Chapel (€25)
    - 6:00 PM: Dinner in Trastevere district
  `,
  complex: `
    European Adventure - 14 Days
    
    Days 1-3: Paris, France
    Accommodation: Hotel Le Meurice (€450/night)
    - Day 1: Arrival, Eiffel Tower, Seine cruise
    - Day 2: Louvre, Champs-Élysées, Arc de Triomphe
    - Day 3: Versailles day trip, Montmartre evening
    
    Days 4-6: Rome, Italy
    Transport: High-speed train Paris-Rome (€89)
    Accommodation: Hotel de Russie (€380/night)
    - Day 4: Colosseum, Roman Forum, Palatine Hill
    - Day 5: Vatican City, St. Peter's Basilica, Sistine Chapel
    - Day 6: Trastevere, Villa Borghese, Spanish Steps
    
    Days 7-9: Barcelona, Spain
    Transport: Flight Rome-Barcelona (€145)
    Accommodation: Hotel Casa Fuster (€320/night)
    - Day 7: Sagrada Familia, Park Güell
    - Day 8: Gothic Quarter, Las Ramblas, Picasso Museum
    - Day 9: Beach day at Barceloneta, Flamenco show
    
    Days 10-12: Amsterdam, Netherlands
    Transport: Flight Barcelona-Amsterdam (€120)
    Accommodation: The Hoxton (€280/night)
    - Day 10: Anne Frank House, Jordaan district
    - Day 11: Van Gogh Museum, Vondelpark, canal cruise
    - Day 12: Keukenhof Gardens day trip (seasonal)
    
    Days 13-14: London, England
    Transport: Eurostar Amsterdam-London (€95)
    Accommodation: The Savoy (€520/night)
    - Day 13: British Museum, Tower of London, Thames cruise
    - Day 14: Buckingham Palace, Hyde Park, West End show
    
    Total estimated cost: €8,500 for 2 people
    Best time to visit: April-June or September-October
  `
};

async function testComprehensiveAISystem() {
  console.log('🚀 Starting Comprehensive AI System Test...\n');

  try {
    // Test 1: Database Connection and Basic Setup
    console.log('1. Testing Database Connection...');
    const { data: testQuery, error } = await getSupabaseClient()
      .from('ai_model_configs')
      .select('id, name, provider')
      .limit(1);

    if (error) {
      throw new Error(`Database connection failed: ${error.message}`);
    }
    console.log('✅ Database connection successful');

    // Test 2: Model Configuration Verification
    console.log('\n2. Testing Model Configurations...');
    const { data: modelConfigs } = await getSupabaseClient()
      .from('ai_model_configs')
      .select('*')
      .eq('is_active', true);

    console.log('✅ Active Model Configurations:');
    modelConfigs?.forEach(config => {
      console.log(`   - ${config.id}: ${config.name} (${config.provider})`);
      console.log(`     Limits: ${config.daily_request_limit} req/day, ${config.rpm_limit} req/min, ${config.tpm_limit} tokens/min`);
    });

    // Test 3: Usage Tracking System
    console.log('\n3. Testing Usage Tracking System...');
    for (const config of modelConfigs || []) {
      const usage = await usageTrackingService.getCurrentUsage(config.id);
      const isAvailable = await usageTrackingService.isModelAvailable(config.id);
      console.log(`   ${config.id}: ${usage.requestCount} requests, Available: ${isAvailable}`);
    }

    // Test 4: Prompt Manager Testing
    console.log('\n4. Testing Prompt Manager for Each Model...');
    for (const config of modelConfigs || []) {
      const systemPrompt = promptManager.getSystemPrompt(config.id);
      const formatInstructions = promptManager.getFormatInstructions(config.id);
      
      console.log(`\n   📝 ${config.id} Prompts:`);
      console.log(`      System Prompt Length: ${systemPrompt.length} chars`);
      console.log(`      Format Instructions Length: ${formatInstructions.length} chars`);
      console.log(`      System Prompt Preview: ${systemPrompt.substring(0, 100)}...`);
    }

    // Test 5: Model Selection for Different Content Complexities
    console.log('\n5. Testing Model Selection for Different Complexities...');
    for (const [complexity, content] of Object.entries(TEST_CONTENT)) {
      console.log(`\n   Testing ${complexity} content...`);
      const selection = await modelSelectorService.selectModel(content);
      const tokenEstimate = modelSelectorService.estimateTokens(content);
      
      console.log(`   ✅ Selected Model: ${selection.modelId}`);
      console.log(`   📊 Token Estimate: ${tokenEstimate.inputTokens} input, ${tokenEstimate.outputTokens} output`);
      console.log(`   💰 Estimated Cost: $${selection.estimatedCost.toFixed(6)}`);
      console.log(`   🔄 Fallback Chain: ${selection.fallbackChain.join(' → ')}`);
      console.log(`   📝 Reason: ${selection.reason}`);
    }

    return true;

  } catch (error) {
    console.error('❌ Comprehensive test failed:', error);
    return false;
  }
}

// Run the test if this script is executed directly
if (require.main === module) {
  testComprehensiveAISystem()
    .then((success) => {
      if (success) {
        console.log('\n🎉 All comprehensive tests completed successfully!');
        console.log('\nNext: Run prompt optimization tests...');
      }
      process.exit(success ? 0 : 1);
    })
    .catch((error) => {
      console.error('Test execution failed:', error);
      process.exit(1);
    });
}

export { testComprehensiveAISystem };
