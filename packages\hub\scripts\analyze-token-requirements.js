/**
 * Token Analysis Script for PDF Import
 * Analyzes typical output token requirements for different itinerary complexities
 */

// Sample itinerary structures to analyze token requirements
const SAMPLE_ITINERARIES = {
  simple: {
    title: "Weekend Paris Trip",
    destination: "Paris, France",
    startDate: "2024-06-01",
    endDate: "2024-06-03",
    activities: [
      {
        title: "Arrive at Charles de Gaulle Airport",
        type: "transport",
        startTime: "10:00",
        location: "Charles de Gaulle Airport, Paris",
        day: 1
      },
      {
        title: "Check into Hotel",
        type: "accommodation",
        startTime: "15:00",
        location: "Le Meurice, Paris",
        price: 500,
        currency: "EUR",
        day: 1
      },
      {
        title: "Visit Eiffel Tower",
        type: "sightseeing",
        startTime: "10:00",
        location: "Eiffel Tower, Paris",
        price: 25,
        currency: "EUR",
        day: 2
      },
      {
        title: "Seine River Cruise",
        type: "tour",
        startTime: "19:00",
        location: "Seine River, Paris",
        price: 120,
        currency: "EUR",
        day: 2
      },
      {
        title: "Departure",
        type: "transport",
        startTime: "14:00",
        location: "Charles de Gaulle Airport, Paris",
        day: 3
      }
    ]
  },

  complex: {
    title: "15-Day European Adventure: London, Madrid, Lisbon and Porto",
    destination: "Europe",
    startDate: "2024-06-01",
    endDate: "2024-06-15",
    activities: Array.from({ length: 45 }, (_, i) => ({
      title: `Activity ${i + 1}`,
      type: ["sightseeing", "dining", "transport", "accommodation", "tour", "shopping"][i % 6],
      startTime: `${9 + (i % 12)}:00`,
      location: `Location ${i + 1}, ${["London", "Madrid", "Lisbon", "Porto"][Math.floor(i / 12)]}`,
      price: Math.floor(Math.random() * 200) + 20,
      currency: ["GBP", "EUR", "EUR", "EUR"][Math.floor(i / 12)],
      day: Math.floor(i / 3) + 1,
      description: `Detailed description for activity ${i + 1} with additional context and recommendations.`,
      notes: `Important notes and tips for activity ${i + 1}.`
    }))
  },

  veryComplex: {
    title: "30-Day World Tour: Multiple Countries and Cities",
    destination: "Multiple Countries",
    startDate: "2024-06-01",
    endDate: "2024-06-30",
    activities: Array.from({ length: 120 }, (_, i) => ({
      title: `Comprehensive Activity ${i + 1}`,
      type: ["sightseeing", "dining", "transport", "accommodation", "tour", "shopping", "entertainment", "activity"][i % 8],
      startTime: `${8 + (i % 14)}:00`,
      location: `Detailed Location ${i + 1}, City ${Math.floor(i / 10)}, Country ${Math.floor(i / 30)}`,
      price: Math.floor(Math.random() * 500) + 50,
      currency: ["USD", "EUR", "GBP", "JPY", "AUD"][Math.floor(i / 24)],
      day: Math.floor(i / 4) + 1,
      description: `Comprehensive description for activity ${i + 1} including historical context, cultural significance, and detailed recommendations for the best experience.`,
      notes: `Detailed notes including booking requirements, dress code, weather considerations, and local customs for activity ${i + 1}.`,
      bookingUrl: `https://example.com/booking/${i + 1}`,
      duration: Math.floor(Math.random() * 4) + 1
    }))
  }
};

function estimateTokens(text) {
  // Rough estimation: 1 token ≈ 4 characters for English text
  // JSON structure adds overhead, so we use 3.5 characters per token
  return Math.ceil(text.length / 3.5);
}

function analyzeItinerary(name, itinerary) {
  const jsonString = JSON.stringify(itinerary, null, 2);
  const compactJsonString = JSON.stringify(itinerary);
  
  const formattedTokens = estimateTokens(jsonString);
  const compactTokens = estimateTokens(compactJsonString);
  
  console.log(`\n📊 ${name.toUpperCase()} ITINERARY ANALYSIS:`);
  console.log(`   Activities: ${itinerary.activities.length}`);
  console.log(`   Days: ${itinerary.activities.reduce((max, a) => Math.max(max, a.day || 0), 0)}`);
  console.log(`   Formatted JSON: ${jsonString.length} chars ≈ ${formattedTokens} tokens`);
  console.log(`   Compact JSON: ${compactJsonString.length} chars ≈ ${compactTokens} tokens`);
  
  return {
    name,
    activities: itinerary.activities.length,
    days: itinerary.activities.reduce((max, a) => Math.max(max, a.day || 0), 0),
    formattedTokens,
    compactTokens,
    jsonLength: compactJsonString.length
  };
}

function analyzeTokenRequirements() {
  console.log('🔍 ANALYZING TOKEN REQUIREMENTS FOR PDF IMPORT\n');
  console.log('This analysis helps determine optimal output token limits for different AI models.\n');
  
  const results = [];
  
  // Analyze each sample itinerary
  Object.entries(SAMPLE_ITINERARIES).forEach(([name, itinerary]) => {
    const result = analyzeItinerary(name, itinerary);
    results.push(result);
  });
  
  // Summary analysis
  console.log('\n📈 SUMMARY ANALYSIS:');
  console.log('┌─────────────┬────────────┬──────┬─────────────┬─────────────┐');
  console.log('│ Complexity  │ Activities │ Days │ Compact     │ Formatted   │');
  console.log('│             │            │      │ Tokens      │ Tokens      │');
  console.log('├─────────────┼────────────┼──────┼─────────────┼─────────────┤');
  
  results.forEach(result => {
    console.log(`│ ${result.name.padEnd(11)} │ ${String(result.activities).padStart(10)} │ ${String(result.days).padStart(4)} │ ${String(result.compactTokens).padStart(11)} │ ${String(result.formattedTokens).padStart(11)} │`);
  });
  
  console.log('└─────────────┴────────────┴──────┴─────────────┴─────────────┘');
  
  // Recommendations
  console.log('\n💡 RECOMMENDATIONS:');
  
  const maxCompactTokens = Math.max(...results.map(r => r.compactTokens));
  const maxFormattedTokens = Math.max(...results.map(r => r.formattedTokens));
  
  console.log(`\n🎯 OPTIMAL TOKEN LIMITS:`);
  console.log(`   • Gemini 2.0 (8K limit): ✅ Handles up to ${Math.floor(8192 / maxCompactTokens * results[results.length - 1].activities)} activities`);
  console.log(`   • DeepSeek v3 (164K limit): ✅ Handles ${Math.floor(164000 / maxCompactTokens * results[results.length - 1].activities)}+ activities`);
  console.log(`   • Kimi-K2 (66K limit): ✅ Handles ${Math.floor(66000 / maxCompactTokens * results[results.length - 1].activities)}+ activities`);
  
  console.log(`\n🚨 CURRENT ISSUES:`);
  if (maxCompactTokens > 8192) {
    console.log(`   • Gemini 2.0 (8K): ❌ Insufficient for very complex itineraries (${maxCompactTokens} tokens needed)`);
  } else {
    console.log(`   • Gemini 2.0 (8K): ✅ Sufficient for current test cases`);
  }
  
  console.log(`\n🔄 RECOMMENDED FALLBACK ORDER:`);
  console.log(`   1. Gemini 2.0 (8K output) - Fast, free, good for simple-medium complexity`);
  console.log(`   2. Kimi-K2 (66K output) - Faster than DeepSeek, good for complex itineraries`);
  console.log(`   3. DeepSeek v3 (164K output) - Highest capacity for very complex itineraries`);
  
  console.log(`\n⚡ PERFORMANCE CONSIDERATIONS:`);
  console.log(`   • Kimi-K2: Faster response times than DeepSeek`);
  console.log(`   • DeepSeek v3: Higher capacity but slower`);
  console.log(`   • Consider dynamic model selection based on estimated complexity`);
  
  return results;
}

// Run the analysis
if (require.main === module) {
  analyzeTokenRequirements();
}

module.exports = { analyzeTokenRequirements, SAMPLE_ITINERARIES, estimateTokens };
