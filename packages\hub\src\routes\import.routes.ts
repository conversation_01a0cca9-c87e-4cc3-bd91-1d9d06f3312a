import { Router } from 'express';
import multer from 'multer';
import { ImportController } from '../controllers/import.controller';
import { authenticateSupabaseUser } from '../middleware/supabase-auth.middleware';
import { validateRequest } from '../middleware/validation.middleware';
import { parseImportSchema, uploadImportSchema } from '../schemas/import.schema';
import { importRateLimit } from '../middleware/rate-limit.middleware';
import { validateTextContent, validateFileUpload } from '../middleware/file-validation.middleware';
import { validateContentLength } from '../middleware/request-size.middleware';
import { SSEImportController } from '../controllers/sse-import.controller';
import { getAIParserService } from '../services/ai-parser.service';
import { pdfParserService } from '../services/pdf-parser.service';
import { createSuccessResponse, createErrorResponse } from '@travelviz/shared';
import { logger } from '../utils/logger';

const router: Router = Router();
const importController = new ImportController();
const sseImportController = new SSEImportController();

// Configure multer for file uploads
const upload = multer({
  storage: multer.memoryStorage(),
  limits: {
    fileSize: 10 * 1024 * 1024, // 10MB limit
  },
  fileFilter: (req, file, cb) => {
    // Only accept PDF files
    if (file.mimetype === 'application/pdf') {
      cb(null, true);
    } else {
      cb(new Error('Only PDF files are allowed'));
    }
  }
});

// Parse and create trip from AI conversation text (direct)
router.post(
  '/parse-direct',
  validateContentLength(50 * 1024), // 50KB limit (50,000 chars) - MUST run first
  importRateLimit, // Apply rate limiting before authentication
  authenticateSupabaseUser,
  validateRequest(parseImportSchema),
  validateTextContent, // Additional server-side validation
  (req, res) => importController.parseAndCreateTrip(req, res)
);

// Parse with SSE progress updates
router.post(
  '/parse',
  validateContentLength(50 * 1024), // 50KB limit (50,000 chars) - MUST run first
  importRateLimit, // Apply rate limiting before authentication
  authenticateSupabaseUser,
  validateRequest(parseImportSchema),
  validateTextContent, // Additional server-side validation
  (req, res) => sseImportController.parseText(req, res)
);

// Parse and create trip from uploaded file (PDF)
router.post(
  '/upload',
  importRateLimit, // Apply rate limiting before authentication
  authenticateSupabaseUser,
  upload.single('file'), // Handle single file upload with field name 'file'
  validateRequest(uploadImportSchema), // Validate request body (source)
  validateFileUpload, // Additional file validation
  (req, res) => importController.parseAndCreateTripFromFile(req, res)
);

// SSE endpoint for real-time import progress
router.get(
  '/progress/:sessionId',
  authenticateSupabaseUser,
  (req, res) => sseImportController.streamProgress(req, res)
);

// Get session status (non-SSE)
router.get(
  '/status/:sessionId',
  authenticateSupabaseUser,
  (req, res) => sseImportController.getSessionStatus(req, res)
);

// Create trip from completed import
router.post(
  '/:sessionId/create-trip',
  authenticateSupabaseUser,
  (req, res) => sseImportController.createTripFromImport(req, res)
);

// NEW MVP ENDPOINTS - Simplified AI parsing

// Start simple AI parsing (no SSE)
router.post(
  '/parse-simple',
  validateContentLength(50 * 1024),
  importRateLimit,
  authenticateSupabaseUser,
  async (req, res) => {
    try {
      const { content, source = 'unknown' } = req.body;
      const userId = req.user!.id;

      if (!content || content.trim().length < 100) {
        return res.status(400).json(
          createErrorResponse('Content too short. Please provide a complete conversation.')
        );
      }

      const sessionId = await getAIParserService().createParseSession(content, source, userId);

      logger.info('Simple parse session created', { sessionId, userId, source });

      return res.json(
        createSuccessResponse({
          importId: sessionId,
          message: 'Parsing started'
        })
      );
    } catch (error) {
      logger.error('Simple parse initiation error:', { 
        error: error instanceof Error ? error.message : String(error),
        stack: error instanceof Error ? error.stack : undefined,
        userId: req.user?.id,
        source: req.body.source
      });
      return res.status(500).json(
        createErrorResponse('Failed to start parsing')
      );
    }
  }
);

// Get simple parse session status
router.get(
  '/parse-simple/:importId',
  authenticateSupabaseUser,
  async (req, res) => {
    try {
      const { importId } = req.params;

      const session = await getAIParserService().getSession(importId);
      
      if (!session) {
        return res.status(404).json(
          createErrorResponse('Parse session not found')
        );
      }

      return res.json(
        createSuccessResponse({
          importId: session.id,
          status: session.status,
          progress: session.progress,
          currentStep: session.currentStep,
          result: session.result,
          error: session.error,
          startedAt: session.startedAt,
          completedAt: session.completedAt,
        })
      );
    } catch (error) {
      logger.error('Get simple parse session error:', { 
        error: error instanceof Error ? error.message : String(error),
        stack: error instanceof Error ? error.stack : undefined,
        importId: req.params.importId
      });
      return res.status(500).json(
        createErrorResponse('Failed to get parse status')
      );
    }
  }
);

// Create trip from simple parse session
router.post(
  '/parse-simple/:importId/create-trip',
  authenticateSupabaseUser,
  async (req, res) => {
    try {
      const { importId } = req.params;
      const userId = req.user!.id;
      const { edits } = req.body;

      const tripId = await getAIParserService().createTripFromParse(
        importId,
        userId,
        edits
      );

      logger.info('Trip created from simple parse', { importId, tripId, userId });

      return res.json(
        createSuccessResponse({
          tripId,
          message: 'Trip created successfully'
        })
      );
    } catch (error) {
      logger.error('Simple trip creation error:', { 
        error: error instanceof Error ? error.message : String(error) 
      });
      return res.status(500).json(
        createErrorResponse(
          error instanceof Error ? error.message : 'Failed to create trip'
        )
      );
    }
  }
);

// PDF Import endpoint with SSE support
router.post(
  '/pdf',
  importRateLimit,
  authenticateSupabaseUser,
  upload.single('file'),
  async (req, res) => {
    try {
      const userId = req.user!.id;
      const file = req.file;

      if (!file) {
        return res.status(400).json(
          createErrorResponse('No file uploaded')
        );
      }

      // Validate PDF
      if (!pdfParserService.validatePDF(file.buffer)) {
        return res.status(400).json(
          createErrorResponse('Invalid PDF file')
        );
      }

      // Parse PDF and create session - need to use the instance method directly
      const result = await pdfParserService.instance.parsePDF(
        file.buffer,
        userId,
        file.originalname
      );

      logger.info('PDF import session created', { 
        sessionId: result.sessionId, 
        userId, 
        source: result.source,
        pageCount: result.metadata.pageCount
      });

      return res.json(
        createSuccessResponse({
          sessionId: result.sessionId,
          sseUrl: `/api/import/progress/${result.sessionId}`,
          source: result.source,
          metadata: result.metadata,
          estimatedTime: 30 // seconds
        })
      );
    } catch (error) {
      logger.error('PDF import error:', { 
        error: error instanceof Error ? error.message : String(error),
        stack: error instanceof Error ? error.stack : undefined,
        userId: req.user?.id,
        filename: req.file?.originalname
      });
      return res.status(500).json(
        createErrorResponse(
          error instanceof Error ? error.message : 'Failed to import PDF'
        )
      );
    }
  }
);

export default router;