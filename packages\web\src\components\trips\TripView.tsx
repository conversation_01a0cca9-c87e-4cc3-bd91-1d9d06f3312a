'use client';

import React, { useState, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { format } from 'date-fns';
import { 
  Calendar, 
  MapPin, 
  Share2, 
  Download, 
  Edit, 
  MoreVertical,
  List,
  Map as MapIcon,
  CalendarDays,
  DollarSign
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Tabs, TabsList, TabsTrigger, TabsContent } from '@/components/ui/tabs';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { VirtualTimeline } from '@/components/timeline';
import { PerformantMap } from '@/components/map';
import { ActivityList } from '@/components/activities';
import { Activity, Trip } from '@travelviz/shared';
import { cn } from '@/lib/utils';

interface TripViewProps {
  trip: Trip;
  activities: Activity[];
  onActivityUpdate?: (activityId: string, updates: Partial<Activity>) => void;
  onActivityReorder?: (newOrder: Activity[]) => void;
  onTripUpdate?: (updates: Partial<Trip>) => void;
  onShare?: () => void;
  onExport?: () => void;
  className?: string;
}

type ViewMode = 'timeline' | 'map' | 'list';

export function TripView({
  trip,
  activities,
  onActivityUpdate,
  onActivityReorder,
  onTripUpdate,
  onShare,
  onExport,
  className,
}: TripViewProps) {
  const [viewMode, setViewMode] = useState<ViewMode>('timeline');
  const [selectedActivity, setSelectedActivity] = useState<Activity | null>(null);
  const [isEditing, setIsEditing] = useState(false);

  const handleActivityClick = useCallback((activity: Activity) => {
    setSelectedActivity(activity);
  }, []);

  const totalBudget = activities.reduce((sum, activity) => {
    return sum + (activity.price || 0);
  }, 0);

  const startDate = trip.start_date ? new Date(trip.start_date) : null;
  const endDate = trip.end_date ? new Date(trip.end_date) : null;
  const tripDuration = startDate && endDate 
    ? Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24)) + 1
    : 0;

  return (
    <div className={cn("flex flex-col h-full", className)}>
      {/* Header */}
      <div className="border-b bg-white px-6 py-4">
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <h1 className="text-2xl font-bold text-gray-900">{trip.title}</h1>
            {trip.description && (
              <p className="mt-1 text-gray-600">{trip.description}</p>
            )}
            
            <div className="flex items-center gap-6 mt-3 text-sm text-gray-500">
              {startDate && endDate && (
                <div className="flex items-center gap-1">
                  <Calendar className="w-4 h-4" />
                  <span>
                    {format(startDate, 'MMM d')} - {format(endDate, 'MMM d, yyyy')}
                  </span>
                  <span className="text-gray-400">({tripDuration} days)</span>
                </div>
              )}
              
              {trip.destination && (
                <div className="flex items-center gap-1">
                  <MapPin className="w-4 h-4" />
                  <span>{trip.destination}</span>
                </div>
              )}
              
              {totalBudget > 0 && (
                <div className="flex items-center gap-1">
                  <DollarSign className="w-4 h-4" />
                  <span>${totalBudget.toFixed(2)}</span>
                </div>
              )}
            </div>
          </div>

          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setIsEditing(!isEditing)}
            >
              <Edit className="w-4 h-4 mr-1" />
              Edit
            </Button>
            
            <Button
              variant="outline"
              size="sm"
              onClick={onShare}
            >
              <Share2 className="w-4 h-4 mr-1" />
              Share
            </Button>
            
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="icon">
                  <MoreVertical className="w-4 h-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem onClick={onExport}>
                  <Download className="w-4 h-4 mr-2" />
                  Export
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem className="text-red-600">
                  Delete Trip
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
      </div>

      {/* View Tabs */}
      <Tabs 
        value={viewMode} 
        onValueChange={(value) => setViewMode(value as ViewMode)}
        className="flex-1 flex flex-col"
      >
        <TabsList className="grid w-full max-w-md mx-auto grid-cols-3">
          <TabsTrigger value="timeline" className="flex items-center gap-2">
            <CalendarDays className="w-4 h-4" />
            Timeline
          </TabsTrigger>
          <TabsTrigger value="map" className="flex items-center gap-2">
            <MapIcon className="w-4 h-4" />
            Map
          </TabsTrigger>
          <TabsTrigger value="list" className="flex items-center gap-2">
            <List className="w-4 h-4" />
            List
          </TabsTrigger>
        </TabsList>

        <div className="flex-1 overflow-hidden">
          <AnimatePresence mode="wait">
            <TabsContent 
              value="timeline" 
              className="h-full m-0"
              forceMount={viewMode === 'timeline' ? true : undefined}
            >
              {viewMode === 'timeline' && (
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -20 }}
                  className="h-full"
                >
                  <VirtualTimeline
                    activities={activities}
                    onActivityUpdate={onActivityUpdate}
                    onActivityReorder={onActivityReorder}
                    onActivityClick={handleActivityClick}
                    className="h-full"
                  />
                </motion.div>
              )}
            </TabsContent>

            <TabsContent 
              value="map" 
              className="h-full m-0"
              forceMount={viewMode === 'map' ? true : undefined}
            >
              {viewMode === 'map' && (
                <motion.div
                  initial={{ opacity: 0, scale: 0.95 }}
                  animate={{ opacity: 1, scale: 1 }}
                  exit={{ opacity: 0, scale: 0.95 }}
                  className="h-full"
                >
                  <PerformantMap
                    activities={activities}
                    selectedActivity={selectedActivity}
                    onActivityClick={handleActivityClick}
                    className="h-full"
                  />
                </motion.div>
              )}
            </TabsContent>

            <TabsContent 
              value="list" 
              className="h-full m-0"
              forceMount={viewMode === 'list' ? true : undefined}
            >
              {viewMode === 'list' && (
                <motion.div
                  initial={{ opacity: 0, x: 20 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: -20 }}
                  className="h-full"
                >
                  <ActivityList
                    activities={activities}
                    onActivityClick={handleActivityClick}
                    onActivityUpdate={onActivityUpdate}
                    className="h-full"
                  />
                </motion.div>
              )}
            </TabsContent>
          </AnimatePresence>
        </div>
      </Tabs>
    </div>
  );
}
