import { 
  Trip, 
  Activity,
  Profile,
  TripResponse,
  ActivityResponse,
  UserProfileResponse,
  CreateTripRequest,
  CreateActivityRequest
} from '@travelviz/shared';

export class MapperService {
  /**
   * Map database trip to API response
   */
  static mapTripToResponse(
    trip: Trip,
    userId?: string,
    access?: { canView: boolean; canEdit: boolean; canDelete: boolean; isOwner: boolean }
  ): TripResponse {
    return {
      id: trip.id,
      userId: trip.user_id,
      title: trip.title,
      description: trip.description,
      destination: trip.destination,
      start_date: trip.start_date,
      end_date: trip.end_date,
      status: trip.status,
      visibility: trip.visibility,
      cover_image: trip.cover_image,
      metadata: trip.metadata,
      tags: trip.tags,
      budget_amount: trip.budget_amount,
      budget_currency: trip.budget_currency,
      views: trip.views,
      created_at: trip.created_at,
      updated_at: trip.updated_at,
      activities: trip.activities,
      activitiesCount: trip.activities?.length || 0,
      isOwner: access?.isOwner ?? (userId === trip.user_id),
      canEdit: access?.canEdit ?? (userId === trip.user_id),
    };
  }

  /**
   * Map database activity to API response
   */
  static mapActivityToResponse(activity: Activity, canEdit = true): ActivityResponse {
    return {
      id: activity.id,
      tripId: activity.trip_id,
      title: activity.title,
      description: activity.description,
      type: activity.type,
      position: activity.position,
      start_time: activity.start_time,
      end_time: activity.end_time,
      location: activity.location,
      location_lat: activity.location_lat,
      location_lng: activity.location_lng,
      price: activity.price,
      currency: activity.currency,
      booking_reference: activity.booking_reference,
      booking_url: activity.booking_url,
      notes: activity.notes,
      metadata: activity.metadata,
      attachments: activity.attachments,
      created_at: activity.created_at,
      updated_at: activity.updated_at,
      canEdit,
      affiliateUrl: activity.affiliate_url || undefined,
    };
  }

  /**
   * Map database profile to API response
   */
  static mapProfileToResponse(profile: Profile): UserProfileResponse {
    return {
      id: profile.id,
      email: profile.email,
      name: profile.name,
      avatarUrl: profile.avatar_url,
      bio: profile.bio,
      preferences: profile.preferences,
      createdAt: profile.created_at,
      updatedAt: profile.updated_at,
    };
  }

  /**
   * Map create trip request to internal data
   */
  static mapCreateTripRequest(request: CreateTripRequest, userId: string): Record<string, unknown> {
    return {
      user_id: userId,
      title: request.title,
      description: request.description,
      destination: request.destination,
      start_date: request.startDate,
      end_date: request.endDate,
      status: request.status || 'draft',
      visibility: request.visibility || 'private',
      cover_image: request.coverImage,
      tags: request.tags || [],
      budget_amount: request.budgetAmount,
      budget_currency: request.budgetCurrency || 'USD',
      metadata: {},
    };
  }

  /**
   * Map create activity request to internal data
   */
  static mapCreateActivityRequest(request: CreateActivityRequest, tripId: string): Record<string, unknown> {
    return {
      trip_id: tripId,
      title: request.title,
      description: request.description,
      type: request.type || 'activity',
      start_time: request.startTime,
      end_time: request.endTime,
      location: request.location,
      location_lat: request.locationLat,
      location_lng: request.locationLng,
      price: request.price,
      currency: request.currency || 'USD',
      booking_reference: request.bookingReference,
      booking_url: request.bookingUrl,
      notes: request.notes,
      attachments: request.attachments || [],
      metadata: request.metadata || {},
    };
  }

  /**
   * Map update request to database format
   */
  static mapUpdateRequest(request: Record<string, unknown>): Record<string, unknown> {
    const mapped: Record<string, unknown> = {};
    
    // Map camelCase to snake_case for database
    const mappings: Record<string, string> = {
      startDate: 'start_date',
      endDate: 'end_date',
      coverImage: 'cover_image',
      budgetAmount: 'budget_amount',
      budgetCurrency: 'budget_currency',
      startTime: 'start_time',
      endTime: 'end_time',
      locationLat: 'location_lat',
      locationLng: 'location_lng',
      bookingReference: 'booking_reference',
      bookingUrl: 'booking_url',
    };

    for (const [key, value] of Object.entries(request)) {
      const dbKey = mappings[key] || key;
      if (value !== undefined) {
        mapped[dbKey] = value;
      }
    }

    return mapped;
  }
}