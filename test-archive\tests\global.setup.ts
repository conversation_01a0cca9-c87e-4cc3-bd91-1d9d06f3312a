import { test as setup, expect } from '@playwright/test';
import path from 'path';

const authFile = path.join(__dirname, '../playwright/.auth/user.json');

setup('authenticate', async ({ page }) => {
  // Go to the login page
  await page.goto('/login');
  
  // Fill in the login form
  await page.fill('input[name="email"]', '<EMAIL>');
  await page.fill('input[name="password"]', 'Flaremmk123!');
  
  // Submit the form
  await page.click('button[type="submit"]');
  
  // Wait for successful login - should redirect to dashboard
  await page.waitForURL('**/dashboard', { timeout: 10000 });
  
  // Verify we're logged in
  await expect(page.locator('h1')).toContainText(/Dashboard|My Trips/i);
  
  // Save storage state
  await page.context().storageState({ path: authFile });
});