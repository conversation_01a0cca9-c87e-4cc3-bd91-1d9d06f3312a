import { test, expect } from '@playwright/test';
import path from 'path';
import fs from 'fs';

const TEST_USER = {
  email: '<EMAIL>',
  password: 'Flaremmk123!'
};

// Load test data
const testDataPath = path.join(__dirname, '..', 'test-data', 'short-itinerary.txt');
const fullItineraryPath = path.join(__dirname, '..', 'test-data', 'european-itinerary.txt');
const shortItinerary = fs.readFileSync(testDataPath, 'utf-8');
const fullItinerary = fs.readFileSync(fullItineraryPath, 'utf-8');

const AI_MODELS = [
  { 
    id: 'anthropic/claude-3-haiku', 
    name: 'Claude 3 Haiku',
    source: 'claude',
    expectedSuccess: true 
  },
  { 
    id: 'openrouter/cypher-alpha:free', 
    name: '<PERSON><PERSON> <PERSON> (Free)',
    source: 'chatgpt',
    expectedSuccess: false // Based on previous test
  },
  { 
    id: 'deepseek/deepseek-chat-v3-0324:free', 
    name: 'DeepSeek Chat v3',
    source: 'chatgpt',
    expectedSuccess: true 
  },
  { 
    id: 'deepseek/deepseek-r1-0528:free', 
    name: 'DeepSeek R1',
    source: 'chatgpt',
    expectedSuccess: true 
  },
  { 
    id: 'google/gemini-2.0-flash-001', 
    name: 'Gemini 2.0 Flash',
    source: 'gemini',
    expectedSuccess: true 
  },
  { 
    id: 'google/gemini-2.5-flash-preview-05-20', 
    name: 'Gemini 2.5 Flash Preview',
    source: 'gemini',
    expectedSuccess: true 
  }
];

// Test results storage
interface TestResult {
  model: string;
  modelName: string;
  success: boolean;
  responseTime: number;
  tripCreated: boolean;
  tripTitle?: string;
  activitiesCount?: number;
  locationsCount?: number;
  pricesCount?: number;
  datesCount?: number;
  error?: string;
  screenshot?: string;
}

const testResults: TestResult[] = [];

test.describe('Comprehensive AI Import Tests', () => {
  test.use({ storageState: 'playwright/.auth/user.json' });

  test.beforeAll(async () => {
    // Create results directory
    const resultsDir = path.join(__dirname, '..', 'test-results', 'ai-import');
    if (!fs.existsSync(resultsDir)) {
      fs.mkdirSync(resultsDir, { recursive: true });
    }
  });

  for (const model of AI_MODELS) {
    test(`Test ${model.name} - Text Paste Mode`, async ({ page }) => {
      const startTime = Date.now();
      const result: TestResult = {
        model: model.id,
        modelName: model.name,
        success: false,
        responseTime: 0,
        tripCreated: false
      };

      try {
        // Navigate to upload page
        await page.goto('/upload');
        
        // Switch to text paste mode
        await page.click('button[role="tab"]:has-text("Paste Text")');
        
        // Select source
        await page.click('#ai-source');
        await page.click(`[role="option"]:has-text("${model.source === 'chatgpt' ? 'ChatGPT' : model.source === 'claude' ? 'Claude' : 'Google Gemini'}")`);
        
        // Select model
        await page.click('#ai-model');
        await page.click(`[role="option"]:has-text("${model.name}")`);
        
        // Paste text
        await page.fill('textarea#travel-text', shortItinerary);
        
        // Click process button
        await page.click('button:has-text("Process Text")');
        
        // Wait for processing with appropriate timeout
        const maxTimeout = 60000; // 60 seconds for AI processing
        
        try {
          // Wait for either success or error
          await Promise.race([
            page.waitForSelector('h2:has-text("Import Complete!")', { timeout: maxTimeout }),
            page.waitForSelector('.error-message, [role="alert"]', { timeout: maxTimeout })
          ]);
          
          result.responseTime = Date.now() - startTime;
          
          // Check if import was successful
          const successElement = await page.locator('h2:has-text("Import Complete!")').isVisible();
          
          if (successElement) {
            result.success = true;
            result.tripCreated = true;
            
            // Extract trip details
            const tripTitle = await page.locator('h4.text-2xl').textContent();
            result.tripTitle = tripTitle || undefined;
            
            // Extract metrics
            const metricsText = await page.locator('.grid').textContent();
            
            // Activities count
            const activitiesMatch = metricsText?.match(/Activities & Events(\d+)/);
            result.activitiesCount = activitiesMatch ? parseInt(activitiesMatch[1]) : 0;
            
            // Locations count
            const locationsMatch = metricsText?.match(/Unique Locations(\d+)/);
            result.locationsCount = locationsMatch ? parseInt(locationsMatch[1]) : 0;
            
            // Extract other metrics if visible
            const durationMatch = metricsText?.match(/(\d+) days/);
            if (durationMatch) {
              result.datesCount = parseInt(durationMatch[1]);
            }
            
            console.log(`✅ ${model.name}: Import successful`);
            console.log(`   Title: ${result.tripTitle}`);
            console.log(`   Activities: ${result.activitiesCount}`);
            console.log(`   Locations: ${result.locationsCount}`);
            
            // Click to view the trip
            await page.click('button:has-text("View Trip Itinerary")');
            
            // Wait for trip page to load
            await page.waitForURL('**/plan/**', { timeout: 10000 });
            
            // Take screenshot of the created trip
            const screenshotPath = path.join(__dirname, '..', 'test-results', 'ai-import', `${model.id.replace(/[/:]/g, '-')}-trip.png`);
            await page.screenshot({ 
              path: screenshotPath,
              fullPage: true 
            });
            result.screenshot = screenshotPath;
            
          } else {
            // Check for error
            const errorElement = await page.locator('.error-message, [role="alert"]').first();
            if (await errorElement.isVisible()) {
              result.error = await errorElement.textContent() || 'Unknown error';
              console.log(`❌ ${model.name}: Import failed - ${result.error}`);
            }
          }
          
        } catch (timeoutError) {
          result.error = 'Timeout waiting for response';
          console.log(`⏱️ ${model.name}: Timeout after ${maxTimeout}ms`);
        }
        
      } catch (error: any) {
        result.error = error.message;
        console.log(`❌ ${model.name}: Test failed - ${error.message}`);
      }
      
      // Take error screenshot if failed
      if (!result.success) {
        const errorScreenshotPath = path.join(__dirname, '..', 'test-results', 'ai-import', `${model.id.replace(/[/:]/g, '-')}-error.png`);
        await page.screenshot({ 
          path: errorScreenshotPath,
          fullPage: true 
        });
        result.screenshot = errorScreenshotPath;
      }
      
      testResults.push(result);
    });
  }

  test.afterAll(async () => {
    // Generate comprehensive report
    const reportPath = path.join(__dirname, '..', 'test-results', 'ai-model-comparison-report.json');
    fs.writeFileSync(reportPath, JSON.stringify(testResults, null, 2));
    
    // Generate markdown report
    const markdownReport = generateMarkdownReport(testResults);
    const mdReportPath = path.join(__dirname, '..', 'ai-model-comparison-report.md');
    fs.writeFileSync(mdReportPath, markdownReport);
    
    console.log('\n📊 Test Summary:');
    console.log('═'.repeat(60));
    console.log(`Total models tested: ${testResults.length}`);
    console.log(`Successful: ${testResults.filter(r => r.success).length}`);
    console.log(`Failed: ${testResults.filter(r => !r.success).length}`);
    console.log(`\n📄 Reports saved:`);
    console.log(`   - JSON: ${reportPath}`);
    console.log(`   - Markdown: ${mdReportPath}`);
  });
});

function generateMarkdownReport(results: TestResult[]): string {
  const successfulModels = results.filter(r => r.success);
  const failedModels = results.filter(r => !r.success);
  
  let report = `# AI Model Comparison Report for TravelViz

**Test Date**: ${new Date().toISOString()}
**Test User**: ${TEST_USER.email}
**Test Type**: Text Paste Import

## Executive Summary

- **Total Models Tested**: ${results.length}
- **Successful**: ${successfulModels.length}
- **Failed**: ${failedModels.length}
- **Success Rate**: ${((successfulModels.length / results.length) * 100).toFixed(1)}%

## Detailed Results

### ✅ Successful Models

| Model | Response Time | Activities | Locations | Trip Title |
|-------|--------------|------------|-----------|------------|
`;

  successfulModels.forEach(r => {
    report += `| ${r.modelName} | ${(r.responseTime / 1000).toFixed(1)}s | ${r.activitiesCount || 'N/A'} | ${r.locationsCount || 'N/A'} | ${r.tripTitle || 'N/A'} |\n`;
  });

  report += `
### ❌ Failed Models

| Model | Error | Response Time |
|-------|-------|---------------|
`;

  failedModels.forEach(r => {
    report += `| ${r.modelName} | ${r.error || 'Unknown'} | ${(r.responseTime / 1000).toFixed(1)}s |\n`;
  });

  report += `
## Model Performance Analysis

### Best Performers

`;

  // Rank by success and metrics
  const rankedModels = successfulModels
    .sort((a, b) => {
      // First by activities count
      const aScore = (a.activitiesCount || 0) + (a.locationsCount || 0);
      const bScore = (b.activitiesCount || 0) + (b.locationsCount || 0);
      return bScore - aScore;
    })
    .slice(0, 3);

  rankedModels.forEach((r, index) => {
    report += `${index + 1}. **${r.modelName}**
   - Activities extracted: ${r.activitiesCount || 0}
   - Locations identified: ${r.locationsCount || 0}
   - Response time: ${(r.responseTime / 1000).toFixed(1)}s
   - Quality score: ${calculateQualityScore(r)}/100

`;
  });

  report += `
## Recommendations

Based on the test results:

1. **Best Overall**: ${rankedModels[0]?.modelName || 'None'}
   - Highest extraction accuracy
   - Good response time
   - Reliable performance

2. **Best Free Option**: ${successfulModels.find(m => m.model.includes('free'))?.modelName || 'None'}
   - Cost-effective solution
   - Acceptable performance

3. **Fastest Response**: ${successfulModels.sort((a, b) => a.responseTime - b.responseTime)[0]?.modelName || 'None'}
   - Best for real-time processing
   - Good user experience

## Technical Notes

- All tests performed with the same sample itinerary
- Response times include API call and processing
- Location extraction quality varies by model
- Some models better at structured data extraction

## Screenshots

Screenshots of successful imports are saved in \`test-results/ai-import/\` directory.
`;

  return report;
}

function calculateQualityScore(result: TestResult): number {
  let score = 0;
  
  // Base score for success
  if (result.success) score += 50;
  
  // Activities extraction (max 20 points)
  if (result.activitiesCount) {
    score += Math.min(20, result.activitiesCount * 2);
  }
  
  // Locations extraction (max 20 points)
  if (result.locationsCount) {
    score += Math.min(20, result.locationsCount * 4);
  }
  
  // Response time (max 10 points)
  if (result.responseTime < 5000) score += 10;
  else if (result.responseTime < 10000) score += 5;
  
  return Math.min(100, score);
}