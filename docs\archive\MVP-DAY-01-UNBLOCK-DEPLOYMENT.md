# MVP Day 01: Unblock Deployment - Principal Engineer's Execution Plan

**Date**: [Execute Date]  
**Goal**: Fix all build and type errors to get deployment working  
**Duration**: 8 hours  
**Critical Path**: YES - Nothing works until this is done  
**Risk Level**: CRITICAL - All subsequent development blocked  
**Recovery Time Objective (RTO)**: 2 hours per major blocker

## Executive Summary

**Current State**: 37 TypeScript errors across monorepo preventing deployment  
**Target State**: Zero errors, successful build pipeline, deployable artifacts  
**Business Impact**: $X/hour in blocked developer productivity × team size  
**Success Probability**: 95% with systematic approach

## Context & Current State

### 1. Technical Risk Assessment

**Critical Blockers** (Must fix in order):

1. **Shared Package Exports** - Blocks all dependent packages
   - Risk: High (foundational dependency)
   - Impact: 100% of codebase
   - Fix Time: 45 min

2. **Hub Server Startup** - Blocks API functionality
   - Risk: High (no backend = no app)
   - Impact: All API-dependent features
   - Fix Time: 30 min

3. **Type System Integrity** - Blocks production build
   - Risk: Medium (can use escape hatches)
   - Impact: Type safety, maintainability
   - Fix Time: 2-3 hours

### 2. Dependency Graph

```mermaid
graph TD
    A[shared] --> B[hub]
    A --> C[web]
    B --> D[API Routes]
    C --> E[UI Components]
    D --> F[Database]
    E --> G[User Interface]

    style A fill:#ff6b6b,stroke:#333,stroke-width:4px
    style B fill:#feca57,stroke:#333,stroke-width:2px
    style C fill:#48dbfb,stroke:#333,stroke-width:2px
```

### Error Summary

```bash
# Current errors preventing build:
- shared package: Missing exports for Activity and ActivityType
- hub/server.ts: Duplicate cors import, wrong errorHandler name
- Total TypeScript errors: 37 across packages

# Error Distribution:
- Shared: 2 critical export errors
- Hub: 5 import/type errors
- Web: 30 component/hook errors
```

### Success Criteria & Performance Benchmarks

#### 3. Minimal CI/CD Setup (Day 1)

```yaml
# .github/workflows/emergency-deploy.yml
name: Emergency Deploy Check
on: push
jobs:
  quick-check:
    runs-on: ubuntu-latest
    timeout-minutes: 5
    steps:
      - uses: actions/checkout@v3
      - uses: pnpm/action-setup@v2
      - run: pnpm install --frozen-lockfile
      - run: pnpm type-check
      - run: pnpm build
```

#### 6. Performance Benchmarks

| Metric       | Current | Target  | Critical |
| ------------ | ------- | ------- | -------- |
| Build Time   | Failed  | < 3 min | < 5 min  |
| Memory Usage | N/A     | < 2GB   | < 4GB    |
| Type Check   | Failed  | < 30s   | < 60s    |
| Bundle Size  | N/A     | < 500KB | < 1MB    |

#### Success Validation

```bash
pnpm build  # Must complete without errors
pnpm dev    # Both hub (3001) and web (3000) must start
pnpm type-check  # 0 errors

# Automated verification script
node scripts/verify-deployment.js  # Exit 0 = success
```

## Morning Tasks (4 hours)

### 4. Error Prioritization Matrix

| Priority | Error Type      | Impact            | Fix Strategy   | Time |
| -------- | --------------- | ----------------- | -------------- | ---- |
| P0       | Missing exports | Blocks all builds | Add exports    | 45m  |
| P0       | Server startup  | No API            | Fix imports    | 30m  |
| P1       | Type mismatches | Build warnings    | Update types   | 1h   |
| P2       | Missing props   | Runtime errors    | Add defaults   | 1h   |
| P3       | Unused vars     | Linting only      | Comment/remove | 30m  |

### 9. Automated Verification Scripts

```bash
# Create scripts/verify-deployment.js
cat > scripts/verify-deployment.js << 'EOF'
#!/usr/bin/env node
const { execSync } = require('child_process');
const chalk = require('chalk');

const checks = [
  { name: 'TypeScript Check', cmd: 'pnpm type-check' },
  { name: 'Build Check', cmd: 'pnpm build' },
  { name: 'Shared Exports', cmd: 'node -e "require(\'@travelviz/shared\')"' },
  { name: 'Hub Health', cmd: 'curl -f http://localhost:3001/health || echo "Server not running"' }
];

let failed = false;
checks.forEach(check => {
  try {
    console.log(chalk.blue(`Running: ${check.name}`));
    execSync(check.cmd, { stdio: 'inherit' });
    console.log(chalk.green(`✓ ${check.name} passed`));
  } catch (e) {
    console.log(chalk.red(`✗ ${check.name} failed`));
    failed = true;
  }
});

process.exit(failed ? 1 : 0);
EOF

chmod +x scripts/verify-deployment.js
```

### Task 1: Fix Shared Package Exports (45 min)

**File**: `packages/shared/src/types/index.ts`

**Current Problem**: ActivityType enum and Activity interface not exported

### 2. Efficient Resolution Path

**Principle**: Fix foundation first, minimize changes, no premature optimization

```bash
# Quick diagnostic
find packages/shared/src -name "*.ts" | xargs grep -l "Activity" | head -5
```

**Action**:

```typescript
// Add these exports to packages/shared/src/types/index.ts
export { ActivityType } from './activity';
export type { Activity } from './activity';

// If these types don't exist, create them:
// packages/shared/src/types/activity.ts
export enum ActivityType {
  FLIGHT = 'flight',
  HOTEL = 'hotel',
  ACTIVITY = 'activity',
  TRANSPORT = 'transport',
  FOOD = 'food',
  OTHER = 'other',
}

export interface Activity {
  id: string;
  tripId: string;
  type: ActivityType;
  name: string;
  description?: string;
  startTime: Date;
  endTime?: Date;
  location?: {
    address?: string;
    lat?: number;
    lng?: number;
  };
  price?: number;
  currency?: string;
  bookingUrl?: string;
  affiliateUrl?: string;
  notes?: string;
  position: number;
  dayNumber: number;
  createdAt: Date;
  updatedAt: Date;
}
```

**Verification**:

```bash
cd packages/shared
pnpm type-check  # Should show 0 errors
```

### Task 2: Fix Hub Server Issues (30 min)

**File**: `packages/hub/src/server.ts`

**Problems**:

1. Duplicate cors import
2. errorHandler named incorrectly

**Action**:

```typescript
// Remove duplicate import
// Keep only one: import cors from 'cors';

// Fix errorHandler import - check actual export name in error.middleware.ts
import { errorHandler } from './middleware/error.middleware';
// or if it's exported as errorMiddleware:
import { errorMiddleware as errorHandler } from './middleware/error.middleware';
```

**Verification**:

```bash
cd packages/hub
pnpm dev  # Should start without errors on port 3001
```

### Task 3: Fix Remaining TypeScript Errors (2.5 hours)

**Strategy**: Run type-check and fix errors package by package

```bash
# From root directory
pnpm type-check 2>&1 | tee type-errors.log
```

**Common fixes**:

1. Missing imports → Add proper imports from @travelviz/shared
2. Type mismatches → Update interfaces to match
3. Missing properties → Add required fields or make optional with ?
4. Wrong import paths → Use package aliases (@travelviz/shared)

**Package Order**:

1. Fix shared first (foundation)
2. Fix hub (backend)
3. Fix web (frontend)

## Afternoon Tasks (4 hours)

### Task 4: Systematic Error Resolution (3 hours)

**Process for each error**:

1. Read error message carefully
2. Open the file mentioned
3. Understand what the code is trying to do
4. Apply minimal fix (don't refactor now)
5. Verify fix with pnpm type-check

**Error Categories & Solutions**:

```typescript
// Category 1: Missing types
// Solution: Import from shared
import { Activity, ActivityType } from '@travelviz/shared';

// Category 2: Property doesn't exist
// Solution: Add to interface or use optional chaining
activity?.affiliateUrl || activity.bookingUrl

// Category 3: Type mismatch
// Solution: Cast or update type definition
const startTime = new Date(activity.startTime);

// Category 4: Module not found
// Solution: Check tsconfig paths and package.json exports
// Ensure packages/shared/package.json has:
"exports": {
  ".": {
    "types": "./dist/index.d.ts",
    "import": "./dist/index.js"
  }
}
```

### Task 5: Final Build Verification (1 hour)

### 5. Rollback Strategy

**Quick Recovery Plan**:

```bash
# Before making changes
git checkout -b fix/day01-deployment
git add . && git commit -m "WIP: Pre-fix snapshot"

# Create restoration points
cp -r packages/shared packages/shared.backup
cp packages/hub/src/server.ts packages/hub/src/server.ts.backup

# If fixes break functionality
rollback() {
  git reset --hard HEAD^
  rm -rf node_modules packages/*/node_modules
  pnpm install
}
```

**Complete Build Test**:

```bash
# Clean everything first
pnpm clean
rm -rf node_modules
pnpm install

# Build in order with timing
time (cd packages/shared && pnpm build)
time (cd packages/hub && pnpm build)
time (cd packages/web && pnpm build)

# Or from root (should work if dependencies are correct)
time pnpm build

# Verify no regression
pnpm test 2>/dev/null || echo "Tests not implemented yet"
```

**Start Everything**:

```bash
pnpm dev

# Verify:
# - Hub API responds at http://localhost:3001/health
# - Web app loads at http://localhost:3000
# - No console errors in either terminal
```

## Troubleshooting Guide

### 7. Technical Debt Log

```typescript
// TECH_DEBT.md - Create this file
interface TechnicalDebt {
  id: string;
  severity: 'low' | 'medium' | 'high';
  description: string;
  workaround: string;
  properFix: string;
  estimate: string;
  addedBy: string;
  date: string;
}

const day01Debts: TechnicalDebt[] = [
  {
    id: 'TD001',
    severity: 'high',
    description: 'Using @ts-ignore for complex type errors',
    workaround: '// @ts-ignore above problematic lines',
    properFix: 'Refactor types to use proper generics',
    estimate: '4 hours',
    addedBy: 'Day 01 Emergency Fix',
    date: new Date().toISOString(),
  },
  {
    id: 'TD002',
    severity: 'medium',
    description: 'Hardcoded any types to bypass checks',
    workaround: 'as any casts',
    properFix: 'Define proper interfaces',
    estimate: '2 hours',
    addedBy: 'Day 01 Emergency Fix',
    date: new Date().toISOString(),
  },
];
```

### If shared package won't build:

1. Check tsconfig.json has correct outDir
2. Ensure all exports are in index.ts
3. No circular dependencies

### If import errors persist:

1. Check package.json "name" field matches imports
2. Verify tsconfig paths are set up
3. Ensure packages are built in correct order

### If type errors are confusing:

1. Use `pnpm type-check --listFiles` to see which files are checked
2. Temporarily add `// @ts-ignore` to complex errors and fix one by one
3. Use VS Code's TypeScript error lens for better visibility

## Extended Thinking Prompts

### 10. Knowledge Transfer Documentation

**Key Learnings Template**:

```markdown
## Day 01 Post-Mortem

### What Worked

- Fixing shared package first unblocked everything
- Using --listFiles flag helped identify scope
- Backup strategy prevented major rollbacks

### What Didn't

- Initial type-check took longer than expected
- Some circular dependencies discovered
- Build order wasn't properly configured

### Patterns Identified

1. **Missing Exports**: Always check index.ts barrels
2. **Type Mismatches**: Version conflicts between packages
3. **Import Errors**: Monorepo resolution issues

### Team Handoff

- All @ts-ignore comments are marked with TD### codes
- Technical debt logged in TECH_DEBT.md
- Verification script at scripts/verify-deployment.js
- Recovery procedure documented above
```

For complex type errors, use this prompt:

```
I'm seeing this TypeScript error: [paste error]
In file: [paste file path]
The code is trying to: [explain intent]
What's the minimal fix that maintains type safety?
Is there a way to fix this without using 'any' or '@ts-ignore'?
```

### Quick Reference Card

```bash
# Most Common Fixes
alias fix-exports="echo 'export * from ./types' >> packages/shared/src/index.ts"
alias check-deps="pnpm ls --depth=0 | grep -E '(missing|unmet)'"
alias quick-build="pnpm --filter shared build && pnpm --filter hub build && pnpm --filter web build"
alias verify-all="node scripts/verify-deployment.js"

# Emergency Escapes
alias ignore-all="find . -name '*.ts' -not -path '*/node_modules/*' -exec sed -i '1i// @ts-nocheck' {} \;"
alias remove-ignores="find . -name '*.ts' -not -path '*/node_modules/*' -exec sed -i '1d' {} \;"
```

## Progress Tracking

- [ ] Shared package exports fixed
- [ ] Shared package builds successfully
- [ ] Hub server.ts errors fixed
- [ ] Hub starts without errors
- [ ] All TypeScript errors catalogued
- [ ] 50% of errors resolved
- [ ] 100% of errors resolved
- [ ] Full build completes
- [ ] Both servers start successfully
- [ ] Basic smoke test passes

## Definition of Done

✅ All commands pass:

```bash
pnpm type-check  # 0 errors
pnpm lint        # 0 errors (or only warnings)
pnpm build       # Completes successfully
pnpm dev         # Both servers running
```

✅ Manual verification:

- Navigate to http://localhost:3000
- See Next.js app loading
- Check console for any errors
- Make API call to http://localhost:3001/health

## Next Day Preview

Once deployment is unblocked, Day 2 will focus on:

- Removing hardcoded JWT secret
- Fixing database foreign keys
- Re-enabling Row Level Security
- Adding ownership middleware

## Notes

### Principal Engineer's Execution Guidelines

1. **Triage Before Action**: Spend first 30 min mapping all errors
2. **Foundation First**: Never fix symptoms before root causes
3. **Escape Hatches**: Use @ts-ignore sparingly, always with TD### code
4. **Time Box**: 20 min per error max, then escalate or defer
5. **Verify Often**: Run checks after each major fix
6. **Document Debt**: Every shortcut must be logged
7. **Rollback Ready**: Always have a way back
8. **Success Metrics**: Define "working" precisely

### Mental Model for Error Resolution

```
Error appears → Is it blocking others? → Yes → Fix immediately
                                      ↓ No
                                      Can it be @ts-ignored safely?
                                      ↓ Yes              ↓ No
                                      Add to debt log    Fix properly
```

### Communication Template

```slack
@channel Day 01 Deployment Unblocking Update [TIME]

✅ Completed:
- Fixed shared package exports (2 critical)
- Resolved hub server startup (5 errors)

🚧 In Progress:
- Type errors in web package (15/30 fixed)

⏱️ ETA: 2 hours to full deployment

🚨 Blockers: None currently

📊 Metrics:
- Build time: Now 2m 15s (was failing)
- Type check: 15 errors remaining (was 37)
```

- Don't refactor or optimize today - just fix errors
- Keep changes minimal and focused
- Document any weird fixes for later cleanup with TD### codes
- If stuck on an error for >20 min, use @ts-ignore and move on
- Success today enables everything else
- Every fix should be verifiable with the automated script
- Maintain the technical debt log religiously
- Share progress updates every 2 hours
