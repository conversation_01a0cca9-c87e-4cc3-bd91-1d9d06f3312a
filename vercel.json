{"buildCommand": "cd ../../ && pnpm --filter @travelviz/shared build && pnpm --filter @travelviz/web build", "outputDirectory": ".next", "installCommand": "cd ../../ && pnpm install --frozen-lockfile", "framework": "nextjs", "regions": ["iad1"], "functions": {"app/api/waitlist/route.ts": {"maxDuration": 10}}, "headers": [{"source": "/(.*)", "headers": [{"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "Referrer-Policy", "value": "origin-when-cross-origin"}, {"key": "Permissions-Policy", "value": "camera=(), microphone=(), geolocation=()"}]}]}