# Database Migration Instructions

## IMPORTANT: Apply these migrations to fix the MVP

### Step 1: Go to Supabase Dashboard
1. Open your Supabase project dashboard
2. Go to the SQL Editor section

### Step 2: Apply Migration 017 (Schema Fixes)
Copy and paste the entire contents of:
`packages/hub/src/db/migrations/017_schema_fixes_and_missing_indexes.sql`

This migration will:
- Add missing columns (affiliate_url, attempt_time, locked_at, unlocked_at)
- Create 30+ performance indexes
- Enable full-text search
- Fix all schema mismatches

### Step 3: Apply Migration 018 (RPC Functions)
Copy and paste the entire contents of:
`packages/hub/src/db/migrations/018_rpc_functions.sql`

This migration will:
- Add optimized query functions
- Enable batch operations
- Add performance monitoring

### Step 4: Verify Migration Success
Run this query in SQL editor to verify:

```sql
-- Check new columns exist
SELECT column_name FROM information_schema.columns 
WHERE table_name = 'activities' AND column_name = 'affiliate_url';

-- Check indexes were created
SELECT indexname FROM pg_indexes 
WHERE tablename = 'trips' AND indexname LIKE 'idx_trips%';

-- Check RPC functions exist
SELECT routine_name FROM information_schema.routines 
WHERE routine_schema = 'public' AND routine_type = 'FUNCTION'
AND routine_name LIKE 'get_%';
```

### Expected Results:
- affiliate_url column should exist
- Multiple idx_trips_* indexes should be listed
- Functions like get_user_dashboard_stats should exist

## Alternative: Manual Application

If Supabase SQL editor has size limits, you can apply the migrations in chunks. The migration files are already organized in logical sections.