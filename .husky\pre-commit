# TravelViz Pre-commit Hook
echo "🔍 Running TravelViz pre-commit checks..."

# Check for sensitive files
echo "🔒 Checking for sensitive files..."
if git diff --cached --name-only | grep -E "\.(env|log|key|pem)$|node_modules|\.tsbuildinfo$"; then
  echo "❌ Error: Attempting to commit sensitive files!"
  echo "The following files should not be committed:"
  git diff --cached --name-only | grep -E "\.(env|log|key|pem)$|node_modules|\.tsbuildinfo$"
  echo ""
  echo "Please remove them from staging:"
  echo "  git rm --cached <filename>"
  echo ""
  echo "Make sure these files are in .gitignore"
  exit 1
fi

# Check for actual secrets in staged files (more specific patterns)
echo "🔐 Scanning for potential secrets..."
# First, check for secrets but exclude test files and common false positives
SECRETS_FOUND=$(git diff --cached | grep -i -E "(password|secret|key|token|api_key)\s*[:=]\s*[\"']?[a-zA-Z0-9_\-]{8,}[\"']?" | \
   grep -v -E "(your[-_].*|example|template|placeholder|test[-_]?token|test[-_]?key|test[-_]?api[-_]?key|test_mapbox_not_real|password123|PRIMARY\s+KEY|share_token|Authorization.*Bearer|process\.env\.|getCacheKey|Redis.*get|\.env\.|JWT_SECRET|SUPABASE_.*_KEY|API_KEY=your_|TOKEN=your_|SECRET=your_|getCacheKey|cache_key|shareToken|share_token|Bearer.*token|Authorization.*Bearer.*token|access_token|refresh_token|new-token|new-refresh|__tests__|\.test\.|\.spec\.|test/|tests/|setup\.ts|travelviz:test-key|dbKey|mappings\[key\]|key:\s*['\"]X-|key:\s*['\"]Content-|key:\s*['\"]Cache-|key:\s*['\"]Referrer-|key:\s*['\"]Permissions-|key:\s*generateCharacterKey|token:\s*redisToken|key:\s*redisToken|undefined|TEST_USER|TestPassword123|Flaremmk123!|test@mmkdev\.com|hasRequiredKey|key.*activity-|key.*creating|fallbackModelKey|modelKey|-\s*key:\s*[A-Z_]+)" || true)

# Also exclude patterns from test files
if [ -n "$SECRETS_FOUND" ]; then
  # Check if the secrets are only in test files or archive docs by examining the actual files
  NON_TEST_SECRETS=""
  for file in $(git diff --cached --name-only); do
    if ! echo "$file" | grep -E "(__tests__|\.test\.|\.spec\.|test/|tests/|e2e/|integration/|test-.*\.js$|docs/archive/)" > /dev/null; then
      # This is not a test file or archive doc, check if it has secrets
      FILE_SECRETS=$(git diff --cached -- "$file" | grep -i -E "(password|secret|key|token|api_key)\s*[:=]\s*[\"']?[a-zA-Z0-9_\-]{8,}[\"']?" | \
        grep -v -E "(your[-_].*|example|template|placeholder|test[-_]?token|test[-_]?key|test[-_]?api[-_]?key|test_mapbox_not_real|password123|PRIMARY\s+KEY|share_token|Authorization.*Bearer|process\.env\.|getCacheKey|Redis.*get|\.env\.|JWT_SECRET|SUPABASE_.*_KEY|API_KEY=your_|TOKEN=your_|SECRET=your_|getCacheKey|cache_key|shareToken|share_token|Bearer.*token|Authorization.*Bearer.*token|access_token|refresh_token|new-token|new-refresh|__tests__|\.test\.|\.spec\.|test/|tests/|setup\.ts|travelviz:test-key|dbKey|mappings\[key\]|key:\s*['\"]X-|key:\s*['\"]Content-|key:\s*['\"]Cache-|key:\s*['\"]Referrer-|key:\s*['\"]Permissions-|key:\s*generateCharacterKey|token:\s*redisToken|key:\s*redisToken|undefined|TEST_USER|TestPassword123|Flaremmk123!|test@mmkdev\.com|hasRequiredKey|key.*activity-|key.*creating|fallbackModelKey|modelKey|key:\s*googleKey|accessToken|authToken|-\s*key:\s*[A-Z_]+)" || true)
      if [ -n "$FILE_SECRETS" ]; then
        NON_TEST_SECRETS="${NON_TEST_SECRETS}${file}:\n${FILE_SECRETS}\n"
      fi
    fi
  done
  
  if [ -n "$NON_TEST_SECRETS" ]; then
    echo "❌ Error: Potential actual secrets found in non-test files!"
    echo "The following lines contain what might be real secrets:"
    echo "$NON_TEST_SECRETS"
    echo ""
    echo "Please review and ensure these are not real secrets. If they are:"
    echo "  1. Remove them from the files"
    echo "  2. Add them to .env files instead"
    echo "  3. Use placeholder values in documentation"
    echo ""
    echo "If these are legitimate documentation examples, add them to the exclusion pattern."
    exit 1
  fi
fi

# Run linting on staged files
echo "🧹 Running ESLint on staged files..."
pnpm lint-staged 2>/dev/null || echo "⚠️  lint-staged not configured, skipping..."

# Run type checking
echo "📘 Running TypeScript type check..."
pnpm type-check

# Skip health check for commits - services don't need to be running for code quality validation
# Health checks are handled by CI/CD pipeline
echo "🏥 Skipping service health checks for commit (handled by CI)..."

echo "✅ Pre-commit checks passed!"
