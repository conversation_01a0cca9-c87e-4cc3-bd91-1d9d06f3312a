# TravelViz Project Structure

## Monorepo Organization

```
travelviz/
├── packages/                    # Workspace packages
│   ├── web/                    # Next.js frontend (@travelviz/web)
│   ├── hub/                    # Express.js backend (@travelviz/hub)
│   ├── shared/                 # Shared types/utils (@travelviz/shared)
│   └── mobile/                 # React Native (Phase 3, placeholder)
├── docs/                       # Project documentation
├── scripts/                    # Build and utility scripts
├── tests/                      # E2E tests (Playwright)
├── supabase/                   # Database migrations and setup
└── .kiro/                      # Kiro steering rules
```

## Package Structure

### Frontend (packages/web/)
```
web/
├── app/                        # Next.js 15 App Router
│   ├── (auth)/                # Route groups for auth pages
│   ├── dashboard/             # Protected dashboard routes
│   ├── plan/                  # Trip planning interface
│   ├── import/                # AI import functionality
│   └── p/[id]/               # Public trip sharing pages
├── components/                # React components
│   ├── ui/                   # shadcn/ui base components
│   ├── dashboard/            # Dashboard-specific components
│   ├── import/               # AI import components
│   ├── map/                  # Map-related components
│   └── timeline/             # Trip timeline components
├── hooks/                     # Custom React hooks
├── lib/                       # Utilities and configurations
│   ├── api/                  # API client functions
│   └── validations/          # Zod schemas for forms
└── stores/                    # Zustand state stores
```

### Backend (packages/hub/)
```
hub/
├── src/
│   ├── controllers/          # HTTP request handlers
│   ├── services/             # Business logic layer
│   │   └── trips/           # Trip-related services
│   ├── routes/              # Express route definitions
│   ├── middleware/          # Custom middleware
│   ├── lib/                 # External integrations (Supabase)
│   ├── types/               # TypeScript type definitions
│   └── utils/               # Utility functions
├── scripts/                 # Utility and test scripts
└── supabase/               # Database configuration
```

### Shared (packages/shared/)
```
shared/
├── src/
│   ├── types/               # Shared TypeScript interfaces
│   ├── dto/                 # Data Transfer Objects
│   ├── utils/               # Common utility functions
│   └── constants.ts         # Shared constants
└── test/                    # Test utilities and fixtures
```

## Architecture Patterns

### Clean Architecture (Hub)
- **Controllers**: HTTP handling only, extend BaseController
- **Services**: Business logic, framework-agnostic
- **Repositories**: Data access patterns (Supabase)
- **DTOs**: API contracts defined in @travelviz/shared

### Component Architecture (Web)
- **Pages**: App Router pages with server components
- **Components**: Reusable UI components with TypeScript
- **Hooks**: Custom hooks for state and side effects
- **Stores**: Zustand for client-side state management

## File Naming Conventions

### TypeScript Files
- **Components**: PascalCase (`TripTimeline.tsx`)
- **Hooks**: camelCase with 'use' prefix (`useTrips.ts`)
- **Services**: camelCase with service suffix (`trip.service.ts`)
- **Types**: PascalCase interfaces, camelCase for DTOs
- **Utils**: camelCase (`api-client.ts`)

### Directories
- **kebab-case** for all directories (`trip-timeline/`)
- **Route groups** in Next.js use parentheses `(auth)/`

## Import Patterns

### Workspace References
```typescript
// Import from shared package
import { TripDto } from '@travelviz/shared';

// Import from same package
import { Button } from '@/components/ui/button';
import { useTrips } from '@/hooks/useTrips';
```

### Path Aliases
- `@/` maps to package src root
- `@travelviz/shared` for shared package imports
- Relative imports for same-directory files

## Testing Structure
- **Unit tests**: Co-located with source files (`.test.ts`)
- **Integration tests**: In `__tests__/` directories
- **E2E tests**: Root-level `tests/` directory
- **Test data**: `test-data/` directories per package

## Configuration Files
- **Root level**: Workspace configuration (pnpm, TypeScript, ESLint)
- **Package level**: Package-specific configs (Next.js, Vitest)
- **Environment**: `.env.local` files per package