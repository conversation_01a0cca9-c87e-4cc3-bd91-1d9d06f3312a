/**
 * Shared utility types for type-safe development
 * These utilities help eliminate 'any' types across the codebase
 */


/**
 * Type-safe record without index signature issues
 * Use this instead of Record<string, any>
 */
export type StrictRecord<K extends string = string, V = unknown> = Record<K, V>;

/**
 * Analytics event data with known property types
 * Replaces Record<string, any> in analytics contexts
 */
export type AnalyticsEventData = StrictRecord<string, string | number | boolean | null | undefined>;

/**
 * Vitest mock function type helper
 * Use this instead of any for mock functions
 */
export type MockedFunction<T> = T extends (...args: infer P) => infer R
  ? import('vitest').MockedFunction<(...args: P) => R>
  : never;

/**
 * Safe event handler type
 * Use for DOM event callbacks instead of Function type
 */
export type EventHandler<T = Event> = (event: T) => void;

/**
 * Configuration object type
 * Use for flexible config objects instead of any
 */
export type ConfigObject = StrictRecord<string, unknown>;

/**
 * JSON-serializable value type
 * Use for API responses and serializable data
 */
export type JsonValue = string | number | boolean | null | JsonObject | JsonArray;
export type JsonObject = { [key: string]: JsonValue };
export type JsonArray = JsonValue[];