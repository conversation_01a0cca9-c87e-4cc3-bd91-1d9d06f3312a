#!/usr/bin/env tsx

/**
 * PDF Import Fix Validation Test
 * Tests the fixes implemented for session 2479b9e7-4336-4b9c-9447-78f747ae26be
 * 
 * Usage: npx tsx src/scripts/test-pdf-import-fix.ts
 */

import { pdfImportDebugger } from '../utils/debug-pdf-import';
import { getAIParserService } from '../services/ai-parser.service';
import { getSupabaseClient } from '../lib/supabase';
import { logger } from '../utils/logger';

async function testOrphanedSessionRecovery() {
  console.log('🧪 Testing Orphaned Session Recovery');
  console.log('-'.repeat(50));

  try {
    // Test the specific session that was failing
    const targetSessionId = '2479b9e7-4336-4b9c-9447-78f747ae26be';
    
    // Get the session status using the fixed getSession method
    const aiParserService = getAIParserService();
    const session = await aiParserService.getSession(targetSessionId);
    
    if (!session) {
      console.log('❌ Session not found');
      return false;
    }

    console.log('✅ Session Status Check Results:');
    console.log(`   Session ID: ${session.id}`);
    console.log(`   Status: ${session.status}`);
    console.log(`   Progress: ${session.progress}%`);
    console.log(`   Current Step: ${session.currentStep}`);
    console.log(`   Error: ${session.error || 'None'}`);
    console.log(`   Started: ${session.startedAt.toISOString()}`);
    console.log(`   Completed: ${session.completedAt?.toISOString() || 'N/A'}`);

    // Verify the session is properly marked as failed
    if (session.status === 'error' && session.error?.includes('timeout')) {
      console.log('✅ Orphaned session properly recovered and marked as failed');
      return true;
    } else {
      console.log('❌ Session not properly recovered');
      return false;
    }

  } catch (error) {
    console.error('❌ Test failed:', error);
    return false;
  }
}

async function testNewSessionCreation() {
  console.log('\n🧪 Testing New Session Creation with Fixes');
  console.log('-'.repeat(50));

  try {
    const testContent = `
    I'm planning a 3-day trip to Paris from March 15-17, 2024.

    Day 1: Arrive at CDG airport, check into hotel near Louvre, visit Eiffel Tower in evening
    Day 2: Morning at Louvre Museum, afternoon at Champs-Élysées shopping, dinner at local bistro
    Day 3: Visit Notre-Dame area, Seine river cruise, departure from CDG

    Budget: €1500 for the trip
    `;

    const testUserId = '697b40b3-42d7-4b32-ad49-0220c2313643'; // Same user as the failing session
    const testSource = 'chatgpt';

    console.log('Creating test session...');
    
    // This should use the fixed createParseSession method
    const aiParserService = getAIParserService();
    const sessionId = await aiParserService.createParseSession(testContent, testSource, testUserId);
    
    console.log(`✅ Test session created: ${sessionId}`);

    // Wait a moment for initial processing
    await new Promise(resolve => setTimeout(resolve, 2000));

    // Check the session was created with proper timestamps
    const { data, error } = await getSupabaseClient()
      .from('ai_import_logs')
      .select('id, import_status, created_at, updated_at')
      .eq('id', sessionId)
      .single();

    if (error) {
      console.log('❌ Failed to query test session:', error.message);
      return false;
    }

    console.log('✅ Test Session Database Check:');
    console.log(`   Status: ${data.import_status}`);
    console.log(`   Created: ${data.created_at}`);
    console.log(`   Updated: ${data.updated_at}`);

    // Verify timestamps are consistent (updated_at should not be before created_at)
    const createdTime = new Date(data.created_at).getTime();
    const updatedTime = new Date(data.updated_at).getTime();

    if (updatedTime >= createdTime) {
      console.log('✅ Timestamp consistency fixed - updated_at is not before created_at');
    } else {
      console.log('❌ Timestamp issue still exists');
      return false;
    }

    // Clean up test session
    await getSupabaseClient()
      .from('ai_import_logs')
      .delete()
      .eq('id', sessionId);

    console.log('✅ Test session cleaned up');
    return true;

  } catch (error) {
    console.error('❌ New session creation test failed:', error);
    return false;
  }
}

async function testStatusUpdateMechanism() {
  console.log('\n🧪 Testing Status Update Mechanism');
  console.log('-'.repeat(50));

  try {
    // Create a test session directly in database
    const testSessionId = `test-${Date.now()}`;
    const testUserId = '697b40b3-42d7-4b32-ad49-0220c2313643';
    const now = new Date().toISOString();

    await getSupabaseClient()
      .from('ai_import_logs')
      .insert({
        id: testSessionId,
        user_id: testUserId,
        ai_platform: 'test',
        import_status: 'processing',
        raw_conversation: 'Test content',
        created_at: now,
        updated_at: now
      });

    console.log(`✅ Test session created: ${testSessionId}`);

    // Test the fixed updateSessionStatus method indirectly through getSession
    const aiParserService = getAIParserService();
    
    // This should trigger orphaned session detection if we wait long enough
    // But for testing, we'll just verify the session exists and can be queried
    const session = await aiParserService.getSession(testSessionId);
    
    if (session && session.status === 'processing') {
      console.log('✅ Status update mechanism working - session properly retrieved');
    } else {
      console.log('❌ Status update mechanism issue');
      return false;
    }

    // Clean up
    await getSupabaseClient()
      .from('ai_import_logs')
      .delete()
      .eq('id', testSessionId);

    console.log('✅ Test session cleaned up');
    return true;

  } catch (error) {
    console.error('❌ Status update test failed:', error);
    return false;
  }
}

async function main() {
  console.log('🔧 PDF Import Fix Validation Tests');
  console.log('='.repeat(60));

  const results = {
    orphanedRecovery: false,
    newSessionCreation: false,
    statusUpdateMechanism: false
  };

  // Test 1: Orphaned Session Recovery
  results.orphanedRecovery = await testOrphanedSessionRecovery();

  // Test 2: New Session Creation with Fixes
  results.newSessionCreation = await testNewSessionCreation();

  // Test 3: Status Update Mechanism
  results.statusUpdateMechanism = await testStatusUpdateMechanism();

  // Summary
  console.log('\n📊 Test Results Summary');
  console.log('='.repeat(60));
  console.log(`Orphaned Session Recovery: ${results.orphanedRecovery ? '✅ PASS' : '❌ FAIL'}`);
  console.log(`New Session Creation: ${results.newSessionCreation ? '✅ PASS' : '❌ FAIL'}`);
  console.log(`Status Update Mechanism: ${results.statusUpdateMechanism ? '✅ PASS' : '❌ FAIL'}`);

  const passedTests = Object.values(results).filter(Boolean).length;
  const totalTests = Object.keys(results).length;

  console.log(`\nOverall: ${passedTests}/${totalTests} tests passed`);

  if (passedTests === totalTests) {
    console.log('🎉 All tests passed! PDF import fix is working correctly.');
    return true;
  } else {
    console.log('⚠️  Some tests failed. Review the issues above.');
    return false;
  }
}

// Run the validation tests
if (require.main === module) {
  main()
    .then(success => {
      process.exit(success ? 0 : 1);
    })
    .catch(error => {
      console.error('Test execution failed:', error);
      process.exit(1);
    });
}

export { main as validatePDFImportFix };