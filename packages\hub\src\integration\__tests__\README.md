# AI Import Integration Tests

This directory contains comprehensive integration tests for the AI import, PDF import, and chat itinerary features.

## Test Files

### 1. `ai-import-comprehensive.test.ts`
Main test suite covering all AI import functionality:
- ChatGPT conversation parsing
- Claude conversation parsing  
- Gemini conversation parsing
- Trip creation from parsed data
- Error handling and edge cases
- Performance tests
- End-to-end workflows

### 2. `ai-import-flow.integration.test.ts`
Tests the complete import flow with real API calls.

### 3. `ai-import-e2e.test.ts`
End-to-end tests simulating real user scenarios.

## Running Tests

### Prerequisites
1. Hub server must be running on port 3001
2. Environment variables configured in `.env.local`:
   - `SUPABASE_SERVICE_ROLE_KEY`
   - `OPENROUTER_API_KEY`
   - `UPSTASH_REDIS_URL` and `UPSTASH_REDIS_TOKEN`

### Commands

```bash
# From hub directory
cd packages/hub

# Run all AI import tests
pnpm test:ai-import

# Run in watch mode
pnpm test:ai-import:watch

# Run with coverage
pnpm test:ai-import:coverage

# Run specific test file
pnpm vitest run src/integration/__tests__/ai-import-comprehensive.test.ts
```

## Test Coverage

The tests cover:

### AI Conversation Parsing
- ✅ Multiple AI platforms (ChatGPT, Claude, Gemini)
- ✅ Different conversation formats
- ✅ Activity extraction and categorization
- ✅ Date/time parsing
- ✅ Location and price extraction
- ✅ Multi-language support

### API Endpoints
- ✅ POST `/api/v1/import/parse-simple` - Simple parsing
- ✅ POST `/api/v1/import/parse` - SSE parsing
- ✅ GET `/api/v1/import/:importId/progress` - SSE progress
- ✅ POST `/api/v1/import/:importId/create-trip` - Trip creation
- ✅ POST `/api/v1/import/upload` - PDF upload

### Error Scenarios
- ✅ Content too short validation
- ✅ Content too long (>50KB) rejection
- ✅ Invalid import IDs
- ✅ Duplicate trip creation prevention
- ✅ Authentication failures
- ✅ Rate limiting

### Performance
- ✅ Concurrent request handling
- ✅ Parse completion within 30 seconds
- ✅ Large content handling
- ✅ Memory management

## Test Data

The tests use realistic conversation data from different AI platforms:

### Tokyo Itinerary (ChatGPT)
- 5-day trip with detailed daily activities
- Includes prices, times, and locations
- Tests multi-day parsing

### Barcelona Weekend (Claude)
- Short weekend trip format
- Tests compact itinerary parsing
- European currency handling

### Rome Trip (Gemini)
- Bullet-point format
- Tests different formatting styles
- Mixed activity types

## Debugging

If tests fail:

1. **Check server logs**
   ```bash
   # View hub server logs
   pnpm dev
   ```

2. **Check Redis connection**
   ```bash
   # Test Redis connection
   curl http://localhost:3001/api/health
   ```

3. **Verify API keys**
   - Ensure `OPENROUTER_API_KEY` is valid
   - Check rate limits on OpenRouter dashboard

4. **Run individual tests**
   ```bash
   # Run single test with verbose output
   pnpm vitest run -t "should parse ChatGPT Tokyo conversation"
   ```

## Adding New Tests

To add new test scenarios:

1. Add conversation data to `TEST_CONVERSATIONS`
2. Create test case in appropriate `describe` block
3. Use `waitForParseCompletion` helper for async parsing
4. Verify both parse result and created trip structure

Example:
```typescript
it('should parse new AI format', async () => {
  const response = await request(server)
    .post('/api/v1/import/parse-simple')
    .set('Authorization', `Bearer ${authToken}`)
    .send({ content: 'your test conversation', source: 'ai-name' })
    .expect(200);

  const result = await waitForParseCompletion(server, response.body.data.importId, authToken);
  
  expect(result.title).toBeDefined();
  expect(result.activities).toHaveLength(expect.any(Number));
});
```

## CI/CD Integration

These tests are skipped in CI by default due to API key requirements. To enable in CI:

1. Add secrets to GitHub Actions
2. Remove the `isCI` check in test files
3. Ensure test database is available

## Performance Benchmarks

Expected performance targets:
- Simple parse: < 5 seconds average
- Complex itineraries: < 15 seconds
- Concurrent requests: 10+ without degradation
- Memory usage: < 200MB per parse session