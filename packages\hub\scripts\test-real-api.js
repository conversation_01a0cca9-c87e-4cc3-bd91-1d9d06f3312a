#!/usr/bin/env node

const { config } = require('dotenv');
const { resolve } = require('path');
const { execSync } = require('child_process');

// Load environment variables from .env.local
config({ path: resolve(__dirname, '../.env.local') });

// Check if test API key is available
if (!process.env.OPENROUTER_TEST_API_KEY) {
  console.error('❌ OPENROUTER_TEST_API_KEY not found in .env.local');
  process.exit(1);
}

console.log('✅ OPENROUTER_TEST_API_KEY found:', process.env.OPENROUTER_TEST_API_KEY.substring(0, 10) + '...');

// Run the test with proper environment
try {
  execSync('pnpm test src/integration/__tests__/openrouter-real.test.ts', {
    cwd: resolve(__dirname, '..'),
    stdio: 'inherit',
    env: {
      ...process.env,
      OPENROUTER_TEST_API_KEY: process.env.OPENROUTER_TEST_API_KEY
    }
  });
} catch (error) {
  console.error('Test failed:', error.message);
  process.exit(1);
}