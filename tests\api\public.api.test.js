/**
 * Public API Tests
 * 
 * Tests public access endpoints without authentication
 * Includes health checks and public trip sharing endpoints
 */

const ApiTestClient = require('./utils/api-client');
const apiConfig = require('./api.config');

// Test results tracking
const testResults = {
  passed: 0,
  failed: 0,
  tests: []
};

function logTest(name, result, details = {}) {
  const passed = result.success || result === true;
  testResults.tests.push({ name, passed, details });
  
  if (passed) {
    testResults.passed++;
    console.log(`✅ ${name}`);
  } else {
    testResults.failed++;
    console.log(`❌ ${name}`, details.error || result.error || '');
  }
}

async function testHealthCheck(client) {
  console.log('\n🏥 Testing Health Check...');
  
  try {
    const response = await client.healthCheck();
    
    logTest('Health check endpoint', response, response);
    
    if (response.success) {
      const data = response.data.data || response.data;
      
      logTest('Health check returns status', !!data.status, {
        status: data.status,
        hasStatus: !!data.status
      });
      
      logTest('Health status is healthy', 
        data.status === 'healthy' || data.status === 'ok', {
        expectedStatus: 'healthy or ok',
        actualStatus: data.status
      });
      
      // Check for additional health info
      if (data.timestamp) {
        logTest('Health check includes timestamp', true, {
          timestamp: data.timestamp
        });
      }
      
      if (data.version) {
        logTest('Health check includes version', true, {
          version: data.version
        });
      }
      
      return true;
    }
    
    return false;
  } catch (error) {
    logTest('Health Check Test', false, { error: error.message });
    return false;
  }
}

async function testHealthCheckResponseTime(client) {
  console.log('\n⏱️ Testing Health Check Response Time...');
  
  try {
    const startTime = Date.now();
    const response = await client.healthCheck();
    const responseTime = Date.now() - startTime;
    
    logTest('Health check responds quickly', responseTime < 1000, {
      responseTime: `${responseTime}ms`,
      threshold: '1000ms'
    });
    
    logTest('Health check successful', response.success, response);
    
    console.log(`   Response time: ${responseTime}ms`);
    
    return response.success;
  } catch (error) {
    logTest('Health Check Response Time Test', false, { error: error.message });
    return false;
  }
}

async function testCorsHeaders(client) {
  console.log('\n🌐 Testing CORS Headers...');
  
  try {
    // Make an OPTIONS request to test CORS
    const response = await client.makeRequest('OPTIONS', '/api/v1/health', null, {
      'Origin': 'http://localhost:3000',
      'Access-Control-Request-Method': 'GET',
      'Access-Control-Request-Headers': 'Content-Type'
    });
    
    logTest('CORS preflight request', response.status === 200 || response.status === 204, {
      status: response.status,
      expectedStatus: '200 or 204'
    });
    
    // Check CORS headers
    const headers = response.headers;
    logTest('CORS Access-Control-Allow-Origin header', 
      !!headers['access-control-allow-origin'], {
      header: headers['access-control-allow-origin']
    });
    
    logTest('CORS Access-Control-Allow-Methods header', 
      !!headers['access-control-allow-methods'], {
      header: headers['access-control-allow-methods']
    });
    
    return true;
  } catch (error) {
    logTest('CORS Headers Test', false, { error: error.message });
    return false;
  }
}

async function testRateLimiting(client) {
  console.log('\n🚦 Testing Rate Limiting...');
  
  try {
    // Make multiple rapid requests to test rate limiting
    const requests = [];
    const requestCount = 10;
    
    console.log(`   Making ${requestCount} rapid requests...`);
    
    for (let i = 0; i < requestCount; i++) {
      requests.push(client.healthCheck());
    }
    
    const responses = await Promise.all(requests);
    
    const successCount = responses.filter(r => r.success).length;
    const rateLimitedCount = responses.filter(r => r.status === 429).length;
    
    logTest('Some requests succeeded', successCount > 0, {
      successCount,
      totalRequests: requestCount
    });
    
    if (rateLimitedCount > 0) {
      logTest('Rate limiting is active', true, {
        rateLimitedCount,
        totalRequests: requestCount
      });
    } else {
      logTest('Rate limiting not triggered (may be configured differently)', true, {
        note: 'This is not necessarily a failure'
      });
    }
    
    return true;
  } catch (error) {
    logTest('Rate Limiting Test', false, { error: error.message });
    return false;
  }
}

async function testInvalidEndpoint(client) {
  console.log('\n🚫 Testing Invalid Endpoint...');
  
  try {
    const response = await client.makeRequest('GET', '/api/v1/nonexistent-endpoint');
    
    logTest('Invalid endpoint returns 404', response.status === 404, {
      expectedStatus: 404,
      actualStatus: response.status
    });
    
    logTest('Invalid endpoint handled gracefully', !response.success, response);
    
    return true;
  } catch (error) {
    logTest('Invalid Endpoint Test', false, { error: error.message });
    return false;
  }
}

async function testInvalidMethod(client) {
  console.log('\n🚫 Testing Invalid HTTP Method...');
  
  try {
    const response = await client.makeRequest('PATCH', '/api/v1/health');
    
    // Should return 405 Method Not Allowed or 404
    logTest('Invalid method handled', 
      response.status === 405 || response.status === 404, {
      expectedStatus: '405 or 404',
      actualStatus: response.status
    });
    
    return true;
  } catch (error) {
    logTest('Invalid Method Test', false, { error: error.message });
    return false;
  }
}

async function testServerHeaders(client) {
  console.log('\n🔒 Testing Security Headers...');
  
  try {
    const response = await client.healthCheck();
    
    if (response.success) {
      const headers = response.headers;
      
      // Check for common security headers
      const securityHeaders = [
        'x-content-type-options',
        'x-frame-options',
        'x-xss-protection',
        'strict-transport-security'
      ];
      
      let securityHeaderCount = 0;
      securityHeaders.forEach(header => {
        if (headers[header]) {
          securityHeaderCount++;
          logTest(`Security header: ${header}`, true, {
            value: headers[header]
          });
        }
      });
      
      logTest('Some security headers present', securityHeaderCount > 0, {
        count: securityHeaderCount,
        total: securityHeaders.length
      });
      
      // Check server header (should be hidden or generic)
      if (headers.server) {
        logTest('Server header present', true, {
          server: headers.server,
          note: 'Consider hiding for security'
        });
      } else {
        logTest('Server header hidden (good for security)', true);
      }
      
      return true;
    }
    
    return false;
  } catch (error) {
    logTest('Server Headers Test', false, { error: error.message });
    return false;
  }
}

async function runTests() {
  console.log('🚀 Starting Public API Tests');
  console.log('============================');
  console.log(`API Base URL: ${apiConfig.baseUrl}`);
  console.log('Testing public endpoints (no authentication required)\n');

  const client = new ApiTestClient();
  
  try {
    // Run all public API tests in sequence
    const tests = [
      () => testHealthCheck(client),
      () => testHealthCheckResponseTime(client),
      () => testCorsHeaders(client),
      () => testRateLimiting(client),
      () => testInvalidEndpoint(client),
      () => testInvalidMethod(client),
      () => testServerHeaders(client),
    ];

    for (const test of tests) {
      await test();
    }
    
  } catch (error) {
    console.error('\n💥 Test execution error:', error.message);
    testResults.failed++;
  }

  // Summary
  console.log('\n============================');
  console.log('📊 Public API Test Results');
  console.log('============================');
  console.log(`✅ Passed: ${testResults.passed}`);
  console.log(`❌ Failed: ${testResults.failed}`);
  console.log(`📋 Total: ${testResults.tests.length}`);
  console.log(`📈 Success Rate: ${Math.round((testResults.passed / testResults.tests.length) * 100)}%`);

  // Detailed failures
  if (testResults.failed > 0) {
    console.log('\n❌ Failed Tests:');
    testResults.tests
      .filter(t => !t.passed)
      .forEach(t => {
        console.log(`\n- ${t.name}`);
        if (t.details.error) {
          console.log('  Error:', t.details.error);
        }
      });
  }

  return testResults;
}

// Export for use by test runner
module.exports = { runTests };

// Run directly if called as script
if (require.main === module) {
  runTests().catch(console.error);
}