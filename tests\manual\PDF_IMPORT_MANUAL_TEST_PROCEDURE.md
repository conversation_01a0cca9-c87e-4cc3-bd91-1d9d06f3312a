# PDF Import Manual Testing Procedure

## Overview
This document provides step-by-step manual testing procedures to validate the PDF import functionality and identify any remaining issues in the import pipeline.

## Prerequisites

### Environment Setup
1. **Backend Server**: Ensure hub server is running on `http://localhost:3001`
2. **Frontend Server**: Ensure web server is running on `http://localhost:3000`
3. **Test Account**: Use `<EMAIL>` with password from `.env.local`
4. **Test File**: `C:\Users\<USER>\Travelviz\Travelviz\Updated 15-Day European Travel Itinerary_ London, Madrid, Lisbon and Porto.pdf`

### Required <PERSON><PERSON>
- <PERSON>rowser with Developer Tools (Chrome/Firefox recommended)
- Network monitoring tool (built-in DevTools Network tab)
- Backend logs access (terminal/console)

## Test Procedures

### Test 1: Authentication Verification

**Objective**: Verify test account authentication works correctly

**Steps**:
1. Open browser to `http://localhost:3000/login`
2. Enter credentials: `<EMAIL>` / password from `.env.local`
3. Click "Sign In"
4. Open DevTools Network tab
5. Verify successful login response

**Expected Results**:
- Successful redirect to dashboard
- Auth token received in response
- No authentication errors in console

**Monitoring Points**:
- Response time for login request
- Token format and validity
- Any authentication errors

### Test 2: PDF Upload Flow

**Objective**: Test the complete PDF upload and parsing initiation

**Steps**:
1. Navigate to import page (`/import` or `/upload`)
2. Open DevTools Network tab
3. Select the test PDF file
4. Click upload/import button
5. Monitor network requests

**Expected Results**:
- Successful file upload (200 response)
- Session ID returned
- Parsing process initiated

**Monitoring Points**:
- Upload request size and timing
- Response structure and session ID
- Any upload errors or timeouts

### Test 3: Real-time Progress Monitoring

**Objective**: Monitor the parsing process and progress updates

**Steps**:
1. After successful upload, stay on parsing/progress page
2. Open DevTools Network tab and Console
3. Monitor polling requests to `/api/v1/import/parse-simple/{sessionId}`
4. Track progress updates and status changes
5. Note any stuck or hanging behavior

**Expected Results**:
- Regular polling every 2 seconds
- Progress increases from 0% to 100%
- Status updates: pending → processing → completed
- Completion within 3 minutes

**Critical Monitoring Points**:
- **Progress Stuck at 40%**: This indicates the hanging issue
- **304 Not Modified responses**: Indicates backend not updating status
- **Timeout after 3 minutes**: Frontend timeout mechanism
- **Model selection logs**: Check backend logs for model chosen

### Test 4: Backend Log Analysis

**Objective**: Analyze backend behavior during parsing

**Steps**:
1. Open terminal with backend server logs
2. Start PDF import process
3. Monitor logs for:
   - Model selection decisions
   - AI API calls and responses
   - Timeout events
   - Circuit breaker activity
   - Error messages

**Key Log Patterns to Watch**:
```
# Model Selection
"AI configuration loaded" - Shows available models
"Content complexity estimated" - Shows complexity level
"Selected optimal model" - Shows which model was chosen

# AI Processing
"AI API call started" - Start of AI processing
"AI API returned" - Successful completion
"AI API call timed out" - Timeout detection
"Circuit breaker OPENED" - Circuit breaker activation

# Progress Updates
"Session status updated" - Progress tracking
"Parse session completed" - Final completion
```

### Test 5: Error Handling Validation

**Objective**: Test error handling and fallback mechanisms

**Steps**:
1. Monitor for specific error conditions:
   - Model unavailability
   - Timeout scenarios
   - Network failures
   - Circuit breaker activation
2. Verify error messages are descriptive
3. Check fallback model activation

**Expected Error Handling**:
- Specific error messages (not generic "Something went wrong!")
- Automatic fallback to alternative models
- Circuit breaker recovery after failures
- Graceful timeout handling

### Test 6: Performance Measurement

**Objective**: Measure and validate performance improvements

**Steps**:
1. Record timing for each phase:
   - Authentication: < 2 seconds
   - Upload: < 10 seconds
   - Parsing: < 3 minutes total
2. Monitor resource usage
3. Check for memory leaks or excessive CPU usage

**Performance Targets**:
- Total import time: < 3 minutes
- Progress updates: Every 2-15 seconds
- No hanging or infinite loops
- Successful completion rate: > 90%

## Troubleshooting Guide

### Issue: Process Hangs at 40%

**Symptoms**:
- Progress stuck at 40% for > 90 seconds
- No new progress updates
- Backend logs show AI API call started but no completion

**Investigation Steps**:
1. Check backend logs for model selection
2. Look for "Kimi-K2" model selection (problematic model)
3. Check for timeout error messages
4. Verify circuit breaker status

**Expected Fix Validation**:
- DeepSeek model should be selected for complex content
- Timeout should trigger after 90 seconds maximum
- Fallback to alternative model should occur

### Issue: Generic Error Messages

**Symptoms**:
- "Something went wrong!" instead of specific errors
- No indication of what failed

**Investigation Steps**:
1. Check browser console for detailed errors
2. Review backend logs for actual error messages
3. Verify error handling middleware is working

### Issue: Frontend Timeout

**Symptoms**:
- Process times out after 3 minutes
- No completion or specific error

**Investigation Steps**:
1. Check if backend is still processing
2. Verify polling mechanism is working
3. Check for 304 Not Modified responses

## Test Results Documentation

### Success Criteria
- [ ] Authentication works correctly
- [ ] PDF upload completes successfully
- [ ] Progress updates occur regularly
- [ ] Process completes within 3 minutes
- [ ] No hanging at 40% progress
- [ ] Specific error messages when failures occur
- [ ] Fallback mechanisms work correctly

### Performance Metrics to Record
- Authentication time: _____ ms
- Upload time: _____ ms
- Total parsing time: _____ ms
- Number of progress updates: _____
- Model selected: _____
- Any timeouts: Yes/No
- Any errors: Yes/No

### Issues Found
Document any issues discovered during testing:

1. **Issue**: ________________________________
   **Severity**: High/Medium/Low
   **Steps to Reproduce**: ____________________
   **Expected vs Actual**: ____________________

2. **Issue**: ________________________________
   **Severity**: High/Medium/Low
   **Steps to Reproduce**: ____________________
   **Expected vs Actual**: ____________________

## Next Steps

Based on test results:
1. **If tests pass**: Validate fixes are working correctly
2. **If tests fail**: Implement additional fixes based on findings
3. **Performance issues**: Optimize based on timing measurements
4. **Error handling issues**: Improve error messages and fallback logic

## Test Report Template

```
PDF Import Manual Test Report
Date: ___________
Tester: ___________
Environment: Development/Staging/Production

Test Results:
- Authentication: PASS/FAIL
- Upload: PASS/FAIL  
- Progress Monitoring: PASS/FAIL
- Completion: PASS/FAIL
- Error Handling: PASS/FAIL
- Performance: PASS/FAIL

Overall Result: PASS/FAIL

Key Findings:
- ________________________________
- ________________________________
- ________________________________

Recommendations:
- ________________________________
- ________________________________
- ________________________________
```
