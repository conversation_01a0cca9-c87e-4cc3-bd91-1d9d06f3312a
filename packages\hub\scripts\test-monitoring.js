#!/usr/bin/env node

/**
 * Test monitoring functionality
 */

const path = require('path');
require('dotenv').config({ path: path.join(__dirname, '../.env.local') });

const axios = require('axios');

const TEST_USER = {
  email: process.env.TEST_USER_EMAIL || '<EMAIL>',
  password: process.env.TEST_USER_PASSWORD || 'Flaremmk123!'
};

const API_BASE = 'http://localhost:3001/api/v1';

async function getAuthToken() {
  try {
    const response = await axios.post(`${API_BASE}/auth/login`, {
      email: TEST_USER.email,
      password: TEST_USER.password
    });
    
    return response.data.data.access_token;
  } catch (error) {
    console.error('Failed to login:', error.response?.data || error.message);
    throw error;
  }
}

async function testMonitoringEndpoints(token) {
  console.log('🧪 Testing Monitoring Endpoints');
  console.log('=' .repeat(50));
  
  const headers = {
    'Authorization': `Bearer ${token}`,
    'Content-Type': 'application/json'
  };
  
  // Test usage metrics
  console.log('\n📊 Testing /monitoring/usage');
  try {
    const response = await axios.get(`${API_BASE}/monitoring/usage`, { headers });
    console.log('✅ Usage metrics:');
    console.log(JSON.stringify(response.data.data, null, 2));
  } catch (error) {
    console.log('❌ Error:', error.response?.data || error.message);
  }
  
  // Test performance metrics
  console.log('\n⚡ Testing /monitoring/performance');
  try {
    const response = await axios.get(`${API_BASE}/monitoring/performance`, { headers });
    console.log('✅ Performance metrics:');
    console.log(JSON.stringify(response.data.data, null, 2));
  } catch (error) {
    console.log('❌ Error:', error.response?.data || error.message);
  }
  
  // Test alerts
  console.log('\n🚨 Testing /monitoring/alerts');
  try {
    const response = await axios.get(`${API_BASE}/monitoring/alerts`, { headers });
    console.log('✅ Active alerts:');
    console.log(JSON.stringify(response.data.data, null, 2));
  } catch (error) {
    console.log('❌ Error:', error.response?.data || error.message);
  }
}

async function simulateUsage(token) {
  console.log('\n🔄 Simulating usage to generate metrics...');
  console.log('=' .repeat(50));
  
  const headers = {
    'Authorization': `Bearer ${token}`,
    'Content-Type': 'application/json'
  };
  
  const testTexts = [
    'Weekend in Paris: Visit Eiffel Tower and Louvre Museum', // < 500 chars
    `15-Day European Adventure
    
    Day 1-3: London
    - Day 1: Arrive Heathrow, check into The Savoy ($450/night), afternoon tea at Fortnum & Mason
    - Day 2: Tower of London, Tower Bridge, Borough Market lunch, West End show
    - Day 3: British Museum, shopping in Covent Garden, departure to Paris
    
    Day 4-6: Paris  
    - Day 4: Eurostar arrival, check into Le Meurice (€500/night), Eiffel Tower
    - Day 5: Louvre, Musée d'Orsay, Seine river cruise
    - Day 6: Versailles day trip, evening at Moulin Rouge` // > 500 chars
  ];
  
  // Make a few parsing requests
  for (let i = 0; i < 3; i++) {
    try {
      const text = testTexts[i % testTexts.length];
      console.log(`\n📝 Request ${i + 1}: Text length = ${text.length} chars`);
      
      const response = await axios.post(`${API_BASE}/import/parse`, {
        text,
        source: 'chatgpt'
      }, { headers });
      
      console.log(`✅ Parsed successfully: ${response.data.data.title}`);
    } catch (error) {
      console.log(`❌ Parse error: ${error.response?.data?.error || error.message}`);
    }
    
    // Small delay between requests
    await new Promise(resolve => setTimeout(resolve, 1000));
  }
}

async function main() {
  try {
    // First, check if the server is running
    try {
      await axios.get(`${API_BASE.replace('/api/v1', '/health')}`);
    } catch (error) {
      console.error('❌ Server is not running. Please start the server with "pnpm dev" first.');
      return;
    }
    
    // Get auth token
    console.log('🔐 Authenticating...');
    const token = await getAuthToken();
    console.log('✅ Authenticated successfully');
    
    // Simulate some usage first
    await simulateUsage(token);
    
    // Wait a bit for metrics to be processed
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // Test monitoring endpoints
    await testMonitoringEndpoints(token);
    
    // Test reset counters (admin only)
    console.log('\n🔄 Testing counter reset (admin only)');
    try {
      const response = await axios.post(`${API_BASE}/monitoring/reset-counters`, {}, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });
      console.log('✅ Counters reset:', response.data.data.message);
    } catch (error) {
      console.log('❌ Reset error:', error.response?.data?.error || error.message);
    }
    
  } catch (error) {
    console.error('Test failed:', error.message);
  }
}

main().catch(console.error);