{"testConfiguration": {"API_BASE_URL": "http://localhost:3001", "TEST_EMAIL": "<EMAIL>", "TEST_PASSWORD": "Flaremmk123!", "PDF_FILE_PATH": "C:\\Users\\<USER>\\Travelviz\\Travelviz\\Updated 15-Day European Travel Itinerary_ London, Madrid, Lisbon and Porto.pdf", "MAX_POLLING_TIME": 300000, "POLLING_INTERVAL": 2000, "TIMEOUT_THRESHOLD": 90000}, "testResults": {"startTime": 1752723210779, "authTime": 999, "uploadTime": 1195, "parsingStartTime": 1752723212975, "parsingEndTime": null, "totalTime": null, "modelSelected": null, "progressUpdates": [{"attempt": 1, "timestamp": 1752723213281, "status": "processing", "progress": 50, "currentStep": "parsing", "pollTime": 306, "elapsedTime": 306}, {"attempt": 2, "timestamp": 1752723215441, "status": "processing", "progress": 50, "currentStep": "parsing", "pollTime": 149, "elapsedTime": 2466}, {"attempt": 3, "timestamp": 1752723217635, "status": "processing", "progress": 50, "currentStep": "parsing", "pollTime": 182, "elapsedTime": 4660}, {"attempt": 4, "timestamp": 1752723219825, "status": "processing", "progress": 50, "currentStep": "parsing", "pollTime": 177, "elapsedTime": 6850}, {"attempt": 5, "timestamp": 1752723221999, "status": "processing", "progress": 50, "currentStep": "parsing", "pollTime": 170, "elapsedTime": 9024}, {"attempt": 6, "timestamp": 1752723224171, "status": "processing", "progress": 50, "currentStep": "parsing", "pollTime": 157, "elapsedTime": 11196}, {"attempt": 7, "timestamp": 1752723226374, "status": "processing", "progress": 50, "currentStep": "parsing", "pollTime": 203, "elapsedTime": 13399}, {"attempt": 8, "timestamp": 1752723228547, "status": "processing", "progress": 50, "currentStep": "parsing", "pollTime": 167, "elapsedTime": 15572}, {"attempt": 9, "timestamp": 1752723230732, "status": "processing", "progress": 50, "currentStep": "parsing", "pollTime": 175, "elapsedTime": 17757}, {"attempt": 10, "timestamp": 1752723232915, "status": "processing", "progress": 50, "currentStep": "parsing", "pollTime": 182, "elapsedTime": 19940}, {"attempt": 11, "timestamp": 1752723235108, "status": "processing", "progress": 50, "currentStep": "parsing", "pollTime": 180, "elapsedTime": 22133}, {"attempt": 12, "timestamp": 1752723237297, "status": "processing", "progress": 50, "currentStep": "parsing", "pollTime": 176, "elapsedTime": 24322}, {"attempt": 13, "timestamp": 1752723239481, "status": "processing", "progress": 50, "currentStep": "parsing", "pollTime": 181, "elapsedTime": 26506}, {"attempt": 14, "timestamp": 1752723241705, "status": "processing", "progress": 50, "currentStep": "parsing", "pollTime": 210, "elapsedTime": 28730}, {"attempt": 15, "timestamp": 1752723243901, "status": "processing", "progress": 50, "currentStep": "parsing", "pollTime": 194, "elapsedTime": 30926}, {"attempt": 16, "timestamp": 1752723246106, "status": "processing", "progress": 50, "currentStep": "parsing", "pollTime": 199, "elapsedTime": 33131}, {"attempt": 17, "timestamp": 1752723248307, "status": "processing", "progress": 50, "currentStep": "parsing", "pollTime": 193, "elapsedTime": 35332}, {"attempt": 18, "timestamp": 1752723250584, "status": "processing", "progress": 50, "currentStep": "parsing", "pollTime": 263, "elapsedTime": 37609}, {"attempt": 19, "timestamp": 1752723252804, "status": "processing", "progress": 50, "currentStep": "parsing", "pollTime": 211, "elapsedTime": 39829}, {"attempt": 20, "timestamp": 1752723255007, "status": "processing", "progress": 50, "currentStep": "parsing", "pollTime": 198, "elapsedTime": 42032}, {"attempt": 21, "timestamp": 1752723257235, "status": "processing", "progress": 50, "currentStep": "parsing", "pollTime": 225, "elapsedTime": 44260}, {"attempt": 22, "timestamp": 1752723259440, "status": "processing", "progress": 50, "currentStep": "parsing", "pollTime": 201, "elapsedTime": 46465}, {"attempt": 23, "timestamp": 1752723261636, "status": "processing", "progress": 50, "currentStep": "parsing", "pollTime": 182, "elapsedTime": 48661}, {"attempt": 24, "timestamp": 1752723263810, "status": "processing", "progress": 50, "currentStep": "parsing", "pollTime": 171, "elapsedTime": 50835}, {"attempt": 25, "timestamp": 1752723265980, "status": "processing", "progress": 50, "currentStep": "parsing", "pollTime": 163, "elapsedTime": 53005}, {"attempt": 26, "timestamp": 1752723268146, "status": "processing", "progress": 50, "currentStep": "parsing", "pollTime": 159, "elapsedTime": 55171}, {"attempt": 27, "timestamp": 1752723270333, "status": "processing", "progress": 50, "currentStep": "parsing", "pollTime": 179, "elapsedTime": 57358}, {"attempt": 28, "timestamp": 1752723272524, "status": "processing", "progress": 50, "currentStep": "parsing", "pollTime": 185, "elapsedTime": 59549}, {"attempt": 29, "timestamp": 1752723274759, "status": "processing", "progress": 50, "currentStep": "parsing", "pollTime": 226, "elapsedTime": 61784}, {"attempt": 30, "timestamp": 1752723276946, "status": "processing", "progress": 50, "currentStep": "parsing", "pollTime": 177, "elapsedTime": 63971}, {"attempt": 31, "timestamp": 1752723279133, "status": "processing", "progress": 50, "currentStep": "parsing", "pollTime": 175, "elapsedTime": 66158}, {"attempt": 32, "timestamp": 1752723281313, "status": "processing", "progress": 50, "currentStep": "parsing", "pollTime": 177, "elapsedTime": 68338}, {"attempt": 33, "timestamp": 1752723283496, "status": "processing", "progress": 50, "currentStep": "parsing", "pollTime": 178, "elapsedTime": 70521}, {"attempt": 34, "timestamp": 1752723285676, "status": "processing", "progress": 50, "currentStep": "parsing", "pollTime": 178, "elapsedTime": 72701}, {"attempt": 35, "timestamp": 1752723287875, "status": "processing", "progress": 50, "currentStep": "parsing", "pollTime": 187, "elapsedTime": 74900}, {"attempt": 36, "timestamp": 1752723290055, "status": "processing", "progress": 50, "currentStep": "parsing", "pollTime": 172, "elapsedTime": 77080}, {"attempt": 37, "timestamp": 1752723292273, "status": "processing", "progress": 50, "currentStep": "parsing", "pollTime": 215, "elapsedTime": 79298}, {"attempt": 38, "timestamp": 1752723294473, "status": "processing", "progress": 50, "currentStep": "parsing", "pollTime": 187, "elapsedTime": 81498}, {"attempt": 39, "timestamp": 1752723296648, "status": "processing", "progress": 50, "currentStep": "parsing", "pollTime": 158, "elapsedTime": 83673}, {"attempt": 40, "timestamp": 1752723298864, "status": "processing", "progress": 50, "currentStep": "parsing", "pollTime": 203, "elapsedTime": 85889}, {"attempt": 41, "timestamp": 1752723301051, "status": "processing", "progress": 50, "currentStep": "parsing", "pollTime": 173, "elapsedTime": 88076}, {"attempt": 42, "timestamp": 1752723303264, "status": "processing", "progress": 50, "currentStep": "parsing", "pollTime": 199, "elapsedTime": 90289}, {"attempt": 43, "timestamp": 1752723305430, "status": "processing", "progress": 50, "currentStep": "parsing", "pollTime": 162, "elapsedTime": 92455}, {"attempt": 44, "timestamp": 1752723307674, "status": "processing", "progress": 50, "currentStep": "parsing", "pollTime": 229, "elapsedTime": 94699}, {"attempt": 45, "timestamp": 1752723309843, "status": "processing", "progress": 50, "currentStep": "parsing", "pollTime": 160, "elapsedTime": 96868}, {"attempt": 46, "timestamp": 1752723312044, "status": "processing", "progress": 50, "currentStep": "parsing", "pollTime": 189, "elapsedTime": 99069}, {"attempt": 47, "timestamp": 1752723314219, "status": "processing", "progress": 50, "currentStep": "parsing", "pollTime": 170, "elapsedTime": 101244}, {"attempt": 48, "timestamp": 1752723316403, "status": "processing", "progress": 50, "currentStep": "parsing", "pollTime": 184, "elapsedTime": 103428}, {"attempt": 49, "timestamp": 1752723318569, "status": "processing", "progress": 50, "currentStep": "parsing", "pollTime": 165, "elapsedTime": 105594}, {"attempt": 50, "timestamp": 1752723320749, "status": "processing", "progress": 50, "currentStep": "parsing", "pollTime": 177, "elapsedTime": 107774}, {"attempt": 51, "timestamp": 1752723322928, "status": "processing", "progress": 50, "currentStep": "parsing", "pollTime": 174, "elapsedTime": 109953}, {"attempt": 52, "timestamp": 1752723325084, "status": "processing", "progress": 50, "currentStep": "parsing", "pollTime": 155, "elapsedTime": 112109}, {"attempt": 53, "timestamp": 1752723327258, "status": "processing", "progress": 50, "currentStep": "parsing", "pollTime": 164, "elapsedTime": 114283}, {"attempt": 54, "timestamp": 1752723329454, "status": "processing", "progress": 50, "currentStep": "parsing", "pollTime": 188, "elapsedTime": 116479}, {"attempt": 55, "timestamp": 1752723331664, "status": "processing", "progress": 50, "currentStep": "parsing", "pollTime": 206, "elapsedTime": 118689}, {"attempt": 56, "timestamp": 1752723333859, "status": "processing", "progress": 50, "currentStep": "parsing", "pollTime": 190, "elapsedTime": 120884}, {"attempt": 57, "timestamp": 1752723336047, "status": "processing", "progress": 50, "currentStep": "parsing", "pollTime": 173, "elapsedTime": 123072}, {"attempt": 58, "timestamp": 1752723338222, "status": "processing", "progress": 50, "currentStep": "parsing", "pollTime": 169, "elapsedTime": 125247}, {"attempt": 59, "timestamp": 1752723340396, "status": "processing", "progress": 50, "currentStep": "parsing", "pollTime": 162, "elapsedTime": 127421}, {"attempt": 60, "timestamp": 1752723342566, "status": "processing", "progress": 50, "currentStep": "parsing", "pollTime": 165, "elapsedTime": 129591}, {"attempt": 61, "timestamp": 1752723344741, "status": "processing", "progress": 50, "currentStep": "parsing", "pollTime": 172, "elapsedTime": 131766}, {"attempt": 62, "timestamp": 1752723346933, "status": "processing", "progress": 50, "currentStep": "parsing", "pollTime": 179, "elapsedTime": 133958}, {"attempt": 63, "timestamp": 1752723349119, "status": "processing", "progress": 50, "currentStep": "parsing", "pollTime": 185, "elapsedTime": 136144}, {"attempt": 64, "timestamp": 1752723351320, "status": "processing", "progress": 50, "currentStep": "parsing", "pollTime": 186, "elapsedTime": 138345}, {"attempt": 65, "timestamp": 1752723353517, "status": "processing", "progress": 50, "currentStep": "parsing", "pollTime": 183, "elapsedTime": 140542}, {"attempt": 66, "timestamp": 1752723355707, "status": "processing", "progress": 50, "currentStep": "parsing", "pollTime": 178, "elapsedTime": 142732}, {"attempt": 67, "timestamp": 1752723357887, "status": "processing", "progress": 50, "currentStep": "parsing", "pollTime": 170, "elapsedTime": 144912}, {"attempt": 68, "timestamp": 1752723360084, "status": "processing", "progress": 50, "currentStep": "parsing", "pollTime": 185, "elapsedTime": 147109}, {"attempt": 69, "timestamp": 1752723362280, "status": "processing", "progress": 50, "currentStep": "parsing", "pollTime": 195, "elapsedTime": 149305}, {"attempt": 70, "timestamp": 1752723364542, "status": "processing", "progress": 50, "currentStep": "parsing", "pollTime": 258, "elapsedTime": 151567}, {"attempt": 71, "timestamp": 1752723366789, "status": "processing", "progress": 50, "currentStep": "parsing", "pollTime": 241, "elapsedTime": 153814}, {"attempt": 72, "timestamp": 1752723369016, "status": "processing", "progress": 50, "currentStep": "parsing", "pollTime": 213, "elapsedTime": 156041}, {"attempt": 73, "timestamp": 1752723371226, "status": "processing", "progress": 50, "currentStep": "parsing", "pollTime": 197, "elapsedTime": 158251}, {"attempt": 74, "timestamp": 1752723373435, "status": "processing", "progress": 50, "currentStep": "parsing", "pollTime": 197, "elapsedTime": 160460}, {"attempt": 75, "timestamp": 1752723375619, "status": "processing", "progress": 50, "currentStep": "parsing", "pollTime": 175, "elapsedTime": 162644}, {"attempt": 76, "timestamp": 1752723377843, "status": "processing", "progress": 50, "currentStep": "parsing", "pollTime": 215, "elapsedTime": 164868}, {"attempt": 77, "timestamp": 1752723380001, "status": "processing", "progress": 50, "currentStep": "parsing", "pollTime": 153, "elapsedTime": 167026}, {"attempt": 78, "timestamp": 1752723382189, "status": "processing", "progress": 50, "currentStep": "parsing", "pollTime": 172, "elapsedTime": 169214}, {"attempt": 79, "timestamp": 1752723384381, "status": "processing", "progress": 50, "currentStep": "parsing", "pollTime": 182, "elapsedTime": 171406}, {"attempt": 80, "timestamp": 1752723386567, "status": "processing", "progress": 50, "currentStep": "parsing", "pollTime": 180, "elapsedTime": 173592}, {"attempt": 81, "timestamp": 1752723388745, "status": "processing", "progress": 50, "currentStep": "parsing", "pollTime": 166, "elapsedTime": 175770}, {"attempt": 82, "timestamp": 1752723390907, "status": "processing", "progress": 50, "currentStep": "parsing", "pollTime": 160, "elapsedTime": 177932}, {"attempt": 83, "timestamp": 1752723393174, "status": "processing", "progress": 50, "currentStep": "parsing", "pollTime": 261, "elapsedTime": 180199}, {"attempt": 84, "timestamp": 1752723395372, "status": "processing", "progress": 50, "currentStep": "parsing", "pollTime": 183, "elapsedTime": 182397}, {"attempt": 85, "timestamp": 1752723397575, "status": "processing", "progress": 50, "currentStep": "parsing", "pollTime": 196, "elapsedTime": 184600}, {"attempt": 86, "timestamp": 1752723399771, "status": "processing", "progress": 50, "currentStep": "parsing", "pollTime": 182, "elapsedTime": 186796}, {"attempt": 87, "timestamp": 1752723401941, "status": "processing", "progress": 50, "currentStep": "parsing", "pollTime": 157, "elapsedTime": 188966}, {"attempt": 88, "timestamp": 1752723404116, "status": "processing", "progress": 50, "currentStep": "parsing", "pollTime": 173, "elapsedTime": 191141}, {"attempt": 89, "timestamp": 1752723406273, "status": "processing", "progress": 50, "currentStep": "parsing", "pollTime": 155, "elapsedTime": 193298}, {"attempt": 90, "timestamp": 1752723408474, "status": "processing", "progress": 50, "currentStep": "parsing", "pollTime": 187, "elapsedTime": 195499}, {"attempt": 91, "timestamp": 1752723410646, "status": "processing", "progress": 50, "currentStep": "parsing", "pollTime": 162, "elapsedTime": 197671}, {"attempt": 92, "timestamp": 1752723412825, "status": "processing", "progress": 50, "currentStep": "parsing", "pollTime": 167, "elapsedTime": 199850}, {"attempt": 93, "timestamp": 1752723414999, "status": "processing", "progress": 50, "currentStep": "parsing", "pollTime": 161, "elapsedTime": 202024}, {"attempt": 94, "timestamp": 1752723417163, "status": "processing", "progress": 50, "currentStep": "parsing", "pollTime": 150, "elapsedTime": 204188}, {"attempt": 95, "timestamp": 1752723419349, "status": "processing", "progress": 50, "currentStep": "parsing", "pollTime": 174, "elapsedTime": 206374}, {"attempt": 96, "timestamp": 1752723421520, "status": "processing", "progress": 50, "currentStep": "parsing", "pollTime": 164, "elapsedTime": 208545}, {"attempt": 97, "timestamp": 1752723423688, "status": "processing", "progress": 50, "currentStep": "parsing", "pollTime": 154, "elapsedTime": 210713}, {"attempt": 98, "timestamp": 1752723425886, "status": "processing", "progress": 50, "currentStep": "parsing", "pollTime": 193, "elapsedTime": 212911}, {"attempt": 99, "timestamp": 1752723428069, "status": "processing", "progress": 50, "currentStep": "parsing", "pollTime": 170, "elapsedTime": 215094}, {"attempt": 100, "timestamp": 1752723430268, "status": "processing", "progress": 50, "currentStep": "parsing", "pollTime": 184, "elapsedTime": 217293}, {"attempt": 101, "timestamp": 1752723432442, "status": "processing", "progress": 50, "currentStep": "parsing", "pollTime": 163, "elapsedTime": 219467}, {"attempt": 102, "timestamp": 1752723434602, "status": "processing", "progress": 50, "currentStep": "parsing", "pollTime": 159, "elapsedTime": 221627}, {"attempt": 103, "timestamp": 1752723436781, "status": "processing", "progress": 50, "currentStep": "parsing", "pollTime": 167, "elapsedTime": 223806}, {"attempt": 104, "timestamp": 1752723439013, "status": "processing", "progress": 50, "currentStep": "parsing", "pollTime": 226, "elapsedTime": 226038}, {"attempt": 105, "timestamp": 1752723441187, "status": "processing", "progress": 50, "currentStep": "parsing", "pollTime": 164, "elapsedTime": 228212}, {"attempt": 106, "timestamp": 1752723443360, "status": "processing", "progress": 50, "currentStep": "parsing", "pollTime": 166, "elapsedTime": 230385}, {"attempt": 107, "timestamp": 1752723445548, "status": "processing", "progress": 50, "currentStep": "parsing", "pollTime": 173, "elapsedTime": 232573}, {"attempt": 108, "timestamp": 1752723447724, "status": "processing", "progress": 50, "currentStep": "parsing", "pollTime": 171, "elapsedTime": 234749}, {"attempt": 109, "timestamp": 1752723449912, "status": "processing", "progress": 50, "currentStep": "parsing", "pollTime": 181, "elapsedTime": 236937}, {"attempt": 110, "timestamp": 1752723452090, "status": "processing", "progress": 50, "currentStep": "parsing", "pollTime": 174, "elapsedTime": 239115}, {"attempt": 111, "timestamp": 1752723454298, "status": "processing", "progress": 50, "currentStep": "parsing", "pollTime": 205, "elapsedTime": 241323}, {"attempt": 112, "timestamp": 1752723456536, "status": "processing", "progress": 50, "currentStep": "parsing", "pollTime": 222, "elapsedTime": 243561}, {"attempt": 113, "timestamp": 1752723458716, "status": "processing", "progress": 50, "currentStep": "parsing", "pollTime": 173, "elapsedTime": 245741}, {"attempt": 114, "timestamp": 1752723460894, "status": "processing", "progress": 50, "currentStep": "parsing", "pollTime": 177, "elapsedTime": 247919}, {"attempt": 115, "timestamp": 1752723463084, "status": "processing", "progress": 50, "currentStep": "parsing", "pollTime": 183, "elapsedTime": 250109}, {"attempt": 116, "timestamp": 1752723465277, "status": "processing", "progress": 50, "currentStep": "parsing", "pollTime": 189, "elapsedTime": 252302}, {"attempt": 117, "timestamp": 1752723467456, "status": "processing", "progress": 50, "currentStep": "parsing", "pollTime": 172, "elapsedTime": 254481}, {"attempt": 118, "timestamp": 1752723469616, "status": "processing", "progress": 50, "currentStep": "parsing", "pollTime": 153, "elapsedTime": 256641}, {"attempt": 119, "timestamp": 1752723471806, "status": "processing", "progress": 50, "currentStep": "parsing", "pollTime": 177, "elapsedTime": 258831}, {"attempt": 120, "timestamp": 1752723474004, "status": "processing", "progress": 50, "currentStep": "parsing", "pollTime": 183, "elapsedTime": 261029}, {"attempt": 121, "timestamp": 1752723476171, "status": "processing", "progress": 50, "currentStep": "parsing", "pollTime": 158, "elapsedTime": 263196}, {"attempt": 122, "timestamp": 1752723478353, "status": "processing", "progress": 50, "currentStep": "parsing", "pollTime": 180, "elapsedTime": 265378}, {"attempt": 123, "timestamp": 1752723480542, "status": "processing", "progress": 50, "currentStep": "parsing", "pollTime": 177, "elapsedTime": 267567}, {"attempt": 124, "timestamp": 1752723482704, "status": "processing", "progress": 50, "currentStep": "parsing", "pollTime": 155, "elapsedTime": 269729}, {"attempt": 125, "timestamp": 1752723484911, "status": "processing", "progress": 50, "currentStep": "parsing", "pollTime": 206, "elapsedTime": 271936}, {"attempt": 126, "timestamp": 1752723487083, "status": "processing", "progress": 50, "currentStep": "parsing", "pollTime": 163, "elapsedTime": 274108}, {"attempt": 127, "timestamp": 1752723489271, "status": "processing", "progress": 50, "currentStep": "parsing", "pollTime": 172, "elapsedTime": 276296}, {"attempt": 128, "timestamp": 1752723491468, "status": "processing", "progress": 50, "currentStep": "parsing", "pollTime": 185, "elapsedTime": 278493}, {"attempt": 129, "timestamp": 1752723493651, "status": "processing", "progress": 50, "currentStep": "parsing", "pollTime": 172, "elapsedTime": 280676}, {"attempt": 130, "timestamp": 1752723495842, "status": "processing", "progress": 50, "currentStep": "parsing", "pollTime": 179, "elapsedTime": 282867}, {"attempt": 131, "timestamp": 1752723498017, "status": "processing", "progress": 50, "currentStep": "parsing", "pollTime": 174, "elapsedTime": 285042}, {"attempt": 132, "timestamp": 1752723500203, "status": "processing", "progress": 50, "currentStep": "parsing", "pollTime": 184, "elapsedTime": 287228}, {"attempt": 133, "timestamp": 1752723502371, "status": "processing", "progress": 50, "currentStep": "parsing", "pollTime": 163, "elapsedTime": 289396}, {"attempt": 134, "timestamp": 1752723504553, "status": "processing", "progress": 50, "currentStep": "parsing", "pollTime": 172, "elapsedTime": 291578}, {"attempt": 135, "timestamp": 1752723506736, "status": "processing", "progress": 50, "currentStep": "parsing", "pollTime": 177, "elapsedTime": 293761}, {"attempt": 136, "timestamp": 1752723508902, "status": "processing", "progress": 50, "currentStep": "parsing", "pollTime": 163, "elapsedTime": 295927}, {"attempt": 137, "timestamp": 1752723511072, "status": "processing", "progress": 50, "currentStep": "parsing", "pollTime": 164, "elapsedTime": 298097}], "errors": [{"timestamp": "2025-07-17T03:38:33.087Z", "level": "ERROR", "message": "Test failed", "error": "Polling timed out after 300 seconds"}], "timeouts": [], "circuitBreakerEvents": [], "performanceMetrics": {}, "success": false}, "summary": {"success": false, "totalTime": null, "keyFindings": ["1 errors encountered", "Process appeared stuck with no progress updates"]}}