import { Express } from 'express';
import { createServer } from '../../../src/server';
import { getSupabaseClient } from '../../../src/lib/supabase';
import request from 'supertest';
import dotenv from 'dotenv';
import path from 'path';

// Load environment variables from .env.local
dotenv.config({ path: path.join(__dirname, '../../../../.env.local') });

/**
 * Test server utilities for integration tests
 * Provides real server setup with authentication
 */
export class TestServer {
  private static instance: TestServer;
  private app: Express | null = null;
  private server: any = null;
  private authToken: string | null = null;

  private constructor() {}

  static getInstance(): TestServer {
    if (!TestServer.instance) {
      TestServer.instance = new TestServer();
    }
    return TestServer.instance;
  }

  async setup(): Promise<void> {
    if (this.app) {
      return; // Already set up
    }

    console.log('🚀 Setting up test server...');
    
    // Create server
    this.app = createServer();
    this.server = this.app.listen(0);

    // Authenticate with real Supabase
    await this.authenticate();
    
    console.log('✅ Test server ready');
  }

  async teardown(): Promise<void> {
    if (this.server) {
      this.server.close();
      this.server = null;
    }
    this.app = null;
    this.authToken = null;
    console.log('✅ Test server shut down');
  }

  private async authenticate(): Promise<void> {
    const TEST_USER_EMAIL = process.env.TEST_USER_EMAIL || '<EMAIL>';
    const TEST_USER_PASSWORD = process.env.TEST_USER_PASSWORD || 'Flaremmk123!';

    const supabase = getSupabaseClient();
    const { data, error } = await supabase.auth.signInWithPassword({
      email: TEST_USER_EMAIL,
      password: TEST_USER_PASSWORD
    });

    if (error) {
      console.error('Auth error:', error);
      throw new Error(`Failed to authenticate: ${error.message}`);
    }

    this.authToken = data.session?.access_token || '';
    console.log('✅ Got auth token');
  }

  getApp(): Express {
    if (!this.app) {
      throw new Error('Test server not set up. Call setup() first.');
    }
    return this.app;
  }

  getAuthToken(): string {
    if (!this.authToken) {
      throw new Error('Not authenticated. Call setup() first.');
    }
    return this.authToken;
  }

  /**
   * Make authenticated request
   */
  request() {
    return request(this.getApp());
  }

  /**
   * Make authenticated request with auth header
   */
  authenticatedRequest() {
    return request(this.getApp()).set('Authorization', `Bearer ${this.getAuthToken()}`);
  }

  /**
   * Helper for common API endpoint patterns
   */
  async post(endpoint: string, data: any) {
    return request(this.getApp())
      .post(endpoint)
      .set('Authorization', `Bearer ${this.getAuthToken()}`)
      .send(data);
  }

  async get(endpoint: string) {
    return request(this.getApp())
      .get(endpoint)
      .set('Authorization', `Bearer ${this.getAuthToken()}`);
  }

  async put(endpoint: string, data: any) {
    return request(this.getApp())
      .put(endpoint)
      .set('Authorization', `Bearer ${this.getAuthToken()}`)
      .send(data);
  }

  async delete(endpoint: string) {
    return request(this.getApp())
      .delete(endpoint)
      .set('Authorization', `Bearer ${this.getAuthToken()}`);
  }
}

/**
 * Global test server instance for reuse across tests
 */
export const testServer = TestServer.getInstance();