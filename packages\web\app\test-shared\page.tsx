'use client';

import { createSuccessResponse, APP_CONFIG } from '@travelviz/shared';

export default function TestSharedPage() {
  const response = createSuccessResponse({ message: 'Testing shared package import!' });
  
  return (
    <div className="container mx-auto p-8">
      <h1 className="text-2xl font-bold mb-4">Shared Package Test</h1>
      <div className="space-y-4">
        <div className="bg-green-100 p-4 rounded">
          <h2 className="font-semibold">✅ Shared Function Test</h2>
          <pre className="mt-2 text-sm">{JSON.stringify(response, null, 2)}</pre>
        </div>
        <div className="bg-blue-100 p-4 rounded">
          <h2 className="font-semibold">✅ Shared Constants Test</h2>
          <p>App Name: {APP_CONFIG.NAME}</p>
          <p>Version: {APP_CONFIG.VERSION}</p>
          <p>Description: {APP_CONFIG.DESCRIPTION}</p>
        </div>
      </div>
    </div>
  );
} 