/**
 * AI Import Flow E2E Test
 * 
 * Tests the AI import functionality with real travel content
 * Uses the proven authentication pattern for setup
 */

const { test, expect } = require('@playwright/test');
const { LoginPage, ImportPage } = require('./utils/page-objects');
const testConfig = require('../test.config');

// Test configuration
const TEST_USER = {
  email: testConfig.auth.testUserEmail,
  password: testConfig.auth.testUserPassword
};

// Different types of travel content to test
const TRAVEL_CONTENTS = {
  chatgpt: `
User: I'm planning a 4-day trip to Paris. Can you help me create an itinerary?

ChatGPT: I'd be happy to help you plan a 4-day Paris itinerary! Here's a comprehensive plan:

Day 1: Classic Paris
- Morning: Visit the Eiffel Tower (€25 elevator to top)
- Afternoon: Seine River cruise (€15)
- Evening: Dinner in Saint-Germain district

Day 2: Art and Culture
- Morning: Louvre Museum (€17 entrance)
- Afternoon: Walk through Tuileries Garden
- Evening: Montmartre and Sacré-Cœur

Day 3: Royal Paris
- Morning: Palace of Versailles day trip (€20 entrance)
- Afternoon: Explore Versailles Gardens
- Evening: Return to Paris, dinner in Le Marais

Day 4: Local Experience
- Morning: Latin Quarter exploration
- Afternoon: Notre-Dame area (exterior viewing)
- Evening: Farewell dinner at a bistro

Total estimated cost: €400-500 per person
  `,
  
  claude: `
Human: Can you help me plan a weekend getaway to Barcelona?

Claude: I'd love to help you plan a Barcelona weekend! Here's a perfect 2-day itinerary:

Saturday:
- 9:00 AM: Breakfast at a local café
- 10:00 AM: Explore Park Güell (€10 entrance)
- 1:00 PM: Lunch in Gràcia neighborhood
- 3:00 PM: Visit Sagrada Familia (€26 entrance)
- 6:00 PM: Walk down Las Ramblas
- 8:00 PM: Dinner in Gothic Quarter

Sunday:
- 10:00 AM: Visit Casa Batlló (€25 entrance)
- 12:00 PM: Explore Boqueria Market
- 2:00 PM: Lunch at the market
- 4:00 PM: Relax at Barceloneta Beach
- 7:00 PM: Sunset drinks at a rooftop bar
- 9:00 PM: Traditional tapas dinner

Budget: Around €300-400 for the weekend
  `,
  
  gemini: `
User: I need help planning a 5-day trip to Tokyo for cherry blossom season.

Gemini: Here's a wonderful 5-day Tokyo cherry blossom itinerary:

Day 1: Arrival and Shibuya
- Arrive at Narita Airport
- Check into hotel in Shibuya
- Evening: Shibuya Crossing and Hachiko statue
- Dinner: Ramen in Shibuya

Day 2: Traditional Tokyo and Sakura
- Morning: Senso-ji Temple in Asakusa
- Afternoon: Ueno Park for cherry blossoms (free)
- Evening: Traditional dinner in Asakusa

Day 3: Modern Tokyo
- Morning: Tokyo Skytree (¥2,100 entrance)
- Afternoon: Harajuku and Takeshita Street
- Evening: Shibuya Sky observation deck (¥1,800)

Day 4: Imperial Palace and Ginza
- Morning: East Gardens of Imperial Palace (free)
- Afternoon: Shopping in Ginza
- Evening: High-end dining experience

Day 5: Final Exploration
- Morning: Meiji Shrine
- Afternoon: Last-minute shopping in Shinjuku
- Evening: Departure

Best time: Late March to early May for sakura
  `
};

test.describe('AI Import Flow', () => {
  test.beforeEach(async ({ page }) => {
    // Set up test isolation
    await page.context().clearCookies();
    await page.context().clearPermissions();
    
    // Login before each test
    const loginPage = new LoginPage(page);
    await loginPage.navigate();
    await loginPage.login(TEST_USER.email, TEST_USER.password);
    const loginSuccess = await loginPage.expectLoginSuccess();
    expect(loginSuccess).toBe(true);
    
    // Create test results directory
    const fs = require('fs');
    const path = require('path');
    const resultsDir = path.join(__dirname, '../../test-results');
    if (!fs.existsSync(resultsDir)) {
      fs.mkdirSync(resultsDir, { recursive: true });
    }
  });

  test('should import ChatGPT travel conversation', async ({ page }) => {
    console.log('🤖 Testing ChatGPT import...');
    
    const importPage = new ImportPage(page);
    await importPage.navigate();
    expect(await importPage.isLoaded()).toBe(true);
    
    await page.screenshot({ 
      path: 'test-results/import-chatgpt-01-page.png',
      fullPage: true 
    });
    
    // Paste ChatGPT content
    await importPage.pasteContent(TRAVEL_CONTENTS.chatgpt);
    console.log('   ✅ ChatGPT content pasted');
    
    // Select source if available
    try {
      await importPage.selectSource('chatgpt');
      console.log('   ✅ ChatGPT source selected');
    } catch (error) {
      console.log('   ℹ️  Source selection not available');
    }
    
    await page.screenshot({ 
      path: 'test-results/import-chatgpt-02-content.png',
      fullPage: true 
    });
    
    // Start import
    await importPage.startImport();
    console.log('   ✅ Import started');
    
    // Wait for processing
    await page.waitForTimeout(10000); // Give it time to process
    
    await page.screenshot({ 
      path: 'test-results/import-chatgpt-03-processing.png',
      fullPage: true 
    });
    
    // Check for success indicators
    const importComplete = await importPage.waitForImportComplete();
    if (importComplete) {
      console.log('   ✅ Import completed');
      
      const importSuccess = await importPage.expectImportSuccess();
      expect(importSuccess).toBe(true);
      
      await page.screenshot({ 
        path: 'test-results/import-chatgpt-04-success.png',
        fullPage: true 
      });
    } else {
      console.log('   ⚠️  Import completion not detected - checking current state');
      
      // Check if we have any trip data displayed
      const hasContent = await page.locator('text=Paris, text=Eiffel Tower, text=Louvre').first().isVisible({ timeout: 5000 }).catch(() => false);
      if (hasContent) {
        console.log('   ✅ Trip content detected on page');
      }
      
      await page.screenshot({ 
        path: 'test-results/import-chatgpt-04-timeout.png',
        fullPage: true 
      });
    }
    
    console.log('🎉 ChatGPT import test completed');
  });

  test('should import Claude travel conversation', async ({ page }) => {
    console.log('🧠 Testing Claude import...');
    
    const importPage = new ImportPage(page);
    await importPage.navigate();
    expect(await importPage.isLoaded()).toBe(true);
    
    await importPage.pasteContent(TRAVEL_CONTENTS.claude);
    console.log('   ✅ Claude content pasted');
    
    try {
      await importPage.selectSource('claude');
      console.log('   ✅ Claude source selected');
    } catch (error) {
      console.log('   ℹ️  Source selection not available');
    }
    
    await page.screenshot({ 
      path: 'test-results/import-claude-01-content.png',
      fullPage: true 
    });
    
    await importPage.startImport();
    console.log('   ✅ Import started');
    
    await page.waitForTimeout(10000);
    
    // Check for Barcelona-specific content
    const hasBarcelonaContent = await page.locator('text=Barcelona, text=Sagrada Familia, text=Park Güell').first().isVisible({ timeout: 5000 }).catch(() => false);
    
    await page.screenshot({ 
      path: 'test-results/import-claude-02-result.png',
      fullPage: true 
    });
    
    if (hasBarcelonaContent) {
      console.log('   ✅ Barcelona trip content detected');
    } else {
      console.log('   ℹ️  Barcelona content not immediately visible');
    }
    
    console.log('🎉 Claude import test completed');
  });

  test('should import Gemini travel conversation', async ({ page }) => {
    console.log('💎 Testing Gemini import...');
    
    const importPage = new ImportPage(page);
    await importPage.navigate();
    expect(await importPage.isLoaded()).toBe(true);
    
    await importPage.pasteContent(TRAVEL_CONTENTS.gemini);
    console.log('   ✅ Gemini content pasted');
    
    try {
      await importPage.selectSource('gemini');
      console.log('   ✅ Gemini source selected');
    } catch (error) {
      console.log('   ℹ️  Source selection not available');
    }
    
    await page.screenshot({ 
      path: 'test-results/import-gemini-01-content.png',
      fullPage: true 
    });
    
    await importPage.startImport();
    console.log('   ✅ Import started');
    
    await page.waitForTimeout(10000);
    
    // Check for Tokyo-specific content
    const hasTokyoContent = await page.locator('text=Tokyo, text=Senso-ji, text=Skytree').first().isVisible({ timeout: 5000 }).catch(() => false);
    
    await page.screenshot({ 
      path: 'test-results/import-gemini-02-result.png',
      fullPage: true 
    });
    
    if (hasTokyoContent) {
      console.log('   ✅ Tokyo trip content detected');
    } else {
      console.log('   ℹ️  Tokyo content not immediately visible');
    }
    
    console.log('🎉 Gemini import test completed');
  });

  test('should handle import errors gracefully', async ({ page }) => {
    console.log('🚫 Testing import error handling...');
    
    const importPage = new ImportPage(page);
    await importPage.navigate();
    expect(await importPage.isLoaded()).toBe(true);
    
    // Test with non-travel content
    const nonTravelContent = "This is not travel content. It's about cooking recipes and programming.";
    
    await importPage.pasteContent(nonTravelContent);
    console.log('   ✅ Non-travel content pasted');
    
    await page.screenshot({ 
      path: 'test-results/import-error-01-content.png',
      fullPage: true 
    });
    
    await importPage.startImport();
    console.log('   ✅ Import started with non-travel content');
    
    await page.waitForTimeout(10000);
    
    // Should either show error or handle gracefully
    const hasError = await page.locator('.error, [data-testid="error"], .toast-error').first().isVisible({ timeout: 5000 }).catch(() => false);
    const hasEmptyResult = await page.locator('text=No activities found, text=No trip data').first().isVisible({ timeout: 5000 }).catch(() => false);
    
    await page.screenshot({ 
      path: 'test-results/import-error-02-result.png',
      fullPage: true 
    });
    
    if (hasError) {
      console.log('   ✅ Error message displayed appropriately');
    } else if (hasEmptyResult) {
      console.log('   ✅ Empty result handled gracefully');
    } else {
      console.log('   ℹ️  Error handling behavior not immediately visible');
    }
    
    console.log('🎉 Error handling test completed');
  });

  test('should handle empty import content', async ({ page }) => {
    console.log('📭 Testing empty import content...');
    
    const importPage = new ImportPage(page);
    await importPage.navigate();
    expect(await importPage.isLoaded()).toBe(true);
    
    // Try to start import with empty content
    try {
      await importPage.startImport();
      console.log('   ℹ️  Import button clicked with empty content');
      
      await page.waitForTimeout(3000);
      
      // Should show validation error or prevent submission
      const hasValidationError = await page.locator('text=required, text=empty, text=content').first().isVisible({ timeout: 5000 }).catch(() => false);
      
      await page.screenshot({ 
        path: 'test-results/import-empty-validation.png',
        fullPage: true 
      });
      
      if (hasValidationError) {
        console.log('   ✅ Validation error shown for empty content');
      } else {
        console.log('   ℹ️  No validation error visible');
      }
      
    } catch (error) {
      console.log('   ✅ Import prevented with empty content');
    }
    
    console.log('🎉 Empty content test completed');
  });

  test.afterEach(async ({ page }, testInfo) => {
    // Take screenshot on failure
    if (testInfo.status !== testInfo.expectedStatus) {
      const screenshotPath = `test-results/import-failure-${testInfo.title.replace(/\s+/g, '-')}-${Date.now()}.png`;
      await page.screenshot({ 
        path: screenshotPath,
        fullPage: true 
      });
      console.log(`📸 Failure screenshot saved: ${screenshotPath}`);
    }
  });
});

// Global setup
test.beforeAll(async () => {
  console.log('🚀 E2E AI Import Tests Starting');
  console.log('===============================');
  console.log(`Base URL: ${testConfig.e2e.baseUrl}`);
  console.log(`Test User: ${TEST_USER.email}`);
  console.log('Testing AI import with ChatGPT, Claude, and Gemini content');
  console.log('');
});