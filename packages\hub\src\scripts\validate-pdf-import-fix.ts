#!/usr/bin/env tsx

/**
 * End-to-End PDF Import Fix Validation
 * Tests the complete fix for the PDF import issue
 * 
 * Usage: npx tsx src/scripts/validate-pdf-import-fix.ts
 */

import { getSupabaseClient } from '../lib/supabase';
import { getAIParserService } from '../services/ai-parser.service';
import { logger } from '../utils/logger';

async function validateOriginalSessionFix() {
  console.log('🔍 Validating Original Session Fix');
  console.log('-'.repeat(50));

  const targetSessionId = '2479b9e7-4336-4b9c-9447-78f747ae26be';

  try {
    // Check the session status directly from database
    const { data, error } = await getSupabaseClient()
      .from('ai_import_logs')
      .select('id, import_status, error_message, created_at, updated_at')
      .eq('id', targetSessionId)
      .single();

    if (error) {
      console.log('❌ Failed to query session:', error.message);
      return false;
    }

    console.log('✅ Original Session Status:');
    console.log(`   ID: ${data.id}`);
    console.log(`   Status: ${data.import_status}`);
    console.log(`   Error: ${data.error_message || 'None'}`);
    console.log(`   Created: ${data.created_at}`);
    console.log(`   Updated: ${data.updated_at}`);

    // Verify the session is properly marked as failed
    if (data.import_status === 'failed' && data.error_message?.includes('timeout')) {
      console.log('✅ Original orphaned session properly recovered');
      
      // Verify timestamp consistency
      const createdTime = new Date(data.created_at).getTime();
      const updatedTime = new Date(data.updated_at).getTime();
      
      if (updatedTime >= createdTime) {
        console.log('✅ Timestamp consistency verified');
        return true;
      } else {
        console.log('❌ Timestamp issue still exists');
        return false;
      }
    } else {
      console.log('❌ Session not properly recovered');
      return false;
    }

  } catch (error) {
    console.error('❌ Validation failed:', error);
    return false;
  }
}

async function testNewSessionCreationFlow() {
  console.log('\n🧪 Testing New Session Creation Flow');
  console.log('-'.repeat(50));

  try {
    // Test content similar to the original failing PDF
    const testContent = `
    Complete 15-Day European Adventure: London, Madrid, Lisbon & Porto

    LONDON: July 23-25 (2 Nights)
    Day 1 - Thursday, July 23: Arrival & Sky Garden Sunset
    - Arrive at Heathrow Airport
    - Check into Hyatt Regency London Blackfriars
    - Visit Sky Garden for sunset views (5:30-7:30pm)
    - Dinner at Dishoom for Indian cuisine

    Day 2 - Friday, July 24: Markets & Culture
    - Morning: Borough Market (9:45am)
    - Afternoon: South Bank Culture Walk
    - Visit Tate Modern and Globe Theatre
    - Evening: Covent Garden dining

    MADRID: July 25-29 (4 Nights)
    Day 3 - Saturday, July 25: Arrival
    - Travel from London to Madrid
    - Check into hotel
    - Explore city center

    Day 4-6: Madrid exploration
    - Prado Museum
    - Retiro Park
    - Flamenco show
    - Tapas tours

    LISBON: July 29-Aug 1 (3 Nights)
    Day 7-9: Lisbon exploration
    - Tram 28 tour
    - Belém Tower
    - Pastéis de nata tasting
    - Fado music evening

    PORTO: Aug 1-6 (5 Nights)
    Day 10-14: Porto exploration
    - Port wine cellars
    - Livraria Lello bookstore
    - Douro River cruise
    - Francesinha sandwich

    Budget: €3000 total
    `;

    const testUserId = '697b40b3-42d7-4b32-ad49-0220c2313643';
    const testSource = 'gemini';

    console.log('Creating test session with fixed implementation...');
    
    const aiParserService = getAIParserService();
    const sessionId = await aiParserService.createParseSession(testContent, testSource, testUserId);
    
    console.log(`✅ Test session created: ${sessionId}`);

    // Wait for initial processing
    await new Promise(resolve => setTimeout(resolve, 3000));

    // Check session state multiple times to verify proper status transitions
    for (let i = 0; i < 5; i++) {
      const session = await aiParserService.getSession(sessionId);
      
      if (!session) {
        console.log('❌ Session not found');
        return false;
      }

      console.log(`   Check ${i + 1}: Status=${session.status}, Progress=${session.progress}%`);

      if (session.status === 'complete') {
        console.log('✅ Session completed successfully');
        break;
      } else if (session.status === 'error') {
        console.log(`✅ Session failed gracefully: ${session.error}`);
        break;
      }

      // Wait before next check
      await new Promise(resolve => setTimeout(resolve, 5000));
    }

    // Verify database consistency
    const { data, error } = await getSupabaseClient()
      .from('ai_import_logs')
      .select('id, import_status, created_at, updated_at, error_message')
      .eq('id', sessionId)
      .single();

    if (error) {
      console.log('❌ Failed to verify database state:', error.message);
      return false;
    }

    console.log('✅ Database State Verification:');
    console.log(`   Status: ${data.import_status}`);
    console.log(`   Created: ${data.created_at}`);
    console.log(`   Updated: ${data.updated_at}`);

    // Verify timestamp consistency
    const createdTime = new Date(data.created_at).getTime();
    const updatedTime = new Date(data.updated_at).getTime();

    if (updatedTime >= createdTime) {
      console.log('✅ Timestamp consistency maintained');
    } else {
      console.log('❌ Timestamp inconsistency detected');
      return false;
    }

    // Clean up test session
    await getSupabaseClient()
      .from('ai_import_logs')
      .delete()
      .eq('id', sessionId);

    console.log('✅ Test session cleaned up');
    return true;

  } catch (error) {
    console.error('❌ New session test failed:', error);
    return false;
  }
}

async function testOrphanedSessionDetection() {
  console.log('\n🧪 Testing Orphaned Session Detection');
  console.log('-'.repeat(50));

  try {
    // Create a test session that simulates an old processing session
    const testSessionId = `orphan-test-${Date.now()}`;
    const testUserId = '697b40b3-42d7-4b32-ad49-0220c2313643';
    
    // Create session with old timestamp (6 minutes ago)
    const oldTimestamp = new Date(Date.now() - 6 * 60 * 1000).toISOString();
    
    await getSupabaseClient()
      .from('ai_import_logs')
      .insert({
        id: testSessionId,
        user_id: testUserId,
        ai_platform: 'test',
        import_status: 'processing',
        raw_conversation: 'Test orphaned session detection',
        created_at: oldTimestamp,
        updated_at: oldTimestamp
      });

    console.log(`✅ Created test orphaned session: ${testSessionId}`);

    // Test the getSession method which should detect and fix orphaned sessions
    const aiParserService = getAIParserService();
    const session = await aiParserService.getSession(testSessionId);

    if (!session) {
      console.log('❌ Session not found');
      return false;
    }

    console.log('✅ Orphaned Session Detection Results:');
    console.log(`   Status: ${session.status}`);
    console.log(`   Error: ${session.error || 'None'}`);

    // Verify the session was marked as failed due to timeout
    if (session.status === 'error' && session.error?.includes('timeout')) {
      console.log('✅ Orphaned session properly detected and marked as failed');
      
      // Verify database was updated
      const { data } = await getSupabaseClient()
        .from('ai_import_logs')
        .select('import_status, error_message')
        .eq('id', testSessionId)
        .single();

      if (data?.import_status === 'failed' && data.error_message?.includes('timeout')) {
        console.log('✅ Database properly updated for orphaned session');
      } else {
        console.log('❌ Database not properly updated');
        return false;
      }
    } else {
      console.log('❌ Orphaned session not properly handled');
      return false;
    }

    // Clean up
    await getSupabaseClient()
      .from('ai_import_logs')
      .delete()
      .eq('id', testSessionId);

    console.log('✅ Test orphaned session cleaned up');
    return true;

  } catch (error) {
    console.error('❌ Orphaned session detection test failed:', error);
    return false;
  }
}

async function main() {
  console.log('🔧 PDF Import Fix Validation - End-to-End Tests');
  console.log('='.repeat(70));

  const results = {
    originalSessionFix: false,
    newSessionFlow: false,
    orphanedDetection: false
  };

  // Test 1: Validate original session fix
  results.originalSessionFix = await validateOriginalSessionFix();

  // Test 2: Test new session creation flow
  results.newSessionFlow = await testNewSessionCreationFlow();

  // Test 3: Test orphaned session detection
  results.orphanedDetection = await testOrphanedSessionDetection();

  // Summary
  console.log('\n📊 Validation Results Summary');
  console.log('='.repeat(70));
  console.log(`Original Session Fix: ${results.originalSessionFix ? '✅ PASS' : '❌ FAIL'}`);
  console.log(`New Session Flow: ${results.newSessionFlow ? '✅ PASS' : '❌ FAIL'}`);
  console.log(`Orphaned Detection: ${results.orphanedDetection ? '✅ PASS' : '❌ FAIL'}`);

  const passedTests = Object.values(results).filter(Boolean).length;
  const totalTests = Object.keys(results).length;

  console.log(`\nOverall: ${passedTests}/${totalTests} tests passed`);

  if (passedTests === totalTests) {
    console.log('\n🎉 ALL TESTS PASSED! PDF import fix is working correctly.');
    console.log('\n✅ Root Cause Fixed:');
    console.log('   - Timestamp race condition resolved');
    console.log('   - Status update mechanism improved');
    console.log('   - Orphaned session detection implemented');
    console.log('   - Error handling enhanced');
    console.log('\n✅ Frontend Impact:');
    console.log('   - No more infinite polling');
    console.log('   - Proper error messages displayed');
    console.log('   - Session timeouts handled gracefully');
    
    return true;
  } else {
    console.log('\n⚠️  Some tests failed. The fix needs additional work.');
    return false;
  }
}

// Run the validation
if (require.main === module) {
  main()
    .then(success => {
      process.exit(success ? 0 : 1);
    })
    .catch(error => {
      console.error('Validation execution failed:', error);
      process.exit(1);
    });
}

export { main as validatePDFImportFix };