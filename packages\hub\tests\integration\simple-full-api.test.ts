import { describe, it, expect, beforeAll, afterAll } from 'vitest';
import request from 'supertest';
import path from 'path';
import dotenv from 'dotenv';

// Load environment variables from .env.local BEFORE importing anything else
dotenv.config({ path: path.resolve(__dirname, '../../../.env.local') });

// Enable info logging in tests
process.env.LOG_LEVEL = 'INFO';

// Verify environment variables are loaded
console.log('ENV CHECK:', {
  GOOGLE_GEMINI_API_KEY: !!process.env.GOOGLE_GEMINI_API_KEY,
  OPENROUTER_API_KEY: !!process.env.OPENROUTER_API_KEY,
  SUPABASE_URL: !!process.env.SUPABASE_URL,
  SUPABASE_ANON_KEY: !!process.env.SUPABASE_ANON_KEY,
});

import { getSupabaseClient } from '../../src/lib/supabase';
import { createServer } from '../../src/server';
import { Express } from 'express';
import { logger } from '../../src/utils/logger';

// Test configuration
const TEST_USER_EMAIL = '<EMAIL>';
const TEST_USER_PASSWORD = 'Flaremmk123!';

describe('Simplified Full API Tests', () => {
  let app: Express;
  let server: any;
  let authToken: string;
  let createdTripId: string;

  // Helper function to wait for parse completion
  async function waitForParseCompletion(
    app: Express,
    authToken: string,
    importId: string,
    maxAttempts: number = 15,
    delayMs: number = 2000
  ): Promise<{ complete: boolean; error?: string }> {
    for (let i = 0; i < maxAttempts; i++) {
      await new Promise(resolve => setTimeout(resolve, delayMs));
      
      const statusCheck = await request(app)
        .get(`/api/v1/import/parse-simple/${importId}`)
        .set('Authorization', `Bearer ${authToken}`);
        
      logger.info(`Parse status check ${i + 1}:`, {
        status: statusCheck.body.data?.status,
        progress: statusCheck.body.data?.progress,
        error: statusCheck.body.data?.error
      });
      
      if (statusCheck.body.data?.status === 'complete') {
        return { complete: true };
      }
      
      if (statusCheck.body.data?.status === 'error') {
        return { complete: false, error: statusCheck.body.data.error };
      }
    }
    
    return { complete: false, error: 'Parsing timed out' };
  }

  beforeAll(async () => {
    console.log('🚀 Starting simplified API tests...');
    app = createServer();
    server = app.listen(0);

    // Get auth token directly from Supabase
    const supabase = getSupabaseClient();
    const { data, error } = await supabase.auth.signInWithPassword({
      email: TEST_USER_EMAIL,
      password: TEST_USER_PASSWORD
    });

    if (error) {
      console.error('Auth error:', error);
      throw new Error(`Failed to authenticate: ${error.message}`);
    }

    authToken = data.session?.access_token || '';
    console.log('✅ Got auth token');
  });

  afterAll(async () => {
    server?.close();
  });

  describe('AI Import Tests', () => {
    it('should import a conversation and check status', async () => {
      const conversation = `User: Plan a 3-day trip to Rome.
Assistant: Here's your Rome itinerary:

Day 1:
- Morning: Colosseum and Roman Forum
- Afternoon: Pantheon and Trevi Fountain
- Evening: Dinner in Trastevere

Day 2:
- Morning: Vatican Museums and Sistine Chapel
- Afternoon: St. Peter's Basilica
- Evening: Spanish Steps and Villa Borghese

Day 3:
- Morning: Galleria Borghese
- Afternoon: Campo de' Fiori market
- Evening: Sunset at Gianicolo Hill`;

      // Start import
      const importResponse = await request(app)
        .post('/api/v1/import/parse-simple')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          content: conversation,
          source: 'chatgpt'
        });

      expect(importResponse.status).toBe(200);
      expect(importResponse.body.success).toBe(true);
      expect(importResponse.body.data).toHaveProperty('importId');

      const importId = importResponse.body.data.importId;

      // Wait for parse completion using helper (allow more time for first test)
      const parseResult = await waitForParseCompletion(app, authToken, importId, 15, 3000);
      
      expect(parseResult.complete).toBe(true);
      expect(parseResult.error).toBeUndefined();

      // Verify status endpoint works
      const statusResponse = await request(app)
        .get(`/api/v1/import/parse-simple/${importId}`)
        .set('Authorization', `Bearer ${authToken}`);

      expect(statusResponse.status).toBe(200);
      expect(statusResponse.body.success).toBe(true);
      expect(statusResponse.body.data).toHaveProperty('status', 'complete');
      expect(statusResponse.body.data).toHaveProperty('importId', importId);
    });

    it('should require authentication for import', async () => {
      const response = await request(app)
        .post('/api/v1/import/parse-simple')
        .send({
          content: 'Some content',
          source: 'chatgpt'
        });

      expect(response.status).toBe(401);
    });

    it('should validate content length', async () => {
      const response = await request(app)
        .post('/api/v1/import/parse-simple')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          content: 'Too short',
          source: 'chatgpt'
        });

      expect(response.status).toBe(400);
      expect(response.body.error).toContain('too short');
    });
  });

  describe('Trip Management Tests', () => {
    it('should create trip from import', async () => {
      // First create an import with unique content
      const timestamp = Date.now();
      const conversation = `User: Weekend trip to Paris (Test ${timestamp})
Assistant: Here's your Paris weekend:

Day 1:
- Morning: Eiffel Tower
- Afternoon: Louvre Museum
- Evening: Seine cruise

Day 2:
- Morning: Versailles
- Afternoon: Montmartre
- Evening: Latin Quarter`;

      const importResponse = await request(app)
        .post('/api/v1/import/parse-simple')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          content: conversation,
          source: 'chatgpt'
        });

      expect(importResponse.status).toBe(200);
      const importId = importResponse.body.data.importId;

      // Wait for parse completion using helper
      const parseResult = await waitForParseCompletion(app, authToken, importId);
      
      if (!parseResult.complete) {
        logger.error('Parse failed:', parseResult.error);
      }
      
      // Check parse session directly from database
      const supabase = getSupabaseClient();
      const { data: dbSession } = await supabase
        .from('ai_import_logs')
        .select('*')
        .eq('id', importId)
        .single();
        
      console.log('DB parse session:', {
        id: dbSession?.id,
        status: dbSession?.import_status,
        hasData: !!dbSession?.parsed_data,
        error: dbSession?.error_message
      });

      // Create trip from import
      const tripResponse = await request(app)
        .post(`/api/v1/import/parse-simple/${importId}/create-trip`)
        .set('Authorization', `Bearer ${authToken}`)
        .send({});

      expect(tripResponse.status).toBe(200);
      expect(tripResponse.body.success).toBe(true);
      expect(tripResponse.body.data).toHaveProperty('tripId');

      createdTripId = tripResponse.body.data.tripId;
    });

    it('should list user trips', async () => {
      const response = await request(app)
        .get('/api/v1/trips')
        .set('Authorization', `Bearer ${authToken}`);

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveProperty('trips');
      expect(Array.isArray(response.body.data.trips)).toBe(true);
    });

    it('should get trip details', async () => {
      if (!createdTripId) {
        console.log('No trip ID available, skipping...');
        return;
      }

      const response = await request(app)
        .get(`/api/v1/trips/${createdTripId}`)
        .set('Authorization', `Bearer ${authToken}`);

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveProperty('id', createdTripId);
    });

    it('should require authentication for trip endpoints', async () => {
      const response = await request(app)
        .get('/api/v1/trips');

      expect(response.status).toBe(401);
    });
  });

  describe('Error Handling', () => {
    it('should handle non-existent imports', async () => {
      const response = await request(app)
        .get('/api/v1/import/parse-simple/non-existent-id')
        .set('Authorization', `Bearer ${authToken}`);

      expect(response.status).toBe(404);
      expect(response.body.success).toBe(false);
    });

    it('should handle malformed requests', async () => {
      const response = await request(app)
        .post('/api/v1/import/parse-simple')
        .set('Authorization', `Bearer ${authToken}`)
        .set('Content-Type', 'application/json')
        .send('{ invalid json');

      expect(response.status).toBe(400);
    });

    it('should validate source parameter', async () => {
      const response = await request(app)
        .post('/api/v1/import/parse-simple')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          content: 'Valid content that is long enough to pass validation checks',
          source: 'invalid-source'
        });

      // The API accepts any source, so this should succeed
      expect([200, 400]).toContain(response.status);
    });
  });
});