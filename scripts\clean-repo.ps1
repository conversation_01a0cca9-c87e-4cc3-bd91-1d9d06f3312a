# TravelViz Repository Cleanup Script (PowerShell)
# Removes files that shouldn't be committed to GitHub

Write-Host "🧹 Cleaning TravelViz repository..." -ForegroundColor Green

# Remove environment files from Git tracking (but keep them locally)
Write-Host "📝 Removing environment files from Git tracking..." -ForegroundColor Yellow
try { git rm --cached packages/hub/.env 2>$null } catch { Write-Host "packages/hub/.env not tracked" }
try { git rm --cached packages/web/.env.local 2>$null } catch { Write-Host "packages/web/.env.local not tracked" }
try { git rm --cached .env 2>$null } catch { Write-Host ".env not tracked" }

# Remove build outputs from Git tracking
Write-Host "🏗️ Removing build outputs from Git tracking..." -ForegroundColor Yellow
try { git rm -r --cached packages/*/dist/ 2>$null } catch { Write-Host "No dist directories tracked" }
try { git rm -r --cached packages/web/.next/ 2>$null } catch { Write-Host ".next not tracked" }
try { git rm -r --cached packages/*/build/ 2>$null } catch { Write-Host "No build directories tracked" }

# Remove TypeScript build info
Write-Host "📘 Removing TypeScript build info..." -ForegroundColor Yellow
try { git rm --cached packages/*/*.tsbuildinfo 2>$null } catch { Write-Host "No .tsbuildinfo files tracked" }
try { git rm --cached *.tsbuildinfo 2>$null } catch { Write-Host "No root .tsbuildinfo files tracked" }

# Remove node_modules if accidentally tracked
Write-Host "📦 Removing node_modules from Git tracking..." -ForegroundColor Yellow
try { git rm -r --cached node_modules/ 2>$null } catch { Write-Host "node_modules not tracked" }
try { git rm -r --cached packages/*/node_modules/ 2>$null } catch { Write-Host "No package node_modules tracked" }

# Remove log files
Write-Host "📋 Removing log files..." -ForegroundColor Yellow
try { git rm --cached *.log 2>$null } catch { Write-Host "No log files tracked" }
try { git rm --cached packages/*/*.log 2>$null } catch { Write-Host "No package log files tracked" }

# Check for any remaining sensitive files
Write-Host "🔍 Checking for sensitive files..." -ForegroundColor Yellow
$sensitiveFiles = git ls-files | Select-String -Pattern "\.(env|log)$"
if ($sensitiveFiles) {
    Write-Host "⚠️  Warning: Some environment or log files are still tracked:" -ForegroundColor Red
    $sensitiveFiles
    Write-Host "Please review and remove manually if needed." -ForegroundColor Red
} else {
    Write-Host "✅ No sensitive files found in tracking" -ForegroundColor Green
}

# Show current status
Write-Host "📊 Current repository status:" -ForegroundColor Cyan
git status --porcelain

Write-Host "✨ Repository cleanup complete!" -ForegroundColor Green
Write-Host ""
Write-Host "Next steps:" -ForegroundColor Cyan
Write-Host "1. Review the changes: git status"
Write-Host "2. Commit the cleanup: git commit -m 'chore: remove sensitive files from tracking'"
Write-Host "3. Make sure .gitignore is working: git status --ignored"
