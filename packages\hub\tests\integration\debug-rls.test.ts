import { describe, it, expect, beforeAll } from 'vitest';
import { getSupabaseClient } from '../../src/lib/supabase';

describe('Debug RLS Issues', () => {
  let authToken: string;
  let userId: string;

  beforeAll(async () => {
    const supabase = getSupabaseClient();
    
    // Authenticate as test user
    const { data: authData, error: authError } = await supabase.auth.signInWithPassword({
      email: '<EMAIL>',
      password: 'Flaremmk123!'
    });

    if (authError) throw authError;
    
    authToken = authData.session?.access_token || '';
    userId = authData.user?.id || '';
    console.log('Authenticated as user:', userId);
  });

  it('should test direct database count query', async () => {
    const supabase = getSupabaseClient();
    
    // Test count query directly
    console.log('Testing count query...');
    const { count, error } = await supabase
      .from('trips')
      .select('*', { count: 'exact', head: true })
      .eq('user_id', userId);
      
    console.log('Count result:', { count, error });
    
    if (error) {
      console.error('Count error details:', {
        message: error.message,
        code: error.code,
        details: error.details,
        hint: error.hint,
        errorKeys: Object.keys(error)
      });
    }
    
    expect(error).toBeNull();
    expect(count).toBeGreaterThanOrEqual(0);
  });

  it('should test trips with activities query', async () => {
    const supabase = getSupabaseClient();
    
    // Test full query with activities
    console.log('Testing trips with activities query...');
    const { data, error } = await supabase
      .from('trips')
      .select(`
        *,
        activities (*)
      `)
      .eq('user_id', userId)
      .limit(1);
      
    console.log('Query result:', { 
      hasData: !!data, 
      dataLength: data?.length,
      error 
    });
    
    if (error) {
      console.error('Query error details:', {
        message: error.message,
        code: error.code,
        details: error.details,
        hint: error.hint,
        errorKeys: Object.keys(error)
      });
    }
    
    expect(error).toBeNull();
    expect(data).toBeDefined();
  });

  it('should test RLS function directly', async () => {
    const supabase = getSupabaseClient();
    
    // Create a test trip first
    const { data: trip, error: createError } = await supabase
      .from('trips')
      .insert({
        user_id: userId,
        title: 'Test Trip for RLS',
        destination: 'Test City',
        start_date: '2025-07-20',
        end_date: '2025-07-25',
        status: 'draft',
        visibility: 'private'
      })
      .select()
      .single();
      
    if (createError) {
      console.error('Create trip error:', createError);
      throw createError;
    }
    
    console.log('Created test trip:', trip.id);
    
    // Test the ownership function
    const { data: ownershipCheck, error: funcError } = await supabase
      .rpc('user_owns_trip', { trip_id: trip.id });
      
    console.log('Ownership check result:', { ownershipCheck, funcError });
    
    // Clean up
    await supabase.from('trips').delete().eq('id', trip.id);
    
    expect(funcError).toBeNull();
    expect(ownershipCheck).toBe(true);
  });
});