import { logger } from '../utils/logger';
import { usageTrackingService } from './usage-tracking.service';

/**
 * Intelligent Model Selector Service
 * Selects optimal model based on usage, content complexity, and cost
 * Requirements: 2.1, 2.2, 3.1, 3.2, 3.3 from requirements.md
 */

export interface ModelRequirements {
  preferSpeed?: boolean;
  preferQuality?: boolean;
  maxCost?: number;
  minTokenCapacity?: number;
}

export interface ModelSelection {
  modelId: string;
  provider: 'moonshot' | 'google' | 'openrouter';
  reason: string;
  estimatedCost: number;
  fallbackChain: string[];
}

export interface TokenEstimate {
  inputTokens: number;
  outputTokens: number;
  complexity: 'simple' | 'medium' | 'complex' | 'very_complex';
}

export type ContentComplexity = 'simple' | 'medium' | 'complex' | 'very_complex';

export class ModelSelectorService {
  // Model priority chain per design.md: Moonshot → Gemini → OpenRouter
  private readonly MODEL_PRIORITY = [
    'moonshotai/kimi-k2:free',
    'google/gemini-2.5-flash',
    'google/gemini-2.0-flash', 
    'google/gemini-2.5-pro',
    'openai/gpt-4.1-nano'
  ];

  // Model provider mapping
  private readonly PROVIDER_MAP: Record<string, 'moonshot' | 'google' | 'openrouter'> = {
    'moonshotai/kimi-k2:free': 'moonshot',
    'google/gemini-2.5-pro': 'google',
    'google/gemini-2.5-flash': 'google', 
    'google/gemini-2.0-flash': 'google',
    'openai/gpt-4.1-nano': 'openrouter'
  };

  /**
   * Select best available model - Requirements 2.1, 2.2: Prioritize free tier models
   */
  async selectModel(content: string, requirements?: ModelRequirements): Promise<ModelSelection> {
    try {
      const tokenEstimate = this.estimateTokens(content);
      
      logger.info('Starting model selection', {
        contentLength: content.length,
        complexity: tokenEstimate.complexity,
        estimatedTokens: tokenEstimate.inputTokens + tokenEstimate.outputTokens
      });

      // Try database-dependent model selection first
      try {
        // Requirements 2.1: First attempt moonshotai/kimi-k2:free if under 1000 daily requests
        const moonshotAvailable = await usageTrackingService.isModelAvailable('moonshotai/kimi-k2:free');
        if (moonshotAvailable) {
          const moonshotUsage = await usageTrackingService.getCurrentUsage('moonshotai/kimi-k2:free');
          if (moonshotUsage.requestCount < 1000) {
            return {
              modelId: 'moonshotai/kimi-k2:free',
              provider: 'moonshot',
              reason: 'Primary free tier model - under daily limit',
              estimatedCost: 0,
              fallbackChain: this.getFallbackChain('moonshotai/kimi-k2:free', tokenEstimate.complexity)
            };
          }
        }

        // Requirements 2.2: Use appropriate Google Gemini models based on RPM/TPM/RPD limits
        const geminiModel = await this.selectGeminiModel(tokenEstimate);
        if (geminiModel) {
          return geminiModel;
        }

        // Requirements 2.3: Fall back to openai/gpt-4.1-nano if all free options exhausted
        const openaiAvailable = await usageTrackingService.isModelAvailable('openai/gpt-4.1-nano');
        if (openaiAvailable) {
          return {
            modelId: 'openai/gpt-4.1-nano',
            provider: 'openrouter',
            reason: 'Fallback to paid model - all free options exhausted',
            estimatedCost: this.calculateEstimatedCost('openai/gpt-4.1-nano', tokenEstimate),
            fallbackChain: []
          };
        }

        // No models available
        throw new Error('No AI models are currently available');

      } catch (dbError) {
        logger.warn('Database-dependent model selection failed, using fallback logic', {
          error: dbError instanceof Error ? dbError.message : String(dbError)
        });

        // Fallback to simple model selection without database dependency
        return this.selectModelWithoutDatabase(tokenEstimate);
      }

    } catch (error) {
      logger.error('Model selection failed', { error });
      throw new Error('Failed to select AI model');
    }
  }

  /**
   * Select model without database dependency (fallback mode)
   */
  private selectModelWithoutDatabase(tokenEstimate: TokenEstimate): ModelSelection {
    logger.info('Using fallback model selection without database dependency');

    // Default to Moonshot AI as primary free tier model
    return {
      modelId: 'moonshotai/kimi-k2:free',
      provider: 'moonshot',
      reason: 'Fallback mode - database unavailable, using default free tier model',
      estimatedCost: 0,
      fallbackChain: this.getFallbackChain('moonshotai/kimi-k2:free', tokenEstimate.complexity)
    };
  }

  /**
   * Get fallback models for a given primary model
   */
  getFallbackChain(primaryModel: string, complexity: ContentComplexity): string[] {
    const primaryIndex = this.MODEL_PRIORITY.indexOf(primaryModel);
    
    if (primaryIndex === -1) {
      return this.MODEL_PRIORITY.slice(1); // Skip first if not found
    }

    // Return remaining models in priority order
    const fallbacks = this.MODEL_PRIORITY.slice(primaryIndex + 1);
    
    // Adjust based on complexity
    switch (complexity) {
      case 'very_complex':
        // For very complex content, prioritize high-capacity models
        return fallbacks.filter(model => 
          model.includes('gemini-2.0-flash') || 
          model.includes('kimi-k2') ||
          model.includes('gpt-4')
        );
      
      case 'complex':
        // For complex content, balance capacity and speed
        return fallbacks;
      
      default:
        // For simple/medium, prioritize speed
        return fallbacks;
    }
  }

  /**
   * Estimate token requirements - Requirements 3.1, 3.2: Estimate tokens before model selection
   */
  estimateTokens(content: string): TokenEstimate {
    const length = content.length;
    
    // Rough token estimation: ~4 characters per token for English text
    const inputTokens = Math.ceil(length / 4);
    
    // Estimate output tokens based on content complexity
    let outputTokens: number;
    let complexity: ContentComplexity;
    
    // Analyze content structure for complexity
    const dayMatches = content.match(/day\s*\d+/gi) || [];
    const timeMatches = content.match(/\d+:\d+|morning|afternoon|evening|\d+\s*(am|pm)/gi) || [];
    const locationMatches = content.match(/hotel|restaurant|museum|tower|airport|station|city|country/gi) || [];
    
    const dayCount = dayMatches.length;
    const timeCount = timeMatches.length;
    const locationCount = locationMatches.length;
    
    if (length < 2000 && dayCount <= 3 && timeCount <= 10) {
      complexity = 'simple';
      outputTokens = Math.min(1000, inputTokens * 0.5);
    } else if (length < 5000 && dayCount <= 7 && timeCount <= 25) {
      complexity = 'medium';
      outputTokens = Math.min(2000, inputTokens * 0.7);
    } else if (length < 15000 && dayCount <= 15 && timeCount <= 60) {
      complexity = 'complex';
      outputTokens = Math.min(4000, inputTokens * 1.0);
    } else {
      complexity = 'very_complex';
      outputTokens = Math.min(8000, inputTokens * 1.2);
    }
    
    logger.debug('Token estimation completed', {
      contentLength: length,
      inputTokens,
      outputTokens,
      complexity,
      indicators: { dayCount, timeCount, locationCount }
    });
    
    return {
      inputTokens,
      outputTokens,
      complexity
    };
  }

  /**
   * Select appropriate Gemini model based on RPM/TPM/RPD limits
   */
  private async selectGeminiModel(tokenEstimate: TokenEstimate): Promise<ModelSelection | null> {
    const geminiModels = [
      'google/gemini-2.0-flash',  // Highest capacity: RPM: 15, TPM: 1M, RPD: 200
      'google/gemini-2.5-flash',  // Balanced: RPM: 10, TPM: 250k, RPD: 250  
      'google/gemini-2.5-pro'     // Lowest capacity but highest quality: RPM: 5, TPM: 250k, RPD: 100
    ];

    for (const modelId of geminiModels) {
      const available = await usageTrackingService.isModelAvailable(modelId);
      if (available) {
        const usage = await usageTrackingService.getCurrentUsage(modelId);
        
        // Check if model can handle the estimated tokens
        const totalTokens = tokenEstimate.inputTokens + tokenEstimate.outputTokens;
        const modelLimits = this.getModelTokenLimits(modelId);
        
        if (totalTokens <= modelLimits.maxTokens) {
          return {
            modelId,
            provider: 'google',
            reason: `Gemini model selected - within limits (${usage.requestCount} requests used)`,
            estimatedCost: 0, // Free via Google API
            fallbackChain: this.getFallbackChain(modelId, tokenEstimate.complexity)
          };
        }
      }
    }

    return null;
  }

  /**
   * Get model token limits
   */
  private getModelTokenLimits(modelId: string): { maxTokens: number } {
    const limits: Record<string, { maxTokens: number }> = {
      'moonshotai/kimi-k2:free': { maxTokens: 66000 },
      'google/gemini-2.5-pro': { maxTokens: 8192 },
      'google/gemini-2.5-flash': { maxTokens: 8192 },
      'google/gemini-2.0-flash': { maxTokens: 8192 },
      'openai/gpt-4.1-nano': { maxTokens: 4000 }
    };

    return limits[modelId] || { maxTokens: 4000 };
  }

  /**
   * Calculate estimated cost for a model
   */
  private calculateEstimatedCost(modelId: string, tokenEstimate: TokenEstimate): number {
    const costPer1k: Record<string, number> = {
      'moonshotai/kimi-k2:free': 0,
      'google/gemini-2.5-pro': 0,
      'google/gemini-2.5-flash': 0,
      'google/gemini-2.0-flash': 0,
      'openai/gpt-4.1-nano': 0.00015
    };

    const cost = costPer1k[modelId] || 0;
    const totalTokens = tokenEstimate.inputTokens + tokenEstimate.outputTokens;
    
    return (totalTokens / 1000) * cost;
  }
}

// Export singleton instance
export const modelSelectorService = new ModelSelectorService();