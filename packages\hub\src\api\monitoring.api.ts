import { Router, Request } from 'express';
import { MonitoringService } from '../services/monitoring.service';
import { createSuccessResponse, createErrorResponse } from '@travelviz/shared';
import { authenticateSupabaseUser, SupabaseAuthenticatedRequest } from '../middleware/supabase-auth.middleware';
import { logger } from '../utils/logger';

// Use SupabaseAuthenticatedRequest from middleware

const router: Router = Router();
const monitoringService = MonitoringService.getInstance();

/**
 * Get usage metrics for all AI services
 */
router.get('/usage', authenticateSupabaseUser, async (req, res) => {
  try {
    const metrics = await monitoringService.getUsageMetrics();
    return res.json(createSuccessResponse(metrics));
  } catch (error) {
    logger.error('Failed to get usage metrics', { error });
    return res.status(500).json(
      createErrorResponse('Failed to get usage metrics')
    );
  }
});

/**
 * Get performance metrics
 */
router.get('/performance', authenticateSupabaseUser, async (req, res) => {
  try {
    const metrics = await monitoringService.getPerformanceMetrics();
    return res.json(createSuccessResponse(metrics));
  } catch (error) {
    logger.error('Failed to get performance metrics', { error });
    return res.status(500).json(
      createErrorResponse('Failed to get performance metrics')
    );
  }
});

/**
 * Check for usage alerts
 */
router.get('/alerts', authenticateSupabaseUser, async (req, res) => {
  try {
    const alerts = await monitoringService.checkUsageAlerts();
    return res.json(createSuccessResponse(alerts));
  } catch (error) {
    logger.error('Failed to check usage alerts', { error });
    return res.status(500).json(
      createErrorResponse('Failed to check usage alerts')
    );
  }
});

/**
 * Reset daily counters (admin only)
 */
router.post('/reset-counters', authenticateSupabaseUser, async (req, res) => {
  try {
    // Check if user is admin (you might want to add proper admin check)
    const userEmail = (req as SupabaseAuthenticatedRequest).user?.email;
    if (!userEmail || !userEmail.includes('@mmkdev.com')) {
      return res.status(403).json(
        createErrorResponse('Admin access required')
      );
    }
    
    await monitoringService.resetDailyCounters();
    return res.json(createSuccessResponse({ message: 'Counters reset successfully' }));
  } catch (error) {
    logger.error('Failed to reset counters', { error });
    return res.status(500).json(
      createErrorResponse('Failed to reset counters')
    );
  }
});

export { router as monitoringRouter };