'use client';

import React, { useRef, useEffect, useState, useCallback, useMemo } from 'react';
import mapboxgl from 'mapbox-gl';
import 'mapbox-gl/dist/mapbox-gl.css';
import { Activity, ActivityType } from '@travelviz/shared';
import { cn } from '@/lib/utils';

// Make sure to set your Mapbox token
if (!process.env.NEXT_PUBLIC_MAPBOX_TOKEN) {
  console.warn('Mapbox token not found. Map will not load properly.');
} else {
  mapboxgl.accessToken = process.env.NEXT_PUBLIC_MAPBOX_TOKEN;
}

interface PerformantMapProps {
  activities: Activity[];
  selectedActivity?: Activity | null;
  onActivityClick?: (activity: Activity) => void;
  className?: string;
  showRoute?: boolean;
  clusterMarkers?: boolean;
}

const activityColors: Record<ActivityType, string> = {
  [ActivityType.flight]: '#3B82F6', // blue
  [ActivityType.accommodation]: '#8B5CF6', // purple
  [ActivityType.activity]: '#10B981', // green
  [ActivityType.transport]: '#F97316', // orange
  [ActivityType.dining]: '#EC4899', // pink
  [ActivityType.shopping]: '#6366F1', // indigo
  [ActivityType.car_rental]: '#F97316', // orange
  [ActivityType.tour]: '#10B981', // green
  [ActivityType.sightseeing]: '#10B981', // green
  [ActivityType.entertainment]: '#6366F1', // indigo
  [ActivityType.other]: '#6B7280', // gray
};

const activityIcons: Record<ActivityType, string> = {
  [ActivityType.flight]: '✈️',
  [ActivityType.accommodation]: '🏨',
  [ActivityType.activity]: '📍',
  [ActivityType.transport]: '🚗',
  [ActivityType.dining]: '🍽️',
  [ActivityType.shopping]: '🛍️',
  [ActivityType.car_rental]: '🚗',
  [ActivityType.tour]: '🎫',
  [ActivityType.sightseeing]: '🏛️',
  [ActivityType.entertainment]: '🎭',
  [ActivityType.other]: '📌',
};

export function PerformantMap({
  activities,
  selectedActivity,
  onActivityClick,
  className,
  showRoute = true,
  clusterMarkers = true,
}: PerformantMapProps) {
  const mapContainer = useRef<HTMLDivElement>(null);
  const map = useRef<mapboxgl.Map | null>(null);
  const markers = useRef<Map<string, mapboxgl.Marker>>(new Map());
  const [isLoaded, setIsLoaded] = useState(false);

  // Filter activities with valid coordinates
  const validActivities = useMemo(() => 
    activities.filter(activity => 
      activity.location_lat && 
      activity.location_lng &&
      !isNaN(activity.location_lat) &&
      !isNaN(activity.location_lng)
    ),
    [activities]
  );

  // Create GeoJSON from activities
  const geoJsonData = useMemo(() => ({
    type: 'FeatureCollection' as const,
    features: validActivities.map(activity => ({
      type: 'Feature' as const,
      geometry: {
        type: 'Point' as const,
        coordinates: [activity.location_lng!, activity.location_lat!]
      },
      properties: {
        id: activity.id,
        name: activity.title,
        type: activity.type,
        description: activity.description,
        startTime: activity.start_time,
        price: activity.price,
        currency: activity.currency,
        icon: activityIcons[activity.type],
        color: activityColors[activity.type],
      }
    }))
  }), [validActivities]);

  // Initialize map
  useEffect(() => {
    if (!mapContainer.current || map.current) return;

    try {
      const mapInstance = new mapboxgl.Map({
        container: mapContainer.current,
        style: 'mapbox://styles/mapbox/light-v11',
        center: [0, 0],
        zoom: 2,
        pitch: 0,
        bearing: 0,
        antialias: true,
      });

      map.current = mapInstance;

      mapInstance.on('load', () => {
        setIsLoaded(true);
      });

      // Add navigation controls
      mapInstance.addControl(new mapboxgl.NavigationControl(), 'top-right');

      // Clean up on unmount
      return () => {
        mapInstance.remove();
        map.current = null;
      };
    } catch (error) {
      console.error('Failed to initialize map:', error);
      return;
    }
  }, []);

  // Update markers when activities change
  useEffect(() => {
    if (!map.current || !isLoaded) return;

    // Clear existing markers
    markers.current.forEach(marker => marker.remove());
    markers.current.clear();

    if (validActivities.length === 0) return;

    const bounds = new mapboxgl.LngLatBounds();

    if (clusterMarkers && validActivities.length > 20) {
      // Use clustering for large datasets
      const sourceId = 'activities';
      const clusterId = 'clusters';
      const unclusteredId = 'unclustered-point';

      // Remove existing sources and layers
      if (map.current.getSource(sourceId)) {
        if (map.current.getLayer(clusterId)) map.current.removeLayer(clusterId);
        if (map.current.getLayer(unclusteredId)) map.current.removeLayer(unclusteredId);
        map.current.removeSource(sourceId);
      }

      // Add source
      map.current.addSource(sourceId, {
        type: 'geojson',
        data: geoJsonData,
        cluster: true,
        clusterMaxZoom: 14,
        clusterRadius: 50,
      });

      // Add cluster layer
      map.current.addLayer({
        id: clusterId,
        type: 'circle',
        source: sourceId,
        filter: ['has', 'point_count'],
        paint: {
          'circle-color': [
            'step',
            ['get', 'point_count'],
            '#51bbd6',
            10,
            '#f1f075',
            30,
            '#f28cb1'
          ],
          'circle-radius': [
            'step',
            ['get', 'point_count'],
            20,
            10,
            30,
            30,
            40
          ]
        }
      });

      // Add cluster count layer
      map.current.addLayer({
        id: 'cluster-count',
        type: 'symbol',
        source: sourceId,
        filter: ['has', 'point_count'],
        layout: {
          'text-field': '{point_count_abbreviated}',
          'text-font': ['DIN Offc Pro Medium', 'Arial Unicode MS Bold'],
          'text-size': 12
        }
      });

      // Add unclustered points
      map.current.addLayer({
        id: unclusteredId,
        type: 'circle',
        source: sourceId,
        filter: ['!', ['has', 'point_count']],
        paint: {
          'circle-color': ['get', 'color'],
          'circle-radius': 8,
          'circle-stroke-width': 2,
          'circle-stroke-color': '#fff'
        }
      });

      // Add click handlers
      map.current.on('click', unclusteredId, (e) => {
        if (!e.features || e.features.length === 0) return;
        const feature = e.features[0];
        const activityId = feature.properties?.id;
        const activity = validActivities.find(a => a.id === activityId);
        if (activity && onActivityClick) {
          onActivityClick(activity);
        }
      });

      // Zoom to cluster on click
      map.current.on('click', clusterId, (e) => {
        const features = map.current!.queryRenderedFeatures(e.point, {
          layers: [clusterId]
        });
        const clusterIdValue = features[0].properties?.cluster_id;
        const source = map.current!.getSource(sourceId) as mapboxgl.GeoJSONSource;
        source.getClusterExpansionZoom(clusterIdValue, (err, zoom) => {
          if (err || zoom === null) return;
          map.current!.easeTo({
            center: (features[0].geometry as any).coordinates,
            zoom: zoom
          });
        });
      });

      // Change cursor on hover
      map.current.on('mouseenter', clusterId, () => {
        map.current!.getCanvas().style.cursor = 'pointer';
      });
      map.current.on('mouseleave', clusterId, () => {
        map.current!.getCanvas().style.cursor = '';
      });
      map.current.on('mouseenter', unclusteredId, () => {
        map.current!.getCanvas().style.cursor = 'pointer';
      });
      map.current.on('mouseleave', unclusteredId, () => {
        map.current!.getCanvas().style.cursor = '';
      });

    } else {
      // Use individual markers for smaller datasets
      validActivities.forEach((activity, index) => {
        const el = document.createElement('div');
        el.className = 'custom-marker';
        el.style.width = '32px';
        el.style.height = '32px';
        el.style.borderRadius = '50%';
        el.style.backgroundColor = activityColors[activity.type];
        el.style.border = '3px solid white';
        el.style.boxShadow = '0 2px 4px rgba(0,0,0,0.2)';
        el.style.cursor = 'pointer';
        el.style.display = 'flex';
        el.style.alignItems = 'center';
        el.style.justifyContent = 'center';
        el.style.fontSize = '16px';
        el.innerHTML = activityIcons[activity.type];

        const marker = new mapboxgl.Marker(el)
          .setLngLat([activity.location_lng!, activity.location_lat!])
          .setPopup(
            new mapboxgl.Popup({ offset: 25 })
              .setHTML(`
                <div class="p-2">
                  <h3 class="font-semibold">${activity.title}</h3>
                  ${activity.description ? `<p class="text-sm text-gray-600 mt-1">${activity.description}</p>` : ''}
                  ${activity.price ? `<p class="text-sm font-medium mt-1">$${activity.price} ${activity.currency || 'USD'}</p>` : ''}
                </div>
              `)
          )
          .addTo(map.current!);

        el.addEventListener('click', () => {
          if (onActivityClick) {
            onActivityClick(activity);
          }
        });

        markers.current.set(activity.id, marker);
        bounds.extend([activity.location_lng!, activity.location_lat!]);
      });
    }

    // Fit bounds to show all markers
    validActivities.forEach(activity => {
      bounds.extend([activity.location_lng!, activity.location_lat!]);
    });

    if (!bounds.isEmpty()) {
      map.current.fitBounds(bounds, {
        padding: { top: 50, bottom: 50, left: 50, right: 50 },
        maxZoom: 15,
      });
    }

    // Draw route if enabled
    if (showRoute && validActivities.length > 1) {
      const routeCoordinates = validActivities
        .sort((a, b) => {
          const aTime = a.start_time ? new Date(a.start_time).getTime() : 0;
          const bTime = b.start_time ? new Date(b.start_time).getTime() : 0;
          return aTime - bTime;
        })
        .map(activity => [activity.location_lng!, activity.location_lat!]);

      const routeSourceId = 'route';
      const routeLayerId = 'route-layer';

      // Remove existing route
      if (map.current.getSource(routeSourceId)) {
        if (map.current.getLayer(routeLayerId)) {
          map.current.removeLayer(routeLayerId);
        }
        map.current.removeSource(routeSourceId);
      }

      // Add route
      map.current.addSource(routeSourceId, {
        type: 'geojson',
        data: {
          type: 'Feature',
          properties: {},
          geometry: {
            type: 'LineString',
            coordinates: routeCoordinates,
          },
        },
      });

      map.current.addLayer({
        id: routeLayerId,
        type: 'line',
        source: routeSourceId,
        layout: {
          'line-join': 'round',
          'line-cap': 'round',
        },
        paint: {
          'line-color': '#3B82F6',
          'line-width': 2,
          'line-opacity': 0.6,
          'line-dasharray': [2, 2],
        },
      });
    }
  }, [validActivities, isLoaded, clusterMarkers, showRoute, onActivityClick]);

  // Handle selected activity
  useEffect(() => {
    if (!map.current || !selectedActivity || !isLoaded) return;

    const marker = markers.current.get(selectedActivity.id);
    if (marker) {
      marker.togglePopup();
      map.current.flyTo({
        center: [selectedActivity.location_lng!, selectedActivity.location_lat!],
        zoom: 15,
        duration: 1000,
      });
    }
  }, [selectedActivity, isLoaded]);

  return (
    <div className={cn("relative w-full h-full", className)}>
      <div ref={mapContainer} className="absolute inset-0" />
      
      {validActivities.length === 0 && (
        <div className="absolute inset-0 flex items-center justify-center bg-gray-100">
          <p className="text-gray-500">No activities with location data to display</p>
        </div>
      )}
    </div>
  );
}