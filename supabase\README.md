# Supabase Database Setup

This directory contains SQL scripts to initialize your TravelViz Supabase database.

## Setup Instructions

### 1. Run the Main Initialization Script

Copy the contents of `init.sql` and run it in your Supabase SQL Editor:

1. Go to your Supabase Dashboard: https://app.supabase.com/project/[YOUR_PROJECT_ID]
2. Navigate to SQL Editor
3. Create a new query
4. Paste the contents of `init.sql`
5. Run the query

This will create:

- All required tables (profiles, trips, activities, etc.)
- Indexes for performance
- Row Level Security (RLS) policies
- Triggers and functions
- Custom types

### 2. Run Post-Initialization Configuration

After the main script succeeds, run `post-init.sql` to:

- Configure auth settings
- Create storage buckets
- Add storage policies
- Create helper functions

### 3. Apply Row Level Security Policies

Run `rls-policies.sql` to enable comprehensive RLS policies:

- Enables RLS on all tables (trips, activities, ai_import_logs)
- Creates policies ensuring users can only access their own data
- Adds performance indexes for RLS queries
- Grants necessary permissions to authenticated users

**Important**: This script is idempotent and can be re-run safely.

### 4. Environment Variables

Create `.env.local` files in each package with your actual Supabase credentials:

#### Hub Package (`packages/hub/.env.local`):

```env
SUPABASE_URL=https://[YOUR_PROJECT_ID].supabase.co
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key_here
SUPABASE_JWT_SECRET=your_jwt_secret_here
```

#### Web Package (`packages/web/.env.local`):

```env
NEXT_PUBLIC_SUPABASE_URL=https://[YOUR_PROJECT_ID].supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_anon_key_here
```

**IMPORTANT SECURITY NOTES:**

- NEVER commit `.env.local` files to version control
- Add `.env.local` to your `.gitignore` file
- Use environment variables or secret management systems in production
- Rotate these keys immediately if they were ever exposed
- The service role key has full database access - keep it secure!

### 4. Test the Setup

After running both scripts:

1. Test authentication by creating a user through the app
2. Verify tables are created: Check the Table Editor in Supabase Dashboard
3. Test RLS policies: Try to query data with and without authentication

### 5. Optional: Add Test Data

Uncomment the test data section at the bottom of `post-init.sql` if you want sample data for development.

## Database Schema

### Core Tables:

- **profiles**: User profiles (extends auth.users)
- **trips**: User trips with status, visibility, and metadata
- **activities**: Trip activities/events with timing and location
- **trip_shares**: Collaboration and sharing functionality
- **trip_templates**: Reusable trip templates
- **ai_import_logs**: Track AI conversation imports

### Security Tables:

- **auth_failed_attempts**: Track failed login attempts
- **auth_account_lockouts**: Manage account lockouts
- **audit_logs**: Comprehensive audit trail

### Storage Buckets:

- **trip-covers**: Public bucket for trip cover images
- **activity-attachments**: Private bucket for activity attachments

## Troubleshooting

If you encounter errors:

1. **Extension errors**: Make sure you're running as a superuser or have the necessary permissions
2. **RLS errors**: Ensure RLS is enabled on all tables before creating policies
3. **Foreign key errors**: Run the script in order - tables must exist before references

## Direct Database Connection

For advanced operations or debugging, you can find your connection string in:

- Supabase Dashboard → Settings → Database → Connection String

**Connection String Format:**

```
postgresql://postgres:[YOUR_DB_PASSWORD]@db.[YOUR_PROJECT_ID].supabase.co:5432/postgres
```

**SECURITY WARNING**:

- NEVER hardcode database passwords in your code or documentation
- Use connection pooling for production applications
- Restrict direct database access to development environments only
- Always use the service role key for backend operations and the anon key for frontend operations
