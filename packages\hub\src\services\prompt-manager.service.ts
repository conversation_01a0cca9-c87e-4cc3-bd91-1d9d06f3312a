import { logger } from '../utils/logger';

/**
 * Model-Specific Prompt Manager
 * Manages optimized system prompts for each AI model
 * Requirements: 4.1, 4.2, 4.3 from requirements.md
 */

export class PromptManager {
  private prompts: Map<string, string> = new Map();
  private formatInstructions: Map<string, string> = new Map();

  constructor() {
    this.initializePrompts();
    this.initializeFormatInstructions();
  }

  /**
   * Get optimized prompt for model - Requirements 4.2: Use appropriate optimized prompt
   */
  getSystemPrompt(modelId: string): string {
    const prompt = this.prompts.get(modelId);
    if (prompt) {
      logger.debug('Using model-specific prompt', { modelId });
      return prompt;
    }

    // Requirements 4.3: Use default TravelViz-optimized prompt if model-specific not found
    logger.debug('Using default prompt for model', { modelId });
    return this.getDefaultPrompt();
  }

  /**
   * Get model-specific formatting instructions
   */
  getFormatInstructions(modelId: string): string {
    return this.formatInstructions.get(modelId) || this.getDefaultFormatInstructions();
  }

  /**
   * Update prompts (for A/B testing) - Requirements 4.4: Validate against test scenarios
   */
  async updatePrompt(modelId: string, prompt: string): Promise<void> {
    try {
      // Validate prompt against test scenarios
      const isValid = await this.validatePrompt(prompt);
      if (!isValid) {
        throw new Error('Prompt validation failed against test scenarios');
      }

      this.prompts.set(modelId, prompt);
      logger.info('Prompt updated successfully', { modelId });
    } catch (error) {
      logger.error('Failed to update prompt', { modelId, error });
      throw error;
    }
  }

  /**
   * Initialize model-specific prompts - Requirements 4.1: Load model-specific system prompts
   */
  private initializePrompts(): void {
    // Moonshot AI (kimi-k2:free) - Optimized for structured JSON output
    // Per design.md: "System Prompt: Optimized for structured JSON output, Temperature: 0.2"
    this.prompts.set('moonshotai/kimi-k2:free', `You are a travel itinerary parser specialized in extracting structured data from AI conversations.

Extract trip information as valid JSON with this exact structure:
{
  "title": "destination + dates (e.g., 'Paris Adventure - March 2024')",
  "startDate": "YYYY-MM-DD",
  "endDate": "YYYY-MM-DD", 
  "destination": "primary city/country",
  "description": "brief trip summary",
  "activities": [{
    "name": "activity name",
    "type": "flight|accommodation|transport|dining|activity|shopping|car_rental|tour|sightseeing|entertainment|other",
    "startTime": "YYYY-MM-DDTHH:mm:ss",
    "endTime": "YYYY-MM-DDTHH:mm:ss (optional)",
    "location": {"address": "specific location", "lat": 0, "lng": 0, "confidence": 0.8},
    "price": 0,
    "currency": "USD",
    "dayNumber": 1,
    "confidence": 0.8
  }],
  "metadata": {"totalDays": 0, "confidence": 0.8}
}

Rules:
- Only extract explicitly mentioned items
- Use confidence scores (0.1-1.0) based on clarity
- Normalize activity types to the specified list
- Include realistic time estimates
- Return {"error": true, "message": "reason"} if no valid trip found

Focus on accuracy and completeness. Kimi excels at detailed extraction.`);

    // Google Gemini 2.5 Pro - Complex itineraries, high accuracy needs
    // Per design.md: "Gemini 2.5 Pro: Complex itineraries, high accuracy needs"
    this.prompts.set('google/gemini-2.5-pro', `You are an expert travel itinerary analyzer. Extract comprehensive trip data with maximum accuracy.

CRITICAL: Return ONLY valid JSON that can be parsed by JSON.parse(). No markdown, no explanations, no additional text.

Required JSON Structure:
{
  "title": "descriptive trip title with destination and dates",
  "startDate": "YYYY-MM-DD",
  "endDate": "YYYY-MM-DD",
  "destination": "primary destination",
  "description": "detailed trip overview",
  "activities": [{
    "name": "precise activity name",
    "type": "flight|accommodation|transport|dining|activity|shopping|car_rental|tour|sightseeing|entertainment|other",
    "startTime": "YYYY-MM-DDTHH:mm:ss",
    "endTime": "YYYY-MM-DDTHH:mm:ss",
    "location": {"address": "full address", "lat": 0, "lng": 0, "confidence": 0.9},
    "price": 0,
    "currency": "USD",
    "dayNumber": 1,
    "confidence": 0.9
  }],
  "metadata": {"totalDays": 0, "confidence": 0.9}
}

Processing Steps:
1. Identify trip title, dates, and primary destination
2. Extract each activity with precise details
3. Infer logical connections and resolve ambiguities
4. Validate temporal consistency
5. Apply travel domain knowledge for missing details
6. Format as valid JSON with high confidence scores

Use your advanced reasoning for complex, multi-destination trips. Return {"error": true, "message": "reason"} if no valid trip found.`);

    // Google Gemini 2.5 Flash - Balanced performance for medium complexity
    // Per design.md: "Gemini 2.5 Flash: Balanced performance for medium complexity"
    this.prompts.set('google/gemini-2.5-flash', `You are a fast and accurate travel itinerary parser.

IMPORTANT: Return ONLY valid JSON. No markdown formatting, no explanations, no additional text.

Extract trip data as this exact JSON structure:
{
  "title": "trip title with destination and dates",
  "startDate": "YYYY-MM-DD",
  "endDate": "YYYY-MM-DD",
  "destination": "main destination",
  "description": "trip summary",
  "activities": [{
    "name": "activity name",
    "type": "flight|accommodation|transport|dining|activity|shopping|car_rental|tour|sightseeing|entertainment|other",
    "startTime": "YYYY-MM-DDTHH:mm:ss",
    "location": {"address": "location", "lat": 0, "lng": 0, "confidence": 0.8},
    "price": 0,
    "currency": "USD",
    "dayNumber": 1,
    "confidence": 0.8
  }],
  "metadata": {"totalDays": 0, "confidence": 0.8}
}

Processing Rules:
- Extract clear, explicitly mentioned travel items
- Use reasonable defaults for missing data
- Maintain consistent JSON formatting
- Focus on the most important activities
- Balance speed with accuracy for medium complexity trips

Return {"error": true, "message": "reason"} if no valid trip found.`);

    // Google Gemini 2.0 Flash - High-volume processing, simple to medium complexity
    // Per design.md: "Gemini 2.0 Flash: High-volume processing, simple to medium complexity"
    this.prompts.set('google/gemini-2.0-flash', `Travel itinerary parser. Extract trip data as valid JSON ONLY.

CRITICAL: Return valid JSON that can be parsed by JSON.parse(). No markdown, no explanations.

JSON Format:
{
  "title": "destination + timeframe",
  "startDate": "YYYY-MM-DD",
  "endDate": "YYYY-MM-DD",
  "destination": "primary location",
  "activities": [{
    "name": "activity",
    "type": "flight|accommodation|transport|dining|activity|shopping|car_rental|tour|sightseeing|entertainment|other",
    "startTime": "YYYY-MM-DDTHH:mm:ss",
    "location": {"address": "location", "lat": 0, "lng": 0, "confidence": 0.7},
    "dayNumber": 1,
    "confidence": 0.7
  }],
  "metadata": {"totalDays": 0, "confidence": 0.7}
}

Quick extraction rules:
- Focus on main activities and locations
- Use standard activity types from the list above
- Include day numbers for organization
- Prioritize speed while maintaining essential accuracy
- Return {"error": true, "message": "reason"} if no trip found`);

    // OpenAI GPT-4.1 Nano - Last resort fallback, minimal token usage
    // Per design.md: "System Prompt: Minimal token usage optimization"
    this.prompts.set('openai/gpt-4.1-nano', `Extract trip as valid JSON ONLY:
{
  "title": "trip title",
  "startDate": "YYYY-MM-DD",
  "endDate": "YYYY-MM-DD",
  "destination": "location",
  "activities": [{
    "name": "activity",
    "type": "flight|accommodation|transport|dining|activity|other",
    "startTime": "YYYY-MM-DDTHH:mm:ss",
    "location": {"address": "location"},
    "dayNumber": 1
  }]
}

Extract clear, explicit items only. Return {"error":true,"message":"reason"} if no trip.`);

    logger.info('Model-specific prompts initialized', {
      promptCount: this.prompts.size,
      models: Array.from(this.prompts.keys())
    });
  }

  /**
   * Initialize format instructions for each model
   */
  private initializeFormatInstructions(): void {
    // Moonshot AI - Structured JSON focus
    this.formatInstructions.set('moonshotai/kimi-k2:free',
      'Return valid JSON only. No markdown formatting. Use double quotes for all strings. Must be parseable by JSON.parse().');

    // Gemini models - Enhanced JSON requirements
    this.formatInstructions.set('google/gemini-2.5-pro',
      'CRITICAL: Return ONLY valid JSON that can be parsed by JSON.parse(). No markdown, no explanations, no additional text. Ensure all required fields are present.');

    this.formatInstructions.set('google/gemini-2.5-flash',
      'IMPORTANT: Return ONLY valid JSON. No markdown formatting, no explanations. Must be parseable by JSON.parse().');

    this.formatInstructions.set('google/gemini-2.0-flash',
      'Return valid JSON ONLY. No markdown, no explanations. Must be parseable by JSON.parse(). Focus on essential data.');

    // OpenAI - Minimal but valid JSON
    this.formatInstructions.set('openai/gpt-4.1-nano',
      'Valid JSON only. No markdown. Must be parseable by JSON.parse(). Essential fields only.');
  }

  /**
   * Get default TravelViz-optimized prompt
   */
  private getDefaultPrompt(): string {
    return `Extract trip data from the conversation as JSON:
{
  "title": "destination + dates",
  "startDate": "YYYY-MM-DD",
  "endDate": "YYYY-MM-DD",
  "destination": "main city/country",
  "activities": [{
    "name": "activity",
    "type": "flight|accommodation|transport|dining|activity|shopping|car_rental|tour|sightseeing|entertainment|other",
    "startTime": "YYYY-MM-DDTHH:mm:ss",
    "location": {"address": "location", "lat": 0, "lng": 0},
    "price": 0,
    "currency": "USD",
    "dayNumber": 1,
    "confidence": 0.8
  }],
  "metadata": {"totalDays": 0, "confidence": 0.8}
}

Types: flight=air travel, accommodation=hotels, activity=attractions, transport=ground travel, dining=food
Only extract explicitly mentioned items. Return {"error":true,"message":"reason"} if no trip found.`;
  }

  /**
   * Get default format instructions
   */
  private getDefaultFormatInstructions(): string {
    return 'Return valid JSON format with all required fields. Use appropriate confidence scores.';
  }

  /**
   * Validate prompt against test scenarios
   */
  private async validatePrompt(prompt: string): Promise<boolean> {
    try {
      // Basic validation checks
      const hasJsonStructure = prompt.includes('{') && prompt.includes('}');
      const hasRequiredFields = prompt.includes('title') && prompt.includes('activities');
      const hasActivityTypes = prompt.includes('flight|accommodation|transport');
      
      if (!hasJsonStructure || !hasRequiredFields || !hasActivityTypes) {
        logger.warn('Prompt validation failed - missing required elements');
        return false;
      }

      // Additional validation could include:
      // - Testing against sample conversations
      // - Checking token count limits
      // - Validating JSON schema compliance
      
      return true;
    } catch (error) {
      logger.error('Prompt validation error', { error });
      return false;
    }
  }
}

// Export singleton instance
export const promptManager = new PromptManager();