#!/bin/bash

# TravelViz Repository Cleanup Script
# Removes files that shouldn't be committed to GitHub

echo "🧹 Cleaning TravelViz repository..."

# Remove environment files from Git tracking (but keep them locally)
echo "📝 Removing environment files from Git tracking..."
git rm --cached packages/hub/.env 2>/dev/null || echo "packages/hub/.env not tracked"
git rm --cached packages/web/.env.local 2>/dev/null || echo "packages/web/.env.local not tracked"
git rm --cached .env 2>/dev/null || echo ".env not tracked"

# Remove build outputs from Git tracking
echo "🏗️ Removing build outputs from Git tracking..."
git rm -r --cached packages/*/dist/ 2>/dev/null || echo "No dist directories tracked"
git rm -r --cached packages/web/.next/ 2>/dev/null || echo ".next not tracked"
git rm -r --cached packages/*/build/ 2>/dev/null || echo "No build directories tracked"

# Remove TypeScript build info
echo "📘 Removing TypeScript build info..."
git rm --cached packages/*/*.tsbuildinfo 2>/dev/null || echo "No .tsbuildinfo files tracked"
git rm --cached *.tsbuildinfo 2>/dev/null || echo "No root .tsbuildinfo files tracked"

# Remove node_modules if accidentally tracked
echo "📦 Removing node_modules from Git tracking..."
git rm -r --cached node_modules/ 2>/dev/null || echo "node_modules not tracked"
git rm -r --cached packages/*/node_modules/ 2>/dev/null || echo "No package node_modules tracked"

# Remove log files
echo "📋 Removing log files..."
git rm --cached *.log 2>/dev/null || echo "No log files tracked"
git rm --cached packages/*/*.log 2>/dev/null || echo "No package log files tracked"

# Check for any remaining sensitive files
echo "🔍 Checking for sensitive files..."
if git ls-files | grep -E "\.(env|log)$" > /dev/null; then
    echo "⚠️  Warning: Some environment or log files are still tracked:"
    git ls-files | grep -E "\.(env|log)$"
    echo "Please review and remove manually if needed."
else
    echo "✅ No sensitive files found in tracking"
fi

# Show current status
echo "📊 Current repository status:"
git status --porcelain

echo "✨ Repository cleanup complete!"
echo ""
echo "Next steps:"
echo "1. Review the changes: git status"
echo "2. Commit the cleanup: git commit -m 'chore: remove sensitive files from tracking'"
echo "3. Make sure .gitignore is working: git status --ignored"
