import { describe, it, expect, beforeAll, afterAll, beforeEach, vi } from 'vitest';
import request from 'supertest';
import { createServer } from '../server';
import { Express } from 'express';
import { createClient } from '@supabase/supabase-js';
import { generateTokenPair } from '../utils/tokens';
import fs from 'fs';
import path from 'path';

// Mock environment variables for testing
process.env.NODE_ENV = 'test';
process.env.JWT_SECRET = 'test-jwt-secret-key-for-testing-only';
process.env.SUPABASE_URL = process.env.SUPABASE_URL || 'https://test.supabase.co';
process.env.SUPABASE_ANON_KEY = process.env.SUPABASE_ANON_KEY || 'test-anon-key';
process.env.GOOGLE_PLACES_API_KEY = process.env.GOOGLE_PLACES_API_KEY || 'test-places-key';

describe('TravelViz API Integration Tests', () => {
  let app: Express;
  let authToken: string;
  let testUserId: string;
  let testTripId: string;
  let testActivityId: string;

  const testUser = {
    id: 'test-user-123',
    email: '<EMAIL>',
    name: 'Test User'
  };

  beforeAll(async () => {
    app = createServer();
    
    // Generate auth token for testing
    const tokens = generateTokenPair(testUser);
    authToken = tokens.accessToken;
    testUserId = testUser.id;
  });

  beforeEach(() => {
    // Reset any mocks between tests
    vi.clearAllMocks();
  });

  describe('Health Check', () => {
    it('should return healthy status', async () => {
      const response = await request(app)
        .get('/health')
        .expect(200);

      expect(response.body).toHaveProperty('status', 'ok');
    });
  });

  describe('Authentication API', () => {
    describe('POST /api/v1/auth/signup', () => {
      it('should validate required fields', async () => {
        const response = await request(app)
          .post('/api/v1/auth/signup')
          .send({})
          .expect(400);

        expect(response.body).toHaveProperty('success', false);
        expect(response.body).toHaveProperty('error');
      });

      it('should validate email format', async () => {
        const response = await request(app)
          .post('/api/v1/auth/signup')
          .send({
            email: 'invalid-email',
            password: 'ValidPassword123!',
            name: 'Test User'
          })
          .expect(400);

        expect(response.body).toHaveProperty('success', false);
      });

      it('should validate password strength', async () => {
        const response = await request(app)
          .post('/api/v1/auth/signup')
          .send({
            email: '<EMAIL>',
            password: 'weak',
            name: 'Test User'
          })
          .expect(400);

        expect(response.body).toHaveProperty('success', false);
      });
    });

    describe('POST /api/v1/auth/login', () => {
      it('should validate required fields', async () => {
        const response = await request(app)
          .post('/api/v1/auth/login')
          .send({})
          .expect(400);

        expect(response.body).toHaveProperty('success', false);
      });

      it('should validate email format', async () => {
        const response = await request(app)
          .post('/api/v1/auth/login')
          .send({
            email: 'invalid-email',
            password: 'password123'
          })
          .expect(400);

        expect(response.body).toHaveProperty('success', false);
      });
    });

    describe('GET /api/v1/auth/me', () => {
      it('should require authentication', async () => {
        const response = await request(app)
          .get('/api/v1/auth/me')
          .expect(401);

        expect(response.body).toHaveProperty('success', false);
        expect(response.body).toHaveProperty('error', 'Unauthorized');
      });

      it('should return user info with valid token', async () => {
        const response = await request(app)
          .get('/api/v1/auth/me')
          .set('Authorization', `Bearer ${authToken}`)
          .expect(200);

        expect(response.body).toHaveProperty('success', true);
        expect(response.body.data).toHaveProperty('id', testUser.id);
        expect(response.body.data).toHaveProperty('email', testUser.email);
      });
    });
  });

  describe('Trips API', () => {
    describe('POST /api/v1/trips', () => {
      it('should require authentication', async () => {
        const response = await request(app)
          .post('/api/v1/trips')
          .send({
            title: 'Test Trip',
            destination: 'Paris, France'
          })
          .expect(401);

        expect(response.body).toHaveProperty('success', false);
      });

      it('should create a trip with valid data', async () => {
        const tripData = {
          title: 'Paris Adventure',
          destination: 'Paris, France',
          startDate: '2024-06-01',
          endDate: '2024-06-07',
          description: 'A wonderful trip to Paris'
        };

        const response = await request(app)
          .post('/api/v1/trips')
          .set('Authorization', `Bearer ${authToken}`)
          .send(tripData)
          .expect(201);

        expect(response.body).toHaveProperty('success', true);
        expect(response.body.data).toHaveProperty('id');
        expect(response.body.data).toHaveProperty('title', tripData.title);
        expect(response.body.data).toHaveProperty('destination', tripData.destination);
        
        // Store for later tests
        testTripId = response.body.data.id;
      });

      it('should validate required fields', async () => {
        const response = await request(app)
          .post('/api/v1/trips')
          .set('Authorization', `Bearer ${authToken}`)
          .send({})
          .expect(400);

        expect(response.body).toHaveProperty('success', false);
      });
    });

    describe('GET /api/v1/trips', () => {
      it('should require authentication', async () => {
        const response = await request(app)
          .get('/api/v1/trips')
          .expect(401);

        expect(response.body).toHaveProperty('success', false);
      });

      it('should return user trips', async () => {
        const response = await request(app)
          .get('/api/v1/trips')
          .set('Authorization', `Bearer ${authToken}`)
          .expect(200);

        expect(response.body).toHaveProperty('success', true);
        expect(response.body.data).toBeInstanceOf(Array);
      });
    });

    describe('GET /api/v1/trips/:id', () => {
      it('should return trip details', async () => {
        if (!testTripId) {
          // Create a trip first
          const tripResponse = await request(app)
            .post('/api/v1/trips')
            .set('Authorization', `Bearer ${authToken}`)
            .send({
              title: 'Test Trip for Details',
              destination: 'London, UK',
              startDate: '2024-07-01',
              endDate: '2024-07-05'
            });
          testTripId = tripResponse.body.data.id;
        }

        const response = await request(app)
          .get(`/api/v1/trips/${testTripId}`)
          .set('Authorization', `Bearer ${authToken}`)
          .expect(200);

        expect(response.body).toHaveProperty('success', true);
        expect(response.body.data).toHaveProperty('id', testTripId);
      });

      it('should return 404 for non-existent trip', async () => {
        const response = await request(app)
          .get('/api/v1/trips/non-existent-id')
          .set('Authorization', `Bearer ${authToken}`)
          .expect(404);

        expect(response.body).toHaveProperty('success', false);
      });
    });
  });

  describe('Activities API', () => {
    beforeEach(async () => {
      // Ensure we have a test trip
      if (!testTripId) {
        const tripResponse = await request(app)
          .post('/api/v1/trips')
          .set('Authorization', `Bearer ${authToken}`)
          .send({
            title: 'Test Trip for Activities',
            destination: 'Rome, Italy',
            startDate: '2024-08-01',
            endDate: '2024-08-05'
          });
        testTripId = tripResponse.body.data.id;
      }
    });

    describe('POST /api/v1/trips/:tripId/activities', () => {
      it('should create an activity', async () => {
        const activityData = {
          title: 'Visit Colosseum',
          type: 'sightseeing',
          startTime: '09:00',
          endTime: '11:00',
          date: '2024-08-02',
          location: 'Colosseum, Rome',
          description: 'Explore the ancient amphitheater'
        };

        const response = await request(app)
          .post(`/api/v1/trips/${testTripId}/activities`)
          .set('Authorization', `Bearer ${authToken}`)
          .send(activityData)
          .expect(201);

        expect(response.body).toHaveProperty('success', true);
        expect(response.body.data).toHaveProperty('id');
        expect(response.body.data).toHaveProperty('title', activityData.title);
        
        testActivityId = response.body.data.id;
      });

      it('should validate activity data', async () => {
        const response = await request(app)
          .post(`/api/v1/trips/${testTripId}/activities`)
          .set('Authorization', `Bearer ${authToken}`)
          .send({})
          .expect(400);

        expect(response.body).toHaveProperty('success', false);
      });
    });
  });

  describe('AI Import API', () => {
    describe('POST /api/v1/import/parse-simple', () => {
      it('should require authentication', async () => {
        const response = await request(app)
          .post('/api/v1/import/parse-simple')
          .send({
            content: 'Sample travel conversation content here...',
            source: 'chatgpt'
          })
          .expect(401);

        expect(response.body).toHaveProperty('success', false);
      });

      it('should validate content length', async () => {
        const response = await request(app)
          .post('/api/v1/import/parse-simple')
          .set('Authorization', `Bearer ${authToken}`)
          .send({
            content: 'Too short',
            source: 'chatgpt'
          })
          .expect(400);

        expect(response.body).toHaveProperty('success', false);
        expect(response.body.error).toContain('too short');
      });

      it('should accept valid AI conversation content', async () => {
        const longContent = `
          User: I'm planning a 5-day trip to Tokyo in March. Can you help me create an itinerary?
          
          Assistant: I'd be happy to help you plan your Tokyo trip! Here's a suggested 5-day itinerary for March:
          
          Day 1: Arrival and Shibuya/Harajuku
          - Arrive at Narita/Haneda Airport
          - Check into hotel in Shibuya area
          - Visit Shibuya Crossing and Hachiko Statue
          - Explore Harajuku and Takeshita Street
          - Dinner in Shibuya
          
          Day 2: Traditional Tokyo
          - Morning: Visit Senso-ji Temple in Asakusa
          - Afternoon: Explore Tokyo National Museum in Ueno
          - Evening: Stroll through Ueno Park
          
          Day 3: Modern Tokyo
          - Morning: Tokyo Skytree observation deck
          - Afternoon: Shopping in Ginza district
          - Evening: Dinner in Tsukiji area
          
          Day 4: Day trip to Nikko
          - Early morning train to Nikko
          - Visit Toshogu Shrine
          - Explore Nikko National Park
          - Return to Tokyo in evening
          
          Day 5: Final day
          - Morning: Last-minute shopping in Shinjuku
          - Afternoon: Departure preparations
          - Evening: Departure from airport
        `;

        const response = await request(app)
          .post('/api/v1/import/parse-simple')
          .set('Authorization', `Bearer ${authToken}`)
          .send({
            content: longContent,
            source: 'chatgpt'
          })
          .expect(200);

        expect(response.body).toHaveProperty('success', true);
        expect(response.body.data).toHaveProperty('importId');
        expect(response.body.data).toHaveProperty('message', 'Parsing started');
      });
    });

    describe('PDF Import', () => {
      it('should require authentication', async () => {
        const response = await request(app)
          .post('/api/v1/import/pdf')
          .expect(401);

        expect(response.body).toHaveProperty('success', false);
      });

      it('should validate file upload', async () => {
        const response = await request(app)
          .post('/api/v1/import/pdf')
          .set('Authorization', `Bearer ${authToken}`)
          .expect(400);

        expect(response.body).toHaveProperty('success', false);
        expect(response.body.error).toContain('No file uploaded');
      });

      // Note: Testing actual PDF upload would require a test PDF file
      // This would be implemented with a sample PDF in the test-data directory
    });
  });

  describe('Places API', () => {
    describe('GET /api/v1/places/autocomplete', () => {
      it('should require authentication', async () => {
        const response = await request(app)
          .get('/api/v1/places/autocomplete?query=Paris')
          .expect(401);

        expect(response.body).toHaveProperty('success', false);
      });

      it('should validate query parameter', async () => {
        const response = await request(app)
          .get('/api/v1/places/autocomplete')
          .set('Authorization', `Bearer ${authToken}`)
          .expect(400);

        expect(response.body).toHaveProperty('success', false);
        expect(response.body.error).toContain('Query parameter is required');
      });

      it('should return places for valid query', async () => {
        const response = await request(app)
          .get('/api/v1/places/autocomplete?query=Paris')
          .set('Authorization', `Bearer ${authToken}`)
          .expect(200);

        expect(response.body).toHaveProperty('success', true);
        expect(response.body.data).toBeInstanceOf(Array);
      });
    });

    describe('GET /api/v1/places/:placeId', () => {
      it('should require authentication', async () => {
        const response = await request(app)
          .get('/api/v1/places/test-place-id')
          .expect(401);

        expect(response.body).toHaveProperty('success', false);
      });

      it('should validate place ID', async () => {
        const response = await request(app)
          .get('/api/v1/places/')
          .set('Authorization', `Bearer ${authToken}`)
          .expect(404); // Route not found

        // This tests that the route requires a placeId parameter
      });
    });
  });

  describe('Rate Limiting', () => {
    it('should apply rate limiting to auth endpoints', async () => {
      // Make multiple rapid requests to test rate limiting
      const requests = Array(10).fill(null).map(() =>
        request(app)
          .post('/api/v1/auth/login')
          .send({
            email: '<EMAIL>',
            password: 'password123'
          })
      );

      const responses = await Promise.all(requests);
      
      // At least one should be rate limited (429 status)
      const rateLimitedResponses = responses.filter(r => r.status === 429);
      expect(rateLimitedResponses.length).toBeGreaterThan(0);
    });
  });

  describe('Error Handling', () => {
    it('should handle 404 for non-existent routes', async () => {
      const response = await request(app)
        .get('/api/v1/non-existent-route')
        .expect(404);

      expect(response.body).toHaveProperty('success', false);
    });

    it('should handle malformed JSON', async () => {
      const response = await request(app)
        .post('/api/v1/auth/login')
        .set('Content-Type', 'application/json')
        .send('{"invalid": json}')
        .expect(400);

      expect(response.body).toHaveProperty('success', false);
    });

    it('should handle oversized requests', async () => {
      const largePayload = 'x'.repeat(2 * 1024 * 1024); // 2MB payload

      const response = await request(app)
        .post('/api/v1/import/parse-simple')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          content: largePayload,
          source: 'chatgpt'
        })
        .expect(413);

      expect(response.body).toHaveProperty('success', false);
    });
  });

  describe('Security Headers', () => {
    it('should include security headers', async () => {
      const response = await request(app)
        .get('/health')
        .expect(200);

      expect(response.headers).toHaveProperty('x-content-type-options', 'nosniff');
      expect(response.headers).toHaveProperty('x-frame-options', 'DENY');
      expect(response.headers).toHaveProperty('x-xss-protection', '1; mode=block');
    });
  });

  describe('CORS', () => {
    it('should handle CORS preflight requests', async () => {
      const response = await request(app)
        .options('/api/v1/trips')
        .set('Origin', 'http://localhost:3000')
        .set('Access-Control-Request-Method', 'POST')
        .set('Access-Control-Request-Headers', 'Content-Type,Authorization')
        .expect(204);

      expect(response.headers).toHaveProperty('access-control-allow-origin');
      expect(response.headers).toHaveProperty('access-control-allow-methods');
    });
  });
});