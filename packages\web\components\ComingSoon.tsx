"use client";

import { motion } from 'framer-motion';
import { <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>R<PERSON> } from 'lucide-react';
import { Button } from '@/components/ui/button';
import Link from 'next/link';

interface ComingSoonProps {
  title: string;
  description: string;
  features?: string[];
  showSignup?: boolean;
  className?: string;
}

export function ComingSoon({ 
  title, 
  description, 
  features = [],
  showSignup = true,
  className = ''
}: ComingSoonProps) {
  return (
    <div className={`min-h-[60vh] flex items-center justify-center ${className}`}>
      <div className="max-w-2xl mx-auto text-center px-4 sm:px-6 lg:px-8">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="space-y-6"
        >
          {/* Icon */}
          <div className="flex justify-center">
            <div className="bg-orange-100 rounded-full p-4">
              <Clock className="h-8 w-8 text-orange-600" />
            </div>
          </div>

          {/* Title */}
          <div className="space-y-3">
            <h1 className="text-3xl sm:text-4xl font-bold text-gray-900">
              {title}
            </h1>
            <p className="text-lg text-gray-600 max-w-xl mx-auto">
              {description}
            </p>
          </div>

          {/* Available Now Badge */}
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ delay: 0.2, duration: 0.5 }}
            className="inline-flex items-center space-x-2 bg-green-100 text-green-800 rounded-full px-4 py-2 text-sm font-medium"
          >
            <Sparkles className="h-4 w-4" />
            <span>Available Now</span>
          </motion.div>

          {/* Features List */}
          {features.length > 0 && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.4, duration: 0.5 }}
              className="bg-gray-50 rounded-xl p-6 text-left"
            >
              <h3 className="font-semibold text-gray-900 mb-4 text-center">
                What's Coming:
              </h3>
              <ul className="space-y-2">
                {features.map((feature, index) => (
                  <li key={index} className="flex items-start space-x-3">
                    <ArrowRight className="h-4 w-4 text-orange-500 mt-0.5 flex-shrink-0" />
                    <span className="text-gray-700">{feature}</span>
                  </li>
                ))}
              </ul>
            </motion.div>
          )}

          {/* Get Started */}
          {showSignup && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.6, duration: 0.5 }}
              className="space-y-4"
            >
              <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                <div className="flex items-center justify-center space-x-2 mb-4">
                  <Sparkles className="h-5 w-5 text-orange-500" />
                  <h3 className="font-semibold text-gray-900">Ready to Get Started?</h3>
                </div>
                <div className="flex flex-col sm:flex-row gap-3 justify-center max-w-md mx-auto">
                  <Button size="lg" asChild className="flex-1">
                    <Link href="/signup">Get Started Free</Link>
                  </Button>
                  <Button size="lg" variant="outline" asChild className="flex-1">
                    <Link href="/examples">View Examples</Link>
                  </Button>
                </div>
              </div>
            </motion.div>
          )}

          {/* Estimated Timeline */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.8, duration: 0.5 }}
            className="text-sm text-gray-500"
          >
            <p>Join thousands of travelers already using TravelViz! 🚀</p>
          </motion.div>
        </motion.div>
      </div>
    </div>
  );
} 