-- AI Model Optimization Schema Migration
-- Creates tables and functions for intelligent AI model selection and usage tracking
-- Requirements: 1.1, 1.2, 1.3 from requirements.md

-- Enable pg_cron extension for daily resets
CREATE EXTENSION IF NOT EXISTS pg_cron;

-- AI model usage tracking table
-- Tracks daily usage per model with Pacific Time reset
CREATE TABLE IF NOT EXISTS ai_model_usage (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    model_id TEXT NOT NULL,
    date DATE NOT NULL DEFAULT CURRENT_DATE,
    request_count INTEGER DEFAULT 0,
    input_tokens BIGINT DEFAULT 0,
    output_tokens BIGINT DEFAULT 0,
    total_cost DECIMAL(10,6) DEFAULT 0,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    
    UNIQUE(model_id, date)
);

-- AI request logs for detailed tracking
-- Records individual API calls for monitoring and debugging
CREATE TABLE IF NOT EXISTS ai_request_logs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES auth.users(id),
    session_id UUID,
    model_id TEXT NOT NULL,
    input_tokens INTEGER,
    output_tokens INTEGER,
    cost DECIMAL(8,6),
    duration_ms INTEGER,
    success BOOLEAN DEFAULT true,
    error_message TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Model configuration and limits
-- Stores model metadata, limits, and cost information
CREATE TABLE IF NOT EXISTS ai_model_configs (
    id TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    provider TEXT NOT NULL,
    daily_request_limit INTEGER,
    rpm_limit INTEGER,
    tpm_limit BIGINT,
    rpd_limit INTEGER,
    cost_per_1k_tokens DECIMAL(8,6),
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Add usage tracking columns to existing ai_import_logs table
-- Per design.md: "Add model tracking to existing ai_import_logs"
ALTER TABLE ai_import_logs ADD COLUMN IF NOT EXISTS model_used TEXT;
ALTER TABLE ai_import_logs ADD COLUMN IF NOT EXISTS input_tokens INTEGER;
ALTER TABLE ai_import_logs ADD COLUMN IF NOT EXISTS output_tokens INTEGER;
ALTER TABLE ai_import_logs ADD COLUMN IF NOT EXISTS processing_cost DECIMAL(8,6);
ALTER TABLE ai_import_logs ADD COLUMN IF NOT EXISTS fallback_attempts INTEGER DEFAULT 0;

-- Create indexes for optimal query performance
-- Per design.md: "Indexes: On model_id, date, user_id for fast queries"
CREATE INDEX IF NOT EXISTS idx_ai_model_usage_model_date ON ai_model_usage(model_id, date);
CREATE INDEX IF NOT EXISTS idx_ai_model_usage_date ON ai_model_usage(date);
CREATE INDEX IF NOT EXISTS idx_ai_request_logs_model_id ON ai_request_logs(model_id);
CREATE INDEX IF NOT EXISTS idx_ai_request_logs_user_id ON ai_request_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_ai_request_logs_created_at ON ai_request_logs(created_at);
CREATE INDEX IF NOT EXISTS idx_ai_request_logs_session_id ON ai_request_logs(session_id);

-- Function to reset daily usage counters
-- Per design.md: "Reset daily counters (called at midnight PT)"
CREATE OR REPLACE FUNCTION reset_daily_ai_usage()
RETURNS void AS $$
BEGIN
    -- Archive yesterday's data if needed (for future archival feature)
    -- INSERT INTO ai_model_usage_archive 
    -- SELECT * FROM ai_model_usage 
    -- WHERE date < CURRENT_DATE;
    
    -- Reset current day counters
    UPDATE ai_model_usage 
    SET request_count = 0, 
        input_tokens = 0, 
        output_tokens = 0,
        total_cost = 0,
        updated_at = NOW()
    WHERE date = CURRENT_DATE;
    
    -- Clear Redis cache for usage counters
    PERFORM pg_notify('ai_usage_reset', 'daily_reset_complete');
    
    -- Log the reset operation
    RAISE NOTICE 'Daily AI usage counters reset at %', NOW();
END;
$$ LANGUAGE plpgsql;

-- Schedule daily reset at midnight Pacific Time
-- Per design.md: "pg_cron uses UTC, so midnight PT = 8:00 UTC (PST) or 7:00 UTC (PDT)"
-- Using 8:00 UTC for PST (covers most of the year)
SELECT cron.schedule(
    'daily-ai-usage-reset',
    '0 8 * * *', -- 8:00 UTC = midnight PST
    'SELECT reset_daily_ai_usage();'
);

-- Insert initial model configurations
-- Per design.md model specifications and current ai.config.ts
INSERT INTO ai_model_configs (id, name, provider, daily_request_limit, rpm_limit, tpm_limit, rpd_limit, cost_per_1k_tokens, is_active) VALUES
    ('moonshotai/kimi-k2:free', 'Moonshot AI Kimi-K2 Free', 'moonshot', 1000, 60, 1000000, 1000, 0.000000, true),
    ('google/gemini-2.5-pro', 'Google Gemini 2.5 Pro', 'google', 100, 5, 250000, 100, 0.000000, true),
    ('google/gemini-2.5-flash', 'Google Gemini 2.5 Flash', 'google', 250, 10, 250000, 250, 0.000000, true),
    ('google/gemini-2.0-flash', 'Google Gemini 2.0 Flash', 'google', 200, 15, 1000000, 200, 0.000000, true),
    ('openai/gpt-4.1-nano', 'OpenAI GPT-4.1 Nano', 'openrouter', 10000, 500, 2000000, 10000, 0.000150, true)
ON CONFLICT (id) DO UPDATE SET
    name = EXCLUDED.name,
    provider = EXCLUDED.provider,
    daily_request_limit = EXCLUDED.daily_request_limit,
    rpm_limit = EXCLUDED.rpm_limit,
    tpm_limit = EXCLUDED.tpm_limit,
    rpd_limit = EXCLUDED.rpd_limit,
    cost_per_1k_tokens = EXCLUDED.cost_per_1k_tokens,
    is_active = EXCLUDED.is_active,
    updated_at = NOW();

-- Create function to get current usage for a model
CREATE OR REPLACE FUNCTION get_model_usage(model_id_param TEXT)
RETURNS TABLE(
    model_id TEXT,
    request_count INTEGER,
    input_tokens BIGINT,
    output_tokens BIGINT,
    total_cost DECIMAL(10,6),
    last_reset TIMESTAMPTZ
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        u.model_id,
        u.request_count,
        u.input_tokens,
        u.output_tokens,
        u.total_cost,
        u.updated_at as last_reset
    FROM ai_model_usage u
    WHERE u.model_id = model_id_param 
    AND u.date = CURRENT_DATE;
    
    -- If no record exists for today, return zeros
    IF NOT FOUND THEN
        RETURN QUERY
        SELECT 
            model_id_param,
            0::INTEGER,
            0::BIGINT,
            0::BIGINT,
            0::DECIMAL(10,6),
            NOW()::TIMESTAMPTZ;
    END IF;
END;
$$ LANGUAGE plpgsql;

-- Create function to check if model is available (not rate limited)
CREATE OR REPLACE FUNCTION is_model_available(model_id_param TEXT)
RETURNS BOOLEAN AS $$
DECLARE
    current_usage RECORD;
    model_config RECORD;
BEGIN
    -- Get current usage
    SELECT * INTO current_usage FROM get_model_usage(model_id_param);
    
    -- Get model configuration
    SELECT * INTO model_config FROM ai_model_configs WHERE id = model_id_param;
    
    -- If model config not found, assume unavailable
    IF model_config IS NULL OR NOT model_config.is_active THEN
        RETURN FALSE;
    END IF;
    
    -- Check daily request limit
    IF model_config.daily_request_limit IS NOT NULL AND 
       current_usage.request_count >= model_config.daily_request_limit THEN
        RETURN FALSE;
    END IF;
    
    -- Model is available
    RETURN TRUE;
END;
$$ LANGUAGE plpgsql;

-- Grant necessary permissions
GRANT SELECT, INSERT, UPDATE ON ai_model_usage TO authenticated;
GRANT SELECT, INSERT ON ai_request_logs TO authenticated;
GRANT SELECT ON ai_model_configs TO authenticated;
GRANT EXECUTE ON FUNCTION get_model_usage(TEXT) TO authenticated;
GRANT EXECUTE ON FUNCTION is_model_available(TEXT) TO authenticated;

-- Enable Row Level Security
ALTER TABLE ai_model_usage ENABLE ROW LEVEL SECURITY;
ALTER TABLE ai_request_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE ai_model_configs ENABLE ROW LEVEL SECURITY;

-- RLS Policies for ai_model_usage (system-wide, no user restriction)
CREATE POLICY "Allow all authenticated users to read usage" ON ai_model_usage
    FOR SELECT TO authenticated USING (true);

CREATE POLICY "Allow service to update usage" ON ai_model_usage
    FOR ALL TO authenticated USING (true);

-- RLS Policies for ai_request_logs (users can only see their own logs)
CREATE POLICY "Users can view their own request logs" ON ai_request_logs
    FOR SELECT TO authenticated USING (auth.uid() = user_id);

CREATE POLICY "Allow service to insert request logs" ON ai_request_logs
    FOR INSERT TO authenticated WITH CHECK (true);

-- RLS Policies for ai_model_configs (read-only for all authenticated users)
CREATE POLICY "Allow all authenticated users to read model configs" ON ai_model_configs
    FOR SELECT TO authenticated USING (true);

-- Create a view for easy usage monitoring
CREATE OR REPLACE VIEW ai_usage_summary AS
SELECT 
    c.name as model_name,
    c.provider,
    COALESCE(u.request_count, 0) as daily_requests,
    COALESCE(u.input_tokens, 0) as daily_input_tokens,
    COALESCE(u.output_tokens, 0) as daily_output_tokens,
    COALESCE(u.total_cost, 0) as daily_cost,
    c.daily_request_limit,
    c.is_active,
    CASE 
        WHEN c.daily_request_limit IS NULL THEN 0
        ELSE ROUND((COALESCE(u.request_count, 0)::DECIMAL / c.daily_request_limit) * 100, 2)
    END as usage_percentage
FROM ai_model_configs c
LEFT JOIN ai_model_usage u ON c.id = u.model_id AND u.date = CURRENT_DATE
WHERE c.is_active = true
ORDER BY c.provider, c.name;

GRANT SELECT ON ai_usage_summary TO authenticated;