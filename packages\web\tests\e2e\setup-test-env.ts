import dotenv from 'dotenv';
import path from 'path';
import fs from 'fs';

// Load test environment variables
export function setupTestEnv() {
  // Try to load from multiple sources in order of precedence
  const envPaths = [
    path.join(__dirname, '.env.test'),           // Test-specific env
    path.join(__dirname, '../../.env.local'),    // Local development env
    path.join(__dirname, '../../.env.test'),     // Alternative test env
  ];

  for (const envPath of envPaths) {
    if (fs.existsSync(envPath)) {
      console.log(`Loading environment from: ${envPath}`);
      dotenv.config({ path: envPath });
      break;
    }
  }

  // Validate required environment variables
  const required = [
    'NEXT_PUBLIC_SUPABASE_URL',
    'NEXT_PUBLIC_SUPABASE_ANON_KEY',
    'NEXT_PUBLIC_API_URL'
  ];

  const missing = required.filter(key => !process.env[key]);
  if (missing.length > 0) {
    console.error('Missing required environment variables:', missing);
    console.error('Please ensure .env.local or .env.test is properly configured');
    process.exit(1);
  }

  // Set default test credentials if not provided
  if (!process.env.TEST_EMAIL || !process.env.TEST_PASSWORD) {
    console.log('Using default test credentials from auth-helpers.ts');
  }

  return {
    supabaseUrl: process.env.NEXT_PUBLIC_SUPABASE_URL!,
    supabaseAnonKey: process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    apiUrl: process.env.NEXT_PUBLIC_API_URL!,
    testEmail: process.env.TEST_EMAIL || '<EMAIL>',
    testPassword: process.env.TEST_PASSWORD || 'Flaremmk123!'
  };
}