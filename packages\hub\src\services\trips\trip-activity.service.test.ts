import { describe, it, expect, vi, beforeEach } from 'vitest';
import { TripActivityService } from './trip-activity.service';
import { getSupabaseClient, handleSupabaseError, TABLES, ActivitySchema, validateDatabaseResponse } from '../../lib/supabase';
import { getErrorMessage } from '../../utils/error-handler';
import { sanitizeActivityData } from '../../utils/sanitizer';
import { logger } from '../../utils/logger';
import { affiliateLinkInjector } from './affiliateLinkInjector.service';
import type { CreateActivityData } from '../types';

// Mock dependencies
vi.mock('../../../lib/supabase', () => ({
  getSupabaseClient: vi.fn(),
  handleSupabaseError: vi.fn(),
  validateDatabaseResponse: vi.fn(),
  ActivitySchema: {},
  TABLES: {
    TRIPS: 'trips',
    ACTIVITIES: 'activities',
  },
}));

vi.mock('../../../utils/error-handler', () => ({
  getErrorMessage: vi.fn(),
}));

vi.mock('../../../utils/sanitizer', () => ({
  sanitizeActivityData: vi.fn(),
}));

vi.mock('../../../utils/logger', () => ({
  logger: {
    info: vi.fn(),
    error: vi.fn(),
    debug: vi.fn(),
  },
}));

vi.mock('../../affiliateLinkInjector.service', () => ({
  affiliateLinkInjector: {
    injectAffiliateLinks: vi.fn(),
  },
}));

describe('TripActivityService', () => {
  let service: TripActivityService;
  let mockSupabase: any;

  const mockTrip = { id: 'trip123', user_id: 'user123' };
  const mockActivity = {
    id: 'activity123',
    trip_id: 'trip123',
    title: 'Test Activity',
    description: 'Test Description',
    type: 'activity',
    position: 0,
    currency: 'USD',
    metadata: {},
    attachments: [],
  };

  beforeEach(() => {
    vi.clearAllMocks();
    service = new TripActivityService();
    
    // Create a proper mock chain for Supabase operations  
    // Create a mock that always returns itself except for terminal operations
    const createChainableMock = () => {
      const chain: any = {
        from: vi.fn(() => chain),
        select: vi.fn(() => chain),
        insert: vi.fn(() => chain),
        update: vi.fn(() => chain),
        delete: vi.fn(() => chain),
        upsert: vi.fn(() => chain),
        eq: vi.fn(() => chain),
        order: vi.fn(() => chain),
        limit: vi.fn(() => chain),
        single: vi.fn(() => Promise.resolve({ data: null, error: null })),
        rpc: vi.fn(() => Promise.resolve({ data: null, error: null })),
      };
      return chain;
    };
    
    mockSupabase = createChainableMock();

    vi.mocked(getSupabaseClient).mockReturnValue(mockSupabase);
    vi.mocked(handleSupabaseError).mockImplementation((error) => ({
      message: error?.message || 'Unknown error',
      code: error?.code,
    }));
    vi.mocked(sanitizeActivityData).mockImplementation((data) => data);
    vi.mocked(validateDatabaseResponse).mockImplementation((schema, data) => data);
    vi.mocked(getErrorMessage).mockImplementation((error) => error.message);
  });

  describe('addActivityToTrip', () => {
    const activityData: CreateActivityData = {
      title: 'New Activity',
      description: 'Activity description',
      type: 'activity',
      startTime: '2024-01-01T10:00:00Z',
      endTime: '2024-01-01T12:00:00Z',
      location: 'Paris',
      locationLat: 48.8566,
      locationLng: 2.3522,
      price: 100,
      currency: 'USD',
      bookingUrl: 'https://example.com/booking',
      notes: 'Test notes',
    };

    it('should successfully add activity to trip', async () => {
      // Mock trip verification
      mockSupabase.single.mockResolvedValueOnce({
        data: mockTrip,
        error: null,
      });

      // Mock RPC call for position
      mockSupabase.rpc.mockResolvedValueOnce({
        data: 6,
        error: null,
      });

      // Mock affiliate link injection
      vi.mocked(affiliateLinkInjector.injectAffiliateLinks).mockResolvedValue([
        { ...mockActivity, affiliate_url: 'https://affiliate.com/link' }
      ]);

      // Mock activity creation
      mockSupabase.single.mockResolvedValueOnce({
        data: mockActivity,
        error: null,
      });

      const result = await service.addActivityToTrip('trip123', 'user123', activityData);

      expect(result).toEqual(mockActivity);
      expect(mockSupabase.from).toHaveBeenCalledWith(TABLES.TRIPS);
      expect(mockSupabase.from).toHaveBeenCalledWith(TABLES.ACTIVITIES);
      expect(mockSupabase.rpc).toHaveBeenCalledWith('get_next_activity_position', { 
        trip_id_param: 'trip123' 
      });
      expect(mockSupabase.insert).toHaveBeenCalledWith(
        expect.objectContaining({
          trip_id: 'trip123',
          title: activityData.title,
          position: 6,
        })
      );
      expect(logger.info).toHaveBeenCalledWith(
        expect.stringContaining('Injected affiliate link')
      );
    });

    it('should handle first activity (position 0)', async () => {
      // Mock trip verification
      mockSupabase.single.mockResolvedValueOnce({
        data: mockTrip,
        error: null,
      });

      // Mock RPC call for position returning 0 for first activity
      mockSupabase.rpc.mockResolvedValueOnce({
        data: 0,
        error: null,
      });

      // Mock activity creation
      mockSupabase.single.mockResolvedValueOnce({
        data: { ...mockActivity, position: 0 },
        error: null,
      });

      await service.addActivityToTrip('trip123', 'user123', activityData);

      expect(mockSupabase.insert).toHaveBeenCalledWith(
        expect.objectContaining({
          position: 0,
        })
      );
    });

    it('should throw error for unauthorized trip access', async () => {
      mockSupabase.single.mockResolvedValueOnce({
        data: null,
        error: { message: 'Not found' },
      });

      await expect(
        service.addActivityToTrip('trip123', 'user123', activityData)
      ).rejects.toThrow('Trip not found or unauthorized');
    });

    it('should validate end time is after start time', async () => {
      const invalidData = {
        ...activityData,
        startTime: '2024-01-01T12:00:00Z',
        endTime: '2024-01-01T10:00:00Z', // before start time
      };

      // Mock trip verification
      mockSupabase.single.mockResolvedValueOnce({
        data: mockTrip,
        error: null,
      });

      await expect(
        service.addActivityToTrip('trip123', 'user123', invalidData)
      ).rejects.toThrow('End time must be after or equal to start time');
    });

    it('should handle position query error', async () => {
      // Mock trip verification
      mockSupabase.single.mockResolvedValueOnce({
        data: mockTrip,
        error: null,
      });

      // Mock RPC error - it will fallback to manual calculation
      mockSupabase.rpc.mockResolvedValueOnce({
        data: null,
        error: { message: 'RPC function not found' },
      });

      // Mock manual position query
      mockSupabase.limit.mockResolvedValueOnce({
        data: [{ position: 5 }],
        error: null,
      });

      // Mock activity creation
      mockSupabase.single.mockResolvedValueOnce({
        data: mockActivity,
        error: null,
      });

      const result = await service.addActivityToTrip('trip123', 'user123', activityData);

      expect(result).toEqual(mockActivity);
      expect(mockSupabase.insert).toHaveBeenCalledWith(
        expect.objectContaining({
          position: 6, // max position + 1
        })
      );
    });

    it('should handle activity creation error', async () => {
      // Mock trip verification
      mockSupabase.single.mockResolvedValueOnce({
        data: mockTrip,
        error: null,
      });

      // Mock RPC call for position
      mockSupabase.rpc.mockResolvedValueOnce({
        data: 0,
        error: null,
      });

      // Mock activity creation error
      mockSupabase.single.mockResolvedValueOnce({
        data: null,
        error: { message: 'Creation failed' },
      });

      await expect(
        service.addActivityToTrip('trip123', 'user123', activityData)
      ).rejects.toThrow('Failed to create activity: Creation failed');
    });

    it('should not inject affiliate links without price', async () => {
      const dataWithoutPrice = { ...activityData, price: undefined };

      // Mock trip verification
      mockSupabase.single.mockResolvedValueOnce({
        data: mockTrip,
        error: null,
      });

      // Mock RPC call for position
      mockSupabase.rpc.mockResolvedValueOnce({
        data: 0,
        error: null,
      });

      // Mock activity creation
      mockSupabase.single.mockResolvedValueOnce({
        data: mockActivity,
        error: null,
      });

      await service.addActivityToTrip('trip123', 'user123', dataWithoutPrice);

      expect(affiliateLinkInjector.injectAffiliateLinks).not.toHaveBeenCalled();
    });
  });

  describe('updateActivity', () => {
    const updateData: Partial<CreateActivityData> = {
      title: 'Updated Activity',
      price: 150,
    };

    it('should successfully update activity', async () => {
      // Mock authorization check
      mockSupabase.single.mockResolvedValueOnce({
        data: { id: 'activity123', trips: { user_id: 'user123' } },
        error: null,
      });

      // Mock current activity data for affiliate links
      mockSupabase.single.mockResolvedValueOnce({
        data: { price: 100, type: 'activity' },
        error: null,
      });

      // Mock update
      const updatedActivity = { ...mockActivity, title: 'Updated Activity', price: 150 };
      mockSupabase.single.mockResolvedValueOnce({
        data: updatedActivity,
        error: null,
      });

      // Mock validateDatabaseResponse to return the updatedActivity
      vi.mocked(validateDatabaseResponse).mockReturnValueOnce(updatedActivity);

      const result = await service.updateActivity('activity123', 'user123', updateData);

      expect(result).toEqual(updatedActivity);
      expect(mockSupabase.update).toHaveBeenCalledWith(
        expect.objectContaining({
          title: updateData.title,
          price: updateData.price,
        })
      );
    });

    it('should return null for unauthorized access', async () => {
      mockSupabase.single.mockResolvedValueOnce({
        data: null,
        error: { message: 'Not found' },
      });

      const result = await service.updateActivity('activity123', 'user123', updateData);

      expect(result).toBeNull();
    });

    it('should validate time updates', async () => {
      const invalidTimeUpdate = {
        startTime: '2024-01-01T12:00:00Z',
        endTime: '2024-01-01T10:00:00Z', // before start time
      };

      // Mock authorization check
      mockSupabase.single.mockResolvedValueOnce({
        data: { id: 'activity123', trips: { user_id: 'user123' } },
        error: null,
      });

      await expect(
        service.updateActivity('activity123', 'user123', invalidTimeUpdate)
      ).rejects.toThrow('End time must be after start time');
    });

    it('should inject affiliate links on booking URL update', async () => {
      const updateWithBookingUrl = {
        bookingUrl: 'https://new-booking.com',
      };

      // Mock authorization check
      mockSupabase.single.mockResolvedValueOnce({
        data: { id: 'activity123', trips: { user_id: 'user123' } },
        error: null,
      });

      // Mock current activity data
      mockSupabase.single.mockResolvedValueOnce({
        data: { price: 100, type: 'activity' },
        error: null,
      });

      // Mock affiliate injection
      vi.mocked(affiliateLinkInjector.injectAffiliateLinks).mockResolvedValue([
        { ...mockActivity, affiliate_url: 'https://affiliate.com/new' }
      ]);

      // Mock update
      mockSupabase.single.mockResolvedValueOnce({
        data: { ...mockActivity, booking_url: 'https://affiliate.com/new' },
        error: null,
      });

      await service.updateActivity('activity123', 'user123', updateWithBookingUrl);

      expect(affiliateLinkInjector.injectAffiliateLinks).toHaveBeenCalled();
      expect(logger.info).toHaveBeenCalledWith(
        expect.stringContaining('Injected affiliate link for activity update')
      );
    });
  });

  describe('deleteActivity', () => {
    it('should successfully delete activity', async () => {
      // Mock authorization check (first .single() call)
      mockSupabase.single.mockResolvedValueOnce({
        data: { id: 'activity123', trips: { user_id: 'user123' } },
        error: null,
      });

      // For delete operation, the chain returns { error: null } at the end
      // The delete method itself should be mocked to return the chain
      mockSupabase.delete = vi.fn(() => ({
        eq: vi.fn(() => Promise.resolve({ error: null }))
      }));

      const result = await service.deleteActivity('activity123', 'user123');

      expect(result).toBe(true);
      expect(mockSupabase.from).toHaveBeenCalledWith(TABLES.ACTIVITIES);
      expect(mockSupabase.delete).toHaveBeenCalled();
    });

    it('should return false for unauthorized access', async () => {
      mockSupabase.single.mockResolvedValueOnce({
        data: null,
        error: { message: 'Not found' },
      });

      const result = await service.deleteActivity('activity123', 'user123');

      expect(result).toBe(false);
      expect(mockSupabase.delete).not.toHaveBeenCalled();
    });

    it('should handle delete error', async () => {
      // Mock authorization check
      mockSupabase.single.mockResolvedValueOnce({
        data: { id: 'activity123', trips: { user_id: 'user123' } },
        error: null,
      });

      // Mock delete error
      mockSupabase.delete.mockReturnValueOnce({
        eq: vi.fn(() => Promise.resolve({ error: { message: 'Delete failed' } })),
      });

      await expect(
        service.deleteActivity('activity123', 'user123')
      ).rejects.toThrow('Failed to delete activity: Delete failed');
    });
  });

  describe('reorderActivities', () => {
    const orderedIds = ['activity1', 'activity2', 'activity3'];
    const mockActivities = [
      { id: 'activity1', trip_id: 'trip123', position: 0 },
      { id: 'activity2', trip_id: 'trip123', position: 1 },
      { id: 'activity3', trip_id: 'trip123', position: 2 },
    ];

    it('should successfully reorder activities', async () => {
      // Mock trip ownership check
      mockSupabase.single.mockResolvedValueOnce({
        data: { id: 'trip123', user_id: 'user123' },
        error: null,
      });

      // Create a separate chain for the activities query
      const activitiesChain = {
        eq: vi.fn(() => Promise.resolve({
          data: mockActivities,
          error: null,
        }))
      };
      
      // Override select to return our activities chain when called after from(TABLES.ACTIVITIES)
      let fromCallCount = 0;
      mockSupabase.from.mockImplementation((table) => {
        fromCallCount++;
        if (fromCallCount === 2 && table === TABLES.ACTIVITIES) {
          return {
            ...mockSupabase,
            select: vi.fn(() => activitiesChain)
          };
        }
        return mockSupabase;
      });

      // Mock batch update - upsert returns a promise
      mockSupabase.upsert.mockResolvedValueOnce({
        error: null,
      });

      // Mock fetch updated activities - order returns a promise
      mockSupabase.order.mockResolvedValueOnce({
        data: mockActivities.map((a, i) => ({ ...a, ...mockActivity, position: i })),
        error: null,
      });

      const result = await service.reorderActivities('trip123', 'user123', orderedIds);

      expect(result).toHaveLength(3);
      expect(mockSupabase.upsert).toHaveBeenCalledWith(
        expect.arrayContaining([
          { id: 'activity1', trip_id: 'trip123', position: 0 },
          { id: 'activity2', trip_id: 'trip123', position: 1 },
          { id: 'activity3', trip_id: 'trip123', position: 2 },
        ]),
        { onConflict: 'id' }
      );
    });

    it('should throw error for unauthorized trip access', async () => {
      mockSupabase.single.mockResolvedValueOnce({
        data: null,
        error: { message: 'Trip not found' },
      });

      await expect(
        service.reorderActivities('trip123', 'user123', orderedIds)
      ).rejects.toThrow('Trip not found');
    });

    it('should throw error for wrong user', async () => {
      mockSupabase.single.mockResolvedValueOnce({
        data: { id: 'trip123', user_id: 'other_user' },
        error: null,
      });

      await expect(
        service.reorderActivities('trip123', 'user123', orderedIds)
      ).rejects.toThrow('Unauthorized to modify this trip');
    });

    it('should validate activity IDs belong to trip', async () => {
      // Mock trip ownership check
      mockSupabase.single.mockResolvedValueOnce({
        data: { id: 'trip123', user_id: 'user123' },
        error: null,
      });

      // Create a separate chain for the activities query with different activities
      const activitiesChain = {
        eq: vi.fn(() => Promise.resolve({
          data: [{ id: 'different_activity', trip_id: 'trip123' }],
          error: null,
        }))
      };
      
      // Override select to return our activities chain when called after from(TABLES.ACTIVITIES)
      let fromCallCount = 0;
      mockSupabase.from.mockImplementation((table) => {
        fromCallCount++;
        if (fromCallCount === 2 && table === TABLES.ACTIVITIES) {
          return {
            ...mockSupabase,
            select: vi.fn(() => activitiesChain)
          };
        }
        return mockSupabase;
      });

      await expect(
        service.reorderActivities('trip123', 'user123', orderedIds)
      ).rejects.toThrow('Invalid activity IDs provided');
    });

    it('should handle activities fetch error', async () => {
      // Mock trip ownership check
      mockSupabase.single.mockResolvedValueOnce({
        data: { id: 'trip123', user_id: 'user123' },
        error: null,
      });

      // Create a separate chain for the activities query with error
      const activitiesChain = {
        eq: vi.fn(() => Promise.resolve({
          data: null,
          error: { message: 'Activities fetch failed' },
        }))
      };
      
      // Override select to return our activities chain when called after from(TABLES.ACTIVITIES)
      let fromCallCount = 0;
      mockSupabase.from.mockImplementation((table) => {
        fromCallCount++;
        if (fromCallCount === 2 && table === TABLES.ACTIVITIES) {
          return {
            ...mockSupabase,
            select: vi.fn(() => activitiesChain)
          };
        }
        return mockSupabase;
      });

      await expect(
        service.reorderActivities('trip123', 'user123', orderedIds)
      ).rejects.toThrow('Failed to fetch activities');
    });

    it('should handle update error', async () => {
      // Mock trip ownership check
      mockSupabase.single.mockResolvedValueOnce({
        data: { id: 'trip123', user_id: 'user123' },
        error: null,
      });

      // Create a separate chain for the activities query
      const activitiesChain = {
        eq: vi.fn(() => Promise.resolve({
          data: mockActivities,
          error: null,
        }))
      };
      
      // Override select to return our activities chain when called after from(TABLES.ACTIVITIES)
      let fromCallCount = 0;
      mockSupabase.from.mockImplementation((table) => {
        fromCallCount++;
        if (fromCallCount === 2 && table === TABLES.ACTIVITIES) {
          return {
            ...mockSupabase,
            select: vi.fn(() => activitiesChain)
          };
        }
        return mockSupabase;
      });

      // Mock update error
      mockSupabase.upsert.mockResolvedValueOnce({
        error: { message: 'Update failed' },
      });

      await expect(
        service.reorderActivities('trip123', 'user123', orderedIds)
      ).rejects.toThrow('Failed to update activity positions');
    });
  });
});