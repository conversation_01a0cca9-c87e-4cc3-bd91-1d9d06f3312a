"use client";

import { motion } from 'framer-motion';
import { Check, Star, Heart, Users } from 'lucide-react';
import { Button } from '@/components/ui/button';

const communityPlan = {
  name: "Community",
  price: { monthly: 0, annual: 0 },
  description: "Join our travel community and get access to all TravelViz features completely free",
  features: [
    "Automatic price drop alerts for flights & hotels",
    "Budget tracking within your itinerary",
    "AI finds deals on suggested places",
    "Share costs transparently with travel partners",
    "One link to share entire trip plan",
    "See who has viewed and approved plans",
    "Real-time collaborative editing",
    "Coordinate group bookings effortlessly",
    "Seamless AI conversation → bookable itinerary",
    "Easy sharing with your travel group",
    "Price tracking without manual checking",
    "Full offline access during trips"
  ],
  cta: "Join Our Community Free",
  popular: true,
  badge: "100% Free Forever"
};

export function PricingSection() {
  return (
    <section id="pricing" className="py-12 sm:py-16 lg:py-20 bg-gray-50 safe-area-inset-left safe-area-inset-right">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
          className="text-center mb-12 sm:mb-16"
        >
          <h2 className="text-2xl sm:text-3xl md:text-4xl font-bold text-gray-900 mb-3 sm:mb-4 px-4 sm:px-0">
            Join Our Travel Community
          </h2>
          <p className="text-lg sm:text-xl text-gray-600 max-w-3xl mx-auto mb-6 sm:mb-8 px-4 sm:px-0">
            Get access to all TravelViz features completely free. Transform your AI travel plans into beautiful itineraries and start saving money on your trips.
          </p>

          {/* Community Message */}
          <div className="bg-gradient-to-r from-orange-50 to-pink-50 rounded-xl p-6 mb-8 border border-orange-200">
            <div className="flex items-center justify-center space-x-2 mb-3">
              <Heart className="h-5 w-5 text-orange-500" />
              <span className="font-semibold text-gray-900">Built by Travelers, for Travelers</span>
            </div>
            <p className="text-gray-700 text-sm max-w-2xl mx-auto">
              No subscriptions, no hidden fees, no artificial limits. We believe travel planning should be accessible to everyone. 
              Join our community and help shape the future of AI-powered travel.
            </p>
          </div>
        </motion.div>

        {/* Single Community Plan */}
        <div className="max-w-2xl mx-auto">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6 }}
            className="relative mobile-card bg-white rounded-2xl shadow-sm hover:shadow-xl transition-all duration-300 ring-2 ring-orange-500"
          >
            {communityPlan.badge && (
              <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                <span className="inline-flex items-center px-3 sm:px-4 py-1 rounded-full text-xs sm:text-sm font-medium bg-orange-500 text-white">
                  <Star className="h-3 w-3 sm:h-4 sm:w-4 mr-1" />
                  {communityPlan.badge}
                </span>
              </div>
            )}

            <div className="p-6 sm:p-8">
              <div className="text-center mb-6 sm:mb-8">
                <h3 className="text-xl sm:text-2xl font-bold text-gray-900 mb-2">{communityPlan.name}</h3>
                <p className="text-gray-600 mb-4 text-sm sm:text-base">{communityPlan.description}</p>
                
                <div className="mb-4">
                  <span className="text-3xl sm:text-4xl font-bold text-gray-900">Free</span>
                  <span className="text-gray-600 ml-2 text-sm sm:text-base">Forever</span>
                </div>
              </div>

              <div className="mb-6 sm:mb-8">
                <Button size="lg" className="w-full bg-orange-500 hover:bg-orange-600 text-white" asChild>
                  <a href="/signup">Get Started Free</a>
                </Button>
              </div>

              <div className="space-y-4">
                <h4 className="font-semibold text-gray-900 text-sm sm:text-base">What's included:</h4>
                <ul className="space-y-2 sm:space-y-3">
                  {communityPlan.features.map((feature, featureIndex) => (
                    <li key={featureIndex} className="flex items-start">
                      <Check className="h-4 w-4 sm:h-5 sm:w-5 text-green-500 mr-2 sm:mr-3 mt-0.5 flex-shrink-0" />
                      <span className="text-gray-600 text-sm sm:text-base">{feature}</span>
                    </li>
                  ))}
                </ul>
              </div>
            </div>
          </motion.div>
        </div>

        {/* Community Promise */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ delay: 0.8, duration: 0.6 }}
          className="mt-16 sm:mt-20 text-center"
        >
          <div className="bg-gradient-to-r from-orange-500 to-pink-500 rounded-2xl p-8 text-white">
            <h3 className="text-xl sm:text-2xl font-bold mb-4 px-4 sm:px-0">
              Join Us in Building Something Meaningful
            </h3>
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 mb-6 sm:mb-8">
              <div className="text-center">
                <div className="text-lg sm:text-xl font-bold">Authentic</div>
                <div className="text-xs sm:text-sm text-white/80">No fake promises</div>
              </div>
              <div className="text-center">
                <div className="text-lg sm:text-xl font-bold">Community</div>
                <div className="text-xs sm:text-sm text-white/80">Driven development</div>
              </div>
              <div className="text-center">
                <div className="text-lg sm:text-xl font-bold">Transparent</div>
                <div className="text-xs sm:text-sm text-white/80">Open process</div>
              </div>
              <div className="text-center">
                <div className="text-lg sm:text-xl font-bold">Growing</div>
                <div className="text-xs sm:text-sm text-white/80">Together daily</div>
              </div>
            </div>
            
            <p className="text-base sm:text-lg md:text-xl mb-6 sm:mb-8 text-white/90 px-4 sm:px-0">
              We're just starting our journey, but we're committed to growing alongside our community of travelers. 
              Join us in building something meaningful together.
            </p>
            
            <div className="max-w-md mx-auto">
              <Button size="lg" className="w-full bg-white text-orange-600 hover:bg-white/90" asChild>
                <a href="/signup">Join the Community</a>
              </Button>
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  );
}