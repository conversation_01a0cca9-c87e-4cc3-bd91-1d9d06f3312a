"use client";

import { useState } from 'react';
import { motion } from 'framer-motion';
import { TripCard } from './TripCard';

interface Trip {
  id: string;
  title: string;
  destination: string;
  startDate: string;
  endDate: string;
  status: 'upcoming' | 'in-progress' | 'completed';
  imageUrl: string;
  viewCount: number;
  activities: number;
}

interface TripGridProps {
  trips: Trip[];
}

export function TripGrid({ trips }: TripGridProps) {
  const [selectedTrip, setSelectedTrip] = useState<string | null>(null);

  return (
    <div className="space-y-6">
      {/* Grid Header */}
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold text-gray-900">Your Trips</h2>
        <div className="flex items-center space-x-2 text-sm text-gray-600">
          <span>{trips.length} trip{trips.length !== 1 ? 's' : ''}</span>
        </div>
      </div>

      {/* Trip Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6">
        {trips.map((trip, index) => (
          <motion.div
            key={trip.id}
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.1, duration: 0.4 }}
          >
            <TripCard
              trip={trip}
              isSelected={selectedTrip === trip.id}
              onSelect={() => setSelectedTrip(trip.id === selectedTrip ? null : trip.id)}
            />
          </motion.div>
        ))}
      </div>
    </div>
  );
}