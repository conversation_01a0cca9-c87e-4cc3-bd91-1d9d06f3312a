import { vi } from 'vitest';
import { config } from 'dotenv';
import { resolve } from 'path';
import { execSync } from 'child_process';
import { existsSync } from 'fs';

// Load environment variables from .env.test for test-specific credentials
const testEnvPath = resolve(__dirname, '../.env.test');
const localEnvPath = resolve(__dirname, '../.env.local');

console.log('Loading test environment from:', testEnvPath);
config({ path: testEnvPath });

// Fallback to .env.local if .env.test doesn't exist
if (!existsSync(testEnvPath)) {
  console.log('No .env.test found, falling back to:', localEnvPath);
  config({ path: localEnvPath });
}

// Debug Redis env vars
console.log('Redis URL loaded:', process.env.UPSTASH_REDIS_REST_URL ? 'Yes' : 'No');
console.log('Redis Token loaded:', process.env.UPSTASH_REDIS_REST_TOKEN ? 'Yes' : 'No');

// Pre-test validation: Check for common issues
console.log('\n🔍 Pre-test validation:');

// 1. Validate imports before tests run
try {
  execSync('pnpm validate:imports', {
    cwd: resolve(__dirname, '..'),
    stdio: 'pipe'
  });
  console.log('✅ Import validation passed');
} catch (error) {
  console.error('❌ Import validation failed:', error.message);
  console.error('   Run "pnpm validate:imports" to see details');
  process.exit(1);
}

// 2. Ensure shared package is built
const sharedDistPath = resolve(__dirname, '../../shared/dist');
if (!existsSync(sharedDistPath)) {
  console.log('📦 Building @travelviz/shared package...');
  try {
    execSync('pnpm build', {
      cwd: resolve(__dirname, '../../shared'),
      stdio: 'inherit'
    });
    console.log('✅ Shared package built successfully');
  } catch (error) {
    console.error('❌ Failed to build shared package:', error);
    process.exit(1);
  }
} else {
  console.log('✅ Shared package dist found');
}

// 3. Verify runtime-accurate module resolution
console.log('✅ Using runtime-accurate module resolution (no package aliases)');
console.log('');

// Set test environment
process.env.NODE_ENV = 'test';

// Check if required test credentials are available
if (!process.env.UPSTASH_REDIS_REST_URL || !process.env.UPSTASH_REDIS_REST_TOKEN) {
  console.warn('\n⚠️  WARNING: Redis credentials not found!');
  console.warn('   Tests will fail without real service credentials.');
  console.warn('   Copy .env.test.example to .env.test and add your test credentials.\n');
}

// Enhanced error handler for module resolution
const originalConsoleError = console.error;
console.error = (...args) => {
  const errorStr = args.join(' ');
  
  // Intercept module resolution errors with helpful guidance
  if (errorStr.includes('Cannot find module') || errorStr.includes('Cannot find package')) {
    originalConsoleError('\n🚨 MODULE RESOLUTION ERROR DETECTED 🚨');
    originalConsoleError(...args);
    originalConsoleError('\n📋 Common causes:');
    originalConsoleError('1. Using subpath imports: @travelviz/shared/utils/something');
    originalConsoleError('2. Missing build artifacts in shared/dist/');
    originalConsoleError('3. Incorrect import syntax');
    originalConsoleError('\n✅ Solution:');
    originalConsoleError('- Use: import { something } from "@travelviz/shared"');
    originalConsoleError('- Run: pnpm build (in root) to build shared package');
    originalConsoleError('- Check: packages/shared/src/index.ts exports your module\n');
  } else {
    originalConsoleError(...args);
  }
};

// Global test utilities
global.testUtils = {
  // Helper to debug module resolution
  debugImport: async (modulePath: string) => {
    try {
      // Use require for now as dynamic import causes esbuild issues
      const module = require(modulePath);
      console.log(`✅ Successfully imported: ${modulePath}`);
      return module;
    } catch (error) {
      console.error(`❌ Failed to import: ${modulePath}`);
      console.error(`   Error: ${error.message}`);
      throw error;
    }
  },
};

// Don't mock fetch globally - it breaks Upstash Redis HTTP requests
// Individual tests that need fetch mocked should do it themselves

// Note: AIRouter mocking removed from global setup to avoid conflicts
// Individual tests that need AIRouter mocking should add it themselves