# MVP Days 10-11: Basic Monetization

**Date**: [Execute Date]  
**Goal**: Implement simple monetization to validate willingness to pay  
**Duration**: 16 hours (2 days)  
**Critical Path**: YES - Revenue validates the business model

## Context & Monetization Strategy

### Revenue Streams (MVP)

1. **Pro Subscription** - $9/month via Stripe Payment Links
2. **Affiliate Commissions** - Travelpayouts integration
3. **Future**: Team plans, API access, white-label

### Day 1 Focus: Pro Features

- Unlimited imports (vs 3/month free)
- Priority AI processing
- Advanced customization
- Export to PDF/Calendar
- Remove TravelViz branding

### Day 2 Focus: Affiliate Integration

- Hotel booking links
- Flight search integration
- Activity recommendations
- Transparent disclosure

### Revenue-Critical Architecture

#### 1. Payment Architecture

- **PCI Compliance**: Use Stripe Checkout (no card data touches our servers)
- **Webhook Security**:
  - Signature validation (HMAC)
  - Idempotency keys for replay protection
  - Event deduplication (24h window)
- **Failed Payment Recovery**:
  - 3 retry attempts over 7 days
  - Email notifications at each failure
  - Grace period before downgrade
- **Payment Methods**: Card, Apple Pay, Google Pay, SEPA (EU)

#### 2. Subscription Logic

- **Proration Handling**:
  ```typescript
  // Upgrade mid-cycle
  const unusedDays = daysUntilEnd(currentPeriod);
  const credit = (monthlyPrice / 30) * unusedDays;
  const newCharge = yearlyPrice - credit;
  ```
- **Trial Management**:
  - 7-day free trial for new users
  - No trial for previously subscribed
  - Auto-convert unless cancelled
- **Cancellation Flow**:
  - Immediate cancellation (retain access until period end)
  - Pause subscription option
  - Win-back offer at 50% off

#### 3. Revenue Recognition

- **Accounting Events**:
  - Revenue recognized daily (1/30th monthly)
  - Refunds create negative revenue entries
  - Affiliate commissions on 30-day delay
- **Tax Handling**:
  - Stripe Tax for automatic calculation
  - VAT for EU customers
  - Invoice generation with tax breakdown

#### 4. Fraud Prevention

- **Card Testing Detection**:
  - Multiple failed attempts from same IP
  - Rapid subscription create/cancel patterns
  - Small amount authorizations
- **Unusual Patterns**:
  - > 5 subscriptions from same card
  - Geographical inconsistencies
  - Velocity checks (signups/hour)
- **Actions**:
  - Auto-block suspicious cards
  - Manual review queue
  - Stripe Radar integration

#### 5. Billing Engine

- **Invoice Generation**:
  - PDF with company branding
  - Line items with descriptions
  - Tax calculations shown
  - Payment history included
- **Dunning Management**:
  - Pre-dunning email 3 days before
  - Failed payment email immediately
  - 3 retry emails over 7 days
  - Final cancellation notice
- **Currency Support**:
  - USD (default)
  - EUR, GBP with auto-conversion
  - Show local currency at checkout

## Day 10 Morning: Stripe Payment Setup (4 hours)

### Task 1: Pro Plan Database Schema (30 min)

**File**: `packages/hub/src/migrations/013_pro_subscriptions.sql`

```sql
BEGIN;

-- User subscription status
CREATE TYPE subscription_status AS ENUM ('free', 'pro', 'pro_trial', 'cancelled');
CREATE TYPE billing_period AS ENUM ('monthly', 'yearly');

-- Add subscription fields to users
ALTER TABLE auth.users
  ADD COLUMN subscription_status subscription_status DEFAULT 'free',
  ADD COLUMN subscription_started_at TIMESTAMP WITH TIME ZONE,
  ADD COLUMN subscription_ends_at TIMESTAMP WITH TIME ZONE,
  ADD COLUMN stripe_customer_id TEXT,
  ADD COLUMN stripe_subscription_id TEXT,
  ADD COLUMN billing_period billing_period;

-- Usage tracking
CREATE TABLE user_usage (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  month DATE NOT NULL,
  imports_count INTEGER DEFAULT 0,
  shares_count INTEGER DEFAULT 0,
  exports_count INTEGER DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(user_id, month)
);

-- Payment history
CREATE TABLE payment_history (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  stripe_payment_intent_id TEXT UNIQUE,
  amount INTEGER NOT NULL, -- in cents
  currency TEXT DEFAULT 'usd',
  status TEXT NOT NULL,
  description TEXT,
  billing_period billing_period,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Subscription events for analytics
CREATE TABLE subscription_events (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  event_type TEXT NOT NULL, -- 'started', 'renewed', 'cancelled', 'failed'
  previous_status subscription_status,
  new_status subscription_status,
  metadata JSONB,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes
CREATE INDEX idx_user_subscription_status ON auth.users(subscription_status);
CREATE INDEX idx_user_usage_month ON user_usage(user_id, month);
CREATE INDEX idx_payment_history_user ON payment_history(user_id, created_at DESC);

-- RLS Policies
ALTER TABLE user_usage ENABLE ROW LEVEL SECURITY;
ALTER TABLE payment_history ENABLE ROW LEVEL SECURITY;
ALTER TABLE subscription_events ENABLE ROW LEVEL SECURITY;

-- Users can only see their own data
CREATE POLICY "Users can view own usage" ON user_usage
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can view own payments" ON payment_history
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can view own subscription events" ON subscription_events
  FOR SELECT USING (auth.uid() = user_id);

COMMIT;
```

### Task 2: Stripe Webhook Handler (1.5 hours)

**File**: `packages/hub/src/routes/webhooks.routes.ts`

```typescript
import { Router } from 'express';
import Stripe from 'stripe';
import { supabase } from '../config/supabase';
import { createSuccessResponse, createErrorResponse } from '@travelviz/shared';

const router = Router();
const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
  apiVersion: '2023-10-16',
});

// Stripe webhook endpoint with enhanced security
router.post('/stripe', express.raw({ type: 'application/json' }), async (req, res) => {
  const sig = req.headers['stripe-signature'] as string;
  const endpointSecret = process.env.STRIPE_WEBHOOK_SECRET!;

  // Webhook timestamp validation (prevent replay attacks)
  const timestamp = req.headers['stripe-signature']?.split('t=')[1]?.split(',')[0];
  if (timestamp && Date.now() - parseInt(timestamp) * 1000 > 300000) {
    // 5 min window
    return res.status(400).send('Webhook timestamp too old');
  }

  let event: Stripe.Event;

  try {
    event = stripe.webhooks.constructEvent(req.body, sig, endpointSecret);
  } catch (err: any) {
    console.error('Webhook signature verification failed:', err.message);
    // Log potential attack
    await logSecurityEvent('webhook_signature_failed', {
      ip: req.ip,
      userAgent: req.headers['user-agent'],
      signature: sig?.substring(0, 20) + '...',
    });
    return res.status(400).send(`Webhook Error: ${err.message}`);
  }

  // Idempotency check
  const eventId = event.id;
  const processed = await redis.get(`webhook:${eventId}`);
  if (processed) {
    console.log(`Duplicate webhook ${eventId}, skipping`);
    return res.json({ received: true, duplicate: true });
  }

  // Mark as processing (24h TTL)
  await redis.setex(`webhook:${eventId}`, 86400, 'processing');

  // Handle the event
  switch (event.type) {
    case 'checkout.session.completed':
      await handleCheckoutCompleted(event.data.object as Stripe.Checkout.Session);
      break;

    case 'customer.subscription.created':
    case 'customer.subscription.updated':
      await handleSubscriptionUpdate(event.data.object as Stripe.Subscription);
      break;

    case 'customer.subscription.deleted':
      await handleSubscriptionCancelled(event.data.object as Stripe.Subscription);
      break;

    case 'invoice.payment_succeeded':
      await handlePaymentSucceeded(event.data.object as Stripe.Invoice);
      break;

    case 'invoice.payment_failed':
      await handlePaymentFailed(event.data.object as Stripe.Invoice);
      break;

    default:
      console.log(`Unhandled event type ${event.type}`);
  }

  res.json({ received: true });
});

async function handleCheckoutCompleted(session: Stripe.Checkout.Session) {
  console.log('Checkout completed:', session.id);

  const { userId, billingPeriod } = session.metadata || {};
  if (!userId) {
    console.error('No userId in checkout session metadata');
    // Alert ops team - this should never happen
    await sendOpsAlert('Missing userId in Stripe checkout', { sessionId: session.id });
    return;
  }

  // Start database transaction
  const client = await db.getClient();
  try {
    await client.query('BEGIN');

    // Update user to Pro (with transaction)
    const { error: userError } = await client.query(
      `UPDATE auth.users 
       SET subscription_status = $1,
           subscription_started_at = $2,
           subscription_ends_at = $3,
           stripe_customer_id = $4,
           stripe_subscription_id = $5,
           billing_period = $6,
           updated_at = NOW()
       WHERE id = $7
       RETURNING *`,
      [
        'pro',
        new Date().toISOString(),
        calculateEndDate(billingPeriod as any),
        session.customer,
        session.subscription,
        billingPeriod || 'monthly',
        userId,
      ]
    );

    if (userError) {
      await client.query('ROLLBACK');
      console.error('Failed to update user subscription:', userError);
      throw userError;
    }

    // Record subscription event
    await client.query(
      `INSERT INTO subscription_events (user_id, event_type, previous_status, new_status, metadata)
       VALUES ($1, $2, $3, $4, $5)`,
      [
        userId,
        'started',
        'free',
        'pro',
        {
          session_id: session.id,
          amount: session.amount_total,
          billing_period: billingPeriod,
          currency: session.currency,
          payment_method_type: session.payment_method_types?.[0],
        },
      ]
    );

    // Record payment for revenue tracking
    await client.query(
      `INSERT INTO payment_history (user_id, stripe_payment_intent_id, amount, currency, status, description, billing_period)
       VALUES ($1, $2, $3, $4, $5, $6, $7)`,
      [
        userId,
        session.payment_intent,
        session.amount_total,
        session.currency,
        'succeeded',
        `Pro subscription - ${billingPeriod}`,
        billingPeriod,
      ]
    );

    await client.query('COMMIT');

    // Post-transaction actions
    await Promise.all([
      sendProWelcomeEmail(userId),
      trackRevenueEvent('new_subscription', {
        userId,
        amount: session.amount_total / 100, // Convert cents to dollars
        currency: session.currency,
        billing_period: billingPeriod,
        ltv_estimate:
          billingPeriod === 'yearly'
            ? session.amount_total / 100
            : (session.amount_total / 100) * 10,
      }),
      updateCustomerLTV(session.customer as string),
    ]);
  } catch (error) {
    await client.query('ROLLBACK');
    console.error('Checkout processing failed:', error);
    // Notify ops and potentially refund
    await handleFailedSubscriptionActivation(session, error);
  } finally {
    client.release();
  }
}

async function handleSubscriptionUpdate(subscription: Stripe.Subscription) {
  const userId = subscription.metadata.userId;
  if (!userId) return;

  const status = mapStripeStatus(subscription.status);

  await supabase
    .from('users')
    .update({
      subscription_status: status,
      subscription_ends_at: new Date(subscription.current_period_end * 1000).toISOString(),
    })
    .eq('stripe_subscription_id', subscription.id);
}

async function handleSubscriptionCancelled(subscription: Stripe.Subscription) {
  const userId = subscription.metadata.userId;
  if (!userId) return;

  await supabase
    .from('users')
    .update({
      subscription_status: 'cancelled',
      subscription_ends_at: new Date(subscription.current_period_end * 1000).toISOString(),
    })
    .eq('stripe_subscription_id', subscription.id);

  // Record cancellation
  await supabase.from('subscription_events').insert({
    user_id: userId,
    event_type: 'cancelled',
    previous_status: 'pro',
    new_status: 'cancelled',
    metadata: {
      reason: subscription.cancellation_details?.reason,
      feedback: subscription.cancellation_details?.feedback,
    },
  });

  // Send cancellation email
  await sendCancellationEmail(userId);
}

async function handlePaymentSucceeded(invoice: Stripe.Invoice) {
  const subscription = invoice.subscription as string;
  const customerId = invoice.customer as string;

  // Record payment
  const { data: user } = await supabase
    .from('users')
    .select('id')
    .eq('stripe_customer_id', customerId)
    .single();

  if (user) {
    await supabase.from('payment_history').insert({
      user_id: user.id,
      stripe_payment_intent_id: invoice.payment_intent as string,
      amount: invoice.amount_paid,
      currency: invoice.currency,
      status: 'succeeded',
      description: `Pro subscription - ${invoice.period_start ? new Date(invoice.period_start * 1000).toLocaleDateString() : ''}`,
    });
  }
}

async function handlePaymentFailed(invoice: Stripe.Invoice) {
  const customerId = invoice.customer as string;

  // Update user status
  await supabase
    .from('users')
    .update({ subscription_status: 'free' })
    .eq('stripe_customer_id', customerId);

  // Send payment failed email
  const { data: user } = await supabase
    .from('users')
    .select('id, email')
    .eq('stripe_customer_id', customerId)
    .single();

  if (user) {
    await sendPaymentFailedEmail(user.email);
  }
}

function mapStripeStatus(stripeStatus: string): subscription_status {
  switch (stripeStatus) {
    case 'active':
      return 'pro';
    case 'trialing':
      return 'pro_trial';
    case 'canceled':
    case 'unpaid':
      return 'cancelled';
    default:
      return 'free';
  }
}

function calculateEndDate(billingPeriod: 'monthly' | 'yearly'): string {
  const date = new Date();
  if (billingPeriod === 'yearly') {
    date.setFullYear(date.getFullYear() + 1);
  } else {
    date.setMonth(date.getMonth() + 1);
  }
  return date.toISOString();
}

export default router;
```

### Task 3: Pro Feature Gates (1 hour)

**File**: `packages/web/src/hooks/useProFeatures.ts`

```typescript
import { useAuth } from '@/contexts/AuthContext';
import { useState, useEffect } from 'react';
import { toast } from '@/components/ui/use-toast';

interface ProLimits {
  importsPerMonth: number;
  exportsPerMonth: number;
  customBranding: boolean;
  priorityProcessing: boolean;
  advancedCustomization: boolean;
  teamCollaboration: boolean;
}

const FREE_LIMITS: ProLimits = {
  importsPerMonth: 3,
  exportsPerMonth: 1,
  customBranding: false,
  priorityProcessing: false,
  advancedCustomization: false,
  teamCollaboration: false
};

const PRO_LIMITS: ProLimits = {
  importsPerMonth: -1, // unlimited
  exportsPerMonth: -1,
  customBranding: true,
  priorityProcessing: true,
  advancedCustomization: true,
  teamCollaboration: false // Future feature
};

export function useProFeatures() {
  const { user } = useAuth();
  const [usage, setUsage] = useState({
    importsThisMonth: 0,
    exportsThisMonth: 0
  });
  const [loading, setLoading] = useState(true);

  const isPro = user?.subscription_status === 'pro' || user?.subscription_status === 'pro_trial';
  const limits = isPro ? PRO_LIMITS : FREE_LIMITS;

  useEffect(() => {
    if (user) {
      fetchUsage();
    }
  }, [user]);

  const fetchUsage = async () => {
    try {
      const response = await fetch('/api/v1/user/usage', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });
      const data = await response.json();
      setUsage(data);
    } catch (error) {
      console.error('Failed to fetch usage:', error);
    } finally {
      setLoading(false);
    }
  };

  const checkLimit = (feature: keyof ProLimits): boolean => {
    if (isPro) return true;

    switch (feature) {
      case 'importsPerMonth':
        return usage.importsThisMonth < limits.importsPerMonth;
      case 'exportsPerMonth':
        return usage.exportsThisMonth < limits.exportsPerMonth;
      default:
        return limits[feature] as boolean;
    }
  };

  const requirePro = (feature: string, action?: () => void) => {
    if (!isPro) {
      toast({
        title: '🌟 Pro Feature',
        description: `${feature} is available with TravelViz Pro`,
        action: (
          <Button
            size="sm"
            onClick={() => window.location.href = '/pricing'}
          >
            Upgrade Now
          </Button>
        )
      });
      return false;
    }

    action?.();
    return true;
  };

  const showUpgradePrompt = (
    title: string,
    description: string,
    benefits?: string[]
  ) => {
    return (
      <UpgradePrompt
        title={title}
        description={description}
        benefits={benefits}
        onUpgrade={() => window.location.href = '/pricing'}
      />
    );
  };

  return {
    isPro,
    limits,
    usage,
    loading,
    checkLimit,
    requirePro,
    showUpgradePrompt,
    remainingImports: limits.importsPerMonth === -1
      ? 'Unlimited'
      : Math.max(0, limits.importsPerMonth - usage.importsThisMonth)
  };
}
```

### Task 4: Pricing Page (1 hour)

**File**: `packages/web/src/app/pricing/page.tsx`

```typescript
'use client';

import { useState } from 'react';
import { motion } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { Switch } from '@/components/ui/switch';
import { Check, Sparkles, Zap, X } from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { cn } from '@/lib/utils';

const STRIPE_MONTHLY_LINK = process.env.NEXT_PUBLIC_STRIPE_MONTHLY_LINK!;
const STRIPE_YEARLY_LINK = process.env.NEXT_PUBLIC_STRIPE_YEARLY_LINK!;

export default function PricingPage() {
  const { user } = useAuth();
  const [billingPeriod, setBillingPeriod] = useState<'monthly' | 'yearly'>('monthly');

  const handleUpgrade = async (plan: 'free' | 'pro') => {
    if (plan === 'free') return;

    // Track pricing page conversion
    await fetch('/api/v1/analytics/track', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        event: 'upgrade_clicked',
        properties: {
          source: 'pricing_page',
          plan,
          billingPeriod,
          price: billingPeriod === 'yearly' ? 84 : 9
        }
      })
    });

    if (!user) {
      // Save pricing preference
      sessionStorage.setItem('preferredBilling', billingPeriod);
      window.location.href = '/auth/signup?intent=upgrade&billing=' + billingPeriod;
      return;
    }

    // Create checkout session with metadata
    const response = await fetch('/api/v1/checkout/create', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${localStorage.getItem('token')}`
      },
      body: JSON.stringify({
        billingPeriod,
        successUrl: window.location.origin + '/dashboard?upgraded=true',
        cancelUrl: window.location.origin + '/pricing?cancelled=true'
      })
    });

    if (response.ok) {
      const { checkoutUrl } = await response.json();
      window.location.href = checkoutUrl;
    } else {
      // Fallback to payment link
      const checkoutUrl = billingPeriod === 'yearly'
        ? STRIPE_YEARLY_LINK
        : STRIPE_MONTHLY_LINK;
      window.location.href = `${checkoutUrl}?client_reference_id=${user.id}&prefilled_email=${user.email}`;
    }
  };

  const plans = [
    {
      name: 'Free',
      price: 0,
      period: '',
      description: 'Perfect for trying out TravelViz',
      features: [
        '3 AI imports per month',
        'Basic timeline & map views',
        'Public sharing',
        'TravelViz branding',
        'Community support'
      ],
      limitations: [
        'Limited to 3 imports/month',
        'No PDF exports',
        'No priority processing',
        'No custom branding'
      ],
      cta: 'Current Plan',
      variant: 'outline' as const,
      popular: false
    },
    {
      name: 'Pro',
      price: billingPeriod === 'yearly' ? 7 : 9,
      period: '/month',
      description: 'For serious travel planners',
      features: [
        'Unlimited AI imports',
        'Priority AI processing',
        'Export to PDF & Calendar',
        'Remove TravelViz branding',
        'Advanced customization',
        'Priority support',
        'Early access to new features'
      ],
      limitations: [],
      cta: user?.subscription_status === 'pro' ? 'Current Plan' : 'Upgrade to Pro',
      variant: 'default' as const,
      popular: true
    }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50">
      <div className="container mx-auto px-4 py-16">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center mb-12"
        >
          <h1 className="text-4xl md:text-5xl font-bold mb-4">
            Simple, Transparent Pricing
          </h1>
          <p className="text-xl text-gray-600 mb-8">
            Start free, upgrade when you need more
          </p>

          {/* Billing Toggle */}
          <div className="flex items-center justify-center gap-4">
            <span className={cn(
              "text-sm font-medium",
              billingPeriod === 'monthly' ? 'text-gray-900' : 'text-gray-500'
            )}>
              Monthly
            </span>
            <Switch
              checked={billingPeriod === 'yearly'}
              onCheckedChange={(checked) => setBillingPeriod(checked ? 'yearly' : 'monthly')}
            />
            <span className={cn(
              "text-sm font-medium",
              billingPeriod === 'yearly' ? 'text-gray-900' : 'text-gray-500'
            )}>
              Yearly
              <span className="ml-1 text-green-600">Save 22%</span>
            </span>
          </div>
        </motion.div>

        {/* Plans */}
        <div className="grid md:grid-cols-2 gap-8 max-w-5xl mx-auto">
          {plans.map((plan, index) => (
            <motion.div
              key={plan.name}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
              className={cn(
                "relative bg-white rounded-2xl shadow-lg p-8",
                plan.popular && "ring-2 ring-blue-600"
              )}
            >
              {plan.popular && (
                <div className="absolute -top-4 left-1/2 -translate-x-1/2">
                  <div className="bg-gradient-to-r from-blue-600 to-purple-600 text-white px-4 py-1 rounded-full text-sm font-medium">
                    Most Popular
                  </div>
                </div>
              )}

              <div className="mb-8">
                <h3 className="text-2xl font-bold mb-2">{plan.name}</h3>
                <div className="flex items-baseline mb-4">
                  <span className="text-4xl font-bold">${plan.price}</span>
                  <span className="text-gray-600 ml-1">{plan.period}</span>
                  {plan.name === 'Pro' && billingPeriod === 'yearly' && (
                    <span className="ml-2 text-sm text-green-600">
                      (was $9/mo)
                    </span>
                  )}
                </div>
                <p className="text-gray-600">{plan.description}</p>
              </div>

              {/* Features */}
              <div className="space-y-3 mb-8">
                {plan.features.map((feature) => (
                  <div key={feature} className="flex items-start gap-3">
                    <Check className="w-5 h-5 text-green-600 flex-shrink-0 mt-0.5" />
                    <span className="text-sm">{feature}</span>
                  </div>
                ))}
                {plan.limitations.map((limitation) => (
                  <div key={limitation} className="flex items-start gap-3 opacity-60">
                    <X className="w-5 h-5 text-gray-400 flex-shrink-0 mt-0.5" />
                    <span className="text-sm">{limitation}</span>
                  </div>
                ))}
              </div>

              {/* CTA */}
              <Button
                variant={plan.variant}
                className="w-full"
                size="lg"
                onClick={() => handleUpgrade(plan.name.toLowerCase() as any)}
                disabled={
                  (plan.name === 'Free' && !user?.subscription_status) ||
                  (plan.name === 'Pro' && user?.subscription_status === 'pro')
                }
              >
                {plan.cta}
                {plan.name === 'Pro' && <Zap className="ml-2 h-4 w-4" />}
              </Button>
            </motion.div>
          ))}
        </div>

        {/* FAQ */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.3 }}
          className="max-w-3xl mx-auto mt-16"
        >
          <h2 className="text-2xl font-bold text-center mb-8">
            Frequently Asked Questions
          </h2>

          <div className="space-y-6">
            <FAQItem
              question="Can I cancel anytime?"
              answer="Yes! You can cancel your Pro subscription anytime. You'll keep Pro features until the end of your billing period."
            />
            <FAQItem
              question="What happens to my trips if I downgrade?"
              answer="All your trips remain accessible. You just won't be able to import new trips beyond the free limit or access Pro features."
            />
            <FAQItem
              question="Do you offer refunds?"
              answer="We offer a 7-day money-back guarantee. If you're not satisfied, contact support for a full refund."
            />
            <FAQItem
              question="Is my payment information secure?"
              answer="Yes! We use Stripe for payment processing. We never store your credit card information."
            />
          </div>
        </motion.div>

        {/* Social Proof */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.4 }}
          className="text-center mt-16"
        >
          <p className="text-sm text-gray-600">
            Trusted by <span className="font-semibold">2,847</span> travel planners
          </p>
          <div className="flex items-center justify-center gap-1 mt-2">
            {[...Array(5)].map((_, i) => (
              <Sparkles key={i} className="w-5 h-5 text-yellow-500 fill-current" />
            ))}
          </div>
        </motion.div>
      </div>
    </div>
  );
}

function FAQItem({ question, answer }: { question: string; answer: string }) {
  const [isOpen, setIsOpen] = useState(false);

  return (
    <div className="border-b pb-6">
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="flex items-center justify-between w-full text-left"
      >
        <span className="font-medium">{question}</span>
        <motion.div
          animate={{ rotate: isOpen ? 180 : 0 }}
          transition={{ duration: 0.2 }}
        >
          <ChevronDown className="w-5 h-5 text-gray-600" />
        </motion.div>
      </button>
      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ height: 0, opacity: 0 }}
            animate={{ height: 'auto', opacity: 1 }}
            exit={{ height: 0, opacity: 0 }}
            transition={{ duration: 0.2 }}
            className="overflow-hidden"
          >
            <p className="text-gray-600 mt-2">{answer}</p>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
}
```

## Day 10 Afternoon: Usage Tracking & Limits (4 hours)

### Task 5: Usage Middleware & Tracking (2 hours)

**File**: `packages/hub/src/middleware/usage.middleware.ts`

```typescript
import { Request, Response, NextFunction } from 'express';
import { supabase } from '../config/supabase';
import { createErrorResponse } from '@travelviz/shared';

interface UsageLimits {
  imports: number;
  exports: number;
  shares: number;
}

const FREE_LIMITS: UsageLimits = {
  imports: 3,
  exports: 1,
  shares: -1, // unlimited
};

const PRO_LIMITS: UsageLimits = {
  imports: -1, // unlimited
  exports: -1,
  shares: -1,
};

export async function checkUsageLimit(limitType: keyof UsageLimits) {
  return async (req: AuthRequest, res: Response, next: NextFunction) => {
    const userId = req.user!.id;

    try {
      // Cache subscription status for 5 minutes
      const cacheKey = `sub:${userId}`;
      let subscriptionStatus = await redis.get(cacheKey);

      if (!subscriptionStatus) {
        // Get user subscription status
        const { data: user, error: userError } = await supabase
          .from('users')
          .select('subscription_status, subscription_ends_at')
          .eq('id', userId)
          .single();

        if (user) {
          // Check if subscription expired
          if (
            user.subscription_status === 'pro' &&
            new Date(user.subscription_ends_at) < new Date()
          ) {
            // Downgrade expired subscription
            await supabase.from('users').update({ subscription_status: 'free' }).eq('id', userId);
            subscriptionStatus = 'free';
          } else {
            subscriptionStatus = user.subscription_status;
          }

          await redis.setex(cacheKey, 300, subscriptionStatus);
        }
      }

      if (userError || !user) {
        return res.status(500).json(createErrorResponse('Failed to check subscription status'));
      }

      const isPro = user.subscription_status === 'pro' || user.subscription_status === 'pro_trial';
      const limits = isPro ? PRO_LIMITS : FREE_LIMITS;

      // Check if unlimited
      if (limits[limitType] === -1) {
        return next();
      }

      // Get current month usage
      const startOfMonth = new Date();
      startOfMonth.setDate(1);
      startOfMonth.setHours(0, 0, 0, 0);

      const { data: usage, error: usageError } = await supabase
        .from('user_usage')
        .select('*')
        .eq('user_id', userId)
        .eq('month', startOfMonth.toISOString().slice(0, 7))
        .single();

      const currentUsage = usage?.[`${limitType}_count`] || 0;

      if (currentUsage >= limits[limitType]) {
        // Track limit hit for conversion optimization
        await trackEvent('usage_limit_hit', {
          userId,
          limitType,
          currentUsage,
          limit: limits[limitType],
          dayOfMonth: new Date().getDate(),
        });

        return res.status(403).json(
          createErrorResponse(
            `Monthly ${limitType} limit reached. Upgrade to Pro for unlimited access.`,
            {
              limit: limits[limitType],
              used: currentUsage,
              upgradeUrl: '/pricing',
              promoCode: await getPersonalizedPromoCode(userId),
            }
          )
        );
      }

      // Attach usage info to request
      req.usage = {
        limit: limits[limitType],
        used: currentUsage,
        remaining: limits[limitType] - currentUsage,
      };

      next();
    } catch (error) {
      console.error('Usage check error:', error);
      return res.status(500).json(createErrorResponse('Failed to check usage limits'));
    }
  };
}

export async function incrementUsage(userId: string, usageType: 'imports' | 'exports' | 'shares') {
  const startOfMonth = new Date();
  startOfMonth.setDate(1);
  startOfMonth.setHours(0, 0, 0, 0);
  const monthKey = startOfMonth.toISOString().slice(0, 7);

  try {
    // Upsert usage record
    const { error } = await supabase.rpc('increment_usage', {
      p_user_id: userId,
      p_month: monthKey,
      p_usage_type: usageType,
    });

    if (error) throw error;

    // Track analytics
    await trackEvent('usage_incremented', {
      userId,
      usageType,
      month: monthKey,
    });
  } catch (error) {
    console.error('Failed to increment usage:', error);
  }
}

// Database function
/*
CREATE OR REPLACE FUNCTION increment_usage(
  p_user_id UUID,
  p_month DATE,
  p_usage_type TEXT
) RETURNS VOID AS $$
BEGIN
  INSERT INTO user_usage (user_id, month, imports_count, exports_count, shares_count)
  VALUES (p_user_id, p_month, 
    CASE WHEN p_usage_type = 'imports' THEN 1 ELSE 0 END,
    CASE WHEN p_usage_type = 'exports' THEN 1 ELSE 0 END,
    CASE WHEN p_usage_type = 'shares' THEN 1 ELSE 0 END
  )
  ON CONFLICT (user_id, month) DO UPDATE
  SET 
    imports_count = user_usage.imports_count + 
      CASE WHEN p_usage_type = 'imports' THEN 1 ELSE 0 END,
    exports_count = user_usage.exports_count + 
      CASE WHEN p_usage_type = 'exports' THEN 1 ELSE 0 END,
    shares_count = user_usage.shares_count + 
      CASE WHEN p_usage_type = 'shares' THEN 1 ELSE 0 END,
    updated_at = NOW();
END;
$$ LANGUAGE plpgsql;
*/
```

### Task 6: Pro Feature UI Components (2 hours)

**File**: `packages/web/src/components/pro/ProGate.tsx`

```typescript
'use client';

import { ReactNode } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Zap, Crown, Lock } from 'lucide-react';
import { useProFeatures } from '@/hooks/useProFeatures';

interface ProGateProps {
  feature: string;
  children: ReactNode;
  fallback?: ReactNode;
  soft?: boolean; // Show content but with upgrade prompt
}

export function ProGate({ feature, children, fallback, soft = false }: ProGateProps) {
  const { isPro } = useProFeatures();

  if (isPro || soft) {
    return <>{children}</>;
  }

  return (
    <div className="relative">
      {soft && (
        <div className="opacity-50 pointer-events-none">
          {children}
        </div>
      )}

      <motion.div
        initial={{ opacity: 0, scale: 0.95 }}
        animate={{ opacity: 1, scale: 1 }}
        className={soft ? 'absolute inset-0 flex items-center justify-center' : ''}
      >
        {fallback || <DefaultProPrompt feature={feature} />}
      </motion.div>
    </div>
  );
}

function DefaultProPrompt({ feature }: { feature: string }) {
  return (
    <Card className="max-w-md mx-auto">
      <CardContent className="text-center py-8">
        <Crown className="w-12 h-12 text-yellow-500 mx-auto mb-4" />
        <h3 className="text-xl font-semibold mb-2">Pro Feature</h3>
        <p className="text-gray-600 mb-6">
          {feature} is available with TravelViz Pro
        </p>
        <Button
          className="bg-gradient-to-r from-blue-600 to-purple-600"
          onClick={() => window.location.href = '/pricing'}
        >
          Upgrade to Pro
          <Zap className="ml-2 h-4 w-4" />
        </Button>
      </CardContent>
    </Card>
  );
}

export function ProBadge({ className }: { className?: string }) {
  return (
    <span className={cn(
      "inline-flex items-center gap-1 px-2 py-1 bg-gradient-to-r from-yellow-400 to-orange-400 text-white text-xs font-medium rounded-full",
      className
    )}>
      <Crown className="w-3 h-3" />
      PRO
    </span>
  );
}

export function UsageIndicator() {
  const { usage, limits, isPro, remainingImports } = useProFeatures();

  if (isPro) {
    return (
      <div className="text-sm text-gray-600">
        <span className="font-medium text-green-600">Unlimited imports</span>
      </div>
    );
  }

  const percentage = (usage.importsThisMonth / limits.importsPerMonth) * 100;

  return (
    <div className="space-y-2">
      <div className="flex items-center justify-between text-sm">
        <span className="text-gray-600">Monthly imports</span>
        <span className="font-medium">
          {usage.importsThisMonth} / {limits.importsPerMonth}
        </span>
      </div>
      <div className="relative h-2 bg-gray-200 rounded-full overflow-hidden">
        <motion.div
          initial={{ width: 0 }}
          animate={{ width: `${percentage}%` }}
          className={cn(
            "absolute inset-y-0 left-0 rounded-full",
            percentage >= 100 ? "bg-red-500" :
            percentage >= 66 ? "bg-yellow-500" :
            "bg-green-500"
          )}
        />
      </div>
      {percentage >= 66 && (
        <p className="text-xs text-gray-500">
          {remainingImports} imports remaining.{' '}
          <a href="/pricing" className="text-blue-600 hover:underline">
            Upgrade for unlimited
          </a>
        </p>
      )}
    </div>
  );
}

export function UpgradePrompt({
  title,
  description,
  benefits,
  onUpgrade,
  onDismiss
}: {
  title: string;
  description: string;
  benefits?: string[];
  onUpgrade: () => void;
  onDismiss?: () => void;
}) {
  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0, y: 50 }}
        animate={{ opacity: 1, y: 0 }}
        exit={{ opacity: 0, y: 50 }}
        className="fixed bottom-4 right-4 max-w-sm z-50"
      >
        <Card className="shadow-xl border-2 border-purple-200">
          <CardContent className="p-6">
            <div className="flex items-start gap-4">
              <div className="p-3 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full">
                <Zap className="w-6 h-6 text-white" />
              </div>
              <div className="flex-1">
                <h4 className="font-semibold mb-1">{title}</h4>
                <p className="text-sm text-gray-600 mb-3">{description}</p>

                {benefits && (
                  <ul className="text-sm space-y-1 mb-4">
                    {benefits.map((benefit, i) => (
                      <li key={i} className="flex items-center gap-2">
                        <Check className="w-3 h-3 text-green-600" />
                        <span>{benefit}</span>
                      </li>
                    ))}
                  </ul>
                )}

                <div className="flex gap-2">
                  <Button size="sm" onClick={onUpgrade}>
                    Upgrade Now
                  </Button>
                  {onDismiss && (
                    <Button size="sm" variant="ghost" onClick={onDismiss}>
                      Maybe Later
                    </Button>
                  )}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </motion.div>
    </AnimatePresence>
  );
}
```

## Day 11: Affiliate Integration (8 hours)

### Task 7: Affiliate Link Management (4 hours)

**File**: `packages/hub/src/services/affiliate.service.ts`

```typescript
import crypto from 'crypto';

interface AffiliateProvider {
  name: string;
  baseUrl: string;
  partnerId: string;
  generateUrl: (params: any) => string;
}

const AFFILIATE_PROVIDERS: Record<string, AffiliateProvider> = {
  travelpayouts: {
    name: 'Travelpayouts',
    baseUrl: 'https://www.travelpayouts.com',
    partnerId: process.env.TRAVELPAYOUTS_PARTNER_ID!,
    generateUrl: params => {
      const { type, ...rest } = params;

      switch (type) {
        case 'hotel':
          return `https://search.hotellook.com/?marker=${AFFILIATE_PROVIDERS.travelpayouts.partnerId}&destination=${rest.destination}&checkIn=${rest.checkIn}&checkOut=${rest.checkOut}`;

        case 'flight':
          return `https://www.aviasales.com/?marker=${AFFILIATE_PROVIDERS.travelpayouts.partnerId}&origin=${rest.origin}&destination=${rest.destination}&depart_date=${rest.departDate}&return_date=${rest.returnDate}`;

        case 'car':
          return `https://cars.travelpayouts.com/?marker=${AFFILIATE_PROVIDERS.travelpayouts.partnerId}&location=${rest.location}&pick_up=${rest.pickUp}&drop_off=${rest.dropOff}`;

        default:
          return '';
      }
    },
  },
  booking: {
    name: 'Booking.com',
    baseUrl: 'https://www.booking.com',
    partnerId: process.env.BOOKING_PARTNER_ID!,
    generateUrl: params => {
      return `https://www.booking.com/hotel/${params.hotelId}.html?aid=${AFFILIATE_PROVIDERS.booking.partnerId}&checkin=${params.checkIn}&checkout=${params.checkOut}`;
    },
  },
  viator: {
    name: 'Viator',
    baseUrl: 'https://www.viator.com',
    partnerId: process.env.VIATOR_PARTNER_ID!,
    generateUrl: params => {
      return `https://www.partner.viator.com/en/${AFFILIATE_PROVIDERS.viator.partnerId}/tours/${params.destination}`;
    },
  },
};

export class AffiliateService {
  /**
   * Generate affiliate URLs for activities
   */
  static generateAffiliateUrls(activity: Activity): AffiliateUrls {
    const urls: AffiliateUrls = {};

    switch (activity.type) {
      case 'hotel':
        urls.booking = this.generateBookingUrl(activity);
        urls.hotellook = this.generateHotellookUrl(activity);
        break;

      case 'flight':
        urls.aviasales = this.generateFlightUrl(activity);
        break;

      case 'activity':
        urls.viator = this.generateViatorUrl(activity);
        urls.getyourguide = this.generateGetYourGuideUrl(activity);
        break;

      case 'transport':
        if (activity.subType === 'car_rental') {
          urls.rentalcars = this.generateRentalCarsUrl(activity);
        }
        break;
    }

    return urls;
  }

  /**
   * Track affiliate click
   */
  static async trackClick(
    userId: string,
    activityId: string,
    provider: string,
    url: string,
    context?: {
      tripId?: string;
      position?: number;
      totalItems?: number;
    }
  ) {
    try {
      // Generate click ID for conversion tracking
      const clickId = crypto.randomUUID();

      // Store click data with attribution
      await supabase.from('affiliate_clicks').insert({
        id: clickId,
        user_id: userId,
        activity_id: activityId,
        provider,
        url,
        clicked_at: new Date().toISOString(),
        context,
        ip_address: await hashIP(context?.ipAddress),
        user_agent: context?.userAgent,
      });

      // Set conversion tracking cookie (30 days)
      if (context?.response) {
        context.response.cookie('travelviz_affiliate', clickId, {
          maxAge: 30 * 24 * 60 * 60 * 1000,
          httpOnly: true,
          secure: true,
          sameSite: 'lax',
        });
      }

      // Track analytics with revenue attribution
      await trackRevenueEvent('affiliate_click', {
        userId,
        clickId,
        activityId,
        provider,
        estimatedCommission: COMMISSION_RATES[provider] || 0.03,
        activityType: context?.activityType,
        clickPosition: context?.position,
        totalOptions: context?.totalItems,
      });

      // Update user's affiliate engagement score
      await updateUserEngagement(userId, 'affiliate_click');
    } catch (error) {
      console.error('Failed to track affiliate click:', error);
      // Don't throw - don't break the user experience
    }
  }

  /**
   * Generate tracking URL with our analytics
   */
  static wrapWithTracking(originalUrl: string, activityId: string, provider: string): string {
    const trackingId = crypto.randomBytes(8).toString('hex');

    // Store tracking info in Redis with TTL
    redis.setex(
      `affiliate:${trackingId}`,
      86400, // 24 hour TTL
      JSON.stringify({
        originalUrl,
        activityId,
        provider,
        timestamp: Date.now(),
      })
    );

    // Return our tracking URL
    return `${process.env.API_URL}/api/v1/affiliate/track/${trackingId}`;
  }

  private static generateBookingUrl(activity: Activity): string {
    const params = new URLSearchParams({
      aid: AFFILIATE_PROVIDERS.booking.partnerId,
      dest_name: activity.location?.address || activity.name,
      checkin: format(new Date(activity.startTime), 'yyyy-MM-dd'),
      checkout: format(addDays(new Date(activity.startTime), 1), 'yyyy-MM-dd'),
      group_adults: '2',
      no_rooms: '1',
    });

    return `https://www.booking.com/searchresults.html?${params}`;
  }

  private static generateHotellookUrl(activity: Activity): string {
    return AFFILIATE_PROVIDERS.travelpayouts.generateUrl({
      type: 'hotel',
      destination: activity.location?.address || activity.name,
      checkIn: format(new Date(activity.startTime), 'yyyy-MM-dd'),
      checkOut: format(addDays(new Date(activity.startTime), 1), 'yyyy-MM-dd'),
    });
  }

  private static generateFlightUrl(activity: Activity): string {
    // Extract origin/destination from activity details
    const origin = activity.metadata?.origin || 'NYC';
    const destination = activity.metadata?.destination || activity.location?.address;

    return AFFILIATE_PROVIDERS.travelpayouts.generateUrl({
      type: 'flight',
      origin,
      destination,
      departDate: format(new Date(activity.startTime), 'yyyy-MM-dd'),
      returnDate: activity.endTime ? format(new Date(activity.endTime), 'yyyy-MM-dd') : undefined,
    });
  }

  private static generateViatorUrl(activity: Activity): string {
    const destination = activity.location?.address?.split(',')[0] || 'destination';
    return AFFILIATE_PROVIDERS.viator.generateUrl({
      destination: destination.toLowerCase().replace(/\s+/g, '-'),
    });
  }

  private static generateGetYourGuideUrl(activity: Activity): string {
    const location = activity.location?.address || activity.name;
    const searchQuery = encodeURIComponent(`${activity.name} ${location}`);

    return `https://www.getyourguide.com/?partner_id=${process.env.GETYOURGUIDE_PARTNER_ID}&q=${searchQuery}`;
  }

  private static generateRentalCarsUrl(activity: Activity): string {
    return AFFILIATE_PROVIDERS.travelpayouts.generateUrl({
      type: 'car',
      location: activity.location?.address || activity.name,
      pickUp: format(new Date(activity.startTime), 'yyyy-MM-dd'),
      dropOff: activity.endTime
        ? format(new Date(activity.endTime), 'yyyy-MM-dd')
        : format(addDays(new Date(activity.startTime), 1), 'yyyy-MM-dd'),
    });
  }
}

// Database schema for tracking
/*
CREATE TABLE affiliate_clicks (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id),
  activity_id UUID REFERENCES activities(id),
  provider TEXT NOT NULL,
  url TEXT NOT NULL,
  clicked_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  converted BOOLEAN DEFAULT FALSE,
  commission_amount DECIMAL(10,2),
  commission_currency TEXT DEFAULT 'USD'
);

CREATE INDEX idx_affiliate_clicks_user ON affiliate_clicks(user_id, clicked_at DESC);
CREATE INDEX idx_affiliate_clicks_activity ON affiliate_clicks(activity_id);
*/
```

### Task 8: Affiliate UI Components (4 hours)

**File**: `packages/web/src/components/activities/ActivityBookingCard.tsx`

```typescript
'use client';

import { useState } from 'react';
import { motion } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import {
  Hotel,
  Plane,
  Car,
  Ticket,
  ExternalLink,
  DollarSign,
  Star,
  Info
} from 'lucide-react';
import { Activity, ActivityType } from '@travelviz/shared';
import { toast } from '@/components/ui/use-toast';

interface BookingOption {
  provider: string;
  logo: string;
  url: string;
  price?: number;
  rating?: number;
  color: string;
}

export function ActivityBookingCard({ activity }: { activity: Activity }) {
  const [loadingProvider, setLoadingProvider] = useState<string | null>(null);
  const [bookingOptions, setBookingOptions] = useState<BookingOption[]>([]);

  // Get booking options based on activity type
  const getBookingOptions = (): BookingOption[] => {
    switch (activity.type) {
      case 'hotel':
        return [
          {
            provider: 'Booking.com',
            logo: '/logos/booking.svg',
            url: activity.affiliateUrls?.booking || '#',
            price: activity.price,
            rating: 8.5,
            color: 'bg-blue-600'
          },
          {
            provider: 'Hotels.com',
            logo: '/logos/hotels.svg',
            url: activity.affiliateUrls?.hotellook || '#',
            price: activity.price ? activity.price * 0.95 : undefined,
            rating: 8.3,
            color: 'bg-red-600'
          }
        ];

      case 'flight':
        return [
          {
            provider: 'Skyscanner',
            logo: '/logos/skyscanner.svg',
            url: activity.affiliateUrls?.aviasales || '#',
            price: activity.price,
            color: 'bg-sky-600'
          },
          {
            provider: 'Kayak',
            logo: '/logos/kayak.svg',
            url: activity.affiliateUrls?.kayak || '#',
            price: activity.price ? activity.price * 1.05 : undefined,
            color: 'bg-orange-600'
          }
        ];

      case 'activity':
        return [
          {
            provider: 'Viator',
            logo: '/logos/viator.svg',
            url: activity.affiliateUrls?.viator || '#',
            price: activity.price,
            rating: 4.5,
            color: 'bg-purple-600'
          },
          {
            provider: 'GetYourGuide',
            logo: '/logos/getyourguide.svg',
            url: activity.affiliateUrls?.getyourguide || '#',
            price: activity.price ? activity.price * 0.9 : undefined,
            rating: 4.6,
            color: 'bg-yellow-600'
          }
        ];

      default:
        return [];
    }
  };

  const handleBookingClick = async (option: BookingOption) => {
    setLoadingProvider(option.provider);

    // Track click
    await fetch('/api/v1/affiliate/track', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${localStorage.getItem('token')}`
      },
      body: JSON.stringify({
        activityId: activity.id,
        provider: option.provider,
        url: option.url
      })
    });

    // Open in new tab
    window.open(option.url, '_blank');

    setTimeout(() => {
      setLoadingProvider(null);
    }, 1000);

    // Show affiliate disclosure
    toast({
      title: 'Affiliate Link',
      description: 'We may earn a small commission from bookings, at no extra cost to you.',
      duration: 5000
    });
  };

  const options = getBookingOptions();

  if (options.length === 0) return null;

  const getIcon = () => {
    switch (activity.type) {
      case 'hotel': return Hotel;
      case 'flight': return Plane;
      case 'transport': return Car;
      default: return Ticket;
    }
  };

  const Icon = getIcon();

  return (
    <Card className="mt-4">
      <CardContent className="p-4">
        <div className="flex items-center gap-2 mb-4">
          <Icon className="w-5 h-5 text-gray-600" />
          <h4 className="font-medium">Book This {activity.type}</h4>
          <div className="ml-auto">
            <AffiliateDisclosure />
          </div>
        </div>

        <div className="space-y-3">
          {options.map((option, index) => (
            <motion.div
              key={option.provider}
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
            >
              <Button
                variant="outline"
                className="w-full justify-between h-auto p-4"
                onClick={() => handleBookingClick(option)}
                disabled={!!loadingProvider}
              >
                <div className="flex items-center gap-3">
                  <div className={`w-10 h-10 ${option.color} rounded-lg p-2`}>
                    <img
                      src={option.logo}
                      alt={option.provider}
                      className="w-full h-full object-contain filter brightness-0 invert"
                    />
                  </div>
                  <div className="text-left">
                    <p className="font-medium">{option.provider}</p>
                    {option.rating && (
                      <div className="flex items-center gap-1 text-xs text-gray-500">
                        <Star className="w-3 h-3 fill-current" />
                        <span>{option.rating}</span>
                      </div>
                    )}
                  </div>
                </div>

                <div className="flex items-center gap-2">
                  {option.price && (
                    <div className="text-right">
                      <p className="text-lg font-semibold">
                        ${option.price}
                      </p>
                      <p className="text-xs text-gray-500">per night</p>
                    </div>
                  )}
                  {loadingProvider === option.provider ? (
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-gray-600" />
                  ) : (
                    <ExternalLink className="w-4 h-4 text-gray-400" />
                  )}
                </div>
              </Button>
            </motion.div>
          ))}
        </div>

        {/* Price comparison note */}
        {options.some(o => o.price) && (
          <p className="text-xs text-gray-500 mt-3 text-center">
            Prices may vary. Click to see current rates.
          </p>
        )}
      </CardContent>
    </Card>
  );
}

function AffiliateDisclosure() {
  const [showInfo, setShowInfo] = useState(false);

  return (
    <div className="relative">
      <button
        onClick={() => setShowInfo(!showInfo)}
        className="text-gray-400 hover:text-gray-600"
      >
        <Info className="w-4 h-4" />
      </button>

      {showInfo && (
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          className="absolute right-0 top-6 w-64 p-3 bg-white border rounded-lg shadow-lg z-10"
        >
          <p className="text-xs text-gray-600">
            <strong>Affiliate Disclosure:</strong> We earn a small commission
            from bookings made through these links, at no extra cost to you.
            This helps us keep TravelViz free to use.
          </p>
        </motion.div>
      )}
    </div>
  );
}

// Smart booking recommendations
export function SmartBookingRecommendations({ trip }: { trip: Trip }) {
  const [recommendations, setRecommendations] = useState<Recommendation[]>([]);

  useEffect(() => {
    // Analyze trip and suggest bookings
    const hotels = trip.activities.filter(a => a.type === 'hotel' && !a.bookingUrl);
    const flights = trip.activities.filter(a => a.type === 'flight' && !a.bookingUrl);

    if (hotels.length > 0 || flights.length > 0) {
      setRecommendations([
        {
          type: 'alert',
          message: `You have ${hotels.length} hotels and ${flights.length} flights to book`,
          action: 'Book Now',
          priority: 'high'
        }
      ]);
    }
  }, [trip]);

  if (recommendations.length === 0) return null;

  return (
    <Card className="bg-amber-50 border-amber-200">
      <CardContent className="p-4">
        <h4 className="font-medium text-amber-900 mb-2">
          Booking Reminders
        </h4>
        <div className="space-y-2">
          {recommendations.map((rec, i) => (
            <div key={i} className="flex items-center justify-between">
              <p className="text-sm text-amber-800">{rec.message}</p>
              <Button size="sm" variant="outline">
                {rec.action}
              </Button>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}
```

## Testing & Analytics

### Monetization Testing Checklist

- [ ] Stripe webhook receives events
- [ ] User upgrades to Pro successfully
- [ ] Pro features unlock immediately
- [ ] Usage limits enforced for free users
- [ ] Affiliate links track clicks
- [ ] Commission tracking works
- [ ] Billing period changes work
- [ ] Cancellation handled gracefully
- [ ] Payment retry logic works
- [ ] Fraud detection triggers appropriately
- [ ] Tax calculations are accurate
- [ ] Invoices generate correctly
- [ ] Refunds process properly
- [ ] Currency conversion works
- [ ] Proration calculations correct

### Analytics to Track

```typescript
// Key monetization metrics
const metrics = {
  conversion: {
    freeToProRate: 0.05, // 5% target
    trialToProRate: 0.4, // 40% target
    churnRate: 0.1, // 10% monthly
    winBackRate: 0.15, // 15% of churned users return
    upgradeTime: 7.5, // Days from signup to upgrade
  },
  revenue: {
    mrr: 0, // Monthly Recurring Revenue
    arr: 0, // Annual Recurring Revenue
    arpu: 9, // Average Revenue Per User
    ltv: 90, // Lifetime Value
    cac: 15, // Customer Acquisition Cost
    ltv_cac_ratio: 6, // Should be >3
    paybackPeriod: 1.67, // Months to recover CAC
  },
  affiliate: {
    clickRate: 0.15, // 15% click on affiliate links
    conversionRate: 0.03, // 3% book through links
    averageCommission: 5.5,
    revenuePerUser: 16.5, // Annual affiliate revenue/user
    topProviders: ['Booking.com', 'Viator', 'Skyscanner'],
  },
  payments: {
    successRate: 0.94, // 94% of payments succeed
    failureReasons: {
      insufficient_funds: 0.45,
      expired_card: 0.3,
      declined: 0.2,
      other: 0.05,
    },
    recoveryRate: 0.7, // 70% of failed payments recover
    chargebackRate: 0.002, // 0.2% (keep <1%)
  },
};

// Revenue monitoring alerts
const REVENUE_ALERTS = {
  mrr_drop: -0.05, // Alert on 5% MRR drop
  churn_spike: 0.15, // Alert if churn >15%
  payment_failure_spike: 0.1, // Alert if failures >10%
  fraud_threshold: 5, // Transactions per hour
};
```

### Advanced Revenue Analytics

```typescript
// Cohort analysis
interface CohortMetrics {
  cohortMonth: string;
  size: number;
  revenue: {
    month1: number;
    month2: number;
    month3: number;
    month6: number;
    month12: number;
  };
  retention: {
    month1: number;
    month2: number;
    month3: number;
    month6: number;
    month12: number;
  };
  ltv: number;
}

// Price elasticity testing
interface PriceTest {
  price: number;
  conversions: number;
  revenue: number;
  churn: number;
  elasticity: number; // % change in demand / % change in price
}

// Affiliate performance
interface AffiliateMetrics {
  provider: string;
  clicks: number;
  bookings: number;
  revenue: number;
  avgOrderValue: number;
  conversionRate: number;
  userLifetimeValue: number; // Users who book tend to stay longer
}
```

## Extended Thinking Prompts

For pricing decisions:

```
Current conversion rate: [number]
Price sensitivity data: [user feedback]
Competitor pricing: [list]
What's the optimal price point for growth?
```

For payment optimization:

```
Current failure rate: [percentage]
Top failure reasons: [list]
Recovery success rate: [percentage]
How can we reduce involuntary churn?
```

For affiliate strategy:

```
Top performing providers: [list with rates]
User booking patterns: [when/what they book]
Commission vs user experience: [tradeoffs]
How to increase booking rate without being pushy?
```

For fraud prevention:

```
Current fraud indicators: [patterns]
False positive rate: [percentage]
Revenue loss from fraud: [amount]
How to tighten without blocking legitimate users?
```

## Definition of Done

✅ Monetization Complete:

- Stripe payments working
- Pro features gated properly
- Usage tracking accurate
- Affiliate links integrated
- Analytics tracking revenue

✅ User Flow Test:

1. Free user hits import limit
2. Sees helpful upgrade prompt
3. Clicks to pricing page
4. Subscribes via Stripe
5. Returns to app with Pro features
6. Books hotel through affiliate link

## Next Day Preview

Day 12: User Testing Prep

- Demo content creation
- FAQ documentation
- Feedback system setup

## Notes

- Keep monetization simple initially
- Be transparent about affiliate links
- Free tier must provide real value
- Pro features must feel worth it
- Track everything but don't be creepy
- Revenue validates the business model
- Every payment failure is lost revenue
- Churn prevention > new acquisition
- Price anchoring matters (show annual savings)
- Trust badges increase conversion
- Localized pricing can 2x revenue
- Dunning emails save 15-25% of failures

## Revenue Protection Strategies

### 1. Involuntary Churn Prevention

```typescript
// Smart retry schedule
const RETRY_SCHEDULE = [
  { day: 1, time: '10:00' }, // Next day morning
  { day: 3, time: '14:00' }, // 3 days later afternoon
  { day: 7, time: '09:00' }, // Week later morning
  { day: 14, time: '19:00' }, // Two weeks evening
];

// Pre-dunning alerts
const PRE_DUNNING = {
  cardExpiring: 30, // Days before expiry
  paymentUpcoming: 3, // Days before charge
  insufficientFunds: 1, // Day after soft decline
};
```

### 2. Expansion Revenue

```typescript
// Upsell triggers
const UPSELL_TRIGGERS = {
  usage_based: {
    near_limit: 0.8, // 80% of limit
    hit_limit: 1.0, // 100% of limit
    consistent_high_usage: 3, // Months at >70%
  },

  behavior_based: {
    power_user_score: 0.8, // High engagement
    team_size: 3, // Multiple trip collaborators
    premium_destinations: true, // Expensive locations
  },

  timing_based: {
    account_age: 90, // Days
    last_upgrade_prompt: 30, // Days
    upcoming_trip: 14, // Days before trip
  },
};
```

### 3. Affiliate Revenue Optimization

```typescript
// Smart placement algorithm
function getOptimalAffiliatePosition(activity: Activity): PlacementStrategy {
  const factors = {
    user_intent: getUserBookingIntent(activity),
    activity_value: getActivityValue(activity),
    provider_performance: getProviderMetrics(activity.type),
    user_history: getUserAffiliateHistory(),
  };

  return {
    show_inline: factors.user_intent > 0.7,
    providers: rankProvidersByRevenue(factors),
    timing: factors.user_intent > 0.9 ? 'immediate' : 'delayed',
    prominence: calculateProminence(factors),
  };
}
```

### 4. Churn Prediction & Prevention

```typescript
// Churn risk scoring
interface ChurnRiskFactors {
  usage_decline: number; // % decrease in activity
  days_since_last_login: number;
  support_tickets: number;
  failed_payments: number;
  competitor_visits: boolean; // Via tracking pixels
  engagement_score: number;
}

// Retention offers
const RETENTION_OFFERS = {
  low_risk: null,
  medium_risk: {
    type: 'feature_unlock',
    message: 'Try our new AI trip optimizer free!',
  },
  high_risk: {
    type: 'discount',
    amount: 0.5, // 50% off next 3 months
    message: "We miss you! Here's 50% off",
  },
  churned: {
    type: 'win_back',
    amount: 0.7, // 70% off first month
    message: 'Come back for 70% off!',
  },
};
```
