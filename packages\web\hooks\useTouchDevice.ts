import { useState, useEffect } from 'react';

/**
 * Hook to detect if the device supports touch
 * @returns Object with touch capability information
 */
export function useTouchDevice() {
  const [isTouchDevice, setIsTouchDevice] = useState(false);
  const [hasHover, setHasHover] = useState(true);

  useEffect(() => {
    // Check if window is defined (SSR safety)
    if (typeof window === 'undefined') return;

    // Multiple detection methods for better accuracy
    const checkTouchDevice = () => {
      const hasTouch = (
        'ontouchstart' in window ||
        navigator.maxTouchPoints > 0 ||
        // @ts-ignore - Legacy API
        navigator.msMaxTouchPoints > 0 ||
        window.matchMedia('(pointer: coarse)').matches
      );
      
      const hasHoverCapability = window.matchMedia('(hover: hover)').matches;
      
      setIsTouchDevice(hasTouch);
      setHasHover(hasHoverCapability);
    };

    checkTouchDevice();

    // Listen for changes (e.g., connecting/disconnecting devices)
    const pointerQuery = window.matchMedia('(pointer: coarse)');
    const hoverQuery = window.matchMedia('(hover: hover)');

    const handlePointerChange = () => checkTouchDevice();

    if (pointerQuery.addEventListener) {
      pointerQuery.addEventListener('change', handlePointerChange);
      hoverQuery.addEventListener('change', handlePointerChange);
      
      return () => {
        pointerQuery.removeEventListener('change', handlePointerChange);
        hoverQuery.removeEventListener('change', handlePointerChange);
      };
    }
    
    return undefined;
  }, []);

  return {
    isTouchDevice,
    hasHover,
    // Hybrid devices (touch + mouse, like some laptops)
    isHybrid: isTouchDevice && hasHover,
    // Primary input is touch (phones, tablets)
    isTouchPrimary: isTouchDevice && !hasHover,
  };
}

/**
 * Hook to handle touch gestures with proper event handling
 */
export function useTouchGestures(
  ref: React.RefObject<HTMLElement>,
  options: {
    onSwipeLeft?: () => void;
    onSwipeRight?: () => void;
    onSwipeUp?: () => void;
    onSwipeDown?: () => void;
    onTap?: () => void;
    threshold?: number;
  } = {}
) {
  const { threshold = 50 } = options;

  useEffect(() => {
    const element = ref.current;
    if (!element) return;

    let startX = 0;
    let startY = 0;
    let startTime = 0;

    const handleTouchStart = (e: TouchEvent) => {
      startX = e.touches[0].clientX;
      startY = e.touches[0].clientY;
      startTime = Date.now();
    };

    const handleTouchEnd = (e: TouchEvent) => {
      const endX = e.changedTouches[0].clientX;
      const endY = e.changedTouches[0].clientY;
      const endTime = Date.now();

      const deltaX = endX - startX;
      const deltaY = endY - startY;
      const deltaTime = endTime - startTime;

      // Tap detection (short duration, minimal movement)
      if (deltaTime < 200 && Math.abs(deltaX) < 10 && Math.abs(deltaY) < 10) {
        options.onTap?.();
        return;
      }

      // Swipe detection
      if (Math.abs(deltaX) > Math.abs(deltaY)) {
        // Horizontal swipe
        if (Math.abs(deltaX) > threshold) {
          if (deltaX > 0) {
            options.onSwipeRight?.();
          } else {
            options.onSwipeLeft?.();
          }
        }
      } else {
        // Vertical swipe
        if (Math.abs(deltaY) > threshold) {
          if (deltaY > 0) {
            options.onSwipeDown?.();
          } else {
            options.onSwipeUp?.();
          }
        }
      }
    };

    element.addEventListener('touchstart', handleTouchStart, { passive: true });
    element.addEventListener('touchend', handleTouchEnd, { passive: true });

    return () => {
      element.removeEventListener('touchstart', handleTouchStart);
      element.removeEventListener('touchend', handleTouchEnd);
    };
  }, [ref, options, threshold]);
}

/**
 * Hook for safe area insets (notches, home indicators)
 */
export function useSafeArea() {
  const [safeAreaInsets, setSafeAreaInsets] = useState({
    top: 0,
    right: 0,
    bottom: 0,
    left: 0,
  });

  useEffect(() => {
    if (typeof window === 'undefined') return;

    const updateSafeAreaInsets = () => {
      const computedStyle = getComputedStyle(document.documentElement);
      
      setSafeAreaInsets({
        top: parseInt(computedStyle.getPropertyValue('--sat') || '0', 10),
        right: parseInt(computedStyle.getPropertyValue('--sar') || '0', 10),
        bottom: parseInt(computedStyle.getPropertyValue('--sab') || '0', 10),
        left: parseInt(computedStyle.getPropertyValue('--sal') || '0', 10),
      });
    };

    updateSafeAreaInsets();
    window.addEventListener('resize', updateSafeAreaInsets);
    
    return () => window.removeEventListener('resize', updateSafeAreaInsets);
  }, []);

  return safeAreaInsets;
}