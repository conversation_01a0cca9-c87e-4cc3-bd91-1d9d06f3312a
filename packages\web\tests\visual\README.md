# Visual Regression Testing

This directory contains visual regression tests for critical UI components. These tests capture screenshots and compare them against baseline images to detect unintended visual changes.

## How it Works

1. **Baseline Images**: The first time a test runs, it creates a baseline screenshot
2. **Comparison**: Subsequent runs compare against the baseline
3. **Diff Detection**: Any pixel differences above the threshold are flagged as failures
4. **Smart Features**: Tests include animation disabling, mobile testing, and dark mode support

## Directory Structure

```
tests/visual/
├── baselines/           # Reference screenshots (committed to git)
├── actual/              # Latest test screenshots (ignored by git)
├── diff/                # Diff images showing changes (ignored by git)
├── visual-regression.spec.ts  # Test definitions
└── README.md           # This file
```

## Running Tests

```bash
# Run all visual tests
pnpm test:visual

# Update baselines (when changes are intentional)
pnpm test:visual:update

# Run specific test
pnpm test:visual --grep "Import wizard"
```

## Test Coverage

### Critical Components
- **Import Wizard**: Initial state, with content, error states
- **Homepage Hero**: Main landing section
- **Mobile Layout**: Responsive design verification
- **Dark Mode**: Theme compatibility
- **Loading States**: Progress indicators and spinners

### Test Features
- **Animation Disabling**: Ensures consistent screenshots
- **Mobile Testing**: Tests responsive layouts
- **Error State Testing**: Captures error UIs
- **Threshold Configuration**: Customizable sensitivity
- **Clipping**: Test specific UI sections

## Configuration

Each test can specify:
- `threshold`: Pixel difference sensitivity (0.0-1.0)
- `maxDiffPixels`: Maximum acceptable different pixels
- `animations`: Enable/disable animations
- `clip`: Screenshot specific areas
- `waitForSelector`: Wait for elements to load

## Best Practices

### When to Update Baselines
- After intentional design changes
- When new features change existing UI
- After fixing visual bugs

### Writing Tests
```typescript
visualTest('Component name', async (page, compare) => {
  await page.goto('/page');
  
  // Setup test state
  await page.fill('input', 'test data');
  
  // Compare screenshot
  const passed = await compare('screenshot-name', {
    threshold: 0.1,
    animations: 'disabled',
  });
  
  expect(passed).toBeTruthy();
});
```

### Debugging Failures
1. Check the `diff/` directory for visual differences
2. Review the `actual/` vs `baselines/` images
3. Determine if changes are intentional or bugs
4. Update baselines if changes are expected

## CI/CD Integration

Visual tests run in CI with:
- Consistent browser versions
- Stable viewport sizes
- Disabled animations
- Headless execution

Failed tests include diff images as artifacts for review.

## Maintenance

### Regular Tasks
- Review and update baselines when UI changes
- Monitor test stability and adjust thresholds
- Add tests for new critical components
- Clean up obsolete test files

### Performance Considerations
- Tests run with animations disabled
- Screenshots are compressed PNG format
- Only critical UI paths are tested
- Tests run in parallel when possible