# MVP Days 13-14: Scale Preparation

**Date**: [Execute Date]  
**Goal**: Prepare infrastructure to handle viral growth without crashing  
**Duration**: 16 hours (2 days)  
**Critical Path**: YES - Can't launch if we'll crash under load

## Context & Scaling Strategy

### What Kills Startups at Launch

1. **Database melts** - N+1 queries, missing indexes
2. **API rate limits** - OpenRouter blocks us
3. **Server crashes** - Memory leaks, no queuing
4. **Costs explode** - Inefficient queries, no caching
5. **Data loss** - No backups, no monitoring

### Our Scale Plan

- Handle 10,000 users on launch day
- 100 concurrent imports
- <2s page loads under load
- Zero data loss
- Costs stay under $500/month

## Auto-scaling Architecture

### Kubernetes Setup for Production

**File**: `infrastructure/k8s/deployment.yaml`

```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: travelviz-hub
  labels:
    app: travelviz-hub
spec:
  replicas: 3
  selector:
    matchLabels:
      app: travelviz-hub
  template:
    metadata:
      labels:
        app: travelviz-hub
    spec:
      containers:
        - name: hub
          image: travelviz/hub:latest
          ports:
            - containerPort: 3001
          env:
            - name: NODE_ENV
              value: 'production'
            - name: DATABASE_URL
              valueFrom:
                secretKeyRef:
                  name: travelviz-secrets
                  key: database-url
          resources:
            requests:
              memory: '256Mi'
              cpu: '250m'
            limits:
              memory: '512Mi'
              cpu: '500m'
          livenessProbe:
            httpGet:
              path: /health
              port: 3001
            initialDelaySeconds: 30
            periodSeconds: 10
          readinessProbe:
            httpGet:
              path: /ready
              port: 3001
            initialDelaySeconds: 5
            periodSeconds: 5
---
apiVersion: v1
kind: Service
metadata:
  name: travelviz-hub-service
spec:
  selector:
    app: travelviz-hub
  ports:
    - port: 80
      targetPort: 3001
  type: LoadBalancer
---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: travelviz-hub-hpa
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: travelviz-hub
  minReplicas: 3
  maxReplicas: 20
  metrics:
    - type: Resource
      resource:
        name: cpu
        target:
          type: Utilization
          averageUtilization: 70
    - type: Resource
      resource:
        name: memory
        target:
          type: Utilization
          averageUtilization: 80
    - type: Pods
      pods:
        metric:
          name: http_requests_per_second
        target:
          type: AverageValue
          averageValue: '1000'
  behavior:
    scaleDown:
      stabilizationWindowSeconds: 300
      policies:
        - type: Percent
          value: 10
          periodSeconds: 60
    scaleUp:
      stabilizationWindowSeconds: 30
      policies:
        - type: Percent
          value: 100
          periodSeconds: 30
        - type: Pods
          value: 4
          periodSeconds: 30
      selectPolicy: Max
```

### Load Balancer Configuration

**File**: `infrastructure/nginx/nginx.conf`

```nginx
upstream travelviz_backend {
    least_conn;

    # Kubernetes service discovery
    server travelviz-hub-service:80 max_fails=3 fail_timeout=30s;

    # Health check
    keepalive 32;
    keepalive_requests 100;
    keepalive_timeout 60s;
}

# Rate limiting zones
limit_req_zone $binary_remote_addr zone=api_limit:10m rate=10r/s;
limit_req_zone $binary_remote_addr zone=import_limit:10m rate=1r/m;
limit_req_zone $binary_remote_addr zone=auth_limit:10m rate=5r/m;

# Connection limiting
limit_conn_zone $binary_remote_addr zone=conn_limit:10m;

server {
    listen 80;
    server_name api.travelviz.com;

    # Security headers
    add_header X-Frame-Options "DENY" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://cdn.jsdelivr.net; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' data:; connect-src 'self' https://api.mapbox.com https://openrouter.ai;" always;

    # Gzip compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types text/plain text/css application/json application/javascript text/xml application/xml;

    # Connection limits
    limit_conn conn_limit 100;

    # General API rate limiting
    location /api/ {
        limit_req zone=api_limit burst=20 nodelay;
        limit_req_status 429;

        proxy_pass http://travelviz_backend;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;

        # Timeouts
        proxy_connect_timeout 10s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;

        # Buffering
        proxy_buffering on;
        proxy_buffer_size 4k;
        proxy_buffers 8 4k;
        proxy_busy_buffers_size 8k;
    }

    # Import endpoint - stricter limits
    location /api/v1/trips/import {
        limit_req zone=import_limit burst=2 nodelay;
        limit_req_status 429;

        # Larger timeouts for AI processing
        proxy_read_timeout 120s;
        proxy_send_timeout 120s;

        proxy_pass http://travelviz_backend;
        include /etc/nginx/proxy_params;
    }

    # Auth endpoints - prevent brute force
    location ~ ^/api/v1/auth/(login|register) {
        limit_req zone=auth_limit burst=5 nodelay;
        limit_req_status 429;

        proxy_pass http://travelviz_backend;
        include /etc/nginx/proxy_params;
    }

    # Health checks
    location /health {
        access_log off;
        proxy_pass http://travelviz_backend;
        proxy_set_header Host $host;
    }

    # Metrics endpoint (internal only)
    location /metrics {
        allow 10.0.0.0/8;
        deny all;
        proxy_pass http://travelviz_backend;
    }

    # Custom error pages
    error_page 429 /errors/429.json;
    error_page 502 503 504 /errors/50x.json;

    location ^~ /errors/ {
        internal;
        root /usr/share/nginx/html;
        default_type application/json;
    }
}

# CDN origin server
server {
    listen 80;
    server_name cdn-origin.travelviz.com;

    root /var/www/static;

    # Cache headers for static assets
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        add_header Vary "Accept-Encoding";
    }

    # Serve pre-compressed files if available
    gzip_static on;
}
```

### Container Orchestration Script

**File**: `infrastructure/scripts/deploy.sh`

```bash
#!/bin/bash
set -euo pipefail

# Configuration
CLUSTER_NAME="travelviz-prod"
REGION="us-east-1"
MIN_NODES=3
MAX_NODES=10
NAMESPACE="production"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m'

log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1" >&2
    exit 1
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# Pre-deployment checks
pre_deploy_checks() {
    log "Running pre-deployment checks..."

    # Check kubectl
    if ! command -v kubectl &> /dev/null; then
        error "kubectl not found. Please install kubectl."
    fi

    # Check cluster connectivity
    if ! kubectl cluster-info &> /dev/null; then
        error "Cannot connect to Kubernetes cluster"
    fi

    # Check namespace
    if ! kubectl get namespace $NAMESPACE &> /dev/null; then
        log "Creating namespace $NAMESPACE"
        kubectl create namespace $NAMESPACE
    fi

    # Check secrets
    if ! kubectl get secret travelviz-secrets -n $NAMESPACE &> /dev/null; then
        error "Secrets not found. Run setup-secrets.sh first."
    fi

    log "Pre-deployment checks passed ✓"
}

# Build and push Docker images
build_images() {
    log "Building Docker images..."

    # Hub service
    docker build -t travelviz/hub:latest ./packages/hub
    docker tag travelviz/hub:latest travelviz/hub:$(git rev-parse --short HEAD)

    # Web service
    docker build -t travelviz/web:latest ./packages/web
    docker tag travelviz/web:latest travelviz/web:$(git rev-parse --short HEAD)

    # Push to registry
    log "Pushing images to registry..."
    docker push travelviz/hub:latest
    docker push travelviz/hub:$(git rev-parse --short HEAD)
    docker push travelviz/web:latest
    docker push travelviz/web:$(git rev-parse --short HEAD)

    log "Images built and pushed ✓"
}

# Deploy to Kubernetes
deploy_to_k8s() {
    log "Deploying to Kubernetes..."

    # Apply configurations
    kubectl apply -f infrastructure/k8s/namespace.yaml
    kubectl apply -f infrastructure/k8s/configmap.yaml -n $NAMESPACE
    kubectl apply -f infrastructure/k8s/deployment.yaml -n $NAMESPACE
    kubectl apply -f infrastructure/k8s/service.yaml -n $NAMESPACE
    kubectl apply -f infrastructure/k8s/hpa.yaml -n $NAMESPACE
    kubectl apply -f infrastructure/k8s/ingress.yaml -n $NAMESPACE

    # Wait for rollout
    log "Waiting for deployment rollout..."
    kubectl rollout status deployment/travelviz-hub -n $NAMESPACE --timeout=300s
    kubectl rollout status deployment/travelviz-web -n $NAMESPACE --timeout=300s

    log "Deployment completed ✓"
}

# Configure auto-scaling
configure_autoscaling() {
    log "Configuring auto-scaling..."

    # Cluster autoscaler
    cat <<EOF | kubectl apply -f -
apiVersion: v1
kind: ConfigMap
metadata:
  name: cluster-autoscaler-status
  namespace: kube-system
data:
  nodes.min: "$MIN_NODES"
  nodes.max: "$MAX_NODES"
  scale-down-delay-after-add: "10m"
  scale-down-unneeded-time: "10m"
  skip-nodes-with-system-pods: "false"
EOF

    # Metrics server (required for HPA)
    kubectl apply -f https://github.com/kubernetes-sigs/metrics-server/releases/latest/download/components.yaml

    # Custom metrics for advanced scaling
    kubectl apply -f infrastructure/k8s/custom-metrics.yaml -n $NAMESPACE

    log "Auto-scaling configured ✓"
}

# Setup monitoring
setup_monitoring() {
    log "Setting up monitoring..."

    # Prometheus
    helm repo add prometheus-community https://prometheus-community.github.io/helm-charts
    helm repo update

    helm upgrade --install prometheus prometheus-community/kube-prometheus-stack \
        --namespace monitoring \
        --create-namespace \
        --set prometheus.prometheusSpec.retention=30d \
        --set prometheus.prometheusSpec.storageSpec.volumeClaimTemplate.spec.resources.requests.storage=50Gi

    # Custom alerts
    kubectl apply -f infrastructure/monitoring/alerts.yaml -n monitoring

    log "Monitoring setup complete ✓"
}

# Run load test
run_load_test() {
    log "Running load test..."

    # Get service endpoint
    SERVICE_URL=$(kubectl get service travelviz-hub-service -n $NAMESPACE -o jsonpath='{.status.loadBalancer.ingress[0].hostname}')

    if [ -z "$SERVICE_URL" ]; then
        warning "Load balancer URL not ready yet. Skipping load test."
        return
    fi

    # Run Artillery test
    cd packages/hub/src/tests/load
    npm run load-test -- --target "http://$SERVICE_URL"

    log "Load test complete ✓"
}

# Main deployment flow
main() {
    log "Starting TravelViz production deployment..."

    pre_deploy_checks
    build_images
    deploy_to_k8s
    configure_autoscaling
    setup_monitoring
    run_load_test

    # Get endpoints
    log "\n=== Deployment Summary ==="
    echo "Hub Service: $(kubectl get service travelviz-hub-service -n $NAMESPACE -o jsonpath='{.status.loadBalancer.ingress[0].hostname}')"
    echo "Web Service: $(kubectl get service travelviz-web-service -n $NAMESPACE -o jsonpath='{.status.loadBalancer.ingress[0].hostname}')"
    echo "Monitoring: $(kubectl get service prometheus-grafana -n monitoring -o jsonpath='{.status.loadBalancer.ingress[0].hostname}')"

    log "\n🚀 Deployment successful! TravelViz is ready for scale."
}

# Run main function
main "$@"
```

## Database Sharding Strategy

### Sharding Implementation for 100k+ Users

**File**: `packages/hub/src/database/sharding.config.ts`

```typescript
import { createHash } from 'crypto';
import { Pool } from 'pg';

interface ShardConfig {
  id: number;
  host: string;
  port: number;
  database: string;
  minUsers: string;
  maxUsers: string;
  weight: number;
  readonly: boolean;
}

interface ShardingStrategy {
  getShardForUser(userId: string): ShardConfig;
  getShardForRead(userId: string): ShardConfig;
  getAllShards(): ShardConfig[];
  rebalanceShards(): Promise<void>;
}

class ConsistentHashSharding implements ShardingStrategy {
  private shards: ShardConfig[];
  private connections: Map<number, Pool> = new Map();
  private hashRing: Array<{ hash: string; shardId: number }> = [];
  private readonly VIRTUAL_NODES = 150; // Virtual nodes per shard

  constructor(shards: ShardConfig[]) {
    this.shards = shards;
    this.initializeHashRing();
    this.initializeConnections();
  }

  private initializeHashRing() {
    // Create virtual nodes for consistent hashing
    this.shards.forEach(shard => {
      if (shard.readonly) return; // Skip read replicas

      for (let i = 0; i < this.VIRTUAL_NODES * shard.weight; i++) {
        const hash = this.hash(`${shard.id}-${i}`);
        this.hashRing.push({ hash, shardId: shard.id });
      }
    });

    // Sort ring by hash
    this.hashRing.sort((a, b) => a.hash.localeCompare(b.hash));
  }

  private initializeConnections() {
    this.shards.forEach(shard => {
      const pool = new Pool({
        host: shard.host,
        port: shard.port,
        database: shard.database,
        user: process.env.DB_USER,
        password: process.env.DB_PASSWORD,
        max: 20,
        idleTimeoutMillis: 30000,
        connectionTimeoutMillis: 2000,
        statement_timeout: 30000,
        query_timeout: 30000,
      });

      // Connection pool monitoring
      pool.on('error', err => {
        console.error(`Shard ${shard.id} pool error:`, err);
        // Trigger alert
      });

      this.connections.set(shard.id, pool);
    });
  }

  private hash(key: string): string {
    return createHash('sha256').update(key).digest('hex');
  }

  getShardForUser(userId: string): ShardConfig {
    const userHash = this.hash(userId);

    // Binary search in hash ring
    let low = 0;
    let high = this.hashRing.length - 1;

    while (low <= high) {
      const mid = Math.floor((low + high) / 2);
      if (this.hashRing[mid].hash === userHash) {
        return this.getShardById(this.hashRing[mid].shardId)!;
      }
      if (this.hashRing[mid].hash < userHash) {
        low = mid + 1;
      } else {
        high = mid - 1;
      }
    }

    // If no exact match, return next shard in ring
    const index = low % this.hashRing.length;
    return this.getShardById(this.hashRing[index].shardId)!;
  }

  getShardForRead(userId: string): ShardConfig {
    const writeShard = this.getShardForUser(userId);

    // Find read replicas for this shard
    const replicas = this.shards.filter(
      s => s.readonly && s.minUsers <= userId && s.maxUsers >= userId
    );

    if (replicas.length === 0) {
      return writeShard; // Fallback to write shard
    }

    // Random selection among replicas
    return replicas[Math.floor(Math.random() * replicas.length)];
  }

  getAllShards(): ShardConfig[] {
    return this.shards.filter(s => !s.readonly);
  }

  private getShardById(id: number): ShardConfig | undefined {
    return this.shards.find(s => s.id === id);
  }

  async rebalanceShards(): Promise<void> {
    console.log('Starting shard rebalancing...');

    // Get current shard statistics
    const stats = await this.getShardStatistics();

    // Calculate ideal distribution
    const totalUsers = stats.reduce((sum, s) => sum + s.userCount, 0);
    const idealUsersPerShard = Math.ceil(totalUsers / this.shards.length);

    // Identify overloaded shards
    const overloadedShards = stats.filter(
      s => s.userCount > idealUsersPerShard * 1.2 // 20% threshold
    );

    if (overloadedShards.length === 0) {
      console.log('Shards are balanced');
      return;
    }

    // Move users from overloaded shards
    for (const overloaded of overloadedShards) {
      const usersToMove = overloaded.userCount - idealUsersPerShard;
      await this.moveUsers(overloaded.shardId, usersToMove);
    }

    console.log('Shard rebalancing complete');
  }

  private async getShardStatistics() {
    const stats = [];

    for (const shard of this.shards) {
      if (shard.readonly) continue;

      const pool = this.connections.get(shard.id)!;
      const result = await pool.query(`
        SELECT 
          COUNT(DISTINCT user_id) as user_count,
          COUNT(*) as total_records,
          pg_database_size(current_database()) as db_size
        FROM trips
      `);

      stats.push({
        shardId: shard.id,
        userCount: parseInt(result.rows[0].user_count),
        totalRecords: parseInt(result.rows[0].total_records),
        dbSize: parseInt(result.rows[0].db_size),
      });
    }

    return stats;
  }

  private async moveUsers(fromShardId: number, count: number) {
    // Implementation of user migration between shards
    // This is complex and requires careful transaction handling
    console.log(`Moving ${count} users from shard ${fromShardId}`);

    // 1. Select users to move (least active)
    // 2. Begin transaction on both shards
    // 3. Copy data to new shard
    // 4. Verify data integrity
    // 5. Delete from old shard
    // 6. Update routing table
    // 7. Commit both transactions
  }

  async executeQuery(userId: string, query: string, params: any[], readonly = false) {
    const shard = readonly ? this.getShardForRead(userId) : this.getShardForUser(userId);

    const pool = this.connections.get(shard.id)!;

    try {
      const result = await pool.query(query, params);
      return result;
    } catch (error) {
      console.error(`Query error on shard ${shard.id}:`, error);

      // Retry on replica if read query
      if (readonly && !shard.readonly) {
        const replica = this.getShardForRead(userId);
        if (replica.id !== shard.id) {
          return pool.query(query, params);
        }
      }

      throw error;
    }
  }

  // Cross-shard query execution
  async executeGlobalQuery(query: string, params: any[] = []) {
    const results = await Promise.all(
      this.getAllShards().map(async shard => {
        const pool = this.connections.get(shard.id)!;
        return pool.query(query, params);
      })
    );

    // Merge results
    return {
      rows: results.flatMap(r => r.rows),
      rowCount: results.reduce((sum, r) => sum + r.rowCount, 0),
    };
  }

  async close() {
    await Promise.all(Array.from(this.connections.values()).map(pool => pool.end()));
  }
}

// Shard configuration
const SHARD_CONFIG: ShardConfig[] = [
  {
    id: 1,
    host: process.env.SHARD1_HOST || 'shard1.db.travelviz.com',
    port: 5432,
    database: 'travelviz_shard1',
    minUsers: '00000000-0000-0000-0000-000000000000',
    maxUsers: '3fffffff-ffff-ffff-ffff-ffffffffffff',
    weight: 1,
    readonly: false,
  },
  {
    id: 2,
    host: process.env.SHARD2_HOST || 'shard2.db.travelviz.com',
    port: 5432,
    database: 'travelviz_shard2',
    minUsers: '40000000-0000-0000-0000-000000000000',
    maxUsers: '7fffffff-ffff-ffff-ffff-ffffffffffff',
    weight: 1,
    readonly: false,
  },
  {
    id: 3,
    host: process.env.SHARD3_HOST || 'shard3.db.travelviz.com',
    port: 5432,
    database: 'travelviz_shard3',
    minUsers: '80000000-0000-0000-0000-000000000000',
    maxUsers: 'bfffffff-ffff-ffff-ffff-ffffffffffff',
    weight: 1,
    readonly: false,
  },
  {
    id: 4,
    host: process.env.SHARD4_HOST || 'shard4.db.travelviz.com',
    port: 5432,
    database: 'travelviz_shard4',
    minUsers: 'c0000000-0000-0000-0000-000000000000',
    maxUsers: 'ffffffff-ffff-ffff-ffff-ffffffffffff',
    weight: 1,
    readonly: false,
  },
  // Read replicas
  {
    id: 11,
    host: process.env.SHARD1_REPLICA_HOST || 'shard1-replica.db.travelviz.com',
    port: 5432,
    database: 'travelviz_shard1',
    minUsers: '00000000-0000-0000-0000-000000000000',
    maxUsers: '3fffffff-ffff-ffff-ffff-ffffffffffff',
    weight: 0,
    readonly: true,
  },
  // Add more replicas as needed
];

export const shardingStrategy = new ConsistentHashSharding(SHARD_CONFIG);

// Database access layer with sharding
export class ShardedDatabase {
  async getUserTrips(userId: string, limit = 20, offset = 0) {
    const query = `
      SELECT * FROM trips 
      WHERE user_id = $1 
      ORDER BY created_at DESC 
      LIMIT $2 OFFSET $3
    `;

    const result = await shardingStrategy.executeQuery(
      userId,
      query,
      [userId, limit, offset],
      true // Read query
    );

    return result.rows;
  }

  async createTrip(userId: string, tripData: any) {
    const query = `
      INSERT INTO trips (user_id, title, description, start_date, end_date, metadata)
      VALUES ($1, $2, $3, $4, $5, $6)
      RETURNING *
    `;

    const result = await shardingStrategy.executeQuery(userId, query, [
      userId,
      tripData.title,
      tripData.description,
      tripData.startDate,
      tripData.endDate,
      JSON.stringify(tripData.metadata),
    ]);

    return result.rows[0];
  }

  async getGlobalStats() {
    const query = `
      SELECT 
        COUNT(DISTINCT user_id) as total_users,
        COUNT(*) as total_trips,
        COUNT(CASE WHEN created_at > NOW() - INTERVAL '24 hours' THEN 1 END) as trips_today
      FROM trips
    `;

    const result = await shardingStrategy.executeGlobalQuery(query);

    // Aggregate results from all shards
    return result.rows.reduce(
      (acc, row) => ({
        total_users: acc.total_users + parseInt(row.total_users),
        total_trips: acc.total_trips + parseInt(row.total_trips),
        trips_today: acc.trips_today + parseInt(row.trips_today),
      }),
      { total_users: 0, total_trips: 0, trips_today: 0 }
    );
  }
}

// Migration script for sharding
export async function setupSharding() {
  console.log('Setting up database sharding...');

  // Create shard databases
  for (const shard of SHARD_CONFIG) {
    if (shard.readonly) continue;

    console.log(`Setting up shard ${shard.id}...`);

    // Run migrations on each shard
    await runMigrations(shard);

    // Set up replication if needed
    if (!shard.readonly) {
      await setupReplication(shard);
    }
  }

  console.log('Sharding setup complete');
}

async function runMigrations(shard: ShardConfig) {
  // Implementation of migration runner for each shard
}

async function setupReplication(shard: ShardConfig) {
  // Implementation of replication setup
}
```

## CDN Strategy & Global Edge Deployment

### CloudFlare Workers Edge Computing

**File**: `infrastructure/cloudflare/worker.js`

```javascript
// CloudFlare Worker for edge computing and caching

// Environment variables (configured in CloudFlare)
const ORIGIN_URL = 'https://api.travelviz.com';
const STATIC_BUCKET = 'travelviz-static';
const CACHE_TTL = {
  static: 31536000, // 1 year
  api: 300, // 5 minutes
  trips: 3600, // 1 hour for public trips
  user: 0, // No cache for user-specific data
};

// Rate limiting configuration
const RATE_LIMITS = {
  api: { requests: 100, window: 60 }, // 100 req/min
  import: { requests: 5, window: 3600 }, // 5 imports/hour
  auth: { requests: 10, window: 900 }, // 10 attempts/15min
};

addEventListener('fetch', event => {
  event.respondWith(handleRequest(event));
});

async function handleRequest(event) {
  const request = event.request;
  const url = new URL(request.url);
  const path = url.pathname;

  // Health check endpoint
  if (path === '/health') {
    return new Response('OK', { status: 200 });
  }

  // Apply rate limiting
  const rateLimitResponse = await checkRateLimit(request);
  if (rateLimitResponse) {
    return rateLimitResponse;
  }

  // Route to appropriate handler
  if (path.startsWith('/static/')) {
    return handleStaticAsset(request, event);
  } else if (path.startsWith('/api/')) {
    return handleAPIRequest(request, event);
  } else if (path === '/sitemap.xml' || path === '/robots.txt') {
    return handleSEOFiles(request, event);
  }

  // Default: proxy to origin
  return fetch(request);
}

// Static asset handling with R2 storage
async function handleStaticAsset(request, event) {
  const url = new URL(request.url);
  const key = url.pathname.slice(1); // Remove leading slash

  // Try cache first
  const cache = caches.default;
  const cachedResponse = await cache.match(request);
  if (cachedResponse) {
    return cachedResponse;
  }

  // Check R2 bucket
  const object = await STATIC_BUCKET.get(key);
  if (!object) {
    return new Response('Not Found', { status: 404 });
  }

  // Build response with appropriate headers
  const headers = new Headers();
  headers.set('Content-Type', object.httpMetadata.contentType || 'application/octet-stream');
  headers.set('Cache-Control', `public, max-age=${CACHE_TTL.static}, immutable`);
  headers.set('ETag', object.httpEtag);

  // Add security headers
  headers.set('X-Content-Type-Options', 'nosniff');
  headers.set('X-Frame-Options', 'DENY');

  const response = new Response(object.body, { headers });

  // Cache the response
  event.waitUntil(cache.put(request, response.clone()));

  return response;
}

// API request handling with intelligent caching
async function handleAPIRequest(request, event) {
  const url = new URL(request.url);
  const path = url.pathname;

  // Determine cache strategy
  let cacheTime = 0;
  let cacheKey = request.url;

  if (path.includes('/trips/public')) {
    cacheTime = CACHE_TTL.trips;
    // Cache by URL without auth headers
    cacheKey = `${url.origin}${url.pathname}${url.search}`;
  } else if (path.includes('/search')) {
    cacheTime = CACHE_TTL.api;
  } else if (request.headers.get('Authorization')) {
    // User-specific data - no edge caching
    cacheTime = 0;
  }

  // Try cache for GET requests
  if (request.method === 'GET' && cacheTime > 0) {
    const cache = caches.default;
    const cachedResponse = await cache.match(cacheKey);

    if (cachedResponse) {
      // Check if still fresh
      const age = Date.now() - new Date(cachedResponse.headers.get('Date')).getTime();
      if (age < cacheTime * 1000) {
        return cachedResponse;
      }
    }
  }

  // Add geo headers for origin
  const country = request.cf?.country || 'XX';
  const modifiedRequest = new Request(request, {
    headers: {
      ...Object.fromEntries(request.headers),
      'CF-Connecting-IP': request.headers.get('CF-Connecting-IP'),
      'CF-IPCountry': country,
      'CF-Ray': request.headers.get('CF-Ray'),
      'X-Forwarded-For': request.headers.get('X-Forwarded-For'),
    },
  });

  // Fetch from origin
  const response = await fetch(`${ORIGIN_URL}${path}${url.search}`, modifiedRequest);

  // Cache successful GET responses
  if (request.method === 'GET' && response.status === 200 && cacheTime > 0) {
    const headers = new Headers(response.headers);
    headers.set('Cache-Control', `public, max-age=${cacheTime}`);
    headers.set('CF-Cache-Status', 'HIT');

    const cachedResponse = new Response(response.body, {
      status: response.status,
      statusText: response.statusText,
      headers,
    });

    event.waitUntil(caches.default.put(cacheKey, cachedResponse.clone()));

    return cachedResponse;
  }

  return response;
}

// Rate limiting implementation
async function checkRateLimit(request) {
  const ip = request.headers.get('CF-Connecting-IP') || '127.0.0.1';
  const path = new URL(request.url).pathname;

  // Determine rate limit rules
  let limit = RATE_LIMITS.api;
  if (path.includes('/import')) {
    limit = RATE_LIMITS.import;
  } else if (path.includes('/auth/')) {
    limit = RATE_LIMITS.auth;
  }

  const key = `rate_limit:${ip}:${path}`;
  const current = await getWithExpiry(key);

  if (current >= limit.requests) {
    return new Response(
      JSON.stringify({
        error: 'Rate limit exceeded',
        retryAfter: limit.window,
      }),
      {
        status: 429,
        headers: {
          'Content-Type': 'application/json',
          'X-RateLimit-Limit': limit.requests.toString(),
          'X-RateLimit-Remaining': '0',
          'X-RateLimit-Reset': new Date(Date.now() + limit.window * 1000).toISOString(),
          'Retry-After': limit.window.toString(),
        },
      }
    );
  }

  // Increment counter
  await incrementWithExpiry(key, limit.window);

  return null;
}

// KV store helpers
async function getWithExpiry(key) {
  const value = await RATE_LIMIT_KV.get(key);
  return value ? parseInt(value) : 0;
}

async function incrementWithExpiry(key, ttl) {
  const current = await getWithExpiry(key);
  await RATE_LIMIT_KV.put(key, (current + 1).toString(), {
    expirationTtl: ttl,
  });
}

// SEO file handling
async function handleSEOFiles(request, event) {
  const url = new URL(request.url);
  const file = url.pathname.slice(1);

  // Generate dynamic sitemap
  if (file === 'sitemap.xml') {
    const sitemap = await generateSitemap();
    return new Response(sitemap, {
      headers: {
        'Content-Type': 'application/xml',
        'Cache-Control': 'public, max-age=3600',
      },
    });
  }

  // Robots.txt
  if (file === 'robots.txt') {
    const robots = `
User-agent: *
Allow: /
Disallow: /api/
Disallow: /admin/
Sitemap: https://travelviz.com/sitemap.xml
    `.trim();

    return new Response(robots, {
      headers: {
        'Content-Type': 'text/plain',
        'Cache-Control': 'public, max-age=86400',
      },
    });
  }

  return new Response('Not Found', { status: 404 });
}

async function generateSitemap() {
  // Fetch public trips for sitemap
  const response = await fetch(`${ORIGIN_URL}/api/v1/trips/public?limit=1000`);
  const data = await response.json();

  let sitemap = `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
  <url>
    <loc>https://travelviz.com/</loc>
    <lastmod>${new Date().toISOString()}</lastmod>
    <changefreq>daily</changefreq>
    <priority>1.0</priority>
  </url>
  <url>
    <loc>https://travelviz.com/import</loc>
    <changefreq>weekly</changefreq>
    <priority>0.9</priority>
  </url>`;

  // Add public trips
  if (data.trips) {
    data.trips.forEach(trip => {
      sitemap += `
  <url>
    <loc>https://travelviz.com/trip/${trip.shareId}</loc>
    <lastmod>${trip.updatedAt}</lastmod>
    <changefreq>weekly</changefreq>
    <priority>0.7</priority>
  </url>`;
    });
  }

  sitemap += '\n</urlset>';

  return sitemap;
}

// A/B testing at the edge
async function getABTestVariant(request, experimentId) {
  const cookie = request.headers.get('Cookie');
  const userId = extractUserId(cookie);

  // Check existing assignment
  const key = `ab_test:${experimentId}:${userId}`;
  let variant = await AB_TEST_KV.get(key);

  if (!variant) {
    // Assign variant
    variant = Math.random() < 0.5 ? 'control' : 'variant';
    await AB_TEST_KV.put(key, variant, {
      expirationTtl: 30 * 24 * 60 * 60, // 30 days
    });
  }

  return variant;
}

function extractUserId(cookie) {
  if (!cookie) return 'anonymous-' + Math.random().toString(36).substr(2, 9);

  const match = cookie.match(/user_id=([^;]+)/);
  return match ? match[1] : 'anonymous-' + Math.random().toString(36).substr(2, 9);
}
```

### Multi-Region Deployment Configuration

**File**: `infrastructure/terraform/multi-region.tf`

```hcl
# Terraform configuration for multi-region deployment

terraform {
  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "~> 5.0"
    }
    cloudflare = {
      source  = "cloudflare/cloudflare"
      version = "~> 4.0"
    }
  }
}

# Primary region (US East)
provider "aws" {
  alias  = "us_east_1"
  region = "us-east-1"
}

# Secondary region (EU West)
provider "aws" {
  alias  = "eu_west_1"
  region = "eu-west-1"
}

# Asia Pacific region
provider "aws" {
  alias  = "ap_southeast_1"
  region = "ap-southeast-1"
}

# Global resources
resource "aws_route53_zone" "main" {
  name = "travelviz.com"
}

# DynamoDB Global Table for session data
resource "aws_dynamodb_table" "sessions" {
  provider = aws.us_east_1

  name             = "travelviz-sessions"
  billing_mode     = "PAY_PER_REQUEST"
  hash_key         = "session_id"
  stream_enabled   = true
  stream_view_type = "NEW_AND_OLD_IMAGES"

  attribute {
    name = "session_id"
    type = "S"
  }

  attribute {
    name = "user_id"
    type = "S"
  }

  global_secondary_index {
    name            = "user_id_index"
    hash_key        = "user_id"
    projection_type = "ALL"
  }

  replica {
    region_name = "eu-west-1"
  }

  replica {
    region_name = "ap-southeast-1"
  }

  ttl {
    attribute_name = "ttl"
    enabled        = true
  }
}

# EKS Cluster per region
module "eks_us_east" {
  source = "./modules/eks"

  providers = {
    aws = aws.us_east_1
  }

  cluster_name    = "travelviz-us-east"
  cluster_version = "1.28"
  region          = "us-east-1"

  node_groups = {
    general = {
      desired_capacity = 3
      max_capacity     = 10
      min_capacity     = 3
      instance_types   = ["t3.medium"]
    }

    spot = {
      desired_capacity = 2
      max_capacity     = 20
      min_capacity     = 0
      instance_types   = ["t3.medium", "t3a.medium"]
      capacity_type    = "SPOT"
    }
  }
}

# RDS Aurora Global Database
resource "aws_rds_global_cluster" "main" {
  global_cluster_identifier = "travelviz-global-db"
  engine                    = "aurora-postgresql"
  engine_version           = "15.4"
  database_name            = "travelviz"
  storage_encrypted        = true
}

resource "aws_rds_cluster" "primary" {
  provider = aws.us_east_1

  cluster_identifier      = "travelviz-primary"
  engine                  = aws_rds_global_cluster.main.engine
  engine_version          = aws_rds_global_cluster.main.engine_version
  global_cluster_identifier = aws_rds_global_cluster.main.id
  master_username         = "travelviz_admin"
  master_password         = random_password.db_password.result
  backup_retention_period = 7
  preferred_backup_window = "07:00-09:00"

  enabled_cloudwatch_logs_exports = ["postgresql"]
}

resource "aws_rds_cluster_instance" "primary" {
  provider = aws.us_east_1
  count    = 2

  identifier         = "travelviz-primary-${count.index}"
  cluster_identifier = aws_rds_cluster.primary.id
  instance_class     = "db.r6g.large"
  engine             = aws_rds_cluster.primary.engine
  engine_version     = aws_rds_cluster.primary.engine_version
}

# Read replicas in other regions
resource "aws_rds_cluster" "secondary_eu" {
  provider = aws.eu_west_1

  cluster_identifier        = "travelviz-eu"
  engine                    = aws_rds_global_cluster.main.engine
  engine_version            = aws_rds_global_cluster.main.engine_version
  global_cluster_identifier = aws_rds_global_cluster.main.id

  lifecycle {
    ignore_changes = [replication_source_identifier]
  }
}

# CloudFront distribution
resource "aws_cloudfront_distribution" "main" {
  enabled             = true
  is_ipv6_enabled     = true
  comment             = "TravelViz Global CDN"
  default_root_object = "index.html"

  origin {
    domain_name = aws_lb.alb_us_east.dns_name
    origin_id   = "alb-us-east"

    custom_origin_config {
      http_port              = 80
      https_port             = 443
      origin_protocol_policy = "https-only"
      origin_ssl_protocols   = ["TLSv1.2"]
    }
  }

  origin {
    domain_name = aws_lb.alb_eu_west.dns_name
    origin_id   = "alb-eu-west"

    custom_origin_config {
      http_port              = 80
      https_port             = 443
      origin_protocol_policy = "https-only"
      origin_ssl_protocols   = ["TLSv1.2"]
    }
  }

  # Geolocation routing
  origin_group {
    origin_id = "multi-region-group"

    failover_criteria {
      status_codes = [500, 502, 503, 504]
    }

    member {
      origin_id = "alb-us-east"
    }

    member {
      origin_id = "alb-eu-west"
    }
  }

  default_cache_behavior {
    allowed_methods  = ["DELETE", "GET", "HEAD", "OPTIONS", "PATCH", "POST", "PUT"]
    cached_methods   = ["GET", "HEAD", "OPTIONS"]
    target_origin_id = "multi-region-group"

    forwarded_values {
      query_string = true
      headers      = ["Origin", "Access-Control-Request-Method", "Access-Control-Request-Headers"]

      cookies {
        forward = "whitelist"
        whitelisted_names = ["session_id", "user_preferences"]
      }
    }

    viewer_protocol_policy = "redirect-to-https"
    min_ttl                = 0
    default_ttl            = 300
    max_ttl                = 86400
    compress               = true
  }

  # Static assets with long cache
  ordered_cache_behavior {
    path_pattern     = "/static/*"
    allowed_methods  = ["GET", "HEAD", "OPTIONS"]
    cached_methods   = ["GET", "HEAD", "OPTIONS"]
    target_origin_id = "multi-region-group"

    forwarded_values {
      query_string = false
      headers      = ["Origin"]

      cookies {
        forward = "none"
      }
    }

    viewer_protocol_policy = "redirect-to-https"
    min_ttl                = 0
    default_ttl            = 31536000
    max_ttl                = 31536000
    compress               = true
  }

  price_class = "PriceClass_All"

  restrictions {
    geo_restriction {
      restriction_type = "none"
    }
  }

  viewer_certificate {
    acm_certificate_arn      = aws_acm_certificate.main.arn
    ssl_support_method       = "sni-only"
    minimum_protocol_version = "TLSv1.2_2021"
  }

  web_acl_id = aws_wafv2_web_acl.main.arn
}

# WAF for DDoS protection
resource "aws_wafv2_web_acl" "main" {
  provider = aws.us_east_1

  name  = "travelviz-waf"
  scope = "CLOUDFRONT"

  default_action {
    allow {}
  }

  # Rate limiting rule
  rule {
    name     = "RateLimitRule"
    priority = 1

    statement {
      rate_based_statement {
        limit              = 2000
        aggregate_key_type = "IP"
      }
    }

    action {
      block {}
    }

    visibility_config {
      cloudwatch_metrics_enabled = true
      metric_name                = "RateLimitRule"
      sampled_requests_enabled   = true
    }
  }

  # Geo blocking (if needed)
  rule {
    name     = "GeoBlockingRule"
    priority = 2

    statement {
      geo_match_statement {
        country_codes = ["CN", "RU", "KP"] # Example blocked countries
      }
    }

    action {
      block {}
    }

    visibility_config {
      cloudwatch_metrics_enabled = true
      metric_name                = "GeoBlockingRule"
      sampled_requests_enabled   = true
    }
  }
}

# Route 53 health checks
resource "aws_route53_health_check" "us_east" {
  fqdn              = aws_lb.alb_us_east.dns_name
  port              = 443
  type              = "HTTPS"
  resource_path     = "/health"
  failure_threshold = "3"
  request_interval  = "30"
}

resource "aws_route53_health_check" "eu_west" {
  fqdn              = aws_lb.alb_eu_west.dns_name
  port              = 443
  type              = "HTTPS"
  resource_path     = "/health"
  failure_threshold = "3"
  request_interval  = "30"
}

# DNS records with health check routing
resource "aws_route53_record" "api_us" {
  zone_id = aws_route53_zone.main.zone_id
  name    = "api.travelviz.com"
  type    = "A"

  alias {
    name                   = aws_cloudfront_distribution.main.domain_name
    zone_id                = aws_cloudfront_distribution.main.hosted_zone_id
    evaluate_target_health = false
  }

  set_identifier = "us-east-1"
  health_check_id = aws_route53_health_check.us_east.id

  geolocation_routing_policy {
    continent = "NA"
  }
}

resource "aws_route53_record" "api_eu" {
  zone_id = aws_route53_zone.main.zone_id
  name    = "api.travelviz.com"
  type    = "A"

  alias {
    name                   = aws_cloudfront_distribution.main.domain_name
    zone_id                = aws_cloudfront_distribution.main.hosted_zone_id
    evaluate_target_health = false
  }

  set_identifier = "eu-west-1"
  health_check_id = aws_route53_health_check.eu_west.id

  geolocation_routing_policy {
    continent = "EU"
  }
}

# Outputs
output "cloudfront_domain" {
  value = aws_cloudfront_distribution.main.domain_name
}

output "primary_db_endpoint" {
  value = aws_rds_cluster.primary.endpoint
}

output "health_check_urls" {
  value = {
    us_east = "https://${aws_lb.alb_us_east.dns_name}/health"
    eu_west = "https://${aws_lb.alb_eu_west.dns_name}/health"
  }
}
```

## Queue Architecture for Background Jobs

### BullMQ Advanced Configuration

**File**: `packages/hub/src/queues/queue-config.ts`

```typescript
import { Queue, Worker, QueueScheduler, QueueEvents } from 'bullmq';
import IORedis from 'ioredis';
import { logger } from '../utils/logger';

// Redis cluster configuration for high availability
const redisConnection = new IORedis.Cluster(
  [
    {
      host: process.env.REDIS_NODE1_HOST,
      port: parseInt(process.env.REDIS_NODE1_PORT || '6379'),
    },
    {
      host: process.env.REDIS_NODE2_HOST,
      port: parseInt(process.env.REDIS_NODE2_PORT || '6379'),
    },
    {
      host: process.env.REDIS_NODE3_HOST,
      port: parseInt(process.env.REDIS_NODE3_PORT || '6379'),
    },
  ],
  {
    redisOptions: {
      password: process.env.REDIS_PASSWORD,
      maxRetriesPerRequest: 3,
      enableReadyCheck: true,
      reconnectOnError: err => {
        const targetError = 'READONLY';
        if (err.message.includes(targetError)) {
          // Only reconnect when the error contains "READONLY"
          return true;
        }
        return false;
      },
    },
    clusterRetryStrategy: times => {
      const delay = Math.min(times * 50, 2000);
      return delay;
    },
  }
);

// Queue definitions with specific configurations
export const queues = {
  aiParsing: new Queue('ai-parsing', {
    connection: redisConnection,
    defaultJobOptions: {
      attempts: 3,
      backoff: {
        type: 'exponential',
        delay: 2000,
      },
      removeOnComplete: {
        age: 24 * 3600, // 24 hours
        count: 100,
      },
      removeOnFail: {
        age: 7 * 24 * 3600, // 7 days
      },
    },
  }),

  email: new Queue('email', {
    connection: redisConnection,
    defaultJobOptions: {
      attempts: 5,
      backoff: {
        type: 'fixed',
        delay: 5000,
      },
    },
  }),

  imageProcessing: new Queue('image-processing', {
    connection: redisConnection,
    defaultJobOptions: {
      attempts: 3,
      timeout: 30000, // 30 seconds
    },
  }),

  dataExport: new Queue('data-export', {
    connection: redisConnection,
    defaultJobOptions: {
      attempts: 2,
      timeout: 300000, // 5 minutes
    },
  }),

  webhooks: new Queue('webhooks', {
    connection: redisConnection,
    defaultJobOptions: {
      attempts: 3,
      backoff: {
        type: 'exponential',
        delay: 1000,
      },
    },
  }),

  analytics: new Queue('analytics', {
    connection: redisConnection,
    defaultJobOptions: {
      removeOnComplete: true,
      removeOnFail: false,
    },
  }),

  cleanup: new Queue('cleanup', {
    connection: redisConnection,
  }),
};

// Queue schedulers for delayed jobs
export const schedulers = {
  aiParsing: new QueueScheduler('ai-parsing', { connection: redisConnection }),
  email: new QueueScheduler('email', { connection: redisConnection }),
  cleanup: new QueueScheduler('cleanup', { connection: redisConnection }),
};

// Queue event listeners for monitoring
export const queueEvents = new QueueEvents('ai-parsing', { connection: redisConnection });

queueEvents.on('completed', ({ jobId, returnvalue }) => {
  logger.info(`Job ${jobId} completed with result:`, returnvalue);
});

queueEvents.on('failed', ({ jobId, failedReason }) => {
  logger.error(`Job ${jobId} failed with reason:`, failedReason);
  // Send alert to monitoring
});

// Advanced job processors with concurrency control
export function createWorkers() {
  // AI Parsing Worker - Limited concurrency to prevent API rate limits
  const aiParsingWorker = new Worker(
    'ai-parsing',
    async job => {
      const { userId, tripId, content, model } = job.data;

      // Update progress
      await job.updateProgress(10);

      // Implement circuit breaker for AI service
      const circuitBreaker = await checkAIServiceHealth();
      if (!circuitBreaker.isOpen) {
        throw new Error('AI service circuit breaker is open');
      }

      // Process with rate limiting
      const result = await processAIParsing(job.data);

      // Store result in cache for quick retrieval
      await cacheResult(tripId, result);

      return result;
    },
    {
      connection: redisConnection,
      concurrency: 5, // Process 5 jobs concurrently
      limiter: {
        max: 10,
        duration: 60000, // 10 jobs per minute
      },
    }
  );

  // Email Worker - Batch processing for efficiency
  const emailWorker = new Worker(
    'email',
    async job => {
      const { to, subject, template, data } = job.data;

      // Batch similar emails
      const batch = await collectEmailBatch(template, 100, 5000); // 100 emails or 5 seconds

      if (batch.length > 1) {
        // Use batch sending API
        return await sendBatchEmails(batch);
      } else {
        // Send single email
        return await sendEmail(job.data);
      }
    },
    {
      connection: redisConnection,
      concurrency: 10,
    }
  );

  // Image Processing Worker - CPU intensive
  const imageWorker = new Worker(
    'image-processing',
    async job => {
      const { imageUrl, operations } = job.data;

      // Download image
      const imageBuffer = await downloadImage(imageUrl);

      // Process in worker thread to avoid blocking
      const processed = await processImageInWorker(imageBuffer, operations);

      // Upload to CDN
      const cdnUrl = await uploadToCDN(processed);

      return { cdnUrl };
    },
    {
      connection: redisConnection,
      concurrency: 2, // Limit CPU-intensive work
      useWorkerThreads: true,
    }
  );

  // Cleanup Worker - Scheduled maintenance
  const cleanupWorker = new Worker(
    'cleanup',
    async job => {
      const { type, olderThan } = job.data;

      switch (type) {
        case 'orphaned-files':
          return await cleanupOrphanedFiles(olderThan);
        case 'expired-sessions':
          return await cleanupExpiredSessions(olderThan);
        case 'old-analytics':
          return await cleanupOldAnalytics(olderThan);
        case 'failed-jobs':
          return await cleanupFailedJobs(olderThan);
        default:
          throw new Error(`Unknown cleanup type: ${type}`);
      }
    },
    {
      connection: redisConnection,
      concurrency: 1,
    }
  );

  return {
    aiParsingWorker,
    emailWorker,
    imageWorker,
    cleanupWorker,
  };
}

// Job scheduling for recurring tasks
export async function scheduleRecurringJobs() {
  // Daily cleanup at 3 AM
  await queues.cleanup.add(
    'daily-cleanup',
    {
      type: 'orphaned-files',
      olderThan: 7 * 24 * 60 * 60 * 1000, // 7 days
    },
    {
      repeat: {
        pattern: '0 3 * * *', // Cron pattern
      },
    }
  );

  // Hourly analytics aggregation
  await queues.analytics.add(
    'aggregate-hourly',
    {},
    {
      repeat: {
        every: 60 * 60 * 1000, // Every hour
      },
    }
  );

  // Weekly data export for backups
  await queues.dataExport.add(
    'weekly-backup',
    {
      type: 'full',
      destination: 's3',
    },
    {
      repeat: {
        pattern: '0 0 * * 0', // Sunday at midnight
      },
    }
  );
}

// Priority job handling
export async function addPriorityJob(queue: Queue, jobName: string, data: any, priority: number) {
  return await queue.add(jobName, data, {
    priority,
    lifo: priority > 100, // Last-in-first-out for high priority
  });
}

// Bulk job operations
export async function addBulkJobs(
  queue: Queue,
  jobs: Array<{ name: string; data: any; opts?: any }>
) {
  const jobsToAdd = jobs.map(job => ({
    name: job.name,
    data: job.data,
    opts: {
      ...queue.defaultJobOptions,
      ...job.opts,
    },
  }));

  return await queue.addBulk(jobsToAdd);
}

// Job flow orchestration
export async function createJobFlow(steps: Array<{ queue: Queue; data: any }>) {
  let previousJobId: string | undefined;

  for (const step of steps) {
    const job = await step.queue.add(
      'flow-step',
      {
        ...step.data,
        previousJobId,
      },
      {
        delay: previousJobId ? 0 : undefined,
        parent: previousJobId ? { id: previousJobId, queue: step.queue.name } : undefined,
      }
    );

    previousJobId = job.id;
  }

  return previousJobId;
}

// Circuit breaker for external services
class CircuitBreaker {
  private failures = 0;
  private lastFailureTime = 0;
  private state: 'closed' | 'open' | 'half-open' = 'closed';

  constructor(
    private threshold = 5,
    private timeout = 60000 // 1 minute
  ) {}

  get isOpen() {
    if (this.state === 'open') {
      if (Date.now() - this.lastFailureTime > this.timeout) {
        this.state = 'half-open';
      }
    }
    return this.state !== 'open';
  }

  recordSuccess() {
    this.failures = 0;
    this.state = 'closed';
  }

  recordFailure() {
    this.failures++;
    this.lastFailureTime = Date.now();

    if (this.failures >= this.threshold) {
      this.state = 'open';
      logger.error('Circuit breaker opened due to failures');
    }
  }
}

const aiServiceCircuitBreaker = new CircuitBreaker();

async function checkAIServiceHealth() {
  try {
    // Health check implementation
    const response = await fetch(`${process.env.AI_SERVICE_URL}/health`);
    if (response.ok) {
      aiServiceCircuitBreaker.recordSuccess();
    } else {
      aiServiceCircuitBreaker.recordFailure();
    }
  } catch (error) {
    aiServiceCircuitBreaker.recordFailure();
  }

  return aiServiceCircuitBreaker;
}

// Helper functions
async function processAIParsing(data: any) {
  // Implementation
}

async function cacheResult(key: string, data: any) {
  await redisConnection.setex(`result:${key}`, 3600, JSON.stringify(data));
}

async function collectEmailBatch(template: string, maxSize: number, maxWait: number) {
  // Implementation
  return [];
}

async function sendBatchEmails(batch: any[]) {
  // Implementation
}

async function sendEmail(data: any) {
  // Implementation
}

async function downloadImage(url: string) {
  // Implementation
  return Buffer.from('');
}

async function processImageInWorker(buffer: Buffer, operations: any) {
  // Implementation
  return buffer;
}

async function uploadToCDN(buffer: Buffer) {
  // Implementation
  return 'https://cdn.travelviz.com/image.jpg';
}

// Cleanup implementations
async function cleanupOrphanedFiles(olderThan: number) {
  // Implementation
}

async function cleanupExpiredSessions(olderThan: number) {
  // Implementation
}

async function cleanupOldAnalytics(olderThan: number) {
  // Implementation
}

async function cleanupFailedJobs(olderThan: number) {
  // Implementation
}
```

## Disaster Recovery Plan

### Backup and Recovery Strategy

**File**: `infrastructure/disaster-recovery/backup-strategy.md`

````markdown
# TravelViz Disaster Recovery Plan

## Recovery Objectives

### RTO (Recovery Time Objective): 4 hours

- Time to restore service after disaster
- Includes detection, decision, and recovery

### RPO (Recovery Point Objective): 1 hour

- Maximum data loss tolerance
- Achieved through continuous replication

## Backup Strategy

### 1. Database Backups

#### Automated Backups

- **Frequency**: Every 6 hours
- **Retention**: 30 days
- **Type**: Full backup with transaction logs
- **Storage**: Cross-region S3 buckets with lifecycle policies

#### Point-in-Time Recovery (PITR)

- **Enabled**: Yes
- **Recovery Window**: Up to 5 minutes before failure
- **Transaction Log Backup**: Every 5 minutes

#### Backup Verification

```bash
#!/bin/bash
# Daily backup verification script

# Test restore to verification instance
aws rds restore-db-instance-from-db-snapshot \
  --db-instance-identifier travelviz-verify-$(date +%Y%m%d) \
  --db-snapshot-identifier $(aws rds describe-db-snapshots \
    --query 'DBSnapshots[0].DBSnapshotIdentifier' --output text)

# Wait for restore
aws rds wait db-instance-available \
  --db-instance-identifier travelviz-verify-$(date +%Y%m%d)

# Run integrity checks
psql -h travelviz-verify-$(date +%Y%m%d).region.rds.amazonaws.com \
  -U admin -d travelviz -c "SELECT COUNT(*) FROM trips;"

# Clean up verification instance
aws rds delete-db-instance \
  --db-instance-identifier travelviz-verify-$(date +%Y%m%d) \
  --skip-final-snapshot
```
````

### 2. Application State Backups

#### Redis Snapshots

- **Frequency**: Every hour
- **Type**: RDB snapshots + AOF logs
- **Storage**: S3 with encryption
- **Retention**: 7 days

#### Session State

- **Method**: DynamoDB Global Tables
- **Replication**: Multi-region active-active
- **Backup**: Daily DynamoDB backups

### 3. File Storage Backups

#### User Uploads

- **Primary**: S3 with versioning enabled
- **Replication**: Cross-region replication
- **Lifecycle**: Move to Glacier after 90 days

#### Static Assets

- **Method**: Git repository + CDN
- **Backup**: Multiple git remotes
- **Recovery**: Rebuild from source

## Disaster Scenarios & Recovery Procedures

### Scenario 1: Database Failure

#### Detection

- CloudWatch alarms on connection failures
- Health check failures
- Application errors

#### Recovery Steps

1. **Immediate**: Failover to read replica (automatic)
2. **Verify**: Check data consistency
3. **Promote**: Make replica primary
4. **Rebuild**: Create new replicas
5. **Update**: DNS and connection strings

```bash
# Automated failover script
#!/bin/bash

REGION=$1
CLUSTER_ID="travelviz-cluster"

# Promote read replica
aws rds promote-read-replica-db-cluster \
  --db-cluster-identifier $CLUSTER_ID \
  --region $REGION

# Update Route53
aws route53 change-resource-record-sets \
  --hosted-zone-id $ZONE_ID \
  --change-batch file://failover-dns.json

# Notify team
aws sns publish \
  --topic-arn $ALERT_TOPIC \
  --message "Database failover completed to $REGION"
```

### Scenario 2: Region Failure

#### Detection

- Multi-region health checks
- Route53 health check failures
- CloudWatch cross-region alarms

#### Recovery Steps

1. **Traffic**: Route53 automatically redirects
2. **Verify**: Check secondary region health
3. **Scale**: Increase capacity in healthy regions
4. **Communicate**: Update status page
5. **Monitor**: Watch for cascading failures

### Scenario 3: Data Corruption

#### Detection

- Data validation failures
- User reports
- Automated integrity checks

#### Recovery Steps

1. **Isolate**: Stop writes to affected data
2. **Identify**: Find corruption timestamp
3. **Restore**: PITR to before corruption
4. **Replay**: Re-apply valid transactions
5. **Verify**: Run integrity checks

```typescript
// Data integrity checker
async function verifyDataIntegrity() {
  const checks = [
    checkTripDates(),
    checkActivityReferences(),
    checkUserData(),
    checkFinancialRecords(),
  ];

  const results = await Promise.all(checks);

  const failures = results.filter(r => !r.success);
  if (failures.length > 0) {
    await notifyOps(failures);
    await initiateRecovery(failures);
  }
}

async function checkTripDates() {
  const invalid = await db.query(`
    SELECT COUNT(*) as count
    FROM trips
    WHERE start_date > end_date
    OR start_date < '2020-01-01'
    OR end_date > CURRENT_DATE + INTERVAL '2 years'
  `);

  return {
    check: 'trip_dates',
    success: invalid.rows[0].count === 0,
    details: invalid.rows[0],
  };
}
```

### Scenario 4: Security Breach

#### Detection

- WAF alerts
- Unusual API patterns
- Failed auth spike
- Data exfiltration patterns

#### Recovery Steps

1. **Contain**: Block suspicious IPs/users
2. **Assess**: Determine breach scope
3. **Revoke**: Invalidate all sessions
4. **Rotate**: Change all credentials
5. **Audit**: Review all recent changes
6. **Notify**: Inform affected users

```typescript
// Emergency security response
async function securityLockdown(threatLevel: 'low' | 'medium' | 'high' | 'critical') {
  logger.alert(`Security lockdown initiated: ${threatLevel}`);

  if (threatLevel === 'critical') {
    // Immediate actions
    await revokeAllSessions();
    await enableReadOnlyMode();
    await blockAllNewRegistrations();
  }

  // Rotate credentials
  await rotateAPIKeys();
  await rotateDBPasswords();
  await invalidateAllTokens();

  // Enable enhanced monitoring
  await enableSecurityAuditMode();

  // Notify
  await notifySecurityTeam(threatLevel);
  await updateStatusPage('security-maintenance');
}
```

## Runbooks

### Database Failover Runbook

1. **Alert Receipt** (0-5 min)
   - PagerDuty alert triggered
   - On-call engineer acknowledges
   - Open incident channel

2. **Assessment** (5-15 min)
   - Check primary DB status
   - Verify replica health
   - Assess data lag

3. **Decision** (15-20 min)
   - If primary is unrecoverable → proceed
   - If temporary issue → wait and monitor
   - Get approval for failover

4. **Execution** (20-30 min)

   ```bash
   ./scripts/db-failover.sh production us-west-2
   ```

5. **Verification** (30-45 min)
   - Test application connectivity
   - Verify data integrity
   - Check performance metrics

6. **Communication** (45-60 min)
   - Update status page
   - Notify customers if needed
   - Document incident

### Full Region Failover Runbook

1. **Detection & Triage** (0-10 min)
   - Multiple service alerts
   - Confirm region-wide issue
   - Initiate emergency response

2. **Traffic Redirect** (10-20 min)

   ```bash
   ./scripts/region-failover.sh us-east-1 eu-west-1
   ```

3. **Service Verification** (20-40 min)
   - Check all services in new region
   - Verify data synchronization
   - Test critical user paths

4. **Capacity Scaling** (40-60 min)
   - Scale up secondary region
   - Monitor resource usage
   - Adjust auto-scaling policies

5. **Stabilization** (60-90 min)
   - Monitor error rates
   - Check performance metrics
   - Ensure stable operation

## Testing Schedule

### Monthly Tests

- Backup restoration (random sample)
- Read replica promotion
- Cache failover
- Queue recovery

### Quarterly Tests

- Full region failover
- Data corruption recovery
- Security incident response
- Complete disaster recovery

### Annual Tests

- Multi-region failure simulation
- Complete data center loss
- Extended outage recovery
- Vendor failure scenarios

## Contact Information

### Escalation Path

1. On-call Engineer
2. Team Lead
3. VP Engineering
4. CTO
5. CEO

### External Contacts

- AWS Support: [Premium support number]
- Cloudflare: [Enterprise support]
- PagerDuty: [Account manager]
- Security Team: [24/7 hotline]

## Recovery Metrics Tracking

```sql
-- Track recovery performance
CREATE TABLE disaster_recovery_metrics (
  incident_id UUID PRIMARY KEY,
  incident_type VARCHAR(50),
  detection_time TIMESTAMP,
  acknowledgment_time TIMESTAMP,
  recovery_start_time TIMESTAMP,
  recovery_end_time TIMESTAMP,
  data_loss_minutes INTEGER,
  affected_users INTEGER,
  root_cause TEXT,
  lessons_learned TEXT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Calculate metrics
SELECT
  incident_type,
  AVG(EXTRACT(EPOCH FROM (recovery_end_time - detection_time))/3600) as avg_rto_hours,
  MAX(data_loss_minutes) as max_rpo_minutes,
  COUNT(*) as incident_count
FROM disaster_recovery_metrics
WHERE created_at > NOW() - INTERVAL '1 year'
GROUP BY incident_type;
```

## Continuous Improvement

### Post-Incident Review

- Conduct within 48 hours
- Document timeline
- Identify improvement areas
- Update runbooks
- Schedule remediation

### Metrics Goals

- Reduce RTO by 10% quarterly
- Maintain RPO under 1 hour
- 100% successful monthly tests
- Zero data loss incidents

### Investment Areas

- Automated recovery tools
- Improved monitoring
- Faster replication
- Better testing frameworks
- Team training

````

## Day 13 Morning: Database Optimization (4 hours)

### Task 1: Query Performance Audit (1.5 hours)

**File**: `packages/hub/src/scripts/analyze-query-performance.ts`

```typescript
import { createClient } from '@supabase/supabase-js';
import chalk from 'chalk';

const supabase = createClient(
  process.env.SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_KEY!
);

interface QueryAnalysis {
  query: string;
  avgTime: number;
  calls: number;
  totalTime: number;
  suggestion?: string;
}

async function analyzeQueryPerformance() {
  console.log(chalk.blue('🔍 Analyzing Query Performance...\n'));

  // Get slow queries from pg_stat_statements
  const { data: slowQueries } = await supabase.rpc('get_slow_queries', {
    min_mean_time_ms: 100
  });

  const analyses: QueryAnalysis[] = [];

  // Common N+1 patterns to detect
  const n1Patterns = [
    {
      pattern: /SELECT .+ FROM trips WHERE user_id = \$1 AND id = \$2/i,
      issue: 'N+1: Loading trips one by one',
      fix: 'Use JOIN or batch load with IN clause'
    },
    {
      pattern: /SELECT .+ FROM activities WHERE trip_id = \$1$/i,
      issue: 'N+1: Loading activities per trip',
      fix: 'Use trips.include({ activities: true }) in query'
    },
    {
      pattern: /SELECT .+ FROM users WHERE id = \$1$/i,
      issue: 'N+1: Loading user per entity',
      fix: 'Cache user data or JOIN in main query'
    }
  ];

  // Missing index patterns
  const indexPatterns = [
    {
      pattern: /WHERE .+ LIKE '%[^']+'%/i,
      issue: 'Full table scan with LIKE',
      fix: 'Add GIN index for full-text search'
    },
    {
      pattern: /ORDER BY created_at/i,
      issue: 'Sorting without index',
      fix: 'Add index on created_at DESC'
    },
    {
      pattern: /WHERE metadata->>/i,
      issue: 'JSON field query without index',
      fix: 'Add GIN index on metadata'
    }
  ];

  // Analyze each slow query
  for (const query of slowQueries || []) {
    const analysis: QueryAnalysis = {
      query: query.query,
      avgTime: query.mean_time,
      calls: query.calls,
      totalTime: query.total_time
    };

    // Check for N+1 queries
    for (const n1 of n1Patterns) {
      if (n1.pattern.test(query.query)) {
        analysis.suggestion = `${n1.issue}. Fix: ${n1.fix}`;
        break;
      }
    }

    // Check for missing indexes
    if (!analysis.suggestion) {
      for (const idx of indexPatterns) {
        if (idx.pattern.test(query.query)) {
          analysis.suggestion = `${idx.issue}. Fix: ${idx.fix}`;
          break;
        }
      }
    }

    analyses.push(analysis);
  }

  // Generate report
  console.log(chalk.red('⚠️  Critical Issues:\n'));

  const critical = analyses.filter(a => a.avgTime > 500);
  for (const issue of critical) {
    console.log(chalk.red(`Query taking ${issue.avgTime}ms (${issue.calls} calls)`));
    console.log(chalk.gray(issue.query.substring(0, 100) + '...'));
    if (issue.suggestion) {
      console.log(chalk.yellow(`Fix: ${issue.suggestion}`));
    }
    console.log();
  }

  // Optimization script
  const optimizations = generateOptimizations(analyses);

  console.log(chalk.green('\n✅ Run these optimizations:\n'));
  console.log(optimizations);

  return analyses;
}

function generateOptimizations(analyses: QueryAnalysis[]): string {
  const indexes = new Set<string>();

  // Collect needed indexes
  for (const analysis of analyses) {
    if (analysis.query.includes('trips') && analysis.query.includes('user_id')) {
      indexes.add('CREATE INDEX IF NOT EXISTS idx_trips_user_created ON trips(user_id, created_at DESC);');
    }
    if (analysis.query.includes('activities') && analysis.query.includes('trip_id')) {
      indexes.add('CREATE INDEX IF NOT EXISTS idx_activities_trip_day ON activities(trip_id, day_number, start_time);');
    }
    if (analysis.query.includes('metadata')) {
      indexes.add('CREATE INDEX IF NOT EXISTS idx_trips_metadata ON trips USING GIN(metadata);');
    }
  }

  return Array.from(indexes).join('\n');
}
````

### Task 2: Connection Pooling & Caching (1.5 hours)

**File**: `packages/hub/src/config/cache.config.ts`

```typescript
import { Redis } from '@upstash/redis';
import { LRUCache } from 'lru-cache';

// Upstash Redis for distributed cache
export const redis = new Redis({
  url: process.env.UPSTASH_REDIS_REST_URL!,
  token: process.env.UPSTASH_REDIS_REST_TOKEN!,
});

// Local LRU cache for hot data
export const localCache = new LRUCache<string, any>({
  max: 500, // 500 items max
  ttl: 1000 * 60 * 5, // 5 minutes
  updateAgeOnGet: true,
  updateAgeOnHas: true,
});

// Cache utilities
export const cache = {
  async get<T>(key: string): Promise<T | null> {
    // Try local first
    const local = localCache.get(key);
    if (local) return local as T;

    // Try Redis
    try {
      const remote = await redis.get(key);
      if (remote) {
        localCache.set(key, remote);
        return remote as T;
      }
    } catch (error) {
      console.error('Redis error:', error);
    }

    return null;
  },

  async set(key: string, value: any, ttl?: number): Promise<void> {
    // Set in both caches
    localCache.set(key, value);

    try {
      if (ttl) {
        await redis.set(key, value, { ex: ttl });
      } else {
        await redis.set(key, value);
      }
    } catch (error) {
      console.error('Redis error:', error);
    }
  },

  async delete(key: string): Promise<void> {
    localCache.delete(key);
    try {
      await redis.del(key);
    } catch (error) {
      console.error('Redis error:', error);
    }
  },

  // Pattern-based deletion
  async deletePattern(pattern: string): Promise<void> {
    // Clear local cache entries matching pattern
    for (const key of localCache.keys()) {
      if (key.includes(pattern)) {
        localCache.delete(key);
      }
    }

    // Clear Redis entries
    try {
      const keys = await redis.keys(pattern);
      if (keys.length > 0) {
        await redis.del(...keys);
      }
    } catch (error) {
      console.error('Redis error:', error);
    }
  },
};

// Connection pool configuration
export const poolConfig = {
  // Supabase connection pooling
  db: {
    max: 20, // Maximum connections
    idleTimeoutMillis: 30000,
    connectionTimeoutMillis: 2000,
  },

  // HTTP agent pooling
  http: {
    keepAlive: true,
    keepAliveMsecs: 30000,
    maxSockets: 100,
    maxFreeSockets: 10,
    timeout: 60000,
  },
};
```

**File**: `packages/hub/src/middleware/cache.middleware.ts`

```typescript
import { Request, Response, NextFunction } from 'express';
import { cache } from '../config/cache.config';
import crypto from 'crypto';

interface CacheOptions {
  ttl?: number; // Time to live in seconds
  key?: (req: Request) => string; // Custom key generator
  condition?: (req: Request) => boolean; // When to cache
  invalidateOn?: string[]; // Routes that invalidate this cache
}

export function cacheMiddleware(options: CacheOptions = {}) {
  const {
    ttl = 300, // 5 minutes default
    key = defaultKeyGenerator,
    condition = () => true,
    invalidateOn = [],
  } = options;

  return async (req: Request, res: Response, next: NextFunction) => {
    // Skip caching for non-GET requests
    if (req.method !== 'GET' || !condition(req)) {
      return next();
    }

    const cacheKey = key(req);

    // Try to get from cache
    const cached = await cache.get(cacheKey);
    if (cached) {
      res.setHeader('X-Cache', 'HIT');
      res.setHeader('X-Cache-Key', cacheKey);
      return res.json(cached);
    }

    // Store original json method
    const originalJson = res.json.bind(res);

    // Override json method to cache response
    res.json = function (data: any) {
      // Cache successful responses only
      if (res.statusCode >= 200 && res.statusCode < 300) {
        cache.set(cacheKey, data, ttl).catch(console.error);
      }

      res.setHeader('X-Cache', 'MISS');
      res.setHeader('X-Cache-Key', cacheKey);

      return originalJson(data);
    };

    next();
  };
}

// Default cache key generator
function defaultKeyGenerator(req: Request): string {
  const userId = req.user?.id || 'anonymous';
  const query = JSON.stringify(req.query);
  const path = req.path;

  return `cache:${userId}:${path}:${crypto.createHash('md5').update(query).digest('hex')}`;
}

// Cache invalidation middleware
export function invalidateCache(patterns: string[]) {
  return async (req: Request, res: Response, next: NextFunction) => {
    // After successful mutation, invalidate related caches
    const originalJson = res.json.bind(res);

    res.json = function (data: any) {
      if (res.statusCode >= 200 && res.statusCode < 300) {
        // Invalidate cache patterns
        Promise.all(
          patterns.map(pattern => {
            const key = pattern.replace(':userId', req.user?.id || '');
            return cache.deletePattern(key);
          })
        ).catch(console.error);
      }

      return originalJson(data);
    };

    next();
  };
}

// Specific cache configurations
export const cacheConfigs = {
  // Public trip pages - cache for 1 hour
  publicTrip: cacheMiddleware({
    ttl: 3600,
    key: req => `public:trip:${req.params.shareId}`,
  }),

  // User trips list - cache for 5 minutes
  userTrips: cacheMiddleware({
    ttl: 300,
    key: req => `user:${req.user?.id}:trips:${req.query.page || 1}`,
  }),

  // Trip details - cache for 10 minutes
  tripDetails: cacheMiddleware({
    ttl: 600,
    key: req => `trip:${req.params.tripId}:details`,
  }),

  // Search results - cache for 15 minutes
  search: cacheMiddleware({
    ttl: 900,
    key: req =>
      `search:${crypto.createHash('md5').update(JSON.stringify(req.query)).digest('hex')}`,
  }),
};
```

### Task 3: Database Optimization Migrations (1 hour)

**File**: `packages/hub/src/migrations/014_performance_indexes.sql`

```sql
BEGIN;

-- Composite indexes for common queries
CREATE INDEX IF NOT EXISTS idx_trips_user_created
  ON trips(user_id, created_at DESC)
  WHERE deleted_at IS NULL;

CREATE INDEX IF NOT EXISTS idx_activities_trip_day
  ON activities(trip_id, day_number, start_time);

CREATE INDEX IF NOT EXISTS idx_trips_public_share
  ON trips(share_id)
  WHERE is_public = true;

-- GIN indexes for JSON and full-text search
CREATE INDEX IF NOT EXISTS idx_trips_metadata
  ON trips USING GIN(metadata);

CREATE INDEX IF NOT EXISTS idx_activities_search
  ON activities USING GIN(
    to_tsvector('english', name || ' ' || COALESCE(description, ''))
  );

-- Partial indexes for common filters
CREATE INDEX IF NOT EXISTS idx_trips_active_pro
  ON trips(user_id, created_at DESC)
  WHERE deleted_at IS NULL
  AND (metadata->>'subscription_status' = 'pro');

-- Foreign key indexes (often missed)
CREATE INDEX IF NOT EXISTS idx_activities_trip_id
  ON activities(trip_id);

CREATE INDEX IF NOT EXISTS idx_payment_history_user
  ON payment_history(user_id, created_at DESC);

-- Analyze tables for query planner
ANALYZE trips;
ANALYZE activities;
ANALYZE auth.users;

-- Create materialized view for expensive aggregations
CREATE MATERIALIZED VIEW IF NOT EXISTS user_trip_stats AS
SELECT
  u.id as user_id,
  COUNT(DISTINCT t.id) as total_trips,
  COUNT(DISTINCT CASE WHEN t.created_at > NOW() - INTERVAL '30 days'
    THEN t.id END) as trips_last_30_days,
  COUNT(DISTINCT a.id) as total_activities,
  AVG(t.metadata->>'confidence')::FLOAT as avg_confidence,
  MAX(t.created_at) as last_trip_created
FROM auth.users u
LEFT JOIN trips t ON t.user_id = u.id AND t.deleted_at IS NULL
LEFT JOIN activities a ON a.trip_id = t.id
GROUP BY u.id;

-- Index for fast lookups
CREATE UNIQUE INDEX idx_user_trip_stats_user
  ON user_trip_stats(user_id);

-- Refresh function
CREATE OR REPLACE FUNCTION refresh_user_trip_stats()
RETURNS void AS $$
BEGIN
  REFRESH MATERIALIZED VIEW CONCURRENTLY user_trip_stats;
END;
$$ LANGUAGE plpgsql;

-- Schedule refresh (requires pg_cron extension)
-- SELECT cron.schedule('refresh-user-stats', '*/10 * * * *',
--   'SELECT refresh_user_trip_stats()');

COMMIT;
```

## Day 13 Afternoon: API Rate Limiting & Queuing (4 hours)

### Task 4: Rate Limiting Implementation (1.5 hours)

**File**: `packages/hub/src/middleware/rate-limit.middleware.ts`

```typescript
import { Request, Response, NextFunction } from 'express';
import { RateLimiterRedis, RateLimiterMemory } from 'rate-limiter-flexible';
import { redis } from '../config/cache.config';

// Different rate limit tiers
const rateLimitTiers = {
  // Public endpoints
  public: {
    points: 60, // requests
    duration: 60, // per minute
    blockDuration: 60, // block for 1 minute
  },

  // Authenticated free users
  free: {
    points: 100,
    duration: 60,
    blockDuration: 60,
  },

  // Pro users
  pro: {
    points: 500,
    duration: 60,
    blockDuration: 30,
  },

  // AI parsing endpoint (expensive)
  aiParsing: {
    points: 10, // 10 parses
    duration: 3600, // per hour
    blockDuration: 3600, // block for 1 hour
  },

  // Auth endpoints (prevent brute force)
  auth: {
    points: 5,
    duration: 900, // 15 minutes
    blockDuration: 900,
  },
};

// Create rate limiters
const limiters: Record<string, RateLimiterRedis | RateLimiterMemory> = {};

// Initialize rate limiters
for (const [name, config] of Object.entries(rateLimitTiers)) {
  try {
    limiters[name] = new RateLimiterRedis({
      storeClient: redis,
      keyPrefix: `rl:${name}:`,
      ...config,
    });
  } catch (error) {
    // Fallback to memory if Redis fails
    console.warn(`Failed to create Redis limiter for ${name}, using memory`);
    limiters[name] = new RateLimiterMemory(config);
  }
}

// Rate limit middleware factory
export function rateLimit(
  tier: keyof typeof rateLimitTiers = 'public',
  customKey?: (req: Request) => string
) {
  return async (req: Request, res: Response, next: NextFunction) => {
    try {
      // Determine rate limit key
      const key = customKey ? customKey(req) : getDefaultKey(req);

      // Get user's tier if authenticated
      const userTier = getUserTier(req);
      const limiterName = tier === 'public' ? userTier : tier;
      const limiter = limiters[limiterName];

      // Consume a point
      const rateLimitRes = await limiter.consume(key);

      // Set rate limit headers
      res.setHeader('X-RateLimit-Limit', rateLimitTiers[limiterName].points);
      res.setHeader('X-RateLimit-Remaining', rateLimitRes.remainingPoints);
      res.setHeader(
        'X-RateLimit-Reset',
        new Date(Date.now() + rateLimitRes.msBeforeNext).toISOString()
      );

      next();
    } catch (rejRes: any) {
      // Rate limit exceeded
      res.setHeader('X-RateLimit-Limit', rejRes.totalPoints);
      res.setHeader('X-RateLimit-Remaining', rejRes.remainingPoints || 0);
      res.setHeader('X-RateLimit-Reset', new Date(Date.now() + rejRes.msBeforeNext).toISOString());
      res.setHeader('Retry-After', Math.round(rejRes.msBeforeNext / 1000));

      res.status(429).json({
        error: 'Too Many Requests',
        message: `Rate limit exceeded. Try again in ${Math.round(rejRes.msBeforeNext / 1000)} seconds.`,
        retryAfter: rejRes.msBeforeNext,
      });
    }
  };
}

// Get default rate limit key
function getDefaultKey(req: Request): string {
  // For authenticated users, use user ID
  if (req.user?.id) {
    return req.user.id;
  }

  // For public endpoints, use IP
  return req.ip || req.socket.remoteAddress || 'unknown';
}

// Get user's rate limit tier
function getUserTier(req: Request): keyof typeof rateLimitTiers {
  if (!req.user) return 'public';

  const subscription = req.user.subscription_status;
  if (subscription === 'pro' || subscription === 'pro_trial') {
    return 'pro';
  }

  return 'free';
}

// Specific rate limiters
export const rateLimiters = {
  api: rateLimit('public'),
  auth: rateLimit('auth'),
  aiParsing: rateLimit('aiParsing'),

  // Custom limiter for expensive operations
  expensive: rateLimit('free', req => {
    // Rate limit by user + operation
    const userId = req.user?.id || req.ip;
    const operation = req.path;
    return `${userId}:${operation}`;
  }),
};
```

### Task 5: Job Queue Implementation (1.5 hours)

**File**: `packages/hub/src/services/queue.service.ts`

```typescript
import Bull from 'bull';
import { redis } from '../config/cache.config';

// Queue types
export enum QueueName {
  AI_PARSING = 'ai-parsing',
  EMAIL = 'email',
  ANALYTICS = 'analytics',
  CLEANUP = 'cleanup',
}

// Job interfaces
export interface AIParsingJob {
  userId: string;
  tripId: string;
  content: string;
  model?: string;
}

export interface EmailJob {
  to: string;
  subject: string;
  template: string;
  data: Record<string, any>;
}

// Create queues
const queues: Record<QueueName, Bull.Queue> = {
  [QueueName.AI_PARSING]: new Bull(QueueName.AI_PARSING, {
    redis: process.env.UPSTASH_REDIS_URL!,
    defaultJobOptions: {
      attempts: 3,
      backoff: {
        type: 'exponential',
        delay: 2000,
      },
      removeOnComplete: 100,
      removeOnFail: 50,
    },
  }),

  [QueueName.EMAIL]: new Bull(QueueName.EMAIL, {
    redis: process.env.UPSTASH_REDIS_URL!,
    defaultJobOptions: {
      attempts: 5,
      backoff: {
        type: 'fixed',
        delay: 5000,
      },
    },
  }),

  [QueueName.ANALYTICS]: new Bull(QueueName.ANALYTICS, {
    redis: process.env.UPSTASH_REDIS_URL!,
    defaultJobOptions: {
      removeOnComplete: true,
      removeOnFail: false,
    },
  }),

  [QueueName.CLEANUP]: new Bull(QueueName.CLEANUP, {
    redis: process.env.UPSTASH_REDIS_URL!,
  }),
};

// Queue service
export const queueService = {
  // Add job to queue
  async addJob<T>(queueName: QueueName, data: T, options?: Bull.JobOptions): Promise<Bull.Job<T>> {
    const queue = queues[queueName];
    return await queue.add(data, options);
  },

  // Bulk add jobs
  async addBulkJobs<T>(
    queueName: QueueName,
    jobs: Array<{ data: T; opts?: Bull.JobOptions }>
  ): Promise<Bull.Job<T>[]> {
    const queue = queues[queueName];
    return await queue.addBulk(jobs);
  },

  // Get queue status
  async getQueueStatus(queueName: QueueName) {
    const queue = queues[queueName];
    const [waitingCount, activeCount, completedCount, failedCount, delayedCount] =
      await Promise.all([
        queue.getWaitingCount(),
        queue.getActiveCount(),
        queue.getCompletedCount(),
        queue.getFailedCount(),
        queue.getDelayedCount(),
      ]);

    return {
      name: queueName,
      waiting: waitingCount,
      active: activeCount,
      completed: completedCount,
      failed: failedCount,
      delayed: delayedCount,
      total: waitingCount + activeCount + delayedCount,
    };
  },

  // Get all queue statuses
  async getAllQueueStatuses() {
    const statuses = await Promise.all(
      Object.values(QueueName).map(name => this.getQueueStatus(name))
    );
    return statuses;
  },

  // Clean old jobs
  async cleanQueue(
    queueName: QueueName,
    grace: number = 3600000 // 1 hour
  ) {
    const queue = queues[queueName];
    await queue.clean(grace, 'completed');
    await queue.clean(grace * 24, 'failed'); // Keep failed jobs longer
  },

  // Pause/resume queue
  async pauseQueue(queueName: QueueName) {
    await queues[queueName].pause();
  },

  async resumeQueue(queueName: QueueName) {
    await queues[queueName].resume();
  },

  // Get specific job
  async getJob(queueName: QueueName, jobId: string) {
    return await queues[queueName].getJob(jobId);
  },
};

// Export queues for processors
export const aiParsingQueue = queues[QueueName.AI_PARSING];
export const emailQueue = queues[QueueName.EMAIL];
export const analyticsQueue = queues[QueueName.ANALYTICS];
export const cleanupQueue = queues[QueueName.CLEANUP];
```

### Task 6: Queue Processors (1 hour)

**File**: `packages/hub/src/workers/ai-parsing.worker.ts`

```typescript
import { Job, DoneCallback } from 'bull';
import { aiParsingQueue } from '../services/queue.service';
import { AIParserService } from '../services/ai-parser.service';
import { supabase } from '../config/supabase';
import { EventEmitter } from 'events';

// Create event emitter for SSE
export const parsingEvents = new EventEmitter();

// Process AI parsing jobs
aiParsingQueue.process(5, async (job: Job) => {
  const { userId, tripId, content, model } = job.data;

  try {
    // Update job progress
    await job.progress(10);
    parsingEvents.emit(`parsing:${tripId}`, {
      status: 'processing',
      progress: 10,
      message: 'Starting AI parsing...',
    });

    // Initialize parser
    const parser = new AIParserService(model);

    // Parse content with progress updates
    parser.on('progress', async progress => {
      await job.progress(progress.percent);
      parsingEvents.emit(`parsing:${tripId}`, progress);
    });

    await job.progress(20);
    const result = await parser.parseContent(content);

    await job.progress(80);
    parsingEvents.emit(`parsing:${tripId}`, {
      status: 'saving',
      progress: 80,
      message: 'Saving trip data...',
    });

    // Save to database
    const { error: updateError } = await supabase
      .from('trips')
      .update({
        title: result.title,
        description: result.description,
        start_date: result.startDate,
        end_date: result.endDate,
        destination: result.destination,
        metadata: {
          ...result.metadata,
          parsed_at: new Date().toISOString(),
          job_id: job.id,
        },
        status: 'ready',
      })
      .eq('id', tripId);

    if (updateError) throw updateError;

    // Save activities
    if (result.activities.length > 0) {
      const { error: activitiesError } = await supabase.from('activities').insert(
        result.activities.map(activity => ({
          ...activity,
          trip_id: tripId,
          user_id: userId,
        }))
      );

      if (activitiesError) throw activitiesError;
    }

    await job.progress(100);
    parsingEvents.emit(`parsing:${tripId}`, {
      status: 'completed',
      progress: 100,
      message: 'Trip imported successfully!',
      data: result,
    });

    // Track analytics
    await analyticsQueue.add({
      event: 'trip_parsed',
      userId,
      tripId,
      properties: {
        activities_count: result.activities.length,
        confidence: result.metadata.confidence,
        model: model || 'default',
        parse_time: Date.now() - job.timestamp,
      },
    });

    return result;
  } catch (error: any) {
    // Emit error event
    parsingEvents.emit(`parsing:${tripId}`, {
      status: 'error',
      error: error.message,
      progress: 0,
    });

    // Update trip status
    await supabase
      .from('trips')
      .update({
        status: 'failed',
        metadata: {
          error: error.message,
          failed_at: new Date().toISOString(),
        },
      })
      .eq('id', tripId);

    throw error;
  }
});

// Handle job events
aiParsingQueue.on('failed', async (job, err) => {
  console.error(`Job ${job.id} failed:`, err);

  // Notify user if final attempt
  if (job.attemptsMade >= job.opts.attempts!) {
    await emailQueue.add({
      to: job.data.userEmail,
      subject: 'Trip Import Failed',
      template: 'import-failed',
      data: {
        tripId: job.data.tripId,
        error: err.message,
      },
    });
  }
});

aiParsingQueue.on('stalled', job => {
  console.warn(`Job ${job.id} stalled and will be retried`);
});

// Health check
setInterval(async () => {
  const health = await aiParsingQueue.checkHealth();
  if (!health) {
    console.error('AI parsing queue is unhealthy!');
    // Could trigger alert here
  }
}, 30000); // Every 30 seconds
```

## Day 14 Morning: Monitoring & Alerts (4 hours)

### Task 7: Health Check System (1.5 hours)

**File**: `packages/hub/src/services/health.service.ts`

```typescript
import { supabase } from '../config/supabase';
import { redis } from '../config/cache.config';
import { queueService } from './queue.service';
import axios from 'axios';

export interface HealthStatus {
  service: string;
  status: 'healthy' | 'degraded' | 'down';
  responseTime?: number;
  error?: string;
  lastChecked: Date;
}

export interface SystemHealth {
  status: 'healthy' | 'degraded' | 'down';
  services: HealthStatus[];
  metrics: {
    cpu: number;
    memory: number;
    activeConnections: number;
    queueDepth: number;
  };
}

class HealthService {
  private checks = new Map<string, () => Promise<HealthStatus>>();

  constructor() {
    this.registerChecks();
  }

  private registerChecks() {
    // Database health
    this.checks.set('database', async () => {
      const start = Date.now();
      try {
        const { data, error } = await supabase.from('trips').select('id').limit(1);

        if (error) throw error;

        return {
          service: 'database',
          status: 'healthy',
          responseTime: Date.now() - start,
          lastChecked: new Date(),
        };
      } catch (error: any) {
        return {
          service: 'database',
          status: 'down',
          error: error.message,
          lastChecked: new Date(),
        };
      }
    });

    // Redis health
    this.checks.set('redis', async () => {
      const start = Date.now();
      try {
        await redis.ping();

        return {
          service: 'redis',
          status: 'healthy',
          responseTime: Date.now() - start,
          lastChecked: new Date(),
        };
      } catch (error: any) {
        return {
          service: 'redis',
          status: 'down',
          error: error.message,
          lastChecked: new Date(),
        };
      }
    });

    // AI service health
    this.checks.set('ai-service', async () => {
      const start = Date.now();
      try {
        const response = await axios.get('https://openrouter.ai/api/v1/models', {
          headers: {
            Authorization: `Bearer ${process.env.OPENROUTER_API_KEY}`,
          },
          timeout: 5000,
        });

        return {
          service: 'ai-service',
          status: response.status === 200 ? 'healthy' : 'degraded',
          responseTime: Date.now() - start,
          lastChecked: new Date(),
        };
      } catch (error: any) {
        return {
          service: 'ai-service',
          status: 'down',
          error: error.message,
          lastChecked: new Date(),
        };
      }
    });

    // Queue health
    this.checks.set('queues', async () => {
      try {
        const statuses = await queueService.getAllQueueStatuses();
        const totalWaiting = statuses.reduce((sum, s) => sum + s.waiting, 0);

        // Consider degraded if too many waiting jobs
        const status = totalWaiting > 1000 ? 'degraded' : 'healthy';

        return {
          service: 'queues',
          status,
          lastChecked: new Date(),
        };
      } catch (error: any) {
        return {
          service: 'queues',
          status: 'down',
          error: error.message,
          lastChecked: new Date(),
        };
      }
    });
  }

  async checkHealth(): Promise<SystemHealth> {
    // Run all health checks
    const checks = await Promise.all(Array.from(this.checks.values()).map(check => check()));

    // Get system metrics
    const metrics = await this.getSystemMetrics();

    // Determine overall status
    const hasDown = checks.some(c => c.status === 'down');
    const hasDegraded = checks.some(c => c.status === 'degraded');

    const status = hasDown ? 'down' : hasDegraded ? 'degraded' : 'healthy';

    return {
      status,
      services: checks,
      metrics,
    };
  }

  private async getSystemMetrics() {
    // Get queue depth
    const queues = await queueService.getAllQueueStatuses();
    const queueDepth = queues.reduce((sum, q) => sum + q.total, 0);

    // Get process metrics
    const usage = process.memoryUsage();
    const cpuUsage = process.cpuUsage();

    return {
      cpu: cpuUsage.user / 1000000, // Convert to seconds
      memory: usage.heapUsed / 1024 / 1024, // Convert to MB
      activeConnections: await this.getActiveConnections(),
      queueDepth,
    };
  }

  private async getActiveConnections(): Promise<number> {
    try {
      const { data } = await supabase.rpc('get_active_connections');
      return data || 0;
    } catch {
      return 0;
    }
  }

  // Alert if unhealthy
  async checkAndAlert() {
    const health = await this.checkHealth();

    if (health.status !== 'healthy') {
      // Send alert (implement your alerting logic)
      console.error('System unhealthy:', health);

      // Could send to Slack, PagerDuty, etc.
      // await alertService.send({
      //   severity: health.status === 'down' ? 'critical' : 'warning',
      //   message: `System ${health.status}: ${health.services.filter(s => s.status !== 'healthy').map(s => s.service).join(', ')}`
      // });
    }
  }
}

export const healthService = new HealthService();
```

### Task 8: Metrics Collection (1.5 hours)

**File**: `packages/hub/src/services/metrics.service.ts`

```typescript
import { StatsD } from 'node-statsd';
import { Request, Response, NextFunction } from 'express';

// Initialize StatsD client (or use Datadog, New Relic, etc.)
const statsd = new StatsD({
  host: process.env.STATSD_HOST || 'localhost',
  port: parseInt(process.env.STATSD_PORT || '8125'),
  prefix: 'travelviz.',
  errorHandler: error => {
    console.error('StatsD error:', error);
  },
});

export const metrics = {
  // Increment counter
  increment(stat: string, value: number = 1, tags?: string[]) {
    statsd.increment(stat, value, tags);
  },

  // Gauge - absolute value
  gauge(stat: string, value: number, tags?: string[]) {
    statsd.gauge(stat, value, tags);
  },

  // Histogram - for distributions
  histogram(stat: string, value: number, tags?: string[]) {
    statsd.histogram(stat, value, tags);
  },

  // Timing
  timing(stat: string, time: number, tags?: string[]) {
    statsd.timing(stat, time, tags);
  },

  // Middleware for request metrics
  middleware() {
    return (req: Request, res: Response, next: NextFunction) => {
      const start = Date.now();

      // Track request
      metrics.increment('api.requests', 1, [
        `method:${req.method}`,
        `path:${req.route?.path || 'unknown'}`,
      ]);

      // Override end to capture metrics
      const originalEnd = res.end;
      res.end = function (...args: any[]) {
        // Calculate duration
        const duration = Date.now() - start;

        // Track response
        metrics.timing('api.response_time', duration, [
          `method:${req.method}`,
          `path:${req.route?.path || 'unknown'}`,
          `status:${res.statusCode}`,
        ]);

        // Track status codes
        metrics.increment(`api.status_codes.${res.statusCode}`, 1);

        // Track errors
        if (res.statusCode >= 400) {
          metrics.increment('api.errors', 1, [
            `status:${res.statusCode}`,
            `path:${req.route?.path || 'unknown'}`,
          ]);
        }

        // Call original end
        return originalEnd.apply(res, args);
      };

      next();
    };
  },

  // Track custom business metrics
  trackImport(success: boolean, parseTime: number, activitiesCount: number) {
    metrics.increment(success ? 'imports.success' : 'imports.failed');
    metrics.timing('imports.parse_time', parseTime);
    metrics.histogram('imports.activities_count', activitiesCount);
  },

  trackUser(event: 'signup' | 'login' | 'upgrade' | 'churn', userId: string) {
    metrics.increment(`users.${event}`, 1);

    if (event === 'upgrade') {
      metrics.increment('revenue.conversions', 1);
    }
  },

  trackRevenue(amount: number, type: 'subscription' | 'affiliate') {
    metrics.increment(`revenue.${type}`, amount);
    metrics.gauge('revenue.mrr', amount); // Would need to calculate actual MRR
  },

  // Performance metrics
  async collectPerformanceMetrics() {
    const usage = process.memoryUsage();

    metrics.gauge('system.memory.heap_used', usage.heapUsed);
    metrics.gauge('system.memory.heap_total', usage.heapTotal);
    metrics.gauge('system.memory.rss', usage.rss);
    metrics.gauge('system.memory.external', usage.external);

    const cpuUsage = process.cpuUsage();
    metrics.gauge('system.cpu.user', cpuUsage.user);
    metrics.gauge('system.cpu.system', cpuUsage.system);
  },
};

// Collect system metrics every 30 seconds
setInterval(() => {
  metrics.collectPerformanceMetrics();
}, 30000);
```

### Task 9: Error Tracking Setup (1 hour)

**File**: `packages/hub/src/services/error-tracking.service.ts`

```typescript
import * as Sentry from '@sentry/node';
import { Request } from 'express';

// Initialize Sentry
Sentry.init({
  dsn: process.env.SENTRY_DSN,
  environment: process.env.NODE_ENV || 'development',
  tracesSampleRate: process.env.NODE_ENV === 'production' ? 0.1 : 1.0,

  beforeSend(event, hint) {
    // Filter out expected errors
    const error = hint.originalException;

    // Don't send rate limit errors
    if (error?.message?.includes('Rate limit')) {
      return null;
    }

    // Don't send validation errors
    if (error?.name === 'ValidationError') {
      return null;
    }

    // Sanitize sensitive data
    if (event.request?.cookies) {
      delete event.request.cookies;
    }

    return event;
  },

  integrations: [
    new Sentry.Integrations.Http({ tracing: true }),
    new Sentry.Integrations.Express({
      router: true,
      methods: ['all'],
    }),
  ],
});

export const errorTracking = {
  // Capture exception with context
  captureException(error: Error, context?: Record<string, any>) {
    Sentry.withScope(scope => {
      if (context) {
        scope.setContext('additional', context);
      }
      Sentry.captureException(error);
    });
  },

  // Capture message
  captureMessage(message: string, level: Sentry.SeverityLevel = 'info') {
    Sentry.captureMessage(message, level);
  },

  // Add user context
  setUser(user: { id: string; email?: string; subscription?: string }) {
    Sentry.setUser({
      id: user.id,
      email: user.email,
      subscription: user.subscription,
    });
  },

  // Add breadcrumb
  addBreadcrumb(breadcrumb: {
    message: string;
    category?: string;
    level?: Sentry.SeverityLevel;
    data?: Record<string, any>;
  }) {
    Sentry.addBreadcrumb(breadcrumb);
  },

  // Express error handler
  errorHandler() {
    return Sentry.Handlers.errorHandler({
      shouldHandleError(error) {
        // Capture all 500 errors
        if (error.status === 500) {
          return true;
        }

        // Capture specific errors
        if (error.name === 'DatabaseError') {
          return true;
        }

        return false;
      },
    });
  },

  // Request handler
  requestHandler() {
    return Sentry.Handlers.requestHandler({
      request: ['method', 'url', 'query_string'],
      user: ['id', 'email'],
      ip: true,
    });
  },

  // Transaction helper
  startTransaction(name: string, op: string) {
    return Sentry.startTransaction({
      name,
      op,
    });
  },
};

// Custom error classes with Sentry integration
export class ApplicationError extends Error {
  public statusCode: number;
  public isOperational: boolean;

  constructor(message: string, statusCode: number = 500, isOperational: boolean = true) {
    super(message);
    this.statusCode = statusCode;
    this.isOperational = isOperational;

    Error.captureStackTrace(this, this.constructor);

    // Only capture non-operational errors
    if (!isOperational) {
      errorTracking.captureException(this);
    }
  }
}

export class ValidationError extends ApplicationError {
  constructor(message: string) {
    super(message, 400, true);
    this.name = 'ValidationError';
  }
}

export class AuthenticationError extends ApplicationError {
  constructor(message: string = 'Authentication failed') {
    super(message, 401, true);
    this.name = 'AuthenticationError';
  }
}

export class RateLimitError extends ApplicationError {
  constructor(message: string = 'Rate limit exceeded') {
    super(message, 429, true);
    this.name = 'RateLimitError';
  }
}
```

## Day 14 Afternoon: Load Testing & Optimization (4 hours)

### Task 10: Load Testing Setup (2 hours)

**File**: `packages/hub/src/tests/load/artillery-config.yml`

```yaml
config:
  target: 'http://localhost:3001'
  phases:
    # Warm up
    - duration: 60
      arrivalRate: 5
      name: 'Warm up'

    # Ramp up load
    - duration: 300
      arrivalRate: 5
      rampTo: 50
      name: 'Ramp up'

    # Sustained load
    - duration: 600
      arrivalRate: 50
      name: 'Sustained load'

    # Spike test
    - duration: 60
      arrivalRate: 200
      name: 'Spike test'

  processor: './load-test-processor.js'
  variables:
    testUsers:
      - '<EMAIL>'
      - '<EMAIL>'
      - '<EMAIL>'

scenarios:
  # Scenario 1: Browse public trips
  - name: 'Browse Public Trips'
    weight: 40
    flow:
      - get:
          url: '/api/trips/public'
          capture:
            - json: '$.data[0].shareId'
              as: 'shareId'
      - think: 2
      - get:
          url: '/api/trips/public/{{ shareId }}'
      - think: 3

  # Scenario 2: User login and import
  - name: 'User Import Flow'
    weight: 30
    flow:
      - post:
          url: '/api/auth/login'
          json:
            email: '{{ testUsers }}'
            password: 'testpass123'
          capture:
            - json: '$.token'
              as: 'authToken'
      - think: 1
      - post:
          url: '/api/trips/import'
          headers:
            Authorization: 'Bearer {{ authToken }}'
          json:
            content: '{{ $randomString() }}'
          capture:
            - json: '$.tripId'
              as: 'tripId'
      - think: 2
      - loop:
          - get:
              url: '/api/trips/{{ tripId }}/status'
              headers:
                Authorization: 'Bearer {{ authToken }}'
          - think: 1
        count: 10

  # Scenario 3: API stress test
  - name: 'API Stress Test'
    weight: 20
    flow:
      - loop:
          - get:
              url: '/api/health'
          - get:
              url: '/api/trips/search?q={{ $randomString() }}'
          - think: 0.5
        count: 20

  # Scenario 4: Static assets
  - name: 'Static Asset Load'
    weight: 10
    flow:
      - get:
          url: '/'
      - get:
          url: '/static/js/bundle.js'
      - get:
          url: '/static/css/main.css'
```

**File**: `packages/hub/src/tests/load/load-test-processor.js`

```javascript
module.exports = {
  $randomString: function () {
    const conversations = [
      'Plan a 5 day trip to Paris in April',
      'I want to visit Tokyo and Kyoto next month',
      'Weekend getaway to Barcelona',
      'Family vacation to Orlando Disney World',
    ];
    return conversations[Math.floor(Math.random() * conversations.length)];
  },

  setAuthHeader: function (requestParams, context, ee, next) {
    if (context.vars.authToken) {
      requestParams.headers = requestParams.headers || {};
      requestParams.headers['Authorization'] = `Bearer ${context.vars.authToken}`;
    }
    return next();
  },

  logErrors: function (requestParams, response, context, ee, next) {
    if (response.statusCode >= 400) {
      console.error(`Error ${response.statusCode}: ${response.body}`);
    }
    return next();
  },
};
```

**File**: `packages/hub/src/tests/load/run-load-test.sh`

```bash
#!/bin/bash

echo "🚀 Starting TravelViz Load Test"
echo "================================"

# Check if Artillery is installed
if ! command -v artillery &> /dev/null; then
    echo "Installing Artillery..."
    npm install -g artillery
fi

# Create test users if they don't exist
echo "Setting up test users..."
node ./setup-test-users.js

# Start monitoring
echo "Starting monitoring..."
node ./monitor-metrics.js &
MONITOR_PID=$!

# Run load test
echo "Running load test..."
artillery run artillery-config.yml -o results.json

# Generate report
echo "Generating report..."
artillery report results.json -o load-test-report.html

# Stop monitoring
kill $MONITOR_PID

# Analyze results
echo "Analyzing results..."
node ./analyze-results.js results.json

echo "✅ Load test complete! View report: load-test-report.html"
```

### Task 11: Performance Optimization (2 hours)

**File**: `packages/hub/src/optimizations/request-coalescing.ts`

```typescript
import { LRUCache } from 'lru-cache';

interface PendingRequest {
  promise: Promise<any>;
  timestamp: number;
}

/**
 * Request coalescing to prevent duplicate concurrent requests
 */
export class RequestCoalescer {
  private pending = new Map<string, PendingRequest>();
  private cache: LRUCache<string, any>;

  constructor(
    options: {
      maxAge?: number;
      maxSize?: number;
    } = {}
  ) {
    this.cache = new LRUCache({
      max: options.maxSize || 1000,
      ttl: options.maxAge || 5000, // 5 seconds default
    });
  }

  async coalesce<T>(
    key: string,
    fn: () => Promise<T>,
    options: {
      cache?: boolean;
      cacheTime?: number;
    } = {}
  ): Promise<T> {
    // Check cache first
    if (options.cache) {
      const cached = this.cache.get(key);
      if (cached !== undefined) {
        return cached;
      }
    }

    // Check if request is already pending
    const pending = this.pending.get(key);
    if (pending && Date.now() - pending.timestamp < 30000) {
      // Return existing promise if less than 30s old
      return pending.promise;
    }

    // Create new request
    const promise = fn().then(
      result => {
        // Cache result
        if (options.cache) {
          this.cache.set(key, result);
        }

        // Clean up pending
        this.pending.delete(key);

        return result;
      },
      error => {
        // Clean up pending on error
        this.pending.delete(key);
        throw error;
      }
    );

    // Store pending request
    this.pending.set(key, {
      promise,
      timestamp: Date.now(),
    });

    return promise;
  }

  // Clear specific key
  invalidate(key: string) {
    this.cache.delete(key);
    this.pending.delete(key);
  }

  // Clear pattern
  invalidatePattern(pattern: string) {
    // Clear cache entries matching pattern
    for (const key of this.cache.keys()) {
      if (key.includes(pattern)) {
        this.cache.delete(key);
      }
    }

    // Clear pending requests matching pattern
    for (const key of this.pending.keys()) {
      if (key.includes(pattern)) {
        this.pending.delete(key);
      }
    }
  }
}

// Global instance
export const requestCoalescer = new RequestCoalescer();

// Usage in route handlers
export function coalescedRoute(
  keyFn: (req: Request) => string,
  options?: { cache?: boolean; cacheTime?: number }
) {
  return (target: any, propertyKey: string, descriptor: PropertyDescriptor) => {
    const originalMethod = descriptor.value;

    descriptor.value = async function (req: Request, res: Response) {
      const key = keyFn(req);

      try {
        const result = await requestCoalescer.coalesce(
          key,
          () => originalMethod.call(this, req, res),
          options
        );

        return result;
      } catch (error) {
        throw error;
      }
    };

    return descriptor;
  };
}
```

**File**: `packages/hub/src/optimizations/batch-processor.ts`

```typescript
/**
 * Batch processor for aggregating multiple requests
 */
export class BatchProcessor<T, R> {
  private batch: T[] = [];
  private timeout: NodeJS.Timeout | null = null;
  private processing = false;

  constructor(
    private processor: (items: T[]) => Promise<R[]>,
    private options: {
      maxBatchSize: number;
      maxWaitTime: number;
      keyExtractor?: (item: T) => string;
    }
  ) {}

  async add(item: T): Promise<R> {
    return new Promise((resolve, reject) => {
      // Add to batch with callback
      this.batch.push(item);
      const index = this.batch.length - 1;

      // Store resolve/reject for later
      (item as any).__resolve = resolve;
      (item as any).__reject = reject;
      (item as any).__index = index;

      // Process if batch is full
      if (this.batch.length >= this.options.maxBatchSize) {
        this.processBatch();
      } else if (!this.timeout) {
        // Set timeout for batch processing
        this.timeout = setTimeout(() => {
          this.processBatch();
        }, this.options.maxWaitTime);
      }
    });
  }

  private async processBatch() {
    if (this.processing || this.batch.length === 0) {
      return;
    }

    this.processing = true;

    // Clear timeout
    if (this.timeout) {
      clearTimeout(this.timeout);
      this.timeout = null;
    }

    // Get current batch
    const currentBatch = [...this.batch];
    this.batch = [];

    try {
      // Process batch
      const results = await this.processor(currentBatch);

      // Resolve promises
      currentBatch.forEach((item, index) => {
        const resolve = (item as any).__resolve;
        const result = results[index];

        if (result instanceof Error) {
          (item as any).__reject(result);
        } else {
          resolve(result);
        }
      });
    } catch (error) {
      // Reject all promises on batch error
      currentBatch.forEach(item => {
        (item as any).__reject(error);
      });
    } finally {
      this.processing = false;

      // Process next batch if items were added during processing
      if (this.batch.length > 0) {
        this.processBatch();
      }
    }
  }
}

// Example: Batch geocoding
export const geocodingBatcher = new BatchProcessor(
  async (addresses: string[]) => {
    // Batch geocode multiple addresses in one request
    const results = await mapboxClient.batchGeocode(addresses);
    return results;
  },
  {
    maxBatchSize: 50,
    maxWaitTime: 100, // 100ms
  }
);
```

## Testing & Monitoring Checklist

### Load Testing

- [ ] Run baseline load test
- [ ] Identify bottlenecks
- [ ] Apply optimizations
- [ ] Re-run load test
- [ ] Document improvements

### Performance Targets

- [ ] 95th percentile response time <500ms
- [ ] Can handle 100 req/sec sustained
- [ ] Memory usage stable under load
- [ ] No errors at 2x expected load
- [ ] Database connections stay under limit

### Monitoring Setup

- [ ] Health checks return within 1s
- [ ] All critical paths have metrics
- [ ] Error tracking captures context
- [ ] Alerts configured for failures
- [ ] Dashboard shows key metrics

### Scale Readiness

- [ ] Database indexes optimized
- [ ] Caching strategy implemented
- [ ] Rate limiting protects APIs
- [ ] Job queues handle spikes
- [ ] Costs projected and acceptable

## Extended Thinking Prompts

For scaling decisions:

```
Current bottleneck: [database/API/memory]
Load profile: [steady/spiky/growing]
Cost constraint: [$X/month]
What's the most cost-effective scaling approach?
```

For monitoring setup:

```
Critical user journey: [import flow]
Failure impact: [users affected]
Recovery time objective: [X minutes]
What metrics and alerts do we need?
```

## Definition of Done

✅ Load Testing Complete:

```bash
# Run full load test suite
cd packages/hub
./tests/load/run-load-test.sh

# Verify targets met
- 95th percentile <500ms ✓
- Zero errors at 100 req/sec ✓
- Memory stable over 10 minutes ✓
```

✅ Database Optimized:

```sql
-- Check query performance
SELECT query, mean_time, calls
FROM pg_stat_statements
WHERE mean_time > 100
ORDER BY mean_time DESC;

-- All queries <100ms ✓
```

✅ Monitoring Active:

- Health endpoint responds <1s
- Metrics flowing to dashboard
- Alerts tested and working
- Error tracking capturing issues

✅ Scale Ready:

- Can handle 10k users on day 1
- Costs stay under $500/month
- Auto-scaling configured
- Graceful degradation tested

## Next Day Preview

Day 15: Launch!

- Final testing checklist
- Launch announcement prep
- Monitor systems live
- Rapid response plan

## Notes

- Most startups die from success, not failure
- Scale issues are good problems to have
- Monitor everything, alert on what matters
- Cache aggressively, invalidate precisely
- Batch when possible, stream when necessary
- Every millisecond counts at scale
