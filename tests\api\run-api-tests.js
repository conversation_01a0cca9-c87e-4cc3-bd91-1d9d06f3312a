#!/usr/bin/env node

/**
 * API Test Runner
 * 
 * Runs all API integration tests in sequence
 * Requires the hub server to be running on port 3001
 */

const fs = require('fs');
const path = require('path');

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function logHeader(message) {
  log('\n' + '='.repeat(60), 'cyan');
  log(`  ${message}`, 'bright');
  log('='.repeat(60), 'cyan');
}

function logSuccess(message) {
  log(`✅ ${message}`, 'green');
}

function logError(message) {
  log(`❌ ${message}`, 'red');
}

function logWarning(message) {
  log(`⚠️  ${message}`, 'yellow');
}

async function checkServerRunning() {
  const testConfig = require('./api.config');
  
  try {
    const response = await fetch(`${testConfig.baseUrl}/api/v1/health`);
    if (response.ok) {
      logSuccess('Hub server is running');
      return true;
    } else {
      logError('Hub server health check failed');
      return false;
    }
  } catch (error) {
    logError('Hub server is not running. Please start it with: pnpm dev');
    logError('Make sure the server is running on port 3001');
    return false;
  }
}

async function runApiTests() {
  logHeader('TravelViz API Integration Tests');
  
  log('Checking server status...', 'blue');
  const serverRunning = await checkServerRunning();
  
  if (!serverRunning) {
    log('\nTo run API tests:', 'yellow');
    log('1. Start the development server: pnpm dev');
    log('2. Wait for hub server to be ready on port 3001');
    log('3. Run tests again: pnpm test:api');
    process.exit(1);
  }
  
  // Get all test files in specific order
  const testDir = __dirname;
  const testFiles = [
    'public.api.test.js',      // Run public tests first (no auth needed)
    'auth.api.test.js',        // Then auth to establish tokens
    'trips.api.test.js',       // Then protected endpoints
    'import.api.test.js',      // AI import functionality
    'places.api.test.js',      // Places integration
  ].filter(file => fs.existsSync(path.join(testDir, file)));
  
  if (testFiles.length === 0) {
    logWarning('No API test files found');
    return;
  }
  
  log(`\nFound ${testFiles.length} test files:`, 'blue');
  testFiles.forEach(file => log(`  - ${file}`));
  
  let totalPassed = 0;
  let totalFailed = 0;
  
  // Run each test file
  for (const testFile of testFiles) {
    log(`\n🧪 Running ${testFile}...`, 'blue');
    
    try {
      const testModule = require(path.join(testDir, testFile));
      const results = await testModule.runTests();
      
      totalPassed += results.passed;
      totalFailed += results.failed;
      
      if (results.failed === 0) {
        logSuccess(`${testFile} - All tests passed`);
      } else {
        logError(`${testFile} - ${results.failed} tests failed`);
      }
    } catch (error) {
      logError(`${testFile} - Error running tests: ${error.message}`);
      totalFailed++;
    }
  }
  
  // Summary
  logHeader('Test Results Summary');
  log(`✅ Passed: ${totalPassed}`, 'green');
  log(`❌ Failed: ${totalFailed}`, 'red');
  log(`📈 Success Rate: ${Math.round((totalPassed / (totalPassed + totalFailed)) * 100)}%`, 'blue');
  
  if (totalFailed === 0) {
    logSuccess('🎉 All API tests passed!');
    process.exit(0);
  } else {
    logError('Some tests failed. Check the output above for details.');
    process.exit(1);
  }
}

// Handle process termination
process.on('SIGINT', () => {
  log('\nTest interrupted by user', 'yellow');
  process.exit(1);
});

// Run the tests
runApiTests().catch((error) => {
  logError(`Fatal error: ${error.message}`);
  process.exit(1);
});