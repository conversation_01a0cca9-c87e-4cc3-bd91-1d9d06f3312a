import React from 'react';
import { cn } from '@/lib/utils';
import { useIsMobile, useIsTablet, useIsDesktop } from '@/hooks/useMediaQuery';
import { useTouchDevice, useSafeArea } from '@/hooks/useTouchDevice';

interface ResponsiveContainerProps {
  children: React.ReactNode;
  className?: string;
  mobileClassName?: string;
  tabletClassName?: string;
  desktopClassName?: string;
  enableSafeArea?: boolean;
  maxWidth?: 'sm' | 'md' | 'lg' | 'xl' | '2xl' | 'full';
}

/**
 * Responsive container that adapts to different screen sizes
 * Automatically applies appropriate padding and layout adjustments
 */
export function ResponsiveContainer({
  children,
  className,
  mobileClassName,
  tabletClassName,
  desktopClassName,
  enableSafeArea = true,
  maxWidth = 'xl',
}: ResponsiveContainerProps) {
  const isMobile = useIsMobile();
  const isTablet = useIsTablet();
  const isDesktop = useIsDesktop();
  const { isTouchDevice } = useTouchDevice();
  const safeArea = useSafeArea();

  const maxWidthClasses = {
    sm: 'max-w-sm',
    md: 'max-w-md',
    lg: 'max-w-lg',
    xl: 'max-w-xl',
    '2xl': 'max-w-2xl',
    full: 'max-w-full',
  };

  return (
    <div
      className={cn(
        'mx-auto w-full',
        maxWidthClasses[maxWidth],
        // Base padding
        'px-4 sm:px-6 lg:px-8',
        // Touch-optimized spacing
        isTouchDevice && 'touch-manipulation',
        // Responsive classes
        isMobile && mobileClassName,
        isTablet && tabletClassName,
        isDesktop && desktopClassName,
        className
      )}
      style={enableSafeArea ? {
        paddingTop: `calc(1rem + ${safeArea.top}px)`,
        paddingBottom: `calc(1rem + ${safeArea.bottom}px)`,
        paddingLeft: `calc(1rem + ${safeArea.left}px)`,
        paddingRight: `calc(1rem + ${safeArea.right}px)`,
      } : undefined}
    >
      {children}
    </div>
  );
}

interface ResponsiveGridProps {
  children: React.ReactNode;
  className?: string;
  cols?: {
    default?: number;
    sm?: number;
    md?: number;
    lg?: number;
    xl?: number;
  };
  gap?: 'sm' | 'md' | 'lg';
}

/**
 * Responsive grid that automatically adjusts columns based on screen size
 */
export function ResponsiveGrid({
  children,
  className,
  cols = { default: 1, sm: 2, md: 3, lg: 4 },
  gap = 'md',
}: ResponsiveGridProps) {
  const gapClasses = {
    sm: 'gap-2',
    md: 'gap-4',
    lg: 'gap-6',
  };

  const gridCols = cn(
    'grid',
    gapClasses[gap],
    cols.default && `grid-cols-${cols.default}`,
    cols.sm && `sm:grid-cols-${cols.sm}`,
    cols.md && `md:grid-cols-${cols.md}`,
    cols.lg && `lg:grid-cols-${cols.lg}`,
    cols.xl && `xl:grid-cols-${cols.xl}`,
    className
  );

  return <div className={gridCols}>{children}</div>;
}

interface ResponsiveStackProps {
  children: React.ReactNode;
  className?: string;
  direction?: 'vertical' | 'horizontal' | 'responsive';
  gap?: 'sm' | 'md' | 'lg';
  align?: 'start' | 'center' | 'end' | 'stretch';
}

/**
 * Responsive stack that can switch between vertical and horizontal layouts
 */
export function ResponsiveStack({
  children,
  className,
  direction = 'responsive',
  gap = 'md',
  align = 'stretch',
}: ResponsiveStackProps) {
  const gapClasses = {
    sm: 'gap-2',
    md: 'gap-4',
    lg: 'gap-6',
  };

  const alignClasses = {
    start: 'items-start',
    center: 'items-center',
    end: 'items-end',
    stretch: 'items-stretch',
  };

  const directionClasses = {
    vertical: 'flex flex-col',
    horizontal: 'flex flex-row',
    responsive: 'flex flex-col md:flex-row',
  };

  return (
    <div
      className={cn(
        directionClasses[direction],
        gapClasses[gap],
        alignClasses[align],
        className
      )}
    >
      {children}
    </div>
  );
}