# TravelViz Security Guide

This comprehensive guide consolidates all security-related information for the TravelViz application. It serves as the single source of truth for security principles, implementation details, and best practices.

## Table of Contents

1. [Security Overview](#security-overview)
2. [Credential Management](#credential-management)
3. [Authentication & Authorization](#authentication--authorization)
4. [API Security](#api-security)
5. [Database Security (RLS)](#database-security-rls)
6. [Input Validation & Sanitization](#input-validation--sanitization)
7. [Security Monitoring & Alerting](#security-monitoring--alerting)
8. [Security Incident Response](#security-incident-response)
9. [Security Implementation Checklist](#security-implementation-checklist)
10. [Best Practices](#best-practices)

## Security Overview

### Core Security Principles

1. **Zero Trust Architecture** - Never trust, always verify
2. **Defense in Depth** - Multiple layers of security controls
3. **Least Privilege** - Minimal permissions by default
4. **Fail Secure** - Default to secure state on errors
5. **Assume Breach** - Design for compromised components

### Security Architecture

```
┌─────────────────────────────────────────────────────────────┐
│                        Frontend (Vercel)                      │
│  - Security headers via Vercel                               │
│  - Client-side input validation                              │
│  - No direct database access                                 │
└─────────────────────────────────────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────┐
│                      API Hub (Render)                         │
│  - JWT authentication                                         │
│  - Ownership middleware                                       │
│  - Rate limiting                                              │
│  - Input sanitization                                         │
└─────────────────────────────────────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────┐
│                    Database (Supabase)                        │
│  - Row Level Security (RLS)                                  │
│  - Encrypted at rest                                         │
│  - Audit logging                                             │
│  - Connection pooling                                         │
└─────────────────────────────────────────────────────────────┘
```

## Credential Management

### Critical Environment Variables

**NEVER** commit these files to version control:

- `.env`
- `.env.local`
- `.env.development.local`
- `.env.test.local`
- `.env.production.local`

### Supabase Credentials

#### 1. Service Role Key (`SUPABASE_SERVICE_ROLE_KEY`)

- **Access Level**: FULL database access
- **Usage**: Backend services only
- **Security**: NEVER expose to client-side code
- **Rotation**: Every 90 days

#### 2. Anonymous Key (`NEXT_PUBLIC_SUPABASE_ANON_KEY`)

- **Access Level**: Limited by RLS policies
- **Usage**: Client-side safe
- **Security**: Still rotate if compromised
- **Rotation**: As needed

#### 3. JWT Secret (`SUPABASE_JWT_SECRET`)

- **Access Level**: Token validation
- **Usage**: Backend JWT operations
- **Security**: Keep absolutely secret
- **Rotation**: Every 90 days

#### 4. Database Password

- **Access Level**: Direct database connection
- **Usage**: Migration scripts only
- **Security**: Use connection pooling in production
- **Rotation**: Every 30 days

### How to Get Your Credentials

```bash
# 1. Log into Supabase Dashboard
# 2. Select your project
# 3. Go to Settings → API
# 4. Copy credentials to .env.local files

# Generate secure JWT secret
openssl rand -base64 32

# Verify environment setup
grep -E "SUPABASE|JWT" packages/*/.env.example
```

### Credential Rotation Process

If credentials were exposed:

1. **Immediate Actions**:

   ```bash
   # Rotate in Supabase Dashboard
   # Settings → API → Click "Roll" next to each key
   ```

2. **Update All Services**:

   ```bash
   # Update .env.local files
   echo "SUPABASE_SERVICE_ROLE_KEY=new_key" >> packages/hub/.env.local
   echo "NEXT_PUBLIC_SUPABASE_ANON_KEY=new_key" >> packages/web/.env.local
   ```

3. **Restart Services**:

   ```bash
   pnpm dev:restart
   ```

4. **Monitor for Unauthorized Access**:
   - Check Supabase Dashboard → Authentication → Logs
   - Review audit_logs table

## Authentication & Authorization

### JWT Implementation

```typescript
// packages/hub/src/utils/tokens.ts
const JWT_SECRET = process.env.JWT_SECRET;

if (!JWT_SECRET || JWT_SECRET.length < 32) {
  console.error('CRITICAL: JWT_SECRET must be at least 32 characters');
  process.exit(1);
}

export function generateToken(userId: string): string {
  return jwt.sign(
    {
      userId,
      iat: Math.floor(Date.now() / 1000),
      exp: Math.floor(Date.now() / 1000) + 60 * 60 * 24 * 7, // 7 days
    },
    JWT_SECRET,
    { algorithm: 'HS256' }
  );
}

export function verifyToken(token: string): { userId: string } {
  try {
    const decoded = jwt.verify(token, JWT_SECRET, {
      algorithms: ['HS256'],
      maxAge: '7d',
    }) as any;

    if (!decoded.userId) {
      throw new Error('Invalid token structure');
    }

    return { userId: decoded.userId };
  } catch (error) {
    throw new Error('Invalid or expired token');
  }
}
```

### Supabase JWT Authentication

For production, use Supabase's native JWT verification:

```typescript
// packages/hub/src/middleware/supabase-auth.middleware.ts
export async function authenticateSupabaseUser(
  req: AuthRequest,
  res: Response,
  next: NextFunction
) {
  try {
    const token = req.headers.authorization?.replace('Bearer ', '');

    if (!token) {
      return res.status(401).json({ error: 'No token provided' });
    }

    const {
      data: { user },
      error,
    } = await supabase.auth.getUser(token);

    if (error || !user) {
      return res.status(401).json({ error: 'Invalid token' });
    }

    req.user = user;
    next();
  } catch (error) {
    res.status(401).json({ error: 'Authentication failed' });
  }
}
```

### Ownership Middleware

```typescript
// packages/hub/src/middleware/ownership.middleware.ts
export async function verifyTripOwnership(req: AuthRequest, res: Response, next: NextFunction) {
  const { tripId } = req.params;
  const userId = req.user?.id;

  if (!userId) {
    return res.status(401).json({ error: 'Unauthorized' });
  }

  const { data: trip, error } = await supabase
    .from('trips')
    .select('id, user_id, is_public')
    .eq('id', tripId)
    .single();

  if (error || !trip) {
    return res.status(404).json({ error: 'Trip not found' });
  }

  const isOwner = trip.user_id === userId;
  const isPublicRead = trip.is_public && req.method === 'GET';

  if (!isOwner && !isPublicRead) {
    return res.status(403).json({
      error: 'Forbidden',
      message: 'You do not have permission to access this trip',
    });
  }

  req.trip = trip;
  next();
}
```

## API Security

### Security Headers

```typescript
// packages/hub/src/middleware/security-headers.middleware.ts
import helmet from 'helmet';

export const securityHeaders = helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      scriptSrc: ["'self'", "'unsafe-inline'", 'https://maps.googleapis.com'],
      styleSrc: ["'self'", "'unsafe-inline'", 'https://fonts.googleapis.com'],
      imgSrc: ["'self'", 'data:', 'https:', 'blob:'],
      connectSrc: ["'self'", 'https://api.mapbox.com', 'wss://travelviz.com'],
      fontSrc: ["'self'", 'https://fonts.gstatic.com'],
      objectSrc: ["'none'"],
      mediaSrc: ["'self'"],
      frameSrc: ["'none'"],
    },
  },
  hsts: {
    maxAge: 31536000,
    includeSubDomains: true,
    preload: true,
  },
  referrerPolicy: { policy: 'strict-origin-when-cross-origin' },
  noSniff: true,
  xssFilter: true,
  ieNoOpen: true,
  frameguard: { action: 'deny' },
});
```

### Rate Limiting

```typescript
// packages/hub/src/middleware/rate-limit.middleware.ts
import rateLimit from 'express-rate-limit';
import RedisStore from 'rate-limit-redis';

// Base rate limiter
export const apiLimiter = rateLimit({
  store: new RedisStore({
    client: redisClient,
    prefix: 'rl:api:',
  }),
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // 100 requests per window
  standardHeaders: true,
  legacyHeaders: false,
});

// Strict limiter for auth endpoints
export const authLimiter = rateLimit({
  store: new RedisStore({
    client: redisClient,
    prefix: 'rl:auth:',
  }),
  windowMs: 15 * 60 * 1000,
  max: 5, // Only 5 auth attempts per 15 minutes
  skipSuccessfulRequests: true,
});

// AI endpoint limiter (expensive operations)
export const aiLimiter = rateLimit({
  store: new RedisStore({
    client: redisClient,
    prefix: 'rl:ai:',
  }),
  windowMs: 60 * 60 * 1000, // 1 hour
  max: 10, // 10 AI requests per hour
});
```

### CORS Configuration

```typescript
// packages/hub/src/middleware/cors.ts
import cors from 'cors';

const corsOptions = {
  origin: function (origin: string | undefined, callback: Function) {
    const allowedOrigins = [
      'http://localhost:3000',
      'https://travelviz.com',
      'https://*.travelviz.com',
    ];

    if (
      !origin ||
      allowedOrigins.some(allowed => origin.match(new RegExp(allowed.replace('*', '.*'))))
    ) {
      callback(null, true);
    } else {
      callback(new Error('Not allowed by CORS'));
    }
  },
  credentials: true,
  maxAge: 86400, // 24 hours
};

export default cors(corsOptions);
```

## Database Security (RLS)

### Row Level Security Policies

```sql
-- Enable RLS on all tables
ALTER TABLE trips ENABLE ROW LEVEL SECURITY;
ALTER TABLE activities ENABLE ROW LEVEL SECURITY;
ALTER TABLE search_history ENABLE ROW LEVEL SECURITY;
ALTER TABLE trip_clones ENABLE ROW LEVEL SECURITY;

-- Trips policies
CREATE POLICY "Users can view own trips" ON trips
  FOR SELECT USING (
    auth.uid() = user_id OR
    (is_public = true AND published_at IS NOT NULL)
  );

CREATE POLICY "Users can create own trips" ON trips
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own trips" ON trips
  FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete own trips" ON trips
  FOR DELETE USING (auth.uid() = user_id);

-- Activities policies (through trip ownership)
CREATE POLICY "Users can view activities" ON activities
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM trips
      WHERE trips.id = activities.trip_id
      AND (trips.user_id = auth.uid() OR (trips.is_public = true AND trips.published_at IS NOT NULL))
    )
  );

CREATE POLICY "Users can manage own activities" ON activities
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM trips
      WHERE trips.id = activities.trip_id
      AND trips.user_id = auth.uid()
    )
  );

-- Performance indexes for RLS
CREATE INDEX idx_trips_user_id ON trips(user_id);
CREATE INDEX idx_trips_public_access ON trips(is_public, published_at)
  WHERE is_public = true AND published_at IS NOT NULL;
```

### Testing RLS Policies

```typescript
// Test RLS policies
const supabase1 = createClient(url, key, { auth: { persistSession: false } });
const supabase2 = createClient(url, key, { auth: { persistSession: false } });

// Login as different users
await supabase1.auth.signInWithPassword({ email: '<EMAIL>', password });
await supabase2.auth.signInWithPassword({ email: '<EMAIL>', password });

// User 1 creates a trip
const { data: trip } = await supabase1.from('trips').insert({ title: 'My Trip' }).select().single();

// User 2 tries to access it (should fail)
const { data, error } = await supabase2.from('trips').select().eq('id', trip.id);
console.assert(error || !data?.length, 'RLS not working!');
```

## Input Validation & Sanitization

### Zod Schema Validation

```typescript
// packages/shared/src/schemas/trip.schema.ts
import { z } from 'zod';

export const createTripSchema = z.object({
  title: z.string().min(1).max(100).trim(),
  description: z.string().max(1000).optional(),
  start_date: z.string().datetime().optional(),
  end_date: z.string().datetime().optional(),
  budget: z.number().min(0).max(1000000).optional(),
  is_public: z.boolean().default(false),
});

export const updateTripSchema = createTripSchema.partial();

// Usage in controller
export async function createTrip(req: Request, res: Response) {
  try {
    const validatedData = createTripSchema.parse(req.body);
    // Process validated data
  } catch (error) {
    if (error instanceof z.ZodError) {
      return res.status(400).json({
        error: 'Validation failed',
        details: error.errors,
      });
    }
  }
}
```

### XSS Prevention

```typescript
// packages/hub/src/utils/sanitize.ts
import DOMPurify from 'isomorphic-dompurify';

export function sanitizeHtml(input: string): string {
  return DOMPurify.sanitize(input, {
    ALLOWED_TAGS: ['b', 'i', 'em', 'strong', 'a', 'p', 'br'],
    ALLOWED_ATTR: ['href', 'target'],
  });
}

export function sanitizeInput(data: any): any {
  if (typeof data === 'string') {
    return sanitizeHtml(data);
  }
  if (typeof data === 'object' && data !== null) {
    const sanitized: any = {};
    for (const key in data) {
      sanitized[key] = sanitizeInput(data[key]);
    }
    return sanitized;
  }
  return data;
}
```

## Security Monitoring & Alerting

### Platform-Native Monitoring

#### Vercel (Frontend)

- **Web Vitals**: Monitor performance and security headers
- **Edge Functions**: Track API route latencies and errors
- **Alerts**: Email notifications for performance degradation

#### Render (Backend)

- **Metrics**: Memory, CPU, response times
- **Health Checks**: Automatic health endpoint monitoring
- **Deploy Status**: Track deployment success/failure

#### Supabase (Database)

- **Query Performance**: SQL Editor → Performance tab
- **RLS Violations**: Authentication → Logs
- **API Rate Limiting**: API → Usage tab

#### Upstash (Redis)

- **Connection Count**: Monitor connection pool efficiency
- **Memory Usage**: Track Redis memory consumption
- **Command Statistics**: Most used commands and latencies

### Security Monitoring Queries

```sql
-- Check RLS violations (last 24 hours)
SELECT
  timestamp,
  event_message,
  metadata
FROM auth.audit_log_entries
WHERE timestamp > NOW() - INTERVAL '24 hours'
  AND event_message LIKE '%policy%'
ORDER BY timestamp DESC;

-- Monitor failed login attempts
SELECT
  COUNT(*) as failed_attempts,
  metadata->>'email' as email,
  DATE_TRUNC('hour', timestamp) as hour
FROM auth.audit_log_entries
WHERE event_message = 'login_failed'
  AND timestamp > NOW() - INTERVAL '24 hours'
GROUP BY email, hour
HAVING COUNT(*) > 5
ORDER BY failed_attempts DESC;

-- Check for suspicious activity patterns
SELECT
  user_id,
  COUNT(DISTINCT ip_address) as unique_ips,
  COUNT(*) as total_requests
FROM audit_logs
WHERE timestamp > NOW() - INTERVAL '1 hour'
GROUP BY user_id
HAVING COUNT(DISTINCT ip_address) > 5
ORDER BY unique_ips DESC;
```

### Audit Logging

```typescript
// packages/hub/src/services/audit.service.ts
export interface AuditLog {
  id: string;
  timestamp: Date;
  userId: string;
  action: string;
  resource: string;
  resourceId: string;
  ipAddress: string;
  userAgent: string;
  result: 'success' | 'failure';
  metadata?: Record<string, any>;
}

export class AuditService {
  async log(event: Omit<AuditLog, 'id' | 'timestamp'>): Promise<void> {
    const auditLog: AuditLog = {
      id: crypto.randomUUID(),
      timestamp: new Date(),
      ...event,
    };

    await supabase.from('audit_logs').insert(auditLog);

    // Alert on suspicious patterns
    if (event.result === 'failure') {
      await this.checkFailurePatterns(event.userId, event.action);
    }
  }

  private async checkFailurePatterns(userId: string, action: string) {
    const recentFailures = await supabase
      .from('audit_logs')
      .select('*')
      .eq('userId', userId)
      .eq('result', 'failure')
      .gte('timestamp', new Date(Date.now() - 300000)); // Last 5 minutes

    if (recentFailures.data?.length > 5) {
      // Trigger security alert
      await this.sendSecurityAlert({
        type: 'MULTIPLE_FAILURES',
        userId,
        action,
        count: recentFailures.data.length,
      });
    }
  }
}
```

## Security Incident Response

### Critical Security Incident Response Plan

If you discover exposed credentials:

1. **IMMEDIATELY** rotate all affected keys in Supabase Dashboard
2. Remove credentials from all files and git history
3. Check access logs for unauthorized usage
4. Notify the security <NAME_EMAIL>

### Incident Response Process

1. **Detection**: Identify the security breach
   - Monitor audit logs
   - Check failed authentication attempts
   - Review RLS violations

2. **Containment**: Limit the damage
   - Rotate affected credentials immediately
   - Block suspicious IP addresses
   - Disable compromised accounts

3. **Investigation**: Understand the scope
   - Check logs for unauthorized access
   - Review audit trail
   - Identify attack vectors

4. **Recovery**: Restore normal operations
   - Update all systems with new credentials
   - Patch vulnerabilities
   - Restore from backups if needed

5. **Documentation**: Learn from the incident
   - Document the incident timeline
   - Identify root causes
   - Update security procedures

6. **Prevention**: Implement improvements
   - Add additional monitoring
   - Update security policies
   - Conduct security training

### Emergency Commands

```bash
# Block suspicious IP immediately
iptables -A INPUT -s SUSPICIOUS_IP -j DROP

# Rotate all Supabase keys
# Go to Supabase Dashboard → Settings → API → Roll all keys

# Check for unauthorized database access
psql -c "SELECT * FROM auth.audit_log_entries WHERE timestamp > NOW() - INTERVAL '1 hour' AND event_message LIKE '%unauthorized%';"

# Revoke all active sessions
psql -c "DELETE FROM auth.sessions WHERE created_at < NOW();"

# Generate new JWT secret
echo "JWT_SECRET=$(openssl rand -base64 32)" > .env.local.new
```

## Security Implementation Checklist

### Pre-Deployment Security Checklist

#### Environment Security

- [ ] JWT_SECRET removed from code
- [ ] JWT_SECRET in .env.local only (minimum 32 characters)
- [ ] Server fails to start without JWT_SECRET
- [ ] All secrets in environment variables
- [ ] .env.local in .gitignore
- [ ] No hardcoded credentials in code

#### Database Security

- [ ] Foreign keys properly constrained with CASCADE
- [ ] RLS enabled on all tables
- [ ] RLS policies test correctly
- [ ] No direct database access from frontend
- [ ] Connection pooling configured
- [ ] Audit logging enabled

#### API Security

- [ ] All routes require authentication (except public endpoints)
- [ ] Ownership middleware on all protected routes
- [ ] 403 returned for unauthorized access
- [ ] Rate limiting implemented
- [ ] CORS properly configured
- [ ] Security headers in place

#### Input Validation

- [ ] Zod schemas for all inputs
- [ ] XSS prevention with DOMPurify
- [ ] SQL injection protection (parameterized queries)
- [ ] File upload restrictions
- [ ] Request size limits

#### Authentication

- [ ] Strong password requirements
- [ ] Account lockout after failed attempts
- [ ] Session timeout implemented
- [ ] Secure token storage
- [ ] Password reset flow secure

#### Monitoring

- [ ] Audit logging active
- [ ] Failed login monitoring
- [ ] RLS violation alerts
- [ ] Performance monitoring
- [ ] Error tracking

### Testing Security

```bash
# Run security test suite
cd packages/hub
pnpm test security.test.ts

# Check for hardcoded secrets
grep -r "secret\|password\|key" --include="*.ts" --include="*.js" --exclude-dir=node_modules .

# Verify RLS is enabled
echo "SELECT tablename, rowsecurity FROM pg_tables WHERE schemaname = 'public';" | psql $DATABASE_URL

# Test authentication
curl -X POST http://localhost:3001/api/v1/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"wrongpassword"}'
# Should fail after 5 attempts

# Test ownership protection
# As User A, create a trip
curl -X POST http://localhost:3001/api/v1/trips \
  -H "Authorization: Bearer $USER_A_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"title": "My Secret Trip"}'

# As User B, try to access it (should fail with 403)
curl -X GET http://localhost:3001/api/v1/trips/$TRIP_ID \
  -H "Authorization: Bearer $USER_B_TOKEN"
```

## Best Practices

### Development Security

1. **Code Reviews**
   - Check for hardcoded credentials
   - Verify no .env files in commits
   - Review API endpoint security
   - Ensure proper error handling

2. **Development Environment**
   - Use placeholder values in documentation
   - Create .env.example files with dummy values
   - Never use production credentials locally
   - Regular dependency updates

3. **Git Security**
   - Use .gitignore to exclude sensitive files
   - Clean git history if credentials were committed
   - Use signed commits
   - Enable branch protection

### Production Security

1. **Deployment**
   - Use environment variables in hosting platform
   - Enable secret rotation
   - Implement proper CORS policies
   - Use HTTPS everywhere
   - Enable security headers

2. **Access Control**
   - Implement proper RLS policies
   - Use least-privilege principle
   - Regular security audits
   - Monitor access patterns

3. **Monitoring**
   - Enable audit logging
   - Monitor for suspicious activity
   - Set up alerts for failed auth attempts
   - Regular security reviews

### Security Culture

1. **Training**
   - Regular security awareness training
   - Incident response drills
   - Security best practices documentation
   - Threat modeling sessions

2. **Communication**
   - Clear security incident reporting
   - Regular security updates
   - Documented security procedures
   - Security contact: <EMAIL>

3. **Continuous Improvement**
   - Regular penetration testing
   - Security metrics tracking
   - Vulnerability scanning
   - Security roadmap updates

## Compliance & Standards

### OWASP Top 10 Compliance

- **A01:2021 – Broken Access Control**: RLS + Ownership middleware
- **A02:2021 – Cryptographic Failures**: Environment variables + HTTPS
- **A03:2021 – Injection**: Parameterized queries + Input validation
- **A04:2021 – Insecure Design**: Threat modeling + Security reviews
- **A05:2021 – Security Misconfiguration**: Security headers + Error handling
- **A06:2021 – Vulnerable Components**: Dependency scanning + Updates
- **A07:2021 – Authentication Failures**: Rate limiting + Strong passwords
- **A08:2021 – Software and Data Integrity**: Audit logs + Validation
- **A09:2021 – Security Logging & Monitoring**: Comprehensive logging
- **A10:2021 – Server-Side Request Forgery**: URL validation + Allowlists

### Future Security Enhancements

1. **Short-term (Next Sprint)**
   - Multi-factor authentication (MFA)
   - Advanced rate limiting with IP reputation
   - Security scanning in CI/CD
   - Automated security alerts

2. **Medium-term (Next Quarter)**
   - OAuth2/OIDC integration
   - Hardware security key support
   - Bug bounty program
   - SOC2 preparation

3. **Long-term (Next Year)**
   - Zero-trust network architecture
   - Advanced threat detection
   - Security operations center (SOC)
   - Compliance certifications

## Contact

For security concerns or to report vulnerabilities:

- Email: <EMAIL>
- Response time: Within 24 hours for critical issues

Remember: Security is not optional. Every shortcut today equals a potential breach tomorrow.
