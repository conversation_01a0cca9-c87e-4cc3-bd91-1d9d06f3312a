# TravelViz Tech Stack & Build System

## Build System
- **Package Manager**: pnpm 9+ (required)
- **Node Version**: 20+ (specified in .nvmrc)
- **Monorepo**: pnpm workspaces with TypeScript project references
- **Build Tool**: Native TypeScript compiler with Next.js for web

## Tech Stack

### Frontend (@travelviz/web)
- **Framework**: Next.js 15 with App Router
- **Language**: TypeScript 5.4+ (strict mode)
- **Styling**: Tailwind CSS 3.3 + shadcn/ui components
- **State Management**: Zustand + React Query
- **UI Components**: Radix UI primitives
- **Maps**: Mapbox GL JS + react-map-gl
- **Drag & Drop**: @dnd-kit
- **Forms**: React Hook Form + Zod validation
- **Animation**: Framer Motion
- **Testing**: Vitest + Playwright + Testing Library

### Backend (@travelviz/hub)
- **Framework**: Express.js with TypeScript
- **Database**: Supabase (PostgreSQL)
- **Authentication**: Supabase Auth + JWT
- **Validation**: Zod schemas
- **File Processing**: PDF parsing, document upload
- **Rate Limiting**: express-rate-limit
- **Security**: Helmet, CORS, compression

### Shared (@travelviz/shared)
- **Types**: Shared TypeScript interfaces and DTOs
- **Validation**: Zod schemas for API contracts
- **Utils**: Common utility functions

## Common Commands

### Development
```bash
# Start all services (hub:3001, web:3000)
pnpm dev

# Start individual packages
pnpm --filter @travelviz/web dev
pnpm --filter @travelviz/hub dev

# Build shared package (required first)
pnpm --filter @travelviz/shared build
```

### Building & Testing
```bash
# Build all packages
pnpm build

# Type check all packages
pnpm type-check

# Lint all packages
pnpm lint

# Run tests
pnpm test
pnpm test:coverage

# E2E tests
pnpm --filter @travelviz/web test:e2e
```

### Package Management
```bash
# Add dependency to specific package
pnpm add <package> --filter @travelviz/web
pnpm add -D <package> --filter @travelviz/hub

# Add to workspace root
pnpm add -D <package> -w
```

## Code Quality Tools
- **ESLint**: TypeScript recommended rules
- **Prettier**: Consistent formatting (single quotes, 2 spaces)
- **Husky**: Git hooks for pre-commit validation
- **Commitlint**: Conventional commit messages
- **TypeScript**: Strict mode with project references

## Performance Requirements
- **Bundle Size**: < 250KB gzipped
- **Lighthouse Scores**: 95+ performance, 100 accessibility
- **Memory**: Optimized with --max-old-space-size=6144