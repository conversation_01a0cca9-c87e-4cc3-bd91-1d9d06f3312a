import { Activity } from '../lib/supabase';
import { travelpayoutsService } from './travelpayouts.service';
import { logger } from '../utils/logger';

interface AffiliateConfig {
  minCommission: number; // $10 minimum rule
  commissionRates: {
    flight: number;
    hotel: number;
    car: number;
    activity: number;
  };
}

class AffiliateLinkInjectorService {
  private config: AffiliateConfig = {
    minCommission: 10,
    commissionRates: {
      flight: 0.02,      // 2% for flights
      hotel: 0.04,       // 4% for hotels  
      car: 0.05,         // 5% for car rentals
      activity: 0.08,    // 8% for activities/tours
    },
  };

  /**
   * Process activities and inject affiliate links where applicable
   */
  async injectAffiliateLinks(activities: Activity[]): Promise<Activity[]> {
    const processedActivities = await Promise.all(
      activities.map(async (activity) => {
        try {
          // Skip if no booking URL or already has affiliate URL
          if (!activity.booking_url || activity.affiliate_url) {
            return activity;
          }

          // Determine affiliate type based on activity type
          const affiliateType = this.getAffiliateType(activity.type);
          if (!affiliateType) {
            return activity;
          }

          // Check if meets minimum commission requirement
          if (!this.meetsMinimumCommission(activity.price || 0, affiliateType)) {
            logger.info(`Activity ${activity.id} doesn't meet $10 minimum commission`);
            return activity;
          }

          // Generate affiliate URL
          const affiliateUrl = this.generateAffiliateUrl(
            activity.booking_url,
            affiliateType,
            activity
          );

          return {
            ...activity,
            affiliate_url: affiliateUrl,
            affiliate_type: affiliateType,
          };
        } catch (error) {
          logger.error(`Failed to inject affiliate link for activity ${activity.id}:`, { error: error instanceof Error ? error.message : String(error) });
          return activity;
        }
      })
    );

    return processedActivities;
  }

  /**
   * Detect bookable activities and their types
   */
  detectBookableActivities(activities: Activity[]): Activity[] {
    return activities.filter((activity) => {
      // Check if activity has indicators of being bookable
      const hasBookingUrl = !!activity.booking_url;
      const hasPrice = activity.price && activity.price > 0;
      const isBookableType = this.isBookableActivityType(activity.type);
      
      // Check description for booking-related keywords
      const bookingKeywords = [
        'book', 'reserve', 'ticket', 'admission', 'tour',
        'flight', 'hotel', 'accommodation', 'rental'
      ];
      const descriptionHasBookingKeywords = activity.description?.toLowerCase()
        .split(' ')
        .some(word => bookingKeywords.includes(word));

      return hasBookingUrl || (hasPrice && (isBookableType || descriptionHasBookingKeywords));
    });
  }

  /**
   * Auto-detect booking URLs from activity descriptions
   */
  extractBookingUrls(activity: Activity): string[] {
    const urls: string[] = [];
    
    if (!activity.description) return urls;

    // Common booking domains
    const bookingDomains = [
      'booking.com', 'hotels.com', 'expedia.com', 'agoda.com',
      'airbnb.com', 'vrbo.com', 'kayak.com', 'skyscanner.com',
      'viator.com', 'getyourguide.com', 'tripadvisor.com'
    ];

    // Extract URLs from description
    const urlRegex = /https?:\/\/(www\.)?[-a-zA-Z0-9@:%._+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b([-a-zA-Z0-9()@:%_+.~#?&//=]*)/g;
    const matches = activity.description.match(urlRegex) || [];

    // Filter for booking-related URLs
    const bookingUrls = matches.filter(url => 
      bookingDomains.some(domain => url.includes(domain))
    );

    return bookingUrls;
  }

  /**
   * Determine affiliate type based on activity type
   */
  private getAffiliateType(activityType: string): 'flight' | 'hotel' | 'car' | 'activity' | null {
    const typeMapping: Record<string, 'flight' | 'hotel' | 'car' | 'activity' | null> = {
      'flight': 'flight',
      'accommodation': 'hotel',
      'transport': 'car',
      'activity': 'activity',
      'dining': null,
      'shopping': null,
      'car_rental': 'car',
      'tour': 'activity',
      'sightseeing': 'activity',
      'entertainment': 'activity',
      'other': null,
    };

    return typeMapping[activityType] || null;
  }

  /**
   * Check if activity type is typically bookable
   */
  private isBookableActivityType(type: string): boolean {
    const bookableTypes = [
      'flight', 
      'accommodation', 
      'transport',
      'activity'
    ];
    return bookableTypes.includes(type);
  }

  /**
   * Check if price meets minimum commission requirement
   */
  private meetsMinimumCommission(price: number, type: 'flight' | 'hotel' | 'car' | 'activity'): boolean {
    const estimatedCommission = price * this.config.commissionRates[type];
    return estimatedCommission >= this.config.minCommission;
  }

  /**
   * Generate affiliate URL based on type and partner
   */
  private generateAffiliateUrl(
    originalUrl: string, 
    type: 'flight' | 'hotel' | 'car' | 'activity',
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    activity: Activity
  ): string {
    // For Travelpayouts partners - only if API key is configured
    if (this.isTravelpayoutsPartner(originalUrl) && process.env.TRAVELPAYOUTS_API_KEY) {
      try {
        return travelpayoutsService.generateAffiliateUrl(originalUrl, type as 'flight' | 'hotel');
      } catch (error) {
        logger.warn('Travelpayouts affiliate URL generation failed, using original URL', { error: error instanceof Error ? error.message : String(error) });
        return originalUrl;
      }
    }

    // For other partners, implement their specific affiliate link generation
    // This is where you'd add support for other affiliate networks
    
    // Default: return original URL if no affiliate program detected
    return originalUrl;
  }

  /**
   * Check if URL is from a Travelpayouts partner
   */
  private isTravelpayoutsPartner(url: string): boolean {
    const travelpayoutsPartners = [
      'aviasales.com', 'jetradar.com', 'hotellook.com',
      'booking.com', 'agoda.com', 'hostelworld.com',
      'rentalcars.com', 'economybookings.com'
    ];

    return travelpayoutsPartners.some(partner => url.includes(partner));
  }

  /**
   * Batch process activities for a trip
   */
  async processTrip(tripId: string, activities: Activity[]): Promise<{
    processed: number;
    injected: number;
    totalPotentialRevenue: number;
  }> {
    const bookableActivities = this.detectBookableActivities(activities);
    const processedActivities = await this.injectAffiliateLinks(bookableActivities);
    
    let injected = 0;
    let totalPotentialRevenue = 0;

    processedActivities.forEach((activity, index) => {
      if (activity.affiliate_url && !bookableActivities[index].affiliate_url) {
        injected++;
        const affiliateType = this.getAffiliateType(activity.type);
        if (affiliateType && activity.price) {
          totalPotentialRevenue += activity.price * this.config.commissionRates[affiliateType];
        }
      }
    });

    logger.info(`Processed trip ${tripId}: ${injected}/${bookableActivities.length} affiliate links injected`);
    logger.info(`Potential revenue: $${totalPotentialRevenue.toFixed(2)}`);

    return {
      processed: bookableActivities.length,
      injected,
      totalPotentialRevenue,
    };
  }

  /**
   * Update configuration
   */
  updateConfig(config: Partial<AffiliateConfig>) {
    this.config = { ...this.config, ...config };
  }
}

export const affiliateLinkInjector = new AffiliateLinkInjectorService();