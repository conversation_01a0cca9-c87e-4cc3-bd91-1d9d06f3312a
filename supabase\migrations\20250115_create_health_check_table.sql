-- Create health check table for monitoring database connectivity
CREATE TABLE IF NOT EXISTS _health_check (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  timestamp TIMESTAMPTZ NOT NULL DEFAULT now(),
  status TEXT NOT NULL DEFAULT 'healthy',
  metadata JSONB
);

-- Add index for timestamp queries
CREATE INDEX idx_health_check_timestamp ON _health_check(timestamp DESC);

-- Add a cleanup function to remove old health check entries
CREATE OR REPLACE FUNCTION cleanup_old_health_checks()
RETURNS void AS $$
BEGIN
  DELETE FROM _health_check 
  WHERE timestamp < now() - INTERVAL '7 days';
END;
$$ LANGUAGE plpgsql;

-- Add RLS policies for health check table
ALTER TABLE _health_check ENABLE ROW LEVEL SECURITY;

-- Allow authenticated users to insert health checks
CREATE POLICY "Allow authenticated users to insert health checks" ON _health_check
  FOR INSERT
  TO authenticated
  WITH CHECK (true);

-- Allow anyone to read health checks (for monitoring)
CREATE POLICY "Allow public read access to health checks" ON _health_check
  FOR SELECT
  TO public
  USING (true);

-- Add comment
COMMENT ON TABLE _health_check IS 'Table for monitoring database health and connectivity';