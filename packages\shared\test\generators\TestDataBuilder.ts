import type { Trip, Activity, ActivityType } from '../../src/types';
import { faker } from '@faker-js/faker';

/**
 * Base builder class with common functionality
 */
abstract class BaseBuilder<T> {
  protected data: Partial<T> = {};

  with(overrides: Partial<T>): this {
    Object.assign(this.data, overrides);
    return this;
  }

  abstract build(): T;
}

/**
 * Trip builder with edge case support
 */
export class TripBuilder extends BaseBuilder<Trip> {
  constructor() {
    super();
    // Default valid trip
    this.data = {
      id: faker.string.uuid(),
      user_id: faker.string.uuid(),
      title: faker.location.city() + ' Adventure',
      status: 'draft',
      visibility: 'private',
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      metadata: {},
      tags: [],
      budget_currency: 'USD',
    };
  }

  withPartialDates(): this {
    this.data.start_date = undefined;
    this.data.end_date = faker.date.future().toISOString();
    return this;
  }

  withMixedLanguages(): this {
    this.data.title = `${faker.location.city()} 旅行 - Voyage à ${faker.location.city()}`;
    this.data.description = 'Un voyage incroyable 素晴らしい旅 amazing trip';
    return this;
  }

  withLongContent(): this {
    this.data.title = faker.lorem.words(50);
    this.data.description = faker.lorem.paragraphs(100);
    return this;
  }

  withCorruptedData(): this {
    console.warn('⚠️ SECURITY WARNING: withCorruptedData() contains null bytes and should NEVER be used in production');
    console.warn('⚠️ This method is for testing data sanitization only');
    
    this.data.title = '���� Corrupted ����';
    this.data.metadata = { 
      corrupt: '\u0000\u0001\u0002',
      warning: 'TEST_DATA_ONLY_DO_NOT_USE_IN_PRODUCTION'
    };
    return this;
  }

  build(): Trip {
    return this.data as Trip;
  }
}

/**
 * Activity builder with edge case support
 */
export class ActivityBuilder extends BaseBuilder<Activity> {
  constructor() {
    super();
    // Default valid activity
    this.data = {
      id: faker.string.uuid(),
      trip_id: faker.string.uuid(),
      title: faker.company.name() + ' Visit',
      type: 'activity' as ActivityType,
      position: 0,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      metadata: {},
      attachments: [],
      currency: 'USD',
    };
  }

  withPartialLocation(): this {
    this.data.location = {
      address: faker.location.streetAddress(),
      // Missing lat/lng
    };
    return this;
  }

  withVagueTime(): this {
    this.data.start_time = 'sometime in the morning';
    this.data.end_time = 'late afternoon';
    return this;
  }

  withMultiplePrices(): this {
    this.data.price = 100;
    this.data.metadata = {
      prices: {
        adult: 100,
        child: 50,
        senior: 75,
      },
      currencies: ['USD', 'EUR', 'JPY'],
    };
    return this;
  }

  withEmoji(): this {
    this.data.title = '🌟 Amazing Place 🎉';
    this.data.notes = 'Must see! 👀 Don\'t forget camera 📸';
    return this;
  }

  build(): Activity {
    return this.data as Activity;
  }
}

/**
 * AI Conversation builder for import testing
 */
export class ConversationBuilder {
  private messages: string[] = [];

  addUserMessage(content: string): this {
    this.messages.push(`User: ${content}`);
    return this;
  }

  addAssistantMessage(content: string): this {
    this.messages.push(`Assistant: ${content}`);
    return this;
  }

  withSimpleItinerary(): this {
    this.addUserMessage('Plan a 3-day trip to Paris');
    this.addAssistantMessage(`Here's your 3-day Paris itinerary:
    
Day 1:
- 9:00 AM: Eiffel Tower visit
- 1:00 PM: Lunch at Café de Flore
- 3:00 PM: Louvre Museum
- 7:00 PM: Seine River cruise

Day 2:
- 10:00 AM: Versailles Palace
- 2:00 PM: Lunch in Versailles
- 5:00 PM: Return to Paris
- 7:00 PM: Dinner in Montmartre

Day 3:
- 9:00 AM: Notre-Dame Cathedral
- 11:00 AM: Sainte-Chapelle
- 2:00 PM: Latin Quarter exploration
- 6:00 PM: Farewell dinner`);
    return this;
  }

  withComplexMultiCity(): this {
    this.addUserMessage('I want to visit London, Paris, and Rome in 10 days');
    this.addAssistantMessage(faker.lorem.paragraphs(20));
    return this;
  }

  withPartialDates(): this {
    this.addAssistantMessage(`Your trip:
- Sometime in March: Arrive in Tokyo
- A few days later: Visit Kyoto
- End of the month: Return home`);
    return this;
  }

  withMixedLanguages(): this {
    this.addAssistantMessage(`Jour 1 - パリ:
- 朝: Tour Eiffel 参观
- Déjeuner: 在咖啡厅
- 下午: Musée du Louvre
- Soirée: クルーズ on the Seine`);
    return this;
  }

  withCorruptedText(): this {
    console.warn('⚠️ SECURITY WARNING: withCorruptedText() creates potentially unsafe content for testing only');
    
    const corrupted = faker.lorem.paragraph().split('').map((char, i) => 
      i % 10 === 0 ? '�' : char
    ).join('');
    this.addAssistantMessage(corrupted + ' [TEST_DATA_ONLY]');
    return this;
  }

  build(): string {
    return this.messages.join('\n\n');
  }
}

/**
 * Main test data builder factory
 */
export class TestDataBuilder {
  static trip(): TripBuilder {
    return new TripBuilder();
  }

  static activity(): ActivityBuilder {
    return new ActivityBuilder();
  }

  static conversation(): ConversationBuilder {
    return new ConversationBuilder();
  }

  /**
   * Generate multiple items with variations
   */
  static trips(count: number, modifier?: (builder: TripBuilder, index: number) => void): Trip[] {
    return Array.from({ length: count }, (_, i) => {
      const builder = new TripBuilder();
      if (modifier) modifier(builder, i);
      return builder.build();
    });
  }

  static activities(count: number, tripId: string, modifier?: (builder: ActivityBuilder, index: number) => void): Activity[] {
    return Array.from({ length: count }, (_, i) => {
      const builder = new ActivityBuilder().with({ trip_id: tripId, position: i });
      if (modifier) modifier(builder, i);
      return builder.build();
    });
  }
}