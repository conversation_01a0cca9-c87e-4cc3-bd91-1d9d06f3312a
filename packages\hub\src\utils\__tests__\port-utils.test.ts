import { describe, it, expect, beforeAll, afterAll } from 'vitest';
import { createServer } from 'net';
import { isPortAvailable, findAvailablePort, getAvailablePort } from '../port-utils';

describe('Port Utils', () => {
  let testServer: any;
  const testPort = 9999;

  beforeAll(async () => {
    // Start a test server to occupy a port
    testServer = createServer();
    await new Promise<void>((resolve) => {
      testServer.listen(testPort, () => {
        resolve();
      });
    });
  });

  afterAll(async () => {
    // Clean up test server
    if (testServer) {
      await new Promise<void>((resolve) => {
        testServer.close(() => {
          resolve();
        });
      });
    }
  });

  describe('isPortAvailable', () => {
    it('should return false for occupied port', async () => {
      const available = await isPortAvailable(testPort);
      expect(available).toBe(false);
    });

    it('should return true for available port', async () => {
      const available = await isPortAvailable(testPort + 1);
      expect(available).toBe(true);
    });
  });

  describe('findAvailablePort', () => {
    it('should find next available port when starting port is occupied', async () => {
      const port = await findAvailablePort(testPort, 5);
      expect(port).toBe(testPort + 1);
    });

    it('should return starting port if available', async () => {
      const port = await findAvailablePort(testPort + 10, 5);
      expect(port).toBe(testPort + 10);
    });

    it('should throw error if no ports available in range', async () => {
      // This test assumes ports 9999-10003 are occupied (we only have 9999 occupied)
      // So it should find 10000, but let's test the error case with a very limited range
      await expect(findAvailablePort(testPort, 1)).rejects.toThrow();
    });
  });

  describe('getAvailablePort', () => {
    it('should use default port when no PORT env var', async () => {
      const originalPort = process.env.PORT;
      delete process.env.PORT;
      
      const port = await getAvailablePort(testPort + 20);
      expect(port).toBe(testPort + 20);
      
      // Restore original PORT env var
      if (originalPort) {
        process.env.PORT = originalPort;
      }
    });

    it('should use PORT env var when available', async () => {
      const originalPort = process.env.PORT;
      process.env.PORT = String(testPort + 30);
      
      const port = await getAvailablePort(3001);
      expect(port).toBe(testPort + 30);
      
      // Restore original PORT env var
      if (originalPort) {
        process.env.PORT = originalPort;
      } else {
        delete process.env.PORT;
      }
    });

    it('should find alternative when PORT env var is occupied', async () => {
      const originalPort = process.env.PORT;
      process.env.PORT = String(testPort);
      
      const port = await getAvailablePort(3001);
      expect(port).toBe(testPort + 1);
      
      // Restore original PORT env var
      if (originalPort) {
        process.env.PORT = originalPort;
      } else {
        delete process.env.PORT;
      }
    });
  });
});
