import { getSupabaseClient } from '../../../src/lib/supabase';

/**
 * Test cleanup utilities to ensure tests don't interfere with each other
 */
export class TestCleanup {
  private static instance: TestCleanup;
  private supabase = getSupabaseClient();
  private testDataIds: Set<string> = new Set();

  private constructor() {}

  static getInstance(): TestCleanup {
    if (!TestCleanup.instance) {
      TestCleanup.instance = new TestCleanup();
    }
    return TestCleanup.instance;
  }

  /**
   * Track test data for cleanup
   */
  trackTestData(table: string, id: string): void {
    this.testDataIds.add(`${table}:${id}`);
  }

  /**
   * Clean up all tracked test data
   */
  async cleanupAll(): Promise<void> {
    console.log(`🧹 Cleaning up ${this.testDataIds.size} test records...`);
    
    const cleanup = Array.from(this.testDataIds).map(async (entry) => {
      const [table, id] = entry.split(':');
      try {
        await this.cleanupTable(table, id);
      } catch (error) {
        console.warn(`Failed to cleanup ${table}:${id}:`, error);
      }
    });

    await Promise.all(cleanup);
    this.testDataIds.clear();
    console.log('✅ Test cleanup completed');
  }

  /**
   * Clean up specific table records
   */
  private async cleanupTable(table: string, id: string): Promise<void> {
    switch (table) {
      case 'trips':
        await this.cleanupTrips(id);
        break;
      case 'activities':
        await this.cleanupActivities(id);
        break;
      case 'import_requests':
        await this.cleanupImportRequests(id);
        break;
      default:
        console.warn(`Unknown table for cleanup: ${table}`);
    }
  }

  /**
   * Clean up trips and their related data
   */
  async cleanupTrips(tripId?: string): Promise<void> {
    const query = tripId 
      ? this.supabase.from('trips').delete().eq('id', tripId)
      : this.supabase.from('trips').delete().like('title', 'Test Trip%');
    
    const { error } = await query;
    if (error && !error.message.includes('No rows found')) {
      console.warn('Trip cleanup error:', error);
    }
  }

  /**
   * Clean up activities
   */
  async cleanupActivities(activityId?: string): Promise<void> {
    const query = activityId
      ? this.supabase.from('activities').delete().eq('id', activityId)
      : this.supabase.from('activities').delete().like('title', 'Test Activity%');
    
    const { error } = await query;
    if (error && !error.message.includes('No rows found')) {
      console.warn('Activity cleanup error:', error);
    }
  }

  /**
   * Clean up import requests
   */
  async cleanupImportRequests(importId?: string): Promise<void> {
    const query = importId
      ? this.supabase.from('import_requests').delete().eq('id', importId)
      : this.supabase.from('import_requests').delete().like('source_url', '%test%');
    
    const { error } = await query;
    if (error && !error.message.includes('No rows found')) {
      console.warn('Import request cleanup error:', error);
    }
  }

  /**
   * Clean up test data by pattern
   */
  async cleanupByPattern(): Promise<void> {
    console.log('🧹 Cleaning up test data by pattern...');
    
    await Promise.all([
      this.cleanupTrips(),
      this.cleanupActivities(), 
      this.cleanupImportRequests()
    ]);

    console.log('✅ Pattern-based cleanup completed');
  }

  /**
   * Reset for new test run
   */
  reset(): void {
    this.testDataIds.clear();
  }
}

/**
 * Global test cleanup instance
 */
export const testCleanup = TestCleanup.getInstance();

/**
 * Helper for tracking and cleaning up test data
 */
export const withTestCleanup = async (testFn: () => Promise<void>) => {
  try {
    await testFn();
  } finally {
    await testCleanup.cleanupAll();
  }
};