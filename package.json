{"name": "travelviz", "version": "1.0.0", "engines": {"node": ">=20.0.0 <25.0.0", "pnpm": ">=9.0.0"}, "private": true, "workspaces": ["packages/*"], "scripts": {"dev": "node scripts/dev-services-enhanced.js", "dev:basic": "node scripts/dev-services.js", "dev:concurrently": "concurrently \"pnpm --filter @travelviz/hub dev\" \"pnpm --filter @travelviz/web dev\" --names \"hub,web\" --prefix-colors \"blue,green\" --handle-input", "build": "pnpm --filter @travelviz/shared build && pnpm --filter @travelviz/hub build && pnpm --filter @travelviz/web build", "start": "concurrently \"pnpm --filter @travelviz/hub start\" \"pnpm --filter @travelviz/web start\"", "lint": "pnpm --filter \"./packages/*\" lint", "lint:fix": "pnpm --filter \"./packages/*\" lint --fix", "type-check": "pnpm --filter \"./packages/*\" type-check", "test": "cross-env NODE_OPTIONS='--max-old-space-size=6144 --expose-gc' vitest run --no-coverage", "test:watch": "cross-env NODE_OPTIONS='--max-old-space-size=6144 --expose-gc' vitest --no-coverage", "test:coverage": "cross-env NODE_OPTIONS='--max-old-space-size=6144 --expose-gc' vitest run --coverage", "test:check-coverage": "cross-env NODE_OPTIONS='--max-old-space-size=6144 --expose-gc' vitest run --coverage && node scripts/check-coverage.js", "test:memory-safe": "node scripts/test-memory-safe.js", "tdd": "concurrently \"pnpm test:watch\" \"pnpm dev\"", "test:create": "node scripts/create-test.js", "test:qa": "pnpm --filter \"./packages/*\" test", "test:pdf-import": "node scripts/run-pdf-import-test.js", "test:pdf-import-force": "node scripts/run-pdf-import-test.js --force", "validate:test-env": "node scripts/validate-test-environment.js", "clean": "pnpm --filter \"./packages/*\" clean || true", "clean:all": "pnpm clean && rm -rf node_modules packages/*/node_modules", "format": "prettier --write .", "format:check": "prettier --check .", "prepare": "node -e \"if (process.env.NODE_ENV !== 'production') { require('child_process').execSync('husky', {stdio: 'inherit'}); }\"", "postinstall": "pnpm --filter @travelviz/shared build"}, "devDependencies": {"@commitlint/cli": "^19.8.1", "@commitlint/config-conventional": "^19.8.1", "@playwright/test": "^1.53.2", "@types/node": "^24.0.13", "@typescript-eslint/eslint-plugin": "^6.21.0", "@typescript-eslint/parser": "^6.21.0", "@vitest/coverage-v8": "^3.2.4", "@vitest/ui": "^3.2.4", "axios": "^1.10.0", "concurrently": "^8.2.2", "cross-env": "^7.0.3", "eslint": "^8.57.0", "form-data": "^4.0.3", "happy-dom": "^18.0.1", "husky": "^9.1.7", "lint-staged": "^15.2.0", "msw": "^2.10.3", "pdf-parse": "^1.1.1", "playwright": "^1.53.2", "prettier": "^3.3.3", "tsx": "^4.19.1", "typescript": "^5.4.0", "vitest": "^3.2.4"}, "packageManager": "pnpm@9.15.9", "lint-staged": {"*.{ts,tsx,js,jsx}": ["eslint --fix", "prettier --write"], "*.{md,json,yaml,yml}": ["prettier --write"]}, "dependencies": {"web-vitals": "^5.0.3"}}