import { describe, it, expect, beforeAll, afterAll } from 'vitest';
import request from 'supertest';
import { getSupabaseClient } from '../../src/lib/supabase';
import { createServer } from '../../src/server';

// Skip these tests in CI - they require real API keys and authentication
const isCI = process.env.CI === 'true';
const describeIntegration = isCI ? describe.skip : describe;

// Test configuration
const TEST_USER_EMAIL = '<EMAIL>';
const TEST_USER_PASSWORD = 'TestPassword123!';
const API_TIMEOUT = 60000;

// Helper to get auth token
async function getAuthToken() {
  const supabase = getSupabaseClient();
  const { data, error } = await supabase.auth.signInWithPassword({
    email: TEST_USER_EMAIL,
    password: TEST_USER_PASSWORD
  });

  if (error) throw new Error(`Auth failed: ${error.message}`);
  return data.session?.access_token || '';
}

describeIntegration('AI Import E2E Tests', () => {
  let authToken: string;
  let server: any;
  let app: any;

  beforeAll(async () => {
    // Get auth token first
    authToken = await getAuthToken();
    
    // Create and start server
    app = createServer();
    server = app.listen(0); // Random port
  }, API_TIMEOUT);

  afterAll(async () => {
    server?.close();
  });

  it('should handle ChatGPT conversation import', async () => {
    const chatgptContent = 'User: I need a 3-day trip to Rome.\nAssistant: Here is your itinerary...';
    
    const response = await request(server)
      .post('/api/import/conversation')
      .set('Authorization', `Bearer ${authToken}`)
      .send({
        content: chatgptContent,
        source: 'chatgpt'
      })
      .expect(200);

    expect(response.body.success).toBe(true);
    expect(response.body.data).toHaveProperty('tripId');
  }, API_TIMEOUT);
});