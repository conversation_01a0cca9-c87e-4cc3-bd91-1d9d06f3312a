import { logger } from '../utils/logger';
import axios from 'axios';

interface MapAPIValidation {
  provider: 'mapbox' | 'google';
  isValid: boolean;
  error?: string;
  capabilities?: string[];
}

export class MapValidationService {
  private static instance: MapValidationService;
  
  private constructor() {}
  
  static getInstance(): MapValidationService {
    if (!MapValidationService.instance) {
      MapValidationService.instance = new MapValidationService();
    }
    return MapValidationService.instance;
  }
  
  /**
   * Validate all map configurations
   */
  async validateMapConfig(): Promise<{
    mapbox: MapAPIValidation;
    google: MapAPIValidation;
    allValid: boolean;
  }> {
    const [mapbox, google] = await Promise.all([
      this.validateMapboxToken(),
      this.validateGoogleMapsKey()
    ]);
    
    return {
      mapbox,
      google,
      allValid: mapbox.isValid && google.isValid
    };
  }
  
  /**
   * Validate Mapbox access token
   */
  private async validateMapboxToken(): Promise<MapAPIValidation> {
    const token = process.env.MAPBOX_ACCESS_TOKEN;
    
    if (!token) {
      return {
        provider: 'mapbox',
        isValid: false,
        error: 'MAPBOX_ACCESS_TOKEN is not configured'
      };
    }
    
    try {
      // Test the token by making a simple API call
      const response = await axios.get(
        `https://api.mapbox.com/geocoding/v5/mapbox.places/Tokyo.json`,
        {
          params: {
            access_token: token,
            limit: 1
          },
          timeout: 5000
        }
      );
      
      if (response.status === 200) {
        return {
          provider: 'mapbox',
          isValid: true,
          capabilities: ['geocoding', 'maps', 'directions']
        };
      }
      
      return {
        provider: 'mapbox',
        isValid: false,
        error: `Unexpected response status: ${response.status}`
      };
    } catch (error) {
      logger.error('Mapbox validation failed', { error });
      
      if (axios.isAxiosError(error)) {
        if (error.response?.status === 401) {
          return {
            provider: 'mapbox',
            isValid: false,
            error: 'Invalid Mapbox access token'
          };
        }
        if (error.response?.status === 403) {
          return {
            provider: 'mapbox',
            isValid: false,
            error: 'Mapbox token lacks required permissions'
          };
        }
      }
      
      return {
        provider: 'mapbox',
        isValid: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }
  
  /**
   * Validate Google Maps API key
   */
  private async validateGoogleMapsKey(): Promise<MapAPIValidation> {
    const key = process.env.GOOGLE_MAPS_API_KEY;
    
    if (!key) {
      return {
        provider: 'google',
        isValid: false,
        error: 'GOOGLE_MAPS_API_KEY is not configured'
      };
    }
    
    try {
      // Test the key with a simple geocoding request
      const response = await axios.get(
        'https://maps.googleapis.com/maps/api/geocode/json',
        {
          params: {
            address: 'Tokyo, Japan',
            key: key
          },
          timeout: 5000
        }
      );
      
      if (response.data.status === 'OK') {
        return {
          provider: 'google',
          isValid: true,
          capabilities: ['geocoding', 'maps', 'places', 'directions']
        };
      }
      
      // Handle specific error statuses
      switch (response.data.status) {
        case 'REQUEST_DENIED':
          return {
            provider: 'google',
            isValid: false,
            error: 'Google Maps API key is invalid or lacks required permissions'
          };
        case 'OVER_QUERY_LIMIT':
          return {
            provider: 'google',
            isValid: true, // Key is valid but rate limited
            error: 'API quota exceeded',
            capabilities: ['geocoding', 'maps', 'places', 'directions']
          };
        default:
          return {
            provider: 'google',
            isValid: false,
            error: `API returned status: ${response.data.status}`
          };
      }
    } catch (error) {
      logger.error('Google Maps validation failed', { error });
      
      return {
        provider: 'google',
        isValid: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }
  
  /**
   * Test geocoding with fallback
   */
  async geocodeWithFallback(location: string): Promise<{
    lat: number;
    lng: number;
    provider: 'mapbox' | 'google' | 'none';
  } | null> {
    // Try Mapbox first
    const mapboxToken = process.env.MAPBOX_ACCESS_TOKEN;
    if (mapboxToken) {
      try {
        const response = await axios.get(
          `https://api.mapbox.com/geocoding/v5/mapbox.places/${encodeURIComponent(location)}.json`,
          {
            params: {
              access_token: mapboxToken,
              limit: 1
            },
            timeout: 3000
          }
        );
        
        if (response.data.features?.[0]) {
          const [lng, lat] = response.data.features[0].center;
          return { lat, lng, provider: 'mapbox' };
        }
      } catch (error) {
        logger.warn('Mapbox geocoding failed, trying Google', { location, error });
      }
    }
    
    // Fallback to Google
    const googleKey = process.env.GOOGLE_MAPS_API_KEY;
    if (googleKey) {
      try {
        const response = await axios.get(
          'https://maps.googleapis.com/maps/api/geocode/json',
          {
            params: {
              address: location,
              key: googleKey
            },
            timeout: 3000
          }
        );
        
        if (response.data.results?.[0]) {
          const { lat, lng } = response.data.results[0].geometry.location;
          return { lat, lng, provider: 'google' };
        }
      } catch (error) {
        logger.error('Google geocoding also failed', { location, error });
      }
    }
    
    return null;
  }
  
  /**
   * Get static map URL with fallback
   */
  getStaticMapUrl(lat: number, lng: number, zoom = 13): string | null {
    // Try Google Static Maps first (better quality)
    const googleKey = process.env.GOOGLE_MAPS_API_KEY;
    if (googleKey) {
      return `https://maps.googleapis.com/maps/api/staticmap?center=${lat},${lng}&zoom=${zoom}&size=600x400&markers=color:red|${lat},${lng}&key=${googleKey}`;
    }
    
    // Fallback to Mapbox Static Images
    const mapboxToken = process.env.MAPBOX_ACCESS_TOKEN;
    if (mapboxToken) {
      return `https://api.mapbox.com/styles/v1/mapbox/streets-v11/static/pin-s+ff0000(${lng},${lat})/${lng},${lat},${zoom}/600x400@2x?access_token=${mapboxToken}`;
    }
    
    logger.warn('No map API keys configured for static maps');
    return null;
  }
}

export const mapValidationService = MapValidationService.getInstance();