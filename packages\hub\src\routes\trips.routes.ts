import { Router } from 'express';
import { TripsController } from '../controllers/trips.controller';
import { ActivitiesController } from '../controllers/activities.controller';
import { authenticateSupabaseUser } from '../middleware/supabase-auth.middleware';
import { publicTripService } from '../services/publicTrip.service';
import { createSuccessResponse, createErrorResponse } from '@travelviz/shared';
import { validateRequest } from '../middleware/validation.middleware';
import { tripsRateLimit } from '../middleware/rate-limit.middleware';
import { cacheMiddleware, cacheInvalidationMiddleware, cacheConfigs } from '../middleware/cache.middleware';
import {
  verifyTripOwnership,
  verifyCreateOwnership,
  filterOwnedResources,
} from '../middleware/ownership.middleware';
import {
  createTripSchema,
  updateTripSchema,
  createActivitySchema,
  tripIdParamSchema,
  reorderActivitiesSchema,
} from '../schemas/trip.schema';

const router: Router = Router();
const tripsController = new TripsController();
const activitiesController = new ActivitiesController();

// Apply rate limiting to all routes
router.use(tripsRateLimit);

// All trip routes require authentication
router.use(authenticateSupabaseUser);

// Trip CRUD operations with validation
router.post('/', 
  validateRequest(createTripSchema),
  verifyCreateOwnership,
  cacheInvalidationMiddleware(['user-trips']), // Invalidate user trips cache
  tripsController.createTrip.bind(tripsController)
);

router.get('/', 
  filterOwnedResources('trips'),
  cacheMiddleware(cacheConfigs.userTrips), // Cache user trips
  tripsController.getUserTrips.bind(tripsController)
);

router.get('/:id', 
  validateRequest(tripIdParamSchema, 'params'),
  verifyTripOwnership,
  cacheMiddleware(cacheConfigs.tripDetails), // Cache trip details
  tripsController.getTripById.bind(tripsController)
);

router.put('/:id', 
  validateRequest(tripIdParamSchema, 'params'),
  validateRequest(updateTripSchema),
  verifyTripOwnership,
  cacheInvalidationMiddleware((req) => [
    'user-trips',
    `trip-details:${req.params.id}`
  ]), // Invalidate related caches
  tripsController.updateTrip.bind(tripsController)
);

router.delete('/:id', 
  validateRequest(tripIdParamSchema, 'params'),
  verifyTripOwnership,
  cacheInvalidationMiddleware((req) => [
    'user-trips',
    `trip-details:${req.params.id}`
  ]), // Invalidate related caches
  tripsController.deleteTrip.bind(tripsController)
);

// Trip activities with validation
// Create activity under a specific trip
router.post('/:tripId/activities', 
  validateRequest(createActivitySchema),
  verifyTripOwnership,
  cacheInvalidationMiddleware((req) => [
    `trip-details:${req.params.tripId}`
  ]), // Invalidate trip details cache
  tripsController.addActivity.bind(tripsController)
);

// Reorder activities within a trip
router.patch('/:tripId/activities/reorder',
  validateRequest(reorderActivitiesSchema),
  verifyTripOwnership,
  cacheInvalidationMiddleware((req) => [
    `trip-details:${req.params.tripId}`
  ]), // Invalidate trip details cache
  activitiesController.reorderActivities.bind(activitiesController)
);

// Update and delete activities by ID only (no tripId needed)
// These are moved to a separate activities router for proper REST structure

// Clone a public trip
router.post('/:id/clone',
  validateRequest(tripIdParamSchema, 'params'),
  verifyTripOwnership,
  tripsController.cloneTrip.bind(tripsController)
);

// Make trip public/private
router.post('/:id/share',
  validateRequest(tripIdParamSchema, 'params'),
  verifyTripOwnership,
  async (req, res) => {
    try {
      const { id } = req.params;
      const userId = req.user?.id;
      
      if (!userId) {
        return res.status(401).json(createErrorResponse('Unauthorized'));
      }
      
      const slug = await publicTripService.makePublic(id, userId);
      
      res.json(createSuccessResponse({
        slug,
        shareUrl: `${process.env.FRONTEND_URL || 'https://travelviz.ai'}/p/${slug}`,
      }));
    } catch (error) {
      res.status(400).json(createErrorResponse(error instanceof Error ? error.message : 'Failed to share trip'));
    }
  }
);

router.delete('/:id/share',
  validateRequest(tripIdParamSchema, 'params'),
  verifyTripOwnership,
  async (req, res) => {
    try {
      const { id } = req.params;
      const userId = req.user?.id;
      
      if (!userId) {
        return res.status(401).json(createErrorResponse('Unauthorized'));
      }
      
      await publicTripService.makePrivate(id, userId);
      
      res.json(createSuccessResponse({
        message: 'Trip is now private',
      }));
    } catch (error) {
      res.status(400).json(createErrorResponse(error instanceof Error ? error.message : 'Failed to share trip'));
    }
  }
);

export default router;