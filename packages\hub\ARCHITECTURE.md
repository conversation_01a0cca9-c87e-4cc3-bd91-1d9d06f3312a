# Hub Architecture Guide

## Overview

The TravelViz Hub follows a clean, layered architecture with strict separation of concerns:

```
┌─────────────────┐
│     Routes      │  ← Validation, HTTP routing
├─────────────────┤
│   Controllers   │  ← Request/Response handling, orchestration
├─────────────────┤
│    Services     │  ← Business logic, data access
├─────────────────┤
│   Repository    │  ← Database operations (Supabase)
└─────────────────┘
```

## Core Principles

### 1. Type Safety
- All API contracts defined in `@travelviz/shared` DTOs
- Zod schemas for runtime validation
- TypeScript strict mode enforced

### 2. Separation of Concerns
- **Controllers**: HTTP handling only, no business logic
- **Services**: Business logic and orchestration
- **Repositories**: Data access patterns
- **Middleware**: Cross-cutting concerns

### 3. Standardized API Contracts
- Request DTOs with validation schemas
- Response DTOs with consistent structure
- Error responses follow standard format

### 4. Clean Architecture Patterns
- Dependency injection
- Interface segregation
- Single responsibility principle
- Domain-driven design

## Project Structure

```
packages/hub/src/
├── controllers/          # HTTP request handlers
│   ├── base.controller.ts
│   └── *.controller.ts
├── services/            # Business logic
│   ├── authorization.service.ts
│   ├── mapper.service.ts
│   └── trips/
│       ├── trip-crud.service.ts
│       ├── trip-activity.service.ts
│       └── trip-clone.service.ts
├── routes/              # Express route definitions
│   └── *.routes.ts
├── middleware/          # Cross-cutting concerns
│   ├── validation.middleware.ts
│   ├── auth.middleware.ts
│   └── response-transformer.middleware.ts
├── dto/                 # Data transfer objects (in shared)
└── lib/                 # External integrations
    └── supabase.ts
```

## Implementation Patterns

### Controllers

Controllers extend `BaseController` and use the async handler pattern:

```typescript
export class TripsController extends BaseController {
  createTrip = this.asyncHandler(async (req: Request, res: Response) => {
    const user = this.requireAuth(req);
    const data: CreateTripRequest = req.body; // Pre-validated
    
    const result = await this.tripsService.createTrip(data, user.id);
    const response = MapperService.mapTripToResponse(result);
    
    this.sendCreated(res, response);
  });
}
```

### Services

Services contain business logic and are framework-agnostic:

```typescript
export class TripsService {
  async createTrip(data: CreateTripRequest, userId: string): Promise<Trip> {
    // Business validation
    await this.validateTripDates(data);
    
    // Authorization
    await authorizationService.ensurePermission(userId, 'trip', 'create');
    
    // Data transformation
    const dbData = MapperService.mapCreateTripRequest(data, userId);
    
    // Persistence
    return await this.repository.create(dbData);
  }
}
```

### Validation Middleware

All requests are validated using Zod schemas:

```typescript
router.post(
  '/trips',
  authenticateSupabaseUser,
  validateRequest(CreateTripRequestSchema),
  controller.createTrip
);
```

### Authorization Service

Centralized authorization logic:

```typescript
await authorizationService.ensurePermission(
  userId,
  'trip',
  tripId,
  'edit'
);
```

## API Response Format

All responses follow a consistent structure:

### Success Response
```json
{
  "success": true,
  "data": {
    "id": "123",
    "title": "My Trip"
  }
}
```

### Error Response
```json
{
  "success": false,
  "error": "Validation failed",
  "message": "Invalid request data",
  "code": "VALIDATION_ERROR"
}
```

### Paginated Response
```json
{
  "success": true,
  "data": {
    "items": [...],
    "total": 100,
    "page": 1,
    "limit": 10,
    "totalPages": 10
  }
}
```

## Error Handling

Errors are handled at multiple levels:

1. **Validation Errors**: Caught by validation middleware
2. **Business Errors**: Thrown by services, caught by controllers
3. **System Errors**: Caught by global error handler

## Testing Strategy

### Unit Tests
- Test services in isolation
- Mock external dependencies
- Focus on business logic

### Integration Tests
- Test full request/response cycle
- Use test database
- Validate API contracts

### Example Test
```typescript
describe('TripsController', () => {
  it('should create a trip with valid data', async () => {
    const response = await request(app)
      .post('/api/trips')
      .set('Authorization', 'Bearer token')
      .send({
        title: 'Test Trip',
        destination: 'Paris'
      });

    expect(response.status).toBe(201);
    expect(response.body.success).toBe(true);
    expect(response.body.data).toMatchObject({
      title: 'Test Trip',
      destination: 'Paris'
    });
  });
});
```

## Best Practices

1. **Always validate input**: Use Zod schemas at route level
2. **Handle errors gracefully**: Use BaseController error handling
3. **Keep controllers thin**: Business logic belongs in services
4. **Use DTOs**: Never expose internal models directly
5. **Test thoroughly**: Follow TDD practices
6. **Document APIs**: Keep OpenAPI spec updated
7. **Monitor performance**: Use APM tools in production

## Migration Guide

To migrate existing controllers:

1. Extend `BaseController`
2. Move business logic to services
3. Add validation middleware to routes
4. Use DTOs for request/response
5. Implement proper error handling
6. Add comprehensive tests

## Future Improvements

- [ ] Add caching layer
- [ ] Implement event sourcing
- [ ] Add GraphQL support
- [ ] Enhance monitoring
- [ ] Add API versioning