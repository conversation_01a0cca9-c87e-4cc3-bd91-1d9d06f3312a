import { describe, it, expect, beforeAll, afterAll } from 'vitest';
import request from 'supertest';
import { createServer } from '../../src/server';
import { getSupabaseClient } from '../../src/lib/supabase';

// Skip these tests in CI
const isCI = process.env.CI === 'true';
const describeIntegration = describe;

// Test credentials
const TEST_USER_EMAIL = '<EMAIL>';
const TEST_USER_PASSWORD = 'TestPassword123!';
const API_TIMEOUT = 60000;

// Helper to get auth token
async function getAuthToken(): Promise<string> {
  const supabase = getSupabaseClient();
  const { data, error } = await supabase.auth.signInWithPassword({
    email: TEST_USER_EMAIL,
    password: TEST_USER_PASSWORD
  });

  if (error) throw new Error(`Auth failed: ${error.message}`);
  return data.session?.access_token || '';
}

describeIntegration('AI Import API Tests', () => {
  let authToken: string;
  let server: any;
  let app: any;

  beforeAll(async () => {
    // Get auth token first
    authToken = await getAuthToken();
    
    // Create and start server
    app = createServer();
    server = app.listen(0); // Random port
  }, API_TIMEOUT);

  afterAll(async () => {
    server?.close();
  });

  describe('Basic API Tests', () => {
    it('should have import routes available', async () => {
      const response = await request(server)
        .get('/health')
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.status).toBe('healthy');
    });

    it('should require authentication for import endpoints', async () => {
      const response = await request(server)
        .post('/api/v1/import/parse-simple')
        .send({ content: 'test', source: 'test' })
        .expect(401);

      expect(response.body.success).toBe(false);
    });
  });

  describe('Simple AI Parsing', () => {
    it('should parse a simple conversation', async () => {
      const testConversation = {
        content: `User: I want to visit Paris for 3 days next month.
        
AI: Here's a 3-day Paris itinerary:

Day 1:
- 9:00 AM: Visit the Eiffel Tower
- 1:00 PM: Lunch at a café  
- 3:00 PM: Explore the Louvre Museum
- 7:00 PM: Dinner cruise on the Seine

Day 2:
- 10:00 AM: Notre-Dame Cathedral
- 12:00 PM: Walk through Latin Quarter
- 2:00 PM: Visit Musée d'Orsay
- 6:00 PM: Montmartre and Sacré-Cœur

Day 3:
- 9:00 AM: Versailles day trip
- 6:00 PM: Return to Paris
- 8:00 PM: Farewell dinner`,
        source: 'test'
      };

      // Start parsing
      const parseResponse = await request(server)
        .post('/api/v1/import/parse-simple')
        .set('Authorization', `Bearer ${authToken}`)
        .send(testConversation)
        .expect(200);

      expect(parseResponse.body.success).toBe(true);
      expect(parseResponse.body.data).toHaveProperty('importId');
      
      const importId = parseResponse.body.data.importId;
      
      // Wait a bit for parsing to start
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // Check status
      const statusResponse = await request(server)
        .get(`/api/v1/import/parse-simple/${importId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(statusResponse.body.success).toBe(true);
      expect(statusResponse.body.data).toHaveProperty('status');
      expect(['pending', 'processing', 'complete', 'error']).toContain(statusResponse.body.data.status);
      
      // If processing, wait for completion
      if (statusResponse.body.data.status === 'processing' || statusResponse.body.data.status === 'pending') {
        let attempts = 0;
        const maxAttempts = 30; // 30 seconds max
        
        while (attempts < maxAttempts) {
          await new Promise(resolve => setTimeout(resolve, 1000));
          
          const checkResponse = await request(server)
            .get(`/api/v1/import/parse-simple/${importId}`)
            .set('Authorization', `Bearer ${authToken}`);
            
          if (checkResponse.body.data.status === 'complete') {
            expect(checkResponse.body.data.result).toBeDefined();
            expect(checkResponse.body.data.result.title).toBeDefined();
            expect(checkResponse.body.data.result.activities).toBeInstanceOf(Array);
            break;
          } else if (checkResponse.body.data.status === 'error') {
            console.error('Parse error:', checkResponse.body.data.error);
            break;
          }
          
          attempts++;
        }
      }
    }, API_TIMEOUT);

    it('should reject content that is too short', async () => {
      const response = await request(server)
        .post('/api/v1/import/parse-simple')
        .set('Authorization', `Bearer ${authToken}`)
        .send({ content: 'too short', source: 'test' })
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.error).toContain('too short');
    });
  });
});