# Environment Variables Documentation

This document describes all environment variables used in the TravelViz application.

## Overview

TravelViz uses environment variables to configure various services and features. Each package has its own `.env.local` file for local development.

## Hub Package (`packages/hub/.env.local`)

### Core Configuration

| Variable | Required | Description | Example |
|----------|----------|-------------|---------|
| `PORT` | No | Port for the hub server (default: 3001) | `3001` |
| `NODE_ENV` | No | Environment mode | `development`, `production` |
| `FRONTEND_URL` | Yes | URL of the frontend application | `http://localhost:3000` |

### Database

| Variable | Required | Description | Example |
|----------|----------|-------------|---------|
| `SUPABASE_URL` | Yes | Your Supabase project URL | `https://xxxx.supabase.co` |
| `SUPABASE_SERVICE_ROLE_KEY` | Yes | Service role key for server-side operations | `eyJhbGciOiJIUzI1NiIs...` |

### AI Services

| Variable | Required | Description | Example |
|----------|----------|-------------|---------|
| `OPENROUTER_API_KEY` | No* | API key for AI parsing functionality | `sk-or-v1-xxxxx` |

*Optional but recommended. Without this key, AI parsing will not be available and only basic text extraction will work.

**How to obtain**: 
1. Visit https://openrouter.ai
2. Create an account
3. Generate an API key from the dashboard
4. The key should start with `sk-or-`

### Security

| Variable | Required | Description | Example |
|----------|----------|-------------|---------|
| `JWT_SECRET` | Yes | Secret key for JWT token signing | Random 64+ character string |
| `SUPABASE_JWT_SECRET` | Yes | JWT secret from Supabase dashboard | Base64 encoded string |

### Advanced JWT Configuration (Optional)

| Variable | Required | Description | Example |
|----------|----------|-------------|---------|
| `USE_RSA` | No | Use RSA algorithm for JWT instead of HS256 (default: false) | `false` |
| `JWT_PRIVATE_KEY` | No* | RSA private key for JWT signing | `-----BEGIN PRIVATE KEY-----...` |
| `JWT_PUBLIC_KEY` | No* | RSA public key for JWT verification | `-----BEGIN PUBLIC KEY-----...` |
| `JWT_ISSUER` | No | JWT issuer (default: travelviz-api) | `travelviz-api` |
| `JWT_AUDIENCE` | No | JWT audience (default: travelviz-app) | `travelviz-app` |
| `ACCESS_TOKEN_EXPIRY` | No | Access token expiry in seconds (default: 900 - 15 min) | `900` |
| `REFRESH_TOKEN_EXPIRY` | No | Refresh token expiry in seconds (default: 604800 - 7 days) | `604800` |

*Required only if `USE_RSA` is set to true

### Rate Limiting

| Variable | Required | Description | Example |
|----------|----------|-------------|---------|
| `RATE_LIMIT_WINDOW_MS` | No | Rate limit window in milliseconds (default: 900000 - 15 min) | `900000` |
| `RATE_LIMIT_MAX_REQUESTS` | No | Max requests per window (default: 5) | `5` |
| `SKIP_RATE_LIMIT` | No | Skip rate limiting (for testing only, default: false) | `false` |

### Logging

| Variable | Required | Description | Example |
|----------|----------|-------------|---------|
| `LOG_LEVEL` | No | Logging level (default: info) | `debug`, `info`, `warn`, `error` |

### External Services (Optional)

| Variable | Required | Description | Example |
|----------|----------|-------------|---------|
| `MAPBOX_ACCESS_TOKEN` | No | Server-side Mapbox token for geocoding | `sk.eyJ1Ijoi...` |

### Migration Scripts (Development Only)

| Variable | Required | Description | Example |
|----------|----------|-------------|---------|
| `SUPABASE_ACCESS_TOKEN` | No* | Supabase Management API token for migration scripts | `sbp_xxxxx` |

*Only required when running migration scripts manually. Not needed for normal application operation.

## Web Package (`packages/web/.env.local`)

### Core Configuration

| Variable | Required | Description | Example |
|----------|----------|-------------|---------|
| `NEXT_PUBLIC_APP_URL` | Yes | Public URL of the web app | `http://localhost:3000` |
| `NEXT_PUBLIC_API_URL` | Yes | URL of the API server | `http://localhost:3001` |
| `NEXT_PUBLIC_HUB_URL` | Yes | URL of the hub server | `http://localhost:3001` |

### Supabase (Client-side)

| Variable | Required | Description | Example |
|----------|----------|-------------|---------|
| `NEXT_PUBLIC_SUPABASE_URL` | Yes | Your Supabase project URL | `https://xxxx.supabase.co` |
| `NEXT_PUBLIC_SUPABASE_ANON_KEY` | Yes | Anonymous key for client-side operations | `eyJhbGciOiJIUzI1NiIs...` |

### Maps

| Variable | Required | Description | Example |
|----------|----------|-------------|---------|
| `NEXT_PUBLIC_MAPBOX_ACCESS_TOKEN` | No* | Public Mapbox token for map display | `pk.eyJ1Ijoi...` |

*Optional but recommended. Without this token, maps will show a fallback UI.

**How to obtain**:
1. Visit https://www.mapbox.com
2. Create an account
3. Go to Account > Tokens
4. Create a new public token with these scopes:
   - `styles:read` (for map styles)
   - `fonts:read` (for map fonts)
   - `datasets:read` (for datasets)
   - `vision:read` (for static images)

### Feature Flags

| Variable | Required | Description | Example |
|----------|----------|-------------|---------|
| `NEXT_PUBLIC_ENABLE_AI_CHAT` | No | Enable AI chat feature (default: true) | `true` |
| `NEXT_PUBLIC_ENABLE_PRICE_TRACKING` | No | Enable price tracking (default: true) | `true` |
| `NEXT_PUBLIC_ENABLE_OFFLINE_MAPS` | No | Enable offline maps (default: true) | `true` |

### Analytics (Optional)

| Variable | Required | Description | Example |
|----------|----------|-------------|---------|
| `NEXT_PUBLIC_VERCEL_ANALYTICS_ID` | No | Vercel Analytics ID | `xxx` |
| `NEXT_PUBLIC_GOOGLE_ANALYTICS_ID` | No | Google Analytics ID | `G-XXXXXXXXXX` |

## Security Best Practices

1. **Never commit `.env.local` files** - They contain sensitive keys
2. **Use different keys for different environments** - Don't reuse production keys in development
3. **Rotate keys regularly** - Especially if they may have been exposed
4. **Limit key permissions** - Use read-only keys where possible
5. **Store production secrets securely** - Use services like Vercel Environment Variables, AWS Secrets Manager, etc.

## Setting Up for Local Development

1. Copy the example files:
   ```bash
   cp packages/hub/.env.example packages/hub/.env.local
   cp packages/web/.env.example packages/web/.env.local
   ```

2. Fill in the required values in each `.env.local` file

3. Start the development servers:
   ```bash
   pnpm dev
   ```

## Production Deployment

For production deployments on platforms like Vercel or Render:

1. Add all required environment variables to your deployment platform
2. Ensure `NODE_ENV` is set to `production`
3. Use production-grade values for all secrets
4. Enable appropriate security headers and CORS settings

## Troubleshooting

### Common Issues

1. **"OPENROUTER_API_KEY not set" warning**
   - This is normal if you haven't set up AI parsing
   - The app will fall back to basic text extraction
   - To enable AI parsing, sign up at openrouter.ai and add your key

2. **Map shows fallback UI**
   - Check that `NEXT_PUBLIC_MAPBOX_ACCESS_TOKEN` is set in web/.env.local
   - Ensure the token has the correct permissions
   - The token should start with `pk.` for public tokens

3. **Authentication errors**
   - Verify Supabase URL and keys match your project
   - Check that JWT secrets are correctly set
   - Ensure CORS is configured for your domain

4. **Rate limiting too strict/loose**
   - Adjust `RATE_LIMIT_WINDOW_MS` and `RATE_LIMIT_MAX_REQUESTS`
   - Default is 10 requests per 15 minutes for imports

## Additional Resources

- [Supabase Environment Variables](https://supabase.com/docs/guides/hosting/overview#environment-variables)
- [Next.js Environment Variables](https://nextjs.org/docs/basic-features/environment-variables)
- [Mapbox Access Tokens](https://docs.mapbox.com/help/getting-started/access-tokens/)
- [OpenRouter Documentation](https://openrouter.ai/docs)