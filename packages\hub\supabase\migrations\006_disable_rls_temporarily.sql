-- Temporarily disable <PERSON><PERSON> to test if that's the issue
-- Date: 2025-07-07

-- Drop all existing policies first
DROP POLICY IF EXISTS "Users can view their own trips" ON public.trips;
DROP POLICY IF EXISTS "Users can insert their own trips" ON public.trips;
DROP POLICY IF EXISTS "Users can update their own trips" ON public.trips;
DROP POLICY IF EXISTS "Users can delete their own trips" ON public.trips;
DROP POLICY IF EXISTS "Anyone can view public trips" ON public.trips;
DROP POLICY IF EXISTS "Users can manage their trip activities" ON public.activities;

-- Disable R<PERSON> temporarily
ALTER TABLE public.trips DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.activities DISABLE ROW LEVEL SECURITY;

-- Note: This is for testing only! Re-enable RLS with proper policies in production!