import React, { useEffect, useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { animations } from '@/lib/animations';
import { cn } from '@/lib/utils';
import confetti from 'canvas-confetti';

interface SparkleProps {
  className?: string;
  size?: 'sm' | 'md' | 'lg';
  color?: 'orange' | 'blue' | 'green' | 'purple';
}

/**
 * Animated sparkle component for success states
 */
export function Sparkle({ className, size = 'md', color = 'orange' }: SparkleProps) {
  const sizeClasses = {
    sm: 'w-3 h-3',
    md: 'w-4 h-4',
    lg: 'w-6 h-6',
  };

  const colorClasses = {
    orange: 'text-orange-500',
    blue: 'text-blue-500',
    green: 'text-green-500',
    purple: 'text-purple-500',
  };

  return (
    <motion.div
      className={cn('inline-block', sizeClasses[size], colorClasses[color], className)}
      variants={animations.sparkle}
      initial="initial"
      animate="animate"
    >
      <svg viewBox="0 0 24 24" fill="currentColor">
        <path d="M12 0L14.59 8.41L23 12L14.59 15.59L12 24L9.41 15.59L1 12L9.41 8.41L12 0Z" />
      </svg>
    </motion.div>
  );
}

interface ConfettiButtonProps {
  children: React.ReactNode;
  variant?: 'default' | 'success' | 'celebration';
  triggerConfetti?: boolean;
  onClick?: (e: React.MouseEvent<HTMLButtonElement>) => void;
  className?: string;
  disabled?: boolean;
  type?: 'button' | 'submit' | 'reset';
}

/**
 * Button that triggers confetti animation on click
 */
export function ConfettiButton({ 
  children, 
  variant = 'default', 
  triggerConfetti = true,
  onClick,
  className,
  disabled,
  type = 'button'
}: ConfettiButtonProps) {
  const handleClick = (e: React.MouseEvent<HTMLButtonElement>) => {
    if (triggerConfetti) {
      // Get button position for confetti origin
      const rect = e.currentTarget.getBoundingClientRect();
      const x = (rect.left + rect.width / 2) / window.innerWidth;
      const y = (rect.top + rect.height / 2) / window.innerHeight;

      // Trigger confetti
      confetti({
        particleCount: variant === 'celebration' ? 150 : 100,
        spread: 70,
        origin: { x, y },
        colors: variant === 'success' 
          ? ['#22c55e', '#16a34a', '#15803d']
          : ['#f97316', '#ea580c', '#dc2626'],
      });
    }
    
    onClick?.(e);
  };

  return (
    <motion.button
      whileHover={animations.hover}
      whileTap={animations.tap}
      onClick={handleClick}
      className={className}
      disabled={disabled}
      type={type}
    >
      {children}
    </motion.button>
  );
}

interface ShimmerSkeletonProps {
  className?: string;
  width?: string | number;
  height?: string | number;
  rounded?: boolean;
}

/**
 * Shimmer loading skeleton
 */
export function ShimmerSkeleton({ 
  className, 
  width = '100%', 
  height = '1rem',
  rounded = false 
}: ShimmerSkeletonProps) {
  return (
    <div
      className={cn(
        'relative overflow-hidden bg-gray-200 dark:bg-gray-800',
        rounded ? 'rounded-full' : 'rounded',
        className
      )}
      style={{ width, height }}
    >
      <motion.div
        className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent dark:via-white/10"
        animate={animations.shimmer}
        style={{
          backgroundImage: 'linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent)',
          backgroundSize: '200% 100%',
        }}
      />
    </div>
  );
}

interface FloatingElementProps {
  children: React.ReactNode;
  className?: string;
  amplitude?: number;
  duration?: number;
  delay?: number;
}

/**
 * Floating animation for subtle movement
 */
export function FloatingElement({ 
  children, 
  className, 
  amplitude = 10, 
  duration = 3,
  delay = 0 
}: FloatingElementProps) {
  return (
    <motion.div
      className={className}
      animate={{
        y: [-amplitude/2, amplitude/2, -amplitude/2],
      }}
      transition={{
        duration,
        delay,
        repeat: Infinity,
        ease: 'easeInOut',
      }}
    >
      {children}
    </motion.div>
  );
}

interface GlowingBorderProps {
  children: React.ReactNode;
  className?: string;
  glowColor?: string;
  intensity?: 'low' | 'medium' | 'high';
}

/**
 * Glowing border effect
 */
export function GlowingBorder({ 
  children, 
  className, 
  glowColor = '#f97316',
  intensity = 'medium' 
}: GlowingBorderProps) {
  const intensityMap = {
    low: '2px',
    medium: '4px',
    high: '8px',
  };

  return (
    <div
      className={cn('relative rounded-lg', className)}
      style={{
        boxShadow: `0 0 ${intensityMap[intensity]} ${glowColor}40`,
        border: `1px solid ${glowColor}60`,
      }}
    >
      {children}
    </div>
  );
}

interface PulsingDotProps {
  className?: string;
  color?: string;
  size?: 'sm' | 'md' | 'lg';
}

/**
 * Pulsing dot indicator
 */
export function PulsingDot({ className, color = '#f97316', size = 'md' }: PulsingDotProps) {
  const sizeClasses = {
    sm: 'w-2 h-2',
    md: 'w-3 h-3',
    lg: 'w-4 h-4',
  };

  return (
    <div className={cn('relative', className)}>
      <motion.div
        className={cn('rounded-full', sizeClasses[size])}
        style={{ backgroundColor: color }}
        animate={animations.pulse}
      />
      <motion.div
        className={cn('absolute inset-0 rounded-full', sizeClasses[size])}
        style={{ backgroundColor: color, opacity: 0.4 }}
        animate={{
          scale: [1, 2, 1],
          opacity: [0.4, 0, 0.4],
        }}
        transition={{
          duration: 2,
          repeat: Infinity,
          ease: 'easeOut',
        }}
      />
    </div>
  );
}

interface TypewriterTextProps {
  text: string;
  className?: string;
  speed?: number;
  showCursor?: boolean;
}

/**
 * Typewriter text animation
 */
export function TypewriterText({ 
  text, 
  className, 
  speed = 50,
  showCursor = true 
}: TypewriterTextProps) {
  const [displayText, setDisplayText] = useState('');
  const [currentIndex, setCurrentIndex] = useState(0);

  useEffect(() => {
    if (currentIndex < text.length) {
      const timeout = setTimeout(() => {
        setDisplayText(prev => prev + text[currentIndex]);
        setCurrentIndex(prev => prev + 1);
      }, speed);
      return () => clearTimeout(timeout);
    }
    return undefined;
  }, [currentIndex, text, speed]);

  return (
    <span className={className}>
      {displayText}
      {showCursor && (
        <motion.span
          animate={{ opacity: [1, 0, 1] }}
          transition={{ duration: 1, repeat: Infinity }}
          className="ml-1"
        >
          |
        </motion.span>
      )}
    </span>
  );
}

interface RandomSparklesProps {
  children: React.ReactNode;
  className?: string;
  count?: number;
  colors?: string[];
}

/**
 * Random sparkles around content
 */
export function RandomSparkles({ 
  children, 
  className, 
  count = 5,
  colors = ['#f97316', '#eab308', '#06b6d4'] 
}: RandomSparklesProps) {
  const [sparkles, setSparkles] = useState<Array<{
    id: number;
    x: number;
    y: number;
    color: string;
    delay: number;
  }>>([]);

  useEffect(() => {
    const newSparkles = Array.from({ length: count }, (_, i) => ({
      id: i,
      x: Math.random() * 100,
      y: Math.random() * 100,
      color: colors[Math.floor(Math.random() * colors.length)],
      delay: Math.random() * 2,
    }));
    setSparkles(newSparkles);
  }, [count, colors]);

  return (
    <div className={cn('relative', className)}>
      {children}
      {sparkles.map(sparkle => (
        <motion.div
          key={sparkle.id}
          className="absolute w-1 h-1 rounded-full pointer-events-none"
          style={{
            left: `${sparkle.x}%`,
            top: `${sparkle.y}%`,
            backgroundColor: sparkle.color,
          }}
          animate={{
            scale: [0, 1, 0],
            opacity: [0, 1, 0],
          }}
          transition={{
            duration: 2,
            delay: sparkle.delay,
            repeat: Infinity,
            repeatDelay: 3,
          }}
        />
      ))}
    </div>
  );
}

// Export all components as a convenience object
export const DelightfulElements = {
  Sparkle,
  ConfettiButton,
  ShimmerSkeleton,
  FloatingElement,
  GlowingBorder,
  PulsingDot,
  TypewriterText,
  RandomSparkles,
};