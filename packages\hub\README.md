# TravelViz Hub - Express Backend

Express.js backend server for the TravelViz application.

## Development

```bash
# Install dependencies
pnpm install

# Run in development mode
pnpm dev

# Build for production
pnpm build

# Run production build
pnpm start
```

## Environment Variables

Create a `.env` file in the `packages/hub` directory with the following variables:

```env
PORT=3001
NODE_ENV=development
FRONTEND_URL=http://localhost:3000
```

## API Endpoints

- `GET /health` - Health check endpoint
- `GET /api/test` - Test endpoint 