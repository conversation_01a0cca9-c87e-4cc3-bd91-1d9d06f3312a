import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { ImportController } from './import.controller';
import { PDFParserService } from '../services/pdf-parser.service';
import { ParserService } from '../services/parser.service';
import { TripsService } from '../services/trips.service';
import { Response } from 'express';
import { AuthenticatedRequest } from '../middleware/auth.middleware';

// Mock services
vi.mock('../services/pdf-parser.service');
vi.mock('../services/parser.service');
vi.mock('../services/trips.service');
vi.mock('../services/import-lock.service', () => ({
  ImportLockService: {
    getInstance: () => ({
      acquireLock: vi.fn().mockReturnValue(true),
      releaseLock: vi.fn()
    })
  }
}));

describe('ImportController - File Upload', () => {
  let importController: ImportController;
  let mockReq: Partial<AuthenticatedRequest>;
  let mockRes: Partial<Response>;
  let mockPDFParser: any;
  let mockParserService: any;
  let mockTripsService: any;

  beforeEach(() => {
    importController = new ImportController();
    
    // Mock request with file
    mockReq = {
      user: { id: 'test-user-id', email: '<EMAIL>' },
      file: {
        buffer: Buffer.from('%PDF-1.4 test content'),
        originalname: 'test-itinerary.pdf',
        mimetype: 'application/pdf',
        size: 1024
      } as Express.Multer.File,
      body: {
        source: 'chatgpt'
      }
    };

    // Mock response
    mockRes = {
      status: vi.fn().mockReturnThis(),
      json: vi.fn()
    };

    // Mock PDF parser
    mockPDFParser = {
      extractText: vi.fn().mockResolvedValue(`
        Day 1: London
        - Morning: Tower Bridge
        - Afternoon: British Museum
        - Evening: West End show
        
        Day 2: Paris
        - Morning: Eiffel Tower
        - Afternoon: Louvre Museum
      `),
      validatePDF: vi.fn().mockReturnValue(true)
    };
    PDFParserService.prototype.extractText = mockPDFParser.extractText;
    PDFParserService.prototype.validatePDF = mockPDFParser.validatePDF;

    // Mock parser service
    mockParserService = {
      parseTextToTrip: vi.fn().mockResolvedValue({
        title: 'London and Paris Trip',
        destination: 'Europe',
        activities: [
          { title: 'Visit Tower Bridge', day: 1, location: 'London' },
          { title: 'British Museum', day: 1, location: 'London' },
          { title: 'Eiffel Tower', day: 2, location: 'Paris' }
        ]
      })
    };
    ParserService.prototype.parseTextToTrip = mockParserService.parseTextToTrip;

    // Mock trips service
    mockTripsService = {
      createTrip: vi.fn().mockResolvedValue({
        id: 'test-trip-id',
        title: 'London and Paris Trip',
        user_id: 'test-user-id'
      }),
      addActivityToTrip: vi.fn().mockResolvedValue({
        id: 'test-activity-id'
      })
    };
    TripsService.prototype.createTrip = mockTripsService.createTrip;
    TripsService.prototype.addActivityToTrip = mockTripsService.addActivityToTrip;
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  describe('parseAndCreateTripFromFile', () => {
    it('should successfully parse PDF and create trip', async () => {
      await importController.parseAndCreateTripFromFile(
        mockReq as AuthenticatedRequest,
        mockRes as Response
      );

      // Verify PDF was validated and parsed
      expect(mockPDFParser.validatePDF).toHaveBeenCalledWith(mockReq.file!.buffer);
      expect(mockPDFParser.extractText).toHaveBeenCalledWith(mockReq.file!.buffer);

      // Verify text was parsed to trip
      expect(mockParserService.parseTextToTrip).toHaveBeenCalled();
      expect(mockParserService.parseTextToTrip).toHaveBeenCalledWith(
        expect.stringContaining('Day 1: London'),
        'chatgpt',
        expect.any(String)
      );

      // Verify trip was created
      expect(mockTripsService.createTrip).toHaveBeenCalled();

      // Verify activities were added
      expect(mockTripsService.addActivityToTrip).toHaveBeenCalledTimes(3);

      // Verify response
      expect(mockRes.status).toHaveBeenCalledWith(201);
      expect(mockRes.json).toHaveBeenCalledWith(
        expect.objectContaining({
          success: true,
          data: expect.objectContaining({
            trip: expect.any(Object),
            importStats: expect.objectContaining({
              totalActivitiesParsed: 3,
              successfullyCreated: 3
            })
          })
        })
      );
    });

    it('should handle invalid PDF', async () => {
      mockPDFParser.validatePDF.mockReturnValue(false);

      await importController.parseAndCreateTripFromFile(
        mockReq as AuthenticatedRequest,
        mockRes as Response
      );

      expect(mockRes.status).toHaveBeenCalledWith(400);
      expect(mockRes.json).toHaveBeenCalledWith(
        expect.objectContaining({
          success: false,
          error: expect.stringContaining('Invalid PDF')
        })
      );
    });

    it('should handle PDF extraction errors', async () => {
      mockPDFParser.extractText.mockRejectedValue(new Error('Failed to extract text'));

      await importController.parseAndCreateTripFromFile(
        mockReq as AuthenticatedRequest,
        mockRes as Response
      );

      expect(mockRes.status).toHaveBeenCalledWith(500);
      expect(mockRes.json).toHaveBeenCalledWith(
        expect.objectContaining({
          success: false,
          error: expect.stringContaining('Failed to extract text')
        })
      );
    });

    it('should handle missing file', async () => {
      mockReq.file = undefined;

      await importController.parseAndCreateTripFromFile(
        mockReq as AuthenticatedRequest,
        mockRes as Response
      );

      expect(mockRes.status).toHaveBeenCalledWith(400);
      expect(mockRes.json).toHaveBeenCalledWith(
        expect.objectContaining({
          success: false,
          error: 'No file uploaded'
        })
      );
    });

    it('should handle empty PDF text', async () => {
      mockPDFParser.extractText.mockResolvedValue('');

      await importController.parseAndCreateTripFromFile(
        mockReq as AuthenticatedRequest,
        mockRes as Response
      );

      expect(mockRes.status).toHaveBeenCalledWith(400);
      expect(mockRes.json).toHaveBeenCalledWith(
        expect.objectContaining({
          success: false,
          error: expect.stringContaining('No text content found')
        })
      );
    });

    // Import lock tests are handled in the mocked service
  });
});