#!/usr/bin/env node

const { chromium } = require('playwright');

async function debugLogin() {
  console.log('🔍 Debugging E2E Login Flow...');
  
  const browser = await chromium.launch({ headless: false });
  const context = await browser.newContext();
  const page = await context.newPage();
  
  // Listen to console messages
  page.on('console', msg => {
    console.log(`[BROWSER] ${msg.type()}: ${msg.text()}`);
  });
  
  // Listen to network requests
  page.on('request', request => {
    if (request.url().includes('login') || request.url().includes('auth')) {
      console.log(`[REQUEST] ${request.method()} ${request.url()}`);
    }
  });
  
  page.on('response', response => {
    if (response.url().includes('login') || response.url().includes('auth')) {
      console.log(`[RESPONSE] ${response.status()} ${response.url()}`);
    }
  });
  
  try {
    // Navigate to homepage
    console.log('1. Navigating to homepage...');
    await page.goto('http://localhost:3000');
    await page.waitForLoadState('networkidle');
    
    // Take screenshot
    await page.screenshot({ path: 'debug-01-homepage.png', fullPage: true });
    console.log('   Screenshot saved: debug-01-homepage.png');
    
    // Navigate to login
    console.log('2. Navigating to login page...');
    await page.goto('http://localhost:3000/login');
    await page.waitForLoadState('networkidle');
    
    // Take screenshot
    await page.screenshot({ path: 'debug-02-login-page.png', fullPage: true });
    console.log('   Screenshot saved: debug-02-login-page.png');
    
    // Fill login form
    console.log('3. Filling login form...');
    await page.fill('input[type="email"]', '<EMAIL>');
    await page.fill('input[type="password"]', 'Flaremmk123!');
    
    // Take screenshot before submit
    await page.screenshot({ path: 'debug-03-form-filled.png', fullPage: true });
    console.log('   Screenshot saved: debug-03-form-filled.png');
    
    // Submit form
    console.log('4. Submitting form...');
    await page.click('button[type="submit"]');
    
    // Wait a bit for any network requests
    console.log('5. Waiting for response...');
    await page.waitForTimeout(5000);
    
    // Take screenshot after submit
    await page.screenshot({ path: 'debug-04-after-submit.png', fullPage: true });
    console.log('   Screenshot saved: debug-04-after-submit.png');
    
    // Check current URL
    const currentUrl = page.url();
    console.log(`6. Current URL: ${currentUrl}`);
    
    // Check if we're on dashboard
    if (currentUrl.includes('/dashboard')) {
      console.log('   ✅ Successfully redirected to dashboard');
    } else if (currentUrl.includes('/login')) {
      console.log('   ⚠️  Still on login page');
      
      // Check for error messages
      const errorElements = await page.locator('.error, [role="alert"], .text-red-500').all();
      if (errorElements.length > 0) {
        for (const element of errorElements) {
          const text = await element.textContent();
          console.log(`   Error message: ${text}`);
        }
      }
    } else {
      console.log(`   ℹ️  On different page: ${currentUrl}`);
    }
    
    // Wait a bit more to see if there's a delayed redirect
    console.log('7. Waiting for potential delayed redirect...');
    await page.waitForTimeout(3000);
    
    const finalUrl = page.url();
    console.log(`8. Final URL: ${finalUrl}`);
    
    // Take final screenshot
    await page.screenshot({ path: 'debug-05-final-state.png', fullPage: true });
    console.log('   Screenshot saved: debug-05-final-state.png');
    
  } catch (error) {
    console.error('❌ Error:', error.message);
    await page.screenshot({ path: 'debug-error.png', fullPage: true });
  } finally {
    await browser.close();
  }
}

debugLogin();