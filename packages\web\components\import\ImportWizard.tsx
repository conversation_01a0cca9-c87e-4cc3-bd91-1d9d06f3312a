'use client';

import { useRef } from 'react';
import { useImport } from '@/contexts/ImportContext';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { StepIndicator } from './StepIndicator';
import { InputStep } from './steps/InputStep';
import { ParsingStep } from './steps/ParsingStep';
import { PreviewStep } from './steps/PreviewStep';
import { CreatingStep } from './steps/CreatingStep';
import { Upload, Brain, CheckCircle, Sparkles, AlertCircle } from 'lucide-react';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { motion } from 'framer-motion';
import { ResponsiveContainer } from '@/components/ui/ResponsiveContainer';
import { useIsMobile } from '@/hooks/useMediaQuery';
import { useTouchDevice, useTouchGestures } from '@/hooks/useTouchDevice';
import { animations } from '@/lib/animations';
import { cn } from '@/lib/utils';

const steps = [
  { id: 'input', label: 'Import Conversation', icon: Upload },
  { id: 'parsing', label: 'AI Processing', icon: Brain },
  { id: 'preview', label: 'Review & Create', icon: CheckCircle }
];

export function ImportWizard() {
  const { currentStep, error, setStep } = useImport();
  const isMobile = useIsMobile();
  const { isTouchDevice } = useTouchDevice();
  const wizardRef = useRef<HTMLDivElement>(null);

  // Touch gestures for mobile step navigation
  useTouchGestures(wizardRef, {
    onSwipeLeft: () => {
      // Navigate to next step if possible
      const stepOrder = ['input', 'parsing', 'preview', 'creating'];
      const currentIndex = stepOrder.indexOf(currentStep);
      if (currentIndex < stepOrder.length - 1 && currentIndex !== -1) {
        // Only allow forward navigation from input step
        if (currentStep === 'input') return; // Requires form submission
      }
    },
    onSwipeRight: () => {
      // Navigate to previous step if possible
      if (currentStep === 'parsing' || currentStep === 'preview') {
        setStep('input');
      }
    },
  });

  return (
    <ResponsiveContainer maxWidth="2xl" className="py-4 md:py-8">
      <div ref={wizardRef}>
        {/* Header */}
        <motion.div 
          variants={animations.pageEnter}
          initial="hidden"
          animate="visible"
          className="text-center mb-6 md:mb-8"
        >
          <motion.div 
            className="inline-flex items-center justify-center w-12 h-12 md:w-16 md:h-16 bg-gradient-to-br from-orange-500 to-pink-500 rounded-full mb-3 md:mb-4 shadow-lg"
            variants={animations.sparkle}
            initial="initial"
            animate="animate"
          >
            <Sparkles className="w-6 h-6 md:w-8 md:h-8 text-white" />
          </motion.div>
          <h1 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold bg-gradient-to-r from-orange-500 to-pink-500 bg-clip-text text-transparent font-['Cal_Sans'] px-4">
            {isMobile ? 'Import AI Chat' : 'Import Your AI Travel Conversation'}
          </h1>
          <p className="text-gray-600 mt-2 text-base md:text-lg lg:text-xl max-w-2xl mx-auto px-4">
            {isMobile 
              ? 'Turn your chat into a visual itinerary'
              : 'Transform your ChatGPT, Claude, or Gemini chat into a visual itinerary in seconds'
            }
          </p>
        </motion.div>

        {/* Main Card */}
        <motion.div
          variants={animations.slideUp}
          initial="hidden"
          animate="visible"
          transition={{ delay: 0.2 }}
        >
          <Card className="shadow-lg md:shadow-xl hover:shadow-2xl transition-shadow duration-300">
            <CardHeader className="p-4 md:p-6 pb-0 md:pb-0">
              <StepIndicator steps={steps} currentStep={currentStep} />
            </CardHeader>

            <CardContent className="p-4 md:p-6 lg:p-8">
              {/* Error Alert */}
              {error && (
                <motion.div
                  variants={animations.slideDown}
                  initial="hidden"
                  animate="visible"
                >
                  <Alert variant="destructive" className="mb-4 md:mb-6 bg-red-50 border-red-200">
                    <AlertCircle className="h-4 w-4 text-red-600 flex-shrink-0" />
                    <AlertDescription className="text-red-800 text-sm md:text-base">{error}</AlertDescription>
                  </Alert>
                </motion.div>
              )}

              {/* Step Content with responsive min-height */}
              <div className={cn(
                "transition-all duration-300",
                isMobile ? "min-h-[300px]" : "min-h-[400px]",
                isTouchDevice && "select-none" // Prevent text selection on touch devices
              )}>
                {currentStep === 'input' && <InputStep />}
                {currentStep === 'parsing' && <ParsingStep />}
                {currentStep === 'preview' && <PreviewStep />}
                {currentStep === 'creating' && <CreatingStep />}
              </div>
            </CardContent>
          </Card>
        </motion.div>

        {/* Help Text */}
        <motion.div 
          variants={animations.fadeIn}
          initial="hidden"
          animate="visible"
          transition={{ delay: 0.4 }}
          className="mt-6 md:mt-8 text-center text-xs md:text-sm text-gray-500 px-4"
        >
          <p>
            {isMobile ? (
              <>
                Need help?{' '}
                <a 
                  href="/help/import" 
                  className="text-orange-600 hover:text-orange-700 active:text-orange-800 font-medium"
                >
                  View guide
                </a>
              </>
            ) : (
              <>
                Having trouble? Check out our{' '}
                <a 
                  href="/help/import" 
                  className="text-orange-600 hover:text-orange-700 hover:underline font-medium focus:outline-none focus:ring-2 focus:ring-orange-500 focus:ring-offset-2 rounded"
                >
                  import guide
                </a>{' '}
                or try one of our{' '}
                <button 
                  className="text-orange-600 hover:text-orange-700 hover:underline font-medium focus:outline-none focus:ring-2 focus:ring-orange-500 focus:ring-offset-2 rounded"
                  onClick={() => {
                    // Scroll to sample conversations
                    const samples = document.querySelector('[aria-labelledby="sample-conversations-label"]');
                    samples?.scrollIntoView({ behavior: 'smooth', block: 'center' });
                  }}
                >
                  sample conversations
                </button>
              </>
            )}
          </p>
          
          {/* Mobile swipe hint */}
          {isMobile && isTouchDevice && currentStep !== 'input' && (
            <p className="mt-2 text-xs text-gray-400">
              Swipe right to go back
            </p>
          )}
        </motion.div>
      </div>
    </ResponsiveContainer>
  );
}