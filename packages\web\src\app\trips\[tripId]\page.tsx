import { notFound } from 'next/navigation';
import { getServerSupabase } from '@/lib/supabase/server';
import { TripView } from '@/components/trips';
import { Activity, Trip } from '@travelviz/shared';

interface TripPageProps {
  params: {
    tripId: string;
  };
}

async function getTrip(tripId: string): Promise<Trip | null> {
  const supabase = await getServerSupabase();
  
  const { data, error } = await supabase
    .from('trips')
    .select('*')
    .eq('id', tripId)
    .single();

  if (error || !data) {
    return null;
  }

  return data as Trip;
}

async function getTripActivities(tripId: string): Promise<Activity[]> {
  const supabase = await getServerSupabase();
  
  const { data, error } = await supabase
    .from('activities')
    .select('*')
    .eq('trip_id', tripId)
    .order('start_time', { ascending: true });

  if (error || !data) {
    return [];
  }

  return data as Activity[];
}

export default async function TripPage({ params }: TripPageProps) {
  const trip = await getTrip(params.tripId);
  
  if (!trip) {
    notFound();
  }

  const activities = await getTripActivities(params.tripId);

  return (
    <div className="h-screen flex flex-col">
      <TripView
        trip={trip}
        activities={activities}
        className="flex-1"
      />
    </div>
  );
}

export async function generateMetadata({ params }: TripPageProps) {
  const trip = await getTrip(params.tripId);
  
  if (!trip) {
    return {
      title: 'Trip Not Found',
    };
  }

  return {
    title: `${trip.title} - TravelViz`,
    description: trip.description || `View and manage your trip to ${trip.destination}`,
  };
}