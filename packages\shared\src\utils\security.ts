/**
 * Security utilities for error handling and data protection
 */

import type { JsonValue } from '../types/utility-types';

export interface ErrorLogDetails {
  error: unknown;
  context?: Record<string, unknown>;
  timestamp: Date;
  userId?: string;
  endpoint?: string;
}

export class SecurityUtils {
  private static errorLogHandlers: ((details: ErrorLogDetails) => void)[] = [];

  /**
   * Register a custom error log handler
   */
  static addErrorLogHandler(handler: (details: ErrorLogDetails) => void): void {
    this.errorLogHandlers.push(handler);
  }

  /**
   * Sanitize error messages for external consumption
   * Returns generic messages while logging detailed errors internally
   */
  static sanitizeError(error: unknown, context?: Record<string, unknown>): string {
    const errorDetails: ErrorLogDetails = {
      error,
      context,
      timestamp: new Date(),
      userId: context?.userId as string | undefined,
      endpoint: context?.endpoint as string | undefined
    };

    // Log detailed error internally
    this.logError(errorDetails);

    // Return generic message based on error type
    if (error instanceof Error) {
      // Check for specific error types that need special handling
      if (error.name === 'ValidationError' || error.message.includes('validation')) {
        return 'Invalid input provided. Please check your data and try again.';
      }
      
      if (error.name === 'AuthenticationError' || error.message.includes('auth')) {
        return 'Authentication failed. Please log in and try again.';
      }
      
      if (error.name === 'NotFoundError' || error.message.includes('not found')) {
        return 'The requested resource was not found.';
      }
      
      if (error.name === 'RateLimitError' || error.message.includes('rate limit')) {
        return 'Too many requests. Please try again later.';
      }
    }

    // Default generic error message
    return 'An error occurred. Please try again later.';
  }

  /**
   * Log error details internally
   */
  private static logError(details: ErrorLogDetails): void {
    // Use registered handlers
    this.errorLogHandlers.forEach(handler => {
      try {
        handler(details);
      } catch (handlerError) {
        // Prevent logging errors from breaking the app
        console.error('Error in error log handler:', handlerError);
      }
    });

    // Default console logging in development
    if (process.env.NODE_ENV === 'development') {
      console.error('Detailed error:', {
        ...details,
        stack: details.error instanceof Error ? details.error.stack : undefined,
        message: details.error instanceof Error ? details.error.message : String(details.error)
      });
    }
  }

  /**
   * Sanitize sensitive data from objects before logging
   */
  static sanitizeObjectForLogging<T extends Record<string, unknown>>(
    obj: T,
    sensitiveKeys: string[] = ['password', 'token', 'key', 'secret', 'authorization', 'cookie', 'session']
  ): T {
    const sanitized = { ...obj };

    const sanitizeRecursive = (item: JsonValue | undefined, path: string = ''): JsonValue | undefined => {
      if (item === null || item === undefined) {
        return item;
      }

      if (Array.isArray(item)) {
        return item.map((val, index) => sanitizeRecursive(val, `${path}[${index}]`)).filter((v): v is JsonValue => v !== undefined);
      }

      if (typeof item === 'object') {
        const result: Record<string, JsonValue> = {};
        
        for (const [key, value] of Object.entries(item)) {
          const currentPath = path ? `${path}.${key}` : key;
          const lowerKey = key.toLowerCase();
          
          // Check if key contains sensitive keywords
          const isSensitive = sensitiveKeys.some(sensitive => 
            lowerKey.includes(sensitive.toLowerCase())
          );
          
          if (isSensitive && typeof value === 'string') {
            result[key] = '[REDACTED]';
          } else {
            const sanitizedValue = sanitizeRecursive(value, currentPath);
            if (sanitizedValue !== undefined) {
              result[key] = sanitizedValue;
            }
          }
        }
        
        return result;
      }

      return item;
    };

    const result = sanitizeRecursive(sanitized as unknown as JsonValue);
    return (result ?? sanitized) as T;
  }

  /**
   * Create a safe error response object
   */
  static createSafeErrorResponse(
    error: unknown,
    requestId?: string
  ): { 
    error: string; 
    requestId?: string; 
    timestamp: string;
  } {
    return {
      error: this.sanitizeError(error),
      ...(requestId && { requestId }),
      timestamp: new Date().toISOString()
    };
  }

  /**
   * Validate and sanitize environment variables
   */
  static getSecureEnvVar(
    key: string,
    options?: {
      required?: boolean;
      defaultValue?: string;
      validator?: (value: string) => boolean;
    }
  ): string | undefined {
    const value = process.env[key];

    if (!value) {
      if (options?.required) {
        throw new Error(`Required environment variable ${key} is not set`);
      }
      return options?.defaultValue;
    }

    if (options?.validator && !options.validator(value)) {
      throw new Error(`Environment variable ${key} has invalid value`);
    }

    return value;
  }
}