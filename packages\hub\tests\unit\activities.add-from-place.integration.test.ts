import request from 'supertest';
import { vi, describe, it, expect, beforeEach } from 'vitest';
import express from 'express';
import { createSuccessResponse, createErrorResponse } from '@travelviz/shared';
import { ActivitiesController } from '../controllers/activities.controller';
import { PlacesService } from '../services/places.service';
import { TripsService } from '../services/trips.service';

// Mock the services
vi.mock('../services/places.service');
vi.mock('../services/trips.service');

describe('Activities Controller - Add from Place Integration', () => {
  let app: express.Application;
  let controller: ActivitiesController;
  let mockPlacesService: any;
  let mockTripsService: any;

  beforeEach(() => {
    vi.clearAllMocks();

    // Create mock services
    mockPlacesService = {
      getPlaceDetails: vi.fn(),
      mapPlaceToActivity: vi.fn()
    };
    
    mockTripsService = {
      addActivityToTrip: vi.fn()
    };

    // Mock the service constructors
    (PlacesService as any).mockImplementation(() => mockPlacesService);
    (TripsService as any).mockImplementation(() => mockTripsService);

    // Create controller instance (this will use mocked services)
    controller = new ActivitiesController();

    // Set up Express app
    app = express();
    app.use(express.json());
    
    // Mock authentication middleware
    app.use((req: any, res, next) => {
      req.user = { id: 'user-123' };
      next();
    });

    // Set up route
    app.post('/api/activities/add-from-place', controller.addFromPlace.bind(controller));
  });

  describe('POST /api/activities/add-from-place', () => {
    it('should create activity from place details', async () => {
      // Arrange
      const requestBody = {
        place_id: 'ChIJLU7jZClu5kcR4PcOOO6p3I0',
        tripId: 'trip-123',
      };

      const mockPlaceDetails = {
        place_id: 'ChIJLU7jZClu5kcR4PcOOO6p3I0',
        name: 'Eiffel Tower',
        formatted_address: 'Paris, France',
        location_lat: 48.8584,
        location_lng: 2.2945,
        types: ['tourist_attraction'],
        rating: 4.6,
        user_ratings_total: 158925,
        website: 'https://www.toureiffel.paris/',
        international_phone_number: '+33 8 92 70 12 39',
      };

      const mockActivityData = {
        title: 'Eiffel Tower',
        location: 'Champ de Mars, 5 Avenue Anatole France, 75007 Paris, France',
        locationLat: 48.8583736,
        locationLng: 2.2944813,
        type: 'activity' as const,
        description: 'Tourist attraction with a rating of 4.6 stars (158925 reviews)',
        bookingUrl: 'https://www.toureiffel.paris/',
        notes: 'Phone: +33 8 92 70 12 39',
      };

      const mockCreatedActivity = {
        id: 'activity-456',
        trip_id: 'trip-123',
        user_id: 'user-123',
        position: 0,
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-01T00:00:00Z',
        ...mockActivityData,
      };

      mockPlacesService.getPlaceDetails.mockResolvedValue(mockPlaceDetails);
      mockPlacesService.mapPlaceToActivity.mockReturnValue(mockActivityData);
      mockTripsService.addActivityToTrip.mockResolvedValue(mockCreatedActivity);

      // Act
      const response = await request(app)
        .post('/api/activities/add-from-place')
        .send(requestBody);

      // Assert
      expect(response.status).toBe(201);
      expect(response.body).toEqual(createSuccessResponse(mockCreatedActivity));

      expect(mockPlacesService.getPlaceDetails).toHaveBeenCalledWith('ChIJLU7jZClu5kcR4PcOOO6p3I0');
      expect(mockPlacesService.mapPlaceToActivity).toHaveBeenCalledWith(mockPlaceDetails);
      expect(mockTripsService.addActivityToTrip).toHaveBeenCalledWith(
        'trip-123',
        'user-123',
        mockActivityData
      );
    });

    it('should return 400 when place service fails', async () => {
      // Arrange
      const requestBody = {
        place_id: 'invalid-place-id',
        tripId: 'trip-123',
      };

      mockPlacesService.getPlaceDetails.mockRejectedValue(
        new Error('Failed to get place details: Invalid place ID')
      );

      // Act
      const response = await request(app)
        .post('/api/activities/add-from-place')
        .send(requestBody);

      // Assert
      expect(response.status).toBe(400);
      expect(response.body).toEqual(createErrorResponse('Invalid place: Invalid place ID'));
    });

    it('should return 500 when activity creation fails', async () => {
      // Arrange
      const requestBody = {
        place_id: 'ChIJLU7jZClu5kcR4PcOOO6p3I0',
        tripId: 'trip-123',
      };

      const mockPlaceDetails = {
        place_id: 'ChIJLU7jZClu5kcR4PcOOO6p3I0',
        name: 'Eiffel Tower',
      };

      const mockActivityData = {
        title: 'Eiffel Tower',
      };

      mockPlacesService.getPlaceDetails.mockResolvedValue(mockPlaceDetails);
      mockPlacesService.mapPlaceToActivity.mockReturnValue(mockActivityData);
      mockTripsService.addActivityToTrip.mockRejectedValue(new Error('Database error'));

      // Act
      const response = await request(app)
        .post('/api/activities/add-from-place')
        .send(requestBody);

      // Assert
      expect(response.status).toBe(500);
      expect(response.body).toEqual(createErrorResponse('Failed to create activity from place'));
    });

    it('should return 400 for missing place_id', async () => {
      // Act
      const response = await request(app)
        .post('/api/activities/add-from-place')
        .send({ tripId: 'trip-123' });

      // Assert
      expect(response.status).toBe(400);
      expect(response.body).toEqual(createErrorResponse('Place ID is required'));
    });

    it('should return 400 for missing tripId', async () => {
      // Act
      const response = await request(app)
        .post('/api/activities/add-from-place')
        .send({ place_id: 'ChIJLU7jZClu5kcR4PcOOO6p3I0' });

      // Assert
      expect(response.status).toBe(400);
      expect(response.body).toEqual(createErrorResponse('Trip ID is required'));
    });

    it('should return 401 when user is not authenticated', async () => {
      // Override authentication for this test
      app = express();
      app.use(express.json());
      app.post('/api/activities/add-from-place', controller.addFromPlace.bind(controller));

      // Act
      const response = await request(app)
        .post('/api/activities/add-from-place')
        .send({
          place_id: 'ChIJLU7jZClu5kcR4PcOOO6p3I0',
          tripId: 'trip-123',
        });

      // Assert
      expect(response.status).toBe(401);
      expect(response.body).toEqual(createErrorResponse('Authentication required'));
    });
  });
});