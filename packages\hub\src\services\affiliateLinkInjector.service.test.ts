import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { affiliateLinkInjector } from './affiliateLinkInjector.service';
import { travelpayoutsService } from './travelpayouts.service';
import { logger } from '../utils/logger';
import { Activity } from '../lib/supabase';

// Mock dependencies
vi.mock('../travelpayouts.service', () => ({
  travelpayoutsService: {
    generateAffiliateUrl: vi.fn(),
  },
}));

vi.mock('../../utils/logger', () => ({
  logger: {
    info: vi.fn(),
    error: vi.fn(),
  },
}));

describe('AffiliateLinkInjectorService', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  afterEach(() => {
    // Reset config to defaults
    affiliateLinkInjector.updateConfig({
      minCommission: 10,
      commissionRates: {
        flight: 0.02,
        hotel: 0.04,
        car: 0.05,
        activity: 0.08,
      },
    });
  });

  describe('injectAffiliateLinks', () => {
    it('should inject affiliate links for eligible activities', async () => {
      const activities: Activity[] = [
        {
          id: '1',
          trip_id: 'trip1',
          name: 'Flight to Paris',
          type: 'flight',
          booking_url: 'https://aviasales.com/flight123',
          price: 600,
          position: 0,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        },
        {
          id: '2',
          trip_id: 'trip1',
          name: 'Hotel Stay',
          type: 'accommodation',
          booking_url: 'https://booking.com/hotel456',
          price: 300,
          position: 1,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        },
      ];

      vi.mocked(travelpayoutsService.generateAffiliateUrl)
        .mockReturnValueOnce('https://tp.media/affiliate-flight')
        .mockReturnValueOnce('https://tp.media/affiliate-hotel');

      const result = await affiliateLinkInjector.injectAffiliateLinks(activities);

      expect(result).toHaveLength(2);
      expect(result[0].affiliate_url).toBe('https://tp.media/affiliate-flight');
      expect(result[0].affiliate_type).toBe('flight');
      expect(result[1].affiliate_url).toBe('https://tp.media/affiliate-hotel');
      expect(result[1].affiliate_type).toBe('hotel');
    });

    it('should skip activities without booking URLs', async () => {
      const activities: Activity[] = [
        {
          id: '1',
          trip_id: 'trip1',
          name: 'Sightseeing',
          type: 'activity',
          price: 50,
          position: 0,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        },
      ];

      const result = await affiliateLinkInjector.injectAffiliateLinks(activities);

      expect(result[0].affiliate_url).toBeUndefined();
      expect(travelpayoutsService.generateAffiliateUrl).not.toHaveBeenCalled();
    });

    it('should skip activities that already have affiliate URLs', async () => {
      const activities: Activity[] = [
        {
          id: '1',
          trip_id: 'trip1',
          name: 'Flight',
          type: 'flight',
          booking_url: 'https://example.com',
          affiliate_url: 'https://existing-affiliate.com',
          price: 1000,
          position: 0,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        },
      ];

      const result = await affiliateLinkInjector.injectAffiliateLinks(activities);

      expect(result[0].affiliate_url).toBe('https://existing-affiliate.com');
      expect(travelpayoutsService.generateAffiliateUrl).not.toHaveBeenCalled();
    });

    it('should skip activities that do not meet minimum commission requirement', async () => {
      const activities: Activity[] = [
        {
          id: '1',
          trip_id: 'trip1',
          name: 'Cheap Flight',
          type: 'flight',
          booking_url: 'https://aviasales.com/cheap',
          price: 100, // $100 * 0.02 = $2 commission (below $10 minimum)
          position: 0,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        },
      ];

      const result = await affiliateLinkInjector.injectAffiliateLinks(activities);

      expect(result[0].affiliate_url).toBeUndefined();
      expect(logger.info).toHaveBeenCalledWith(expect.stringContaining("doesn't meet $10 minimum commission"));
    });

    it('should handle errors gracefully and continue processing', async () => {
      const activities: Activity[] = [
        {
          id: '1',
          trip_id: 'trip1',
          name: 'Flight',
          type: 'flight',
          booking_url: 'https://aviasales.com/flight',
          price: 1000,
          position: 0,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        },
        {
          id: '2',
          trip_id: 'trip1',
          name: 'Hotel',
          type: 'accommodation',
          booking_url: 'https://booking.com/hotel',
          price: 500,
          position: 1,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        },
      ];

      vi.mocked(travelpayoutsService.generateAffiliateUrl)
        .mockImplementationOnce(() => { throw new Error('API Error'); })
        .mockReturnValueOnce('https://affiliate-hotel');

      const result = await affiliateLinkInjector.injectAffiliateLinks(activities);

      expect(result[0].affiliate_url).toBeUndefined();
      expect(result[1].affiliate_url).toBe('https://affiliate-hotel');
      expect(logger.error).toHaveBeenCalledWith(
        expect.stringContaining('Failed to inject affiliate link'),
        expect.any(Object)
      );
    });
  });

  describe('detectBookableActivities', () => {
    it('should detect activities with booking URLs', () => {
      const activities: Activity[] = [
        {
          id: '1',
          trip_id: 'trip1',
          name: 'Flight',
          type: 'flight',
          booking_url: 'https://example.com',
          position: 0,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        },
        {
          id: '2',
          trip_id: 'trip1',
          name: 'Restaurant',
          type: 'dining',
          position: 1,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        },
      ];

      const result = affiliateLinkInjector.detectBookableActivities(activities);

      expect(result).toHaveLength(1);
      expect(result[0].id).toBe('1');
    });

    it('should detect activities with price and bookable type', () => {
      const activities: Activity[] = [
        {
          id: '1',
          trip_id: 'trip1',
          name: 'Hotel',
          type: 'accommodation',
          price: 200,
          position: 0,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        },
        {
          id: '2',
          trip_id: 'trip1',
          name: 'Shopping',
          type: 'shopping',
          price: 50,
          position: 1,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        },
      ];

      const result = affiliateLinkInjector.detectBookableActivities(activities);

      expect(result).toHaveLength(1);
      expect(result[0].id).toBe('1');
    });

    it('should detect activities with booking keywords in description', () => {
      const activities: Activity[] = [
        {
          id: '1',
          trip_id: 'trip1',
          name: 'Museum Visit',
          type: 'other',
          price: 25,
          description: 'Need to book tickets in advance',
          position: 0,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        },
        {
          id: '2',
          trip_id: 'trip1',
          name: 'Walk in Park',
          type: 'other',
          price: 0,
          description: 'Free public park',
          position: 1,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        },
      ];

      const result = affiliateLinkInjector.detectBookableActivities(activities);

      expect(result).toHaveLength(1);
      expect(result[0].id).toBe('1');
    });
  });

  describe('extractBookingUrls', () => {
    it('should extract booking URLs from description', () => {
      const activity: Activity = {
        id: '1',
        trip_id: 'trip1',
        name: 'Hotel',
        type: 'accommodation',
        description: 'Great hotel at https://booking.com/hotel123 or check https://hotels.com/hotel456',
        position: 0,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      };

      const urls = affiliateLinkInjector.extractBookingUrls(activity);

      expect(urls).toHaveLength(2);
      expect(urls).toContain('https://booking.com/hotel123');
      expect(urls).toContain('https://hotels.com/hotel456');
    });

    it('should only extract URLs from known booking domains', () => {
      const activity: Activity = {
        id: '1',
        trip_id: 'trip1',
        name: 'Activity',
        type: 'activity',
        description: 'Book at https://viator.com/tour123 or visit https://example.com/info',
        position: 0,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      };

      const urls = affiliateLinkInjector.extractBookingUrls(activity);

      expect(urls).toHaveLength(1);
      expect(urls[0]).toBe('https://viator.com/tour123');
    });

    it('should return empty array if no description', () => {
      const activity: Activity = {
        id: '1',
        trip_id: 'trip1',
        name: 'Activity',
        type: 'activity',
        position: 0,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      };

      const urls = affiliateLinkInjector.extractBookingUrls(activity);

      expect(urls).toHaveLength(0);
    });
  });

  describe('processTrip', () => {
    it('should process all bookable activities in a trip', async () => {
      const activities: Activity[] = [
        {
          id: '1',
          trip_id: 'trip1',
          name: 'Flight',
          type: 'flight',
          booking_url: 'https://aviasales.com/flight',
          price: 800,
          position: 0,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        },
        {
          id: '2',
          trip_id: 'trip1',
          name: 'Hotel',
          type: 'accommodation',
          booking_url: 'https://booking.com/hotel',
          price: 400,
          position: 1,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        },
        {
          id: '3',
          trip_id: 'trip1',
          name: 'Lunch',
          type: 'dining',
          price: 30,
          position: 2,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        },
      ];

      vi.mocked(travelpayoutsService.generateAffiliateUrl)
        .mockReturnValueOnce('https://affiliate-flight')
        .mockReturnValueOnce('https://affiliate-hotel');

      const result = await affiliateLinkInjector.processTrip('trip1', activities);

      expect(result.processed).toBe(2); // Only flight and hotel are bookable
      expect(result.injected).toBe(2);
      expect(result.totalPotentialRevenue).toBe(32); // (800 * 0.02) + (400 * 0.04)
      expect(logger.info).toHaveBeenCalledWith(expect.stringContaining('Processed trip trip1'));
    });

    it('should handle trips with no bookable activities', async () => {
      const activities: Activity[] = [
        {
          id: '1',
          trip_id: 'trip1',
          name: 'Walking Tour',
          type: 'other',
          position: 0,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        },
      ];

      const result = await affiliateLinkInjector.processTrip('trip1', activities);

      expect(result.processed).toBe(0);
      expect(result.injected).toBe(0);
      expect(result.totalPotentialRevenue).toBe(0);
    });
  });

  describe('updateConfig', () => {
    it('should update configuration', () => {
      affiliateLinkInjector.updateConfig({
        minCommission: 15,
        commissionRates: {
          flight: 0.03,
          hotel: 0.05,
          car: 0.06,
          activity: 0.10,
        },
      });

      // Test with new config by checking if activity meets new minimum
      const activities: Activity[] = [
        {
          id: '1',
          trip_id: 'trip1',
          name: 'Flight',
          type: 'flight',
          booking_url: 'https://example.com',
          price: 400, // 400 * 0.03 = $12 (below new $15 minimum)
          position: 0,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        },
      ];

      affiliateLinkInjector.injectAffiliateLinks(activities).then(result => {
        expect(result[0].affiliate_url).toBeUndefined();
      });
    });

    it('should support partial config updates', () => {
      affiliateLinkInjector.updateConfig({
        minCommission: 20,
      });

      // Verify partial update by checking commission calculation
      const activities: Activity[] = [
        {
          id: '1',
          trip_id: 'trip1',
          name: 'Hotel',
          type: 'accommodation',
          booking_url: 'https://example.com',
          price: 450, // 450 * 0.04 = $18 (below new $20 minimum)
          position: 0,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        },
      ];

      affiliateLinkInjector.injectAffiliateLinks(activities).then(result => {
        expect(result[0].affiliate_url).toBeUndefined();
      });
    });
  });
});