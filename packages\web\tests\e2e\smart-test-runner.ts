import { test, expect, Page } from '@playwright/test';
import { readFileSync, writeFileSync, existsSync } from 'fs';
import { join } from 'path';

interface TestMetadata {
  name: string;
  file: string;
  lastRun: number;
  duration: number;
  failures: number;
  priority: 'critical' | 'high' | 'medium' | 'low';
  tags: string[];
  dependencies: string[];
}

interface TestRun {
  timestamp: number;
  success: boolean;
  duration: number;
  error?: string;
}

/**
 * Smart test selection and execution
 * Optimizes E2E tests by running critical paths first and skipping stable tests
 */
export class SmartTestRunner {
  private metadataFile = join(process.cwd(), 'tests/e2e/test-metadata.json');
  private testHistory: Map<string, TestMetadata> = new Map();
  private currentRun: number = Date.now();

  constructor() {
    this.loadTestMetadata();
  }

  private loadTestMetadata() {
    if (existsSync(this.metadataFile)) {
      try {
        const data = JSON.parse(readFileSync(this.metadataFile, 'utf8'));
        Object.entries(data).forEach(([key, value]) => {
          this.testHistory.set(key, value as TestMetadata);
        });
      } catch (error) {
        console.warn('Failed to load test metadata:', error);
      }
    }
  }

  private saveTestMetadata() {
    const data = Object.fromEntries(this.testHistory);
    writeFileSync(this.metadataFile, JSON.stringify(data, null, 2));
  }

  /**
   * Determine if a test should run based on smart selection criteria
   */
  shouldRunTest(testName: string, file: string): boolean {
    const metadata = this.testHistory.get(testName);
    
    if (!metadata) {
      // New test - always run
      return true;
    }

    // Always run critical tests
    if (metadata.priority === 'critical') {
      return true;
    }

    // Run tests that have been failing
    if (metadata.failures > 0) {
      return true;
    }

    // Run tests that haven't been run recently
    const daysSinceLastRun = (this.currentRun - metadata.lastRun) / (1000 * 60 * 60 * 24);
    if (daysSinceLastRun > this.getRunInterval(metadata.priority)) {
      return true;
    }

    // Run tests affected by changes (simplified - could integrate with git diff)
    if (this.isAffectedByChanges(metadata)) {
      return true;
    }

    return false;
  }

  private getRunInterval(priority: TestMetadata['priority']): number {
    switch (priority) {
      case 'critical': return 0; // Always run
      case 'high': return 1; // Daily
      case 'medium': return 3; // Every 3 days
      case 'low': return 7; // Weekly
      default: return 1;
    }
  }

  private isAffectedByChanges(metadata: TestMetadata): boolean {
    // Simplified implementation - in real scenario, would check git diff
    // against test dependencies
    return metadata.dependencies.some(dep => {
      // Check if dependency files have been modified recently
      // This is a placeholder - real implementation would use git or file timestamps
      return false;
    });
  }

  /**
   * Record test execution results
   */
  recordTestResult(
    testName: string, 
    file: string, 
    success: boolean, 
    duration: number, 
    error?: string
  ) {
    const existing = this.testHistory.get(testName) || {
      name: testName,
      file,
      lastRun: 0,
      duration: 0,
      failures: 0,
      priority: 'medium' as const,
      tags: [],
      dependencies: [],
    };

    existing.lastRun = this.currentRun;
    existing.duration = duration;
    existing.failures = success ? 0 : existing.failures + 1;

    this.testHistory.set(testName, existing);
    this.saveTestMetadata();
  }

  /**
   * Get prioritized test order
   */
  getPrioritizedTests(allTests: Array<{ name: string; file: string }>): Array<{ name: string; file: string; priority: string }> {
    return allTests
      .map(test => ({
        ...test,
        metadata: this.testHistory.get(test.name),
      }))
      .filter(test => this.shouldRunTest(test.name, test.file))
      .sort((a, b) => {
        // Sort by priority (critical first)
        const priorityOrder = { critical: 0, high: 1, medium: 2, low: 3 };
        const aPriority = a.metadata?.priority || 'medium';
        const bPriority = b.metadata?.priority || 'medium';
        
        if (priorityOrder[aPriority] !== priorityOrder[bPriority]) {
          return priorityOrder[aPriority] - priorityOrder[bPriority];
        }

        // Then by failure count (failing tests first)
        const aFailures = a.metadata?.failures || 0;
        const bFailures = b.metadata?.failures || 0;
        
        if (aFailures !== bFailures) {
          return bFailures - aFailures;
        }

        // Finally by last run time (oldest first)
        const aLastRun = a.metadata?.lastRun || 0;
        const bLastRun = b.metadata?.lastRun || 0;
        
        return aLastRun - bLastRun;
      })
      .map(test => ({
        name: test.name,
        file: test.file,
        priority: test.metadata?.priority || 'medium',
      }));
  }

  /**
   * Set test metadata
   */
  setTestMetadata(
    testName: string, 
    metadata: Partial<TestMetadata>
  ) {
    const existing = this.testHistory.get(testName) || {
      name: testName,
      file: '',
      lastRun: 0,
      duration: 0,
      failures: 0,
      priority: 'medium' as const,
      tags: [],
      dependencies: [],
    };

    this.testHistory.set(testName, { ...existing, ...metadata });
    this.saveTestMetadata();
  }
}

// Global instance
export const smartRunner = new SmartTestRunner();

/**
 * Enhanced test function with smart execution tracking
 */
export function smartTest(
  name: string,
  testFn: (page: Page) => Promise<void>,
  metadata?: Partial<TestMetadata>
) {
  // Set metadata if provided
  if (metadata) {
    smartRunner.setTestMetadata(name, metadata);
  }

  // Only run if smart selection decides to
  const shouldRun = smartRunner.shouldRunTest(name, (expect.getState() as any).currentTestName || '');
  
  if (!shouldRun) {
    test.skip(name, async () => {
      // Skipped by smart test selection
    });
    return;
  }

  test(name, async ({ page }) => {
    const startTime = Date.now();
    let success = false;
    let error: string | undefined;

    try {
      await testFn(page);
      success = true;
    } catch (e) {
      error = e instanceof Error ? e.message : String(e);
      throw e;
    } finally {
      const duration = Date.now() - startTime;
      smartRunner.recordTestResult(name, '', success, duration, error);
    }
  });
}

/**
 * Critical path test - always runs
 */
export function criticalTest(
  name: string,
  testFn: (page: Page) => Promise<void>,
  metadata?: Partial<TestMetadata>
) {
  smartTest(name, testFn, { 
    ...metadata, 
    priority: 'critical',
    tags: [...(metadata?.tags || []), 'critical-path']
  });
}

/**
 * Smoke test - high priority, runs daily
 */
export function smokeTest(
  name: string,
  testFn: (page: Page) => Promise<void>,
  metadata?: Partial<TestMetadata>
) {
  smartTest(name, testFn, { 
    ...metadata, 
    priority: 'high',
    tags: [...(metadata?.tags || []), 'smoke']
  });
}

/**
 * Performance test - medium priority
 */
export function performanceTest(
  name: string,
  testFn: (page: Page) => Promise<void>,
  metadata?: Partial<TestMetadata>
) {
  smartTest(name, testFn, { 
    ...metadata, 
    priority: 'medium',
    tags: [...(metadata?.tags || []), 'performance']
  });
}

/**
 * Visual test - low priority, runs weekly
 */
export function visualTest(
  name: string,
  testFn: (page: Page) => Promise<void>,
  metadata?: Partial<TestMetadata>
) {
  smartTest(name, testFn, { 
    ...metadata, 
    priority: 'low',
    tags: [...(metadata?.tags || []), 'visual']
  });
}