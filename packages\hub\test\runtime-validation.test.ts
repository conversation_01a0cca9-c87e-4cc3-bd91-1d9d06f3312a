import { describe, it, expect } from 'vitest';
import { execSync } from 'child_process';
import { resolve } from 'path';
import { existsSync } from 'fs';

/**
 * Runtime validation tests that ensure our imports work in production
 * These tests validate module resolution matches runtime behavior
 */
describe('Runtime Module Resolution', () => {
  const hubDir = resolve(__dirname, '..');
  const rootDir = resolve(__dirname, '../../..');
  
  it('should successfully compile TypeScript without path aliases', () => {
    // This test ensures that TypeScript can compile without vitest aliases
    try {
      execSync('npx tsc --noEmit', {
        cwd: hubDir,
        encoding: 'utf8',
        stdio: 'pipe'
      });
      // If we get here, compilation succeeded
      expect(true).toBe(true);
    } catch (error: any) {
      // Check if it's a module resolution error
      if (error.stdout && error.stdout.includes('Cannot find module')) {
        throw new Error(`TypeScript compilation failed with module resolution error:\n${error.stdout}`);
      }
      // For other errors, we might want to ignore them in this test
      // as we're specifically testing module resolution
      expect(true).toBe(true);
    }
  });

  it('should validate all @travelviz/shared imports use package exports', () => {
    // Find all files that import from @travelviz/shared
    const files = execSync(
      'grep -r "@travelviz/shared" src --include="*.ts" --include="*.tsx" || true',
      { cwd: hubDir, encoding: 'utf8' }
    );
    
    const invalidImports: string[] = [];
    
    files.split('\n').forEach(line => {
      if (line && line.includes('@travelviz/shared/')) {
        // Check if it's trying to import a subpath
        const match = line.match(/@travelviz\/shared\/[^'"]+/);
        if (match && !match[0].includes('@travelviz/shared\';') && !match[0].includes('@travelviz/shared";')) {
          invalidImports.push(line.trim());
        }
      }
    });
    
    if (invalidImports.length > 0) {
      throw new Error(
        `Found invalid subpath imports from @travelviz/shared:\n${invalidImports.join('\n')}\n\n` +
        'All imports must be from the package root: import { ... } from "@travelviz/shared"'
      );
    }
  });

  it('should validate shared package is built before tests', () => {
    const sharedDistPath = resolve(__dirname, '../../../shared/dist');
    const indexPath = resolve(sharedDistPath, 'index.js');
    
    expect(existsSync(sharedDistPath), 'Shared package dist folder missing').toBe(true);
    expect(existsSync(indexPath), 'Shared package index.js missing').toBe(true);
  });
});

describe('Service Startup Validation', () => {
  it('should successfully load all service modules', async () => {
    // This test attempts to load services to catch import errors
    const services = [
      '../src/services/trips/trip-activity.service',
      '../src/services/trips/trip-crud.service',
      '../src/services/trips.service',
      '../src/services/ai-parser.service',
      '../src/services/parser.service'
    ];
    
    for (const service of services) {
      try {
        await import(service);
      } catch (error: any) {
        if (error.code === 'MODULE_NOT_FOUND') {
          throw new Error(
            `Failed to load ${service}: ${error.message}\n` +
            'This indicates a module resolution issue that would fail at runtime.'
          );
        }
        // Re-throw other errors
        throw error;
      }
    }
  });
});