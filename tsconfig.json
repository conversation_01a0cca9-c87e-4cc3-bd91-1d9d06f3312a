{"compilerOptions": {"target": "ES2020", "lib": ["DOM", "DOM.Iterable", "ES6"], "allowJs": true, "skipLibCheck": true, "strict": true, "forceConsistentCasingInFileNames": true, "noEmit": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true, "baseUrl": ".", "paths": {"@travelviz/shared": ["./packages/shared/src"], "@travelviz/shared/*": ["./packages/shared/src/*"], "@travelviz/web": ["./packages/web/src"], "@travelviz/web/*": ["./packages/web/src/*"], "@travelviz/hub": ["./packages/hub/src"], "@travelviz/hub/*": ["./packages/hub/src/*"]}}, "include": ["packages/*/src/**/*", "packages/*/app/**/*", "packages/*/*.ts", "packages/*/*.tsx"], "exclude": ["node_modules", "packages/*/node_modules", "packages/*/dist", "packages/*/.next"], "references": [{"path": "./packages/shared"}, {"path": "./packages/hub"}, {"path": "./packages/web"}]}