import { logger } from '../utils/logger';
import { getSupabaseClient } from '../lib/supabase';
import { redisConnectionPool } from './redis-connection-pool.service';

/**
 * Usage Tracking Service
 * Tracks daily API usage across all AI models with Pacific Time reset
 * Requirements: 1.1, 1.2, 1.4 from requirements.md
 */

export interface ModelUsage {
  modelId: string;
  requestCount: number;
  inputTokens: number;
  outputTokens: number;
  lastReset: Date;
  dailyLimits: {
    requests?: number;
    inputTokens?: number;
    outputTokens?: number;
  };
}

export interface UsageStatistics {
  [modelId: string]: ModelUsage;
}

export class UsageTrackingService {
  private readonly CACHE_PREFIX = 'ai_usage:';
  private readonly CACHE_TTL = 300; // 5 minutes
  private readonly NOTIFICATION_CHANNEL = 'ai_usage_reset';

  constructor() {
    // Listen for daily reset notifications from pg_cron
    this.setupResetNotificationListener();
  }

  /**
   * Track a request - Requirements 1.2: Record request count, token usage, and timestamp
   */
  async trackRequest(modelId: string, inputTokens: number, outputTokens: number): Promise<void> {
    try {
      const cost = await this.calculateCost(modelId, inputTokens, outputTokens);
      
      // Update database usage counters
      await this.updateUsageCounters(modelId, inputTokens, outputTokens, cost);
      
      // Clear cache to force refresh
      await this.clearUsageCache(modelId);
      
      // Check for threshold warnings (Requirements 1.3)
      await this.checkUsageThresholds(modelId);
      
      logger.debug('Request tracked successfully', {
        modelId,
        inputTokens,
        outputTokens,
        cost
      });
    } catch (error) {
      logger.error('Failed to track request', { modelId, inputTokens, outputTokens, error });
      // Don't throw - tracking failures shouldn't break the main flow
    }
  }

  /**
   * Get current usage for a model - Requirements 1.4: Return current usage statistics
   */
  async getCurrentUsage(modelId: string): Promise<ModelUsage> {
    try {
      // Try cache first
      const cached = await this.getCachedUsage(modelId);
      if (cached) {
        return cached;
      }

      // Get from database
      const usage = await this.getUsageFromDatabase(modelId);
      
      // Cache the result
      await this.cacheUsage(modelId, usage);
      
      return usage;
    } catch (error) {
      logger.error('Failed to get current usage', { modelId, error });
      // Return default usage on error
      return this.getDefaultUsage(modelId);
    }
  }

  /**
   * Get all usage statistics - Requirements 1.4: Return usage statistics for all models
   */
  async getAllUsage(): Promise<UsageStatistics> {
    try {
      const { data: configs, error } = await getSupabaseClient()
        .from('ai_model_configs')
        .select('id')
        .eq('is_active', true);

      if (error) {
        throw error;
      }

      const usageStats: UsageStatistics = {};
      
      // Get usage for each active model
      await Promise.all(
        configs.map(async (config) => {
          usageStats[config.id] = await this.getCurrentUsage(config.id);
        })
      );

      return usageStats;
    } catch (error) {
      logger.error('Failed to get all usage statistics', { error });
      return {};
    }
  }

  /**
   * Check if model is available (not rate limited)
   */
  async isModelAvailable(modelId: string): Promise<boolean> {
    try {
      const { data, error } = await getSupabaseClient()
        .rpc('is_model_available', { model_id_param: modelId });

      if (error) {
        logger.error('Failed to check model availability', { modelId, error });
        return false;
      }

      return data === true;
    } catch (error) {
      logger.error('Error checking model availability', { modelId, error });
      return false;
    }
  }

  /**
   * Reset daily counters (called at midnight PT) - Requirements 1.1
   */
  async resetDailyCounters(): Promise<void> {
    try {
      const { error } = await getSupabaseClient()
        .rpc('reset_daily_ai_usage');

      if (error) {
        throw error;
      }

      // Clear all usage caches
      await this.clearAllUsageCaches();
      
      logger.info('Daily usage counters reset successfully');
    } catch (error) {
      logger.error('Failed to reset daily counters', { error });
      throw error;
    }
  }

  /**
   * Update usage counters in database
   */
  private async updateUsageCounters(
    modelId: string, 
    inputTokens: number, 
    outputTokens: number, 
    cost: number
  ): Promise<void> {
    const { error } = await getSupabaseClient()
      .from('ai_model_usage')
      .upsert({
        model_id: modelId,
        date: new Date().toISOString().split('T')[0], // Current date
        request_count: 1,
        input_tokens: inputTokens,
        output_tokens: outputTokens,
        total_cost: cost,
        updated_at: new Date().toISOString()
      }, {
        onConflict: 'model_id,date',
        // Use SQL to increment existing values
        ignoreDuplicates: false
      });

    if (error) {
      // If upsert fails, try manual increment
      await this.incrementUsageCounters(modelId, inputTokens, outputTokens, cost);
    }
  }

  /**
   * Manually increment usage counters
   */
  private async incrementUsageCounters(
    modelId: string,
    inputTokens: number,
    outputTokens: number,
    cost: number
  ): Promise<void> {
    const today = new Date().toISOString().split('T')[0];
    
    // First, try to get existing record
    const { data: existing } = await getSupabaseClient()
      .from('ai_model_usage')
      .select('*')
      .eq('model_id', modelId)
      .eq('date', today)
      .single();

    if (existing) {
      // Update existing record
      const { error } = await getSupabaseClient()
        .from('ai_model_usage')
        .update({
          request_count: existing.request_count + 1,
          input_tokens: existing.input_tokens + inputTokens,
          output_tokens: existing.output_tokens + outputTokens,
          total_cost: existing.total_cost + cost,
          updated_at: new Date().toISOString()
        })
        .eq('id', existing.id);

      if (error) {
        throw error;
      }
    } else {
      // Create new record
      const { error } = await getSupabaseClient()
        .from('ai_model_usage')
        .insert({
          model_id: modelId,
          date: today,
          request_count: 1,
          input_tokens: inputTokens,
          output_tokens: outputTokens,
          total_cost: cost
        });

      if (error) {
        throw error;
      }
    }
  }

  /**
   * Get usage from database
   */
  private async getUsageFromDatabase(modelId: string): Promise<ModelUsage> {
    const { data, error } = await getSupabaseClient()
      .rpc('get_model_usage', { model_id_param: modelId });

    if (error) {
      throw error;
    }

    const usage = data[0];
    if (!usage) {
      return this.getDefaultUsage(modelId);
    }

    // Get model limits
    const { data: config } = await getSupabaseClient()
      .from('ai_model_configs')
      .select('daily_request_limit, tpm_limit')
      .eq('id', modelId)
      .single();

    return {
      modelId: usage.model_id,
      requestCount: usage.request_count,
      inputTokens: usage.input_tokens,
      outputTokens: usage.output_tokens,
      lastReset: new Date(usage.last_reset),
      dailyLimits: {
        requests: config?.daily_request_limit,
        inputTokens: config?.tpm_limit,
        outputTokens: config?.tpm_limit
      }
    };
  }

  /**
   * Calculate cost for a request
   */
  private async calculateCost(modelId: string, inputTokens: number, outputTokens: number): Promise<number> {
    const { data: config } = await getSupabaseClient()
      .from('ai_model_configs')
      .select('cost_per_1k_tokens')
      .eq('id', modelId)
      .single();

    if (!config || !config.cost_per_1k_tokens) {
      return 0;
    }

    const totalTokens = inputTokens + outputTokens;
    return (totalTokens / 1000) * config.cost_per_1k_tokens;
  }

  /**
   * Check usage thresholds and log warnings - Requirements 1.3
   */
  private async checkUsageThresholds(modelId: string): Promise<void> {
    const usage = await this.getCurrentUsage(modelId);
    
    if (usage.dailyLimits.requests) {
      const usagePercentage = (usage.requestCount / usage.dailyLimits.requests) * 100;
      
      if (usagePercentage >= 90) {
        logger.warn('AI model usage at 90% threshold', {
          modelId,
          currentUsage: usage.requestCount,
          limit: usage.dailyLimits.requests,
          percentage: usagePercentage
        });
      } else if (usagePercentage >= 80) {
        logger.warn('AI model usage at 80% threshold', {
          modelId,
          currentUsage: usage.requestCount,
          limit: usage.dailyLimits.requests,
          percentage: usagePercentage
        });
      }
    }
  }

  /**
   * Cache usage data
   */
  private async cacheUsage(modelId: string, usage: ModelUsage): Promise<void> {
    await redisConnectionPool.execute(async (redis) => {
      const key = `${this.CACHE_PREFIX}${modelId}`;
      await redis.setex(key, this.CACHE_TTL, JSON.stringify(usage));
    });
  }

  /**
   * Get cached usage data
   */
  private async getCachedUsage(modelId: string): Promise<ModelUsage | null> {
    return redisConnectionPool.execute(async (redis) => {
      const key = `${this.CACHE_PREFIX}${modelId}`;
      const cached = await redis.get(key);
      
      if (cached && typeof cached === 'string') {
        try {
          const parsed = JSON.parse(cached);
          return {
            ...parsed,
            lastReset: new Date(parsed.lastReset)
          };
        } catch (error) {
          logger.warn('Failed to parse cached usage data', { modelId, error });
        }
      }
      
      return null;
    });
  }

  /**
   * Clear usage cache for a model
   */
  private async clearUsageCache(modelId: string): Promise<void> {
    await redisConnectionPool.execute(async (redis) => {
      const key = `${this.CACHE_PREFIX}${modelId}`;
      await redis.del(key);
    });
  }

  /**
   * Clear all usage caches
   */
  private async clearAllUsageCaches(): Promise<void> {
    await redisConnectionPool.execute(async (redis) => {
      const keys = await redis.keys(`${this.CACHE_PREFIX}*`);
      if (keys.length > 0) {
        await redis.del(...keys);
      }
    });
  }

  /**
   * Get default usage when no data exists
   */
  private getDefaultUsage(modelId: string): ModelUsage {
    return {
      modelId,
      requestCount: 0,
      inputTokens: 0,
      outputTokens: 0,
      lastReset: new Date(),
      dailyLimits: {}
    };
  }

  /**
   * Setup notification listener for daily resets
   */
  private setupResetNotificationListener(): void {
    // This would be implemented with a Redis pub/sub listener
    // For now, we'll rely on the pg_cron function to handle resets
    logger.info('Usage tracking service initialized with reset notification listener');
  }
}

// Export singleton instance
export const usageTrackingService = new UsageTrackingService();