# TravelViz Deployment Guide

This guide provides comprehensive instructions for deploying TravelViz to production using the approved tech stack.

## Table of Contents

1. [Deployment Overview](#deployment-overview)
2. [Pre-deployment Checklist](#pre-deployment-checklist)
3. [Vercel Deployment (Web)](#vercel-deployment-web)
4. [Render Deployment (Hub)](#render-deployment-hub)
5. [Database Setup (Supabase)](#database-setup-supabase)
6. [Redis Setup (Upstash)](#redis-setup-upstash)
7. [Post-deployment](#post-deployment)
8. [Production Best Practices](#production-best-practices)

## Deployment Overview

### Approved Tech Stack

TravelViz uses a carefully selected, minimal tech stack optimized for cost and performance:

- **Frontend**: Vercel (includes edge functions, analytics, security headers)
- **Backend**: Render (Node.js hosting with built-in metrics)
- **Database**: Supabase (PostgreSQL with RLS, real-time, auth)
- **Redis**: Upstash (serverless Redis with connection pooling)
- **AI**: OpenRouter and Google Gemini (optimized for $0/month)
- **Maps**: Google Maps and Mapbox APIs

### Architecture Overview

```
┌─────────────────┐     ┌─────────────────┐
│   Vercel (Web)  │────▶│  Render (Hub)   │
│   Port: 443     │     │   Port: 443     │
└─────────────────┘     └────────┬────────┘
                                 │
                    ┌────────────┴────────────┐
                    │                         │
              ┌─────▼─────┐           ┌──────▼──────┐
              │ Supabase  │           │   Upstash   │
              │PostgreSQL │           │    Redis    │
              └───────────┘           └─────────────┘
```

### Cost Considerations

- **Vercel**: Free tier includes 100GB bandwidth, perfect for most use cases
- **Render**: Free tier available for web services with sleep after 15 minutes of inactivity
- **Supabase**: Free tier includes 500MB database, 1GB storage, 50MB bandwidth
- **Upstash**: Free tier includes 10,000 commands daily
- **AI Models**: Configured for $0/month using Gemini Flash 2.0 free tier

## Pre-deployment Checklist

### 1. Environment Variables Setup

Ensure all required environment variables are documented:

```bash
# Create production environment files from examples
cp packages/web/.env.example packages/web/.env.production
cp packages/hub/.env.example packages/hub/.env.production
```

### 2. Database Migrations

```bash
# Run all migrations locally first
cd packages/hub
pnpm db:migrate
```

### 3. Security Checks

- [ ] All API keys are in environment variables
- [ ] No sensitive data in source code
- [ ] CORS settings configured correctly
- [ ] Input validation implemented
- [ ] Rate limiting configured

### 4. Performance Optimization

```bash
# Build and analyze bundle size
pnpm build
cd packages/web && pnpm analyze
```

## Vercel Deployment (Web)

### Step 1: Install Vercel CLI

```bash
npm i -g vercel
```

### Step 2: Initialize Vercel Project

```bash
cd packages/web
vercel
```

Follow the prompts:

- Select your account
- Link to existing project or create new
- Confirm project settings

### Step 3: Configure Build Settings

Create `vercel.json` in `packages/web`:

```json
{
  "buildCommand": "cd ../.. && pnpm build:web",
  "outputDirectory": ".next",
  "installCommand": "cd ../.. && pnpm install",
  "framework": "nextjs",
  "env": {
    "NEXT_PUBLIC_API_URL": "@travelviz_api_url",
    "NEXT_PUBLIC_SUPABASE_URL": "@supabase_url",
    "NEXT_PUBLIC_SUPABASE_ANON_KEY": "@supabase_anon_key",
    "NEXT_PUBLIC_MAPBOX_TOKEN": "@mapbox_token",
    "NEXT_PUBLIC_GOOGLE_MAPS_API_KEY": "@google_maps_api_key"
  },
  "headers": [
    {
      "source": "/(.*)",
      "headers": [
        {
          "key": "X-Content-Type-Options",
          "value": "nosniff"
        },
        {
          "key": "X-Frame-Options",
          "value": "DENY"
        },
        {
          "key": "X-XSS-Protection",
          "value": "1; mode=block"
        },
        {
          "key": "Referrer-Policy",
          "value": "strict-origin-when-cross-origin"
        },
        {
          "key": "Permissions-Policy",
          "value": "camera=(), microphone=(), geolocation=()"
        }
      ]
    }
  ]
}
```

### Step 4: Environment Variables

In Vercel Dashboard:

1. Go to Project Settings → Environment Variables
2. Add all variables from `.env.production`
3. Set appropriate scopes (Production, Preview, Development)

### Step 5: Deploy

```bash
vercel --prod
```

### Step 6: Domain Setup

1. In Vercel Dashboard → Domains
2. Add custom domain
3. Configure DNS records as instructed
4. Enable automatic HTTPS

## Render Deployment (Hub)

### Step 1: Create render.yaml

Create `render.yaml` in repository root:

```yaml
services:
  - type: web
    name: travelviz-hub
    env: node
    region: oregon
    plan: free
    buildCommand: 'pnpm install && pnpm build:hub'
    startCommand: 'cd packages/hub && node dist/index.js'
    envVars:
      - key: NODE_ENV
        value: production
      - key: PORT
        value: 3001
      - key: DATABASE_URL
        sync: false
      - key: REDIS_URL
        sync: false
      - key: SUPABASE_URL
        sync: false
      - key: SUPABASE_SERVICE_KEY
        sync: false
      - key: GOOGLE_GEMINI_API_KEY
        sync: false
      - key: OPENROUTER_API_KEY
        sync: false
      - key: JWT_SECRET
        generateValue: true
      - key: CORS_ORIGIN
        value: https://your-app.vercel.app
    healthCheckPath: /health
    autoDeploy: false
```

### Step 2: Connect Repository

1. Sign up/login to [render.com](https://render.com)
2. New → Web Service
3. Connect GitHub/GitLab repository
4. Select branch (usually `main`)

### Step 3: Configure Service

1. Name: `travelviz-hub`
2. Region: Choose closest to users
3. Branch: `main`
4. Build Command: `pnpm install && pnpm build:hub`
5. Start Command: `cd packages/hub && node dist/index.js`

### Step 4: Environment Variables

Add in Render Dashboard:

- `DATABASE_URL`: From Supabase
- `REDIS_URL`: From Upstash
- `SUPABASE_URL`: Your Supabase project URL
- `SUPABASE_SERVICE_KEY`: Service role key
- `GOOGLE_GEMINI_API_KEY`: From Google AI Studio
- `OPENROUTER_API_KEY`: From OpenRouter (if using fallback)
- `CORS_ORIGIN`: Your Vercel app URL

### Step 5: Health Checks

Render automatically monitors the `/health` endpoint. Ensure it's implemented:

```typescript
// packages/hub/src/index.ts
app.get('/health', (req, res) => {
  res.json({ status: 'ok', timestamp: new Date().toISOString() });
});
```

## Database Setup (Supabase)

### Step 1: Create Project

1. Go to [supabase.com](https://supabase.com)
2. Create new project
3. Choose region closest to your users
4. Note the database password

### Step 2: Run Migrations

```bash
# Using Supabase CLI
supabase db push --db-url "postgresql://postgres:[password]@[host]:5432/postgres"
```

Or use the SQL editor in Supabase Dashboard.

### Step 3: Configure RLS Policies

Example RLS policies for trips table:

```sql
-- Enable RLS
ALTER TABLE trips ENABLE ROW LEVEL SECURITY;

-- Users can read their own trips
CREATE POLICY "Users can read own trips" ON trips
  FOR SELECT USING (auth.uid() = user_id);

-- Users can insert their own trips
CREATE POLICY "Users can insert own trips" ON trips
  FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Users can update their own trips
CREATE POLICY "Users can update own trips" ON trips
  FOR UPDATE USING (auth.uid() = user_id);

-- Users can delete their own trips
CREATE POLICY "Users can delete own trips" ON trips
  FOR DELETE USING (auth.uid() = user_id);
```

### Step 4: Backup Strategy

1. Enable Point-in-Time Recovery in Supabase Dashboard
2. Set up daily backups
3. Download backups periodically:

```bash
pg_dump "postgresql://postgres:[password]@[host]:5432/postgres" > backup.sql
```

## Redis Setup (Upstash)

### Step 1: Create Redis Instance

1. Sign up at [upstash.com](https://upstash.com)
2. Create new Redis database
3. Choose region closest to your Render deployment
4. Enable "Eviction" for cache-like behavior

### Step 2: Connection Configuration

Get credentials from Upstash Console:

- Redis URL (includes auth)
- REST API URL and token (for serverless)

### Step 3: Configure Connection Pooling

Upstash handles connection pooling automatically. Use the Redis URL directly:

```typescript
// packages/hub/src/lib/redis.ts
import { Redis } from '@upstash/redis';

export const redis = new Redis({
  url: process.env.UPSTASH_REDIS_REST_URL!,
  token: process.env.UPSTASH_REDIS_REST_TOKEN!,
});
```

### Step 4: Cache Strategies

Implement caching for:

- AI parsing results (TTL: 1 hour)
- Trip templates (TTL: 24 hours)
- User sessions (TTL: 7 days)
- Price monitoring data (TTL: 15 minutes)

## Post-deployment

### Verification Steps

1. **Frontend Health Check**

   ```bash
   curl https://your-app.vercel.app
   ```

2. **API Health Check**

   ```bash
   curl https://your-api.onrender.com/health
   ```

3. **Database Connection**

   ```bash
   curl https://your-api.onrender.com/api/v1/health/db
   ```

4. **Redis Connection**
   ```bash
   curl https://your-api.onrender.com/api/v1/health/redis
   ```

### Monitoring Setup

#### Vercel Analytics

- Automatically enabled in Vercel Dashboard
- View Web Vitals and usage metrics

#### Render Metrics

- CPU and memory usage visible in Render Dashboard
- Set up alerts for high usage

#### Supabase Monitoring

- Database metrics in Supabase Dashboard
- Query performance insights
- Real-time connection monitoring

### Performance Testing

```bash
# Load testing with Artillery
npm install -g artillery

# Create test script
echo 'config:
  target: "https://your-api.onrender.com"
  phases:
    - duration: 60
      arrivalRate: 10
scenarios:
  - name: "Health Check"
    flow:
      - get:
          url: "/health"' > loadtest.yml

# Run test
artillery run loadtest.yml
```

### Rollback Procedures

#### Vercel Rollback

```bash
# List deployments
vercel ls

# Rollback to specific deployment
vercel rollback [deployment-url]
```

#### Render Rollback

1. Go to Render Dashboard
2. Select service → Events
3. Click "Rollback" on previous successful deploy

#### Database Rollback

1. Supabase Dashboard → Database → Backups
2. Restore from point-in-time or daily backup

## Production Best Practices

### Security Headers

Already configured in `vercel.json`. For the API, add to Express:

```typescript
// packages/hub/src/middleware/security.ts
import helmet from 'helmet';

app.use(
  helmet({
    contentSecurityPolicy: {
      directives: {
        defaultSrc: ["'self'"],
        styleSrc: ["'self'", "'unsafe-inline'"],
        scriptSrc: ["'self'"],
        imgSrc: ["'self'", 'data:', 'https:'],
      },
    },
  })
);
```

### CORS Configuration

```typescript
// packages/hub/src/middleware/cors.ts
import cors from 'cors';

const corsOptions = {
  origin: process.env.CORS_ORIGIN || 'https://travelviz.vercel.app',
  credentials: true,
  optionsSuccessStatus: 200,
};

app.use(cors(corsOptions));
```

### Rate Limiting

```typescript
// packages/hub/src/middleware/rateLimit.ts
import rateLimit from 'express-rate-limit';
import { redis } from '../lib/redis';

const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // limit each IP to 100 requests per windowMs
  standardHeaders: true,
  legacyHeaders: false,
  store: new RedisStore({
    client: redis,
    prefix: 'rl:',
  }),
});

app.use('/api/', limiter);
```

### Error Tracking

Use Vercel's built-in error tracking and Render's logs:

```typescript
// packages/hub/src/middleware/errorHandler.ts
app.use((err: Error, req: Request, res: Response, next: NextFunction) => {
  console.error('Error:', {
    message: err.message,
    stack: err.stack,
    url: req.url,
    method: req.method,
    ip: req.ip,
    timestamp: new Date().toISOString(),
  });

  res.status(500).json({
    error: 'Internal Server Error',
    message: process.env.NODE_ENV === 'development' ? err.message : undefined,
  });
});
```

### Input Sanitization

```typescript
// packages/hub/src/middleware/sanitize.ts
import DOMPurify from 'isomorphic-dompurify';

export const sanitizeInput = (req: Request, res: Response, next: NextFunction) => {
  if (req.body) {
    Object.keys(req.body).forEach(key => {
      if (typeof req.body[key] === 'string') {
        req.body[key] = DOMPurify.sanitize(req.body[key]);
      }
    });
  }
  next();
};
```

## Troubleshooting

### Common Issues

1. **CORS Errors**
   - Verify `CORS_ORIGIN` matches your frontend URL exactly
   - Check for trailing slashes

2. **Database Connection Failed**
   - Verify `DATABASE_URL` format
   - Check IP allowlist in Supabase

3. **Redis Connection Issues**
   - Ensure using REST URL for Upstash
   - Verify tokens are correct

4. **Build Failures**
   - Check Node.js version matches local
   - Verify all dependencies are in `package.json`

### Support Resources

- Vercel: [vercel.com/docs](https://vercel.com/docs)
- Render: [render.com/docs](https://render.com/docs)
- Supabase: [supabase.com/docs](https://supabase.com/docs)
- Upstash: [docs.upstash.com](https://docs.upstash.com)

---

Remember: This deployment uses only the approved services. Do not add additional infrastructure or monitoring tools. The selected stack provides everything needed for a production deployment.
