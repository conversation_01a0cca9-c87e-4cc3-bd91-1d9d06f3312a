import type { AnalyticsEventData } from './utility-types';

/**
 * Analytics provider interface for dependency injection
 * This abstraction allows swapping analytics providers without changing application code
 */
export interface AnalyticsProvider {
  /**
   * Track a custom event
   * @param event - Event name
   * @param properties - Optional event properties
   */
  track(event: string, properties?: AnalyticsEventData): void;

  /**
   * Identify a user
   * @param userId - Unique user identifier
   * @param traits - Optional user traits/properties
   */
  identify(userId: string, traits?: AnalyticsEventData): void;

  /**
   * Reset the current user session
   */
  reset(): void;

  /**
   * Set super properties that will be included with every event
   * @param properties - Properties to persist across all events
   */
  setSuperProperties?(properties: AnalyticsEventData): void;

  /**
   * Get the current user's distinct ID
   * @returns The user's distinct ID if available
   */
  getDistinctId?(): string | undefined;
}

/**
 * Analytics event types for type safety
 */
export enum AnalyticsEvent {
  // Import events
  IMPORT_STARTED = 'import_started',
  IMPORT_COMPLETED = 'import_completed',
  IMPORT_FAILED = 'import_failed',
  IMPORT_PROGRESS = 'import_progress',
  
  // Trip events
  TRIP_CREATED = 'trip_created',
  TRIP_VIEWED = 'trip_viewed',
  TRIP_EDITED = 'trip_edited',
  TRIP_SHARED = 'trip_shared',
  TRIP_DELETED = 'trip_deleted',
  
  // User events
  USER_SIGNED_UP = 'user_signed_up',
  USER_LOGGED_IN = 'user_logged_in',
  USER_LOGGED_OUT = 'user_logged_out',
  
  // Feature usage
  FEATURE_USED = 'feature_used',
  FEATURE_DISCOVERED = 'feature_discovered',
}

/**
 * Standard properties for analytics events
 */
export interface AnalyticsProperties {
  // Common properties
  timestamp?: string;
  sessionId?: string;
  
  // Import specific
  source?: 'chatgpt' | 'claude' | 'gemini' | 'text' | 'other';
  importId?: string;
  duration?: number;
  errorMessage?: string;
  errorCode?: string;
  
  // Trip specific
  tripId?: string;
  tripDays?: number;
  tripLocations?: number;
  
  // Feature specific
  featureName?: string;
  featureCategory?: string;
  
  // Any additional properties
  [key: string]: string | number | boolean | null | undefined;
}

/**
 * Analytics context configuration
 */
export interface AnalyticsConfig {
  provider: 'posthog' | 'mock' | 'console';
  apiKey?: string;
  apiHost?: string;
  debug?: boolean;
  disabled?: boolean;
}