import { z } from 'zod';

// Date validation helpers
const isValidDate = (dateString: string) => {
  const date = new Date(dateString);
  return !isNaN(date.getTime());
};

// Trip status enum
export const TripStatus = z.enum([
  'draft',
  'planning',
  'confirmed',
  'in_progress',
  'completed',
  'cancelled'
]);

// Trip visibility enum
export const TripVisibility = z.enum([
  'private',
  'unlisted',
  'public'
]);

// Activity types enum
export const ActivityType = z.enum([
  'flight',
  'accommodation',
  'transport',
  'dining',
  'activity',
  'shopping',
  'other'
]);

// Trip creation schema
export const createTripSchema = z.object({
  title: z.string()
    .min(1, 'Title is required')
    .max(200, 'Title must be less than 200 characters')
    .transform(title => title.trim()),
  
  description: z.string()
    .max(1000, 'Description must be less than 1000 characters')
    .transform(desc => desc?.trim())
    .optional(),
  
  startDate: z.string()
    .refine(isValidDate, 'Invalid start date format')
    .optional(),
  
  endDate: z.string()
    .refine(isValidDate, 'Invalid end date format')
    .optional(),
  
  destination: z.string()
    .max(200, 'Destination must be less than 200 characters')
    .transform(dest => dest?.trim())
    .optional(),
  
  status: TripStatus.optional(),
  visibility: TripVisibility.optional(),
  coverImage: z.string().url('Invalid cover image URL').optional(),
  tags: z.array(z.string()).optional(),
  budgetAmount: z.number().positive('Budget must be positive').optional(),
  budgetCurrency: z.string().length(3, 'Currency must be a 3-letter code').toUpperCase().optional(),
}).refine(
  (data) => {
    if (data.startDate && data.endDate) {
      const start = new Date(data.startDate);
      const end = new Date(data.endDate);
      return end >= start;
    }
    return true;
  },
  {
    message: 'End date must be after or equal to start date',
    path: ['endDate'],
  }
);

// Trip update schema (all fields optional)
export const updateTripSchema = z.object({
  title: z.string()
    .min(1, 'Title cannot be empty')
    .max(200, 'Title must be less than 200 characters')
    .transform(title => title.trim())
    .optional(),
  
  description: z.string()
    .max(1000, 'Description must be less than 1000 characters')
    .transform(desc => desc?.trim())
    .nullish(),
  
  startDate: z.string()
    .refine(isValidDate, 'Invalid start date format')
    .optional(),
  
  endDate: z.string()
    .refine(isValidDate, 'Invalid end date format')
    .optional(),
  
  destination: z.string()
    .min(1, 'Destination cannot be empty')
    .max(200, 'Destination must be less than 200 characters')
    .transform(dest => dest.trim())
    .optional(),
  
  status: TripStatus.optional(),
  visibility: TripVisibility.optional(),
  coverImage: z.string().url('Invalid cover image URL').optional(),
  tags: z.array(z.string()).optional(),
  budgetAmount: z.number().positive('Budget must be positive').optional(),
  budgetCurrency: z.string().length(3, 'Currency must be a 3-letter code').toUpperCase().optional(),
}).refine(
  (data) => {
    if (data.startDate && data.endDate) {
      const start = new Date(data.startDate);
      const end = new Date(data.endDate);
      return end >= start;
    }
    return true;
  },
  {
    message: 'End date must be after or equal to start date',
    path: ['endDate'],
  }
);

// Activity creation schema
export const createActivitySchema = z.object({
  title: z.string()
    .min(1, 'Title is required')
    .max(200, 'Title must be less than 200 characters')
    .transform(title => title.trim()),
  
  description: z.string()
    .max(1000, 'Description must be less than 1000 characters')
    .transform(desc => desc?.trim())
    .optional(),
  
  type: ActivityType.optional(),
  
  startTime: z.string()
    .refine(isValidDate, 'Invalid start time format')
    .optional(),
  
  endTime: z.string()
    .refine(isValidDate, 'Invalid end time format')
    .optional(),
  
  location: z.string()
    .max(200, 'Location must be less than 200 characters')
    .transform(loc => loc?.trim())
    .optional(),
  
  locationLat: z.number().min(-90).max(90).optional(),
  locationLng: z.number().min(-180).max(180).optional(),
  
  price: z.number()
    .positive('Price must be positive')
    .optional(),
  
  currency: z.string()
    .length(3, 'Currency must be a 3-letter code')
    .toUpperCase()
    .optional(),
  
  bookingReference: z.string().optional(),
  
  bookingUrl: z.string()
    .url('Invalid booking URL')
    .optional(),
  
  notes: z.string()
    .max(2000, 'Notes must be less than 2000 characters')
    .optional(),
  
  attachments: z.array(z.string()).optional(),
}).refine(
  (data) => {
    if (data.startTime && data.endTime) {
      const start = new Date(data.startTime);
      const end = new Date(data.endTime);
      return end >= start;
    }
    return true;
  },
  {
    message: 'End time must be after or equal to start time',
    path: ['endTime'],
  }
);

// Activity update schema
export const updateActivitySchema = z.object({
  title: z.string()
    .min(1, 'Title cannot be empty')
    .max(200, 'Title must be less than 200 characters')
    .transform(title => title.trim())
    .optional(),
  
  description: z.string()
    .max(1000, 'Description must be less than 1000 characters')
    .transform(desc => desc?.trim())
    .optional(),
  
  location: z.string()
    .max(200, 'Location must be less than 200 characters')
    .transform(loc => loc?.trim())
    .optional(),
  
  startTime: z.string()
    .refine(isValidDate, 'Invalid start time format')
    .optional(),
  
  endTime: z.string()
    .refine(isValidDate, 'Invalid end time format')
    .optional(),
  
  type: ActivityType.optional(),
  
  price: z.number()
    .positive('Price must be positive')
    .optional(),
  
  currency: z.string()
    .length(3, 'Currency must be a 3-letter code')
    .toUpperCase()
    .optional(),
  
  bookingUrl: z.string()
    .url('Invalid booking URL')
    .optional(),
}).refine(
  (data) => {
    if (data.startTime && data.endTime) {
      const start = new Date(data.startTime);
      const end = new Date(data.endTime);
      return end > start;
    }
    return true;
  },
  {
    message: 'End time must be after start time',
    path: ['endTime'],
  }
);

// Trip ID parameter schema
export const tripIdParamSchema = z.object({
  id: z.string().uuid('Invalid trip ID format'),
});

// Activity ID parameter schema (for routes like /trips/:tripId/activities/:id)
export const activityIdParamSchema = z.object({
  id: z.string().uuid('Invalid activity ID format'),
  tripId: z.string().uuid('Invalid trip ID format'),
});

// Activity ID only parameter schema (for routes like /activities/:id)
export const activityIdOnlyParamSchema = z.object({
  id: z.string().uuid('Invalid activity ID format'),
});

// Reorder activities schema
export const reorderActivitiesSchema = z.object({
  orderedIds: z.array(z.string().uuid('Invalid activity ID format'))
    .min(1, 'At least one activity ID is required')
    .max(100, 'Cannot reorder more than 100 activities at once'),
});

// Type exports
export type CreateTripInput = z.infer<typeof createTripSchema>;
export type UpdateTripInput = z.infer<typeof updateTripSchema>;
export type CreateActivityInput = z.infer<typeof createActivitySchema>;
export type UpdateActivityInput = z.infer<typeof updateActivitySchema>;
export type TripIdParam = z.infer<typeof tripIdParamSchema>;
export type ActivityIdParam = z.infer<typeof activityIdParamSchema>;
export type ReorderActivitiesInput = z.infer<typeof reorderActivitiesSchema>;