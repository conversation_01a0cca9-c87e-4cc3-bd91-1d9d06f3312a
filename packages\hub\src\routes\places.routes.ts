import { Router } from 'express';
import { PlacesService } from '../services/places.service';
import { createSuccessResponse, createErrorResponse, HTTP_STATUS } from '@travelviz/shared';
import { authenticateSupabaseUser, SupabaseAuthenticatedRequest } from '../middleware/supabase-auth.middleware';
import { createRateLimit } from '../middleware/rate-limit.middleware';
import { logger } from '../utils/logger';

const router: Router = Router();
const placesService = new PlacesService();

// Create a specialized rate limiter for Google Places API calls
// More restrictive than general API calls to manage costs
const placesRateLimit = createRateLimit({
  windowMs: 60 * 1000, // 1 minute
  max: 30, // 30 requests per minute (Google Places API has usage limits)
  message: 'Too many places API requests. Please try again later.'
});

// Apply authentication to all places routes
router.use(authenticateSupabaseUser);

// Apply rate limiting to all places routes
router.use(placesRateLimit);

/**
 * GET /api/places/autocomplete
 * Search for places using Google Places Autocomplete
 */
router.get('/autocomplete', async (req: SupabaseAuthenticatedRequest, res) => {
  // Support both 'query' and 'input' parameters for compatibility
  const query = typeof req.query.query === 'string' ? req.query.query.trim() : 
                typeof req.query.input === 'string' ? req.query.input.trim() : '';
  
  try {
    if (!query) {
      logger.warn('Places autocomplete: Missing query parameter', { 
        userId: req.user?.id,
        ip: req.ip 
      });
      return res.status(HTTP_STATUS.BAD_REQUEST).json(
        createErrorResponse('Query parameter is required')
      );
    }

    logger.info('Places autocomplete request', { 
      query,
      userId: req.user?.id 
    });

    const result = await placesService.searchPlaces(query);
    res.json(createSuccessResponse(result));
  } catch (error) {
    logger.error('Places autocomplete error', { 
      error: error instanceof Error ? error.message : 'Unknown error',
      query,
      userId: req.user?.id,
      stack: error instanceof Error ? error.stack : undefined
    });
    res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(
      createErrorResponse('Failed to search places')
    );
  }
});

/**
 * GET /api/places/:placeId
 * Get detailed information about a specific place
 */
router.get('/:placeId', async (req: SupabaseAuthenticatedRequest, res) => {
  try {
    const { placeId } = req.params;

    if (!placeId) {
      logger.warn('Place details: Missing placeId parameter', { 
        userId: req.user?.id,
        ip: req.ip 
      });
      return res.status(HTTP_STATUS.BAD_REQUEST).json(
        createErrorResponse('Place ID is required')
      );
    }

    logger.info('Place details request', { 
      placeId,
      userId: req.user?.id 
    });

    const result = await placesService.getPlaceDetails(placeId);
    res.json(createSuccessResponse(result));
  } catch (error) {
    logger.error('Place details error', { 
      error: error instanceof Error ? error.message : 'Unknown error',
      placeId: req.params.placeId,
      userId: req.user?.id,
      stack: error instanceof Error ? error.stack : undefined
    });
    
    // Check if it's a not found error from Google Places API
    if (error instanceof Error && error.message.includes('not found')) {
      res.status(HTTP_STATUS.NOT_FOUND).json(
        createErrorResponse('Place not found')
      );
    } else {
      res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(
        createErrorResponse('Failed to get place details')
      );
    }
  }
});

/**
 * POST /api/v1/places/geocode
 * Batch geocode multiple locations
 */
router.post('/geocode', async (req: SupabaseAuthenticatedRequest, res) => {
  try {
    const { locations } = req.body;
    
    if (!locations || !Array.isArray(locations) || locations.length === 0) {
      logger.warn('Geocode: Invalid locations parameter', { 
        userId: req.user?.id,
        body: req.body 
      });
      return res.status(HTTP_STATUS.BAD_REQUEST).json(
        createErrorResponse('Locations array is required')
      );
    }

    if (locations.length > 50) {
      return res.status(HTTP_STATUS.BAD_REQUEST).json(
        createErrorResponse('Maximum 50 locations allowed per request')
      );
    }

    logger.info('Batch geocode request', { 
      count: locations.length,
      userId: req.user?.id 
    });

    // Use geocoding service directly since places service doesn't have this method
    const { GeocodingService } = await import('../services/geocoding.service');
    const geocodingService = new GeocodingService();
    
    const results = await geocodingService.geocodeBatch(locations);
    
    // Convert Map to object for JSON response
    interface GeocodeResult {
      lat: number;
      lng: number;
      formatted: string;
    }
    const response: Record<string, GeocodeResult | null> = {};
    results.forEach((value, key) => {
      response[key] = value;
    });

    res.json(createSuccessResponse({
      locations: response,
      geocoded: Object.keys(response).filter(k => response[k] !== null).length,
      failed: Object.keys(response).filter(k => response[k] === null).length
    }));
  } catch (error) {
    logger.error('Batch geocode error', { 
      error: error instanceof Error ? error.message : 'Unknown error',
      userId: req.user?.id,
      stack: error instanceof Error ? error.stack : undefined
    });
    res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(
      createErrorResponse('Failed to geocode locations')
    );
  }
});

export { router as placesRoutes };