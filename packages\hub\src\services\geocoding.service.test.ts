import axios from 'axios';
import { vi, describe, it, expect, beforeEach, afterEach } from 'vitest';
import { GeocodingService } from './geocoding.service';

vi.mock('axios');
const mockedAxios = axios as any;

describe('GeocodingService', () => {
  let geocodingService: GeocodingService;
  const MOCK_MAPBOX_TOKEN = 'mock-test-token-12345';

  beforeEach(() => {
    vi.clearAllMocks();
    // Mock environment variable
    process.env.MAPBOX_ACCESS_TOKEN = MOCK_MAPBOX_TOKEN;
    geocodingService = new GeocodingService();
  });

  afterEach(() => {
    delete process.env.MAPBOX_ACCESS_TOKEN;
  });

  describe('geocode', () => {
    it('should geocode a location successfully', async () => {
      const mockResponse = {
        data: {
          type: 'FeatureCollection',
          features: [{
            geometry: {
              coordinates: [-118.2437, 34.0522]
            },
            properties: {
              place_formatted: 'Los Angeles, California, United States'
            }
          }]
        }
      };

      mockedAxios.get.mockResolvedValueOnce(mockResponse);

      const result = await geocodingService.geocode('Los Angeles');

      expect(result).toEqual({
        lat: 34.0522,
        lng: -118.2437,
        formatted: 'Los Angeles, California, United States'
      });

      expect(mockedAxios.get).toHaveBeenCalledWith(
        'https://api.mapbox.com/search/geocode/v6/forward',
        expect.objectContaining({
          params: {
            q: 'Los Angeles',
            access_token: MOCK_MAPBOX_TOKEN,
            limit: 1,
            types: 'place,address'
          }
        })
      );
    });

    it('should return null for empty location', async () => {
      const result = await geocodingService.geocode('');
      expect(result).toBeNull();
      expect(mockedAxios.get).not.toHaveBeenCalled();
    });

    it('should return null when no access token is available', async () => {
      delete process.env.MAPBOX_ACCESS_TOKEN;
      const service = new GeocodingService();
      
      const result = await service.geocode('Los Angeles');
      expect(result).toBeNull();
      expect(mockedAxios.get).not.toHaveBeenCalled();
    });

    it('should return null when no results are found', async () => {
      mockedAxios.get.mockResolvedValueOnce({
        data: {
          type: 'FeatureCollection',
          features: []
        }
      });

      const result = await geocodingService.geocode('Nonexistent Place XYZ123');
      expect(result).toBeNull();
    });

    it('should handle API errors gracefully', async () => {
      mockedAxios.get.mockRejectedValueOnce(new Error('Network error'));

      const result = await geocodingService.geocode('Los Angeles');
      expect(result).toBeNull();
    });

    it('should use cache for repeated geocoding requests', async () => {
      const mockResponse = {
        data: {
          type: 'FeatureCollection',
          features: [{
            geometry: {
              coordinates: [-118.2437, 34.0522]
            },
            properties: {
              place_formatted: 'Los Angeles, California, United States'
            }
          }]
        }
      };

      mockedAxios.get.mockResolvedValueOnce(mockResponse);

      // First call
      const result1 = await geocodingService.geocode('Los Angeles');
      // Second call (should use cache)
      const result2 = await geocodingService.geocode('Los Angeles');

      expect(result1).toEqual(result2);
      expect(mockedAxios.get).toHaveBeenCalledTimes(1); // Only called once due to cache
    });
  });

  describe('geocodeBatch', () => {
    it('should geocode multiple locations', async () => {
      const locations = ['Los Angeles', 'New York', 'Chicago'];
      const mockResponses = [
        {
          data: {
            type: 'FeatureCollection',
            features: [{
              geometry: { coordinates: [-118.2437, 34.0522] },
              properties: { place_formatted: 'Los Angeles, CA' }
            }]
          }
        },
        {
          data: {
            type: 'FeatureCollection',
            features: [{
              geometry: { coordinates: [-74.0060, 40.7128] },
              properties: { place_formatted: 'New York, NY' }
            }]
          }
        },
        {
          data: {
            type: 'FeatureCollection',
            features: [{
              geometry: { coordinates: [-87.6298, 41.8781] },
              properties: { place_formatted: 'Chicago, IL' }
            }]
          }
        }
      ];

      mockResponses.forEach(response => {
        mockedAxios.get.mockResolvedValueOnce(response);
      });

      const results = await geocodingService.geocodeBatch(locations);

      expect(results.size).toBe(3);
      expect(results.get('Los Angeles')).toEqual({
        lat: 34.0522,
        lng: -118.2437,
        formatted: 'Los Angeles, CA'
      });
      expect(results.get('New York')).toEqual({
        lat: 40.7128,
        lng: -74.0060,
        formatted: 'New York, NY'
      });
      expect(results.get('Chicago')).toEqual({
        lat: 41.8781,
        lng: -87.6298,
        formatted: 'Chicago, IL'
      });
    });

    it('should handle duplicates efficiently', async () => {
      const locations = ['Los Angeles', 'Los Angeles', 'New York'];
      const mockResponses = [
        {
          data: {
            type: 'FeatureCollection',
            features: [{
              geometry: { coordinates: [-118.2437, 34.0522] },
              properties: { place_formatted: 'Los Angeles, CA' }
            }]
          }
        },
        {
          data: {
            type: 'FeatureCollection',
            features: [{
              geometry: { coordinates: [-74.0060, 40.7128] },
              properties: { place_formatted: 'New York, NY' }
            }]
          }
        }
      ];

      mockResponses.forEach(response => {
        mockedAxios.get.mockResolvedValueOnce(response);
      });

      const results = await geocodingService.geocodeBatch(locations);

      expect(results.size).toBe(2); // Only unique locations
      expect(mockedAxios.get).toHaveBeenCalledTimes(2); // Only called for unique locations
    });
  });

  describe('isAvailable', () => {
    it('should return true when access token is present', () => {
      expect(geocodingService.isAvailable()).toBe(true);
    });

    it('should return false when access token is missing', () => {
      delete process.env.MAPBOX_ACCESS_TOKEN;
      const service = new GeocodingService();
      expect(service.isAvailable()).toBe(false);
    });
  });

  describe('clearCache', () => {
    it('should clear the geocoding cache', async () => {
      const mockResponse = {
        data: {
          type: 'FeatureCollection',
          features: [{
            geometry: { coordinates: [-118.2437, 34.0522] },
            properties: { place_formatted: 'Los Angeles, CA' }
          }]
        }
      };

      mockedAxios.get.mockResolvedValue(mockResponse);

      // First call
      await geocodingService.geocode('Los Angeles');
      
      // Clear cache
      geocodingService.clearCache();
      
      // Second call (should not use cache)
      await geocodingService.geocode('Los Angeles');

      expect(mockedAxios.get).toHaveBeenCalledTimes(2);
    });
  });
});