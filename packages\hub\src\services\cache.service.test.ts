import { describe, it, expect, beforeEach, afterEach } from 'vitest';
import { CacheService } from './cache.service';
import { redisConnectionPool } from './redis-connection-pool.service';

// Real integration test - no mocks
// This test requires real Redis credentials in .env.test

describe('CacheService - Real Integration Tests', () => {
  let service: CacheService;
  const testKeyPrefix = `test_${Date.now()}_`;

  beforeEach(async () => {
    // Get singleton instance (already initialized with real Redis)
    service = CacheService.getInstance();
    
    // Wait for any async initialization
    await new Promise(resolve => setTimeout(resolve, 100));
    
    // Clean up any test keys from previous runs
    await cleanupTestKeys();
  });

  afterEach(async () => {
    // Clean up test data
    await cleanupTestKeys();
    service.resetStats();
  });

  async function cleanupTestKeys() {
    try {
      // Delete all test keys
      const testKeys = [
        `${testKeyPrefix}key1`,
        `${testKeyPrefix}key2`,
        `${testKeyPrefix}key3`,
        `${testKeyPrefix}key4`,
        `${testKeyPrefix}key5`,
        `${testKeyPrefix}test-key`,
        `${testKeyPrefix}complex:key:with:colons`
      ];
      
      for (const key of testKeys) {
        await service.del(key);
        await service.del(key, 'api');
        await service.del(key, 'session');
        await service.del(key, 'empty');
      }
    } catch (error) {
      // Ignore cleanup errors
    }
  }

  describe('getInstance', () => {
    it('should return singleton instance', () => {
      const instance1 = CacheService.getInstance();
      const instance2 = CacheService.getInstance();

      expect(instance1).toBe(instance2);
    });
  });

  describe('initialization', () => {
    it('should have Redis available with real credentials', async () => {
      // With real credentials from .env.test, Redis should be available
      const isAvailable = service.isAvailable();
      const healthCheck = await service.healthCheck();
      
      expect(isAvailable).toBe(true);
      expect(healthCheck).toBe(true);
    });

    it('should have proper connection pool metrics', () => {
      const stats = service.getStats();
      
      expect(stats.connectionPool).toBeDefined();
      expect(stats.connectionPool.totalConnections).toBeGreaterThan(0);
      expect(stats.circuitBreaker).toBeDefined();
      expect(stats.circuitBreaker.state).toBe('CLOSED');
    });
  });

  describe('get', () => {
    it('should get value from cache after setting it', async () => {
      const testKey = `${testKeyPrefix}test-key`;
      const testValue = { data: 'test', timestamp: Date.now() };
      
      // Set a value first
      await service.set(testKey, testValue);
      
      // Get it back
      const result = await service.get<typeof testValue>(testKey);

      expect(result).toEqual(testValue);
    });

    it('should handle cache miss for non-existent key', async () => {
      const testKey = `${testKeyPrefix}non-existent-key-${Date.now()}`;
      const result = await service.get(testKey);

      expect(result).toBeNull();
    });

    it('should use namespace in key', async () => {
      const testKey = `${testKeyPrefix}test-key`;
      const value = 'namespaced-value';
      
      // Set with namespace
      await service.set(testKey, value, { namespace: 'api' });
      
      // Get without namespace should return null
      const withoutNamespace = await service.get(testKey);
      expect(withoutNamespace).toBeNull();
      
      // Get with correct namespace should return value
      const withNamespace = await service.get(testKey, { namespace: 'api' });
      expect(withNamespace).toBe(value);
    });

    it('should track stats correctly', async () => {
      const initialStats = service.getStats();
      const baseHits = initialStats.hits;
      const baseMisses = initialStats.misses;
      
      // Set a value
      const testKey = `${testKeyPrefix}stats-test`;
      await service.set(testKey, 'value');
      
      // Hit
      await service.get(testKey);
      
      // Miss
      await service.get(`${testKey}-nonexistent`);

      const stats = service.getStats();
      expect(stats.hits).toBe(baseHits + 1);
      expect(stats.misses).toBe(baseMisses + 1);
    });
  });

  describe('set', () => {
    it('should set value in cache and retrieve it', async () => {
      const testKey = `${testKeyPrefix}set-test`;
      const testValue = { data: 'test', nested: { value: 123 } };
      
      const result = await service.set(testKey, testValue);
      expect(result).toBe(true);
      
      // Verify we can get it back
      const retrieved = await service.get(testKey);
      expect(retrieved).toEqual(testValue);
    });

    it('should set value with custom TTL', async () => {
      const testKey = `${testKeyPrefix}ttl-test`;
      const value = 'ttl-value';
      
      // Set with 2 second TTL
      const result = await service.set(testKey, value, { ttl: 2 });
      expect(result).toBe(true);
      
      // Should exist immediately
      const immediate = await service.get(testKey);
      expect(immediate).toBe(value);
      
      // Should expire after TTL
      await new Promise(resolve => setTimeout(resolve, 2500));
      const expired = await service.get(testKey);
      expect(expired).toBeNull();
    });

    it('should use namespace in key', async () => {
      const testKey = `${testKeyPrefix}namespace-test`;
      const value1 = 'api-value';
      const value2 = 'session-value';
      
      // Set different values in different namespaces
      await service.set(testKey, value1, { namespace: 'api' });
      await service.set(testKey, value2, { namespace: 'session' });
      
      // Each namespace should have its own value
      const apiValue = await service.get(testKey, { namespace: 'api' });
      const sessionValue = await service.get(testKey, { namespace: 'session' });
      
      expect(apiValue).toBe(value1);
      expect(sessionValue).toBe(value2);
    });

    it('should handle complex data types', async () => {
      const testKey = `${testKeyPrefix}complex-test`;
      const complexData = {
        string: 'test',
        number: 42,
        boolean: true,
        null: null,
        array: [1, 2, 3],
        nested: {
          deep: {
            value: 'nested'
          }
        },
        date: new Date().toISOString()
      };
      
      await service.set(testKey, complexData);
      const retrieved = await service.get(testKey);
      
      expect(retrieved).toEqual(complexData);
    });
  });

  describe('del', () => {
    it('should delete value from cache', async () => {
      const testKey = `${testKeyPrefix}delete-test`;
      
      // Set a value first
      await service.set(testKey, 'to-be-deleted');
      
      // Verify it exists
      const exists = await service.get(testKey);
      expect(exists).toBe('to-be-deleted');
      
      // Delete it
      const result = await service.del(testKey);
      expect(result).toBe(true);
      
      // Verify it's gone
      const afterDelete = await service.get(testKey);
      expect(afterDelete).toBeNull();
    });

    it('should delete with namespace', async () => {
      const testKey = `${testKeyPrefix}namespace-delete`;
      
      // Set values in different namespaces
      await service.set(testKey, 'api-value', { namespace: 'api' });
      await service.set(testKey, 'session-value', { namespace: 'session' });
      
      // Delete only from api namespace
      await service.del(testKey, 'api');
      
      // api namespace should be empty
      const apiValue = await service.get(testKey, { namespace: 'api' });
      expect(apiValue).toBeNull();
      
      // session namespace should still have value
      const sessionValue = await service.get(testKey, { namespace: 'session' });
      expect(sessionValue).toBe('session-value');
    });

    it('should handle deleting non-existent keys gracefully', async () => {
      const testKey = `${testKeyPrefix}non-existent-delete-${Date.now()}`;
      
      // Delete should still return true even if key doesn't exist
      const result = await service.del(testKey);
      expect(result).toBe(true);
    });
  });

  describe('clearNamespace', () => {
    it('should clear all keys in namespace', async () => {
      const namespace = `test_clear_${Date.now()}`;
      
      // Set multiple keys in the namespace
      await service.set('key1', 'value1', { namespace });
      await service.set('key2', 'value2', { namespace });
      await service.set('key3', 'value3', { namespace });
      
      // Also set a key in different namespace
      await service.set('key1', 'other-value', { namespace: 'other' });
      
      // Clear the test namespace
      const result = await service.clearNamespace(namespace);
      
      // Should have deleted 3 keys
      expect(result).toBeGreaterThanOrEqual(3);
      
      // Verify keys are gone
      const val1 = await service.get('key1', { namespace });
      const val2 = await service.get('key2', { namespace });
      const val3 = await service.get('key3', { namespace });
      
      expect(val1).toBeNull();
      expect(val2).toBeNull();
      expect(val3).toBeNull();
      
      // Other namespace should be untouched
      const otherVal = await service.get('key1', { namespace: 'other' });
      expect(otherVal).toBe('other-value');
      
      // Clean up
      await service.del('key1', 'other');
    });

    it('should handle empty namespace gracefully', async () => {
      const emptyNamespace = `empty_${Date.now()}`;
      
      const result = await service.clearNamespace(emptyNamespace);
      
      expect(result).toBe(0);
    });
  });

  describe('getOrSet', () => {
    it('should return cached value if exists', async () => {
      const testKey = `${testKeyPrefix}get-or-set`;
      const cachedValue = { data: 'cached', timestamp: Date.now() };
      
      // Pre-set the value
      await service.set(testKey, cachedValue);

      // Factory should not be called
      let factoryCalled = false;
      const factory = async () => {
        factoryCalled = true;
        return { data: 'fresh' };
      };
      
      const result = await service.getOrSet(testKey, factory);

      expect(result).toEqual(cachedValue);
      expect(factoryCalled).toBe(false);
    });

    it('should generate and cache fresh value if not exists', async () => {
      const testKey = `${testKeyPrefix}get-or-set-fresh-${Date.now()}`;
      const freshValue = { data: 'fresh', generated: true };

      let factoryCalled = false;
      const factory = async () => {
        factoryCalled = true;
        return freshValue;
      };
      
      const result = await service.getOrSet(testKey, factory);

      expect(result).toEqual(freshValue);
      expect(factoryCalled).toBe(true);
      
      // Verify it was cached
      const cached = await service.get(testKey);
      expect(cached).toEqual(freshValue);
    });

    it('should use custom options', async () => {
      const testKey = `${testKeyPrefix}get-or-set-options`;
      const namespace = 'custom';
      const value = 'custom-value';
      
      const factory = async () => value;
      
      // Use custom namespace and TTL
      await service.getOrSet(testKey, factory, { namespace, ttl: 300 });
      
      // Should be retrievable with same namespace
      const withNamespace = await service.get(testKey, { namespace });
      expect(withNamespace).toBe(value);
      
      // Should not exist without namespace
      const withoutNamespace = await service.get(testKey);
      expect(withoutNamespace).toBeNull();
    });

    it('should handle factory errors', async () => {
      const testKey = `${testKeyPrefix}get-or-set-error`;
      
      const factory = async () => {
        throw new Error('Factory error');
      };
      
      await expect(service.getOrSet(testKey, factory))
        .rejects.toThrow('Factory error');
      
      // Nothing should be cached
      const cached = await service.get(testKey);
      expect(cached).toBeNull();
    });

    it('should handle concurrent calls to same key', async () => {
      const testKey = `${testKeyPrefix}get-or-set-concurrent-${Date.now()}`;
      let factoryCallCount = 0;
      
      const factory = async () => {
        factoryCallCount++;
        await new Promise(resolve => setTimeout(resolve, 100));
        return `value-${factoryCallCount}`;
      };
      
      // Make multiple concurrent calls
      const [result1, result2, result3] = await Promise.all([
        service.getOrSet(testKey, factory),
        service.getOrSet(testKey, factory),
        service.getOrSet(testKey, factory)
      ]);
      
      // All should return the same value
      expect(result1).toBe(result2);
      expect(result2).toBe(result3);
      
      // Factory should be called multiple times (no in-flight deduplication)
      expect(factoryCallCount).toBeGreaterThanOrEqual(1);
    });
  });

  describe('stats management', () => {
    it('should track all operations accurately', async () => {
      // Reset stats for clean test
      service.resetStats();
      
      const statsKeyPrefix = `${testKeyPrefix}stats-`;
      
      // Generate a hit
      await service.set(`${statsKeyPrefix}hit`, 'value');
      await service.get(`${statsKeyPrefix}hit`);

      // Generate a miss
      await service.get(`${statsKeyPrefix}miss-${Date.now()}`);

      const stats = service.getStats();
      expect(stats.hits).toBeGreaterThanOrEqual(1);
      expect(stats.misses).toBeGreaterThanOrEqual(1);
      
      // Connection pool metrics should be included
      expect(stats.connectionPool).toBeDefined();
      expect(stats.connectionPool.totalConnections).toBeGreaterThan(0);
      expect(stats.circuitBreaker).toBeDefined();
      expect(stats.circuitBreaker.state).toBe('CLOSED');
    });

    it('should reset stats', async () => {
      // Generate some stats
      const testKey = `${testKeyPrefix}reset-stats`;
      await service.set(testKey, 'value');
      await service.get(testKey);
      await service.get(`${testKey}-miss`);

      // Reset
      service.resetStats();

      const stats = service.getStats();
      expect(stats.hits).toBe(0);
      expect(stats.misses).toBe(0);
      expect(stats.errors).toBe(0);
    });

    it('should return a copy of stats', () => {
      const stats1 = service.getStats();
      const originalHits = stats1.hits;
      
      // Modify the returned object
      stats1.hits = 999;

      // Get stats again - should be unaffected
      const stats2 = service.getStats();
      expect(stats2.hits).toBe(originalHits);
      expect(stats2.hits).not.toBe(999);
    });
  });

  describe('key structure', () => {
    it('should use correct key prefixes and namespaces', async () => {
      const baseKey = `${testKeyPrefix}key-structure`;
      
      // Test default namespace
      await service.set(baseKey, 'default');
      const defaultValue = await service.get(baseKey);
      expect(defaultValue).toBe('default');
      
      // Test custom namespaces
      await service.set(baseKey, 'api-data', { namespace: 'api' });
      await service.set(baseKey, 'session-data', { namespace: 'session' });
      
      // Each should be independent
      const apiData = await service.get(baseKey, { namespace: 'api' });
      const sessionData = await service.get(baseKey, { namespace: 'session' });
      const defaultData = await service.get(baseKey);
      
      expect(apiData).toBe('api-data');
      expect(sessionData).toBe('session-data');
      expect(defaultData).toBe('default');
    });

    it('should handle keys with colons correctly', async () => {
      const complexKey = `${testKeyPrefix}complex:key:with:colons`;
      const value = 'complex-value';
      
      await service.set(complexKey, value);
      const retrieved = await service.get(complexKey);
      
      expect(retrieved).toBe(value);
    });
  });

  describe('availability checks', () => {
    it('should report availability correctly', async () => {
      // With real Redis connection, should be available
      const isAvailable = service.isAvailable();
      expect(isAvailable).toBe(true);
      
      // Health check should also pass
      const healthCheck = await service.healthCheck();
      expect(healthCheck).toBe(true);
    });
  });

  describe('real-world scenarios', () => {
    it('should handle high-frequency operations', async () => {
      const operations = 20;
      const testKey = `${testKeyPrefix}high-freq`;
      
      // Rapid sets
      const setPromises = [];
      for (let i = 0; i < operations; i++) {
        setPromises.push(service.set(`${testKey}-${i}`, `value-${i}`));
      }
      await Promise.all(setPromises);
      
      // Rapid gets
      const getPromises = [];
      for (let i = 0; i < operations; i++) {
        getPromises.push(service.get(`${testKey}-${i}`));
      }
      const results = await Promise.all(getPromises);
      
      // All should succeed
      for (let i = 0; i < operations; i++) {
        expect(results[i]).toBe(`value-${i}`);
      }
    });

    it('should handle large payloads', async () => {
      const testKey = `${testKeyPrefix}large-payload`;
      
      // Create a large object (roughly 100KB)
      const largeArray = new Array(1000).fill(null).map((_, i) => ({
        id: i,
        data: 'x'.repeat(100),
        nested: {
          value: i * 2,
          description: 'This is a test description for item ' + i
        }
      }));
      
      const largePayload = {
        timestamp: Date.now(),
        items: largeArray,
        metadata: {
          version: '1.0.0',
          source: 'test'
        }
      };
      
      // Should handle large payloads
      const setResult = await service.set(testKey, largePayload);
      expect(setResult).toBe(true);
      
      const retrieved = await service.get(testKey);
      expect(retrieved).toEqual(largePayload);
    });
  });

  describe('error recovery', () => {
    it('should gracefully handle temporary connection issues', async () => {
      // This test validates that the service continues to work
      // even after transient errors
      const testKey = `${testKeyPrefix}recovery-test`;
      
      // Normal operation
      await service.set(testKey, 'before-error');
      const before = await service.get(testKey);
      expect(before).toBe('before-error');
      
      // Service should continue working
      await service.set(testKey, 'after-recovery');
      const after = await service.get(testKey);
      expect(after).toBe('after-recovery');
    });
  });

  describe('performance characteristics', () => {
    it('should complete operations within reasonable time', async () => {
      const testKey = `${testKeyPrefix}perf-test`;
      const value = { data: 'performance test' };
      
      // Set operation
      const setStart = Date.now();
      await service.set(testKey, value);
      const setDuration = Date.now() - setStart;
      
      // Get operation  
      const getStart = Date.now();
      await service.get(testKey);
      const getDuration = Date.now() - getStart;
      
      // Operations should be reasonably fast (< 100ms for Upstash)
      expect(setDuration).toBeLessThan(100);
      expect(getDuration).toBeLessThan(100);
    });
  });
});