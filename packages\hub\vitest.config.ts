import { defineConfig } from 'vitest/config';
import { resolve } from 'path';

/**
 * Runtime-accurate test configuration with enhanced debugging
 * This configuration mirrors production module resolution exactly
 * and provides maximum debugging capability for development
 */
export default defineConfig({
  test: {
    globals: true,
    environment: 'node',
    setupFiles: ['./test/setup.ts'],
    testTimeout: 30000,
    hookTimeout: 30000,
    
    // Enhanced reporters for better debugging
    reporters: process.env.CI === 'true' 
      ? ['github-actions', 'json'] 
      : ['verbose', 'hanging-process'],
    
    // Better error output
    outputFile: {
      json: './test-results.json',
      html: './test-results.html',
    },
    
    // Performance optimizations
    pool: 'forks',
    poolOptions: {
      forks: {
        maxForks: process.env.CI === 'true' ? 1 : 2,
        minForks: 1,
      },
    },
    
    // Enhanced debugging options
    isolate: true,
    bail: process.env.CI === 'true' ? 1 : 0, // Stop on first failure in CI
    logHeapUsage: true, // Show memory usage
    allowOnly: process.env.CI !== 'true', // Allow .only in dev
    
    // Better stack traces
    onStackTrace: (error, { file }) => {
      // Highlight module resolution errors
      if (error.message?.includes('Cannot find module') || 
          error.message?.includes('Cannot find package')) {
        console.error('\n❌ MODULE RESOLUTION ERROR:');
        console.error(`   File: ${file}`);
        console.error(`   Error: ${error.message}`);
        console.error('\n   💡 Hint: Use package root imports:');
        console.error('      ✅ import { something } from "@travelviz/shared"');
        console.error('      ❌ import { something } from "@travelviz/shared/utils/something"\n');
      }
    },
    
    // Coverage configuration
    coverage: {
      reporter: ['text', 'json', 'html', 'lcov'],
      provider: 'v8',
      all: false,
      include: ['src/**/*.{ts,tsx}'],
      exclude: [
        'src/**/*.d.ts',
        'src/**/*.test.{ts,tsx}',
        'src/**/*.spec.{ts,tsx}',
        'src/**/__tests__/**',
        'src/types/**',
        'node_modules/**',
      ],
      thresholds: {
        branches: 80,
        functions: 80,
        lines: 80,
        statements: 80,
      },
    },
  },
  
  resolve: {
    alias: {
      // ONLY internal aliases - this ensures runtime-accurate module resolution
      '@': resolve(__dirname, './src'),
      // NO external package aliases - must resolve through node_modules
    },
    
    // Enhanced module resolution debugging
    extensions: ['.ts', '.tsx', '.js', '.jsx', '.json'],
    conditions: ['node', 'import', 'module', 'require'],
  },
  
  // Enhanced error formatting
  esbuild: {
    target: 'node18',
    format: 'esm',
    sourcemap: true,
    // Better error messages
    logLevel: 'error',
    logOverride: {
      'unsupported-dynamic-import': 'silent', // Allow dynamic imports in test utils
    },
  },
  
  // Server configuration for better debugging
  server: {
    deps: {
      // Inline all @travelviz/* packages to catch resolution errors
      inline: [/@travelviz\/.*/],
      // Force module resolution through Node.js
      external: ['*'],
      fallbackCJS: true,
    },
  },
});