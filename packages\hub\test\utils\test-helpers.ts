import { vi } from 'vitest';

/**
 * Test utilities for common mocking patterns
 */

export const createMockCircuitBreaker = () => ({
  execute: vi.fn(),
  getState: vi.fn().mockReturnValue('CLOSED'),
  reset: vi.fn(),
  getMetrics: vi.fn().mockReturnValue({
    state: 'CLOSED',
    failures: 0,
    lastFailureTime: 0,
    nextAttemptTime: 0
  })
});

export const createMockSSEResponse = () => ({
  writeHead: vi.fn(),
  write: vi.fn(),
  end: vi.fn(),
  on: vi.fn(),
  headersSent: false,
  finished: false
});

export const advanceTimeAndFlushPromises = async (ms: number) => {
  vi.advanceTimersByTime(ms);
  await new Promise(resolve => setImmediate(resolve));
};

export const createMockRedisClient = () => ({
  get: vi.fn().mockResolvedValue(null),
  set: vi.fn().mockResolvedValue('OK'),
  del: vi.fn().mockResolvedValue(1),
  expire: vi.fn().mockResolvedValue(1),
  publish: vi.fn().mockResolvedValue(1),
  subscribe: vi.fn().mockResolvedValue(undefined),
  unsubscribe: vi.fn().mockResolvedValue(undefined),
  on: vi.fn(),
  quit: vi.fn().mockResolvedValue(undefined)
});

export const createMockParseSession = (overrides = {}) => ({
  id: 'test-session-id',
  status: 'processing',
  progress: 50,
  currentStep: 'parsing',
  result: undefined,
  error: undefined,
  createdAt: new Date().toISOString(),
  updatedAt: new Date().toISOString(),
  ...overrides
});

export const createMockParsedTrip = (overrides = {}) => ({
  title: 'Test Trip to Paris',
  destination: 'Paris, France',
  startDate: '2024-01-01',
  endDate: '2024-01-07',
  activities: [
    {
      title: 'Visit Eiffel Tower',
      type: 'activity' as const,
      day: 1,
      startTime: '09:00 AM',
      location: 'Eiffel Tower, Paris',
      description: 'Morning visit to the iconic tower'
    },
    {
      title: 'Louvre Museum',
      type: 'activity' as const,
      day: 2,
      startTime: '10:00 AM',
      location: 'Louvre Museum, Paris',
      description: 'Explore art collections'
    }
  ],
  ...overrides
});

export const createMockAuthRequest = (userId = 'test-user-123') => ({
  user: { id: userId },
  headers: {
    authorization: 'Bearer test-token'
  },
  body: {},
  params: {},
  query: {},
  on: vi.fn()
});

export const waitForAsync = async (ms: number = 100) => {
  await new Promise(resolve => setTimeout(resolve, ms));
};

export const mockApiResponse = (data: any, status = 200) => ({
  data,
  status,
  statusText: status === 200 ? 'OK' : 'Error',
  headers: {},
  config: {}
});

export const createCircuitBreakerError = (state: string) => 
  new Error(`Circuit breaker is ${state} - operation temporarily disabled`);

export const createRateLimitError = () => 
  new Error('Rate limit exceeded');

export const createValidationError = (message: string) => {
  const error = new Error(message);
  error.name = 'ValidationError';
  return error;
};