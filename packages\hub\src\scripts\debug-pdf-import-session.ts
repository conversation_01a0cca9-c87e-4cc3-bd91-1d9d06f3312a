#!/usr/bin/env tsx

/**
 * PDF Import Session Debugging Script
 * Target Session: 2479b9e7-4336-4b9c-9447-78f747ae26be
 * 
 * Usage: npx tsx src/scripts/debug-pdf-import-session.ts
 */

import { pdfImportDebugger, DebugPhase } from '../utils/debug-pdf-import';
import { logger } from '../utils/logger';

async function main() {
  console.log('🔍 Starting PDF Import Session Debugging');
  console.log('Target Session: 2479b9e7-4336-4b9c-9447-78f747ae26be');
  console.log('=' .repeat(60));

  try {
    // Phase 1: Session State Verification
    console.log('\n📊 Phase 1: Session State Verification');
    console.log('-'.repeat(40));
    
    const sessionInvestigation = await pdfImportDebugger.investigateSessionState();
    
    console.log('✅ Session State Investigation Results:');
    console.log(`   Status: ${sessionInvestigation.actualState.status}`);
    console.log(`   Created: ${sessionInvestigation.actualState.createdAt.toISOString()}`);
    console.log(`   Updated: ${sessionInvestigation.actualState.updatedAt?.toISOString() || 'N/A'}`);
    console.log(`   Has Parsed Data: ${!!sessionInvestigation.actualState.parsedData}`);
    console.log(`   Error Message: ${sessionInvestigation.actualState.errorMessage || 'None'}`);
    console.log(`   Discrepancies: ${sessionInvestigation.discrepancies.length}`);
    
    if (sessionInvestigation.discrepancies.length > 0) {
      console.log('\n⚠️  State Discrepancies Found:');
      sessionInvestigation.discrepancies.forEach((discrepancy, index) => {
        console.log(`   ${index + 1}. ${discrepancy}`);
      });
    }

    // Calculate session age
    const sessionAge = Date.now() - sessionInvestigation.actualState.createdAt.getTime();
    const sessionAgeMinutes = Math.round(sessionAge / 60000);
    console.log(`\n⏱️  Session Age: ${sessionAgeMinutes} minutes`);
    
    if (sessionAgeMinutes > 5 && sessionInvestigation.actualState.status === 'processing') {
      console.log('🚨 CRITICAL: Session is orphaned (processing > 5 minutes)');
    }

    // Phase 2: Generate Evidence Report
    console.log('\n📋 Generating Evidence Report');
    console.log('-'.repeat(40));
    
    const evidence = pdfImportDebugger.getEvidence();
    const report = pdfImportDebugger.generateReport();
    
    console.log('Evidence Summary:');
    console.log(`   Total Phases Investigated: ${evidence.length}`);
    console.log(`   Total Findings: ${evidence.reduce((sum, e) => sum + e.findings.length, 0)}`);
    console.log(`   Critical Issues: ${evidence.reduce((sum, e) => sum + e.findings.filter(f => f.severity === 'critical').length, 0)}`);
    console.log(`   Warning Issues: ${evidence.reduce((sum, e) => sum + e.findings.filter(f => f.severity === 'warning').length, 0)}`);

    // Display detailed findings
    console.log('\n🔍 Detailed Findings:');
    evidence.forEach((evidenceItem, phaseIndex) => {
      console.log(`\n   Phase ${phaseIndex + 1}: ${evidenceItem.phase}`);
      evidenceItem.findings.forEach((finding, findingIndex) => {
        const icon = finding.severity === 'critical' ? '🚨' : finding.severity === 'warning' ? '⚠️' : 'ℹ️';
        console.log(`     ${icon} ${finding.description}`);
        if (finding.evidence && typeof finding.evidence === 'object') {
          console.log(`        Evidence: ${JSON.stringify(finding.evidence, null, 8)}`);
        }
      });
    });

    // Next Steps Recommendation
    console.log('\n🎯 Next Steps Recommendation:');
    console.log('-'.repeat(40));
    
    const criticalIssues = evidence.reduce((sum, e) => sum + e.findings.filter(f => f.severity === 'critical').length, 0);
    
    if (criticalIssues > 0) {
      console.log('❌ Critical issues found. Recommended actions:');
      
      if (sessionInvestigation.actualState.status === 'processing' && sessionAgeMinutes > 5) {
        console.log('   1. Mark orphaned session as failed');
        console.log('   2. Investigate AI processing timeout issues');
        console.log('   3. Implement better error handling in parseAsync()');
      }
      
      if (sessionInvestigation.discrepancies.length > 0) {
        console.log('   4. Fix state transition inconsistencies');
        console.log('   5. Add atomic status update mechanisms');
      }
    } else {
      console.log('✅ No critical issues found. Proceed to next debugging phase.');
    }

    console.log('\n📄 Full Report:');
    console.log(report);

  } catch (error) {
    console.error('❌ Debugging failed:', error);
    logger.error('PDF import debugging failed', { error });
    process.exit(1);
  }
}

// Run the debugging script
if (require.main === module) {
  main().catch(console.error);
}

export { main as debugPDFImportSession };