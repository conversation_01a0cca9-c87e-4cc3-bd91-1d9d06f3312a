import { describe, it, expect, beforeAll, afterAll } from 'vitest';
import request from 'supertest';
import { getSupabaseClient } from '../../../src/lib/supabase';
import { createServer } from '../../../src/server';
import { Express } from 'express';
import path from 'path';
import { randomUUID } from 'crypto';

// Load environment variables from .env.local
import dotenv from 'dotenv';
dotenv.config({ path: path.join(__dirname, '../../../../.env.local') });

const TEST_USER_EMAIL = '<EMAIL>';
const TEST_USER_PASSWORD = 'Flaremmk123!';

/**
 * Performance and Load Testing
 * Tests system performance under concurrent load and measures response times
 */
describe('Performance and Load Testing', () => {
  let app: Express;
  let server: any;
  let authToken: string;
  let testTripId: string;

  beforeAll(async () => {
    console.log('🚀 Starting performance and load tests...');
    app = createServer();
    server = app.listen(0);

    // Get auth token
    const supabase = getSupabaseClient();
    const { data, error } = await supabase.auth.signInWithPassword({
      email: TEST_USER_EMAIL,
      password: TEST_USER_PASSWORD
    });

    if (error) {
      throw new Error(`Failed to authenticate: ${error.message}`);
    }

    authToken = data.session?.access_token || '';

    // Create a test trip for performance testing
    const tripResponse = await request(app)
      .post('/api/v1/trips')
      .set('Authorization', `Bearer ${authToken}`)
      .send({
        title: 'Performance Test Trip',
        description: 'Trip for load testing',
        visibility: 'private',
        start_date: '2024-08-01',
        end_date: '2024-08-07',
        destination: 'Performance City'
      });

    testTripId = tripResponse.body.data.trip.id;
    console.log('✅ Performance test environment ready');
  });

  afterAll(async () => {
    // Cleanup test trip
    if (testTripId) {
      await request(app)
        .delete(`/api/v1/trips/${testTripId}`)
        .set('Authorization', `Bearer ${authToken}`);
    }
    server?.close();
  });

  describe('Response Time Benchmarks', () => {
    it('should respond to GET /trips within performance threshold', async () => {
      const startTime = Date.now();
      
      const response = await request(app)
        .get('/api/v1/trips')
        .set('Authorization', `Bearer ${authToken}`);

      const responseTime = Date.now() - startTime;

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(responseTime).toBeLessThan(2000); // 2 second threshold
      
      console.log(`✅ GET /trips response time: ${responseTime}ms`);
    });

    it('should respond to GET specific trip within performance threshold', async () => {
      const startTime = Date.now();
      
      const response = await request(app)
        .get(`/api/v1/trips/${testTripId}`)
        .set('Authorization', `Bearer ${authToken}`);

      const responseTime = Date.now() - startTime;

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(responseTime).toBeLessThan(1500); // 1.5 second threshold
      
      console.log(`✅ GET /trips/:id response time: ${responseTime}ms`);
    });

    it('should respond to trip creation within performance threshold', async () => {
      const startTime = Date.now();
      
      const response = await request(app)
        .post('/api/v1/trips')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          title: `Performance Test Trip ${randomUUID().slice(0, 8)}`,
          description: 'Testing trip creation performance',
          visibility: 'private',
          start_date: '2024-09-01',
          end_date: '2024-09-07'
        });

      const responseTime = Date.now() - startTime;
      const createdTripId = response.body.data.trip.id;

      expect(response.status).toBe(201);
      expect(response.body.success).toBe(true);
      expect(responseTime).toBeLessThan(3000); // 3 second threshold for creation
      
      // Cleanup
      await request(app)
        .delete(`/api/v1/trips/${createdTripId}`)
        .set('Authorization', `Bearer ${authToken}`);
      
      console.log(`✅ POST /trips response time: ${responseTime}ms`);
    });

    it('should respond to health check within performance threshold', async () => {
      const startTime = Date.now();
      
      const response = await request(app)
        .get('/api/v1/health');

      const responseTime = Date.now() - startTime;

      expect(response.status).toBe(200);
      expect(responseTime).toBeLessThan(500); // 500ms threshold for health check
      
      console.log(`✅ GET /health response time: ${responseTime}ms`);
    });
  });

  describe('Concurrent Load Testing', () => {
    it('should handle 10 concurrent trip list requests', async () => {
      console.log('🔄 Testing 10 concurrent GET /trips requests...');
      
      const startTime = Date.now();
      const concurrentRequests = Array(10).fill(null).map(() =>
        request(app)
          .get('/api/v1/trips')
          .set('Authorization', `Bearer ${authToken}`)
      );

      const responses = await Promise.all(concurrentRequests);
      const totalTime = Date.now() - startTime;

      // All requests should succeed
      responses.forEach((response, index) => {
        expect(response.status).toBe(200);
        expect(response.body.success).toBe(true);
      });

      // Total time should be reasonable (not much slower than sequential)
      expect(totalTime).toBeLessThan(15000); // 15 seconds for 10 concurrent requests
      
      const avgResponseTime = totalTime / responses.length;
      console.log(`✅ 10 concurrent requests completed in ${totalTime}ms (avg: ${avgResponseTime}ms)`);
    });

    it('should handle 5 concurrent trip creation requests', async () => {
      console.log('🔄 Testing 5 concurrent POST /trips requests...');
      
      const startTime = Date.now();
      const concurrentRequests = Array(5).fill(null).map((_, index) =>
        request(app)
          .post('/api/v1/trips')
          .set('Authorization', `Bearer ${authToken}`)
          .send({
            title: `Concurrent Test Trip ${index + 1} ${randomUUID().slice(0, 4)}`,
            description: `Testing concurrent creation ${index + 1}`,
            visibility: 'private',
            start_date: '2024-10-01',
            end_date: '2024-10-07'
          })
      );

      const responses = await Promise.all(concurrentRequests);
      const totalTime = Date.now() - startTime;

      // All requests should succeed
      const createdTripIds: string[] = [];
      responses.forEach((response, index) => {
        expect(response.status).toBe(201);
        expect(response.body.success).toBe(true);
        createdTripIds.push(response.body.data.trip.id);
      });

      // Cleanup created trips
      const cleanupPromises = createdTripIds.map(id =>
        request(app)
          .delete(`/api/v1/trips/${id}`)
          .set('Authorization', `Bearer ${authToken}`)
      );
      await Promise.all(cleanupPromises);

      expect(totalTime).toBeLessThan(20000); // 20 seconds for 5 concurrent creations
      
      const avgResponseTime = totalTime / responses.length;
      console.log(`✅ 5 concurrent trip creations completed in ${totalTime}ms (avg: ${avgResponseTime}ms)`);
    });

    it('should handle mixed concurrent operations', async () => {
      console.log('🔄 Testing mixed concurrent operations...');
      
      const startTime = Date.now();
      
      // Mix of different operations
      const mixedRequests = [
        // Read operations
        request(app).get('/api/v1/trips').set('Authorization', `Bearer ${authToken}`),
        request(app).get(`/api/v1/trips/${testTripId}`).set('Authorization', `Bearer ${authToken}`),
        request(app).get('/api/v1/health'),
        
        // Write operations
        request(app)
          .post('/api/v1/trips')
          .set('Authorization', `Bearer ${authToken}`)
          .send({
            title: `Mixed Test Trip ${randomUUID().slice(0, 4)}`,
            description: 'Mixed operation test',
            visibility: 'private',
            start_date: '2024-11-01',
            end_date: '2024-11-07'
          }),
        
        // Update operation
        request(app)
          .put(`/api/v1/trips/${testTripId}`)
          .set('Authorization', `Bearer ${authToken}`)
          .send({
            description: 'Updated during load test'
          })
      ];

      const responses = await Promise.all(mixedRequests);
      const totalTime = Date.now() - startTime;

      // Check each response type
      expect(responses[0].status).toBe(200); // GET trips
      expect(responses[1].status).toBe(200); // GET specific trip
      expect(responses[2].status).toBe(200); // Health check
      expect(responses[3].status).toBe(201); // POST trip
      expect(responses[4].status).toBe(200); // PUT trip

      // Cleanup created trip
      const createdTripId = responses[3].body.data.trip.id;
      await request(app)
        .delete(`/api/v1/trips/${createdTripId}`)
        .set('Authorization', `Bearer ${authToken}`);

      expect(totalTime).toBeLessThan(15000); // 15 seconds for mixed operations
      
      console.log(`✅ Mixed concurrent operations completed in ${totalTime}ms`);
    });
  });

  describe('AI Import Performance', () => {
    it('should handle AI import within performance threshold', async () => {
      const conversation = `User: Plan a 2-day trip to Tokyo.
Assistant: Here's your Tokyo itinerary:

Day 1:
- Morning: Visit Senso-ji Temple in Asakusa
- Afternoon: Explore Shibuya and Harajuku
- Evening: Dinner in Ginza

Day 2:
- Morning: Trip to Meiji Shrine
- Afternoon: Tokyo National Museum
- Evening: Observation deck at Tokyo Skytree`;

      console.log('🤖 Testing AI import performance...');
      const startTime = Date.now();

      const response = await request(app)
        .post('/api/v1/import/parse-simple')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          content: conversation,
          source: 'chatgpt'
        });

      const responseTime = Date.now() - startTime;

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveProperty('importId');
      expect(responseTime).toBeLessThan(10000); // 10 second threshold for AI import

      console.log(`✅ AI import response time: ${responseTime}ms`);
    });

    it('should handle 3 concurrent AI import requests', async () => {
      const conversations = [
        `User: Quick weekend in San Francisco.
Assistant: SF weekend:
Day 1: Golden Gate Bridge, Fisherman's Wharf
Day 2: Alcatraz, Union Square`,
        
        `User: Day trip to Napa Valley.
Assistant: Napa Valley day:
Morning: Winery tour at Castello di Amorosa
Afternoon: Lunch at Oxbow Public Market
Evening: Sunset at Auberge du Soleil`,
        
        `User: Family trip to Disneyland.
Assistant: Disneyland family trip:
Day 1: Magic Kingdom - Space Mountain, Pirates
Day 2: California Adventure - Cars Land, Marvel Campus`
      ];

      console.log('🤖 Testing 3 concurrent AI imports...');
      const startTime = Date.now();

      const concurrentImports = conversations.map(conversation =>
        request(app)
          .post('/api/v1/import/parse-simple')
          .set('Authorization', `Bearer ${authToken}`)
          .send({
            content: conversation,
            source: 'chatgpt'
          })
      );

      const responses = await Promise.all(concurrentImports);
      const totalTime = Date.now() - startTime;

      // All imports should succeed
      responses.forEach((response, index) => {
        expect(response.status).toBe(200);
        expect(response.body.success).toBe(true);
        expect(response.body.data).toHaveProperty('importId');
      });

      expect(totalTime).toBeLessThan(30000); // 30 seconds for 3 concurrent AI imports
      
      const avgResponseTime = totalTime / responses.length;
      console.log(`✅ 3 concurrent AI imports completed in ${totalTime}ms (avg: ${avgResponseTime}ms)`);
    });
  });

  describe('Database Performance', () => {
    it('should handle database connection pooling efficiently', async () => {
      console.log('🗄️ Testing database connection pooling...');
      
      // Create multiple rapid database requests
      const dbRequests = Array(20).fill(null).map(() =>
        request(app)
          .get('/api/v1/trips')
          .set('Authorization', `Bearer ${authToken}`)
      );

      const startTime = Date.now();
      const responses = await Promise.all(dbRequests);
      const totalTime = Date.now() - startTime;

      // All requests should succeed
      responses.forEach(response => {
        expect(response.status).toBe(200);
        expect(response.body.success).toBe(true);
      });

      // Should handle 20 concurrent DB requests efficiently
      expect(totalTime).toBeLessThan(10000); // 10 seconds for 20 requests
      
      console.log(`✅ 20 concurrent DB requests completed in ${totalTime}ms`);
    });

    it('should maintain performance under sustained load', async () => {
      console.log('🔄 Testing sustained load performance...');
      
      const sustainedRequests = [];
      const requestInterval = 100; // 100ms between request batches
      const batchSize = 3;
      const totalBatches = 5;

      for (let batch = 0; batch < totalBatches; batch++) {
        // Wait between batches to simulate sustained load
        if (batch > 0) {
          await new Promise(resolve => setTimeout(resolve, requestInterval));
        }

        const batchRequests = Array(batchSize).fill(null).map(() =>
          request(app)
            .get('/api/v1/trips')
            .set('Authorization', `Bearer ${authToken}`)
        );

        sustainedRequests.push(...batchRequests);
      }

      const startTime = Date.now();
      const responses = await Promise.all(sustainedRequests);
      const totalTime = Date.now() - startTime;

      // All requests should succeed
      responses.forEach(response => {
        expect(response.status).toBe(200);
        expect(response.body.success).toBe(true);
      });

      const totalRequests = totalBatches * batchSize;
      const avgResponseTime = totalTime / totalRequests;
      
      console.log(`✅ Sustained load: ${totalRequests} requests in ${totalTime}ms (avg: ${avgResponseTime}ms)`);
      
      // Performance should not degrade significantly under sustained load
      expect(avgResponseTime).toBeLessThan(3000); // 3 second average
    });
  });

  describe('Memory and Resource Usage', () => {
    it('should not leak memory during repeated operations', async () => {
      console.log('🧠 Testing memory usage during repeated operations...');
      
      const initialMemory = process.memoryUsage();
      
      // Perform many operations
      for (let i = 0; i < 50; i++) {
        await request(app)
          .get('/api/v1/health');
        
        // Occasional garbage collection hint
        if (i % 10 === 0 && global.gc) {
          global.gc();
        }
      }

      const finalMemory = process.memoryUsage();
      
      // Memory should not increase dramatically
      const heapIncrease = finalMemory.heapUsed - initialMemory.heapUsed;
      const heapIncreaseRatio = heapIncrease / initialMemory.heapUsed;
      
      console.log(`Memory usage - Initial: ${Math.round(initialMemory.heapUsed / 1024 / 1024)}MB, Final: ${Math.round(finalMemory.heapUsed / 1024 / 1024)}MB`);
      console.log(`Heap increase: ${Math.round(heapIncrease / 1024 / 1024)}MB (${Math.round(heapIncreaseRatio * 100)}%)`);
      
      // Heap should not increase by more than 50% during normal operations
      expect(heapIncreaseRatio).toBeLessThan(0.5);
      
      console.log('✅ Memory usage within acceptable limits');
    });
  });

  describe('Error Rate Under Load', () => {
    it('should maintain low error rate under concurrent load', async () => {
      console.log('📊 Testing error rate under load...');
      
      const totalRequests = 25;
      const concurrentRequests = Array(totalRequests).fill(null).map((_, index) => {
        // Mix of valid and edge-case requests
        if (index % 5 === 0) {
          // Test with non-existent trip ID occasionally
          return request(app)
            .get('/api/v1/trips/non-existent-id-12345')
            .set('Authorization', `Bearer ${authToken}`);
        } else {
          // Normal requests
          return request(app)
            .get('/api/v1/trips')
            .set('Authorization', `Bearer ${authToken}`);
        }
      });

      const responses = await Promise.all(concurrentRequests);
      
      const successfulResponses = responses.filter(r => r.status === 200).length;
      const notFoundResponses = responses.filter(r => r.status === 404).length;
      const errorResponses = responses.filter(r => r.status >= 500).length;
      
      const successRate = successfulResponses / totalRequests;
      const errorRate = errorResponses / totalRequests;
      
      console.log(`Success rate: ${Math.round(successRate * 100)}%`);
      console.log(`Not found rate: ${Math.round(notFoundResponses / totalRequests * 100)}%`);
      console.log(`Error rate: ${Math.round(errorRate * 100)}%`);
      
      // Error rate should be very low (< 5%)
      expect(errorRate).toBeLessThan(0.05);
      
      // Success rate for valid requests should be high
      const validRequests = totalRequests - Math.floor(totalRequests / 5); // Subtract non-existent ID requests
      const validSuccessRate = successfulResponses / validRequests;
      expect(validSuccessRate).toBeGreaterThan(0.95); // 95% success rate for valid requests
      
      console.log('✅ Error rate within acceptable limits under load');
    });
  });
});