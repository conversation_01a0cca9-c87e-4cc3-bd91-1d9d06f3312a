'use client';

import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { ReactQueryDevtools } from '@tanstack/react-query-devtools';
import { useState } from 'react';

interface QueryProviderProps {
  children: React.ReactNode;
}

function makeQueryClient() {
  return new QueryClient({
    defaultOptions: {
      queries: {
        // With SSR, we usually want to set some default staleTime
        // above 0 to avoid refetching immediately on the client
        staleTime: 60 * 1000, // 1 minute
        gcTime: 5 * 60 * 1000, // 5 minutes (garbage collection time)
        retry: (failureCount, error) => {
          // Don't retry on 4xx errors except for 429 (rate limit)
          if ((error as any)?.status >= 400 && (error as any)?.status < 500) {
            // Special handling for rate limit errors - don't retry to avoid making the problem worse
            if ((error as any)?.status === 429) {
              return false;
            }
            return false;
          }
          // Retry up to 3 times for other errors
          return failureCount < 3;
        },
        retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
        refetchOnWindowFocus: false, // Disable refetch on window focus for better UX
        refetchOnReconnect: true, // Refetch on network reconnection
      },
      mutations: {
        retry: (failureCount, error) => {
          // Don't retry mutations on 4xx errors, especially 429 (rate limit)
          if ((error as any)?.status >= 400 && (error as any)?.status < 500) {
            return false;
          }
          // Retry up to 2 times for network errors
          return failureCount < 2;
        },
        retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 10000),
      },
    },
  });
}

let browserQueryClient: QueryClient | undefined = undefined;

function getQueryClient() {
  if (typeof window === 'undefined') {
    // Server: always make a new query client
    return makeQueryClient();
  } else {
    // Browser: make a new query client if we don't already have one
    // This is very important, so we don't re-make a new client if React
    // suspends during the initial render. This may not be needed if we
    // have a suspense boundary BELOW the creation of the query client
    if (!browserQueryClient) browserQueryClient = makeQueryClient();
    return browserQueryClient;
  }
}

export function QueryProvider({ children }: QueryProviderProps) {
  // NOTE: Avoid useState when initializing the query client if you don't
  //       have a suspense boundary between this and the code that may
  //       suspend because React will throw away the client on the initial
  //       render if it suspends and there is no boundary
  const [queryClient] = useState(() => getQueryClient());

  return (
    <QueryClientProvider client={queryClient}>
      {children}
      {process.env.NODE_ENV === 'development' && (
        <ReactQueryDevtools 
          initialIsOpen={false} 
          position={"bottom-right" as any}
        />
      )}
    </QueryClientProvider>
  );
}

// Export pre-configured query keys for consistency
export const queryKeys = {
  trips: {
    all: ['trips'] as const,
    lists: () => [...queryKeys.trips.all, 'list'] as const,
    list: (filters: string) => [...queryKeys.trips.lists(), { filters }] as const,
    details: () => [...queryKeys.trips.all, 'detail'] as const,
    detail: (id: string) => [...queryKeys.trips.details(), id] as const,
    activities: (tripId: string) => [...queryKeys.trips.detail(tripId), 'activities'] as const,
  },
  user: {
    profile: ['user', 'profile'] as const,
    preferences: ['user', 'preferences'] as const,
  },
  search: {
    trips: (query: string) => ['search', 'trips', query] as const,
    places: (query: string) => ['search', 'places', query] as const,
  },
} as const;

// Helper function to get fresh query client (useful for server actions)
export function getFreshQueryClient() {
  return makeQueryClient();
}