'use client';

import React, { useState, useMemo, useRef, useCallback } from 'react';
import { useVirtualizer } from '@tanstack/react-virtual';
import { DndContext, closestCenter, KeyboardSensor, PointerSensor, useSensor, useSensors, DragEndEvent } from '@dnd-kit/core';
import { arrayMove, SortableContext, sortableKeyboardCoordinates, verticalListSortingStrategy } from '@dnd-kit/sortable';
import { useSortable } from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { motion, AnimatePresence } from 'framer-motion';
import { format, startOfDay, differenceInMinutes } from 'date-fns';
import { cn } from '@/lib/utils';
import { Activity, ActivityType } from '@travelviz/shared';
import { 
  Plane, 
  Hotel, 
  MapPin, 
  Car, 
  Coffee, 
  ShoppingBag, 
  Activity as ActivityIcon,
  ChevronDown,
  ChevronRight,
  DollarSign,
  Clock,
  GripVertical
} from 'lucide-react';

interface VirtualTimelineProps {
  activities: Activity[];
  onActivityUpdate?: (activityId: string, updates: Partial<Activity>) => void;
  onActivityReorder?: (newOrder: Activity[]) => void;
  onActivityClick?: (activity: Activity) => void;
  className?: string;
}

interface DayGroup {
  date: Date;
  dayNumber: number;
  activities: Activity[];
  totalPrice: number;
}

const activityIcons: Record<ActivityType, React.ReactNode> = {
  [ActivityType.flight]: <Plane className="w-4 h-4" />,
  [ActivityType.accommodation]: <Hotel className="w-4 h-4" />,
  [ActivityType.activity]: <MapPin className="w-4 h-4" />,
  [ActivityType.transport]: <Car className="w-4 h-4" />,
  [ActivityType.dining]: <Coffee className="w-4 h-4" />,
  [ActivityType.shopping]: <ShoppingBag className="w-4 h-4" />,
  [ActivityType.car_rental]: <Car className="w-4 h-4" />,
  [ActivityType.tour]: <MapPin className="w-4 h-4" />,
  [ActivityType.sightseeing]: <MapPin className="w-4 h-4" />,
  [ActivityType.entertainment]: <ActivityIcon className="w-4 h-4" />,
  [ActivityType.other]: <ActivityIcon className="w-4 h-4" />,
};

const activityColors: Record<ActivityType, string> = {
  [ActivityType.flight]: 'bg-blue-100 text-blue-700 border-blue-200',
  [ActivityType.accommodation]: 'bg-purple-100 text-purple-700 border-purple-200',
  [ActivityType.activity]: 'bg-green-100 text-green-700 border-green-200',
  [ActivityType.transport]: 'bg-orange-100 text-orange-700 border-orange-200',
  [ActivityType.dining]: 'bg-pink-100 text-pink-700 border-pink-200',
  [ActivityType.shopping]: 'bg-indigo-100 text-indigo-700 border-indigo-200',
  [ActivityType.car_rental]: 'bg-orange-100 text-orange-700 border-orange-200',
  [ActivityType.tour]: 'bg-green-100 text-green-700 border-green-200',
  [ActivityType.sightseeing]: 'bg-green-100 text-green-700 border-green-200',
  [ActivityType.entertainment]: 'bg-pink-100 text-pink-700 border-pink-200',
  [ActivityType.other]: 'bg-gray-100 text-gray-700 border-gray-200',
};

interface TimelineItemProps {
  activity: Activity;
  isDragging?: boolean;
  onClick?: () => void;
}

const TimelineItem = React.forwardRef<HTMLDivElement, TimelineItemProps>(
  ({ activity, isDragging, onClick }, ref) => {
    const startTime = activity.start_time ? new Date(activity.start_time) : null;
    const endTime = activity.end_time ? new Date(activity.end_time) : null;
    const duration = startTime && endTime 
      ? differenceInMinutes(endTime, startTime) 
      : null;

    return (
      <div
        ref={ref}
        onClick={onClick}
        className={cn(
          "relative flex items-start gap-4 p-4 bg-white rounded-lg border transition-all cursor-pointer hover:shadow-md",
          activityColors[activity.type],
          isDragging && "opacity-50"
        )}
      >
        {/* Drag Handle */}
        <div className="absolute left-0 top-1/2 -translate-y-1/2 -translate-x-1/2 opacity-0 hover:opacity-100 transition-opacity cursor-grab active:cursor-grabbing">
          <GripVertical className="w-5 h-5 text-gray-400" />
        </div>

        {/* Timeline Line */}
        <div className="absolute left-8 top-0 bottom-0 w-0.5 bg-gray-200 -z-10" />
        
        {/* Icon */}
        <div className="flex-shrink-0 w-8 h-8 rounded-full bg-white shadow-sm flex items-center justify-center z-10">
          {activityIcons[activity.type]}
        </div>

        {/* Content */}
        <div className="flex-1 min-w-0">
          <h4 className="font-medium text-gray-900 truncate">{activity.title}</h4>
          
          {activity.description && (
            <p className="text-sm text-gray-600 mt-1 line-clamp-2">
              {activity.description}
            </p>
          )}

          <div className="flex items-center gap-4 mt-2 text-xs text-gray-500">
            {startTime && (
              <div className="flex items-center gap-1">
                <Clock className="w-3 h-3" />
                <span>{format(startTime, 'HH:mm')}</span>
                {duration && <span className="text-gray-400">({duration} min)</span>}
              </div>
            )}
            
            {activity.price && (
              <div className="flex items-center gap-1">
                <DollarSign className="w-3 h-3" />
                <span>
                  {activity.price} {activity.currency || 'USD'}
                </span>
              </div>
            )}

            {activity.location && (
              <div className="flex items-center gap-1 truncate">
                <MapPin className="w-3 h-3" />
                <span className="truncate">{activity.location}</span>
              </div>
            )}
          </div>
        </div>
      </div>
    );
  }
);

TimelineItem.displayName = 'TimelineItem';

interface SortableTimelineItemProps {
  activity: Activity;
  onClick?: () => void;
}

function SortableTimelineItem({ activity, onClick }: SortableTimelineItemProps) {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({ id: activity.id });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
  };

  return (
    <div style={style} {...attributes} {...listeners}>
      <TimelineItem
        ref={setNodeRef}
        activity={activity}
        isDragging={isDragging}
        onClick={onClick}
      />
    </div>
  );
}

export function VirtualTimeline({
  activities,
  onActivityUpdate,
  onActivityReorder,
  onActivityClick,
  className,
}: VirtualTimelineProps) {
  const parentRef = useRef<HTMLDivElement>(null);
  const [expandedDays, setExpandedDays] = useState<Set<number>>(new Set());
  const [items, setItems] = useState(activities);

  const sensors = useSensors(
    useSensor(PointerSensor),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );

  // Group activities by day
  const dayGroups = useMemo(() => {
    const groups = new Map<string, DayGroup>();
    
    items.forEach((activity) => {
      const date = activity.start_time 
        ? startOfDay(new Date(activity.start_time))
        : new Date();
      const dateKey = format(date, 'yyyy-MM-dd');
      
      if (!groups.has(dateKey)) {
        groups.set(dateKey, {
          date,
          dayNumber: groups.size + 1, // Calculate based on number of days so far
          activities: [],
          totalPrice: 0,
        });
      }
      
      const group = groups.get(dateKey)!;
      group.activities.push(activity);
      if (activity.price) {
        group.totalPrice += activity.price;
      }
    });

    // Sort groups by date and activities by time
    return Array.from(groups.values())
      .sort((a, b) => a.date.getTime() - b.date.getTime())
      .map((group) => ({
        ...group,
        activities: group.activities.sort((a, b) => {
          const aTime = a.start_time ? new Date(a.start_time).getTime() : 0;
          const bTime = b.start_time ? new Date(b.start_time).getTime() : 0;
          return aTime - bTime;
        }),
      }));
  }, [items]);

  // Flatten items for virtualization
  const flatItems = useMemo(() => {
    const items: Array<{ type: 'day' | 'activity'; data: DayGroup | Activity }> = [];
    
    dayGroups.forEach((group) => {
      items.push({ type: 'day', data: group });
      if (expandedDays.has(group.dayNumber)) {
        group.activities.forEach((activity) => {
          items.push({ type: 'activity', data: activity });
        });
      }
    });
    
    return items;
  }, [dayGroups, expandedDays]);

  // Virtual list
  const virtualizer = useVirtualizer({
    count: flatItems.length,
    getScrollElement: () => parentRef.current,
    estimateSize: useCallback((index: number) => {
      return flatItems[index].type === 'day' ? 64 : 120;
    }, [flatItems]),
    overscan: 5,
  });

  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event;

    if (active.id !== over?.id) {
      setItems((items) => {
        const oldIndex = items.findIndex((item) => item.id === active.id);
        const newIndex = items.findIndex((item) => item.id === over?.id);
        const newOrder = arrayMove(items, oldIndex, newIndex);
        
        if (onActivityReorder) {
          onActivityReorder(newOrder);
        }
        
        return newOrder;
      });
    }
  };

  const toggleDay = (dayNumber: number) => {
    setExpandedDays((prev) => {
      const next = new Set(prev);
      if (next.has(dayNumber)) {
        next.delete(dayNumber);
      } else {
        next.add(dayNumber);
      }
      return next;
    });
  };

  return (
    <DndContext
      sensors={sensors}
      collisionDetection={closestCenter}
      onDragEnd={handleDragEnd}
    >
      <SortableContext
        items={items.map((item) => item.id)}
        strategy={verticalListSortingStrategy}
      >
        <div
          ref={parentRef}
          className={cn("h-full overflow-auto", className)}
        >
          <div
            style={{
              height: `${virtualizer.getTotalSize()}px`,
              width: '100%',
              position: 'relative',
            }}
          >
            <AnimatePresence>
              {virtualizer.getVirtualItems().map((virtualItem) => {
                const item = flatItems[virtualItem.index];
                
                if (item.type === 'day') {
                  const group = item.data as DayGroup;
                  const isExpanded = expandedDays.has(group.dayNumber);
                  
                  return (
                    <motion.div
                      key={`day-${group.dayNumber}`}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      exit={{ opacity: 0, y: -20 }}
                      style={{
                        position: 'absolute',
                        top: 0,
                        left: 0,
                        width: '100%',
                        height: `${virtualItem.size}px`,
                        transform: `translateY(${virtualItem.start}px)`,
                      }}
                    >
                      <div
                        onClick={() => toggleDay(group.dayNumber)}
                        className="sticky top-0 z-20 bg-gray-50 border-b border-gray-200 px-4 py-3 cursor-pointer hover:bg-gray-100 transition-colors"
                      >
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-2">
                            {isExpanded ? (
                              <ChevronDown className="w-4 h-4 text-gray-500" />
                            ) : (
                              <ChevronRight className="w-4 h-4 text-gray-500" />
                            )}
                            <h3 className="font-semibold text-gray-900">
                              Day {group.dayNumber}
                            </h3>
                            <span className="text-sm text-gray-600">
                              {format(group.date, 'EEEE, MMMM d')}
                            </span>
                            <span className="text-xs text-gray-500">
                              ({group.activities.length} activities)
                            </span>
                          </div>
                          
                          {group.totalPrice > 0 && (
                            <div className="text-sm font-medium text-gray-700">
                              ${group.totalPrice.toFixed(2)}
                            </div>
                          )}
                        </div>
                      </div>
                    </motion.div>
                  );
                }
                
                const activity = item.data as Activity;
                
                return (
                  <motion.div
                    key={activity.id}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    exit={{ opacity: 0, x: 20 }}
                    style={{
                      position: 'absolute',
                      top: 0,
                      left: 0,
                      width: '100%',
                      height: `${virtualItem.size}px`,
                      transform: `translateY(${virtualItem.start}px)`,
                      padding: '0 16px',
                    }}
                  >
                    <SortableTimelineItem
                      activity={activity}
                      onClick={() => onActivityClick?.(activity)}
                    />
                  </motion.div>
                );
              })}
            </AnimatePresence>
          </div>
        </div>
      </SortableContext>
    </DndContext>
  );
}