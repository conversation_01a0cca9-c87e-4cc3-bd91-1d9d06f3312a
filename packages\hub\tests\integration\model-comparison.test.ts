import { describe, it, expect, beforeAll, afterAll } from 'vitest';
import { ParserService } from '../../src/services/parser.service';
import { TripsService } from '../../src/services/trips.service';
import { PromptManager, MODEL_CONFIGS } from '../../src/services/prompt-manager';
import * as fs from 'fs';
import * as path from 'path';

// Test cases covering various formats and complexity levels
const TEST_CASES = [
  {
    id: 'chatgpt_simple',
    name: 'ChatGPT Simple Itinerary',
    source: 'chatgpt',
    text: `Day 1: Tokyo
- 9:00 AM: Arrive at Narita Airport
- 12:00 PM: Check into Park Hyatt Tokyo ($350/night)
- 2:00 PM: Visit Senso-ji Temple
- 7:00 PM: Dinner at Sukiyabashi Jiro ($300)

Day 2: Tokyo
- 9:00 AM: Tsukiji Fish Market tour
- 1:00 PM: Lunch at local ramen shop ($15)
- 3:00 PM: Shopping in Ginza
- 8:00 PM: Tokyo Tower observation deck ($20)`,
    expectedActivities: 8,
    expectedDestination: 'Tokyo',
    expectedDays: 2
  },
  {
    id: 'claude_complex',
    name: '<PERSON> European Trip',
    source: 'claude',
    text: `I've planned an amazing 15-day European adventure for you!

**Trip Overview:**
- Duration: March 15-30, 2024
- Cities: London → Madrid → Lisbon → Porto
- Total Budget: $5,000

**Day 1-4: London**
March 15: Fly from NYC to London Heathrow (arrival 10:30 AM). Take Heathrow Express to city (£25). Check into The Z Hotel Piccadilly (£120/night).
March 16: British Museum (free), lunch at Borough Market (£15), afternoon tea at Fortnum & Mason (£65).
March 17: Tower of London (£29.90), walk across Tower Bridge, evening West End show - Hamilton (£85).
March 18: Day trip to Oxford by train (£30 return), walking tour and pub lunch.

**Day 5-8: Madrid**
March 19: Flight London to Madrid (£80 RyanAir), arrive 2 PM. Check into Hotel Urban (€150/night).
March 20: Prado Museum (€15), Retiro Park, tapas tour in La Latina (€75).

[continues with more details...]`,
    expectedActivities: 15,
    expectedDestination: 'Europe',
    expectedDays: 15
  },
  {
    id: 'pdf_extract',
    name: 'PDF Extract Messy Format',
    source: 'gemini',
    text: `Updated 15-Day European Travel Itinerary_ London, Madrid, Lisbon and Porto.pdf
Page 1 of 5
ITINERARY OVERVIEW
Duration 15 days Cities London Madrid Lisbon Porto
Budget $5000 USD including flights accommodation activities
DAY 1 LONDON Arrival
Morning Flight arrives London Heathrow 1030am Terminal 5
Airport transfer options Heathrow Express 25 pounds Taxi 80 pounds Tube 6 pounds
Accommodation check in 3pm The Z Hotel Piccadilly 120 pounds per night
Evening Free time jet lag recovery light dinner nearby`,
    expectedActivities: 4,
    expectedDestination: 'London',
    expectedDays: 1
  },
  {
    id: 'email_format',
    name: 'Email Format with Incomplete Data',
    source: 'chatgpt',
    text: `Subject: Barcelona Trip Next Week!

Hi Sarah,

Super excited about our Barcelona trip! Here's what I've planned:

Monday:
- Morning: Sagrada Familia (tickets already booked)
- Afternoon: Park Güell 
- Evening: Tapas dinner in the Gothic Quarter

Tuesday:
- Beach day at Barceloneta
- Lunch at a chiringuito
- Evening: Flamenco show

Wednesday:
- Day trip to Montserrat
- Wine tasting

Let me know if you want to add anything!

Best,
John`,
    expectedActivities: 7,
    expectedDestination: 'Barcelona',
    expectedDays: 3
  },
  {
    id: 'minimal_info',
    name: 'Minimal Information',
    source: 'claude',
    text: `Weekend in Paris. Eiffel Tower, Louvre, Notre Dame. Nice cafes.`,
    expectedActivities: 3,
    expectedDestination: 'Paris',
    expectedDays: 1
  },
  {
    id: 'mixed_languages',
    name: 'Mixed Languages and Currencies',
    source: 'gemini',
    text: `Roma Itinerary (3 giorni)

Giorno 1:
- Colosseo e Foro Romano (€16)
- Pranzo: Carbonara at Flavio al Velavevodetto (€25)
- Pomeriggio: Fontana di Trevi
- Cena: Trastevere (€35)

Day 2:
- Vatican Museums and Sistine Chapel (€17 online)
- St. Peter's Basilica (free)
- Lunch near Vatican (€20)
- Spanish Steps sunset

第3天:
- Villa Borghese (¥2000)
- Galleria Borghese (需要预约)
- Lunch: Campo de' Fiori market
- Departure to airport`,
    expectedActivities: 10,
    expectedDestination: 'Roma',
    expectedDays: 3
  },
  {
    id: 'budget_focused',
    name: 'Budget Backpacker Style',
    source: 'chatgpt',
    text: `Southeast Asia Backpacking - 30 days - $1500 total

Week 1: Thailand
- Bangkok: Khao San Road hostel $8/night, street food $3-5/meal, temples
- Chiang Mai: overnight bus $15, cooking class $30, elephant sanctuary $50

Week 2: Laos  
- Slow boat to Luang Prabang $30, 2 days
- Kuang Si Falls day trip $10
- Vang Vieng tubing $20

Week 3: Vietnam
- Hanoi: Train from Laos border $40
- Ha Long Bay cruise 2D1N $80
- Hoi An: Bus $20, tailored clothes $50

Week 4: Cambodia
- Siem Reap: Bus from Vietnam $25
- Angkor Wat 3-day pass $62
- Phnom Penh: Bus $10, Killing Fields tour $15`,
    expectedActivities: 15,
    expectedDestination: 'Southeast Asia',
    expectedDays: 30
  },
  {
    id: 'business_travel',
    name: 'Business Travel Mixed with Leisure',
    source: 'claude',
    text: `NYC Business Trip - Conference + Weekend

Thursday Oct 12:
- 7:45 AM: Flight DEN-JFK (United, already booked)
- 3:30 PM: Arrive JFK, taxi to Midtown ($60)
- 5:00 PM: Check-in Hilton Times Square (company rate $289/night)
- 7:30 PM: Team dinner at STK Steakhouse

Friday Oct 13:
- 9:00 AM - 5:00 PM: AWS Summit at Javits Center
- 6:30 PM: Client dinner at Eleven Madison Park

Weekend (Personal):
Saturday:
- 10:00 AM: MoMA ($25)
- 1:00 PM: Lunch in Central Park
- 3:00 PM: Broadway matinee - Lion King ($150)
- 7:00 PM: Dinner in Little Italy

Sunday:
- Check out, store luggage
- 11:00 AM: High Line walk
- 1:00 PM: Chelsea Market lunch
- 4:00 PM: Airport
- 7:15 PM: Flight JFK-DEN`,
    expectedActivities: 12,
    expectedDestination: 'NYC',
    expectedDays: 4
  },
  {
    id: 'group_trip',
    name: 'Large Group Wedding Trip',
    source: 'gemini',
    text: `Destination Wedding in Tuscany - 25 guests

Day 1 (June 20):
- Group arrives at Florence Airport throughout day
- Chartered buses to Villa Castello (€50/person)
- 7 PM: Welcome cocktails by the pool
- 8:30 PM: Tuscan dinner (included in villa rental)

Day 2 (June 21):
- 10 AM: Optional wine tour to Chianti (€85/person, 15 people signed up)
- Others: Pool, spa, explore grounds
- 4 PM: Wedding rehearsal
- 7 PM: Rehearsal dinner

Day 3 (June 22) - WEDDING DAY:
- Morning: Spa treatments available
- 2 PM: Getting ready
- 5 PM: Ceremony in the garden
- 6 PM: Cocktail hour
- 7:30 PM: Reception dinner and dancing

Day 4 (June 23):
- 11 AM: Farewell brunch
- 2 PM: Buses back to Florence Airport`,
    expectedActivities: 10,
    expectedDestination: 'Tuscany',
    expectedDays: 4
  },
  {
    id: 'adventure_detailed',
    name: 'Adventure Trip with Activities',
    source: 'chatgpt',
    text: `New Zealand South Island Adventure - 14 Days

Day 1-2: Christchurch
- Arrive CHC airport, rental car pickup (NZ$450/week)
- Stay: YHA Christchurch (NZ$35/night)
- Explore city center, Botanic Gardens

Day 3-5: Franz Josef
- Drive to Franz Josef (5 hours)
- Glacier heli-hike (NZ$485)
- Hot pools (NZ$35)
- Stay: Rainforest Retreat (NZ$120/night)

Day 6-8: Queenstown  
- Drive via Wanaka (4 hours)
- Shotover Jet (NZ$135)
- Bungy jump Kawarau Bridge (NZ$205)
- Milford Sound day cruise (NZ$95)
- Stay: Nomads Hostel (NZ$40/night)

Day 9-11: Te Anau & Fiordland
- Kepler Track day hike
- Glowworm caves (NZ$98)
- Stay: Te Anau Lakeview Holiday Park (NZ$50/night)

Day 12-14: Mount Cook
- Drive to Mt Cook Village (3 hours)
- Hooker Valley Track
- Stargazing tour (NZ$85)
- Stay: Mt Cook Lodge (NZ$140/night)
- Return to Christchurch`,
    expectedActivities: 15,
    expectedDestination: 'New Zealand',
    expectedDays: 14
  }
];

// Result tracking
interface TestResult {
  modelId: string;
  modelName: string;
  testCase: string;
  success: boolean;
  accuracy: number;
  parseTime: number;
  tokensUsed: number;
  cost: number;
  error?: string;
  details?: {
    activitiesFound: number;
    destinationFound: string;
    daysFound: number;
  };
}

describe('AI Model Comparison Tests', () => {
  let parserService: ParserService;
  let results: TestResult[] = [];
  const resultsDir = path.join(__dirname, '../../../test-results');
  
  beforeAll(() => {
    // Skip tests if no API keys
    const hasOpenRouter = process.env.OPENROUTER_API_KEY && process.env.OPENROUTER_API_KEY.startsWith('sk-or-');
    const hasGemini = process.env.GOOGLE_GEMINI_API_KEY;
    
    if (!hasOpenRouter && !hasGemini) {
      console.warn('Skipping model comparison tests - no API keys configured');
      return;
    }
    
    // Create results directory
    if (!fs.existsSync(resultsDir)) {
      fs.mkdirSync(resultsDir, { recursive: true });
    }
    
    // Initialize parser service
    const tripsService = {} as TripsService; // Mock trips service
    parserService = new ParserService(tripsService);
  });
  
  afterAll(() => {
    // Save results to file
    if (results.length > 0) {
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const resultsFile = path.join(resultsDir, `model-comparison-${timestamp}.json`);
      fs.writeFileSync(resultsFile, JSON.stringify(results, null, 2));
      
      // Generate summary report
      generateSummaryReport(results, resultsDir, timestamp);
    }
  });
  
  // Add native Gemini to model configs if available
  const modelsToTest = { ...MODEL_CONFIGS };
  if (process.env.GOOGLE_GEMINI_API_KEY) {
    modelsToTest['gemini-native'] = {
      id: 'gemini-native',
      name: 'Gemini Native (FREE)',
      strategy: 'structured' as const,
      temperature: 0.1,
      maxTokens: 2000,
      costPer1K: 0
    };
  }
  
  // Test each model with each test case
  Object.entries(modelsToTest).forEach(([modelKey, modelConfig]) => {
    describe(`Model: ${modelConfig.name}`, () => {
      // Skip tests based on available API keys
      const isGeminiNative = modelKey === 'gemini-native';
      const needsOpenRouter = !isGeminiNative;
      const hasRequiredKey = isGeminiNative 
        ? process.env.GOOGLE_GEMINI_API_KEY 
        : process.env.OPENROUTER_API_KEY && process.env.OPENROUTER_API_KEY.startsWith('sk-or-');
      const skipTests = !hasRequiredKey;
      
      TEST_CASES.forEach(testCase => {
        it.skipIf(skipTests)(`should parse ${testCase.name}`, async () => {
          const startTime = Date.now();
          const result: TestResult = {
            modelId: modelConfig.id,
            modelName: modelConfig.name,
            testCase: testCase.id,
            success: false,
            accuracy: 0,
            parseTime: 0,
            tokensUsed: 0,
            cost: 0
          };
          
          try {
            // Parse with specific model
            const parsed = await parserService.parseTextToTrip(
              testCase.text,
              testCase.source as any,
              modelKey
            );
            
            result.parseTime = Date.now() - startTime;
            
            // Validate results
            result.details = {
              activitiesFound: parsed.activities.length,
              destinationFound: parsed.destination || 'Unknown',
              daysFound: Math.max(...parsed.activities.map(a => a.day || 1))
            };
            
            // Calculate accuracy
            const activityAccuracy = Math.min(100, (result.details.activitiesFound / testCase.expectedActivities) * 100);
            const destinationMatch = result.details.destinationFound.toLowerCase().includes(testCase.expectedDestination.toLowerCase()) ? 100 : 0;
            const dayAccuracy = Math.min(100, (result.details.daysFound / testCase.expectedDays) * 100);
            
            result.accuracy = (activityAccuracy + destinationMatch + dayAccuracy) / 3;
            result.success = result.accuracy > 70; // 70% threshold for success
            
            // Estimate tokens and cost (rough estimate)
            result.tokensUsed = Math.ceil(testCase.text.length / 4) + 500; // Input + output estimate
            result.cost = (result.tokensUsed / 1000) * modelConfig.costPer1K;
            
          } catch (error) {
            result.error = error instanceof Error ? error.message : 'Unknown error';
            result.success = false;
          }
          
          results.push(result);
          
          // Basic assertions
          expect(result.success).toBe(true);
          if (result.success) {
            expect(result.accuracy).toBeGreaterThan(70);
          }
        });
      });
    });
  });
});

// Generate summary report
function generateSummaryReport(results: TestResult[], resultsDir: string, timestamp: string) {
  const modelSummaries = new Map<string, {
    totalTests: number;
    successfulTests: number;
    avgAccuracy: number;
    avgParseTime: number;
    totalCost: number;
    avgTokensUsed: number;
  }>();
  
  // Aggregate results by model
  results.forEach(result => {
    const key = result.modelId;
    if (!modelSummaries.has(key)) {
      modelSummaries.set(key, {
        totalTests: 0,
        successfulTests: 0,
        avgAccuracy: 0,
        avgParseTime: 0,
        totalCost: 0,
        avgTokensUsed: 0
      });
    }
    
    const summary = modelSummaries.get(key)!;
    summary.totalTests++;
    if (result.success) summary.successfulTests++;
    summary.avgAccuracy += result.accuracy;
    summary.avgParseTime += result.parseTime;
    summary.totalCost += result.cost;
    summary.avgTokensUsed += result.tokensUsed;
  });
  
  // Calculate averages
  modelSummaries.forEach(summary => {
    summary.avgAccuracy /= summary.totalTests;
    summary.avgParseTime /= summary.totalTests;
    summary.avgTokensUsed /= summary.totalTests;
  });
  
  // Generate markdown report
  let report = '# AI Model Comparison Report\n\n';
  report += `Generated: ${new Date().toISOString()}\n\n`;
  report += '## Summary\n\n';
  report += '| Model | Success Rate | Avg Accuracy | Avg Time (ms) | Avg Tokens | Total Cost | Cost/1K Parse |\n';
  report += '|-------|--------------|--------------|---------------|------------|------------|---------------|\n';
  
  const sortedModels = Array.from(modelSummaries.entries()).sort((a, b) => b[1].avgAccuracy - a[1].avgAccuracy);
  
  sortedModels.forEach(([modelId, summary]) => {
    // Look for model config in both MODEL_CONFIGS and our extended list
    let modelName = modelId;
    let costPer1K = 0;
    
    if (modelId === 'gemini-native') {
      modelName = 'Gemini Native (FREE)';
      costPer1K = 0;
    } else {
      const modelConfig = Object.values(MODEL_CONFIGS).find(m => m.id === modelId);
      if (modelConfig) {
        modelName = modelConfig.name;
        costPer1K = modelConfig.costPer1K;
      }
    }
    
    const successRate = (summary.successfulTests / summary.totalTests * 100).toFixed(1);
    const costPer1KParse = (summary.totalCost / summary.totalTests * 1000).toFixed(2);
    
    report += `| ${modelName} | ${successRate}% | ${summary.avgAccuracy.toFixed(1)}% | ${summary.avgParseTime.toFixed(0)} | ${summary.avgTokensUsed.toFixed(0)} | $${summary.totalCost.toFixed(4)} | $${costPer1KParse} |\n`;
  });
  
  report += '\n## Recommendations\n\n';
  
  // Find best models
  const bestAccuracy = sortedModels[0];
  const bestValue = sortedModels.find(([id, s]) => {
    if (id === 'gemini-native') return s.avgAccuracy > 85;
    const model = Object.values(MODEL_CONFIGS).find(m => m.id === id);
    return s.avgAccuracy > 85 && model?.costPer1K === 0;
  });
  const bestBalance = sortedModels.find(([id, s]) => {
    if (id === 'gemini-native') return s.avgAccuracy > 90;
    const model = Object.values(MODEL_CONFIGS).find(m => m.id === id);
    return s.avgAccuracy > 90 && model && model.costPer1K < 0.1;
  });
  
  if (bestAccuracy) {
    const modelName = bestAccuracy[0] === 'gemini-native' 
      ? 'Gemini Native (FREE)' 
      : Object.values(MODEL_CONFIGS).find(m => m.id === bestAccuracy[0])?.name || bestAccuracy[0];
    report += `1. **Best Accuracy**: ${modelName} (${bestAccuracy[1].avgAccuracy.toFixed(1)}%)\n`;
  }
  
  if (bestValue) {
    const modelName = bestValue[0] === 'gemini-native' 
      ? 'Gemini Native (FREE)' 
      : Object.values(MODEL_CONFIGS).find(m => m.id === bestValue[0])?.name || bestValue[0];
    report += `2. **Best Value**: ${modelName} (Free, ${bestValue[1].avgAccuracy.toFixed(1)}% accuracy)\n`;
  }
  
  if (bestBalance) {
    const modelName = bestBalance[0] === 'gemini-native' 
      ? 'Gemini Native (FREE)' 
      : Object.values(MODEL_CONFIGS).find(m => m.id === bestBalance[0])?.name || bestBalance[0];
    const cost = bestBalance[0] === 'gemini-native' ? 0 : Object.values(MODEL_CONFIGS).find(m => m.id === bestBalance[0])?.costPer1K || 0;
    report += `3. **Best Balance**: ${modelName} ($${cost}/1K, ${bestBalance[1].avgAccuracy.toFixed(1)}% accuracy)\n`;
  }
  
  // Save report
  const reportFile = path.join(resultsDir, `model-comparison-report-${timestamp}.md`);
  fs.writeFileSync(reportFile, report);
  
  console.log(`\nTest results saved to: ${resultsDir}`);
  console.log(`Summary report: ${reportFile}`);
}