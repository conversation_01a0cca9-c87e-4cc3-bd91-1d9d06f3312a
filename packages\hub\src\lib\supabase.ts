import { SupabaseClient } from '@supabase/supabase-js';
import { z } from 'zod';
import { logger } from '../utils/logger';
import { ProfileSchema, TripSchema, ActivitySchema } from '@travelviz/shared';
import type { Profile, Trip, Activity } from '@travelviz/shared';
import { getConnectionPool } from './database/connection-pool';

// Database table names
export const TABLES = {
  USERS: 'profiles', // Fixed: users table is actually 'profiles' in our schema
  PROFILES: 'profiles',
  TRIPS: 'trips',
  ACTIVITIES: 'activities',
  TRIP_SHARES: 'trip_shares',
  AFFILIATE_CLICKS: 'affiliate_clicks',
  AUTH_FAILED_ATTEMPTS: 'auth_failed_attempts',
  AUTH_ACCOUNT_LOCKOUTS: 'auth_account_lockouts',
} as const;

// Type-safe error handling
interface SupabaseErrorDetails {
  message: string;
  statusCode: number;
  code?: string;
  details?: unknown;
}

/**
 * Creates or returns the existing Supabase client instance
 * Now uses connection pool for optimized connection management
 */
export function getSupabaseClient(): SupabaseClient {
  // Use connection pool client for better performance
  return getConnectionPool().getClient();
}

/**
 * Closes the Supabase client and cleans up resources
 */
export async function closeSupabaseClient(): Promise<void> {
  // Connection pool handles cleanup
  await getConnectionPool().shutdown();
}

/**
 * Type-safe error handler for Supabase errors
 */
export function handleSupabaseError(error: unknown): SupabaseErrorDetails {
  // Handle null/undefined
  if (error == null) {
    return {
      message: 'An unexpected error occurred',
      statusCode: 500,
    };
  }

  // Handle non-object errors
  if (typeof error !== 'object') {
    return {
      message: String(error),
      statusCode: 500,
    };
  }

  // Type assertion with validation
  const err = error as Record<string, unknown>;
  
  // Log the actual error object for debugging
  logger.debug('Supabase error object', { 
    error: err,
    keys: Object.keys(err),
    message: err.message,
    hint: err.hint,
    details: err.details,
    code: err.code 
  });
  
  // Extract error details safely
  const code = typeof err.code === 'string' ? err.code : undefined;
  let message = typeof err.message === 'string' ? err.message : 
                typeof err.hint === 'string' ? err.hint :
                typeof err.details === 'string' ? err.details :
                JSON.stringify(err) !== '{}' ? JSON.stringify(err) :
                'An unexpected error occurred';
  
  // Handle empty message strings
  if (!message || message.trim() === '') {
    logger.warn('Empty error message received from Supabase', { 
      error: err,
      code,
      keys: Object.keys(err)
    });
    // Try to provide a meaningful message based on code
    if (code === '42P17') {
      message = 'Database policy configuration error: infinite recursion detected';
    } else if (code) {
      message = `Database error: ${code}`;
    } else {
      message = 'Database operation failed';
    }
  }
  
  const status = typeof err.status === 'number' ? err.status : 
                 typeof err.statusCode === 'number' ? err.statusCode : 500;

  // Validate status code range
  const statusCode = status >= 100 && status <= 599 ? status : 500;

  // Handle specific Supabase error codes
  switch (code) {
    case 'PGRST301':
      return { 
        message: 'Database query failed: Table or column not found', 
        statusCode: 404,
        code 
      };
    case '42P01':
      return { 
        message: 'Table does not exist', 
        statusCode: 404,
        code 
      };
    case '23505':
      return { 
        message: 'Duplicate entry', 
        statusCode: 409,
        code 
      };
    case '23503':
      return { 
        message: 'Foreign key constraint violation', 
        statusCode: 400,
        code 
      };
    case '42P17':
      return { 
        message: 'Database policy configuration error: infinite recursion detected', 
        statusCode: 500,
        code 
      };
    case 'PGRST116':
      return { 
        message: 'Invalid authentication credentials', 
        statusCode: 401,
        code 
      };
  }

  // Handle network errors
  if (message.includes('fetch failed') || message.includes('ECONNREFUSED')) {
    return { 
      message: 'Unable to connect to database', 
      statusCode: 503,
      code: 'NETWORK_ERROR' 
    };
  }

  // Default error response
  return { 
    message, 
    statusCode,
    code,
    details: err 
  };
}

/**
 * Tests database connection with timeout support
 */
export async function testDatabaseConnection(timeoutMs = 5000): Promise<boolean> {
  try {
    const client = getSupabaseClient();
    
    // Create timeout promise
    const timeoutPromise = new Promise<boolean>((_, reject) => 
      setTimeout(() => reject(new Error('Connection timeout')), timeoutMs)
    );
    
    // Create query promise
    const queryPromise = (async () => {
      const { error } = await client
        .from('_test_connection')
        .select('1')
        .limit(1)
        .single();
      
      // If table doesn't exist, that's fine - we're just testing connectivity
      if (error && error.code !== '42P01' && error.code !== 'PGRST116') {
        logger.error('Database connection test error:', { error: handleSupabaseError(error) });
        return false;
      }
      
      return true;
    })();
    
    // Race between query and timeout
    return await Promise.race([queryPromise, timeoutPromise]);
  } catch (error) {
    logger.error('Database connection test failed:', { error });
    return false;
  }
}

// Re-export schemas and types from shared package
export { ProfileSchema, TripSchema, ActivitySchema };
export type { Profile, Trip, Activity };
export { getSupabaseClient as supabase };

/**
 * Get connection pool statistics
 */
export function getConnectionStats(): import('./database/connection-pool').PoolStats {
  return getConnectionPool().getStats();
}

/**
 * Get connection health status
 */
export function getConnectionHealth(): import('./database/connection-pool').PoolHealth {
  return getConnectionPool().getHealthStatus();
}


// Helper function to ensure valid database responses
export function validateDatabaseResponse<T>(
  schema: z.ZodSchema<T>,
  data: unknown,
  entityName: string
): T {
  try {
    return schema.parse(data);
  } catch (error) {
    if (error instanceof z.ZodError) {
      const issues = error.issues.map(i => `${i.path.join('.')}: ${i.message}`).join(', ');
      throw new Error(`Invalid ${entityName} data: ${issues}`);
    }
    throw error;
  }
}