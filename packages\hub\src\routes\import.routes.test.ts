import { describe, it, expect, beforeEach, afterEach, afterAll, vi } from 'vitest';
import request from 'supertest';
import express from 'express';
import { authenticateSupabaseUser } from '../middleware/supabase-auth.middleware';
import { ImportController } from '../controllers/import.controller';
import path from 'path';
import fs from 'fs';

// Mock middlewares
vi.mock('../middleware/supabase-auth.middleware');
vi.mock('../middleware/file-validation.middleware', () => ({
  validateFileUpload: vi.fn((req: Express.Request, res: Express.Response, next: Express.NextFunction) => next()),
  validateTextContent: vi.fn((req: Express.Request, res: Express.Response, next: Express.NextFunction) => next())
}));
vi.mock('../middleware/rate-limit.middleware', () => ({
  importRateLimit: vi.fn((req: Express.Request, res: Express.Response, next: Express.NextFunction) => next())
}));
// Don't mock validation middleware - we want to test it
// vi.mock('../middleware/validation.middleware', () => ({
//   validate: vi.fn(() => (req: any, res: any, next: any) => next())
// }));
vi.mock('../middleware/request-size.middleware', () => ({
  validateContentLength: vi.fn(() => (req: Express.Request, res: Express.Response, next: Express.NextFunction) => next())
}));

// Mock AIRouter to prevent health checks
vi.mock('../services/aiRouter.service', () => ({
  aiRouter: {
    selectModel: vi.fn(() => ({
      modelId: 'test-model',
      modelName: 'Test Model',
      estimatedCost: 0.01
    }))
  }
}));

// Mock controller before importing routes
vi.mock('../controllers/import.controller', () => {
  const mockParseAndCreateTripFromFile = vi.fn(async (req: Express.Request, res: Express.Response) => {
    res.status(201).json({ success: true, data: { trip: { id: 'test-trip-id' } } });
  });
  
  const mockParseAndCreateTrip = vi.fn(async (req: Express.Request, res: Express.Response) => {
    res.status(201).json({ success: true, data: { trip: { id: 'test-trip-id' } } });
  });
  
  return {
    ImportController: vi.fn().mockImplementation(() => ({
      parseAndCreateTripFromFile: mockParseAndCreateTripFromFile,
      parseAndCreateTrip: mockParseAndCreateTrip
    }))
  };
});

// Import routes after all mocks are set up
import importRoutes from './import.routes';

describe('Import Routes - File Upload', () => {
  let app: express.Application;
  let mockController: InstanceType<typeof ImportController>;

  beforeEach(() => {
    app = express();
    app.use(express.json());
    
    // Mock authenticateSupabaseUser
    (authenticateSupabaseUser as vi.MockedFunction<typeof authenticateSupabaseUser>).mockImplementation((req: Express.Request & { user?: { id: string; email: string } }, res: Express.Response, next: Express.NextFunction) => {
      req.user = { id: 'test-user-id', email: '<EMAIL>' };
      next();
    });
    
    app.use('/api/v1/import', importRoutes);
    
    // Get mock controller instance
    mockController = new (ImportController as unknown as new () => InstanceType<typeof ImportController>)();
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  afterAll(() => {
    // Clean up any test files
    const testDataDir = path.join(__dirname, '../../test-data');
    if (fs.existsSync(testDataDir)) {
      const files = fs.readdirSync(testDataDir);
      files.forEach(file => {
        if (file.startsWith('sample.') || file.startsWith('large.')) {
          fs.unlinkSync(path.join(testDataDir, file));
        }
      });
    }
  });

  describe('POST /api/v1/import/upload', () => {
    it('should accept PDF file upload', async () => {
      const testPdfPath = path.join(__dirname, '../../test-data/sample.pdf');
      
      // Create a test PDF file if it doesn't exist
      if (!fs.existsSync(testPdfPath)) {
        const testDataDir = path.dirname(testPdfPath);
        if (!fs.existsSync(testDataDir)) {
          fs.mkdirSync(testDataDir, { recursive: true });
        }
        // Create a minimal PDF for testing
        const pdfContent = Buffer.from('%PDF-1.4\n1 0 obj\n<< /Type /Catalog /Pages 2 0 R >>\nendobj\n2 0 obj\n<< /Type /Pages /Kids [3 0 R] /Count 1 >>\nendobj\n3 0 obj\n<< /Type /Page /Parent 2 0 R /Resources << >> /MediaBox [0 0 612 792] >>\nendobj\nxref\n0 4\n0000000000 65535 f\n0000000009 00000 n\n0000000058 00000 n\n0000000115 00000 n\ntrailer\n<< /Size 4 /Root 1 0 R >>\nstartxref\n203\n%%EOF');
        fs.writeFileSync(testPdfPath, pdfContent);
      }

      // Mock is already set up with the response

      const response = await request(app)
        .post('/api/v1/import/upload')
        .set('Authorization', 'Bearer test-token')
        .field('source', 'chatgpt')
        .attach('file', testPdfPath);

      expect(response.status).toBe(201);
      expect(response.body.success).toBe(true);
      expect(mockController.parseAndCreateTripFromFile).toHaveBeenCalled();
    });

    it('should reject non-PDF files', async () => {
      const testTxtPath = path.join(__dirname, '../../test-data/sample.txt');
      
      // Create a test text file
      const testDataDir = path.dirname(testTxtPath);
      if (!fs.existsSync(testDataDir)) {
        fs.mkdirSync(testDataDir, { recursive: true });
      }
      fs.writeFileSync(testTxtPath, 'This is not a PDF');

      const response = await request(app)
        .post('/api/v1/import/upload')
        .set('Authorization', 'Bearer test-token')
        .field('source', 'chatgpt')
        .attach('file', testTxtPath);

      // Multer rejects non-PDF files with a 500 error
      expect(response.status).toBe(500);
      // Multer errors return text instead of JSON
      expect(response.text).toContain('Error');
    });

    it('should reject files larger than 10MB', async () => {
      // Create a large test file
      const largePdfPath = path.join(__dirname, '../../test-data/large.pdf');
      const testDataDir = path.dirname(largePdfPath);
      if (!fs.existsSync(testDataDir)) {
        fs.mkdirSync(testDataDir, { recursive: true });
      }
      
      // Create a buffer larger than 10MB
      const largeBuffer = Buffer.alloc(11 * 1024 * 1024); // 11MB
      fs.writeFileSync(largePdfPath, largeBuffer);

      const response = await request(app)
        .post('/api/v1/import/upload')
        .set('Authorization', 'Bearer test-token')
        .field('source', 'chatgpt')
        .attach('file', largePdfPath);

      // Multer rejects large files with a 500 error
      expect(response.status).toBe(500);
      // Multer errors return text instead of JSON
      expect(response.text).toContain('Error');

      // Clean up
      fs.unlinkSync(largePdfPath);
    });

    it('should require authentication', async () => {
      // Mock authenticateSupabaseUser to return 401
      (authenticateSupabaseUser as vi.MockedFunction<typeof authenticateSupabaseUser>).mockImplementationOnce((req: Express.Request, res: Express.Response) => {
        res.status(401).json({ success: false, message: 'Unauthorized' });
      });

      const testPdfPath = path.join(__dirname, '../../test-data/sample.pdf');

      const response = await request(app)
        .post('/api/v1/import/upload')
        .field('source', 'chatgpt')
        .attach('file', testPdfPath);

      expect(response.status).toBe(401);
    });

    it('should require source field', async () => {
      const testPdfPath = path.join(__dirname, '../../test-data/sample.pdf');

      const response = await request(app)
        .post('/api/v1/import/upload')
        .set('Authorization', 'Bearer test-token')
        .attach('file', testPdfPath);

      expect(response.status).toBe(400);
      expect(response.body.message).toContain('source');
    });
  });
});