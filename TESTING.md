# TravelViz Testing Guide

This guide covers the comprehensive end-to-end testing system for TravelViz, including API integration tests, E2E user flow tests, and CI/CD validation.

## Overview

Our testing system replaces fragmented mock-heavy tests with real integration testing that validates actual user flows and API behavior. The system includes:

- **API Integration Tests**: Real database connections and endpoint validation
- **E2E User Flow Tests**: Complete user journeys with real authentication
- **CI/CD Validation**: Environment and startup validation for deployment

## Quick Start

### Prerequisites

1. **Development Server Running**:
   ```bash
   pnpm dev
   ```
   This starts both hub (port 3001) and web (port 3000) servers.

2. **Environment Configuration**:
   - Ensure `.env.local` files exist in `packages/hub/` and `packages/web/`
   - Test user credentials should be configured (see Environment Setup below)

### Running Tests

```bash
# Run all tests (CI validation + API + E2E)
pnpm test:all

# Run individual test suites
pnpm test:api      # All API integration tests
pnpm test:e2e      # All E2E tests (headless)
pnpm test:ci       # CI/CD validation tests

# Run specific test categories
pnpm test:api:auth     # Authentication API tests
pnpm test:e2e:headed   # E2E tests with browser visible
pnpm test:validate     # Validate test foundation
```

## Test Architecture

### Directory Structure

```
tests/
├── api/                          # API Integration Tests
│   ├── auth.api.test.js         # Authentication endpoints
│   ├── trips.api.test.js        # Trip management endpoints
│   ├── import.api.test.js       # AI import endpoints
│   ├── places.api.test.js       # Places integration endpoints
│   ├── public.api.test.js       # Public access endpoints
│   ├── run-api-tests.js         # API test runner
│   ├── api.config.js            # API test configuration
│   └── utils/
│       └── api-client.js        # HTTP client with token management
├── e2e/                         # End-to-End Tests
│   ├── auth-flow.spec.js        # Authentication flows
│   ├── user-journey.spec.js     # Complete user journeys
│   ├── import-flow.spec.js      # AI import flows
│   ├── trip-display.spec.js     # Trip visualization
│   └── utils/
│       └── page-objects.js      # Page object models
├── ci/                          # CI/CD Tests
│   ├── health-check.test.js     # Service health validation
│   ├── env-validation.test.js   # Environment setup check
│   └── startup.test.js          # Service startup validation
├── test.config.js               # Global test configuration
└── validate-foundation.js       # Foundation test validator
```

## Environment Setup

### Required Environment Variables

Create `.env.local` files in both `packages/hub/` and `packages/web/`:

#### packages/hub/.env.local
```bash
# Database
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_ANON_KEY=your-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-service-key

# Authentication
JWT_SECRET=your-strong-jwt-secret-at-least-32-chars

# API Keys
GOOGLE_PLACES_API_KEY=your-google-places-key
OPENAI_API_KEY=your-openai-key

# Test User (create this user in Supabase)
TEST_USER_EMAIL=<EMAIL>
TEST_USER_PASSWORD=Flaremmk123!
```

#### packages/web/.env.local
```bash
# App URLs
NEXT_PUBLIC_APP_URL=http://localhost:3000
API_BASE_URL=http://localhost:3001

# Database (same as hub)
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_ANON_KEY=your-anon-key

# Test User (same as hub)
TEST_USER_EMAIL=<EMAIL>
TEST_USER_PASSWORD=Flaremmk123!
```

### Test User Setup

Create a test user in your Supabase database:

1. Go to Supabase Dashboard → Authentication → Users
2. Create a new user with:
   - Email: `<EMAIL>` (or your preferred test email)
   - Password: `Flaremmk123!` (or your preferred test password)
3. Confirm the user's email
4. Update your `.env.local` files with these credentials

## API Integration Tests

### Features

- **Real Database Connections**: Tests use actual Supabase connections
- **Token Management**: Automatic JWT token persistence and refresh
- **Schema Validation**: All responses validated against expected schemas
- **Error Handling**: Comprehensive error scenarios and edge cases
- **Cleanup**: Automatic test data cleanup after each test

### Test Categories

#### Authentication Tests (`test:api:auth`)
- Login/logout flows
- Token persistence and reuse
- Automatic token refresh
- Invalid credential handling

#### Trip Management Tests (`test:api:trips`)
- CRUD operations for trips
- Activity management
- Pagination and filtering
- Authorization validation

#### AI Import Tests (`test:api:import`)
- Content parsing with real travel conversations
- Different AI source handling (ChatGPT, Claude, Gemini)
- Error handling for invalid content
- Import status tracking

#### Places Integration Tests (`test:api:places`)
- Google Places autocomplete
- Geocoding functionality
- Error handling for invalid queries
- Rate limiting validation

#### Public Access Tests (`test:api:public`)
- Health check endpoints
- CORS configuration
- Rate limiting
- Security headers

### Running API Tests

```bash
# All API tests (requires hub server running)
pnpm test:api

# Individual test suites
pnpm test:api:auth
pnpm test:api:trips
pnpm test:api:import
pnpm test:api:places
pnpm test:api:public
```

### API Test Patterns

#### Basic Test Structure
```javascript
const ApiTestClient = require('./utils/api-client');
const client = new ApiTestClient();

// Login automatically saves and reuses tokens
const loginResponse = await client.login(email, password);

// Authenticated requests use saved tokens automatically
const response = await client.authenticatedRequest('GET', '/api/v1/trips');

// Token refresh is automatic when expired
```

#### Error Handling
```javascript
function logTest(name, result, details = {}) {
  const passed = result.success || result === true;
  if (passed) {
    console.log(`✅ ${name}`);
  } else {
    console.log(`❌ ${name}`, details.error || '');
  }
}
```

## E2E Tests

### Features

- **Real Browser Automation**: Uses Playwright for actual browser testing
- **Complete User Flows**: Tests entire user journeys from start to finish
- **Visual Validation**: Screenshot capture on failures
- **Cross-Browser Support**: Chrome, Firefox, Safari testing
- **Responsive Testing**: Mobile and tablet viewport testing

### Test Categories

#### Authentication Flow Tests (`test:e2e:auth`)
- Homepage to login navigation
- Login form validation
- Authentication success/failure handling
- Session persistence

#### User Journey Tests (`test:e2e:journey`)
- Complete flow: home → login → import → display
- Navigation between pages
- Content handling across flows

#### Import Flow Tests (`test:e2e:import`)
- AI content import with different sources
- Real travel conversation processing
- Import progress monitoring
- Error handling for invalid content

#### Trip Display Tests (`test:e2e:display`)
- Trip visualization and timeline
- Map integration testing
- Interactive features (drag & drop)
- Responsive design validation

### Running E2E Tests

```bash
# All E2E tests (headless)
pnpm test:e2e

# With browser visible (for debugging)
pnpm test:e2e:headed

# Individual test suites
pnpm test:e2e:auth
pnpm test:e2e:journey
pnpm test:e2e:import
pnpm test:e2e:display
```

### E2E Test Patterns

#### Page Object Model
```javascript
const { HomePage, LoginPage } = require('./utils/page-objects');

test('user flow', async ({ page }) => {
  const homePage = new HomePage(page);
  await homePage.navigate();
  await homePage.clickLogin();
  
  const loginPage = new LoginPage(page);
  await loginPage.login(email, password);
  await loginPage.expectLoginSuccess();
});
```

#### Screenshot Capture
```javascript
test.afterEach(async ({ page }, testInfo) => {
  if (testInfo.status !== testInfo.expectedStatus) {
    const screenshotPath = `test-results/failure-${Date.now()}.png`;
    await page.screenshot({ path: screenshotPath, fullPage: true });
  }
});
```

## CI/CD Validation Tests

### Features

- **Environment Validation**: Checks all required environment variables
- **Service Health**: Validates service startup without actually starting servers
- **Build Validation**: Ensures all packages build successfully
- **Security Checks**: Validates secrets and API key formats

### Test Categories

#### Health Check (`test:ci`)
- Environment variable presence
- Package structure validation
- Service startup readiness
- Test configuration validation

#### Environment Validation (`test:ci:env`)
- Supabase configuration validation
- JWT secret strength checking
- API key format validation
- Security configuration review

#### Startup Validation (`test:ci:startup`)
- Dependency installation
- TypeScript compilation
- Build process validation
- Configuration file presence

### Running CI Tests

```bash
# All CI validation tests
pnpm test:ci

# Individual CI tests
pnpm test:ci:env
pnpm test:ci:startup
```

## Test Results and Reporting

### Test Results Directory

All test results are saved to `test-results/`:

```
test-results/
├── playwright-report/           # Playwright HTML reports
├── playwright-artifacts/        # Videos and traces
├── ci-health-report.json       # CI health check results
├── env-validation-report.json  # Environment validation results
├── startup-validation-report.json # Startup validation results
└── *.png                       # Screenshots from failed tests
```

### Viewing Reports

```bash
# Open Playwright HTML report
npx playwright show-report test-results/playwright-report

# View CI reports
cat test-results/ci-health-report.json | jq
```

## Troubleshooting

### Common Issues

#### API Tests Failing
```bash
# Check if hub server is running
curl http://localhost:3001/api/v1/health

# Verify environment variables
pnpm test:ci:env

# Check test user exists in Supabase
```

#### E2E Tests Failing
```bash
# Check if both servers are running
curl http://localhost:3000
curl http://localhost:3001/api/v1/health

# Run with browser visible for debugging
pnpm test:e2e:headed

# Check screenshots in test-results/
```

#### Environment Issues
```bash
# Validate environment setup
pnpm test:ci:env

# Check .env.local files exist
ls -la packages/hub/.env.local
ls -la packages/web/.env.local
```

### Debug Mode

For detailed debugging:

```bash
# API tests with verbose output
DEBUG=1 pnpm test:api:auth

# E2E tests with browser visible and slow motion
pnpm test:e2e:headed --debug

# Validate test foundation
pnpm test:validate
```

## Best Practices

### Writing New Tests

1. **Follow Established Patterns**: Copy from existing tests and modify
2. **Use Real Data**: Avoid mocks, test with actual API calls and user interactions
3. **Clean Up**: Always clean up test data after tests complete
4. **Error Handling**: Test both success and failure scenarios
5. **Screenshots**: Capture screenshots for visual validation

### Test Data Management

1. **Unique Identifiers**: Use timestamps or UUIDs for test data
2. **Cleanup**: Delete test data in `afterEach` or test cleanup sections
3. **Isolation**: Each test should be independent and not rely on other tests

### Performance Considerations

1. **Parallel Execution**: API tests can run in parallel, E2E tests run sequentially
2. **Timeouts**: Set appropriate timeouts for different operations
3. **Resource Cleanup**: Clean up browser instances and API connections

## Contributing

When adding new tests:

1. Follow the established patterns in existing tests
2. Add appropriate documentation
3. Ensure tests clean up after themselves
4. Test both success and failure scenarios
5. Add the new test to the appropriate npm script in `package.json`

## Support

For issues with the testing system:

1. Check this documentation first
2. Run `pnpm test:validate` to check foundation setup
3. Check the troubleshooting section above
4. Review test results and screenshots in `test-results/`