import {
  getSupabaseClient,
  handleSupabaseError,
  TABLES,
  TripSchema,
  ActivitySchema,
  validateDatabaseResponse
} from '../../lib/supabase';
import { getErrorMessage } from '../../utils/error-handler';
import { sanitizeTripData } from '../../utils/sanitizer';
import { logger } from '../../utils/logger';
// TODO: Re-enable when optimized query service is implemented
// import { optimizedQueryService } from '../optimized-query.service';
// import { performanceMonitor } from '../performance-monitor.service';
import type { TripData, TripWithActivities, PaginatedTripsResult } from './types';
import { PaginationQuery } from '@travelviz/shared';
import { validateDateRange } from '@travelviz/shared';

// Cleaning functions removed - Zod schemas now properly handle null values

export class TripCrudService {
  async createTrip(data: TripData): Promise<TripWithActivities> {
    // Use service role client to bypass RLS policies
    const supabase = getSupabaseClient();
    
    try {
      // Validate dates if provided
      if (data.startDate && data.endDate) {
        validateDateRange(data.startDate, data.endDate);
      }

      // Sanitize input data to prevent XSS attacks
      const sanitizedData = sanitizeTripData(data);
      
      // Create trip in database
      const { data: trip, error } = await supabase
        .from(TABLES.TRIPS)
        .insert({
          user_id: sanitizedData.userId,
          title: sanitizedData.title,
          description: sanitizedData.description || null,
          start_date: sanitizedData.startDate || null,
          end_date: sanitizedData.endDate || null,
          destination: sanitizedData.destination || null,
          status: sanitizedData.status || 'draft',
          visibility: sanitizedData.visibility || 'private',
          cover_image: sanitizedData.coverImage || null,
          tags: sanitizedData.tags || [],
          budget_amount: sanitizedData.budgetAmount || null,
          budget_currency: sanitizedData.budgetCurrency || 'USD',
        })
        .select()
        .single();

      if (error) {
        const errorDetails = handleSupabaseError(error);
        throw new Error(`Failed to create trip: ${errorDetails.message}`);
      }

      // Validate response
      const validatedTrip = validateDatabaseResponse(TripSchema, trip, 'trip');
      
      // Ensure all required fields have values
      const completeTrip: TripWithActivities = {
        ...validatedTrip,
        status: validatedTrip.status || 'draft',
        visibility: validatedTrip.visibility || 'private', 
        metadata: validatedTrip.metadata || {},
        tags: validatedTrip.tags || [],
        budget_currency: validatedTrip.budget_currency || 'USD',
        activities: [],
      };
      
      return completeTrip;
    } catch (error: unknown) { 
      if (error instanceof Error && getErrorMessage(error).includes('End date must be after start date')) {
        throw error;
      }
      const errorDetails = handleSupabaseError(error);
      throw new Error(`Failed to create trip: ${errorDetails.message}`);
    }
  }

  async getUserTrips(userId: string): Promise<TripWithActivities[]> {
    const supabase = getSupabaseClient();
    
    try {
      // Get user's trips with activities
      const { data: trips, error } = await supabase
        .from(TABLES.TRIPS)
        .select(`
          *,
          ${TABLES.ACTIVITIES} (*)
        `)
        .eq('user_id', userId)
        .order('created_at', { ascending: false });

      if (error) {
        const errorDetails = handleSupabaseError(error);
        throw new Error(`Failed to get trips: ${errorDetails.message}`);
      }

      // Validate and format response
      const validatedTrips = trips.map(trip => {
        const activities = (trip[TABLES.ACTIVITIES] || []) as Record<string, unknown>[];
        const validatedActivities = activities?.map(activity => {
          const validatedActivity = validateDatabaseResponse(ActivitySchema, activity, 'activity');
          return {
            ...validatedActivity,
            type: validatedActivity.type || 'activity',
            metadata: validatedActivity.metadata || {},
            currency: validatedActivity.currency || 'USD',
            attachments: validatedActivity.attachments || [],
          };
        }) || [];

        // Remove the nested activities before validation
        const tripData = { ...trip };
        delete tripData[TABLES.ACTIVITIES];
        
        const validatedTrip = validateDatabaseResponse(TripSchema, tripData, 'trip');
        
        const completeTrip: TripWithActivities = {
          ...validatedTrip,
          status: validatedTrip.status || 'draft',
          visibility: validatedTrip.visibility || 'private',
          metadata: validatedTrip.metadata || {},
          tags: validatedTrip.tags || [],
          budget_currency: validatedTrip.budget_currency || 'USD',
          activities: validatedActivities,
        };
        
        return completeTrip;
      });

      return validatedTrips;
    } catch (error: unknown) {
      const errorDetails = handleSupabaseError(error);
      throw new Error(`Failed to get trips: ${errorDetails.message}`);
    }
  }

  async getUserTripsPaginated(userId: string, pagination: PaginationQuery): Promise<PaginatedTripsResult> {
    const supabase = getSupabaseClient();
    
    try {
      const offset = (pagination.page - 1) * pagination.limit;
      
      // Get total count for pagination
      const { count: totalCount, error: countError } = await supabase
        .from(TABLES.TRIPS)
        .select('*', { count: 'exact', head: true })
        .eq('user_id', userId);

      if (countError) {
        logger.error('Count trips error', { countError, userId });
        const errorDetails = handleSupabaseError(countError);
        throw new Error(`Failed to count trips: ${errorDetails.message}`);
      }

      // Get paginated trips with activities
      const { data: trips, error } = await supabase
        .from(TABLES.TRIPS)
        .select(`
          *,
          ${TABLES.ACTIVITIES} (*)
        `)
        .eq('user_id', userId)
        .order(pagination.sortBy || 'created_at', { ascending: pagination.sortOrder === 'asc' })
        .range(offset, offset + pagination.limit - 1);

      if (error) {
        const errorDetails = handleSupabaseError(error);
        throw new Error(`Failed to get trips: ${errorDetails.message}`);
      }

      // Validate and format response
      const validatedTrips = trips.map(trip => {
        const activities = (trip[TABLES.ACTIVITIES] || []) as Record<string, unknown>[];
        const validatedActivities = activities?.map(activity => {
          const validatedActivity = validateDatabaseResponse(ActivitySchema, activity, 'activity');
          return {
            ...validatedActivity,
            type: validatedActivity.type || 'activity',
            metadata: validatedActivity.metadata || {},
            currency: validatedActivity.currency || 'USD',
            attachments: validatedActivity.attachments || [],
          };
        }) || [];

        // Remove the nested activities before validation
        const tripData = { ...trip };
        delete tripData[TABLES.ACTIVITIES];
        
        const validatedTrip = validateDatabaseResponse(TripSchema, tripData, 'trip');
        
        const completeTrip: TripWithActivities = {
          ...validatedTrip,
          status: validatedTrip.status || 'draft',
          visibility: validatedTrip.visibility || 'private',
          metadata: validatedTrip.metadata || {},
          tags: validatedTrip.tags || [],
          budget_currency: validatedTrip.budget_currency || 'USD',
          activities: validatedActivities,
        };
        
        return completeTrip;
      });

      const total = totalCount || 0;
      const totalPages = Math.ceil(total / pagination.limit);
      const hasNextPage = pagination.page < totalPages;
      const hasPrevPage = pagination.page > 1;

      return {
        trips: validatedTrips,
        page: pagination.page,
        limit: pagination.limit,
        total,
        totalPages,
        hasNextPage,
        hasPrevPage
      };
    } catch (error: unknown) {
      const errorDetails = handleSupabaseError(error);
      throw new Error(`Failed to get paginated trips: ${errorDetails.message}`);
    }
  }

  async getTripById(tripId: string, userId: string): Promise<TripWithActivities | null> {
    const supabase = getSupabaseClient();
    
    try {
      // Get trip with activities
      const { data: trip, error } = await supabase
        .from(TABLES.TRIPS)
        .select(`
          *,
          ${TABLES.ACTIVITIES} (*)
        `)
        .eq('id', tripId)
        .eq('user_id', userId)
        .single();

      if (error) {
        if ((error as { code?: string }).code === 'PGRST116') {
          return null; // Not found
        }
        const errorDetails = handleSupabaseError(error);
        throw new Error(`Failed to get trip: ${errorDetails.message}`);
      }

      // Validate and format response
      const activities = (trip[TABLES.ACTIVITIES] || []) as Record<string, unknown>[];
      const validatedActivities = activities?.map(activity => {
        const validatedActivity = validateDatabaseResponse(ActivitySchema, activity, 'activity');
        return {
          ...validatedActivity,
          type: validatedActivity.type || 'activity',
          metadata: validatedActivity.metadata || {},
          currency: validatedActivity.currency || 'USD',
          attachments: validatedActivity.attachments || [],
        };
      }) || [];

      // Remove the nested activities before validation
      const tripData = { ...trip };
      delete tripData[TABLES.ACTIVITIES];
      
      const validatedTrip = validateDatabaseResponse(TripSchema, tripData, 'trip');
      
      const completeTrip: TripWithActivities = {
        ...validatedTrip,
        status: validatedTrip.status || 'draft',
        visibility: validatedTrip.visibility || 'private',
        metadata: validatedTrip.metadata || {},
        tags: validatedTrip.tags || [],
        budget_currency: validatedTrip.budget_currency || 'USD',
        activities: validatedActivities,
      };
      
      // Increment view count asynchronously (don't wait for it)
      this.incrementTripViews(tripId).catch(err => {
        logger.error('Failed to track trip view:', { error: err });
      });
      
      return completeTrip;
    } catch (error: unknown) {
      const errorDetails = handleSupabaseError(error);
      throw new Error(`Failed to get trip: ${errorDetails.message}`);
    }
  }

  async updateTrip(tripId: string, userId: string, updateData: Partial<TripData>): Promise<TripWithActivities | null> {
    const supabase = getSupabaseClient();
    
    try {
      // Validate dates if both are provided
      if (updateData.startDate && updateData.endDate) {
        validateDateRange(updateData.startDate, updateData.endDate);
      }

      // Sanitize update data to prevent XSS attacks
      const sanitizedUpdateData = sanitizeTripData(updateData);
      
      // Prepare update data (map camelCase to snake_case)
      const dbUpdateData: Record<string, unknown> = {};
      if (sanitizedUpdateData.title !== undefined) dbUpdateData.title = sanitizedUpdateData.title;
      if (sanitizedUpdateData.description !== undefined) dbUpdateData.description = sanitizedUpdateData.description;
      if (sanitizedUpdateData.startDate !== undefined) dbUpdateData.start_date = sanitizedUpdateData.startDate;
      if (sanitizedUpdateData.endDate !== undefined) dbUpdateData.end_date = sanitizedUpdateData.endDate;
      if (sanitizedUpdateData.destination !== undefined) dbUpdateData.destination = sanitizedUpdateData.destination;
      if (sanitizedUpdateData.tags !== undefined) dbUpdateData.tags = sanitizedUpdateData.tags;
      if (sanitizedUpdateData.status !== undefined) dbUpdateData.status = sanitizedUpdateData.status;
      if (sanitizedUpdateData.visibility !== undefined) dbUpdateData.visibility = sanitizedUpdateData.visibility;

      // Update trip and fetch with activities in a single query to eliminate N+1 pattern
      const { data: trip, error } = await supabase
        .from(TABLES.TRIPS)
        .update(dbUpdateData)
        .eq('id', tripId)
        .eq('user_id', userId)
        .select(`
          *,
          ${TABLES.ACTIVITIES} (*)
        `)
        .single();

      if (error) {
        if ((error as { code?: string }).code === 'PGRST116') {
          return null; // Not found
        }
        const errorDetails = handleSupabaseError(error);
        throw new Error(`Failed to update trip: ${errorDetails.message}`);
      }

      // Validate and format response
      const activities = (trip[TABLES.ACTIVITIES] || []) as Record<string, unknown>[];
      
      // Remove the nested activities before validation
      const tripData = { ...trip };
      delete tripData[TABLES.ACTIVITIES];
      
      const validatedTrip = validateDatabaseResponse(TripSchema, tripData, 'trip');

      const validatedActivities = activities?.map(activity => {
        const validatedActivity = validateDatabaseResponse(ActivitySchema, activity, 'activity');
        return {
          ...validatedActivity,
          type: validatedActivity.type || 'activity',
          metadata: validatedActivity.metadata || {},
          currency: validatedActivity.currency || 'USD',
          attachments: validatedActivity.attachments || [],
        };
      }) || [];

      const completeTrip: TripWithActivities = {
        ...validatedTrip,
        status: validatedTrip.status || 'draft',
        visibility: validatedTrip.visibility || 'private',
        metadata: validatedTrip.metadata || {},
        tags: validatedTrip.tags || [],
        budget_currency: validatedTrip.budget_currency || 'USD',
        activities: validatedActivities,
      };
      
      return completeTrip;
    } catch (error: unknown) { 
      if (error instanceof Error && getErrorMessage(error).includes('End date must be after start date')) {
        throw error;
      }
      const errorDetails = handleSupabaseError(error);
      throw new Error(`Failed to update trip: ${errorDetails.message}`);
    }
  }

  async deleteTrip(tripId: string, userId: string): Promise<boolean> {
    const supabase = getSupabaseClient();
    
    try {
      // Delete trip (activities will cascade delete due to foreign key)
      const { error } = await supabase
        .from(TABLES.TRIPS)
        .delete()
        .eq('id', tripId)
        .eq('user_id', userId);

      if (error) {
        if ((error as { code?: string }).code === 'PGRST116') {
          return false; // Not found
        }
        const errorDetails = handleSupabaseError(error);
        throw new Error(`Failed to delete trip: ${errorDetails.message}`);
      }

      return true;
    } catch (error: unknown) {
      const errorDetails = handleSupabaseError(error);
      throw new Error(`Failed to delete trip: ${errorDetails.message}`);
    }
  }

  async incrementTripViews(tripId: string): Promise<void> {
    const supabase = getSupabaseClient();
    
    try {
      // Use atomic RPC function for thread-safe view increment
      // The migration 003_add_missing_fields.sql creates this function
      const { error } = await supabase.rpc('increment_trip_views', {
        trip_id: tripId
      });

      if (error) {
        // Log but don't throw - view tracking shouldn't break the app
        logger.error('Failed to increment trip views:', { error });
        // Note: If RPC doesn't exist, this indicates a deployment/migration issue
        // that should be fixed at the database level, not with a fallback
      }
    } catch (error) {
      // Log but don't throw - view tracking shouldn't break the app
      logger.error('Failed to increment trip views:', { error });
    }
  }
}