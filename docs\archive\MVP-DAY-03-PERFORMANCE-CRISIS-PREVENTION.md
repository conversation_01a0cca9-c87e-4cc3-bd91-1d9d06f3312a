# MVP Day 03: Performance Crisis Prevention

**Date**: [Execute Date]  
**Goal**: Implement performance optimizations before the app melts under load  
**Duration**: 6 hours  
**Critical Path**: YES - Poor performance = abandoned product

## Context & Performance Threats

### Current Performance Killers

1. **No indexes** = Full table scans on every query
2. **No pagination** = Loading 1000 trips crashes the browser
3. **No query optimization** = 5-second page loads
4. **No caching** = Database hit for every request
5. **No connection pooling** = Connection exhaustion under load
6. **No request batching** = N+1 query problems everywhere
7. **No bundle optimization** = 2MB JavaScript payload

### Real-World Scenario

```
If 100 users each have 50 trips with 20 activities:
- Current: 100,000 rows scanned per page load
- After optimization: <100 rows per query
- Performance gain: 1000x

Production Target Metrics:
- API Response: p95 < 200ms
- Time to Interactive: < 3s
- First Contentful Paint: < 1.5s
- Lighthouse Score: > 90
```

### Performance Budget

```typescript
// Enforce these limits in CI/CD
export const PERFORMANCE_BUDGET = {
  bundle: {
    main: 250_000, // 250KB main bundle
    vendor: 500_000, // 500KB vendor bundle
    total: 1_000_000, // 1MB total JavaScript
  },
  metrics: {
    fcp: 1500, // First Contentful Paint
    lcp: 2500, // Largest Contentful Paint
    tti: 3500, // Time to Interactive
    cls: 0.1, // Cumulative Layout Shift
    fid: 100, // First Input Delay
  },
  api: {
    p50: 100, // 50th percentile
    p95: 200, // 95th percentile
    p99: 500, // 99th percentile
  },
};
```

## Morning Tasks (3 hours)

### Task 1: Critical Database Indexes (1 hour)

**Create Migration**: `packages/hub/src/migrations/012_performance_indexes.sql`

```sql
-- Performance-critical indexes with advanced optimization
BEGIN;

-- User queries (most common) - Covering indexes for common queries
CREATE INDEX IF NOT EXISTS idx_trips_user_created
  ON trips(user_id, created_at DESC)
  INCLUDE (title, destination, start_date, end_date, is_public);

CREATE INDEX IF NOT EXISTS idx_trips_user_updated
  ON trips(user_id, updated_at DESC)
  INCLUDE (title, destination, start_date, end_date);

-- Public trip queries with partial index for better performance
CREATE INDEX IF NOT EXISTS idx_trips_public_published
  ON trips(is_public, published_at DESC)
  INCLUDE (title, destination, user_id, clone_count)
  WHERE is_public = true AND deleted_at IS NULL;

-- Activity queries with composite indexes
CREATE INDEX IF NOT EXISTS idx_activities_trip_position
  ON activities(trip_id, position)
  INCLUDE (name, start_time, location, day_number);

CREATE INDEX IF NOT EXISTS idx_activities_trip_day
  ON activities(trip_id, day_number, position)
  INCLUDE (name, start_time, end_time, type);

-- Full-text search with weighted ranking
CREATE INDEX IF NOT EXISTS idx_trips_search
  ON trips USING gin(
    to_tsvector('english',
      coalesce(title, '') || ' ' ||
      coalesce(description, '') || ' ' ||
      coalesce(destination, '')
    )
  );

-- Add trigram index for fuzzy search
CREATE EXTENSION IF NOT EXISTS pg_trgm;
CREATE INDEX IF NOT EXISTS idx_trips_title_trgm
  ON trips USING gin(title gin_trgm_ops);

-- Clone tracking with foreign key optimization
CREATE INDEX IF NOT EXISTS idx_clones_original
  ON trip_clones(original_trip_id, created_at DESC);

-- Activity location queries (for map bounds)
CREATE INDEX IF NOT EXISTS idx_activities_location
  ON activities USING gist(
    ll_to_earth(location_lat, location_lng)
  ) WHERE location_lat IS NOT NULL;

-- Monitoring and analytics
CREATE INDEX IF NOT EXISTS idx_activities_created
  ON activities(created_at DESC)
  WHERE deleted_at IS NULL;

-- User activity tracking
CREATE INDEX IF NOT EXISTS idx_user_activity
  ON user_sessions(user_id, last_activity DESC)
  WHERE ended_at IS NULL;

-- Query performance monitoring
CREATE TABLE IF NOT EXISTS query_stats (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  query_hash text NOT NULL,
  query_text text NOT NULL,
  execution_count bigint DEFAULT 0,
  total_time numeric DEFAULT 0,
  mean_time numeric DEFAULT 0,
  max_time numeric DEFAULT 0,
  last_executed timestamptz DEFAULT NOW(),
  created_at timestamptz DEFAULT NOW()
);

CREATE INDEX idx_query_stats_performance
  ON query_stats(mean_time DESC, execution_count DESC);

-- Advanced query analysis functions
CREATE OR REPLACE FUNCTION explain_query_detailed(query_text text)
RETURNS TABLE(
  plan_json jsonb,
  execution_time numeric,
  planning_time numeric,
  total_cost numeric,
  rows_estimate bigint
) AS $$
DECLARE
  result jsonb;
  exec_time numeric;
  plan_time numeric;
BEGIN
  -- Execute EXPLAIN ANALYZE and capture timing
  EXECUTE format('EXPLAIN (ANALYZE, BUFFERS, FORMAT JSON, VERBOSE) %s', query_text) INTO result;

  -- Extract metrics
  exec_time := (result->0->>'Execution Time')::numeric;
  plan_time := (result->0->>'Planning Time')::numeric;

  RETURN QUERY SELECT
    result,
    exec_time,
    plan_time,
    (result->0->'Plan'->>'Total Cost')::numeric,
    (result->0->'Plan'->>'Plan Rows')::bigint;
END;
$$ LANGUAGE plpgsql;

-- Index health monitoring
CREATE OR REPLACE VIEW index_usage_stats AS
SELECT
  schemaname,
  tablename,
  indexname,
  idx_scan,
  idx_tup_read,
  idx_tup_fetch,
  pg_size_pretty(pg_relation_size(indexrelid)) AS index_size,
  CASE
    WHEN idx_scan = 0 THEN 'UNUSED'
    WHEN idx_scan < 100 THEN 'RARELY_USED'
    ELSE 'ACTIVE'
  END AS usage_status
FROM pg_stat_user_indexes
ORDER BY idx_scan ASC;

-- Table bloat monitoring
CREATE OR REPLACE VIEW table_bloat AS
WITH constants AS (
  SELECT current_setting('block_size')::numeric AS bs, 23 AS hdr, 8 AS ma
),
bloat_info AS (
  SELECT
    schemaname, tablename,
    (datawidth + (hdr + ma - (CASE WHEN hdr % ma = 0 THEN ma ELSE hdr % ma END)))::numeric AS datahdr,
    (maxfracsum * (nullhdr + ma - (CASE WHEN nullhdr % ma = 0 THEN ma ELSE nullhdr % ma END))) AS nullhdr2
  FROM (
    SELECT
      schemaname, tablename, hdr, ma, bs,
      SUM((1 - null_frac) * avg_width) AS datawidth,
      MAX(null_frac) AS maxfracsum,
      hdr + (
        SELECT 1 + COUNT(*) / 8
        FROM pg_stats s2
        WHERE null_frac <> 0 AND s2.schemaname = s.schemaname AND s2.tablename = s.tablename
      ) AS nullhdr
    FROM pg_stats s, constants
    GROUP BY 1, 2, 3, 4, 5
  ) AS foo
)
SELECT
  schemaname, tablename,
  pg_size_pretty(table_bytes) AS table_size,
  ROUND(100 * (table_bytes - expected_bytes)::numeric / table_bytes, 2) AS bloat_pct
FROM (
  SELECT
    schemaname, tablename,
    pg_relation_size(schemaname||'.'||tablename) AS table_bytes,
    ceil(reltuples * (datahdr + nullhdr2 + 4 + ma)) AS expected_bytes
  FROM bloat_info
  JOIN pg_class ON tablename = relname
  JOIN pg_namespace ON pg_namespace.oid = pg_class.relnamespace AND schemaname = nspname
) AS bloat_calc
WHERE table_bytes > 0
ORDER BY bloat_pct DESC;

COMMIT;
```

**Verify Index Usage**:

```sql
-- Check if indexes are being used
EXPLAIN (ANALYZE, BUFFERS)
SELECT * FROM trips
WHERE user_id = 'some-user-id'
ORDER BY created_at DESC
LIMIT 10;

-- Should show: "Index Scan using idx_trips_user_created"
-- NOT: "Seq Scan on trips"
```

**Index Impact Testing**:

```typescript
// Test query performance
const testIndexPerformance = async () => {
  console.time('Query without index');
  await supabase
    .from('trips')
    .select('*')
    .eq('user_id', userId)
    .order('created_at', { ascending: false });
  console.timeEnd('Query without index');

  // After adding indexes...
  console.time('Query with index');
  await supabase
    .from('trips')
    .select('*')
    .eq('user_id', userId)
    .order('created_at', { ascending: false });
  console.timeEnd('Query with index');

  // Should see ~10-100x improvement
};
```

### Task 2: Implement Pagination (1 hour)

**Update API Endpoints**: `packages/hub/src/controllers/trips.controller.ts`

```typescript
interface PaginationParams {
  page?: number;
  limit?: number;
  sortBy?: 'created_at' | 'updated_at' | 'title';
  sortOrder?: 'asc' | 'desc';
}

export async function getUserTrips(req: AuthRequest, res: Response) {
  try {
    const userId = req.user!.id;

    // Parse pagination params with defaults
    const page = Math.max(1, parseInt(req.query.page as string) || 1);
    const limit = Math.min(100, Math.max(1, parseInt(req.query.limit as string) || 20));
    const sortBy = (req.query.sortBy as string) || 'created_at';
    const sortOrder = (req.query.sortOrder as string) || 'desc';
    const offset = (page - 1) * limit;

    // Get total count for pagination
    const { count } = await supabase
      .from('trips')
      .select('*', { count: 'exact', head: true })
      .eq('user_id', userId);

    // Get paginated results
    const { data: trips, error } = await supabase
      .from('trips')
      .select(
        `
        *,
        activities (
          id,
          name,
          start_time,
          location
        )
      `
      )
      .eq('user_id', userId)
      .order(sortBy, { ascending: sortOrder === 'asc' })
      .range(offset, offset + limit - 1);

    if (error) throw error;

    // Calculate pagination metadata
    const totalPages = Math.ceil((count || 0) / limit);
    const hasNextPage = page < totalPages;
    const hasPrevPage = page > 1;

    return res.json({
      success: true,
      data: {
        trips,
        pagination: {
          page,
          limit,
          total: count || 0,
          totalPages,
          hasNextPage,
          hasPrevPage,
          nextPage: hasNextPage ? page + 1 : null,
          prevPage: hasPrevPage ? page - 1 : null,
        },
      },
    });
  } catch (error) {
    console.error('Error fetching trips:', error);
    return res.status(500).json({
      success: false,
      error: 'Failed to fetch trips',
    });
  }
}

// Optimized single trip query
export async function getTrip(req: AuthRequest, res: Response) {
  try {
    const { tripId } = req.params;

    // Use row-level security + single query
    const { data: trip, error } = await supabase
      .from('trips')
      .select(
        `
        *,
        activities (
          id,
          type,
          name,
          description,
          start_time,
          end_time,
          location,
          price,
          currency,
          booking_url,
          affiliate_url,
          position,
          day_number
        )
      `
      )
      .eq('id', tripId)
      .single();

    if (error) {
      if (error.code === 'PGRST116') {
        return res.status(404).json({
          success: false,
          error: 'Trip not found',
        });
      }
      throw error;
    }

    // Sort activities by day and position
    if (trip?.activities) {
      trip.activities.sort((a, b) => {
        if (a.day_number !== b.day_number) {
          return a.day_number - b.day_number;
        }
        return a.position - b.position;
      });
    }

    return res.json({
      success: true,
      data: trip,
    });
  } catch (error) {
    console.error('Error fetching trip:', error);
    return res.status(500).json({
      success: false,
      error: 'Failed to fetch trip',
    });
  }
}
```

### Task 3: Frontend Pagination Support (1 hour)

**Update Trip List Component**: `packages/web/src/components/trips/TripList.tsx`

```typescript
import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { ChevronLeft, ChevronRight } from 'lucide-react';

interface PaginationData {
  page: number;
  limit: number;
  total: number;
  totalPages: number;
  hasNextPage: boolean;
  hasPrevPage: boolean;
}

export function TripList() {
  const [trips, setTrips] = useState<Trip[]>([]);
  const [pagination, setPagination] = useState<PaginationData>({
    page: 1,
    limit: 20,
    total: 0,
    totalPages: 0,
    hasNextPage: false,
    hasPrevPage: false
  });
  const [loading, setLoading] = useState(false);

  const fetchTrips = async (page: number = 1) => {
    setLoading(true);
    try {
      const response = await fetch(
        `/api/v1/trips?page=${page}&limit=${pagination.limit}`,
        {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        }
      );

      const data = await response.json();
      setTrips(data.data.trips);
      setPagination(data.data.pagination);
    } catch (error) {
      console.error('Failed to fetch trips:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchTrips(pagination.page);
  }, []);

  const handlePageChange = (newPage: number) => {
    fetchTrips(newPage);
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  return (
    <div className="space-y-6">
      {/* Trip Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {trips.map(trip => (
          <TripCard key={trip.id} trip={trip} />
        ))}
      </div>

      {/* Pagination Controls */}
      {pagination.totalPages > 1 && (
        <div className="flex items-center justify-between pt-6">
          <p className="text-sm text-gray-600">
            Showing {((pagination.page - 1) * pagination.limit) + 1} to{' '}
            {Math.min(pagination.page * pagination.limit, pagination.total)} of{' '}
            {pagination.total} trips
          </p>

          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => handlePageChange(pagination.page - 1)}
              disabled={!pagination.hasPrevPage || loading}
            >
              <ChevronLeft className="h-4 w-4" />
              Previous
            </Button>

            {/* Page numbers */}
            <div className="flex gap-1">
              {Array.from({ length: Math.min(5, pagination.totalPages) }, (_, i) => {
                const pageNum = i + 1;
                return (
                  <Button
                    key={pageNum}
                    variant={pageNum === pagination.page ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => handlePageChange(pageNum)}
                    disabled={loading}
                    className="w-10"
                  >
                    {pageNum}
                  </Button>
                );
              })}
              {pagination.totalPages > 5 && (
                <span className="px-2 py-1">...</span>
              )}
            </div>

            <Button
              variant="outline"
              size="sm"
              onClick={() => handlePageChange(pagination.page + 1)}
              disabled={!pagination.hasNextPage || loading}
            >
              Next
              <ChevronRight className="h-4 w-4" />
            </Button>
          </div>
        </div>
      )}

      {/* Loading skeleton */}
      {loading && (
        <div className="absolute inset-0 bg-white/50 flex items-center justify-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600" />
        </div>
      )}
    </div>
  );
}
```

## Afternoon Tasks (3 hours)

### Task 4: Advanced Query Optimization (1.5 hours)

**Optimize Heavy Queries**: `packages/hub/src/services/trips.service.ts`

```typescript
import { QueryBuilder } from '@/lib/database/query-builder';
import { performanceMonitor } from '@/lib/monitoring/performance';

// Before: N+1 query problem
async function getTripWithActivitiesBAD(tripId: string) {
  const trip = await db.query('SELECT * FROM trips WHERE id = $1', [tripId]);
  const activities = await db.query('SELECT * FROM activities WHERE trip_id = $1', [tripId]);

  for (const activity of activities) {
    // This creates N additional queries!
    activity.comments = await db.query('SELECT * FROM comments WHERE activity_id = $1', [
      activity.id,
    ]);
  }

  return { ...trip, activities };
}

// After: Single optimized query with performance monitoring
async function getTripWithActivitiesGOOD(tripId: string) {
  return performanceMonitor.track('getTripWithActivities', async () => {
    // Use query builder for optimal query construction
    const query = new QueryBuilder()
      .select([
        't.*',
        `json_agg(
          json_build_object(
            'id', a.id,
            'type', a.type,
            'name', a.name,
            'description', a.description,
            'start_time', a.start_time,
            'end_time', a.end_time,
            'location', json_build_object(
              'address', a.location_address,
              'lat', a.location_lat,
              'lng', a.location_lng
            ),
            'price', a.price,
            'currency', a.currency,
            'day_number', a.day_number,
            'position', a.position,
            'comments', a.comments
          ) ORDER BY a.day_number, a.position
        ) FILTER (WHERE a.id IS NOT NULL) as activities`,
      ])
      .from('trips t')
      .leftJoin(
        `
        LATERAL (
          SELECT 
            a.*,
            json_agg(
              json_build_object(
                'id', c.id,
                'content', c.content,
                'created_at', c.created_at,
                'user', json_build_object(
                  'id', u.id,
                  'name', u.name,
                  'avatar_url', u.avatar_url
                )
              ) ORDER BY c.created_at DESC
            ) FILTER (WHERE c.id IS NOT NULL) as comments
          FROM activities a
          LEFT JOIN comments c ON c.activity_id = a.id
          LEFT JOIN users u ON u.id = c.user_id
          WHERE a.trip_id = t.id
          GROUP BY a.id
        ) a ON true
      `
      )
      .where('t.id = $1', [tripId])
      .groupBy('t.id')
      .build();

    const result = await db.query(query.text, query.values);

    if (!result.rows[0]) {
      throw new Error('Trip not found');
    }

    // Parse and validate result
    const trip = result.rows[0];
    trip.activities = trip.activities || [];

    // Track query performance
    if (query.executionTime > 100) {
      logger.warn('Slow query detected', {
        query: 'getTripWithActivities',
        tripId,
        executionTime: query.executionTime,
      });
    }

    return trip;
  });
}

// Batch loading for multiple trips
async function getTripsWithActivitiesBatch(tripIds: string[]) {
  if (tripIds.length === 0) return [];

  // Use DataLoader pattern for batching
  return dataLoader.load(tripIds, async ids => {
    const query = `
      WITH trip_activities AS (
        SELECT 
          t.id as trip_id,
          json_agg(
            json_build_object(
              'id', a.id,
              'name', a.name,
              'start_time', a.start_time,
              'location', a.location_address,
              'day_number', a.day_number
            ) ORDER BY a.day_number, a.position
          ) FILTER (WHERE a.id IS NOT NULL) as activities
        FROM trips t
        LEFT JOIN activities a ON a.trip_id = t.id
        WHERE t.id = ANY($1)
        GROUP BY t.id
      )
      SELECT 
        t.*,
        ta.activities
      FROM trips t
      JOIN trip_activities ta ON ta.trip_id = t.id
      WHERE t.id = ANY($1)
    `;

    const result = await db.query(query, [ids]);

    // Map results back to requested order
    const tripMap = new Map(result.rows.map(row => [row.id, row]));
    return ids.map(id => tripMap.get(id));
  });
}

// Search optimization
async function searchTrips(query: string, userId: string) {
  // Use full-text search with indexes
  const { data, error } = await supabase
    .from('trips')
    .select('id, title, description, destination, created_at')
    .or(`user_id.eq.${userId},is_public.eq.true`)
    .textSearch('title,description,destination', query, {
      type: 'websearch',
      config: 'english',
    })
    .limit(20);

  return data;
}

// Dashboard stats optimization
async function getDashboardStats(userId: string) {
  // Single query with aggregates
  const { data, error } = await supabase.rpc('get_user_dashboard_stats', {
    p_user_id: userId,
  });

  return data;

  // RPC function in database:
  /*
  CREATE OR REPLACE FUNCTION get_user_dashboard_stats(p_user_id uuid)
  RETURNS json AS $$
  DECLARE
    result json;
  BEGIN
    SELECT json_build_object(
      'total_trips', COUNT(DISTINCT t.id),
      'total_activities', COUNT(a.id),
      'upcoming_activities', COUNT(a.id) FILTER (WHERE a.start_time > NOW()),
      'recent_trips', (
        SELECT json_agg(trips_data)
        FROM (
          SELECT id, title, start_date, end_date
          FROM trips
          WHERE user_id = p_user_id
          ORDER BY created_at DESC
          LIMIT 5
        ) trips_data
      )
    ) INTO result
    FROM trips t
    LEFT JOIN activities a ON a.trip_id = t.id
    WHERE t.user_id = p_user_id;
    
    RETURN result;
  END;
  $$ LANGUAGE plpgsql STABLE;
  */
}
```

### Task 5: Advanced Caching Architecture (1.5 hours)

**Redis Caching with Patterns**: `packages/hub/src/middleware/cache.middleware.ts`

```typescript
import { Redis } from '@upstash/redis';
import { Request, Response, NextFunction } from 'express';
import { createHash } from 'crypto';
import { LRUCache } from 'lru-cache';

// Multi-layer caching architecture
class CacheManager {
  private redis: Redis;
  private memoryCache: LRUCache<string, any>;
  private cacheStats = {
    hits: 0,
    misses: 0,
    errors: 0,
    bypasses: 0,
  };

  constructor() {
    this.redis = new Redis({
      url: process.env.UPSTASH_REDIS_URL!,
      token: process.env.UPSTASH_REDIS_TOKEN!,
      retry: {
        retries: 3,
        backoff: attemptNum => Math.min(attemptNum * 100, 3000),
      },
    });

    // In-memory LRU cache for hot data
    this.memoryCache = new LRUCache<string, any>({
      max: 1000,
      ttl: 1000 * 60, // 1 minute
      updateAgeOnGet: true,
      updateAgeOnHas: true,
    });
  }

  async get(key: string): Promise<any | null> {
    // Check memory cache first
    const memCached = this.memoryCache.get(key);
    if (memCached) {
      this.cacheStats.hits++;
      return memCached;
    }

    // Check Redis
    try {
      const redisCached = await this.redis.get(key);
      if (redisCached) {
        this.cacheStats.hits++;
        // Populate memory cache
        this.memoryCache.set(key, redisCached);
        return redisCached;
      }
    } catch (error) {
      this.cacheStats.errors++;
      console.error('Redis get error:', error);
    }

    this.cacheStats.misses++;
    return null;
  }

  async set(key: string, value: any, ttl: number): Promise<void> {
    // Set in memory cache
    this.memoryCache.set(key, value);

    // Set in Redis with error handling
    try {
      await this.redis.setex(key, ttl, JSON.stringify(value));
    } catch (error) {
      this.cacheStats.errors++;
      console.error('Redis set error:', error);
    }
  }

  async invalidate(pattern: string): Promise<void> {
    // Clear from memory cache
    for (const key of this.memoryCache.keys()) {
      if (key.match(pattern)) {
        this.memoryCache.delete(key);
      }
    }

    // Clear from Redis
    try {
      const keys = await this.redis.keys(pattern);
      if (keys.length > 0) {
        await this.redis.del(...keys);
        console.log(`Invalidated ${keys.length} cache entries`);
      }
    } catch (error) {
      console.error('Cache invalidation error:', error);
    }
  }

  getStats() {
    const total = this.cacheStats.hits + this.cacheStats.misses;
    return {
      ...this.cacheStats,
      hitRate: total > 0 ? (this.cacheStats.hits / total) * 100 : 0,
      memorySize: this.memoryCache.size,
      memoryCapacity: this.memoryCache.max,
    };
  }
}

const cacheManager = new CacheManager();

// Advanced cache options
interface CacheOptions {
  ttl?: number;
  key?: (req: Request) => string;
  condition?: (req: Request) => boolean;
  tags?: string[];
  staleWhileRevalidate?: number;
  vary?: string[];
}

export function cache(options: CacheOptions = {}) {
  const {
    ttl = 300,
    key = generateCacheKey,
    condition = () => true,
    tags = [],
    staleWhileRevalidate = 0,
    vary = ['Accept', 'Accept-Encoding'],
  } = options;

  return async (req: Request, res: Response, next: NextFunction) => {
    // Skip cache for non-GET requests or conditions
    if (req.method !== 'GET' || !condition(req)) {
      cacheManager.cacheStats.bypasses++;
      return next();
    }

    const cacheKey = key(req);
    const varyKey = generateVaryKey(req, vary);
    const fullKey = `${cacheKey}:${varyKey}`;

    try {
      // Check cache
      const cached = await cacheManager.get(fullKey);
      if (cached) {
        // Add cache headers
        res.setHeader('X-Cache', 'HIT');
        res.setHeader('X-Cache-Key', cacheKey);

        // Handle stale-while-revalidate
        if (staleWhileRevalidate && cached.staleAt && Date.now() > cached.staleAt) {
          res.setHeader('X-Cache-Stale', 'true');
          // Trigger background revalidation
          setImmediate(() => revalidateCache(req, fullKey, ttl));
        }

        return res.json(cached.data);
      }

      // Cache miss - intercept response
      res.setHeader('X-Cache', 'MISS');

      const originalJson = res.json;
      res.json = function (body: any) {
        // Cache successful responses
        if (res.statusCode === 200 && body?.success) {
          const cacheData = {
            data: body,
            timestamp: Date.now(),
            staleAt: staleWhileRevalidate ? Date.now() + ttl * 1000 : null,
            tags,
          };

          cacheManager.set(fullKey, cacheData, ttl + staleWhileRevalidate);
        }

        return originalJson.call(this, body);
      };

      next();
    } catch (error) {
      console.error('Cache middleware error:', error);
      next();
    }
  };
}

// Cache key generation with request fingerprinting
function generateCacheKey(req: Request): string {
  const userId = req.user?.id || 'anonymous';
  const queryHash = createHash('md5').update(JSON.stringify(req.query)).digest('hex');

  return `cache:${req.method}:${req.path}:${userId}:${queryHash}`;
}

// Vary key for content negotiation
function generateVaryKey(req: Request, varyHeaders: string[]): string {
  const varyData = varyHeaders.map(header => `${header}:${req.get(header) || 'none'}`).join(':');

  return createHash('md5').update(varyData).digest('hex');
}

// Background cache revalidation
async function revalidateCache(req: Request, cacheKey: string, ttl: number) {
  try {
    // Make internal request to refresh cache
    // This would be implemented based on your app structure
    console.log(`Revalidating cache: ${cacheKey}`);
  } catch (error) {
    console.error('Cache revalidation error:', error);
  }
}

// Cache invalidation helpers
export async function invalidateTripCache(tripId: string) {
  await cacheManager.invalidate(`cache:*:trips/${tripId}:*`);
  await cacheManager.invalidate(`cache:*:trips:*:*`); // List caches
}

export async function invalidateUserCache(userId: string) {
  await cacheManager.invalidate(`cache:*:*:${userId}:*`);
}

// Export cache stats for monitoring
export function getCacheStats() {
  return cacheManager.getStats();
}

// Cache invalidation helper
export async function invalidateCache(pattern: string) {
  try {
    const keys = await redis.keys(pattern);
    if (keys.length > 0) {
      await redis.del(...keys);
      console.log(`Invalidated ${keys.length} cache entries`);
    }
  } catch (error) {
    console.error('Cache invalidation error:', error);
  }
}

// Usage in routes
router.get(
  '/trips',
  authenticate,
  cache({
    ttl: 60, // 1 minute for list
    key: req => `cache:trips:${req.user.id}:${req.query.page || 1}`,
  }),
  getUserTrips
);

router.get(
  '/trips/:tripId',
  authenticate,
  verifyTripOwnership,
  cache({
    ttl: 300, // 5 minutes for single trip
    key: req => `cache:trip:${req.params.tripId}`,
  }),
  getTrip
);

// Invalidate on updates
router.put('/trips/:tripId', authenticate, verifyTripOwnership, async (req, res, next) => {
  await updateTrip(req, res);
  // Invalidate related caches
  await invalidateCache(`cache:trip:${req.params.tripId}`);
  await invalidateCache(`cache:trips:${req.user.id}:*`);
});
```

## Performance Testing & Monitoring

### Load Test Suite: `packages/hub/src/tests/load-test.js`

```javascript
import http from 'k6/http';
import { check, sleep, group } from 'k6';
import { Counter, Rate, Trend } from 'k6/metrics';
import { randomItem } from 'https://jslib.k6.io/k6-utils/1.2.0/index.js';

// Custom metrics
const apiErrors = new Counter('api_errors');
const cacheHits = new Rate('cache_hits');
const dbQueryTime = new Trend('db_query_time');

export const options = {
  scenarios: {
    // Smoke test
    smoke: {
      executor: 'constant-vus',
      vus: 1,
      duration: '1m',
      tags: { test_type: 'smoke' },
    },
    // Average load test
    average_load: {
      executor: 'ramping-vus',
      startVUs: 0,
      stages: [
        { duration: '5m', target: 100 },
        { duration: '10m', target: 100 },
        { duration: '5m', target: 0 },
      ],
      gracefulRampDown: '30s',
      tags: { test_type: 'average' },
    },
    // Stress test
    stress: {
      executor: 'ramping-vus',
      startVUs: 0,
      stages: [
        { duration: '2m', target: 100 },
        { duration: '5m', target: 200 },
        { duration: '2m', target: 300 },
        { duration: '5m', target: 400 },
        { duration: '2m', target: 0 },
      ],
      gracefulRampDown: '30s',
      tags: { test_type: 'stress' },
    },
    // Spike test
    spike: {
      executor: 'ramping-vus',
      startVUs: 0,
      stages: [
        { duration: '10s', target: 50 },
        { duration: '30s', target: 50 },
        { duration: '10s', target: 500 },
        { duration: '3m', target: 500 },
        { duration: '10s', target: 50 },
        { duration: '30s', target: 50 },
        { duration: '10s', target: 0 },
      ],
      tags: { test_type: 'spike' },
    },
  },
  thresholds: {
    // API response time thresholds
    'http_req_duration{endpoint:trip_list}': ['p(95)<200', 'p(99)<500'],
    'http_req_duration{endpoint:trip_detail}': ['p(95)<150', 'p(99)<300'],
    'http_req_duration{endpoint:search}': ['p(95)<300', 'p(99)<700'],

    // Error rate thresholds
    http_req_failed: ['rate<0.05'], // Less than 5% errors
    api_errors: ['count<100'],

    // Cache performance
    cache_hits: ['rate>0.7'], // At least 70% cache hit rate

    // Database performance
    db_query_time: ['p(95)<50', 'p(99)<100'],
  },
};

const BASE_URL = 'http://localhost:3001';
const AUTH_TOKEN = __ENV.AUTH_TOKEN;

// Test data
const searchTerms = ['Paris', 'Tokyo', 'Rome', 'Beach', 'Mountain'];
const pageSizes = [10, 20, 50];

export function setup() {
  // Warm up cache
  const warmupRequests = 10;
  for (let i = 0; i < warmupRequests; i++) {
    http.get(`${BASE_URL}/api/v1/trips?page=1&limit=20`, {
      headers: { Authorization: `Bearer ${AUTH_TOKEN}` },
    });
  }
  sleep(1);
}

export default function () {
  const headers = {
    Authorization: `Bearer ${AUTH_TOKEN}`,
    'Content-Type': 'application/json',
  };

  group('Trip List Operations', () => {
    const page = Math.floor(Math.random() * 10) + 1;
    const limit = randomItem(pageSizes);

    const response = http.get(`${BASE_URL}/api/v1/trips?page=${page}&limit=${limit}`, {
      headers,
      tags: { endpoint: 'trip_list' },
    });

    check(response, {
      'trip list status is 200': r => r.status === 200,
      'trip list has data': r => r.json('data.trips') !== null,
      'pagination info present': r => r.json('data.pagination') !== null,
    });

    // Check cache header
    const cacheHeader = response.headers['X-Cache'];
    cacheHits.add(cacheHeader === 'HIT');

    // Extract trips for detail tests
    const trips = response.json('data.trips') || [];

    if (trips.length > 0) {
      sleep(randomBetween(0.5, 2));

      // Test trip detail
      const randomTrip = randomItem(trips);
      const detailResponse = http.get(`${BASE_URL}/api/v1/trips/${randomTrip.id}`, {
        headers,
        tags: { endpoint: 'trip_detail' },
      });

      check(detailResponse, {
        'trip detail status is 200': r => r.status === 200,
        'trip has activities': r => Array.isArray(r.json('data.activities')),
      });
    }
  });

  sleep(randomBetween(1, 3));

  group('Search Operations', () => {
    const searchTerm = randomItem(searchTerms);

    const response = http.get(`${BASE_URL}/api/v1/trips/search?q=${searchTerm}`, {
      headers,
      tags: { endpoint: 'search' },
    });

    check(response, {
      'search status is 200': r => r.status === 200,
      'search returns results': r => Array.isArray(r.json('data')),
    });
  });

  sleep(randomBetween(2, 5));
}

function randomBetween(min, max) {
  return Math.random() * (max - min) + min;
}

export function handleSummary(data) {
  return {
    stdout: textSummary(data, { indent: ' ', enableColors: true }),
    'summary.json': JSON.stringify(data),
    'summary.html': htmlReport(data),
  };
}
```

### Artillery Test Configuration: `artillery.yml`

```yaml
config:
  target: 'http://localhost:3001'
  phases:
    - duration: 60
      arrivalRate: 5
      name: 'Warm up'
    - duration: 300
      arrivalRate: 50
      name: 'Sustained load'
    - duration: 60
      arrivalRate: 100
      name: 'Peak load'
  processor: './load-test-processor.js'
  variables:
    authToken: '{{ $processEnvironment.AUTH_TOKEN }}'

scenarios:
  - name: 'Browse trips'
    weight: 60
    flow:
      - get:
          url: '/api/v1/trips?page={{ $randomNumber(1, 10) }}'
          headers:
            Authorization: 'Bearer {{ authToken }}'
          capture:
            - json: '$.data.trips[0].id'
              as: 'tripId'
      - think: 2
      - get:
          url: '/api/v1/trips/{{ tripId }}'
          headers:
            Authorization: 'Bearer {{ authToken }}'
          ifTrue: 'tripId'

  - name: 'Search trips'
    weight: 30
    flow:
      - get:
          url: '/api/v1/trips/search?q={{ $randomString(5) }}'
          headers:
            Authorization: 'Bearer {{ authToken }}'
      - think: 3

  - name: 'Create and update trip'
    weight: 10
    flow:
      - post:
          url: '/api/v1/trips'
          headers:
            Authorization: 'Bearer {{ authToken }}'
            Content-Type: 'application/json'
          json:
            title: 'Load test trip {{ $randomNumber(1000, 9999) }}'
            startDate: '{{ $randomDate() }}'
            endDate: '{{ $randomDate() }}'
            destination: 'Test City'
          capture:
            - json: '$.data.id'
              as: 'newTripId'
      - think: 1
      - put:
          url: '/api/v1/trips/{{ newTripId }}'
          headers:
            Authorization: 'Bearer {{ authToken }}'
            Content-Type: 'application/json'
          json:
            title: 'Updated load test trip'
          ifTrue: 'newTripId'
```

**Run Load Test**:

```bash
# Install k6
brew install k6

# Run test
AUTH_TOKEN="your-test-token" k6 run packages/hub/src/tests/load-test.js
```

## Performance Monitoring Stack

### Prometheus Integration: `packages/hub/src/monitoring/metrics.ts`

```typescript
import { register, Counter, Histogram, Gauge, Summary } from 'prom-client';
import { Request, Response, NextFunction } from 'express';
import os from 'os';

// Initialize Prometheus metrics
const httpRequestDuration = new Histogram({
  name: 'http_request_duration_seconds',
  help: 'Duration of HTTP requests in seconds',
  labelNames: ['method', 'route', 'status_code'],
  buckets: [0.001, 0.005, 0.01, 0.05, 0.1, 0.5, 1, 2, 5],
});

const httpRequestTotal = new Counter({
  name: 'http_requests_total',
  help: 'Total number of HTTP requests',
  labelNames: ['method', 'route', 'status_code'],
});

const dbQueryDuration = new Histogram({
  name: 'db_query_duration_seconds',
  help: 'Duration of database queries in seconds',
  labelNames: ['operation', 'table'],
  buckets: [0.001, 0.005, 0.01, 0.05, 0.1, 0.5],
});

const cacheHitRate = new Gauge({
  name: 'cache_hit_rate',
  help: 'Cache hit rate percentage',
  labelNames: ['cache_type'],
});

const activeConnections = new Gauge({
  name: 'active_connections',
  help: 'Number of active connections',
  labelNames: ['type'],
});

const memoryUsage = new Gauge({
  name: 'nodejs_memory_usage_bytes',
  help: 'Node.js memory usage',
  labelNames: ['type'],
});

const cpuUsage = new Gauge({
  name: 'nodejs_cpu_usage_percent',
  help: 'Node.js CPU usage percentage',
});

// Collect default metrics
import { collectDefaultMetrics } from 'prom-client';
collectDefaultMetrics({ register });

// Performance monitoring middleware
export function performanceMonitoring(req: Request, res: Response, next: NextFunction) {
  const start = process.hrtime.bigint();
  const startCpuUsage = process.cpuUsage();

  // Track active connections
  activeConnections.inc({ type: 'http' });

  res.on('finish', () => {
    const duration = Number(process.hrtime.bigint() - start) / 1e9;
    const route = req.route?.path || req.path || 'unknown';
    const statusCode = res.statusCode.toString();

    // Record metrics
    httpRequestDuration.observe({ method: req.method, route, status_code: statusCode }, duration);

    httpRequestTotal.inc({
      method: req.method,
      route,
      status_code: statusCode,
    });

    // Track slow requests
    if (duration > 1) {
      logger.warn('Slow request detected', {
        method: req.method,
        route,
        duration,
        query: req.query,
        userAgent: req.get('user-agent'),
      });
    }

    // Update active connections
    activeConnections.dec({ type: 'http' });

    // CPU usage
    const endCpuUsage = process.cpuUsage(startCpuUsage);
    const totalCpuUsage = (endCpuUsage.user + endCpuUsage.system) / 1e6;
    cpuUsage.set((totalCpuUsage / duration) * 100);
  });

  next();
}

// Database query monitoring
export function monitorQuery(operation: string, table: string) {
  const end = dbQueryDuration.startTimer({ operation, table });

  return {
    end: () => end(),
    error: () => {
      end();
      dbQueryErrors.inc({ operation, table });
    },
  };
}

// Memory monitoring
setInterval(() => {
  const mem = process.memoryUsage();
  memoryUsage.set({ type: 'rss' }, mem.rss);
  memoryUsage.set({ type: 'heapTotal' }, mem.heapTotal);
  memoryUsage.set({ type: 'heapUsed' }, mem.heapUsed);
  memoryUsage.set({ type: 'external' }, mem.external);
  memoryUsage.set({ type: 'arrayBuffers' }, mem.arrayBuffers);
}, 10000);

// Export metrics endpoint
export function metricsEndpoint(req: Request, res: Response) {
  res.set('Content-Type', register.contentType);
  register.metrics().then(data => {
    res.end(data);
  });
}

// Grafana dashboard config
export const grafanaDashboard = {
  dashboard: {
    title: 'TravelViz Performance',
    panels: [
      {
        title: 'Request Rate',
        targets: [
          {
            expr: 'rate(http_requests_total[5m])',
            legendFormat: '{{method}} {{route}}',
          },
        ],
      },
      {
        title: 'Response Time (p95)',
        targets: [
          {
            expr: 'histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m]))',
            legendFormat: '{{route}}',
          },
        ],
      },
      {
        title: 'Error Rate',
        targets: [
          {
            expr: 'rate(http_requests_total{status_code=~"5.."}[5m])',
            legendFormat: '{{method}} {{route}}',
          },
        ],
      },
      {
        title: 'Database Query Time',
        targets: [
          {
            expr: 'histogram_quantile(0.95, rate(db_query_duration_seconds_bucket[5m]))',
            legendFormat: '{{operation}} {{table}}',
          },
        ],
      },
      {
        title: 'Cache Hit Rate',
        targets: [
          {
            expr: 'cache_hit_rate',
            legendFormat: '{{cache_type}}',
          },
        ],
      },
      {
        title: 'Memory Usage',
        targets: [
          {
            expr: 'nodejs_memory_usage_bytes',
            legendFormat: '{{type}}',
          },
        ],
      },
    ],
  },
};
```

### Frontend Performance Monitoring: `packages/web/src/lib/performance.ts`

```typescript
import { getCLS, getFID, getLCP, getFCP, getTTFB } from 'web-vitals';

interface PerformanceMetrics {
  cls: number | null;
  fid: number | null;
  lcp: number | null;
  fcp: number | null;
  ttfb: number | null;
  bundleSize: number;
  cacheHits: number;
  cacheMisses: number;
}

class PerformanceMonitor {
  private metrics: PerformanceMetrics = {
    cls: null,
    fid: null,
    lcp: null,
    fcp: null,
    ttfb: null,
    bundleSize: 0,
    cacheHits: 0,
    cacheMisses: 0,
  };

  constructor() {
    this.initWebVitals();
    this.monitorResourceTiming();
    this.setupBundleSizeTracking();
  }

  private initWebVitals() {
    getCLS(metric => {
      this.metrics.cls = metric.value;
      this.reportMetric('CLS', metric.value);
    });

    getFID(metric => {
      this.metrics.fid = metric.value;
      this.reportMetric('FID', metric.value);
    });

    getLCP(metric => {
      this.metrics.lcp = metric.value;
      this.reportMetric('LCP', metric.value);
    });

    getFCP(metric => {
      this.metrics.fcp = metric.value;
      this.reportMetric('FCP', metric.value);
    });

    getTTFB(metric => {
      this.metrics.ttfb = metric.value;
      this.reportMetric('TTFB', metric.value);
    });
  }

  private monitorResourceTiming() {
    if ('PerformanceObserver' in window) {
      const observer = new PerformanceObserver(list => {
        for (const entry of list.getEntries()) {
          if (entry.entryType === 'resource') {
            this.trackResourceLoad(entry as PerformanceResourceTiming);
          }
        }
      });

      observer.observe({ entryTypes: ['resource'] });
    }
  }

  private trackResourceLoad(entry: PerformanceResourceTiming) {
    // Track cache performance
    if (entry.transferSize === 0 && entry.decodedBodySize > 0) {
      this.metrics.cacheHits++;
    } else {
      this.metrics.cacheMisses++;
    }

    // Track slow resources
    if (entry.duration > 1000) {
      console.warn('Slow resource:', {
        name: entry.name,
        duration: entry.duration,
        type: entry.initiatorType,
      });
    }
  }

  private setupBundleSizeTracking() {
    // Track JavaScript bundle sizes
    const scripts = document.querySelectorAll('script[src]');
    let totalSize = 0;

    scripts.forEach(script => {
      const src = script.getAttribute('src');
      if (src) {
        // In production, this would fetch actual sizes
        // For now, we'll use a placeholder
        totalSize += 100000; // Placeholder
      }
    });

    this.metrics.bundleSize = totalSize;

    // Check against budget
    if (totalSize > PERFORMANCE_BUDGET.bundle.total) {
      console.error('Bundle size exceeds budget!', {
        actual: totalSize,
        budget: PERFORMANCE_BUDGET.bundle.total,
      });
    }
  }

  private reportMetric(name: string, value: number) {
    // Send to analytics
    if (typeof window !== 'undefined' && window.gtag) {
      window.gtag('event', 'web_vitals', {
        event_category: 'Performance',
        event_label: name,
        value: Math.round(value),
        non_interaction: true,
      });
    }

    // Send to monitoring service
    fetch('/api/metrics', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        metric: name,
        value,
        timestamp: Date.now(),
        url: window.location.href,
        userAgent: navigator.userAgent,
      }),
    }).catch(() => {
      // Silently fail metric reporting
    });
  }

  public getMetrics(): PerformanceMetrics {
    return { ...this.metrics };
  }

  public getCacheHitRate(): number {
    const total = this.metrics.cacheHits + this.metrics.cacheMisses;
    return total > 0 ? (this.metrics.cacheHits / total) * 100 : 0;
  }
}

// Initialize performance monitoring
export const performanceMonitor = new PerformanceMonitor();

// Export for use in components
export function usePerformanceMetrics() {
  return performanceMonitor.getMetrics();
}
```

## Extended Thinking Prompts

For performance optimization decisions:

```
Current performance issue: [describe slowness]
Query/Operation: [show current implementation]
Data volume: [expected rows/operations]
What's the optimal approach considering both performance and maintainability?
```

## Performance Checklist

### Database Performance

- [ ] All foreign keys have indexes
- [ ] User query indexes created
- [ ] Full-text search index added
- [ ] EXPLAIN shows index usage
- [ ] No sequential scans on large tables

### API Performance

- [ ] Pagination implemented
- [ ] Default limit of 20 items
- [ ] Max limit of 100 enforced
- [ ] Single query for related data
- [ ] No N+1 query problems

### Caching Layer

- [ ] Redis configured (Upstash)
- [ ] GET requests cached
- [ ] Cache invalidation on updates
- [ ] TTL appropriate for data type
- [ ] Cache keys include user context

### Load Testing

- [ ] 100 concurrent users supported
- [ ] 95% requests under 500ms
- [ ] Error rate under 10%
- [ ] No memory leaks
- [ ] Graceful degradation

## Definition of Done

✅ Performance Metrics:

```bash
# Run load test
k6 run load-test.js
# Check: ✓ http_req_duration.........: p(95)<500ms
# Check: ✓ http_req_failed..........: <10%

# Check index usage
psql -c "SELECT schemaname,tablename,indexname FROM pg_indexes WHERE schemaname='public';"
# Should show all our performance indexes

# Test pagination
curl "http://localhost:3001/api/v1/trips?page=1&limit=5"
# Should return exactly 5 trips with pagination metadata
```

✅ Manual Performance Verification:

1. Load trip list - should appear instantly
2. Navigate between pages - smooth transitions
3. Load single trip - under 300ms
4. Search trips - results in <500ms
5. No UI freezing with 100+ trips

## Next Day Preview

Days 4-5: AI Import Feature (The Magic)

- Import UI with wizard flow
- Real-time parsing feedback
- Preview with timeline/map
- Error handling and recovery

## Notes

- Performance is a feature, not a nice-to-have
- Users abandon slow apps in 3 seconds
- Monitor everything, optimize the critical path
- Caching solves many problems but creates cache invalidation challenges
- When in doubt, measure don't guess
- Small optimizations compound into huge improvements
