// Direct test of AI processing without authentication
// This will help us identify the exact issue with the AI parsing

const { getAIParserService } = require('./src/services/ai-parser.service');

async function testAIProcessingDirect() {
  console.log('🔍 TESTING AI PROCESSING DIRECTLY');
  console.log('Time:', new Date().toISOString());
  console.log('');

  try {
    // Get the AI parser service
    const aiParserService = getAIParserService();
    
    // Test content (same as the problematic session)
    const testContent = `Your Complete 15-Day European Adventure: London, Madrid, Lisbon & Porto

Pre-Trip Essential Information

Why This Route Makes Sense
Your journey follows a logical progression from London's historic grandeur through Madrid's vibrant culture, continuing to Lisbon's coastal charm, and culminating in Porto's authentic Portuguese soul.

LONDON: July 23-25 (2 Nights)

Day 1 - Thursday, July 23: Arrival & Sky Garden Sunset

Why Sky Garden Over London Eye
While tourists pay £30 for the slow-moving London Eye, locals know Sky Garden offers superior 360° views absolutely FREE.

Getting There from Hotel
Walking route (15 mins): Exit hotel → Cross Blackfriars Bridge → Turn right on Upper Thames Street

Sky Garden Experience
Levels: 35th floor main garden, 36th floor restaurant, 37th floor viewing gallery
Best photo spots: South terrace for Tower Bridge, North for St Paul's
Time needed: 60-90 minutes minimum

Day 2 - Friday, July 24: Markets & Culture

Morning: Borough Market Masters Class
Strategic Eating Route:
1. Start: Monmouth Coffee - flat white £3.50
2. Breakfast: Maria's Market Cafe - full English £12
3. Must-buys: Kappacasein toasted cheese sandwich £7

Afternoon: South Bank Culture Walk
11:30am: Exit market via cathedral exit
11:45am: Southwark Cathedral (free, stunning Gothic)
12:15pm: Globe Theatre tour (£17)
1:30pm: Tate Modern (free, world-class contemporary art)

MADRID: July 25-29 (4 Nights)

Day 3 - Saturday, July 25: Travel & Arrival
Morning departure from London to Madrid
Afternoon: Check into hotel, explore nearby area
Evening: Traditional tapas dinner

Day 4 - Sunday, July 26: Art Triangle
Morning: Prado Museum (€15)
Afternoon: Reina Sofia Museum (€12)
Evening: Retiro Park sunset

LISBON: July 29-Aug 1 (3 Nights)

Day 7 - Tuesday, July 29: Travel & Arrival
Travel from Madrid to Lisbon
Afternoon: Explore Alfama district
Evening: Fado dinner (€45)

Day 8 - Wednesday, July 30: Historic Lisbon
Morning: Jerónimos Monastery (€10)
Afternoon: Belém Tower (€6)
Evening: Sunset at Miradouro da Senhora do Monte

PORTO: Aug 1-6 (5 Nights)

Day 10 - Friday, Aug 1: Travel & Arrival
Travel from Lisbon to Porto
Afternoon: Explore Ribeira district
Evening: Port wine tasting (€20)

Day 11 - Saturday, Aug 2: Porto Highlights
Morning: Livraria Lello bookstore (€5)
Afternoon: Clérigos Tower (€6)
Evening: Sunset at Dom Luís I Bridge

Budget Summary:
- Accommodation: €1,200
- Transportation: €400
- Food & Dining: €800
- Activities & Tours: €350
Total Estimated Budget: €2,750`;

    console.log('1. Creating parse session...');
    const sessionId = await aiParserService.createParseSession(
      testContent, 
      'gemini', 
      '697b40b3-42d7-4b32-ad49-0220c2313643' // User ID from the problematic session
    );
    
    console.log('✅ Session created:', sessionId);
    console.log('');

    // Monitor the session for 3 minutes
    console.log('2. Monitoring session for 3 minutes...');
    const startTime = Date.now();
    const maxWaitTime = 180000; // 3 minutes
    let lastStatus = null;
    let checkCount = 0;

    while (Date.now() - startTime < maxWaitTime) {
      try {
        checkCount++;
        const session = await aiParserService.getSession(sessionId);
        
        if (!session) {
          console.log(`[${new Date().toISOString()}] Check ${checkCount}: Session not found`);
          break;
        }

        const currentStatus = session.status;
        if (currentStatus !== lastStatus) {
          console.log(`[${new Date().toISOString()}] Check ${checkCount}: Status changed: ${lastStatus} → ${currentStatus}`);
          lastStatus = currentStatus;
          
          if (currentStatus === 'complete') {
            console.log('✅ Session completed successfully!');
            console.log('Final session data:', {
              id: session.id,
              status: session.status,
              hasResult: !!session.result,
              error: session.error,
              startedAt: session.startedAt,
              completedAt: session.completedAt
            });
            break;
          } else if (currentStatus === 'failed') {
            console.log('❌ Session failed');
            console.log('Error:', session.error);
            break;
          }
        } else {
          console.log(`[${new Date().toISOString()}] Check ${checkCount}: Status unchanged: ${currentStatus}`);
        }

        // Wait 10 seconds before next check
        await new Promise(resolve => setTimeout(resolve, 10000));
      } catch (error) {
        console.log(`[${new Date().toISOString()}] Check ${checkCount}: Error checking status:`, error.message);
        await new Promise(resolve => setTimeout(resolve, 10000));
      }
    }

    if (Date.now() - startTime >= maxWaitTime) {
      console.log('❌ Session did not complete within 3 minutes - ISSUE REPRODUCED');
      console.log('Last known status:', lastStatus);
      console.log('Total checks performed:', checkCount);
    }

  } catch (error) {
    console.error('❌ Error during direct testing:', error.message);
    console.error('Stack trace:', error.stack);
  }
}

// Run the test
testAIProcessingDirect().catch(console.error);
