#!/usr/bin/env node

/**
 * Clear stuck PDF import sessions
 */

const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: './packages/hub/.env.local' });

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('❌ Missing Supabase configuration');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function clearStuckSessions() {
  console.log('🧹 Clearing stuck PDF import sessions...');
  
  try {
    // Find stuck sessions (processing for more than 10 minutes)
    const tenMinutesAgo = new Date(Date.now() - 10 * 60 * 1000).toISOString();
    
    const { data: stuckSessions, error: findError } = await supabase
      .from('ai_import_logs')
      .select('id, created_at, import_status')
      .eq('import_status', 'processing')
      .lt('created_at', tenMinutesAgo);
    
    if (findError) {
      console.error('❌ Error finding stuck sessions:', findError);
      return;
    }
    
    if (!stuckSessions || stuckSessions.length === 0) {
      console.log('✅ No stuck sessions found');
      return;
    }
    
    console.log(`📋 Found ${stuckSessions.length} stuck sessions:`);
    stuckSessions.forEach(session => {
      const age = Math.round((Date.now() - new Date(session.created_at).getTime()) / 1000 / 60);
      console.log(`  - ${session.id} (${age} minutes old)`);
    });
    
    // Update stuck sessions to failed
    const { error: updateError } = await supabase
      .from('ai_import_logs')
      .update({ 
        import_status: 'failed', 
        error_message: 'Session timeout - cleaned up by script',
        updated_at: new Date().toISOString()
      })
      .eq('import_status', 'processing')
      .lt('created_at', tenMinutesAgo);
    
    if (updateError) {
      console.error('❌ Error updating stuck sessions:', updateError);
      return;
    }
    
    console.log(`✅ Successfully cleared ${stuckSessions.length} stuck sessions`);
    
  } catch (error) {
    console.error('❌ Unexpected error:', error);
  }
}

// Run the cleanup
clearStuckSessions().then(() => {
  console.log('🏁 Cleanup completed');
  process.exit(0);
}).catch(error => {
  console.error('💥 Cleanup failed:', error);
  process.exit(1);
});
