import { test, expect } from '@playwright/test';
import path from 'path';

const TEST_USER = {
  email: '<EMAIL>',
  password: 'Flaremmk123!'
};

const PDF_FILE = path.join(__dirname, '..', 'Updated 15-Day European Travel Itinerary_ London, Madrid, Lisbon and Porto.pdf');

const AI_MODELS = [
  { id: 'openrouter/cypher-alpha:free', name: 'Cypher Alpha' },
  { id: 'deepseek/deepseek-chat-v3-0324:free', name: 'DeepSeek Chat v3' },
  { id: 'deepseek/deepseek-r1-0528:free', name: 'DeepSeek R1' },
  { id: 'google/gemini-2.0-flash-001', name: 'Gemini 2.0 Flash' },
  { id: 'google/gemini-2.5-flash-preview-05-20', name: 'Gemini 2.5 Flash Preview' }
];

test.describe('AI Import Feature Tests', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to login page
    await page.goto('http://localhost:3000/login');
    
    // Login
    await page.fill('input[name="email"]', TEST_USER.email);
    await page.fill('input[name="password"]', TEST_USER.password);
    await page.click('button[type="submit"]');
    
    // Wait for navigation to dashboard
    await page.waitForURL('http://localhost:3000/dashboard');
  });

  for (const model of AI_MODELS) {
    test(`Test AI Import with ${model.name}`, async ({ page }) => {
      // Navigate to import page
      await page.goto('http://localhost:3000/import');
      
      // Upload PDF file
      const fileInput = await page.locator('input[type="file"]');
      await fileInput.setInputFiles(PDF_FILE);
      
      // Select AI model if there's a dropdown
      const modelSelect = await page.locator('select[name="model"]');
      if (await modelSelect.isVisible()) {
        await modelSelect.selectOption(model.id);
      }
      
      // Click import button
      await page.click('button:has-text("Import")');
      
      // Wait for processing (with longer timeout for AI processing)
      await page.waitForLoadState('networkidle', { timeout: 60000 });
      
      // Check for success or error
      const successMessage = page.locator('.success-message');
      const errorMessage = page.locator('.error-message');
      
      if (await successMessage.isVisible({ timeout: 5000 })) {
        console.log(`✅ ${model.name}: Import successful`);
        
        // Check if trip was created
        const tripTitle = await page.locator('h1, h2').first().textContent();
        console.log(`   Trip title: ${tripTitle}`);
        
        // Count activities
        const activities = await page.locator('.activity-item, [data-testid="activity"]').count();
        console.log(`   Activities found: ${activities}`);
        
        // Take screenshot for documentation
        await page.screenshot({ 
          path: `test-results/${model.id.replace(/\//g, '-')}-success.png`,
          fullPage: true 
        });
      } else if (await errorMessage.isVisible({ timeout: 5000 })) {
        const error = await errorMessage.textContent();
        console.log(`❌ ${model.name}: Import failed - ${error}`);
        
        await page.screenshot({ 
          path: `test-results/${model.id.replace(/\//g, '-')}-error.png`,
          fullPage: true 
        });
      }
    });
  }
});

test('Visual comparison of all models', async ({ page }) => {
  // This test will navigate through all imported trips and compare them
  await page.goto('http://localhost:3000/dashboard');
  
  // Take screenshots of the trips list for comparison
  await page.screenshot({ 
    path: 'test-results/all-trips-comparison.png',
    fullPage: true 
  });
  
  console.log('📸 Screenshots saved to test-results/ directory');
});