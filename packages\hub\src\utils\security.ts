/**
 * Security utilities for timing-safe operations and other security measures
 */

import crypto from 'crypto';

/**
 * Perform timing-safe string comparison
 */
export function timingSafeEqual(a: string, b: string): boolean {
  if (a.length !== b.length) {
    // Return false but still do comparison to maintain constant time
    const bufferA = Buffer.from(a);
    const bufferB = Buffer.alloc(a.length);
    crypto.timingSafeEqual(bufferA, bufferB);
    return false;
  }
  
  const bufferA = Buffer.from(a);
  const bufferB = Buffer.from(b);
  return crypto.timingSafeEqual(bufferA, bufferB);
}

/**
 * Execute a function with timing-safe characteristics
 * Helps prevent timing attacks by ensuring consistent execution time
 */
export async function timingSafeExecute<T>(
  fn: () => Promise<T>,
  minTime: number = 100 // Minimum execution time in ms
): Promise<T> {
  const startTime = Date.now();
  
  try {
    const result = await fn();
    const elapsed = Date.now() - startTime;
    
    // Ensure minimum execution time
    if (elapsed < minTime) {
      await new Promise(resolve => setTimeout(resolve, minTime - elapsed));
    }
    
    return result;
  } catch (error) {
    const elapsed = Date.now() - startTime;
    
    // Ensure minimum execution time even on error
    if (elapsed < minTime) {
      await new Promise(resolve => setTimeout(resolve, minTime - elapsed));
    }
    
    throw error;
  }
}

/**
 * Generate a secure random token
 */
export function generateSecureToken(length: number = 32): string {
  return crypto.randomBytes(length).toString('hex');
}

/**
 * Hash a password using bcrypt-compatible algorithm
 */
export async function hashPassword(password: string): Promise<string> {
  // This is a placeholder - in production, use bcrypt
  const salt = crypto.randomBytes(16).toString('hex');
  const hash = crypto.pbkdf2Sync(password, salt, 100000, 64, 'sha512').toString('hex');
  return `${salt}:${hash}`;
}

/**
 * Verify a password against a hash
 */
export async function verifyPassword(password: string, hash: string): Promise<boolean> {
  // This is a placeholder - in production, use bcrypt
  const [salt, storedHash] = hash.split(':');
  const verifyHash = crypto.pbkdf2Sync(password, salt, 100000, 64, 'sha512').toString('hex');
  return timingSafeEqual(storedHash, verifyHash);
}

/**
 * Sanitize user input to prevent XSS
 */
export function sanitizeInput(input: string): string {
  return input
    .replace(/</g, '&lt;')
    .replace(/>/g, '&gt;')
    .replace(/"/g, '&quot;')
    .replace(/'/g, '&#x27;')
    .replace(/\//g, '&#x2F;');
}

/**
 * Validate email format
 */
export function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

/**
 * Rate limit key generator
 */
export function getRateLimitKey(identifier: string, window: string = 'minute'): string {
  const now = new Date();
  let key = identifier;
  
  switch (window) {
    case 'second':
      key += `:${now.getUTCFullYear()}${now.getUTCMonth()}${now.getUTCDate()}${now.getUTCHours()}${now.getUTCMinutes()}${now.getUTCSeconds()}`;
      break;
    case 'minute':
      key += `:${now.getUTCFullYear()}${now.getUTCMonth()}${now.getUTCDate()}${now.getUTCHours()}${now.getUTCMinutes()}`;
      break;
    case 'hour':
      key += `:${now.getUTCFullYear()}${now.getUTCMonth()}${now.getUTCDate()}${now.getUTCHours()}`;
      break;
    case 'day':
      key += `:${now.getUTCFullYear()}${now.getUTCMonth()}${now.getUTCDate()}`;
      break;
  }
  
  return key;
}