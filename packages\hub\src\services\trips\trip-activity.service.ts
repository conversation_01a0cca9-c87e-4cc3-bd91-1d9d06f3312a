import {
  getSupabaseClient,
  handleSupabaseError,
  TABLES,
  Activity,
  ActivitySchema,
  validateDatabaseResponse
} from '../../lib/supabase';
import { getErrorMessage } from '../../utils/error-handler';
import { sanitizeActivityData } from '../../utils/sanitizer';
import { logger } from '../../utils/logger';
import { affiliateLinkInjector } from '../affiliateLinkInjector.service';
import type { CreateActivityData } from './types';
import { validateDateRange } from '@travelviz/shared';

// Cleaning functions no longer needed - Zod schemas now handle null values

export class TripActivityService {
  async addActivityToTrip(
    tripId: string, 
    userId: string, 
    activityData: CreateActivityData
  ): Promise<Activity> {
    const supabase = getSupabaseClient();
    
    try {
      // First verify the user owns the trip
      const { data: trip, error: tripError } = await supabase
        .from(TABLES.TRIPS)
        .select('id')
        .eq('id', tripId)
        .eq('user_id', userId)
        .single();

      if (tripError || !trip) {
        throw new Error('Trip not found or unauthorized');
      }

      // Validate times if both are provided
      if (activityData.startTime && activityData.endTime) {
        validateDateRange(activityData.startTime, activityData.endTime);
      }

      // Sanitize activity data to prevent XSS attacks
      const sanitizedData = sanitizeActivityData(activityData);
      
      // Inject affiliate link if applicable
      if (sanitizedData.bookingUrl && sanitizedData.price) {
        const tempActivity = {
          id: '',
          trip_id: tripId,
          title: sanitizedData.title,
          type: sanitizedData.type || 'activity',
          price: sanitizedData.price,
          booking_url: sanitizedData.bookingUrl,
          description: sanitizedData.description,
        } as Activity;
        
        const [processedActivity] = await affiliateLinkInjector.injectAffiliateLinks([tempActivity]);
        
        if (processedActivity.affiliate_url) {
          logger.info(`Injected affiliate link for activity: ${sanitizedData.title}`);
          sanitizedData.bookingUrl = processedActivity.affiliate_url;
        }
      }
      
      // Use PostgreSQL function to get next position efficiently
      const { data: positionResult, error: positionError } = await supabase
        .rpc('get_next_activity_position', { 
          trip_id_param: tripId 
        });

      let nextPosition: number;
      if (positionError) {
        // Fallback: Calculate next position manually if RPC doesn't exist
        const { data: existingActivities } = await supabase
          .from(TABLES.ACTIVITIES)
          .select('position')
          .eq('trip_id', tripId)
          .order('position', { ascending: false })
          .limit(1);
        
        nextPosition = existingActivities && existingActivities.length > 0 
          ? (existingActivities[0].position + 1) 
          : 0;
      } else {
        nextPosition = positionResult || 0;
      }
      
      // Create activity
      const { data: activity, error } = await supabase
        .from(TABLES.ACTIVITIES)
        .insert({
          trip_id: tripId,
          title: sanitizedData.title,
          description: sanitizedData.description || null,
          type: sanitizedData.type || 'activity',
          start_time: sanitizedData.startTime || null,
          end_time: sanitizedData.endTime || null,
          location: sanitizedData.location || null,
          location_lat: sanitizedData.locationLat || null,
          location_lng: sanitizedData.locationLng || null,
          price: sanitizedData.price || null,
          currency: sanitizedData.currency || 'USD',
          booking_reference: sanitizedData.bookingReference || null,
          booking_url: sanitizedData.bookingUrl || null,
          notes: sanitizedData.notes || null,
          attachments: sanitizedData.attachments || [],
          position: nextPosition,
        })
        .select()
        .single();

      if (error) {
        const errorDetails = handleSupabaseError(error);
        throw new Error(`Failed to create activity: ${errorDetails.message}`);
      }

      // Log the raw response for debugging
      logger.debug('Activity created, raw response', { 
        activityId: activity?.id,
        hasPosition: activity && 'position' in activity,
        positionValue: activity?.position,
        allFields: Object.keys(activity || {})
      });

      // Validate response
      const validatedActivity = validateDatabaseResponse(ActivitySchema, activity, 'activity');
      
      // Ensure all required fields have values
      return {
        ...validatedActivity,
        type: validatedActivity.type || 'activity',
        metadata: validatedActivity.metadata || {},
        currency: validatedActivity.currency || 'USD',
        attachments: validatedActivity.attachments || [],
      };
    } catch (error: unknown) { 
      if (error instanceof Error && 
          (getErrorMessage(error).includes('Trip not found') || 
           error.message.includes('End date must be after'))) {
        throw error;
      }
      const errorDetails = handleSupabaseError(error);
      throw new Error(`Failed to create activity: ${errorDetails.message}`);
    }
  }

  async updateActivity(
    activityId: string, 
    userId: string, 
    updateData: Partial<CreateActivityData>
  ): Promise<Activity | null> {
    const supabase = getSupabaseClient();
    
    try {
      // First verify the user owns the trip this activity belongs to
      const { data: activityCheck, error: checkError } = await supabase
        .from(TABLES.ACTIVITIES)
        .select(`
          id,
          ${TABLES.TRIPS}!inner (
            user_id
          )
        `)
        .eq('id', activityId)
        .single();

      if (checkError || !activityCheck) {
        return null; // Not found or unauthorized
      }
      
      const tripData = (activityCheck as Record<string, unknown>)[TABLES.TRIPS];
      if (!tripData || typeof tripData !== 'object' || (tripData as Record<string, unknown>).user_id !== userId) {
        return null; // Not found or unauthorized
      }

      // Validate times if both are provided
      if (updateData.startTime && updateData.endTime) {
        validateDateRange(updateData.startTime, updateData.endTime);
      }

      // Sanitize update data to prevent XSS attacks
      const sanitizedUpdateData = sanitizeActivityData(updateData);
      
      // Inject affiliate link if bookingUrl is being updated and price exists
      let price = sanitizedUpdateData.price;
      let type = sanitizedUpdateData.type;
      
      if (sanitizedUpdateData.bookingUrl && (!price || !type)) {
        // Get current activity to check price/type if not provided in update
        const { data: currentActivity } = await supabase
          .from(TABLES.ACTIVITIES)
          .select('price, type')
          .eq('id', activityId)
          .single();
        
        price = price || currentActivity?.price;
        type = type || currentActivity?.type || 'activity';
      }
      
      if (sanitizedUpdateData.bookingUrl && price) {
        const tempActivity = {
          id: activityId,
          trip_id: '',
          title: sanitizedUpdateData.title || '',
          type: type,
          price: price,
          booking_url: sanitizedUpdateData.bookingUrl,
          description: sanitizedUpdateData.description,
        } as Activity;
        
        const [processedActivity] = await affiliateLinkInjector.injectAffiliateLinks([tempActivity]);
        
        if (processedActivity.affiliate_url) {
          logger.info(`Injected affiliate link for activity update: ${activityId}`);
          sanitizedUpdateData.bookingUrl = processedActivity.affiliate_url;
        }
      }
      
      // Prepare update data (map camelCase to snake_case)
      const dbUpdateData: Record<string, unknown> = {};
      if (sanitizedUpdateData.title !== undefined) dbUpdateData.title = sanitizedUpdateData.title;
      if (sanitizedUpdateData.description !== undefined) dbUpdateData.description = sanitizedUpdateData.description;
      if (sanitizedUpdateData.location !== undefined) dbUpdateData.location = sanitizedUpdateData.location;
      if (sanitizedUpdateData.startTime !== undefined) dbUpdateData.start_time = sanitizedUpdateData.startTime;
      if (sanitizedUpdateData.endTime !== undefined) dbUpdateData.end_time = sanitizedUpdateData.endTime;
      if (sanitizedUpdateData.type !== undefined) dbUpdateData.type = sanitizedUpdateData.type;
      if (sanitizedUpdateData.price !== undefined) dbUpdateData.price = sanitizedUpdateData.price;
      if (sanitizedUpdateData.currency !== undefined) dbUpdateData.currency = sanitizedUpdateData.currency;
      if (sanitizedUpdateData.bookingUrl !== undefined) dbUpdateData.booking_url = sanitizedUpdateData.bookingUrl;
      if (sanitizedUpdateData.bookingReference !== undefined) dbUpdateData.booking_reference = sanitizedUpdateData.bookingReference;
      if (sanitizedUpdateData.notes !== undefined) dbUpdateData.notes = sanitizedUpdateData.notes;
      if (sanitizedUpdateData.attachments !== undefined) dbUpdateData.attachments = sanitizedUpdateData.attachments;

      // Update activity
      const { data: activity, error } = await supabase
        .from(TABLES.ACTIVITIES)
        .update(dbUpdateData)
        .eq('id', activityId)
        .select()
        .single();

      if (error) {
        const errorDetails = handleSupabaseError(error);
        throw new Error(`Failed to update activity: ${errorDetails.message}`);
      }

      // Validate response
      const validatedActivity = validateDatabaseResponse(ActivitySchema, activity, 'activity');
      
      // Ensure all required fields have values
      return {
        ...validatedActivity,
        type: validatedActivity.type || 'activity',
        metadata: validatedActivity.metadata || {},
        currency: validatedActivity.currency || 'USD',
        attachments: validatedActivity.attachments || [],
      };
    } catch (error: unknown) { 
      if (error instanceof Error && getErrorMessage(error).includes('End date must be after')) {
        throw error;
      }
      const errorDetails = handleSupabaseError(error);
      throw new Error(`Failed to update activity: ${errorDetails.message}`);
    }
  }

  async deleteActivity(activityId: string, userId: string): Promise<boolean> {
    const supabase = getSupabaseClient();
    
    try {
      // First verify the user owns the trip this activity belongs to
      const { data: activityCheck, error: checkError } = await supabase
        .from(TABLES.ACTIVITIES)
        .select(`
          id,
          ${TABLES.TRIPS}!inner (
            user_id
          )
        `)
        .eq('id', activityId)
        .single();

      if (checkError || !activityCheck) {
        return false; // Not found or unauthorized
      }
      
      const tripData = (activityCheck as Record<string, unknown>)[TABLES.TRIPS];
      if (!tripData || typeof tripData !== 'object' || (tripData as Record<string, unknown>).user_id !== userId) {
        return false; // Not found or unauthorized
      }

      // Delete activity
      const { error } = await supabase
        .from(TABLES.ACTIVITIES)
        .delete()
        .eq('id', activityId);

      if (error) {
        const errorDetails = handleSupabaseError(error);
        throw new Error(`Failed to delete activity: ${errorDetails.message}`);
      }

      return true;
    } catch (error: unknown) {
      const errorDetails = handleSupabaseError(error);
      throw new Error(`Failed to delete activity: ${errorDetails.message}`);
    }
  }

  async reorderActivities(tripId: string, userId: string, orderedIds: string[]): Promise<Activity[]> {
    const supabase = getSupabaseClient();
    
    try {
      // Check trip ownership
      const { data: trip, error: tripError } = await supabase
        .from(TABLES.TRIPS)
        .select('id, user_id')
        .eq('id', tripId)
        .single();

      if (tripError) {
        throw new Error('Trip not found');
      }

      if (!trip || trip.user_id !== userId) {
        throw new Error('Unauthorized to modify this trip');
      }

      // Fetch all activities for the trip
      const { data: activities, error: activitiesError } = await supabase
        .from(TABLES.ACTIVITIES)
        .select('id, trip_id, position')
        .eq('trip_id', tripId);

      if (activitiesError) {
        throw new Error('Failed to fetch activities');
      }

      // Validate that all orderedIds belong to this trip
      const activityIds = new Set(activities?.map(a => a.id) || []);
      const invalidIds = orderedIds.filter(id => !activityIds.has(id));
      
      if (invalidIds.length > 0) {
        throw new Error('Invalid activity IDs provided');
      }

      // Prepare updates with new positions
      const updates = orderedIds.map((id, index) => ({
        id,
        trip_id: tripId,
        position: index
      }));

      // Batch update positions
      const { error: updateError } = await supabase
        .from(TABLES.ACTIVITIES)
        .upsert(updates, { onConflict: 'id' });

      if (updateError) {
        throw new Error('Failed to update activity positions');
      }

      // Fetch updated activities to return
      const { data: updatedActivities, error: fetchError } = await supabase
        .from(TABLES.ACTIVITIES)
        .select('*')
        .eq('trip_id', tripId)
        .order('position', { ascending: true });

      if (fetchError) {
        throw new Error('Failed to fetch updated activities');
      }

      // Validate and return activities
      return updatedActivities.map(activity => {
        // Ensure required fields have defaults before validation
        const activityWithDefaults = {
          ...activity,
          type: activity.type || 'activity',
          metadata: activity.metadata || {},
          currency: activity.currency || 'USD',
          attachments: activity.attachments || [],
          position: activity.position ?? 0
        };
        return validateDatabaseResponse(ActivitySchema, activityWithDefaults, 'activity');
      }) as Activity[];
    } catch (error: unknown) {
      const errorDetails = handleSupabaseError(error);
      throw new Error(errorDetails.message);
    }
  }
}