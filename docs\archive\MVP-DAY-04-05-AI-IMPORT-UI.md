# MVP Days 04-05: AI Import UI (The Magic Feature)

**Date**: [Execute Date]  
**Goal**: Build the AI conversation import that makes TravelViz irresistible  
**Duration**: 16 hours (2 days)  
**Critical Path**: YES - This IS the product differentiation

## Context & Vision

### The Magic Moment

When a user pastes their messy ChatGPT conversation and watches it transform into a beautiful, organized visual trip plan. This is our "iPhone moment".

### User Journey

1. **Paste** → Copy conversation from ChatGPT/Claude/Gemini
2. **Process** → Watch real-time AI parsing with delightful animations
3. **Preview** → See their trip visualized on timeline + map
4. **Create** → One click to save and share

## Day 4 Morning: Import Page Structure (4 hours)

### Task 1: Create Import Route (30 min)

**File**: `packages/web/src/app/import/page.tsx`

```typescript
import { Metadata } from 'next';
import { ImportWizard } from '@/components/import/ImportWizard';
import { ImportProvider } from '@/contexts/ImportContext';

export const metadata: Metadata = {
  title: 'Import AI Conversation | TravelViz',
  description: 'Transform your ChatGPT, Claude, or Gemini travel conversation into a visual itinerary',
};

export default function ImportPage() {
  return (
    <ImportProvider>
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50">
        <div className="container mx-auto px-4 py-8">
          <ImportWizard />
        </div>
      </div>
    </ImportProvider>
  );
}
```

### Task 2: Import Context Provider (1 hour)

**File**: `packages/web/src/contexts/ImportContext.tsx`

```typescript
'use client';

import { createContext, useContext, useState, ReactNode } from 'react';

export type ImportStep = 'input' | 'parsing' | 'preview' | 'creating';
export type ImportSource = 'paste' | 'file' | 'url';

interface ParseProgress {
  step: string;
  progress: number;
  message: string;
}

interface ParsedTrip {
  title: string;
  description?: string;
  startDate: Date;
  endDate: Date;
  destination: string;
  activities: ParsedActivity[];
  lowConfidenceItems: LowConfidenceItem[];
  metadata: {
    source: 'chatgpt' | 'claude' | 'gemini' | 'unknown';
    characterCount: number;
    parseTime: number;
    confidence: number;
  };
}

interface ParsedActivity {
  id: string;
  name: string;
  description?: string;
  type: ActivityType;
  startTime: Date;
  endTime?: Date;
  location?: {
    address?: string;
    lat?: number;
    lng?: number;
  };
  price?: number;
  currency?: string;
  confidence: number;
  dayNumber: number;
  position: number;
}

interface LowConfidenceItem {
  field: string;
  value: any;
  message: string;
  suggestion?: string;
}

interface ImportContextType {
  // State
  currentStep: ImportStep;
  source: ImportSource;
  content: string;
  uploadedFile: File | null;
  parsedTrip: ParsedTrip | null;
  parseProgress: ParseProgress | null;
  importId: string | null;
  error: string | null;

  // Actions
  setStep: (step: ImportStep) => void;
  setSource: (source: ImportSource) => void;
  setContent: (content: string) => void;
  setUploadedFile: (file: File | null) => void;
  setParsedTrip: (trip: ParsedTrip | null) => void;
  setParseProgress: (progress: ParseProgress | null) => void;
  setImportId: (id: string | null) => void;
  setError: (error: string | null) => void;
  reset: () => void;
}

const ImportContext = createContext<ImportContextType | undefined>(undefined);

export function ImportProvider({ children }: { children: ReactNode }) {
  const [currentStep, setStep] = useState<ImportStep>('input');
  const [source, setSource] = useState<ImportSource>('paste');
  const [content, setContent] = useState('');
  const [uploadedFile, setUploadedFile] = useState<File | null>(null);
  const [parsedTrip, setParsedTrip] = useState<ParsedTrip | null>(null);
  const [parseProgress, setParseProgress] = useState<ParseProgress | null>(null);
  const [importId, setImportId] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);

  const reset = () => {
    setStep('input');
    setSource('paste');
    setContent('');
    setUploadedFile(null);
    setParsedTrip(null);
    setParseProgress(null);
    setImportId(null);
    setError(null);
  };

  return (
    <ImportContext.Provider value={{
      currentStep,
      source,
      content,
      uploadedFile,
      parsedTrip,
      parseProgress,
      importId,
      error,
      setStep,
      setSource,
      setContent,
      setUploadedFile,
      setParsedTrip,
      setParseProgress,
      setImportId,
      setError,
      reset
    }}>
      {children}
    </ImportContext.Provider>
  );
}

export function useImport() {
  const context = useContext(ImportContext);
  if (!context) {
    throw new Error('useImport must be used within ImportProvider');
  }
  return context;
}
```

### Task 3: Import Wizard Component (2.5 hours)

**File**: `packages/web/src/components/import/ImportWizard.tsx`

```typescript
'use client';

import { useImport } from '@/contexts/ImportContext';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { StepIndicator } from './StepIndicator';
import { InputStep } from './steps/InputStep';
import { ParsingStep } from './steps/ParsingStep';
import { PreviewStep } from './steps/PreviewStep';
import { CreatingStep } from './steps/CreatingStep';
import { Upload, Brain, CheckCircle, Sparkles } from 'lucide-react';

const steps = [
  { id: 'input', label: 'Import Conversation', icon: Upload },
  { id: 'parsing', label: 'AI Processing', icon: Brain },
  { id: 'preview', label: 'Review & Create', icon: CheckCircle }
];

export function ImportWizard() {
  const { currentStep, error } = useImport();

  return (
    <div className="max-w-6xl mx-auto">
      {/* Header */}
      <div className="text-center mb-8">
        <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full mb-4">
          <Sparkles className="w-8 h-8 text-white" />
        </div>
        <h1 className="text-4xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
          Import Your AI Travel Conversation
        </h1>
        <p className="text-gray-600 mt-2 text-lg">
          Transform your ChatGPT, Claude, or Gemini chat into a visual itinerary in seconds
        </p>
      </div>

      {/* Main Card */}
      <Card className="shadow-xl">
        <CardHeader className="pb-0">
          <StepIndicator steps={steps} currentStep={currentStep} />
        </CardHeader>

        <CardContent className="p-6 md:p-8">
          {/* Error Alert */}
          {error && (
            <Alert variant="destructive" className="mb-6">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {/* Step Content */}
          <div className="min-h-[400px]">
            {currentStep === 'input' && <InputStep />}
            {currentStep === 'parsing' && <ParsingStep />}
            {currentStep === 'preview' && <PreviewStep />}
            {currentStep === 'creating' && <CreatingStep />}
          </div>
        </CardContent>
      </Card>

      {/* Help Text */}
      <div className="mt-8 text-center text-sm text-gray-500">
        <p>
          Having trouble? Check out our{' '}
          <a href="/help/import" className="text-blue-600 hover:underline">
            import guide
          </a>{' '}
          or try one of our{' '}
          <button className="text-blue-600 hover:underline">
            sample conversations
          </button>
        </p>
      </div>
    </div>
  );
}
```

## Day 4 Afternoon: Input Methods & UI Polish (4 hours)

### Task 4: Step Indicator Component (30 min)

**File**: `packages/web/src/components/import/StepIndicator.tsx`

```typescript
import { cn } from '@/lib/utils';
import { LucideIcon } from 'lucide-react';
import { ImportStep } from '@/contexts/ImportContext';

interface Step {
  id: string;
  label: string;
  icon: LucideIcon;
}

interface StepIndicatorProps {
  steps: Step[];
  currentStep: ImportStep;
}

export function StepIndicator({ steps, currentStep }: StepIndicatorProps) {
  const currentIndex = steps.findIndex(s => s.id === currentStep);

  return (
    <div className="relative">
      {/* Progress Line */}
      <div className="absolute left-0 right-0 top-6 h-0.5 bg-gray-200">
        <div
          className="h-full bg-gradient-to-r from-blue-600 to-purple-600 transition-all duration-500"
          style={{ width: `${(currentIndex / (steps.length - 1)) * 100}%` }}
        />
      </div>

      {/* Steps */}
      <div className="relative flex justify-between">
        {steps.map((step, index) => {
          const isActive = index === currentIndex;
          const isComplete = index < currentIndex;
          const Icon = step.icon;

          return (
            <div key={step.id} className="flex flex-col items-center">
              <div
                className={cn(
                  "w-12 h-12 rounded-full flex items-center justify-center transition-all duration-300",
                  isComplete && "bg-gradient-to-br from-blue-600 to-purple-600 text-white",
                  isActive && "bg-gradient-to-br from-blue-600 to-purple-600 text-white scale-110 shadow-lg",
                  !isComplete && !isActive && "bg-gray-100 text-gray-400"
                )}
              >
                <Icon className="w-6 h-6" />
              </div>
              <span
                className={cn(
                  "mt-2 text-sm font-medium transition-colors",
                  (isActive || isComplete) && "text-gray-900",
                  !isActive && !isComplete && "text-gray-400"
                )}
              >
                {step.label}
              </span>
            </div>
          );
        })}
      </div>
    </div>
  );
}
```

### Task 5: Input Step Component (3 hours)

**File**: `packages/web/src/components/import/steps/InputStep.tsx`

```typescript
'use client';

import { useState, useCallback } from 'react';
import { useImport } from '@/contexts/ImportContext';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { useDropzone } from 'react-dropzone';
import { Upload, FileText, Link, Sparkles, ArrowRight, Info } from 'lucide-react';
import { cn } from '@/lib/utils';
import { importApi } from '@/lib/api/import';

const SAMPLE_CONVERSATIONS = [
  {
    id: 'paris-rome',
    title: 'Paris & Rome Adventure',
    description: '5-day romantic getaway',
    preview: 'Day 1 - Paris: Arrive at CDG Airport, check into Hotel Malte Opera...',
    content: `Here's a 5-day itinerary for Paris and Rome:

Day 1 - Paris (March 15):
- Morning: Arrive at CDG Airport (flight lands at 9:30 AM)
- 2:00 PM: Check into Hotel Malte Opera (address: 63 Rue de Richelieu, 75002 Paris)
- 4:00 PM: Visit the Eiffel Tower - book skip-the-line tickets
- 7:00 PM: Dinner cruise on the Seine with Bateaux Parisiens
- Price: €99 per person for the cruise

Day 2 - Paris (March 16):
- 9:00 AM: Louvre Museum tour (pre-book tickets for €17)
- 1:00 PM: Lunch at Café de Flore
- 3:00 PM: Walk through Montmartre and visit Sacré-Cœur
- 8:00 PM: Moulin Rouge show (book in advance - €115)

Day 3 - Travel to Rome (March 17):
- 10:00 AM: Check out from hotel
- 12:30 PM: Flight from CDG to Rome FCO (Air France AF1404)
- 3:00 PM: Arrive in Rome
- 4:30 PM: Check into Hotel Artemide (Via Nazionale, 22, 00184 Roma)
- 7:00 PM: Dinner at Trattoria da Valentino near Colosseum

Day 4 - Rome (March 18):
- 9:00 AM: Skip-the-line Colosseum and Roman Forum tour
- 1:00 PM: Lunch at local pizzeria
- 3:00 PM: Visit Vatican Museums and Sistine Chapel
- 6:00 PM: Climb St. Peter's Basilica dome
- 8:30 PM: Dinner in Trastevere

Day 5 - Rome (March 19):
- 10:00 AM: Trevi Fountain and Spanish Steps
- 12:00 PM: Shopping on Via del Corso
- 2:00 PM: Lunch at Giolitti (famous gelato place)
- 4:00 PM: Visit Borghese Gallery (book tickets in advance)
- 8:00 PM: Farewell dinner with view of Rome

Total estimated budget: €1,500 per person including hotels and activities.`
  },
  {
    id: 'tokyo-kyoto',
    title: 'Japan Discovery',
    description: '7-day cultural journey',
    preview: 'Day 1 - Tokyo: Land at Narita Airport, take Narita Express to city...',
    content: `Your 7-day Japan itinerary:...` // Add full content
  }
];

export function InputStep() {
  const { source, content, setContent, setSource, setStep, setError, setImportId, setParseProgress } = useImport();
  const [isProcessing, setIsProcessing] = useState(false);
  const [characterCount, setCharacterCount] = useState(content.length);

  const handleContentChange = (value: string) => {
    setContent(value);
    setCharacterCount(value.length);
  };

  const handleSampleSelect = (sample: typeof SAMPLE_CONVERSATIONS[0]) => {
    setContent(sample.content);
    setCharacterCount(sample.content.length);
    setSource('paste');
  };

  const detectAISource = (text: string): 'chatgpt' | 'claude' | 'gemini' | 'unknown' => {
    if (text.includes('ChatGPT') || text.includes('GPT-4')) return 'chatgpt';
    if (text.includes('Claude') || text.includes('Anthropic')) return 'claude';
    if (text.includes('Gemini') || text.includes('Bard')) return 'gemini';
    return 'unknown';
  };

  const handleParse = async () => {
    if (!content.trim() || content.length < 100) {
      setError('Please paste a longer conversation with trip details');
      return;
    }

    setIsProcessing(true);
    setError(null);

    try {
      // Start parsing
      setStep('parsing');

      // Create import session
      const response = await importApi.parseText(content, detectAISource(content));
      setImportId(response.importId);

      // Will continue in ParsingStep with SSE
    } catch (error) {
      setError('Failed to start import. Please try again.');
      setStep('input');
    } finally {
      setIsProcessing(false);
    }
  };

  const onDrop = useCallback((acceptedFiles: File[]) => {
    const file = acceptedFiles[0];
    if (file && file.type === 'application/pdf') {
      // Handle PDF upload
      const reader = new FileReader();
      reader.onload = async (e) => {
        // In production, parse PDF content
        setError('PDF parsing coming soon! Please paste text for now.');
      };
      reader.readAsArrayBuffer(file);
    }
  }, []);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'application/pdf': ['.pdf']
    },
    maxSize: 10 * 1024 * 1024, // 10MB
    multiple: false
  });

  return (
    <div className="space-y-6">
      {/* Source Tabs */}
      <Tabs value={source} onValueChange={(v) => setSource(v as any)}>
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="paste">
            <FileText className="w-4 h-4 mr-2" />
            Paste Text
          </TabsTrigger>
          <TabsTrigger value="file">
            <Upload className="w-4 h-4 mr-2" />
            Upload PDF
          </TabsTrigger>
          <TabsTrigger value="url" disabled>
            <Link className="w-4 h-4 mr-2" />
            From URL
          </TabsTrigger>
        </TabsList>

        {/* Paste Text */}
        <TabsContent value="paste" className="space-y-4">
          {/* Tips */}
          <Alert className="bg-blue-50 border-blue-200">
            <Info className="h-4 w-4 text-blue-600" />
            <AlertDescription className="text-blue-800">
              <strong>Pro Tips for Best Results:</strong>
              <ul className="mt-2 space-y-1 text-sm">
                <li>• Include specific dates and times in your conversation</li>
                <li>• Mention hotel names, flight numbers, or restaurant names</li>
                <li>• Copy the entire conversation, not just the final itinerary</li>
                <li>• Include price information for better budget tracking</li>
              </ul>
            </AlertDescription>
          </Alert>

          {/* Textarea */}
          <div className="relative">
            <Textarea
              placeholder="Paste your entire ChatGPT, Claude, or Gemini conversation here..."
              className="min-h-[300px] font-mono text-sm resize-none"
              value={content}
              onChange={(e) => handleContentChange(e.target.value)}
            />
            <div className="absolute bottom-2 right-2 text-xs text-gray-400">
              {characterCount} characters
            </div>
          </div>

          {/* Sample Conversations */}
          <div className="space-y-3">
            <p className="text-sm font-medium text-gray-700">
              Try a sample conversation:
            </p>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
              {SAMPLE_CONVERSATIONS.map((sample) => (
                <button
                  key={sample.id}
                  onClick={() => handleSampleSelect(sample)}
                  className="text-left p-4 border rounded-lg hover:border-blue-500 hover:bg-blue-50 transition-colors"
                >
                  <h4 className="font-medium text-gray-900">{sample.title}</h4>
                  <p className="text-sm text-gray-600 mt-1">{sample.description}</p>
                  <p className="text-xs text-gray-500 mt-2 line-clamp-2">
                    {sample.preview}
                  </p>
                </button>
              ))}
            </div>
          </div>
        </TabsContent>

        {/* Upload PDF */}
        <TabsContent value="file">
          <div
            {...getRootProps()}
            className={cn(
              "border-2 border-dashed rounded-lg p-12 text-center cursor-pointer transition-all",
              isDragActive
                ? "border-blue-500 bg-blue-50"
                : "border-gray-300 hover:border-gray-400"
            )}
          >
            <input {...getInputProps()} />
            <Upload className="mx-auto h-12 w-12 text-gray-400 mb-4" />
            <p className="text-lg font-medium text-gray-700">
              {isDragActive
                ? "Drop your PDF here"
                : "Drag & drop your conversation PDF"}
            </p>
            <p className="text-sm text-gray-500 mt-2">
              or click to browse (max 10MB)
            </p>
            <p className="text-xs text-gray-400 mt-4">
              Supports: ChatGPT, Claude, and Gemini PDF exports
            </p>
          </div>
        </TabsContent>
      </Tabs>

      {/* Action Button */}
      <div className="flex justify-end">
        <Button
          size="lg"
          onClick={handleParse}
          disabled={!content.trim() || characterCount < 100 || isProcessing}
          className="min-w-[200px]"
        >
          {isProcessing ? (
            <>
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
              Processing...
            </>
          ) : (
            <>
              Start Import
              <ArrowRight className="ml-2 h-4 w-4" />
            </>
          )}
        </Button>
      </div>
    </div>
  );
}
```

## Day 5 Morning: Parsing Animation & Real-time Feedback (4 hours)

### Task 6: Parsing Step with Animations (4 hours)

**File**: `packages/web/src/components/import/steps/ParsingStep.tsx`

```typescript
'use client';

import { useEffect, useState } from 'react';
import { useImport } from '@/contexts/ImportContext';
import { Progress } from '@/components/ui/progress';
import { cn } from '@/lib/utils';
import {
  Brain,
  Sparkles,
  Upload,
  FileSearch,
  Calendar,
  MapPin,
  List,
  Check,
  AlertCircle
} from 'lucide-react';

const PARSING_STEPS = [
  { id: 'upload', label: 'Uploading conversation', icon: Upload, duration: 1000 },
  { id: 'extract', label: 'Extracting trip details', icon: FileSearch, duration: 2000 },
  { id: 'dates', label: 'Finding dates and times', icon: Calendar, duration: 1500 },
  { id: 'locations', label: 'Identifying locations', icon: MapPin, duration: 2000 },
  { id: 'activities', label: 'Organizing activities', icon: List, duration: 1500 },
  { id: 'optimize', label: 'Creating your itinerary', icon: Sparkles, duration: 1000 }
];

const FUN_FACTS = [
  "Did you know? The average traveler visits 23 websites before booking a trip!",
  "Fun fact: 74% of travelers plan their trips using AI assistants.",
  "Processing tip: Including specific times helps us create a better timeline.",
  "Travel stat: Visual itineraries increase trip satisfaction by 45%.",
  "Almost there! Your organized trip is just moments away."
];

export function ParsingStep() {
  const {
    importId,
    setParsedTrip,
    setStep,
    setError,
    parseProgress,
    setParseProgress
  } = useImport();

  const [currentStepIndex, setCurrentStepIndex] = useState(0);
  const [overallProgress, setOverallProgress] = useState(0);
  const [funFactIndex, setFunFactIndex] = useState(0);

  useEffect(() => {
    if (!importId) return;

    // Connect to SSE endpoint
    const eventSource = new EventSource(`/api/v1/import/${importId}/progress`);

    eventSource.onmessage = (event) => {
      const data = JSON.parse(event.data);

      if (data.type === 'progress') {
        setParseProgress(data);

        // Update step index based on progress
        const stepIndex = PARSING_STEPS.findIndex(s => s.id === data.step);
        if (stepIndex !== -1) {
          setCurrentStepIndex(stepIndex);
          setOverallProgress((stepIndex + 1) / PARSING_STEPS.length * 100);
        }
      } else if (data.type === 'complete') {
        setParsedTrip(data.result);
        setStep('preview');
        eventSource.close();
      } else if (data.type === 'error') {
        setError(data.message);
        setStep('input');
        eventSource.close();
      }
    };

    eventSource.onerror = () => {
      setError('Connection lost. Please try again.');
      setStep('input');
      eventSource.close();
    };

    // Rotate fun facts
    const factInterval = setInterval(() => {
      setFunFactIndex(i => (i + 1) % FUN_FACTS.length);
    }, 4000);

    return () => {
      eventSource.close();
      clearInterval(factInterval);
    };
  }, [importId]);

  // Simulate progress if no real updates
  useEffect(() => {
    if (!parseProgress) {
      const interval = setInterval(() => {
        setCurrentStepIndex(i => {
          if (i < PARSING_STEPS.length - 1) return i + 1;
          return i;
        });
      }, 2000);

      return () => clearInterval(interval);
    }
  }, [parseProgress]);

  const currentStep = PARSING_STEPS[currentStepIndex];

  return (
    <div className="py-8">
      <div className="max-w-lg mx-auto">
        {/* Animated Header */}
        <div className="flex justify-center mb-8">
          <div className="relative">
            <div className="absolute inset-0 animate-pulse">
              <div className="w-32 h-32 bg-blue-400 rounded-full opacity-20 blur-xl" />
            </div>
            <div className="relative bg-gradient-to-br from-blue-500 to-purple-600 rounded-full p-6">
              <Brain className="w-20 h-20 text-white animate-pulse" />
              <Sparkles className="absolute -top-2 -right-2 w-8 h-8 text-yellow-400 animate-spin" />
            </div>
          </div>
        </div>

        {/* Progress Bar */}
        <div className="mb-8">
          <Progress
            value={overallProgress}
            className="h-2"
          />
          <p className="text-center text-sm text-gray-600 mt-2">
            {Math.round(overallProgress)}% complete
          </p>
        </div>

        {/* Step List */}
        <div className="bg-gray-50 rounded-lg p-6 space-y-4">
          {PARSING_STEPS.map((step, index) => {
            const isActive = index === currentStepIndex;
            const isComplete = index < currentStepIndex;
            const Icon = step.icon;

            return (
              <div
                key={step.id}
                className={cn(
                  "flex items-center space-x-3 transition-all duration-500",
                  isActive && "scale-105 translate-x-2",
                  !isActive && !isComplete && "opacity-40"
                )}
              >
                <div
                  className={cn(
                    "flex-shrink-0 w-10 h-10 rounded-full flex items-center justify-center transition-all duration-300",
                    isComplete && "bg-green-100 text-green-600",
                    isActive && "bg-blue-100 text-blue-600",
                    !isActive && !isComplete && "bg-gray-200 text-gray-400"
                  )}
                >
                  {isComplete ? (
                    <Check className="w-5 h-5" />
                  ) : (
                    <Icon className={cn(
                      "w-5 h-5",
                      isActive && "animate-pulse"
                    )} />
                  )}
                </div>

                <div className="flex-1">
                  <p className={cn(
                    "font-medium transition-colors",
                    isActive && "text-blue-600",
                    isComplete && "text-green-600",
                    !isActive && !isComplete && "text-gray-500"
                  )}>
                    {step.label}
                    {isActive && (
                      <span className="ml-2 text-blue-500">
                        <span className="animate-pulse">•</span>
                        <span className="animate-pulse animation-delay-200">•</span>
                        <span className="animate-pulse animation-delay-400">•</span>
                      </span>
                    )}
                  </p>
                  {isActive && parseProgress?.message && (
                    <p className="text-sm text-gray-600 mt-1">
                      {parseProgress.message}
                    </p>
                  )}
                </div>

                {isActive && (
                  <div className="flex-shrink-0">
                    <div className="w-6 h-6 border-2 border-blue-600 border-t-transparent rounded-full animate-spin" />
                  </div>
                )}
              </div>
            );
          })}
        </div>

        {/* Fun Facts */}
        <div className="mt-8 text-center">
          <p className="text-sm text-gray-600 italic animate-fade-in">
            "{FUN_FACTS[funFactIndex]}"
          </p>
        </div>

        {/* Estimated Time */}
        <div className="mt-4 text-center">
          <p className="text-xs text-gray-500">
            Estimated time remaining: ~{Math.max(1, 6 - currentStepIndex)} seconds
          </p>
        </div>
      </div>
    </div>
  );
}
```

## Day 5 Afternoon: Preview & Trip Creation (4 hours)

### Task 7: Preview Step Component (4 hours)

**File**: `packages/web/src/components/import/steps/PreviewStep.tsx`

```typescript
'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useImport } from '@/contexts/ImportContext';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import {
  CheckCircle,
  AlertCircle,
  Edit,
  MapPin,
  Calendar,
  DollarSign,
  Clock,
  ArrowRight,
  Sparkles
} from 'lucide-react';
import { format } from 'date-fns';
import { MiniTimeline } from '@/components/timeline/MiniTimeline';
import { MiniMap } from '@/components/map/MiniMap';
import { importApi } from '@/lib/api/import';

export function PreviewStep() {
  const router = useRouter();
  const { parsedTrip, importId, setStep, setError } = useImport();
  const [activeTab, setActiveTab] = useState<'timeline' | 'map'>('timeline');
  const [isCreating, setIsCreating] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);

  if (!parsedTrip) {
    setError('No parsed trip data available');
    setStep('input');
    return null;
  }

  const handleCreateTrip = async () => {
    if (!importId) return;

    setIsCreating(true);
    setError(null);

    try {
      setStep('creating');
      const { tripId } = await importApi.createTripFromImport(importId);

      // Navigate to the new trip
      router.push(`/trips/${tripId}`);
    } catch (error) {
      setError('Failed to create trip. Please try again.');
      setStep('preview');
    } finally {
      setIsCreating(false);
    }
  };

  const getConfidenceColor = (confidence: number) => {
    if (confidence >= 0.8) return 'text-green-600 bg-green-50';
    if (confidence >= 0.6) return 'text-yellow-600 bg-yellow-50';
    return 'text-red-600 bg-red-50';
  };

  const stats = {
    days: Math.ceil((parsedTrip.endDate.getTime() - parsedTrip.startDate.getTime()) / (1000 * 60 * 60 * 24)),
    activities: parsedTrip.activities.length,
    locations: new Set(parsedTrip.activities.map(a => a.location?.address)).size,
    estimatedBudget: parsedTrip.activities.reduce((sum, a) => sum + (a.price || 0), 0)
  };

  return (
    <div className="space-y-6">
      {/* Success Alert */}
      <Alert className="bg-green-50 border-green-200">
        <CheckCircle className="h-5 w-5 text-green-600" />
        <AlertDescription className="text-green-800">
          <strong>Success!</strong> We extracted {stats.activities} activities
          across {stats.days} days from your conversation.
        </AlertDescription>
      </Alert>

      {/* Trip Overview Card */}
      <Card>
        <CardHeader>
          <div className="flex items-start justify-between">
            <div className="space-y-2">
              <h2 className="text-2xl font-bold">{parsedTrip.title}</h2>
              <p className="text-gray-600">
                {format(parsedTrip.startDate, 'MMM d')} - {format(parsedTrip.endDate, 'MMM d, yyyy')}
              </p>
              {parsedTrip.destination && (
                <div className="flex items-center text-gray-600">
                  <MapPin className="w-4 h-4 mr-1" />
                  {parsedTrip.destination}
                </div>
              )}
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowEditModal(true)}
            >
              <Edit className="w-4 h-4 mr-2" />
              Edit Details
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          {/* Quick Stats */}
          <div className="grid grid-cols-4 gap-4 mb-6">
            <div className="text-center">
              <Calendar className="w-6 h-6 text-gray-400 mx-auto mb-1" />
              <p className="text-2xl font-bold">{stats.days}</p>
              <p className="text-xs text-gray-600">Days</p>
            </div>
            <div className="text-center">
              <MapPin className="w-6 h-6 text-gray-400 mx-auto mb-1" />
              <p className="text-2xl font-bold">{stats.activities}</p>
              <p className="text-xs text-gray-600">Activities</p>
            </div>
            <div className="text-center">
              <Clock className="w-6 h-6 text-gray-400 mx-auto mb-1" />
              <p className="text-2xl font-bold">{stats.locations}</p>
              <p className="text-xs text-gray-600">Locations</p>
            </div>
            <div className="text-center">
              <DollarSign className="w-6 h-6 text-gray-400 mx-auto mb-1" />
              <p className="text-2xl font-bold">
                ${stats.estimatedBudget}
              </p>
              <p className="text-xs text-gray-600">Est. Budget</p>
            </div>
          </div>

          {/* Parse Quality */}
          <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
            <span className="text-sm font-medium">Import Quality</span>
            <div className="flex items-center gap-2">
              <Badge className={getConfidenceColor(parsedTrip.metadata.confidence)}>
                {Math.round(parsedTrip.metadata.confidence * 100)}% Confident
              </Badge>
              <Badge variant="outline">
                {parsedTrip.metadata.source} detected
              </Badge>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Preview Tabs */}
      <Tabs value={activeTab} onValueChange={(v) => setActiveTab(v as any)}>
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="timeline">Timeline View</TabsTrigger>
          <TabsTrigger value="map">Map View</TabsTrigger>
        </TabsList>

        <TabsContent value="timeline" className="mt-6">
          <Card>
            <CardContent className="p-6">
              <MiniTimeline activities={parsedTrip.activities} />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="map" className="mt-6">
          <Card>
            <CardContent className="p-6">
              <div className="h-[400px]">
                <MiniMap activities={parsedTrip.activities} />
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Low Confidence Items */}
      {parsedTrip.lowConfidenceItems.length > 0 && (
        <Card className="border-amber-200 bg-amber-50">
          <CardHeader className="pb-3">
            <div className="flex items-center gap-2">
              <AlertCircle className="w-5 h-5 text-amber-600" />
              <h3 className="font-medium text-amber-900">
                Review These Items
              </h3>
            </div>
          </CardHeader>
          <CardContent>
            <ul className="space-y-2">
              {parsedTrip.lowConfidenceItems.map((item, i) => (
                <li key={i} className="text-sm text-amber-800">
                  <span className="font-medium">{item.field}:</span> {item.message}
                  {item.suggestion && (
                    <span className="text-amber-600 ml-1">
                      (Suggestion: {item.suggestion})
                    </span>
                  )}
                </li>
              ))}
            </ul>
          </CardContent>
        </Card>
      )}

      {/* Action Buttons */}
      <div className="flex items-center justify-between">
        <Button
          variant="outline"
          onClick={() => setStep('input')}
        >
          Start Over
        </Button>

        <Button
          size="lg"
          onClick={handleCreateTrip}
          disabled={isCreating}
          className="min-w-[200px] bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700"
        >
          {isCreating ? (
            <>
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
              Creating Trip...
            </>
          ) : (
            <>
              <Sparkles className="w-4 h-4 mr-2" />
              Create My Trip
              <ArrowRight className="ml-2 h-4 w-4" />
            </>
          )}
        </Button>
      </div>
    </div>
  );
}
```

## Extended Thinking Prompts

For complex UI decisions:

```
The user experience goal is: [describe goal]
Current implementation: [current approach]
User pain points: [list issues]
What's the most intuitive solution that delights users?
```

For parsing logic:

```
Input format: [show example conversation]
Expected output: [show desired structure]
Edge cases: [list potential issues]
What's the most robust parsing approach?
```

## Implementation Checklist

### Day 4 Completion

- [ ] Import route created at /import
- [ ] Import context provider working
- [ ] Import wizard component renders
- [ ] Step indicator animates correctly
- [ ] Input methods (paste/upload) functional
- [ ] Sample conversations load properly
- [ ] Character counter works
- [ ] Source detection (ChatGPT/Claude/Gemini) works
- [ ] Parse button triggers API call

### Day 5 Completion

- [ ] Parsing animations smooth
- [ ] Real-time SSE updates working
- [ ] Progress bar updates correctly
- [ ] Fun facts rotate during parsing
- [ ] Preview shows timeline view
- [ ] Preview shows map view
- [ ] Activity confidence indicators show
- [ ] Low confidence warnings display
- [ ] Edit functionality available
- [ ] Create trip button works
- [ ] Navigation to new trip succeeds

## API Integration Requirements

### Import API Endpoints (Hub)

```typescript
// POST /api/v1/import/parse
interface ParseRequest {
  content: string;
  source: 'chatgpt' | 'claude' | 'gemini' | 'unknown';
  confidence: number;
  options: {
    tokenLimit: number;
    extractPrices: boolean;
    enrichLocations: boolean;
    generateSuggestions: boolean;
    fallbackModel?: string;
  };
}
// Response: { importId: string, estimatedTime: number }

// GET /api/v1/import/:importId/progress (SSE)
interface ProgressEvent {
  type: 'progress' | 'complete' | 'error' | 'fallback' | 'heartbeat';
  step?: string;
  progress?: number;
  message?: string;
  tokensUsed?: number;
  substeps?: {
    completed: string[];
    current: string;
    remaining: string[];
  };
  result?: ParsedTrip;
  error?: {
    code: string;
    message: string;
    recoverable: boolean;
  };
}

// POST /api/v1/import/:importId/create-trip
interface CreateTripRequest {
  edits?: {
    title?: string;
    activities?: ActivityEdit[];
    mergeConflicts?: 'keep' | 'replace' | 'merge';
  };
  options?: {
    visibility: 'private' | 'public';
    autoShare: boolean;
    generateSlug: boolean;
  };
}
// Response: { tripId: string, slug?: string, shareUrl?: string }

// POST /api/v1/import/:importId/retry
// Request: { useModel?: string }
// Response: { success: boolean, importId?: string }

// GET /api/v1/import/usage
// Response: { daily: number, remaining: number, costToday: number }
```

### AI Prompt Engineering Templates

```typescript
// Base prompt template with token optimization
const EXTRACTION_PROMPT = `
Extract trip details from the following conversation.
Return ONLY valid JSON matching this schema:

{
  "title": "string",
  "startDate": "YYYY-MM-DD",
  "endDate": "YYYY-MM-DD",
  "destination": "string",
  "activities": [{
    "name": "string",
    "date": "YYYY-MM-DD",
    "time": "HH:MM",
    "duration": "minutes (number)",
    "location": {
      "address": "string",
      "city": "string",
      "country": "string"
    },
    "type": "flight|hotel|restaurant|attraction|transport|other",
    "price": "number or null",
    "currency": "USD|EUR|GBP|JPY|null",
    "notes": "string or null"
  }]
}

Rules:
1. Extract ONLY factual information present in the text
2. Use null for missing optional fields
3. Infer activity types from context
4. Parse all date formats to YYYY-MM-DD
5. Convert all times to 24-hour format
6. Group activities by day

Conversation:
{content}
`;

// Fallback prompt for simpler models
const SIMPLE_EXTRACTION_PROMPT = `
Find all travel activities in this text.
For each activity, extract:
- What: activity name
- When: date and time
- Where: location
- Cost: price if mentioned

Text: {content}
`;

// Context enhancement prompt
const ENRICHMENT_PROMPT = `
Given this trip activity:
{activity}

Provide missing details:
1. Estimated duration if not specified
2. Activity category (flight/hotel/restaurant/attraction)
3. Typical price range if no price given
4. Geocoding hints for the location

Return JSON with only the missing fields.
`;
```

### Token Optimization Strategies

```typescript
class TokenOptimizer {
  // Truncate conversation while preserving key information
  static truncateContent(content: string, maxTokens: number): string {
    const sections = this.identifySections(content);
    const prioritized = this.prioritizeSections(sections);

    let result = '';
    let tokenCount = 0;

    for (const section of prioritized) {
      const sectionTokens = this.estimateTokens(section.text);
      if (tokenCount + sectionTokens <= maxTokens) {
        result += section.text + '\n\n';
        tokenCount += sectionTokens;
      } else {
        // Add truncated version
        const remaining = maxTokens - tokenCount;
        result += this.smartTruncate(section.text, remaining);
        break;
      }
    }

    return result;
  }

  private static identifySections(content: string) {
    // Identify itinerary sections, dates, locations, etc.
    const sections = [];
    const patterns = [
      /day \d+[^]*?(?=day \d+|$)/gi,
      /\d{1,2}[\/\-]\d{1,2}[^]*?(?=\d{1,2}[\/\-]\d{1,2}|$)/gi,
      /morning:|afternoon:|evening:[^]*?(?=morning:|afternoon:|evening:|$)/gi,
    ];

    // Extract sections based on patterns
    for (const pattern of patterns) {
      const matches = content.matchAll(pattern);
      for (const match of matches) {
        sections.push({
          text: match[0],
          type: this.detectSectionType(match[0]),
          priority: this.calculatePriority(match[0]),
        });
      }
    }

    return sections;
  }

  private static prioritizeSections(sections: any[]) {
    // Sort by priority: itinerary > activities > general text
    return sections.sort((a, b) => b.priority - a.priority);
  }

  private static smartTruncate(text: string, maxTokens: number): string {
    // Truncate at sentence boundaries
    const sentences = text.match(/[^.!?]+[.!?]+/g) || [];
    let result = '';
    let tokens = 0;

    for (const sentence of sentences) {
      const sentenceTokens = this.estimateTokens(sentence);
      if (tokens + sentenceTokens <= maxTokens) {
        result += sentence;
        tokens += sentenceTokens;
      } else {
        result += '...';
        break;
      }
    }

    return result;
  }
}
```

## Success Metrics

### Technical Metrics

- Parse success rate >85%
- Parse time <5 seconds average
- UI responsive during parsing
- Zero crashes during import

### User Experience Metrics

- Completion rate >70%
- Edit rate <30%
- Delight score >8/10
- Time to import <60 seconds

## Definition of Done

✅ Feature Complete:

```bash
# All components render without errors
pnpm dev
# Navigate to http://localhost:3000/import

# Can paste text and see preview
# Animations are smooth
# Creates trip successfully
# Redirects to trip page
```

✅ User Flow Test:

1. Navigate to /import
2. Paste ChatGPT conversation
3. Watch parsing animation (delightful)
4. See preview with timeline + map
5. Click "Create My Trip"
6. Land on beautiful trip page

## Next Days Preview

- Day 6-7: Connect to backend parser
- Day 8: Polish and edge cases
- Day 9: Viral sharing features

## Notes

- This is THE differentiating feature - make it magical
- Every detail matters in the UX
- Parsing animation should feel alive
- Preview must be beautiful
- Success should feel earned
- The whole flow should take <60 seconds
- If it doesn't feel like magic, iterate
