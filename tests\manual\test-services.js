const http = require('http');

async function testService(port, path = '/') {
  return new Promise((resolve) => {
    const req = http.get(`http://localhost:${port}${path}`, (res) => {
      console.log(`✅ Port ${port}: ${res.statusCode} ${res.statusMessage}`);
      resolve({ port, status: res.statusCode, success: res.statusCode < 400 });
    });
    
    req.on('error', (err) => {
      console.log(`❌ Port ${port}: ${err.message}`);
      resolve({ port, status: 'ERROR', success: false, error: err.message });
    });
    
    req.setTimeout(5000, () => {
      console.log(`⏰ Port ${port}: Timeout`);
      req.destroy();
      resolve({ port, status: 'TIMEOUT', success: false });
    });
  });
}

async function testServices() {
  console.log('🔍 Testing TravelViz Services...\n');
  
  // Test Hub API (port 3001)
  console.log('Testing Hub API (port 3001):');
  await testService(3001, '/health');
  await testService(3001, '/api/v1/health');
  
  console.log('\nTesting Web App (port 3000):');
  await testService(3000, '/');
  
  console.log('\n✨ Service test complete!');
}

testServices().catch(console.error);