{"root": true, "extends": ["eslint:recommended", "plugin:@typescript-eslint/recommended"], "plugins": ["@typescript-eslint"], "parser": "@typescript-eslint/parser", "parserOptions": {"ecmaVersion": 2020, "sourceType": "module", "project": "./tsconfig.json"}, "env": {"node": true, "es6": true}, "rules": {"@typescript-eslint/no-unused-vars": "warn", "@typescript-eslint/no-explicit-any": "warn", "prefer-const": "error", "no-var": "error"}, "ignorePatterns": ["node_modules/", "dist/", "**/*.js", "**/*.jsx"]}