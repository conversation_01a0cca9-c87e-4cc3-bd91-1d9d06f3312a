# TravelViz MVP Roadmap & Execution Plan V2

**Created By**: Principal Product Management  
**Date**: January 11, 2025  
**Version**: 2.0 - Engineering Reality Edition  
**Goal**: Ship testable product with AI import → visual itineraries in 15 days

## Executive Summary

After engineering validation revealed the V1 roadmap was 50% fantasy, this V2 focuses on **getting to user testing in 15 days** with our core differentiator working. We're cutting everything that doesn't directly enable: **AI conversation → Visual trip plan**.

### Reality Check

- **V1 Timeline**: 10 days (impossible)
- **Engineering Estimate**: 20-25 days (conservative)
- **V2 Target**: 15 days to testable MVP (aggressive but achievable)

### Core User Journey (Nothing Else Matters)

1. User pastes ChatGPT/Claude conversation
2. AI parses into structured trip
3. User sees visual timeline + map
4. User shares link with travel partners
5. (Future) User books through affiliate links

---

## Phase 0: Stop the Bleeding (Day 1-3)

**Goal**: Fix everything that prevents deployment

### Day 1: Unblock Deployment (8 hours)

```typescript
// Task 1: Fix ALL 37 TypeScript errors (4-5 hours)
// Priority: ActivityType and Activity exports blocking everything
// packages/shared/src/types.ts
export enum ActivityType {
  flight = 'flight',
  accommodation = 'accommodation',
  transport = 'transport',
  activity = 'activity',
  dining = 'dining',
  other = 'other',
}

export interface Activity {
  // existing fields...
  affiliate_url?: string;
  provider?: 'travelpayouts' | 'duffel' | 'hotels';
}

// Task 2: Remove JWT hardcoded fallback (30 min)
// packages/hub/src/utils/tokens.ts
const JWT_SECRET = process.env.JWT_SECRET;
if (!JWT_SECRET) throw new Error('JWT_SECRET is required');

// Task 3: Fix server.ts imports (30 min)
// Remove duplicate cors import, fix errorHandler name
```

### Day 2: Critical Security (8 hours)

```sql
-- Task 4: Fix foreign keys (1 hour)
-- New migration: 010_fix_foreign_keys.sql
ALTER TABLE search_history
  DROP CONSTRAINT search_history_user_id_fkey,
  ADD CONSTRAINT search_history_user_id_fkey
  FOREIGN KEY (user_id) REFERENCES auth.users(id);

ALTER TABLE trip_clones
  DROP CONSTRAINT trip_clones_source_user_id_fkey,
  DROP CONSTRAINT trip_clones_cloned_by_user_id_fkey,
  ADD CONSTRAINT trip_clones_source_user_id_fkey
  FOREIGN KEY (source_user_id) REFERENCES auth.users(id),
  ADD CONSTRAINT trip_clones_cloned_by_user_id_fkey
  FOREIGN KEY (cloned_by_user_id) REFERENCES auth.users(id);

-- Task 5: Re-enable RLS (2 hours)
-- This was disabled "temporarily" - massive security hole
ALTER TABLE trips ENABLE ROW LEVEL SECURITY;
ALTER TABLE activities ENABLE ROW LEVEL SECURITY;

-- Add basic policies
CREATE POLICY "Users can view own trips" ON trips
  FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can modify own trips" ON trips
  FOR ALL USING (auth.uid() = user_id);
-- Repeat for activities
```

```typescript
// Task 6: Add ownership middleware (3 hours)
// packages/hub/src/middleware/ownership.middleware.ts
export const requireOwnership = (resource: 'trip' | 'activity') => {
  return async (req: Request, res: Response, next: NextFunction) => {
    const userId = req.user?.id;
    const resourceId = req.params.id;

    const query =
      resource === 'trip'
        ? supabase.from('trips').select('user_id').eq('id', resourceId)
        : supabase.from('activities').select('trip:trips!inner(user_id)').eq('id', resourceId);

    const { data, error } = await query.single();

    if (error || !data || data.user_id !== userId) {
      return res.status(403).json({ error: 'Forbidden' });
    }

    next();
  };
};
```

### Day 3: Performance Crisis Prevention (6 hours)

```sql
-- Task 7: Add critical indexes (2 hours)
-- Without these, the app dies at 50+ users
CREATE INDEX CONCURRENTLY idx_trips_user_created
  ON trips(user_id, created_at DESC);
CREATE INDEX CONCURRENTLY idx_activities_position
  ON activities(trip_id, position);
CREATE INDEX CONCURRENTLY idx_trips_created_at
  ON trips(created_at DESC);
```

```typescript
// Task 8: Add pagination to prevent memory crashes (2 hours)
// packages/hub/src/services/trips.service.ts
async getUserTrips(userId: string, page = 1, limit = 20) {
  const offset = (page - 1) * limit;

  const { data, error, count } = await this.supabase
    .from('trips')
    .select('*, activities(*)', { count: 'exact' })
    .eq('user_id', userId)
    .order('created_at', { ascending: false })
    .range(offset, offset + limit - 1);

  return { trips: data, total: count, page, limit };
}
```

**Deliverable**: Deployable, secure application that won't crash

---

## Phase 1: Core Feature - AI Import UI (Day 4-8)

**Goal**: Build the ONE feature that makes us unique

### Day 4-5: Build Missing Import UI (16 hours)

```typescript
// Task 9: Create import page (8 hours)
// packages/web/src/app/import/page.tsx
'use client';

export default function ImportPage() {
  const [mode, setMode] = useState<'paste' | 'file'>('paste');
  const [content, setContent] = useState('');
  const [parsing, setParsing] = useState(false);
  const [progress, setProgress] = useState(0);

  const handleImport = async () => {
    setParsing(true);

    // Progress simulation (real progress from API later)
    const progressInterval = setInterval(() => {
      setProgress(p => Math.min(p + 20, 90));
    }, 500);

    try {
      const result = await api.trips.importFromText(content);
      clearInterval(progressInterval);
      setProgress(100);

      // Redirect to new trip
      router.push(`/trips/${result.id}`);
    } catch (error) {
      toast.error('Import failed. Please try again.');
    }
  };

  return (
    <div className="max-w-4xl mx-auto p-6">
      <h1 className="text-3xl font-bold mb-8">
        Import Your AI Travel Conversation
      </h1>

      <div className="bg-blue-50 p-4 rounded-lg mb-6">
        <p className="text-sm">
          ✨ Paste your ChatGPT, Claude, or Gemini conversation below.
          We'll extract your trip details and create a visual itinerary.
        </p>
      </div>

      <Tabs value={mode} onValueChange={setMode}>
        <TabsList>
          <TabsTrigger value="paste">Paste Text</TabsTrigger>
          <TabsTrigger value="file">Upload PDF</TabsTrigger>
        </TabsList>

        <TabsContent value="paste">
          <Textarea
            placeholder="Paste your AI conversation here..."
            className="min-h-[400px]"
            value={content}
            onChange={(e) => setContent(e.target.value)}
          />
        </TabsContent>

        <TabsContent value="file">
          <FileUpload onFileSelect={handleFileUpload} />
        </TabsContent>
      </Tabs>

      <Button
        onClick={handleImport}
        disabled={!content || parsing}
        className="w-full mt-6"
      >
        {parsing ? (
          <>
            <Loader2 className="animate-spin mr-2" />
            Parsing your trip... {progress}%
          </>
        ) : (
          'Create Visual Itinerary'
        )}
      </Button>
    </div>
  );
}
```

### Day 6-7: Connect Import to Visual Display (16 hours)

```typescript
// Task 10: Enhance trip display page (8 hours)
// Fix timeline + map integration for imported trips
// Ensure smooth transition from import → visual display

// Task 11: Add import CTA to dashboard (4 hours)
// packages/web/src/components/dashboard/EmptyState.tsx
<Button onClick={() => router.push('/import')} size="lg">
  <Upload className="mr-2" />
  Import AI Conversation
</Button>
```

### Day 8: Polish & Test Import Flow (8 hours)

- Test with real ChatGPT/Claude conversations
- Handle edge cases (partial data, weird formats)
- Ensure visual output is impressive
- Add share functionality

**Deliverable**: Working AI import → Visual itinerary flow

---

## Phase 2: User Testing Ready (Day 9-12)

**Goal**: Make it shareable and testable

### Day 9: Viral Sharing (8 hours)

```typescript
// Task 12: Enhance public trip view (4 hours)
// Make shared trips look amazing
// Add "Import to My Account" CTA
// Track basic analytics (views, copies)

// Task 13: Generate share preview (4 hours)
// OG images showing trip highlights
// Beautiful social media cards
```

### Day 10-11: Basic Monetization (16 hours)

```typescript
// Task 14: Fix affiliate system (4 hours)
// Currency conversion can wait - just ensure links work
// Focus on Travelpayouts (already working)

// Task 15: Add "Pro" upsell (8 hours)
// Simple Stripe Payment Links approach
// Just track who paid in database
// Email-based verification for now
```

### Day 12: User Testing Prep (8 hours)

- Create demo video
- Write FAQ
- Set up feedback collection (Canny.io)
- Deploy to production
- Create 5 sample trips to showcase

**Deliverable**: Shareable product ready for 100 beta users

---

## Phase 3: Scale Preparation (Day 13-15)

**Goal**: Don't die when users arrive

### Day 13: Monitoring & Analytics (8 hours)

```typescript
// Task 16: Add Sentry (2 hours)
// Just basic error tracking

// Task 17: Add PostHog (2 hours)
// Track: imports, shares, conversions

// Task 18: Add health check endpoint (1 hour)
app.get('/health', (req, res) => {
  res.json({ status: 'ok', timestamp: new Date() });
});
```

### Day 14: Performance & Stability (8 hours)

- Load test with 100 concurrent users
- Fix any crashes
- Add basic caching for public trips
- Ensure imports don't timeout

### Day 15: Launch! (8 hours)

- Final testing
- Backup database
- Deploy to production
- Send to first 10 beta users
- Monitor closely

---

## What We're NOT Building (Yet)

### Cut for V1:

- ❌ Price tracking (Phase 4)
- ❌ Duffel integration (use Travelpayouts only)
- ❌ Email notifications (manual for now)
- ❌ Team features (individual only)
- ❌ Mobile app (responsive web is enough)
- ❌ Offline support (not critical for testing)
- ❌ Complex subscription tiers (just Pro)
- ❌ Background jobs (synchronous is fine for 100 users)

### Why Cut These?

Every day we don't ship costs us learning. These features don't affect whether users will love "AI conversation → Visual trip". Ship, learn, iterate.

---

## Success Metrics for Day 15

### Must Have:

- ✅ 10 successful AI imports
- ✅ 5 shared trips
- ✅ 0 security breaches
- ✅ <3s page load times
- ✅ 1 paying customer

### Nice to Have:

- 📊 50% of testers share their trip
- 📊 20% try to pay for Pro
- 📊 90% successfully import on first try

---

## Risk Management

### Highest Risks:

1. **AI parsing fails** → Have manual trip creation as backup
2. **Server crashes** → Start with 10 users, scale gradually
3. **Security breach** → RLS + ownership checks are critical
4. **Poor import quality** → Test with 20+ real conversations

### Mitigation:

- Soft launch to friends/family first
- Monitor everything
- Have rollback plan
- Keep support channel open

---

## The Hard Truth

This plan requires:

- **15 days of focused execution**
- **No feature creep**
- **Acceptance of imperfection**
- **Willingness to learn from users**

But it delivers:

- **Core value proposition working**
- **Real user feedback in 2 weeks**
- **Revenue potential validated**
- **Technical foundation solid**

---

## Daily Accountability

| Day   | Goal               | Success Criteria                  |
| ----- | ------------------ | --------------------------------- |
| 1     | Unblock deployment | All TypeScript errors fixed       |
| 2     | Security fixes     | RLS enabled, ownership working    |
| 3     | Performance basics | Indexes added, pagination working |
| 4-5   | Import UI          | Users can paste conversations     |
| 6-7   | Visual display     | Beautiful timeline + map          |
| 8     | Polish import      | 90% success rate                  |
| 9     | Sharing            | Public links work                 |
| 10-11 | Monetization       | Payment captured                  |
| 12    | Testing prep       | 5 demo trips ready                |
| 13    | Monitoring         | Errors tracked                    |
| 14    | Performance        | 100 users supported               |
| 15    | Launch             | First users importing trips       |

---

## Remember

**Your competition isn't other trip planners. It's the 15-tab chaos users face today.**

You have ONE unique insight: People already plan trips with AI, but lose those conversations.

Everything else is a commodity. Make the AI → Visual magic work flawlessly. Ship it. Learn. Iterate.

**15 days to change how people plan travel. Let's go.**
