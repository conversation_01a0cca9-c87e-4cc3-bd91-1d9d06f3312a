/**
 * Playwright Configuration for TravelViz E2E Tests
 */

const { defineConfig, devices } = require('@playwright/test');
const testConfig = require('./tests/test.config');

module.exports = defineConfig({
  testDir: './tests/e2e',
  fullyParallel: false, // Run tests sequentially for better reliability
  forbidOnly: !!process.env.CI,
  retries: process.env.CI ? 2 : 1,
  workers: process.env.CI ? 1 : 1, // Single worker for stability
  reporter: [
    ['html', { outputFolder: 'test-results/playwright-report' }],
    ['list']
  ],
  
  use: {
    baseURL: testConfig.e2e.baseUrl,
    trace: 'on-first-retry',
    screenshot: 'only-on-failure',
    video: 'retain-on-failure',
    actionTimeout: 30000,
    navigationTimeout: 60000,
  },

  projects: [
    {
      name: 'chromium',
      use: { 
        ...devices['Desktop Chrome'],
        viewport: testConfig.e2e.viewport,
      },
    },
  ],

  // Don't start web server automatically - require manual start
  // This ensures developers are aware of the server requirement
  outputDir: 'test-results/playwright-artifacts',
});