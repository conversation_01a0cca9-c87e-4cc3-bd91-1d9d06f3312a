// End-to-End Test for AI Import Feature using Playwright
// This test demonstrates the full flow of importing a PDF travel itinerary

const { chromium } = require('playwright');
const path = require('path');

async function runImportTest() {
  const browser = await chromium.launch({ headless: false });
  const context = await browser.newContext();
  const page = await context.newPage();

  try {
    console.log('1. Navigating to TravelViz application...');
    await page.goto('http://localhost:3000', { waitUntil: 'networkidle' });

    console.log('2. Logging in with test account...');
    // Check if we need to login
    const loginButton = await page.locator('text=Login').first();
    if (await loginButton.isVisible()) {
      await loginButton.click();
      
      // Fill login form
      await page.fill('input[type="email"]', '<EMAIL>');
      await page.fill('input[type="password"]', 'Flaremmk123!');
      await page.click('button[type="submit"]');
      
      // Wait for login to complete
      await page.waitForURL('**/dashboard', { timeout: 10000 });
    }

    console.log('3. Navigating to Import page...');
    await page.goto('http://localhost:3000/import');
    await page.waitForLoadState('networkidle');

    console.log('4. Uploading PDF file...');
    const fileInput = await page.locator('input[type="file"]');
    const pdfPath = path.join(__dirname, 'Updated 15-Day European Travel Itinerary_ London, Madrid, Lisbon and Porto.pdf');
    await fileInput.setInputFiles(pdfPath);

    console.log('5. Starting import process...');
    await page.click('button:has-text("Parse Conversation")');

    console.log('6. Monitoring parsing progress...');
    // Wait for parsing step to appear
    await page.waitForSelector('text=Extracting trip details', { timeout: 30000 });
    
    // Monitor each parsing step
    const parsingSteps = [
      'Uploading conversation',
      'Extracting trip details',
      'Finding dates and times',
      'Identifying locations',
      'Organizing activities',
      'Creating your itinerary'
    ];

    for (const step of parsingSteps) {
      console.log(`   - Waiting for: ${step}`);
      await page.waitForSelector(`text=${step}`, { timeout: 60000 });
      
      // Check if step is marked as complete (has checkmark)
      const stepCompleted = await page.locator(`text=${step}`).locator('..').locator('svg.lucide-check').isVisible();
      if (stepCompleted) {
        console.log(`   ✓ ${step} - Complete`);
      }
    }

    console.log('7. Previewing parsed trip...');
    // Wait for preview step
    await page.waitForSelector('text=Review Your Trip', { timeout: 30000 });
    
    // Verify trip details are shown
    const tripTitle = await page.locator('h2').first().textContent();
    console.log(`   - Trip Title: ${tripTitle}`);
    
    // Check for timeline and map
    const hasTimeline = await page.locator('text=Timeline').isVisible();
    const hasMap = await page.locator('text=Map').isVisible();
    console.log(`   - Timeline visible: ${hasTimeline}`);
    console.log(`   - Map visible: ${hasMap}`);

    console.log('8. Creating trip...');
    await page.click('button:has-text("Create Trip")');

    // Wait for creation to complete
    await page.waitForSelector('text=Creating your trip', { timeout: 30000 });
    
    // Should redirect to the new trip page
    await page.waitForURL('**/plan/*', { timeout: 60000 });
    
    const finalUrl = page.url();
    console.log(`9. Trip created successfully! URL: ${finalUrl}`);

    // Verify trip was created
    const tripId = finalUrl.match(/\/plan\/([^\/]+)/)?.[1];
    console.log(`   - Trip ID: ${tripId}`);

    // Take final screenshot
    await page.screenshot({ path: 'import-success.png', fullPage: true });
    console.log('10. Screenshot saved as import-success.png');

  } catch (error) {
    console.error('Test failed:', error);
    await page.screenshot({ path: 'import-error.png', fullPage: true });
    throw error;
  } finally {
    await browser.close();
  }
}

// Run the test
runImportTest()
  .then(() => console.log('✅ Import test completed successfully!'))
  .catch((err) => console.error('❌ Import test failed:', err));