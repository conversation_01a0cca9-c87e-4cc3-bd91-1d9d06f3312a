"use client";

import { useState } from 'react';
import Link from 'next/link';
import { But<PERSON> } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { ArrowLeft, MapPin, Calendar, Users, Star, Clock, DollarSign } from 'lucide-react';
import { motion } from 'framer-motion';

const exampleTrips = [
  {
    id: 1,
    title: 'European Backpacking Adventure',
    description: 'A 3-week journey through 6 European countries',
    duration: '21 days',
    budget: '$2,500',
    travelers: '2 people',
    rating: 4.8,
    image: '/api/placeholder/300/200',
    highlights: ['Paris, France', 'Amsterdam, Netherlands', 'Prague, Czech Republic', 'Berlin, Germany']
  },
  {
    id: 2,
    title: 'Japan Cultural Immersion',
    description: 'Traditional and modern Japan experience',
    duration: '14 days',
    budget: '$3,200',
    travelers: '1 person',
    rating: 4.9,
    image: '/api/placeholder/300/200',
    highlights: ['Tokyo', 'Kyoto', 'Osaka', 'Mount Fuji']
  },
  {
    id: 3,
    title: 'Southeast Asia Explorer',
    description: 'Island hopping and cultural exploration',
    duration: '28 days',
    budget: '$1,800',
    travelers: '2 people',
    rating: 4.7,
    image: '/api/placeholder/300/200',
    highlights: ['Thailand', 'Vietnam', 'Cambodia', 'Laos']
  }
];

export default function ExamplesPage() {
  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <Link href="/" className="flex items-center space-x-2">
              <ArrowLeft className="h-5 w-5 text-gray-600" />
              <span className="text-gray-600">Back to Home</span>
            </Link>
            <div className="flex items-center space-x-4">
              <Link href="/login" className="text-gray-600 hover:text-gray-900">
                Sign In
              </Link>
              <Link href="/signup">
                <Button size="sm">Get Started</Button>
              </Link>
            </div>
          </div>
        </div>
      </div>

      {/* Hero Section */}
      <div className="bg-gradient-to-r from-orange-500 to-pink-500 text-white py-16">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            <h1 className="text-4xl md:text-5xl font-bold mb-4">
              Example Travel Plans
            </h1>
            <p className="text-xl text-white/90 mb-8">
              Get inspired by these sample trips created with TravelViz
            </p>
          </motion.div>
        </div>
      </div>

      {/* Examples Grid */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {exampleTrips.map((trip, index) => (
            <motion.div
              key={trip.id}
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1, duration: 0.5 }}
            >
              <Card className="overflow-hidden hover:shadow-lg transition-shadow">
                <div className="aspect-w-16 aspect-h-9 bg-gray-200">
                  <div className="w-full h-48 bg-gradient-to-br from-orange-200 to-pink-200 flex items-center justify-center">
                    <MapPin className="h-12 w-12 text-orange-600" />
                  </div>
                </div>
                <div className="p-6">
                  <div className="flex items-center space-x-2 mb-2">
                    <Badge variant="secondary">{trip.duration}</Badge>
                    <div className="flex items-center space-x-1">
                      <Star className="h-4 w-4 text-yellow-500 fill-current" />
                      <span className="text-sm font-medium">{trip.rating}</span>
                    </div>
                  </div>
                  <h3 className="text-xl font-bold text-gray-900 mb-2">{trip.title}</h3>
                  <p className="text-gray-600 mb-4">{trip.description}</p>
                  
                  <div className="space-y-2 mb-4">
                    <div className="flex items-center space-x-2 text-sm text-gray-600">
                      <Clock className="h-4 w-4" />
                      <span>{trip.duration}</span>
                    </div>
                    <div className="flex items-center space-x-2 text-sm text-gray-600">
                      <DollarSign className="h-4 w-4" />
                      <span>{trip.budget}</span>
                    </div>
                    <div className="flex items-center space-x-2 text-sm text-gray-600">
                      <Users className="h-4 w-4" />
                      <span>{trip.travelers}</span>
                    </div>
                  </div>
                  
                  <div className="mb-4">
                    <h4 className="font-medium text-gray-900 mb-2">Highlights</h4>
                    <div className="flex flex-wrap gap-1">
                      {trip.highlights.map((highlight, idx) => (
                        <Badge key={idx} variant="outline" className="text-xs">
                          {highlight}
                        </Badge>
                      ))}
                    </div>
                  </div>
                  
                  <Link href="/signup">
                    <Button className="w-full">
                      Create Similar Trip
                    </Button>
                  </Link>
                </div>
              </Card>
            </motion.div>
          ))}
        </div>
      </div>

      {/* CTA Section */}
      <div className="bg-white py-16">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl font-bold text-gray-900 mb-4">
            Ready to Plan Your Own Adventure?
          </h2>
          <p className="text-xl text-gray-600 mb-8">
            Join thousands of travelers who use TravelViz to create amazing trips
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link href="/signup">
              <Button size="lg" className="w-full sm:w-auto">
                Get Started Free
              </Button>
            </Link>
            <Link href="/templates">
              <Button size="lg" variant="outline" className="w-full sm:w-auto">
                Browse Templates
              </Button>
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
}