/**
 * Shared date validation utilities
 */

export class DateValidationError extends Error {
  constructor(message: string) {
    super(message);
    this.name = 'DateValidationError';
  }
}

/**
 * Validates that end date/time is after or equal to start date/time
 * @param startDate - Start date or datetime
 * @param endDate - End date or datetime
 * @throws DateValidationError if validation fails
 */
export function validateDateRange(startDate: Date | string, endDate: Date | string): void {
  const start = typeof startDate === 'string' ? new Date(startDate) : startDate;
  const end = typeof endDate === 'string' ? new Date(endDate) : endDate;
  
  if (isNaN(start.getTime())) {
    throw new DateValidationError('Invalid start date');
  }
  
  if (isNaN(end.getTime())) {
    throw new DateValidationError('Invalid end date');
  }
  
  if (end < start) {
    throw new DateValidationError('End date must be after or equal to start date');
  }
}

/**
 * Validates that a date is not in the past
 * @param date - Date to validate
 * @param allowToday - Whether today's date is valid
 * @throws DateValidationError if validation fails
 */
export function validateNotInPast(date: Date | string, allowToday = true): void {
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  const today = new Date();
  today.setHours(0, 0, 0, 0);
  
  if (isNaN(dateObj.getTime())) {
    throw new DateValidationError('Invalid date');
  }
  
  const compareDate = new Date(dateObj);
  compareDate.setHours(0, 0, 0, 0);
  
  if (allowToday) {
    if (compareDate < today) {
      throw new DateValidationError('Date cannot be in the past');
    }
  } else {
    if (compareDate <= today) {
      throw new DateValidationError('Date must be in the future');
    }
  }
}