# Design Document

## Overview

This design document outlines a systematic approach to debugging the critical PDF import status check failure in TravelViz. The design follows a evidence-based debugging methodology that progresses through seven distinct phases, each building upon concrete findings from the previous phase.

The approach is designed to minimize system disruption while maximizing diagnostic accuracy, ensuring that fixes address root causes rather than symptoms.

## Architecture

### Debugging Flow Architecture

```mermaid
graph TD
    A[Session State Verification] --> B[API Response Chain Analysis]
    B --> C[AI Processing Tracking]
    C --> D[Status Update Analysis]
    D --> E[Frontend Error Analysis]
    E --> F[Root Cause Fix]
    F --> G[End-to-End Validation]
    
    A --> H[Database Evidence]
    A --> I[Cache Evidence]
    B --> J[HTTP Evidence]
    C --> K[Processing Evidence]
    D --> L[Transaction Evidence]
    E --> M[Frontend Evidence]
    F --> N[Fix Evidence]
    G --> O[Validation Evidence]
```

### System Components Involved

1. **Database Layer**: Supabase PostgreSQL with ai_import_logs table
2. **Cache Layer**: Redis for session state caching
3. **API Layer**: Express.js endpoints for status checking
4. **Processing Layer**: AI parser service with OpenRouter integration
5. **Frontend Layer**: Next.js with polling mechanism

## Components and Interfaces

### Evidence Collection Interface

```typescript
interface DebuggingEvidence {
  sessionId: string;
  timestamp: Date;
  phase: DebugPhase;
  findings: Finding[];
  rawData: Record<string, any>;
}

interface Finding {
  type: 'database' | 'cache' | 'api' | 'processing' | 'frontend';
  description: string;
  evidence: any;
  severity: 'critical' | 'warning' | 'info';
}

enum DebugPhase {
  SESSION_STATE = 'session_state',
  API_CHAIN = 'api_chain',
  AI_PROCESSING = 'ai_processing',
  STATUS_UPDATE = 'status_update',
  FRONTEND_ERROR = 'frontend_error',
  ROOT_CAUSE_FIX = 'root_cause_fix',
  VALIDATION = 'validation'
}
```

### Database Investigation Interface

```typescript
interface SessionStateInvestigation {
  databaseQuery: string;
  cacheQuery: string;
  expectedState: ImportStatus;
  actualState: ImportStatus;
  discrepancies: string[];
}

interface ImportStatus {
  sessionId: string;
  status: 'pending' | 'processing' | 'complete' | 'failed';
  createdAt: Date;
  updatedAt: Date;
  parsedData?: any;
  errorMessage?: string;
}
```

### API Response Analysis Interface

```typescript
interface APIResponseAnalysis {
  endpoint: string;
  requestHeaders: Record<string, string>;
  responseHeaders: Record<string, string>;
  statusCode: number;
  body: any;
  cacheHeaders: {
    etag?: string;
    lastModified?: string;
    cacheControl?: string;
  };
}
```

## Data Models

### Debug Session Model

```typescript
interface DebugSession {
  id: string;
  targetSessionId: string; // The failing import session
  startedAt: Date;
  completedAt?: Date;
  currentPhase: DebugPhase;
  evidence: DebuggingEvidence[];
  rootCause?: RootCause;
  fixApplied?: Fix;
  validationResults?: ValidationResult[];
}
```

### Root Cause Model

```typescript
interface RootCause {
  category: 'ai_processing' | 'database_update' | 'caching' | 'frontend_timeout';
  description: string;
  evidence: Finding[];
  confidence: number; // 0-100
  recommendedFix: FixStrategy;
}

enum FixStrategy {
  ADD_TIMEOUT_HANDLING = 'add_timeout_handling',
  FIX_TRANSACTION_HANDLING = 'fix_transaction_handling',
  CLEAR_CACHE_FIX_ETAG = 'clear_cache_fix_etag',
  IMPROVE_FRONTEND_TIMEOUT = 'improve_frontend_timeout'
}
```

## Error Handling

### Phase-Based Error Handling

Each debugging phase implements specific error handling:

1. **Database Connection Failures**: Retry with exponential backoff, fallback to manual queries
2. **API Endpoint Unavailability**: Use alternative debugging endpoints, direct database access
3. **Cache Access Issues**: Continue with database-only investigation, document cache problems
4. **AI Processing Timeouts**: Implement investigation timeouts, capture partial state
5. **Frontend Debugging Limitations**: Use server-side logging, browser dev tools fallback

### Evidence Validation

```typescript
interface EvidenceValidator {
  validateDatabaseEvidence(evidence: any): ValidationResult;
  validateAPIEvidence(evidence: APIResponseAnalysis): ValidationResult;
  validateProcessingEvidence(evidence: any): ValidationResult;
  crossValidateFindings(findings: Finding[]): ValidationResult;
}
```

### Failure Recovery

If any debugging phase fails:
1. Document the specific technical limitation
2. Provide alternative investigation approaches
3. Request manual intervention with specific instructions
4. Preserve all collected evidence for manual analysis

## Testing Strategy

### Evidence-Based Testing

Each phase must produce verifiable evidence:

1. **Database Evidence**: SQL query results, table states, transaction logs
2. **Cache Evidence**: Redis key-value pairs, TTL information, cache hit/miss rates
3. **API Evidence**: Complete HTTP request/response cycles, header analysis
4. **Processing Evidence**: Function execution traces, exception logs, timing data
5. **Frontend Evidence**: Browser console logs, network tab analysis, user interaction flows

### Hypothesis Testing Framework

```typescript
interface HypothesisTest {
  hypothesis: string;
  testMethod: string;
  expectedEvidence: string;
  actualEvidence: any;
  conclusion: 'confirmed' | 'rejected' | 'inconclusive';
  nextSteps: string[];
}
```

### Fix Validation Testing

Before implementing any fix:
1. **Backup Current State**: Database snapshots, configuration backups
2. **Isolated Testing**: Test fixes in development environment first
3. **Rollback Plan**: Clear rollback procedures for each fix type
4. **Success Criteria**: Specific metrics that confirm fix effectiveness

### End-to-End Validation

Final validation must demonstrate:
1. **Same PDF Success**: The originally failing PDF processes successfully
2. **No Infinite Polling**: Frontend polling terminates appropriately
3. **Proper Error Handling**: Graceful handling of AI processing failures
4. **Performance Metrics**: Response times within acceptable ranges
5. **Log Completeness**: Full audit trail from upload to completion

## Implementation Phases

### Phase 1: Diagnostic Infrastructure
- Set up evidence collection systems
- Prepare database query tools
- Configure API testing utilities
- Establish logging mechanisms

### Phase 2: Sequential Investigation
- Execute each debugging phase in order
- Collect and validate evidence at each step
- Build hypothesis chain based on findings
- Document all discoveries with concrete proof

### Phase 3: Root Cause Analysis
- Analyze collected evidence for patterns
- Identify the primary failure point
- Determine fix strategy based on evidence
- Plan minimal intervention approach

### Phase 4: Fix Implementation
- Implement targeted fix based on root cause
- Add comprehensive error handling
- Include monitoring and alerting improvements
- Document all code changes with rationale

### Phase 5: Validation and Monitoring
- Execute end-to-end testing with original failing case
- Monitor system behavior post-fix
- Establish ongoing monitoring for similar issues
- Create runbook for future debugging

This design ensures systematic, evidence-based debugging that leads to permanent fixes rather than temporary workarounds.