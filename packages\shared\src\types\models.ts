import { z } from 'zod';
import { ActivityType } from './activity';

// Database type schemas with runtime validation

export const UserSchema = z.object({
  id: z.string(),
  email: z.string().email(),
  name: z.string().optional(),
  avatar_url: z.string().optional(),
});

export const ProfileSchema = z.object({
  id: z.string().uuid(),
  email: z.string().email(),
  name: z.string().optional(),
  avatar_url: z.string().optional(),
  bio: z.string().optional(),
  preferences: z.record(z.string(), z.unknown()).default({}),
  created_at: z.string(),
  updated_at: z.string(),
});

export const TripSchema = z.object({
  id: z.string().uuid(),
  user_id: z.string().uuid(),
  title: z.string().min(1),
  description: z.string().nullable(),
  destination: z.string().nullable(),
  start_date: z.string().nullable(),
  end_date: z.string().nullable(),
  status: z.enum(['draft', 'planning', 'confirmed', 'in_progress', 'completed', 'cancelled']).default('draft'),
  visibility: z.enum(['private', 'unlisted', 'public']).default('private'),
  cover_image: z.string().nullable(),
  metadata: z.record(z.string(), z.unknown()).default({}),
  tags: z.array(z.string()).default([]),
  budget_amount: z.number().nullable(),
  budget_currency: z.string().regex(/^[A-Z]{3}$/).default('USD'),
  views: z.number().nullable().optional(),
  created_at: z.string(),
  updated_at: z.string(),
  deleted_at: z.string().nullable(),
  activities: z.array(z.lazy(() => ActivitySchema)).optional(),
});

export const ActivitySchema = z.object({
  id: z.string().uuid(),
  trip_id: z.string().uuid(),
  title: z.string().min(1),
  description: z.string().nullable(),
  type: z.nativeEnum(ActivityType).default(ActivityType.activity),
  position: z.number().int().min(0),
  start_time: z.string().nullable(),
  end_time: z.string().nullable(),
  location: z.string().nullable(),
  location_lat: z.number().nullable(),
  location_lng: z.number().nullable(),
  price: z.number().min(0).nullable(),
  currency: z.string().regex(/^[A-Z]{3}$/).default('USD'),
  booking_reference: z.string().nullable(),
  booking_url: z.string().url().nullable().optional(),
  affiliate_url: z.string().url().nullable().optional(),
  notes: z.string().nullable(),
  metadata: z.record(z.string(), z.unknown()).default({}),
  attachments: z.array(z.string()).default([]),
  created_at: z.string(),
  updated_at: z.string(),
});

// Export types from schemas
export type User = z.infer<typeof UserSchema>;
export type Profile = z.infer<typeof ProfileSchema>;
export type Trip = z.infer<typeof TripSchema>;
export type Activity = z.infer<typeof ActivitySchema>;

// Extended types
export type TripWithActivities = Trip & { activities: Activity[] };