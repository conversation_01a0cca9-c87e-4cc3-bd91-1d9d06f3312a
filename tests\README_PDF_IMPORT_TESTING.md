# PDF Import Testing Suite

This directory contains comprehensive testing tools for the PDF import functionality, designed to validate the recent fixes for hanging issues and performance improvements.

## Quick Start

### Automated Testing

```bash
# Run the comprehensive PDF import test
pnpm test:pdf-import

# Skip pre-flight checks and run immediately
pnpm test:pdf-import-force

# Or run directly
node scripts/run-pdf-import-test.js
```

### Manual Testing

Follow the detailed procedures in `manual/PDF_IMPORT_MANUAL_TEST_PROCEDURE.md`

## Test Suite Components

### 1. Root Cause Analysis
- **File**: `PDF_IMPORT_HANGING_ROOT_CAUSE_ANALYSIS.md`
- **Purpose**: Comprehensive analysis of the hanging issue symptoms, causes, and verification steps

### 2. Automated Integration Test
- **File**: `integration/pdf-import-comprehensive-test.js`
- **Purpose**: End-to-end automated testing with detailed monitoring and reporting
- **Features**:
  - Authentication testing
  - PDF upload validation
  - Real-time progress monitoring
  - Timeout detection
  - Performance measurement
  - Comprehensive reporting

### 3. Manual Testing Procedures
- **File**: `manual/PDF_IMPORT_MANUAL_TEST_PROCEDURE.md`
- **Purpose**: Step-by-step manual testing procedures for edge cases and detailed analysis

### 4. Test Runner Script
- **File**: `scripts/run-pdf-import-test.js`
- **Purpose**: Easy-to-use test runner with pre-flight checks and colored output

## Configuration

### Test File
Update the PDF file path in the test configuration:
```javascript
PDF_FILE_PATH: 'C:\\Users\\<USER>\\Travelviz\\Travelviz\\Updated 15-Day European Travel Itinerary_ London, Madrid, Lisbon and Porto.pdf'
```

### Test Account
Default test credentials:
- Email: `<EMAIL>`
- Password: From `.env.local` or `Flaremmk123!`

### Environment Variables
Required for full functionality:
- `OPENROUTER_API_KEY`: For AI model access
- `GOOGLE_GEMINI_API_KEY`: For Gemini model access
- `TEST_PASSWORD`: Test account password (optional)

## Test Objectives

### Primary Goals
1. **Verify Recent Fixes**: Confirm that DeepSeek model selection and aggressive timeouts work
2. **Identify Remaining Issues**: Detect any lingering problems in the import pipeline
3. **Test Error Handling**: Validate fallback mechanisms and error reporting
4. **Measure Performance**: Track improvements in response times and success rates

### Success Criteria
- ✅ Authentication completes in < 2 seconds
- ✅ PDF upload completes in < 30 seconds
- ✅ Parsing completes in < 3 minutes
- ✅ No hanging at 40% progress
- ✅ Specific error messages (not generic "Something went wrong!")
- ✅ Proper model selection based on content complexity
- ✅ Fallback mechanisms activate when needed

## Understanding Test Results

### Automated Test Output

The automated test provides detailed logging and generates a JSON report with:

```json
{
  "testConfiguration": { /* Test settings */ },
  "testResults": {
    "success": true/false,
    "authTime": 1234,
    "uploadTime": 5678,
    "parsingTime": 45000,
    "progressUpdates": [ /* Array of progress updates */ ],
    "errors": [ /* Any errors encountered */ ],
    "timeouts": [ /* Timeout events */ ],
    "performanceMetrics": { /* Timing analysis */ }
  },
  "summary": {
    "keyFindings": [ /* Important discoveries */ ]
  }
}
```

### Key Metrics to Monitor

1. **Authentication Time**: Should be < 2000ms
2. **Upload Time**: Should be < 30000ms  
3. **Parsing Time**: Should be < 180000ms (3 minutes)
4. **Progress Updates**: Should occur regularly every 2-15 seconds
5. **Error Count**: Should be 0 for successful runs
6. **Timeout Events**: Should be 0 for successful runs

### Warning Signs

🚨 **Process Hanging at 40%**
- Indicates AI model selection or timeout issues
- Check backend logs for model selection
- Verify Kimi-K2 vs DeepSeek selection

🚨 **Multiple Timeout Events**
- Suggests timeout mechanisms aren't working
- May indicate network or model availability issues

🚨 **Generic Error Messages**
- "Something went wrong!" instead of specific errors
- Indicates error handling improvements needed

## Troubleshooting

### Common Issues

**Backend Not Running**
```bash
cd packages/hub
pnpm dev
```

**PDF File Not Found**
- Update `PDF_FILE_PATH` in test configuration
- Ensure file exists and is readable

**Authentication Failures**
- Check test credentials in `.env.local`
- Verify Supabase configuration

**AI Model Issues**
- Check `OPENROUTER_API_KEY` environment variable
- Monitor backend logs for model selection decisions
- Verify circuit breaker status

### Debug Mode

For detailed debugging, run with additional logging:
```bash
DEBUG=* node scripts/run-pdf-import-test.js
```

## Integration with CI/CD

The test suite can be integrated into CI/CD pipelines:

```yaml
# Example GitHub Actions step
- name: Test PDF Import
  run: |
    pnpm test:pdf-import
  env:
    OPENROUTER_API_KEY: ${{ secrets.OPENROUTER_API_KEY }}
    GOOGLE_GEMINI_API_KEY: ${{ secrets.GOOGLE_GEMINI_API_KEY }}
```

## Contributing

When adding new tests or modifying existing ones:

1. **Update Documentation**: Keep this README and test procedures current
2. **Add Logging**: Include comprehensive logging for debugging
3. **Error Handling**: Ensure graceful failure and informative error messages
4. **Performance Tracking**: Include timing measurements for new features
5. **Validation**: Test both success and failure scenarios

## Related Documentation

- `PDF_IMPORT_TIMEOUT_FIX_SUMMARY.md`: Previous fix implementation details
- `PDF_IMPORT_HANGING_FIX_SUMMARY.md`: Recent hanging issue fixes
- `packages/hub/src/config/ai.config.ts`: AI model configuration
- `packages/hub/src/services/ai-parser.service.ts`: Core parsing logic

## Support

For issues with the test suite:
1. Check the troubleshooting section above
2. Review backend logs for detailed error information
3. Run manual tests to isolate specific problems
4. Check the root cause analysis document for known issues
