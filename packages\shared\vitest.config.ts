import { defineConfig } from 'vitest/config';
import { resolve } from 'path';

export default defineConfig({
  test: {
    globals: true,
    environment: 'node',
    testTimeout: 30000,
    hookTimeout: 30000,
    reporters: process.env.CI === 'true' 
      ? ['github-actions', 'json'] 
      : ['verbose'],
    // Performance optimizations for CI
    pool: 'forks',
    poolOptions: {
      forks: {
        maxForks: process.env.CI === 'true' ? 1 : 2,
        minForks: 1,
      },
    },
    isolate: true,
    coverage: {
      reporter: ['text', 'json', 'html', 'lcov'],
      provider: 'v8',
      all: false,
      include: ['src/**/*.{ts,tsx}'],
      exclude: [
        'src/**/*.d.ts',
        'src/**/*.test.{ts,tsx}',
        'src/**/*.spec.{ts,tsx}',
        'src/**/__tests__/**',
        'src/types/**',
        'node_modules/**',
      ],
      thresholds: {
        branches: 95,  // Higher for utility functions
        functions: 95,
        lines: 95,
        statements: 95,
      },
    },
  },
  resolve: {
    alias: {
      '@': resolve(__dirname, './src'),
    },
  },
});