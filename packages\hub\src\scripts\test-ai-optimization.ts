#!/usr/bin/env node

/**
 * Test script for AI Model Optimization implementation
 * Validates database schema, services, and model selection logic
 */

import { logger } from '../utils/logger';
import { getSupabaseClient } from '../lib/supabase';
import { usageTrackingService } from '../services/usage-tracking.service';
import { modelSelectorService } from '../services/model-selector.service';
import { promptManager } from '../services/prompt-manager.service';
import { enhancedAIRouterService } from '../services/enhanced-ai-router.service';

async function testDatabaseSchema() {
  console.log('\n🔍 Testing Database Schema...');
  
  try {
    // Test ai_model_usage table
    const { data: usageData, error: usageError } = await getSupabaseClient()
      .from('ai_model_usage')
      .select('*')
      .limit(1);
    
    if (usageError) {
      throw new Error(`ai_model_usage table error: ${usageError.message}`);
    }
    console.log('✅ ai_model_usage table accessible');

    // Test ai_request_logs table
    const { data: logsData, error: logsError } = await getSupabaseClient()
      .from('ai_request_logs')
      .select('*')
      .limit(1);
    
    if (logsError) {
      throw new Error(`ai_request_logs table error: ${logsError.message}`);
    }
    console.log('✅ ai_request_logs table accessible');

    // Test ai_model_configs table
    const { data: configsData, error: configsError } = await getSupabaseClient()
      .from('ai_model_configs')
      .select('*');
    
    if (configsError) {
      throw new Error(`ai_model_configs table error: ${configsError.message}`);
    }
    console.log(`✅ ai_model_configs table accessible (${configsData?.length || 0} models configured)`);

    // Test enhanced ai_import_logs columns
    const { data: importData, error: importError } = await getSupabaseClient()
      .from('ai_import_logs')
      .select('model_used, input_tokens, output_tokens, processing_cost, fallback_attempts')
      .limit(1);
    
    if (importError) {
      throw new Error(`ai_import_logs enhanced columns error: ${importError.message}`);
    }
    console.log('✅ ai_import_logs enhanced columns accessible');

    // Test database functions
    const { data: functionData, error: functionError } = await getSupabaseClient()
      .rpc('get_model_usage', { model_id_param: 'test-model' });
    
    if (functionError) {
      throw new Error(`get_model_usage function error: ${functionError.message}`);
    }
    console.log('✅ Database functions working');

    console.log('✅ Database schema validation completed successfully');
  } catch (error) {
    console.error('❌ Database schema validation failed:', error);
    throw error;
  }
}

async function testUsageTrackingService() {
  console.log('\n📊 Testing Usage Tracking Service...');
  
  try {
    // Test tracking a request
    await usageTrackingService.trackRequest('test-model', 100, 200);
    console.log('✅ Request tracking works');

    // Test getting current usage
    const usage = await usageTrackingService.getCurrentUsage('test-model');
    console.log(`✅ Current usage retrieved: ${usage.requestCount} requests`);

    // Test getting all usage
    const allUsage = await usageTrackingService.getAllUsage();
    console.log(`✅ All usage retrieved: ${Object.keys(allUsage).length} models`);

    // Test model availability check
    const isAvailable = await usageTrackingService.isModelAvailable('moonshotai/kimi-k2:free');
    console.log(`✅ Model availability check: ${isAvailable ? 'available' : 'not available'}`);

    console.log('✅ Usage Tracking Service validation completed');
  } catch (error) {
    console.error('❌ Usage Tracking Service validation failed:', error);
    throw error;
  }
}

async function testModelSelectorService() {
  console.log('\n🤖 Testing Model Selector Service...');
  
  try {
    // Test token estimation
    const testContent = `
      Day 1: Arrive in Paris
      - 10:00 AM: Land at Charles de Gaulle Airport
      - 2:00 PM: Check into Hotel Louvre
      - 4:00 PM: Visit Eiffel Tower
      - 7:00 PM: Dinner at Le Comptoir du Relais
      
      Day 2: Museums and Culture
      - 9:00 AM: Louvre Museum
      - 2:00 PM: Lunch at Café de Flore
      - 4:00 PM: Notre-Dame Cathedral
      - 7:00 PM: Seine River Cruise
    `;

    const tokenEstimate = modelSelectorService.estimateTokens(testContent);
    console.log(`✅ Token estimation: ${tokenEstimate.inputTokens} input, ${tokenEstimate.outputTokens} output, complexity: ${tokenEstimate.complexity}`);

    // Test model selection
    const modelSelection = await modelSelectorService.selectModel(testContent);
    console.log(`✅ Model selection: ${modelSelection.modelId} (${modelSelection.provider}) - ${modelSelection.reason}`);

    // Test fallback chain
    const fallbackChain = modelSelectorService.getFallbackChain(modelSelection.modelId, tokenEstimate.complexity);
    console.log(`✅ Fallback chain: ${fallbackChain.join(' → ')}`);

    console.log('✅ Model Selector Service validation completed');
  } catch (error) {
    console.error('❌ Model Selector Service validation failed:', error);
    throw error;
  }
}

async function testPromptManager() {
  console.log('\n📝 Testing Prompt Manager...');
  
  try {
    // Test model-specific prompts
    const models = [
      'moonshotai/kimi-k2:free',
      'google/gemini-2.5-pro',
      'google/gemini-2.5-flash',
      'google/gemini-2.0-flash',
      'openai/gpt-4.1-nano'
    ];

    for (const modelId of models) {
      const prompt = promptManager.getSystemPrompt(modelId);
      const formatInstructions = promptManager.getFormatInstructions(modelId);
      
      if (!prompt || prompt.length < 100) {
        throw new Error(`Invalid prompt for model ${modelId}`);
      }
      
      console.log(`✅ ${modelId}: prompt (${prompt.length} chars), format instructions (${formatInstructions.length} chars)`);
    }

    // Test default prompt fallback
    const defaultPrompt = promptManager.getSystemPrompt('unknown-model');
    if (!defaultPrompt || defaultPrompt.length < 100) {
      throw new Error('Default prompt is invalid');
    }
    console.log('✅ Default prompt fallback works');

    console.log('✅ Prompt Manager validation completed');
  } catch (error) {
    console.error('❌ Prompt Manager validation failed:', error);
    throw error;
  }
}

async function testEnhancedAIRouter() {
  console.log('\n🚀 Testing Enhanced AI Router Service...');
  
  try {
    // Test usage statistics
    const usageStats = await enhancedAIRouterService.getUsageStats();
    console.log(`✅ Usage statistics: ${usageStats.totalRequests} total requests, $${usageStats.totalCost.toFixed(4)} total cost`);

    // Test rate limit handling (mock scenario)
    try {
      const fallbackModel = await enhancedAIRouterService.handleRateLimit(
        'moonshotai/kimi-k2:free', 
        new Error('Rate limit exceeded')
      );
      console.log(`✅ Rate limit handling: fallback to ${fallbackModel}`);
    } catch (error) {
      // Expected if all models are rate limited
      console.log('✅ Rate limit handling: all models rate limited (expected in test)');
    }

    console.log('✅ Enhanced AI Router Service validation completed');
  } catch (error) {
    console.error('❌ Enhanced AI Router Service validation failed:', error);
    throw error;
  }
}

async function testModelPriorityFlow() {
  console.log('\n🔄 Testing Model Priority Flow...');
  
  try {
    // Test the priority flow: Moonshot → Gemini → OpenRouter
    const testContent = 'Plan a 3-day trip to Tokyo with visits to temples, museums, and restaurants.';
    
    // Check Moonshot availability (should be first choice if under 1000 requests)
    const moonshotUsage = await usageTrackingService.getCurrentUsage('moonshotai/kimi-k2:free');
    const moonshotAvailable = await usageTrackingService.isModelAvailable('moonshotai/kimi-k2:free');
    
    console.log(`📊 Moonshot AI: ${moonshotUsage.requestCount}/1000 requests used, available: ${moonshotAvailable}`);
    
    // Check Gemini models
    const geminiModels = ['google/gemini-2.5-flash', 'google/gemini-2.0-flash', 'google/gemini-2.5-pro'];
    for (const model of geminiModels) {
      const usage = await usageTrackingService.getCurrentUsage(model);
      const available = await usageTrackingService.isModelAvailable(model);
      console.log(`📊 ${model}: ${usage.requestCount} requests used, available: ${available}`);
    }
    
    // Test model selection with priority
    const selection = await modelSelectorService.selectModel(testContent);
    console.log(`🎯 Selected model: ${selection.modelId} (${selection.reason})`);
    
    // Verify priority logic
    if (moonshotAvailable && moonshotUsage.requestCount < 1000) {
      if (selection.modelId !== 'moonshotai/kimi-k2:free') {
        console.warn('⚠️  Expected Moonshot to be selected but got:', selection.modelId);
      } else {
        console.log('✅ Priority logic working: Moonshot selected as expected');
      }
    } else {
      console.log('✅ Priority logic working: Moonshot not available, using fallback');
    }

    console.log('✅ Model Priority Flow validation completed');
  } catch (error) {
    console.error('❌ Model Priority Flow validation failed:', error);
    throw error;
  }
}

async function runAllTests() {
  console.log('🧪 AI Model Optimization Test Suite');
  console.log('=====================================');
  
  try {
    await testDatabaseSchema();
    await testUsageTrackingService();
    await testModelSelectorService();
    await testPromptManager();
    await testEnhancedAIRouter();
    await testModelPriorityFlow();
    
    console.log('\n🎉 All tests passed successfully!');
    console.log('✅ AI Model Optimization implementation is working correctly');
    
  } catch (error) {
    console.error('\n💥 Test suite failed:', error);
    process.exit(1);
  }
}

// Run tests if called directly
if (require.main === module) {
  runAllTests().catch(error => {
    console.error('Test execution failed:', error);
    process.exit(1);
  });
}

export { runAllTests };