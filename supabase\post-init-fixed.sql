-- Post-initialization script for Supabase-specific configurations
-- Run this AFTER init-fixed.sql

-- Note: Auth configuration updates must be done through Supabase Dashboard
-- Go to Authentication > Settings to configure:
-- - Email confirmations (disable for development)
-- - JWT expiry times
-- - Password requirements

-- 1. Create storage buckets for trip images and attachments
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES 
    ('trip-covers', 'trip-covers', true, 5242880, ARRAY['image/jpeg', 'image/png', 'image/webp']),
    ('activity-attachments', 'activity-attachments', false, 10485760, ARRAY['image/jpeg', 'image/png', 'image/pdf'])
ON CONFLICT (id) DO NOTHING;

-- 2. Storage policies
DROP POLICY IF EXISTS "Users can upload their own trip covers" ON storage.objects;
DROP POLICY IF EXISTS "Trip covers are publicly viewable" ON storage.objects;
DROP POLICY IF EXISTS "Users can update their own trip covers" ON storage.objects;
DROP POLICY IF EXISTS "Users can delete their own trip covers" ON storage.objects;

CREATE POLICY "Users can upload their own trip covers"
ON storage.objects FOR INSERT
WITH CHECK (
    bucket_id = 'trip-covers' 
    AND auth.uid()::text = (storage.foldername(name))[1]
);

CREATE POLICY "Trip covers are publicly viewable"
ON storage.objects FOR SELECT
USING (bucket_id = 'trip-covers');

CREATE POLICY "Users can update their own trip covers"
ON storage.objects FOR UPDATE
USING (
    bucket_id = 'trip-covers' 
    AND auth.uid()::text = (storage.foldername(name))[1]
);

CREATE POLICY "Users can delete their own trip covers"
ON storage.objects FOR DELETE
USING (
    bucket_id = 'trip-covers' 
    AND auth.uid()::text = (storage.foldername(name))[1]
);

-- 3. Activity attachments policies
DROP POLICY IF EXISTS "Users can upload activity attachments" ON storage.objects;
DROP POLICY IF EXISTS "Users can view their activity attachments" ON storage.objects;
DROP POLICY IF EXISTS "Users can update their activity attachments" ON storage.objects;
DROP POLICY IF EXISTS "Users can delete their activity attachments" ON storage.objects;

CREATE POLICY "Users can upload activity attachments"
ON storage.objects FOR INSERT
WITH CHECK (
    bucket_id = 'activity-attachments' 
    AND auth.uid()::text = (storage.foldername(name))[1]
);

CREATE POLICY "Users can view their activity attachments"
ON storage.objects FOR SELECT
USING (
    bucket_id = 'activity-attachments' 
    AND auth.uid()::text = (storage.foldername(name))[1]
);

CREATE POLICY "Users can update their activity attachments"
ON storage.objects FOR UPDATE
USING (
    bucket_id = 'activity-attachments' 
    AND auth.uid()::text = (storage.foldername(name))[1]
);

CREATE POLICY "Users can delete their activity attachments"
ON storage.objects FOR DELETE
USING (
    bucket_id = 'activity-attachments' 
    AND auth.uid()::text = (storage.foldername(name))[1]
);

-- 4. Create helper function to get user's upcoming trips
CREATE OR REPLACE FUNCTION get_upcoming_trips(p_user_id UUID)
RETURNS SETOF trips AS $$
BEGIN
    RETURN QUERY
    SELECT *
    FROM trips
    WHERE user_id = p_user_id
    AND deleted_at IS NULL
    AND start_date >= CURRENT_DATE
    ORDER BY start_date ASC;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 5. Create function to calculate trip duration
CREATE OR REPLACE FUNCTION calculate_trip_duration(p_start_date DATE, p_end_date DATE)
RETURNS INTEGER AS $$
BEGIN
    IF p_start_date IS NULL OR p_end_date IS NULL THEN
        RETURN NULL;
    END IF;
    RETURN p_end_date - p_start_date + 1;
END;
$$ LANGUAGE plpgsql IMMUTABLE;

-- 6. Create function to get trip activity count
CREATE OR REPLACE FUNCTION get_trip_activity_count(p_trip_id UUID)
RETURNS INTEGER AS $$
DECLARE
    activity_count INTEGER;
BEGIN
    SELECT COUNT(*)
    INTO activity_count
    FROM activities
    WHERE trip_id = p_trip_id;
    
    RETURN activity_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 7. Create view for trip summaries
CREATE OR REPLACE VIEW trip_summaries AS
SELECT 
    t.id,
    t.user_id,
    t.title,
    t.description,
    t.destination,
    t.start_date,
    t.end_date,
    t.status,
    t.visibility,
    t.cover_image,
    t.budget_amount,
    t.budget_currency,
    t.created_at,
    t.updated_at,
    calculate_trip_duration(t.start_date, t.end_date) as duration_days,
    get_trip_activity_count(t.id) as activity_count,
    (
        SELECT SUM(a.price)
        FROM activities a
        WHERE a.trip_id = t.id
        AND a.currency = t.budget_currency
    ) as total_spent
FROM trips t
WHERE t.deleted_at IS NULL;

-- 8. Grant necessary permissions
GRANT USAGE ON SCHEMA public TO anon, authenticated;
GRANT ALL ON ALL TABLES IN SCHEMA public TO anon, authenticated;
GRANT ALL ON ALL SEQUENCES IN SCHEMA public TO anon, authenticated;
GRANT EXECUTE ON ALL FUNCTIONS IN SCHEMA public TO anon, authenticated;

-- 9. Create indexes for common queries
-- Note: Removed date comparison from index predicate as CURRENT_DATE is not immutable
CREATE INDEX IF NOT EXISTS idx_trips_user_start_date ON trips(user_id, start_date) 
WHERE deleted_at IS NULL;

CREATE INDEX IF NOT EXISTS idx_activities_trip_price ON activities(trip_id, price) 
WHERE price IS NOT NULL;

-- 10. Add helpful comments
COMMENT ON TABLE trips IS 'Stores user travel trips with planning details';
COMMENT ON TABLE activities IS 'Stores individual activities/events within a trip';
COMMENT ON TABLE trip_shares IS 'Manages trip collaboration and sharing between users';
COMMENT ON TABLE profiles IS 'Extended user profile information';
COMMENT ON COLUMN trips.visibility IS 'Controls who can see the trip: private (owner only), unlisted (with link), public (discoverable)';
COMMENT ON COLUMN activities.type IS 'Type of activity: flight, accommodation, transport, dining, activity, shopping, other';

-- Success message
DO $$
BEGIN
    RAISE NOTICE 'Post-initialization completed successfully!';
    RAISE NOTICE 'Remember to configure authentication settings in Supabase Dashboard';
END $$;