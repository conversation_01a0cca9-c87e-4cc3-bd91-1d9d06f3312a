#!/usr/bin/env node

/**
 * Test script for Enhanced AI Router Service
 * Verifies that the AI model optimization system is working correctly
 */

// Simple test without complex imports first
console.log('Starting Enhanced AI Router test...');

async function testEnhancedAIRouter() {
  console.log('🧪 Testing Enhanced AI Router Service...\n');

  try {
    // Test basic functionality first
    console.log('1. Testing basic service availability...');

    // Try to import services dynamically
    let enhancedAIRouterService, usageTrackingService, modelSelectorService;

    try {
      const enhancedModule = await import('../services/enhanced-ai-router.service.js');
      enhancedAIRouterService = enhancedModule.enhancedAIRouterService;
      console.log('✅ Enhanced AI Router Service imported successfully');
    } catch (error) {
      console.log('❌ Failed to import Enhanced AI Router Service:', error instanceof Error ? error.message : String(error));
      return false;
    }

    try {
      const usageModule = await import('../services/usage-tracking.service.js');
      usageTrackingService = usageModule.usageTrackingService;
      console.log('✅ Usage Tracking Service imported successfully');
    } catch (error) {
      console.log('❌ Failed to import Usage Tracking Service:', error instanceof Error ? error.message : String(error));
      return false;
    }

    try {
      const modelModule = await import('../services/model-selector.service.js');
      modelSelectorService = modelModule.modelSelectorService;
      console.log('✅ Model Selector Service imported successfully');
    } catch (error) {
      console.log('❌ Failed to import Model Selector Service:', error instanceof Error ? error.message : String(error));
      return false;
    }

    // Test 2: Basic service methods
    console.log('\n2. Testing service methods...');
    console.log('   - Enhanced AI Router parseContent method:', typeof enhancedAIRouterService?.parseContent);
    console.log('   - Usage Tracking getCurrentUsage method:', typeof usageTrackingService?.getCurrentUsage);
    console.log('   - Model Selector selectModel method:', typeof modelSelectorService?.selectModel);

    console.log('\n🎉 Basic tests completed successfully!');
    console.log('Note: Full integration tests require a running environment with database connections.');
    return true;

  } catch (error) {
    console.error('❌ Test failed:', error);
    return false;
  }
}

// Run the test if this script is executed directly
if (require.main === module) {
  testEnhancedAIRouter()
    .then((success) => {
      process.exit(success ? 0 : 1);
    })
    .catch((error) => {
      console.error('Test execution failed:', error);
      process.exit(1);
    });
}

export { testEnhancedAIRouter };
