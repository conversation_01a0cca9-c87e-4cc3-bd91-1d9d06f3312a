/**
 * Account lockout service for preventing brute force attacks
 */

import { getSupabaseClient, TABLES } from '../lib/supabase';
import { logger } from '../utils/logger';

interface FailedAttempt {
  email: string;
  ipAddress?: string;
  userAgent?: string;
  attemptTime?: Date;
}

interface LockoutStatus {
  isLocked: boolean;
  lockedUntil?: Date;
  attemptCount: number;
}

export class AccountLockoutService {
  private readonly MAX_ATTEMPTS = 5;
  private readonly LOCKOUT_DURATION = 30 * 60 * 1000; // 30 minutes
  private readonly ATTEMPT_WINDOW = 15 * 60 * 1000; // 15 minutes

  async recordFailedAttempt(data: FailedAttempt): Promise<void> {
    const supabase = getSupabaseClient();
    
    try {
      // Record failed attempt in database
      await supabase
        .from(TABLES.AUTH_FAILED_ATTEMPTS)
        .insert({
          email: data.email.toLowerCase(),
          ip_address: data.ipAddress,
          user_agent: data.userAgent,
          attempted_at: new Date().toISOString()
        });

      // Check if we should lock the account
      const recentAttempts = await this.getRecentAttempts(data.email);
      
      if (recentAttempts >= this.MAX_ATTEMPTS) {
        await this.lockAccount(data.email);
      }
    } catch (error) {
      logger.error('Failed to record failed attempt:', { error });
      // Don't throw - we don't want login to fail if lockout tracking fails
    }
  }

  async checkAccountLockout(email: string): Promise<LockoutStatus> {
    const supabase = getSupabaseClient();
    
    try {
      // Check if account is locked
      const { data: lockout } = await supabase
        .from(TABLES.AUTH_ACCOUNT_LOCKOUTS)
        .select('*')
        .eq('email', email.toLowerCase())
        .gte('locked_until', new Date().toISOString())
        .single();

      if (lockout) {
        return {
          isLocked: true,
          lockedUntil: new Date(lockout.locked_until),
          attemptCount: this.MAX_ATTEMPTS
        };
      }

      // Get recent attempt count
      const attemptCount = await this.getRecentAttempts(email);
      
      return {
        isLocked: false,
        attemptCount
      };
    } catch (error) {
      logger.error('Failed to check account lockout:', { error });
      // If we can't check, assume not locked
      return {
        isLocked: false,
        attemptCount: 0
      };
    }
  }

  async clearFailedAttempts(email: string): Promise<void> {
    const supabase = getSupabaseClient();
    
    try {
      // Clear failed attempts
      await supabase
        .from(TABLES.AUTH_FAILED_ATTEMPTS)
        .delete()
        .eq('email', email.toLowerCase());

      // Clear any lockouts
      await supabase
        .from(TABLES.AUTH_ACCOUNT_LOCKOUTS)
        .delete()
        .eq('email', email.toLowerCase());
    } catch (error) {
      logger.error('Failed to clear failed attempts:', { error });
    }
  }

  private async getRecentAttempts(email: string): Promise<number> {
    const supabase = getSupabaseClient();
    const windowStart = new Date(Date.now() - this.ATTEMPT_WINDOW).toISOString();
    
    try {
      const { count } = await supabase
        .from(TABLES.AUTH_FAILED_ATTEMPTS)
        .select('*', { count: 'exact', head: true })
        .eq('email', email.toLowerCase())
        .gte('attempted_at', windowStart);

      return count || 0;
    } catch (error) {
      logger.error('Failed to get recent attempts:', { error });
      return 0;
    }
  }

  private async lockAccount(email: string): Promise<void> {
    const supabase = getSupabaseClient();
    const lockedUntil = new Date(Date.now() + this.LOCKOUT_DURATION).toISOString();
    
    try {
      await supabase
        .from(TABLES.AUTH_ACCOUNT_LOCKOUTS)
        .upsert({
          email: email.toLowerCase(),
          locked_until: lockedUntil,
          created_at: new Date().toISOString()
        }, {
          onConflict: 'email'
        });
    } catch (error) {
      logger.error('Failed to lock account:', { error });
    }
  }
}