"use client";

import { useState } from 'react';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { 
  Search, 
  ArrowLeft, 
  MessageCircle, 
  Book, 
  Video, 
  Mail,
  ChevronRight,
  Star,
  ThumbsUp,
  ThumbsDown,
  ExternalLink,
  Download,
  Play
} from 'lucide-react';

const helpCategories = [
  {
    id: 'getting-started',
    title: 'Getting Started',
    description: 'Learn the basics of TravelViz',
    icon: '🚀',
    articles: 12,
    popular: true
  },
  {
    id: 'ai-features',
    title: 'AI Features',
    description: 'Make the most of AI-powered planning',
    icon: '🤖',
    articles: 8,
    popular: true
  },
  {
    id: 'trip-planning',
    title: 'Trip Planning',
    description: 'Create and organize your itineraries',
    icon: '🗺️',
    articles: 15,
    popular: false
  },
  {
    id: 'sharing',
    title: 'Sharing & Collaboration',
    description: 'Work together on trip plans',
    icon: '👥',
    articles: 6,
    popular: false
  },
  {
    id: 'export',
    title: 'Export & Sync',
    description: 'Get your plans everywhere',
    icon: '📤',
    articles: 9,
    popular: false
  },
  {
    id: 'account',
    title: 'Account & Billing',
    description: 'Manage your subscription',
    icon: '⚙️',
    articles: 7,
    popular: false
  }
];

const popularArticles = [
  {
    id: 1,
    title: 'How to import ChatGPT travel plans',
    category: 'AI Features',
    views: 2847,
    helpful: 94,
    readTime: '3 min'
  },
  {
    id: 2,
    title: 'Creating your first trip itinerary',
    category: 'Getting Started',
    views: 2156,
    helpful: 89,
    readTime: '5 min'
  },
  {
    id: 3,
    title: 'Sharing trips with friends and family',
    category: 'Sharing',
    views: 1923,
    helpful: 92,
    readTime: '4 min'
  },
  {
    id: 4,
    title: 'Exporting to Google Maps and PDF',
    category: 'Export',
    views: 1654,
    helpful: 87,
    readTime: '3 min'
  }
];

const videoTutorials = [
  {
    id: 1,
    title: 'TravelViz in 5 Minutes',
    description: 'Complete overview of all features',
    duration: '5:23',
    thumbnail: 'https://images.pexels.com/photos/1008155/pexels-photo-1008155.jpeg?auto=compress&cs=tinysrgb&w=300'
  },
  {
    id: 2,
    title: 'AI Import Deep Dive',
    description: 'Advanced tips for AI-generated plans',
    duration: '8:15',
    thumbnail: 'https://images.pexels.com/photos/1181467/pexels-photo-1181467.jpeg?auto=compress&cs=tinysrgb&w=300'
  },
  {
    id: 3,
    title: 'Collaboration Best Practices',
    description: 'Plan trips together effectively',
    duration: '6:42',
    thumbnail: 'https://images.pexels.com/photos/1181298/pexels-photo-1181298.jpeg?auto=compress&cs=tinysrgb&w=300'
  }
];

export default function HelpPage() {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('');

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <Link href="/dashboard" className="flex items-center space-x-2 text-gray-600 hover:text-gray-900">
              <ArrowLeft className="h-4 w-4" />
              <span>Back to Dashboard</span>
            </Link>
            <h1 className="text-xl font-bold text-gray-900">Help Center</h1>
            <div className="w-24"></div>
          </div>
        </div>
      </div>

      {/* Hero Section */}
      <div className="bg-gradient-to-r from-orange-500 to-pink-500 text-white py-16">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h1 className="text-4xl font-bold mb-4">How can we help you?</h1>
          <p className="text-xl text-white/90 mb-8">
            Find answers, tutorials, and get support for TravelViz
          </p>
          
          {/* Search */}
          <div className="max-w-2xl mx-auto">
            <div className="relative">
              <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
              <Input
                type="text"
                placeholder="Search for help articles, tutorials, or features..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-12 pr-4 py-4 text-lg bg-white text-gray-900 border-0 rounded-xl shadow-lg"
              />
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-8">
            {/* Categories */}
            <section>
              <h2 className="text-2xl font-bold text-gray-900 mb-6">Browse by Category</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {helpCategories.map((category) => (
                  <Card key={category.id} className="p-6 hover:shadow-lg transition-shadow cursor-pointer group">
                    <div className="flex items-start space-x-4">
                      <div className="text-3xl">{category.icon}</div>
                      <div className="flex-1">
                        <div className="flex items-center space-x-2 mb-2">
                          <h3 className="font-semibold text-gray-900 group-hover:text-orange-600">
                            {category.title}
                          </h3>
                          {category.popular && (
                            <Badge variant="outline" className="text-xs">Popular</Badge>
                          )}
                        </div>
                        <p className="text-gray-600 text-sm mb-3">{category.description}</p>
                        <div className="flex items-center justify-between">
                          <span className="text-xs text-gray-500">{category.articles} articles</span>
                          <ChevronRight className="h-4 w-4 text-gray-400 group-hover:text-orange-500" />
                        </div>
                      </div>
                    </div>
                  </Card>
                ))}
              </div>
            </section>

            {/* Popular Articles */}
            <section>
              <h2 className="text-2xl font-bold text-gray-900 mb-6">Popular Articles</h2>
              <div className="space-y-4">
                {popularArticles.map((article) => (
                  <Card key={article.id} className="p-6 hover:shadow-lg transition-shadow cursor-pointer group">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <h3 className="font-semibold text-gray-900 group-hover:text-orange-600 mb-2">
                          {article.title}
                        </h3>
                        <div className="flex items-center space-x-4 text-sm text-gray-500">
                          <span>{article.category}</span>
                          <span>•</span>
                          <span>{article.readTime} read</span>
                          <span>•</span>
                          <span>{article.views.toLocaleString()} views</span>
                          <span>•</span>
                          <div className="flex items-center space-x-1">
                            <ThumbsUp className="h-3 w-3" />
                            <span>{article.helpful}% helpful</span>
                          </div>
                        </div>
                      </div>
                      <ChevronRight className="h-5 w-5 text-gray-400 group-hover:text-orange-500" />
                    </div>
                  </Card>
                ))}
              </div>
            </section>

            {/* Video Tutorials */}
            <section>
              <h2 className="text-2xl font-bold text-gray-900 mb-6">Video Tutorials</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {videoTutorials.map((video) => (
                  <Card key={video.id} className="overflow-hidden hover:shadow-lg transition-shadow cursor-pointer group">
                    <div className="relative">
                      <img
                        src={video.thumbnail}
                        alt={video.title}
                        className="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300"
                      />
                      <div className="absolute inset-0 bg-black/40 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity">
                        <div className="w-16 h-16 bg-white rounded-full flex items-center justify-center">
                          <Play className="h-8 w-8 text-orange-500 ml-1" />
                        </div>
                      </div>
                      <div className="absolute bottom-2 right-2 bg-black/70 text-white text-xs px-2 py-1 rounded">
                        {video.duration}
                      </div>
                    </div>
                    <div className="p-4">
                      <h3 className="font-semibold text-gray-900 mb-2">{video.title}</h3>
                      <p className="text-gray-600 text-sm">{video.description}</p>
                    </div>
                  </Card>
                ))}
              </div>
            </section>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Quick Actions */}
            <Card className="p-6">
              <h3 className="font-semibold text-gray-900 mb-4">Quick Actions</h3>
              <div className="space-y-3">
                <Button variant="outline" className="w-full justify-start">
                  <MessageCircle className="h-4 w-4 mr-2" />
                  Contact Support
                </Button>
                <Button variant="outline" className="w-full justify-start">
                  <Video className="h-4 w-4 mr-2" />
                  Schedule Demo
                </Button>
                <Button variant="outline" className="w-full justify-start">
                  <Download className="h-4 w-4 mr-2" />
                  Download Guide
                </Button>
              </div>
            </Card>

            {/* Contact Info */}
            <Card className="p-6">
              <h3 className="font-semibold text-gray-900 mb-4">Need More Help?</h3>
              <div className="space-y-4">
                <div className="flex items-start space-x-3">
                  <Mail className="h-5 w-5 text-orange-500 mt-0.5" />
                  <div>
                    <p className="font-medium text-gray-900">Email Support</p>
                    <p className="text-sm text-gray-600"><EMAIL></p>
                    <p className="text-xs text-gray-500">Response within 24 hours</p>
                  </div>
                </div>
                
                <div className="flex items-start space-x-3">
                  <MessageCircle className="h-5 w-5 text-orange-500 mt-0.5" />
                  <div>
                    <p className="font-medium text-gray-900">Live Chat</p>
                    <p className="text-sm text-gray-600">Available 9 AM - 6 PM EST</p>
                    <Button size="sm" className="mt-2 btn-primary">
                      Start Chat
                    </Button>
                  </div>
                </div>
              </div>
            </Card>

            {/* Community */}
            <Card className="p-6">
              <h3 className="font-semibold text-gray-900 mb-4">Community</h3>
              <div className="space-y-3">
                <a href="#" className="flex items-center justify-between p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
                  <span className="text-sm font-medium text-gray-900">User Forum</span>
                  <ExternalLink className="h-4 w-4 text-gray-400" />
                </a>
                <a href="#" className="flex items-center justify-between p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
                  <span className="text-sm font-medium text-gray-900">Feature Requests</span>
                  <ExternalLink className="h-4 w-4 text-gray-400" />
                </a>
                <a href="#" className="flex items-center justify-between p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
                  <span className="text-sm font-medium text-gray-900">Travel Tips Blog</span>
                  <ExternalLink className="h-4 w-4 text-gray-400" />
                </a>
              </div>
            </Card>

            {/* Status */}
            <Card className="p-6">
              <h3 className="font-semibold text-gray-900 mb-4">System Status</h3>
              <div className="flex items-center space-x-2">
                <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                <span className="text-sm text-gray-600">All systems operational</span>
              </div>
              <a href="#" className="text-xs text-orange-600 hover:underline mt-2 inline-block">
                View status page
              </a>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
}