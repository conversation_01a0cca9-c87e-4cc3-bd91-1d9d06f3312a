#!/usr/bin/env node

/**
 * Test script for native Gemini API integration
 */

const axios = require('axios');
const path = require('path');
require('dotenv').config({ path: path.join(__dirname, '../.env.local') });

const TEST_TEXT = `
Day 1: Paris Adventure
- 9:00 AM: Arrive at Charles de Gaulle Airport
- 11:00 AM: Check into Le Meurice Hotel (€500/night)
- 2:00 PM: Visit the Louvre Museum
- 7:00 PM: Dinner cruise on the Seine (€120)

Day 2: Exploring Paris
- 9:00 AM: Eiffel Tower tour
- 1:00 PM: Lunch at a local bistro (€35)
- 3:00 PM: Shopping on Champs-Élysées
- 8:00 PM: Opera at Palais Garnier (€85)
`;

async function testGeminiNative() {
  const apiKey = process.env.GOOGLE_GEMINI_API_KEY;
  
  if (!apiKey) {
    console.error('❌ GOOGLE_GEMINI_API_KEY not found in .env.local');
    process.exit(1);
  }
  
  console.log('🧪 Testing Native Gemini API');
  console.log('=' .repeat(50));
  
  try {
    const startTime = Date.now();
    
    // Compressed prompt
    const prompt = `Extract trip from travel text. Return JSON only.

Text:
${TEST_TEXT}

JSON:
{
  "title": "descriptive title",
  "destination": "main location",
  "startDate": "YYYY-MM-DD",
  "endDate": "YYYY-MM-DD",
  "activities": [{
    "title": "activity name",
    "type": "flight|accommodation|transport|dining|activity|shopping|car_rental|tour|sightseeing|entertainment|other",
    "startTime": "HH:MM",
    "location": "place",
    "price": number,
    "currency": "USD/EUR/etc",
    "day": 1
  }]
}`;

    const response = await axios.post(
      'https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash-exp:generateContent',
      {
        contents: [
          {
            parts: [
              {
                text: prompt
              }
            ]
          }
        ],
        generationConfig: {
          temperature: 0.2,
          maxOutputTokens: 2048,
          topP: 0.8,
          topK: 10
        }
      },
      {
        headers: {
          'Content-Type': 'application/json',
          'x-goog-api-key': apiKey
        },
        timeout: 30000
      }
    );
    
    const duration = Date.now() - startTime;
    const generatedText = response.data.candidates?.[0]?.content?.parts?.[0]?.text;
    const usage = response.data.usageMetadata;
    
    console.log(`✅ Success in ${duration}ms`);
    
    if (usage) {
      console.log('\n📊 Token Usage:');
      console.log(`  Prompt tokens: ${usage.promptTokenCount}`);
      console.log(`  Response tokens: ${usage.candidatesTokenCount}`);
      console.log(`  Total tokens: ${usage.totalTokenCount}`);
      console.log(`  Cost: FREE 🎉`);
    }
    
    console.log(`\n📝 Raw Response:\n${generatedText}\n`);
    
    // Try to parse JSON
    try {
      let jsonString = generatedText;
      if (generatedText.includes('```')) {
        jsonString = generatedText.replace(/```json\s*/, '').replace(/```\s*$/, '');
      }
      const parsed = JSON.parse(jsonString.match(/\{[\s\S]*\}/)[0]);
      console.log(`✅ Valid JSON with ${parsed.activities?.length || 0} activities`);
      console.log(`📍 Destination: ${parsed.destination || 'Not specified'}`);
      console.log(`📋 Title: ${parsed.title || 'Not specified'}`);
      
      // Verify token reduction
      const originalPromptLength = 400; // Approximate original prompt
      const compressedPromptLength = prompt.length;
      const reduction = Math.round((1 - compressedPromptLength / originalPromptLength) * 100);
      console.log(`\n💡 Token Reduction: ~${reduction}% (${originalPromptLength} → ${compressedPromptLength} chars)`);
      
    } catch (e) {
      console.log(`❌ Failed to parse JSON: ${e.message}`);
    }
    
  } catch (error) {
    console.log(`❌ Error: ${error.message}`);
    if (error.response?.data) {
      console.log('API Error:', JSON.stringify(error.response.data, null, 2));
    }
  }
}

testGeminiNative().catch(console.error);