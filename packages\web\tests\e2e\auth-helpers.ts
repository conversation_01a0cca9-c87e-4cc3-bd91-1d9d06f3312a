import { Page, expect } from '@playwright/test';
import { createClient } from '@supabase/supabase-js';
import { setupTestEnv } from './setup-test-env';

// Setup test environment
const testEnv = setupTestEnv();

// Test credentials from environment
const TEST_EMAIL = testEnv.testEmail;
const TEST_PASSWORD = testEnv.testPassword;

// Supabase client for auth operations
const supabaseUrl = testEnv.supabaseUrl;
const supabaseAnonKey = testEnv.supabaseAnonKey;

export interface AuthSession {
  accessToken: string;
  refreshToken: string;
  expiresAt: number;
}

export class AuthHelpers {
  private static authSession: AuthSession | null = null;

  /**
   * Get auth token via Supabase SDK
   */
  static async getAuthToken(): Promise<string> {
    // Return cached token if still valid
    if (this.authSession && this.authSession.expiresAt > Date.now()) {
      return this.authSession.accessToken;
    }

    const supabase = createClient(supabaseUrl, supabaseAnonKey);
    
    const { data, error } = await supabase.auth.signInWithPassword({
      email: TEST_EMAIL,
      password: TEST_PASSWORD
    });

    if (error) {
      throw new Error(`Authentication failed: ${error.message}`);
    }

    if (!data.session) {
      throw new Error('No session returned from authentication');
    }

    // Cache the session
    this.authSession = {
      accessToken: data.session.access_token,
      refreshToken: data.session.refresh_token,
      expiresAt: Date.now() + (data.session.expires_in! * 1000)
    };

    return data.session.access_token;
  }

  /**
   * Login via UI
   */
  static async loginViaUI(page: Page): Promise<void> {
    await page.goto('http://localhost:3000/login');
    
    // Fill login form
    await page.fill('input[type="email"]', TEST_EMAIL);
    await page.fill('input[type="password"]', TEST_PASSWORD);
    
    // Submit form
    await page.click('button[type="submit"]');
    
    // Wait for redirect to dashboard or home
    await expect(page).toHaveURL(/\/(dashboard|home|import)/, { timeout: 10000 });
    
    // Verify we're logged in by checking for user menu or logout button
    await expect(page.locator('[data-testid="user-menu"], button:has-text("Logout")')).toBeVisible();
  }

  /**
   * Login directly by setting auth cookies/storage
   */
  static async loginDirectly(page: Page): Promise<void> {
    const token = await this.getAuthToken();
    
    // Set Supabase auth in localStorage
    await page.addInitScript((authData) => {
      // Supabase stores auth data in localStorage
      const storageKey = `sb-${authData.supabaseUrl.split('//')[1].split('.')[0]}-auth-token`;
      
      localStorage.setItem(storageKey, JSON.stringify({
        access_token: authData.token,
        token_type: 'bearer',
        expires_in: 3600,
        refresh_token: authData.refreshToken,
        user: {
          id: authData.userId,
          email: authData.email
        }
      }));
    }, {
      token,
      refreshToken: this.authSession?.refreshToken || '',
      supabaseUrl,
      email: TEST_EMAIL,
      userId: 'test-user-id' // This would come from the actual auth response
    });
    
    // Navigate to the app
    await page.goto('http://localhost:3000/');
  }

  /**
   * Logout
   */
  static async logout(page: Page): Promise<void> {
    // Try multiple selectors for logout
    const logoutSelectors = [
      'button:has-text("Logout")',
      'button:has-text("Sign out")',
      '[data-testid="logout-button"]',
      'a:has-text("Logout")'
    ];

    for (const selector of logoutSelectors) {
      const element = page.locator(selector);
      if (await element.isVisible()) {
        await element.click();
        break;
      }
    }

    // Wait for redirect to login or home
    await expect(page).toHaveURL(/\/(login|home|\/)/);
  }

  /**
   * Check if user is logged in
   */
  static async isLoggedIn(page: Page): Promise<boolean> {
    // Check for common logged-in indicators
    const indicators = [
      '[data-testid="user-menu"]',
      'button:has-text("Logout")',
      '[data-testid="user-avatar"]',
      '.user-profile'
    ];

    for (const selector of indicators) {
      if (await page.locator(selector).isVisible()) {
        return true;
      }
    }

    return false;
  }

  /**
   * Setup authenticated state for tests
   */
  static async setupAuthenticatedState(page: Page): Promise<void> {
    // Try direct login first (faster)
    try {
      await this.loginDirectly(page);
      
      // Verify we're logged in
      if (await this.isLoggedIn(page)) {
        return;
      }
    } catch (error) {
      console.log('Direct login failed, falling back to UI login:', error);
    }

    // Fallback to UI login
    await this.loginViaUI(page);
  }
}

/**
 * Storage state helper for Playwright
 */
export async function saveAuthState(page: Page): Promise<any> {
  return await page.context().storageState();
}

/**
 * Test user data
 */
export const testUser = {
  email: TEST_EMAIL,
  password: TEST_PASSWORD,
  name: 'Test User'
};