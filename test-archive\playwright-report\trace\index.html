
<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="icon" href="./playwright-logo.svg" type="image/svg+xml">
    <link rel="manifest" href="./manifest.webmanifest">
    <title>Playwright Trace Viewer</title>
    <script type="module" crossorigin src="./index.BjQ9je-p.js"></script>
    <link rel="modulepreload" crossorigin href="./assets/defaultSettingsView-Cjl_e5YM.js">
    <link rel="stylesheet" crossorigin href="./defaultSettingsView.NYBT19Ch.css">
    <link rel="stylesheet" crossorigin href="./index.CFOW-Ezb.css">
  </head>
  <body>
    <div id="root"></div>
    <dialog id="fallback-error">
      <p>The Playwright Trace Viewer must be loaded over the <code>http://</code> or <code>https://</code> protocols.</p>
      <p>For more information, please see the <a href="https://aka.ms/playwright/trace-viewer-file-protocol">docs</a>.</p>
    </dialog>
    <script>
      if (!/^https?:/.test(window.location.protocol)) {
        const fallbackErrorDialog = document.getElementById('fallback-error');
        const isTraceViewerInsidePlaywrightReport = window.location.protocol === 'file:' && window.location.pathname.endsWith('/playwright-report/trace/index.html');
        // Best-effort to show the report path in the dialog.
        if (isTraceViewerInsidePlaywrightReport) {
          const reportPath = (() => {
            const base = window.location.pathname.replace(/\/trace\/index\.html$/, '');
            if (navigator.platform === 'Win32')
              return base.replace(/^\//, '').replace(/\//g, '\\\\');
            return base;
          })();
          const reportLink = document.createElement('div');
          const command = `npx playwright show-report ${reportPath}`;
          reportLink.innerHTML = `You can open the report via <code>${command}</code> from your Playwright project. <button type="button">Copy Command</button>`;
          fallbackErrorDialog.insertBefore(reportLink, fallbackErrorDialog.children[1]);
          reportLink.querySelector('button').addEventListener('click', () => navigator.clipboard.writeText(command));
        }
        fallbackErrorDialog.show();
      }
    </script>
  </body>
</html>
