#!/usr/bin/env node

/**
 * Service Startup Validation Test
 * 
 * Validates that services can start successfully without errors
 * Tests build processes, dependency resolution, and basic functionality
 */

const fs = require('fs');
const path = require('path');
const { spawn } = require('child_process');

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function logHeader(message) {
  log('\n' + '='.repeat(50), 'cyan');
  log(`  ${message}`, 'bright');
  log('='.repeat(50), 'cyan');
}

function logSuccess(message) {
  log(`✅ ${message}`, 'green');
}

function logError(message) {
  log(`❌ ${message}`, 'red');
}

function logWarning(message) {
  log(`⚠️  ${message}`, 'yellow');
}

function logInfo(message) {
  log(`ℹ️  ${message}`, 'blue');
}

// Test results tracking
const testResults = {
  passed: 0,
  failed: 0,
  tests: []
};

function logTest(name, result, details = {}) {
  const passed = result === true || (typeof result === 'object' && result.success);
  testResults.tests.push({ name, passed, details });
  
  if (passed) {
    testResults.passed++;
    logSuccess(name);
  } else {
    testResults.failed++;
    logError(`${name} - ${details.error || 'Failed'}`);
  }
}

async function runCommand(command, args, options = {}) {
  return new Promise((resolve) => {
    const timeoutMs = options.timeout || 60000; // Default 60 seconds
    
    const child = spawn(command, args, {
      stdio: 'pipe',
      shell: true,
      ...options
    });

    let stdout = '';
    let stderr = '';
    let timedOut = false;

    child.stdout?.on('data', (data) => {
      stdout += data.toString();
    });

    child.stderr?.on('data', (data) => {
      stderr += data.toString();
    });

    child.on('close', (code) => {
      if (!timedOut) {
        resolve({ stdout, stderr, code, success: code === 0 });
      }
    });

    child.on('error', (error) => {
      if (!timedOut) {
        resolve({ stdout, stderr, code: -1, success: false, error: error.message });
      }
    });

    // Handle timeout
    const timeout = setTimeout(() => {
      timedOut = true;
      child.kill('SIGTERM');
      
      // Force kill after 5 seconds
      setTimeout(() => {
        child.kill('SIGKILL');
      }, 5000);
      
      resolve({ 
        stdout, 
        stderr, 
        code: -1, 
        success: false, 
        error: `Command timeout after ${timeoutMs}ms` 
      });
    }, timeoutMs);

    child.on('close', () => {
      clearTimeout(timeout);
    });
  });
}

async function validateDependencyInstallation() {
  logHeader('Dependency Installation Validation');

  const workspaceRoot = path.join(__dirname, '../..');

  // Check if package.json exists
  const packageJsonPath = path.join(workspaceRoot, 'package.json');
  const packageJsonExists = fs.existsSync(packageJsonPath);
  logTest('Root package.json exists', packageJsonExists, {
    error: packageJsonExists ? null : 'package.json not found in workspace root'
  });

  // Check if pnpm-lock.yaml exists
  const lockfilePath = path.join(workspaceRoot, 'pnpm-lock.yaml');
  const lockfileExists = fs.existsSync(lockfilePath);
  logTest('pnpm-lock.yaml exists', lockfileExists, {
    error: lockfileExists ? null : 'pnpm-lock.yaml not found - run pnpm install first'
  });

  // Install dependencies
  logInfo('Installing dependencies...');
  const installResult = await runCommand('pnpm', ['install', '--frozen-lockfile'], {
    cwd: workspaceRoot,
    timeout: 120000 // 2 minutes
  });

  logTest('Dependencies install successfully', installResult.success, {
    error: installResult.success ? null : `Install failed: ${installResult.stderr || installResult.error}`,
    stdout: installResult.stdout ? installResult.stdout.slice(-200) : null
  });

  // Check node_modules
  if (installResult.success) {
    const nodeModulesPath = path.join(workspaceRoot, 'node_modules');
    const nodeModulesExists = fs.existsSync(nodeModulesPath);
    logTest('node_modules directory created', nodeModulesExists, {
      error: nodeModulesExists ? null : 'node_modules directory not found after install'
    });
  }

  return installResult.success;
}

async function validateSharedPackageBuild() {
  logHeader('Shared Package Build Validation');

  const workspaceRoot = path.join(__dirname, '../..');

  // Build shared package
  logInfo('Building shared package...');
  const buildResult = await runCommand('pnpm', ['--filter', '@travelviz/shared', 'build'], {
    cwd: workspaceRoot,
    timeout: 60000 // 1 minute
  });

  logTest('Shared package builds successfully', buildResult.success, {
    error: buildResult.success ? null : `Build failed: ${buildResult.stderr || buildResult.error}`,
    stdout: buildResult.stdout ? buildResult.stdout.slice(-200) : null
  });

  // Check if dist directory was created
  if (buildResult.success) {
    const distPath = path.join(workspaceRoot, 'packages/shared/dist');
    const distExists = fs.existsSync(distPath);
    logTest('Shared package dist directory created', distExists, {
      error: distExists ? null : 'dist directory not found after build'
    });

    // Check for key files in dist
    if (distExists) {
      const indexPath = path.join(distPath, 'index.js');
      const indexExists = fs.existsSync(indexPath);
      logTest('Shared package index.js exists', indexExists, {
        error: indexExists ? null : 'index.js not found in dist directory'
      });
    }
  }

  return buildResult.success;
}

async function validateTypeScriptCompilation() {
  logHeader('TypeScript Compilation Validation');

  const workspaceRoot = path.join(__dirname, '../..');

  // Type check hub package
  logInfo('Type checking hub package...');
  const hubTypeCheckResult = await runCommand('pnpm', ['--filter', '@travelviz/hub', 'type-check'], {
    cwd: workspaceRoot,
    timeout: 60000 // 1 minute
  });

  logTest('Hub TypeScript compilation passes', hubTypeCheckResult.success, {
    error: hubTypeCheckResult.success ? null : `Type check failed: ${hubTypeCheckResult.stderr || hubTypeCheckResult.error}`,
    stdout: hubTypeCheckResult.stdout ? hubTypeCheckResult.stdout.slice(-200) : null
  });

  // Type check web package
  logInfo('Type checking web package...');
  const webTypeCheckResult = await runCommand('pnpm', ['--filter', '@travelviz/web', 'type-check'], {
    cwd: workspaceRoot,
    timeout: 60000 // 1 minute
  });

  logTest('Web TypeScript compilation passes', webTypeCheckResult.success, {
    error: webTypeCheckResult.success ? null : `Type check failed: ${webTypeCheckResult.stderr || webTypeCheckResult.error}`,
    stdout: webTypeCheckResult.stdout ? webTypeCheckResult.stdout.slice(-200) : null
  });

  return hubTypeCheckResult.success && webTypeCheckResult.success;
}

async function validateLinting() {
  logHeader('Code Linting Validation');

  const workspaceRoot = path.join(__dirname, '../..');

  // Lint all packages
  logInfo('Running linting...');
  const lintResult = await runCommand('pnpm', ['lint'], {
    cwd: workspaceRoot,
    timeout: 60000 // 1 minute
  });

  logTest('Code linting passes', lintResult.success, {
    error: lintResult.success ? null : `Linting failed: ${lintResult.stderr || lintResult.error}`,
    stdout: lintResult.stdout ? lintResult.stdout.slice(-200) : null
  });

  return lintResult.success;
}

async function validateBuildProcess() {
  logHeader('Build Process Validation');

  const workspaceRoot = path.join(__dirname, '../..');

  // Build hub package
  logInfo('Building hub package...');
  const hubBuildResult = await runCommand('pnpm', ['--filter', '@travelviz/hub', 'build'], {
    cwd: workspaceRoot,
    timeout: 120000 // 2 minutes
  });

  logTest('Hub package builds successfully', hubBuildResult.success, {
    error: hubBuildResult.success ? null : `Hub build failed: ${hubBuildResult.stderr || hubBuildResult.error}`,
    stdout: hubBuildResult.stdout ? hubBuildResult.stdout.slice(-200) : null
  });

  // Check hub dist directory
  if (hubBuildResult.success) {
    const hubDistPath = path.join(workspaceRoot, 'packages/hub/dist');
    const hubDistExists = fs.existsSync(hubDistPath);
    logTest('Hub dist directory created', hubDistExists, {
      error: hubDistExists ? null : 'Hub dist directory not found after build'
    });
  }

  // Build web package (Next.js)
  logInfo('Building web package...');
  const webBuildResult = await runCommand('pnpm', ['--filter', '@travelviz/web', 'build'], {
    cwd: workspaceRoot,
    timeout: 180000 // 3 minutes for Next.js build
  });

  logTest('Web package builds successfully', webBuildResult.success, {
    error: webBuildResult.success ? null : `Web build failed: ${webBuildResult.stderr || webBuildResult.error}`,
    stdout: webBuildResult.stdout ? webBuildResult.stdout.slice(-200) : null
  });

  // Check web .next directory
  if (webBuildResult.success) {
    const webNextPath = path.join(workspaceRoot, 'packages/web/.next');
    const webNextExists = fs.existsSync(webNextPath);
    logTest('Web .next directory created', webNextExists, {
      error: webNextExists ? null : 'Web .next directory not found after build'
    });
  }

  return hubBuildResult.success && webBuildResult.success;
}

async function validateServiceStartupReadiness() {
  logHeader('Service Startup Readiness Validation');

  const workspaceRoot = path.join(__dirname, '../..');

  // Check if services can be started (without actually starting them)
  // This validates that all dependencies and configurations are in place

  // Check hub package.json scripts
  const hubPackageJsonPath = path.join(workspaceRoot, 'packages/hub/package.json');
  if (fs.existsSync(hubPackageJsonPath)) {
    const hubPackageJson = JSON.parse(fs.readFileSync(hubPackageJsonPath, 'utf8'));
    const hasStartScript = !!hubPackageJson.scripts?.start;
    const hasDevScript = !!hubPackageJson.scripts?.dev;
    
    logTest('Hub has start script', hasStartScript, {
      error: hasStartScript ? null : 'Hub package.json missing start script'
    });
    
    logTest('Hub has dev script', hasDevScript, {
      error: hasDevScript ? null : 'Hub package.json missing dev script'
    });
  }

  // Check web package.json scripts
  const webPackageJsonPath = path.join(workspaceRoot, 'packages/web/package.json');
  if (fs.existsSync(webPackageJsonPath)) {
    const webPackageJson = JSON.parse(fs.readFileSync(webPackageJsonPath, 'utf8'));
    const hasStartScript = !!webPackageJson.scripts?.start;
    const hasDevScript = !!webPackageJson.scripts?.dev;
    
    logTest('Web has start script', hasStartScript, {
      error: hasStartScript ? null : 'Web package.json missing start script'
    });
    
    logTest('Web has dev script', hasDevScript, {
      error: hasDevScript ? null : 'Web package.json missing dev script'
    });
  }

  // Check for required configuration files
  const configFiles = [
    'packages/hub/tsconfig.json',
    'packages/web/tsconfig.json',
    'packages/web/next.config.js',
    'packages/web/tailwind.config.ts'
  ];

  configFiles.forEach(configFile => {
    const configPath = path.join(workspaceRoot, configFile);
    const configExists = fs.existsSync(configPath);
    logTest(`Configuration file: ${configFile}`, configExists, {
      error: configExists ? null : `Configuration file ${configFile} not found`
    });
  });

  return true;
}

async function generateStartupReport() {
  logHeader('Generating Startup Report');

  const report = {
    timestamp: new Date().toISOString(),
    environment: process.env.NODE_ENV || 'development',
    ci: !!process.env.CI,
    platform: process.platform,
    nodeVersion: process.version,
    validation: {
      total: testResults.tests.length,
      passed: testResults.passed,
      failed: testResults.failed,
      successRate: Math.round((testResults.passed / testResults.tests.length) * 100)
    },
    tests: testResults.tests.map(test => ({
      name: test.name,
      passed: test.passed,
      error: test.details.error || null
    }))
  };

  const reportPath = path.join(__dirname, '../../test-results/startup-validation-report.json');
  
  // Ensure directory exists
  const reportDir = path.dirname(reportPath);
  if (!fs.existsSync(reportDir)) {
    fs.mkdirSync(reportDir, { recursive: true });
  }

  fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
  logSuccess(`Startup report generated: ${reportPath}`);

  return report;
}

async function main() {
  logHeader('TravelViz Service Startup Validation');
  
  log('This script validates that services can start successfully', 'bright');
  log('It tests build processes, dependencies, and configuration without starting servers\n');

  try {
    // Run all validation steps
    const dependenciesOk = await validateDependencyInstallation();
    
    if (dependenciesOk) {
      await validateSharedPackageBuild();
      await validateTypeScriptCompilation();
      await validateLinting();
      await validateBuildProcess();
      await validateServiceStartupReadiness();
    } else {
      logError('Dependency installation failed - skipping other validations');
    }
    
    // Generate report
    const report = await generateStartupReport();
    
    // Summary
    logHeader('Startup Validation Results');
    
    if (testResults.failed === 0) {
      logSuccess('🎉 All startup validations passed!');
      
      log('\nServices are ready to:', 'bright');
      log('• Start in development mode: pnpm dev');
      log('• Start in production mode: pnpm start');
      log('• Run tests: pnpm test:api && pnpm test:e2e');
      log('• Deploy to production');
      
      process.exit(0);
    } else {
      logError(`❌ ${testResults.failed} startup validations failed`);
      
      log('\nFailed validations:', 'bright');
      testResults.tests
        .filter(t => !t.passed)
        .forEach(t => {
          log(`• ${t.name}: ${t.details.error || 'Unknown error'}`);
        });
      
      log('\nTroubleshooting:', 'bright');
      log('• Run: pnpm install');
      log('• Run: pnpm --filter @travelviz/shared build');
      log('• Check TypeScript errors: pnpm type-check');
      log('• Check linting errors: pnpm lint');
      log('• Verify all configuration files are present');
      
      process.exit(1);
    }
    
  } catch (error) {
    logError(`Fatal error: ${error.message}`);
    console.error(error.stack);
    process.exit(1);
  }
}

// Handle process termination
process.on('SIGINT', () => {
  log('\nStartup validation interrupted by user', 'yellow');
  process.exit(1);
});

// Run the validation
main();