-- Fix RLS infinite recursion issue for trips/activities relationship

-- First, drop the existing problematic policies on activities
DROP POLICY IF EXISTS "Users can view activities for their trips" ON activities;
DROP POLICY IF EXISTS "Users can insert activities for their trips" ON activities;
DROP POLICY IF EXISTS "Users can update activities for their trips" ON activities;
DROP POLICY IF EXISTS "Users can delete activities for their trips" ON activities;

-- <PERSON>reate optimized activity policies that avoid circular dependency
-- These policies use a direct join instead of EXISTS subquery

-- View activities: direct join with trips table
CREATE POLICY "Users can view activities for their trips" ON activities
    FOR SELECT 
    USING (
        trip_id IN (
            SELECT id FROM trips WHERE user_id = auth.uid()
        )
    );

-- Insert activities: check trip ownership
CREATE POLICY "Users can insert activities for their trips" ON activities
    FOR INSERT 
    WITH CHECK (
        trip_id IN (
            SELECT id FROM trips WHERE user_id = auth.uid()
        )
    );

-- Update activities: check trip ownership
CREATE POLICY "Users can update activities for their trips" ON activities
    FOR UPDATE 
    USING (
        trip_id IN (
            SELECT id FROM trips WHERE user_id = auth.uid()
        )
    );

-- Delete activities: check trip ownership
CREATE POLICY "Users can delete activities for their trips" ON activities
    FOR DELETE 
    USING (
        trip_id IN (
            SELECT id FROM trips WHERE user_id = auth.uid()
        )
    );

-- Add security definer function for service role operations
-- This allows the service role to bypass RLS when needed
CREATE OR REPLACE FUNCTION public.service_role_bypass()
RETURNS void
LANGUAGE sql
SECURITY DEFINER
SET search_path = public
AS $$
  -- This is a marker function to indicate service role operations
  -- It doesn't need to do anything
$$;

-- Grant execute permission to service role
GRANT EXECUTE ON FUNCTION public.service_role_bypass() TO service_role;