/**
 * Places API Tests
 * 
 * Tests Google Places integration with API validation
 * Uses the proven authentication pattern for token management
 */

const ApiTestClient = require('./utils/api-client');
const apiConfig = require('./api.config');

// Test results tracking
const testResults = {
  passed: 0,
  failed: 0,
  tests: []
};

function logTest(name, result, details = {}) {
  const passed = result.success || result === true;
  testResults.tests.push({ name, passed, details });
  
  if (passed) {
    testResults.passed++;
    console.log(`✅ ${name}`);
  } else {
    testResults.failed++;
    console.log(`❌ ${name}`, details.error || result.error || '');
  }
}

async function ensureAuthenticated(client) {
  console.log('\n🔐 Ensuring Authentication...');
  
  try {
    let token = client.getStoredToken();
    
    if (!token) {
      console.log('   No valid token found, logging in...');
      const loginResponse = await client.login(
        apiConfig.testData.user.email,
        apiConfig.testData.user.password
      );
      
      if (!loginResponse.success) {
        throw new Error(`Login failed: ${loginResponse.error || 'Unknown error'}`);
      }
      
      token = client.getStoredToken();
    }
    
    const meResponse = await client.getMe();
    if (!meResponse.success) {
      throw new Error(`Token validation failed: ${meResponse.error || 'Unknown error'}`);
    }
    
    logTest('Authentication ready', true, { 
      hasToken: !!token,
      userEmail: meResponse.data.data.email 
    });
    
    return true;
  } catch (error) {
    logTest('Authentication setup', false, { error: error.message });
    return false;
  }
}

async function testAutocompleteBasic(client) {
  console.log('\n🔍 Testing Places Autocomplete - Basic...');
  
  try {
    const response = await client.autocomplete('Paris');
    
    logTest('Autocomplete for "Paris"', response, response);
    
    if (response.success) {
      const places = response.data.data;
      
      logTest('Autocomplete returns array', Array.isArray(places), {
        type: typeof places,
        isArray: Array.isArray(places)
      });
      
      if (Array.isArray(places) && places.length > 0) {
        const firstPlace = places[0];
        logTest('First place has required fields', 
          firstPlace.place_id && firstPlace.description, {
          hasPlaceId: !!firstPlace.place_id,
          hasDescription: !!firstPlace.description,
          description: firstPlace.description
        });
        
        console.log(`   Found ${places.length} places for "Paris"`);
      }
      
      return true;
    }
    
    return false;
  } catch (error) {
    logTest('Autocomplete Basic Test', false, { error: error.message });
    return false;
  }
}

async function testAutocompleteSpecific(client) {
  console.log('\n🗼 Testing Places Autocomplete - Specific Location...');
  
  try {
    const response = await client.autocomplete('Eiffel Tower');
    
    logTest('Autocomplete for "Eiffel Tower"', response, response);
    
    if (response.success) {
      const places = response.data.data;
      
      if (Array.isArray(places) && places.length > 0) {
        const eiffelTower = places.find(place => 
          place.description.toLowerCase().includes('eiffel') ||
          place.description.toLowerCase().includes('tour eiffel')
        );
        
        logTest('Found Eiffel Tower in results', !!eiffelTower, {
          found: !!eiffelTower,
          description: eiffelTower ? eiffelTower.description : null
        });
        
        console.log(`   Found ${places.length} places for "Eiffel Tower"`);
      }
      
      return true;
    }
    
    return false;
  } catch (error) {
    logTest('Autocomplete Specific Test', false, { error: error.message });
    return false;
  }
}

async function testAutocompleteEmpty(client) {
  console.log('\n📭 Testing Places Autocomplete - Empty Query...');
  
  try {
    const response = await client.autocomplete('');
    
    // Should fail with validation error
    logTest('Empty query validation', !response.success, response);
    logTest('Empty query returns 400 status', response.status === 400, {
      expectedStatus: 400,
      actualStatus: response.status
    });
    
    return true;
  } catch (error) {
    logTest('Autocomplete Empty Test', false, { error: error.message });
    return false;
  }
}

async function testAutocompleteInvalid(client) {
  console.log('\n🚫 Testing Places Autocomplete - Invalid Query...');
  
  try {
    const response = await client.autocomplete('xyzabc123nonexistentplace');
    
    logTest('Invalid query handled gracefully', response, response);
    
    if (response.success) {
      const places = response.data.data;
      logTest('Invalid query returns empty or minimal results', 
        !places || places.length === 0, {
        resultCount: places ? places.length : 0
      });
    }
    
    return true;
  } catch (error) {
    logTest('Autocomplete Invalid Test', false, { error: error.message });
    return false;
  }
}

async function testGeocodeBasic(client) {
  console.log('\n🌍 Testing Places Geocode - Basic...');
  
  try {
    const locations = ['Paris, France', 'Tokyo, Japan'];
    const response = await client.geocode(locations);
    
    logTest('Geocode multiple locations', response, response);
    
    if (response.success) {
      const results = response.data.data;
      
      logTest('Geocode returns array', Array.isArray(results), {
        type: typeof results,
        isArray: Array.isArray(results)
      });
      
      if (Array.isArray(results)) {
        logTest('Geocode returns correct count', results.length === locations.length, {
          expected: locations.length,
          actual: results.length
        });
        
        // Check first result structure
        if (results.length > 0) {
          const firstResult = results[0];
          logTest('First result has coordinates', 
            firstResult.lat !== undefined && firstResult.lng !== undefined, {
            hasLat: firstResult.lat !== undefined,
            hasLng: firstResult.lng !== undefined,
            lat: firstResult.lat,
            lng: firstResult.lng
          });
        }
      }
      
      return true;
    }
    
    return false;
  } catch (error) {
    logTest('Geocode Basic Test', false, { error: error.message });
    return false;
  }
}

async function testGeocodeSingle(client) {
  console.log('\n📍 Testing Places Geocode - Single Location...');
  
  try {
    const response = await client.geocode(['Eiffel Tower, Paris']);
    
    logTest('Geocode single location', response, response);
    
    if (response.success) {
      const results = response.data.data;
      
      if (Array.isArray(results) && results.length > 0) {
        const result = results[0];
        
        logTest('Single location has valid coordinates', 
          typeof result.lat === 'number' && typeof result.lng === 'number', {
          lat: result.lat,
          lng: result.lng,
          latType: typeof result.lat,
          lngType: typeof result.lng
        });
        
        // Verify coordinates are in reasonable range for Paris
        if (typeof result.lat === 'number' && typeof result.lng === 'number') {
          const inParisRange = result.lat > 48 && result.lat < 49 && result.lng > 2 && result.lng < 3;
          logTest('Coordinates are in Paris area', inParisRange, {
            lat: result.lat,
            lng: result.lng,
            inRange: inParisRange
          });
        }
      }
      
      return true;
    }
    
    return false;
  } catch (error) {
    logTest('Geocode Single Test', false, { error: error.message });
    return false;
  }
}

async function testGeocodeEmpty(client) {
  console.log('\n📭 Testing Places Geocode - Empty Array...');
  
  try {
    const response = await client.geocode([]);
    
    // Should fail with validation error
    logTest('Empty array validation', !response.success, response);
    logTest('Empty array returns 400 status', response.status === 400, {
      expectedStatus: 400,
      actualStatus: response.status
    });
    
    return true;
  } catch (error) {
    logTest('Geocode Empty Test', false, { error: error.message });
    return false;
  }
}

async function testGeocodeInvalid(client) {
  console.log('\n🚫 Testing Places Geocode - Invalid Locations...');
  
  try {
    const response = await client.geocode(['nonexistentplace123xyz', 'anotherfakeplace456']);
    
    logTest('Invalid locations handled gracefully', response, response);
    
    if (response.success) {
      const results = response.data.data;
      logTest('Invalid locations return null or error results', true, {
        resultCount: results ? results.length : 0,
        results: results
      });
    }
    
    return true;
  } catch (error) {
    logTest('Geocode Invalid Test', false, { error: error.message });
    return false;
  }
}

async function runTests() {
  console.log('🚀 Starting Places API Tests');
  console.log('============================');
  console.log(`API Base URL: ${apiConfig.baseUrl}`);
  console.log(`Test User: ${apiConfig.testData.user.email}\n`);

  const client = new ApiTestClient();
  
  try {
    // Ensure we're authenticated first
    const authReady = await ensureAuthenticated(client);
    if (!authReady) {
      console.log('\n❌ Authentication failed - cannot run places API tests');
      return testResults;
    }
    
    // Run all places API tests in sequence
    const tests = [
      () => testAutocompleteBasic(client),
      () => testAutocompleteSpecific(client),
      () => testAutocompleteEmpty(client),
      () => testAutocompleteInvalid(client),
      () => testGeocodeBasic(client),
      () => testGeocodeSingle(client),
      () => testGeocodeEmpty(client),
      () => testGeocodeInvalid(client),
    ];

    for (const test of tests) {
      await test();
    }
    
  } catch (error) {
    console.error('\n💥 Test execution error:', error.message);
    testResults.failed++;
  }

  // Summary
  console.log('\n============================');
  console.log('📊 Places API Test Results');
  console.log('============================');
  console.log(`✅ Passed: ${testResults.passed}`);
  console.log(`❌ Failed: ${testResults.failed}`);
  console.log(`📋 Total: ${testResults.tests.length}`);
  console.log(`📈 Success Rate: ${Math.round((testResults.passed / testResults.tests.length) * 100)}%`);

  // Detailed failures
  if (testResults.failed > 0) {
    console.log('\n❌ Failed Tests:');
    testResults.tests
      .filter(t => !t.passed)
      .forEach(t => {
        console.log(`\n- ${t.name}`);
        if (t.details.error) {
          console.log('  Error:', t.details.error);
        }
      });
  }

  return testResults;
}

// Export for use by test runner
module.exports = { runTests };

// Run directly if called as script
if (require.main === module) {
  runTests().catch(console.error);
}