import { vi } from 'vitest';

// Mock AI Parser Service
export const mockAIParserService = {
  createParseSession: vi.fn(),
  getSession: vi.fn(),
  createTripFromParse: vi.fn(),
  parseConversation: vi.fn(),
  getCircuitBreakerStatus: vi.fn().mockReturnValue({
    state: 'CLOSED',
    isAvailable: true
  }),
};

// Mock Redis Connection Pool
export const mockRedisConnectionPool = {
  acquire: vi.fn().mockResolvedValue({
    get: vi.fn(),
    set: vi.fn(),
    del: vi.fn(),
    expire: vi.fn(),
    quit: vi.fn()
  }),
  release: vi.fn(),
  getStats: vi.fn().mockReturnValue({
    active: 0,
    available: 10,
    total: 10
  })
};

// Mock Cache Service
export const mockCacheService = {
  get: vi.fn(),
  set: vi.fn(),
  delete: vi.fn(),
  clear: vi.fn()
};

// Mock Logger
export const mockLogger = {
  error: vi.fn(),
  warn: vi.fn(),
  info: vi.fn(),
  debug: vi.fn(),
  trace: vi.fn()
};

// Mock Redis Client
export const mockRedis = {
  subscribe: vi.fn().mockResolvedValue(undefined),
  unsubscribe: vi.fn().mockResolvedValue(undefined),
  on: vi.fn(),
  off: vi.fn(),
  publish: vi.fn().mockResolvedValue(1),
  get: vi.fn().mockResolvedValue(null),
  set: vi.fn().mockResolvedValue('OK'),
  del: vi.fn().mockResolvedValue(1),
  expire: vi.fn().mockResolvedValue(1),
  quit: vi.fn().mockResolvedValue('OK')
};

// Mock Trip CRUD Service
export const mockTripCrudService = {
  createTrip: vi.fn(),
  getTrip: vi.fn(),
  updateTrip: vi.fn(),
  deleteTrip: vi.fn(),
  getUserTrips: vi.fn(),
  getUserTripsPaginated: vi.fn()
};

// Mock Geocoding Service
export const mockGeocodingService = {
  geocodeLocation: vi.fn(),
  reverseGeocode: vi.fn(),
  searchPlaces: vi.fn()
};

// Export factory function for creating fresh mocks
export const createMocks = () => ({
  aiParserService: { ...mockAIParserService },
  redisConnectionPool: { ...mockRedisConnectionPool },
  cacheService: { ...mockCacheService },
  logger: { ...mockLogger },
  redis: { ...mockRedis },
  tripCrudService: { ...mockTripCrudService },
  geocodingService: { ...mockGeocodingService }
});

// Helper to reset all mocks
export const resetAllMocks = () => {
  vi.clearAllMocks();
};