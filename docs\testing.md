# TravelViz Testing Guide

This guide provides comprehensive documentation for writing and running tests in the TravelViz monorepo. We follow Test-Driven Development (TDD) practices and maintain high test coverage for reliable, maintainable code.

## Table of Contents

- [Testing Overview](#testing-overview)
- [Testing Setup](#testing-setup)
- [Writing Tests](#writing-tests)
- [Running Tests](#running-tests)
- [Testing Best Practices](#testing-best-practices)
- [CI/CD Testing](#cicd-testing)
- [Specific Testing Scenarios](#specific-testing-scenarios)

## Testing Overview

### Testing Philosophy

TravelViz follows these core testing principles:

1. **TDD First**: Write tests before implementation (Red-Green-Refactor cycle)
2. **Isolation**: Each test should be independent and not affect others
3. **Fast Feedback**: Tests should run quickly to enable rapid development
4. **Meaningful Coverage**: Focus on behavior, not just line coverage
5. **Clear Failures**: Tests should clearly indicate what went wrong

### Types of Tests

#### Unit Tests

- Test individual functions, components, or modules in isolation
- Mock external dependencies
- Located alongside source files as `*.test.ts` or `*.test.tsx`
- Run with Vitest

#### Integration Tests

- Test interactions between multiple components or services
- May use real databases or services with test data
- Located in `__tests__` directories or `*.integration.test.ts` files
- Run with Vitest

#### End-to-End (E2E) Tests

- Test complete user workflows through the UI
- Use real browser automation
- Located in `packages/web/tests/e2e/`
- Run with Playwright

### Test Coverage Goals

| Type          | Target Coverage | Notes                                      |
| ------------- | --------------- | ------------------------------------------ |
| New Features  | 90%+            | Phase 1-3 features require high coverage   |
| Bug Fixes     | 80%+            | Must include regression tests              |
| Utilities     | 95%+            | Pure functions should be thoroughly tested |
| UI Components | 80%+            | Focus on user interactions and edge cases  |
| API Endpoints | 90%+            | Test success, errors, and validation       |

## Testing Setup

### Test Frameworks

- **Vitest**: Fast unit and integration testing with native ESM support
- **Playwright**: Cross-browser E2E testing
- **MSW (Mock Service Worker)**: API mocking for frontend tests
- **Supertest**: HTTP assertion library for API testing
- **React Testing Library**: Component testing with user-centric approach

### Test Environment Configuration

Each package has its own test setup:

#### Web Package (`packages/web/test/setup.ts`)

```typescript
// Automatically loads .env.local for test credentials
import { config } from 'dotenv';
config({ path: resolve(__dirname, '../.env.local') });

// MSW for API mocking
beforeAll(() => server.listen());
afterEach(() => server.resetHandlers());
afterAll(() => server.close());

// Mock Next.js navigation
vi.mock('next/navigation');
```

#### Hub Package (`packages/hub/test/setup.ts`)

```typescript
// Loads environment variables from .env.local
config({ path: resolve(__dirname, '../.env.local') });

// Uses real Supabase test instance
process.env.SUPABASE_URL = process.env.SUPABASE_URL || 'test-url';
```

### Test File Organization

```
packages/
├── web/
│   ├── src/
│   │   ├── components/
│   │   │   ├── Button.tsx
│   │   │   └── Button.test.tsx        # Unit test
│   │   └── hooks/
│   │       ├── useAuth.ts
│   │       └── __tests__/
│   │           └── useAuth.test.ts     # Unit test
│   ├── test/
│   │   ├── setup.ts                    # Test setup
│   │   ├── utils/                      # Test utilities
│   │   └── mocks/                      # MSW mocks
│   └── tests/
│       └── e2e/                        # Playwright E2E tests
│           ├── ai-import.spec.ts
│           └── trip-management.spec.ts
├── hub/
│   ├── src/
│   │   ├── services/
│   │   │   ├── trips.service.ts
│   │   │   └── __tests__/
│   │   │       └── trips.service.test.ts
│   │   └── integration/
│   │       └── __tests__/              # Integration tests
│   └── test/
│       └── setup.ts
└── shared/
    └── src/
        └── utils/
            ├── validation.ts
            └── validation.test.ts
```

## Writing Tests

### Unit Test Patterns

#### React Component Test

```typescript
import { describe, it, expect, vi } from 'vitest';
import { render, screen } from '@/test/utils/render';
import userEvent from '@testing-library/user-event';
import { TripCard } from './TripCard';

describe('TripCard', () => {
  const mockTrip = {
    id: '123',
    title: 'Tokyo Adventure',
    startDate: '2024-04-15',
    endDate: '2024-04-20',
    destination: 'Tokyo, Japan'
  };

  it('should render trip information', () => {
    render(<TripCard trip={mockTrip} />);

    expect(screen.getByText('Tokyo Adventure')).toBeInTheDocument();
    expect(screen.getByText('Tokyo, Japan')).toBeInTheDocument();
    expect(screen.getByText(/Apr 15 - Apr 20/)).toBeInTheDocument();
  });

  it('should handle click events', async () => {
    const user = userEvent.setup();
    const onEdit = vi.fn();

    render(<TripCard trip={mockTrip} onEdit={onEdit} />);

    await user.click(screen.getByRole('button', { name: /edit/i }));
    expect(onEdit).toHaveBeenCalledWith(mockTrip.id);
  });
});
```

#### API Endpoint Test

```typescript
import { describe, it, expect, beforeEach, vi } from 'vitest';
import request from 'supertest';
import { app } from '../../app';
import { tripsService } from '../../services/trips.service';

vi.mock('../../services/trips.service');

describe('GET /api/trips/:id', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('should return trip details', async () => {
    const mockTrip = {
      id: '123',
      title: 'Test Trip',
      userId: 'user-123',
    };

    vi.mocked(tripsService.getById).mockResolvedValue(mockTrip);

    const response = await request(app)
      .get('/api/trips/123')
      .set('Authorization', 'Bearer valid-token')
      .expect(200);

    expect(response.body).toMatchObject({
      success: true,
      data: mockTrip,
    });
  });

  it('should handle not found errors', async () => {
    vi.mocked(tripsService.getById).mockResolvedValue(null);

    const response = await request(app)
      .get('/api/trips/999')
      .set('Authorization', 'Bearer valid-token')
      .expect(404);

    expect(response.body).toMatchObject({
      success: false,
      error: 'Trip not found',
    });
  });
});
```

#### Service/Utility Test

```typescript
import { describe, it, expect } from 'vitest';
import { parseAIConversation } from './parser.service';

describe('parseAIConversation', () => {
  it('should extract trip details from ChatGPT format', () => {
    const conversation = `
      User: Plan a 3-day trip to Paris
      ChatGPT: Here's your Paris itinerary:
      Day 1: Eiffel Tower and Louvre
      Day 2: Versailles
      Day 3: Montmartre
    `;

    const result = parseAIConversation(conversation, 'chatgpt');

    expect(result).toMatchObject({
      destination: 'Paris',
      duration: 3,
      activities: expect.arrayContaining([
        expect.objectContaining({ name: 'Eiffel Tower' }),
        expect.objectContaining({ name: 'Versailles' }),
      ]),
    });
  });

  it('should handle invalid input', () => {
    expect(() => parseAIConversation('', 'chatgpt')).toThrow('Conversation too short');
  });
});
```

### Integration Test Patterns

```typescript
import { describe, it, expect, beforeAll, afterAll } from 'vitest';
import { createTestClient } from '../utils/test-client';
import { seedTestData, cleanupTestData } from '../utils/test-helpers';

describe('Trip Creation Flow', () => {
  let client: any;
  let testUserId: string;

  beforeAll(async () => {
    const { userId } = await seedTestData();
    testUserId = userId;
    client = await createTestClient(userId);
  });

  afterAll(async () => {
    await cleanupTestData(testUserId);
  });

  it('should create trip from AI conversation', async () => {
    // 1. Parse conversation
    const parseResponse = await client.post('/api/import/parse', {
      content: 'Sample conversation...',
      source: 'chatgpt',
    });

    expect(parseResponse.status).toBe(200);
    const { sessionId } = parseResponse.data;

    // 2. Create trip
    const createResponse = await client.post('/api/trips', {
      sessionId,
      title: 'My Trip',
    });

    expect(createResponse.status).toBe(201);
    const { tripId } = createResponse.data;

    // 3. Verify trip exists
    const getResponse = await client.get(`/api/trips/${tripId}`);
    expect(getResponse.status).toBe(200);
    expect(getResponse.data.title).toBe('My Trip');
  });
});
```

### E2E Test Patterns

```typescript
import { test, expect } from '@playwright/test';

test.describe('AI Import Feature', () => {
  test.beforeEach(async ({ page }) => {
    // Login with test user
    await page.goto('/login');
    await page.fill('input[type="email"]', '<EMAIL>');
    await page.fill('input[type="password"]', 'TestPassword123!');
    await page.click('button[type="submit"]');
    await page.waitForURL('**/dashboard');
  });

  test('should import ChatGPT conversation', async ({ page }) => {
    // Navigate to import
    await page.goto('/import');

    // Paste conversation
    await page.fill('textarea', 'User: Plan a trip...');
    await page.click('button:has-text("Start Import")');

    // Wait for processing
    await page.waitForSelector('text=AI Processing');
    await page.waitForSelector('text=Review & Create', { timeout: 30000 });

    // Verify parsed data
    await expect(page.locator('text=Tokyo')).toBeVisible();

    // Create trip
    await page.click('button:has-text("Create Trip")');
    await page.waitForURL('**/plan/**');

    // Verify success
    await expect(page.locator('h1')).toContainText('Tokyo');
  });
});
```

## Running Tests

### Test Commands

```bash
# Run all tests
pnpm test

# Run tests in watch mode (TDD)
pnpm test:watch

# Run tests with coverage
pnpm test:coverage

# Check coverage thresholds
pnpm test:check-coverage

# Run tests for specific package
pnpm --filter @travelviz/web test
pnpm --filter @travelviz/hub test

# Run specific test file
pnpm test src/components/Button.test.tsx

# Run tests matching pattern
pnpm test --grep "should handle errors"

# Create a new test file (TDD helper)
pnpm test:create components/NewComponent
```

### E2E Test Commands

```bash
# Run all E2E tests
pnpm playwright test

# Run E2E tests in UI mode
pnpm playwright test --ui

# Run specific E2E test
pnpm playwright test ai-import.spec.ts

# Debug E2E tests
pnpm playwright test --debug

# Update visual snapshots
pnpm playwright test --update-snapshots
```

### Coverage Reports

After running `pnpm test:coverage`, view reports at:

- HTML Report: `coverage/index.html`
- Console Summary: Displayed after test run
- JSON Report: `coverage/coverage-summary.json`

## Testing Best Practices

### AAA Pattern (Arrange, Act, Assert)

```typescript
it('should calculate trip total cost', () => {
  // Arrange
  const activities = [
    { name: 'Hotel', cost: 500 },
    { name: 'Flight', cost: 800 },
    { name: 'Food', cost: 200 },
  ];

  // Act
  const total = calculateTripCost(activities);

  // Assert
  expect(total).toBe(1500);
});
```

### Test Isolation

```typescript
describe('UserService', () => {
  let mockDb: any;

  beforeEach(() => {
    // Fresh mock for each test
    mockDb = {
      users: {
        findById: vi.fn(),
        create: vi.fn(),
      },
    };
  });

  afterEach(() => {
    // Clean up after each test
    vi.clearAllMocks();
  });
});
```

### Avoiding Flaky Tests

```typescript
// ❌ Bad: Depends on timing
it('should show message after delay', async () => {
  triggerAction();
  await new Promise(resolve => setTimeout(resolve, 1000));
  expect(getMessage()).toBe('Success');
});

// ✅ Good: Wait for specific condition
it('should show message after action completes', async () => {
  triggerAction();
  await waitFor(() => {
    expect(getMessage()).toBe('Success');
  });
});
```

### Testing Async Code

```typescript
// Testing promises
it('should fetch user data', async () => {
  const userData = await fetchUser('123');
  expect(userData.name).toBe('John');
});

// Testing error cases
it('should handle network errors', async () => {
  mockFetch.mockRejectedValue(new Error('Network error'));

  await expect(fetchUser('123')).rejects.toThrow('Network error');
});

// Testing event handlers
it('should emit events', async () => {
  const onUpdate = vi.fn();
  emitter.on('update', onUpdate);

  await updateData();

  expect(onUpdate).toHaveBeenCalledWith(expect.objectContaining({ status: 'complete' }));
});
```

### Mocking Strategies

#### Mocking External Services

```typescript
// Mock Supabase
vi.mock('@/lib/supabase', () => ({
  supabase: {
    from: vi.fn(() => ({
      select: vi.fn().mockResolvedValue({ data: [], error: null }),
      insert: vi.fn().mockResolvedValue({ data: {}, error: null }),
    })),
  },
}));

// Mock API calls
import { server } from '@/test/mocks/server';
import { rest } from 'msw';

server.use(
  rest.get('/api/trips', (req, res, ctx) => {
    return res(ctx.json({ trips: [] }));
  })
);
```

#### Mocking Time

```typescript
beforeEach(() => {
  vi.useFakeTimers();
  vi.setSystemTime(new Date('2024-01-01'));
});

afterEach(() => {
  vi.useRealTimers();
});

it('should format date correctly', () => {
  const formatted = formatTripDate(new Date());
  expect(formatted).toBe('January 1, 2024');
});
```

## CI/CD Testing

### Pre-commit Hooks

The project uses Husky to run tests before commits:

```bash
# .husky/pre-commit
#!/bin/sh
. "$(dirname "$0")/_/husky.sh"

# Run linting
pnpm lint

# Run type checking
pnpm type-check

# Run affected tests
pnpm test --changed
```

### CI Pipeline Requirements

All PRs must pass:

1. **Linting**: No ESLint errors
2. **Type Checking**: No TypeScript errors
3. **Unit Tests**: All tests passing
4. **Coverage**: Meets minimum thresholds
5. **E2E Tests**: Critical flows passing

### GitHub Actions Workflow

```yaml
name: CI
on: [push, pull_request]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: pnpm/action-setup@v2
      - uses: actions/setup-node@v3

      - name: Install dependencies
        run: pnpm install

      - name: Run tests
        run: |
          pnpm lint
          pnpm type-check
          pnpm test:coverage
          pnpm test:check-coverage
```

## Specific Testing Scenarios

### Testing API Endpoints

```typescript
describe('Trip API', () => {
  it('should require authentication', async () => {
    const response = await request(app).get('/api/trips').expect(401);

    expect(response.body.error).toBe('Unauthorized');
  });

  it('should validate input', async () => {
    const response = await request(app)
      .post('/api/trips')
      .set('Authorization', 'Bearer token')
      .send({ title: '' }) // Invalid
      .expect(400);

    expect(response.body.error).toContain('Title is required');
  });

  it('should handle database errors', async () => {
    vi.mocked(db.query).mockRejectedValue(new Error('DB Error'));

    const response = await request(app)
      .get('/api/trips')
      .set('Authorization', 'Bearer token')
      .expect(500);

    expect(response.body.error).toBe('Internal server error');
  });
});
```

### Testing React Components

```typescript
describe('ImportForm', () => {
  it('should show loading state', () => {
    render(<ImportForm isLoading />);

    expect(screen.getByRole('progressbar')).toBeInTheDocument();
    expect(screen.getByText('Processing...')).toBeInTheDocument();
  });

  it('should validate conversation length', async () => {
    const user = userEvent.setup();
    render(<ImportForm />);

    const textarea = screen.getByRole('textbox');
    await user.type(textarea, 'Too short');
    await user.click(screen.getByRole('button', { name: /import/i }));

    expect(screen.getByText(/at least 100 characters/i)).toBeInTheDocument();
  });

  it('should handle file uploads', async () => {
    const user = userEvent.setup();
    const file = new File(['content'], 'itinerary.pdf', { type: 'application/pdf' });

    render(<ImportForm />);

    const input = screen.getByLabelText(/upload file/i);
    await user.upload(input, file);

    expect(screen.getByText('itinerary.pdf')).toBeInTheDocument();
  });
});
```

### Testing Authentication Flows

```typescript
describe('Authentication', () => {
  it('should redirect to login when unauthorized', async ({ page }) => {
    await page.goto('/dashboard');
    await expect(page).toHaveURL('/login');
  });

  it('should persist session', async ({ page, context }) => {
    // Login
    await page.goto('/login');
    await page.fill('input[type="email"]', '<EMAIL>');
    await page.fill('input[type="password"]', 'password');
    await page.click('button[type="submit"]');

    // Verify logged in
    await expect(page).toHaveURL('/dashboard');

    // Open new page in same context
    const newPage = await context.newPage();
    await newPage.goto('/dashboard');

    // Should still be logged in
    await expect(newPage).toHaveURL('/dashboard');
  });
});
```

### Testing the AI Import Feature

```typescript
describe('AI Import', () => {
  it('should parse ChatGPT conversations', async () => {
    const conversation = `
      User: I want to visit Tokyo for 5 days
      ChatGPT: Here's a 5-day Tokyo itinerary:
      Day 1: Arrive and explore Shibuya
      Day 2: Visit temples in Asakusa
      Day 3: Day trip to Mt. Fuji
      Day 4: Akihabara and Ginza
      Day 5: Morning in Harajuku, depart
    `;

    const result = await aiParser.parse(conversation, 'chatgpt');

    expect(result).toMatchObject({
      destination: 'Tokyo',
      duration: 5,
      activities: expect.arrayContaining([
        expect.objectContaining({
          day: 1,
          description: expect.stringContaining('Shibuya'),
        }),
      ]),
    });
  });

  it('should handle different AI formats', async () => {
    const formats = ['chatgpt', 'claude', 'gemini'];

    for (const format of formats) {
      const result = await aiParser.parse(sampleConversations[format], format);
      expect(result.destination).toBeTruthy();
      expect(result.activities.length).toBeGreaterThan(0);
    }
  });
});
```

## Debugging Tests

### VS Code Debugging

Add to `.vscode/launch.json`:

```json
{
  "type": "node",
  "request": "launch",
  "name": "Debug Vitest Tests",
  "runtimeExecutable": "pnpm",
  "runtimeArgs": ["test", "--no-coverage", "${file}"],
  "console": "integratedTerminal",
  "internalConsoleOptions": "neverOpen"
}
```

### Common Issues and Solutions

#### Test Timeouts

```typescript
// Increase timeout for slow operations
it('should process large file', async () => {
  await processFile(largeFile);
}, 30000); // 30 second timeout
```

#### Memory Leaks

```typescript
// Clean up resources
afterEach(() => {
  // Clear all mocks
  vi.clearAllMocks();

  // Reset modules
  vi.resetModules();

  // Force garbage collection (if available)
  if (global.gc) {
    global.gc();
  }
});
```

#### Flaky Tests

```typescript
// Use waitFor for async assertions
import { waitFor } from '@testing-library/react';

await waitFor(
  () => {
    expect(screen.getByText('Success')).toBeInTheDocument();
  },
  { timeout: 5000 }
);
```

## Test Data Management

### Test Fixtures

```typescript
// test/fixtures/trips.ts
export const mockTrip = {
  id: 'test-trip-123',
  title: 'Test Trip to Tokyo',
  startDate: '2024-04-15',
  endDate: '2024-04-20',
  activities: [
    {
      id: 'activity-1',
      name: 'Visit Senso-ji Temple',
      day: 1,
      time: '09:00',
    },
  ],
};

// Usage in tests
import { mockTrip } from '@test/fixtures/trips';
```

### Test Data Builders

```typescript
// test/builders/trip.builder.ts
export class TripBuilder {
  private trip = {
    id: 'default-id',
    title: 'Default Trip',
    activities: [],
  };

  withTitle(title: string) {
    this.trip.title = title;
    return this;
  }

  withActivities(count: number) {
    this.trip.activities = Array.from({ length: count }, (_, i) => ({
      id: `activity-${i}`,
      name: `Activity ${i + 1}`,
    }));
    return this;
  }

  build() {
    return { ...this.trip };
  }
}

// Usage
const trip = new TripBuilder().withTitle('Paris Vacation').withActivities(5).build();
```

## Continuous Improvement

### Adding New Tests

1. **Use TDD**: Write test first, see it fail, then implement
2. **Test Behavior**: Focus on what the code does, not how
3. **Cover Edge Cases**: Empty data, errors, boundaries
4. **Keep It Simple**: Each test should test one thing
5. **Use Descriptive Names**: Test names should explain the scenario

### Maintaining Tests

1. **Regular Reviews**: Update tests when requirements change
2. **Refactor Tests**: Keep tests DRY but readable
3. **Monitor Coverage**: Track trends, not just numbers
4. **Fix Flaky Tests**: Investigate and fix intermittent failures
5. **Document Patterns**: Share testing patterns that work well

## Resources

- [Vitest Documentation](https://vitest.dev/)
- [Playwright Documentation](https://playwright.dev/)
- [React Testing Library](https://testing-library.com/docs/react-testing-library/intro/)
- [MSW Documentation](https://mswjs.io/)
- [Testing Best Practices](https://github.com/goldbergyoni/javascript-testing-best-practices)

---

Remember: Good tests are an investment in code quality and developer confidence. They should help you move faster, not slower!
