import { describe, it, expect, beforeAll, afterAll } from 'vitest';
import request from 'supertest';
import { createServer } from '../../src/server';
import { getSupabaseClient } from '../../src/lib/supabase';

// Test configuration
const TEST_USER_EMAIL = '<EMAIL>';
const TEST_USER_PASSWORD = 'TestPassword123!';
const API_TIMEOUT = 60000;

// Helper to get auth token
async function getAuthToken(): Promise<string> {
  const supabase = getSupabaseClient();
  const { data, error } = await supabase.auth.signInWithPassword({
    email: TEST_USER_EMAIL,
    password: TEST_USER_PASSWORD
  });

  if (error) throw new Error(`Auth failed: ${error.message}`);
  return data.session?.access_token || '';
}

describe('AI Chat Itinerary Tests', () => {
  let authToken: string;
  let server: any;
  let app: any;

  beforeAll(async () => {
    authToken = await getAuthToken();
    app = createServer();
    server = app.listen(0);
  }, API_TIMEOUT);

  afterAll(async () => {
    server?.close();
  });

  describe('Real-time Chat Import', () => {
    it('should parse incremental chat messages', async () => {
      // Simulate a conversation being built over time
      const chatSession = {
        messages: [
          { role: 'user', content: 'I want to visit Barcelona for 3 days' },
          { role: 'assistant', content: 'Great choice! When are you planning to visit Barcelona?' },
          { role: 'user', content: 'Next month, from April 10-12' },
          { role: 'assistant', content: `Perfect! Here's a 3-day Barcelona itinerary for April 10-12:

Day 1 (April 10):
- 9:00 AM: Start at Sagrada Familia (book tickets online - €26)
- 12:00 PM: Walk through Eixample district
- 1:30 PM: Lunch at a local tapas bar
- 3:00 PM: Visit Park Güell (€10 entry)
- 6:00 PM: Explore Gothic Quarter
- 8:00 PM: Dinner at Els Quatre Gats

Day 2 (April 11):
- 10:00 AM: La Rambla and Boqueria Market
- 12:00 PM: Casa Batlló tour (€25)
- 2:00 PM: Lunch in Gràcia neighborhood
- 4:00 PM: Beach time at Barceloneta
- 7:00 PM: Cable car to Montjuïc
- 9:00 PM: Dinner with city views

Day 3 (April 12):
- 9:00 AM: Day trip to Montserrat (€22 train)
- 2:00 PM: Return to Barcelona
- 4:00 PM: Visit Camp Nou (€26)
- 7:00 PM: Farewell dinner in El Born` }
        ]
      };

      // Convert chat to single conversation string
      const conversationText = chatSession.messages
        .map(msg => `${msg.role}: ${msg.content}`)
        .join('\n\n');

      const response = await request(server)
        .post('/api/v1/import/parse-simple')
        .set('Authorization', `Bearer ${authToken}`)
        .send({ 
          content: conversationText, 
          source: 'chat' 
        })
        .expect(200);

      expect(response.body).toMatchObject({
        success: true,
        data: {
          importId: expect.any(String),
          message: 'Parsing started'
        }
      });
    });

    it('should handle multi-turn conversations', async () => {
      const complexChat = {
        content: `user: I need help planning a trip to Japan
        
assistant: I'd be happy to help you plan a trip to Japan! To create the best itinerary for you, could you tell me:
- How many days will you be traveling?
- What cities are you interested in?
- What's your approximate budget?
- Any specific interests (culture, food, nature, technology)?

user: I have 7 days, want to see Tokyo and Kyoto, budget is around $2000 excluding flights, and I love both traditional culture and modern technology

assistant: Perfect! Here's a 7-day Japan itinerary balancing traditional culture and modern technology:

**Tokyo (Days 1-4)**
Day 1: Arrival & Modern Tokyo
- Arrive at Narita/Haneda Airport
- Check into hotel in Shinjuku ($100/night)
- Evening: Explore Shinjuku's neon lights and Robot Restaurant ($80)

Day 2: Technology & Pop Culture
- 9 AM: Akihabara Electric Town
- 1 PM: teamLab Borderless Digital Art Museum ($32)
- 4 PM: Harajuku and Takeshita Street
- 7 PM: Shibuya Crossing and dinner ($30)

Day 3: Traditional Tokyo
- 8 AM: Tsukiji Outer Market breakfast ($15)
- 10 AM: Senso-ji Temple in Asakusa
- 1 PM: Tokyo National Museum ($10)
- 4 PM: Imperial Palace East Gardens (free)
- 6 PM: Traditional kaiseki dinner ($60)

Day 4: Day Trip to Mount Fuji
- 7 AM: Take train to Mt. Fuji 5th Station ($50 round trip)
- Full day exploring Fuji area
- 8 PM: Return to Tokyo

**Kyoto (Days 5-6)**
Day 5: Travel & Eastern Kyoto
- 8 AM: Shinkansen to Kyoto ($120)
- 11 AM: Kiyomizu-dera Temple ($4)
- 2 PM: Walk through Higashiyama District
- 4 PM: Gion district (spot geishas)
- 6 PM: Traditional ryokan stay ($150)

Day 6: Temples & Bamboo
- 8 AM: Fushimi Inari shrine (free)
- 11 AM: Arashiyama Bamboo Grove
- 2 PM: Tenryu-ji Temple ($5)
- 4 PM: Nijo Castle ($10)
- 7 PM: Pontocho Alley dinner ($40)

Day 7: Return to Tokyo
- Morning: Last minute shopping in Kyoto
- Afternoon: Shinkansen back to Tokyo ($120)
- Evening: Departure from airport

user: This looks amazing! Can you add some specific restaurant recommendations?`,
        source: 'claude'
      };

      const response = await request(server)
        .post('/api/v1/import/parse-simple')
        .set('Authorization', `Bearer ${authToken}`)
        .send(complexChat)
        .expect(200);

      expect(response.body.success).toBe(true);
    });
  });

  describe('Multi-language Chat Support', () => {
    it('should parse Spanish language conversations', async () => {
      const spanishChat = {
        content: `usuario: Quiero planear un viaje a Madrid por 2 días

asistente: ¡Excelente! Te ayudaré a planear tu viaje a Madrid. Aquí está tu itinerario de 2 días:

Día 1:
- 9:00: Palacio Real (€12)
- 12:00: Plaza Mayor y almuerzo
- 14:00: Museo del Prado (€15)
- 17:00: Parque del Retiro
- 20:00: Cena de tapas en La Latina

Día 2:
- 10:00: Museo Reina Sofía (€10)
- 13:00: Mercado de San Miguel
- 15:00: Gran Vía para compras
- 18:00: Templo de Debod para el atardecer
- 21:00: Cena y flamenco show (€45)`,
        source: 'chat-es'
      };

      const response = await request(server)
        .post('/api/v1/import/parse-simple')
        .set('Authorization', `Bearer ${authToken}`)
        .send(spanishChat)
        .expect(200);

      expect(response.body.success).toBe(true);
      // The AI should still be able to parse non-English conversations
    });

    it('should handle mixed language conversations', async () => {
      const mixedChat = {
        content: `user: I want to visit Paris. Can you help me plan?

assistant: Bien sûr! I'd be happy to help you plan your Paris trip. Here's a suggested itinerary:

Jour 1 (Day 1):
- Morning: Tour Eiffel (Eiffel Tower) - €26
- Afternoon: Musée du Louvre - €17
- Evening: Seine river cruise - €15

Jour 2:
- Morning: Versailles day trip - €20
- Afternoon: Return to Paris
- Evening: Dinner in Montmartre

The total cost for activities would be around €78 per person.`,
        source: 'chat-mixed'
      };

      const response = await request(server)
        .post('/api/v1/import/parse-simple')
        .set('Authorization', `Bearer ${authToken}`)
        .send(mixedChat)
        .expect(200);

      expect(response.body.success).toBe(true);
    });
  });

  describe('Chat Context Understanding', () => {
    it('should understand context from previous messages', async () => {
      const contextualChat = {
        content: `user: I'm interested in visiting Italy

assistant: Italy is a wonderful choice! Which cities are you considering?

user: Rome and Florence, maybe Venice if I have time

assistant: Great selections! How many days do you have for your trip?

user: I have 8 days total

assistant: Perfect! Here's an 8-day Italy itinerary covering Rome, Florence, and Venice:

**Rome (Days 1-3)**
Day 1: Ancient Rome - Colosseum, Roman Forum
Day 2: Vatican City - Museums, Sistine Chapel
Day 3: City exploration - Trevi, Spanish Steps

**Florence (Days 4-5)**
Day 4: Travel to Florence, Duomo, Ponte Vecchio
Day 5: Uffizi Gallery, day trip to Pisa

**Venice (Days 6-7)**
Day 6: Travel to Venice, St. Mark's Square
Day 7: Murano & Burano islands

Day 8: Return travel`,
        source: 'chatgpt'
      };

      const response = await request(server)
        .post('/api/v1/import/parse-simple')
        .set('Authorization', `Bearer ${authToken}`)
        .send(contextualChat)
        .expect(200);

      expect(response.body.success).toBe(true);
    });

    it('should handle clarification requests', async () => {
      const clarificationChat = {
        content: `user: Plan a trip to Asia

assistant: I'd love to help plan your Asia trip! Since Asia is quite large, could you be more specific about which countries or cities you'd like to visit?

user: I'm thinking Thailand and Vietnam

assistant: Excellent choices! How long will your trip be?

user: 10 days total

assistant: Here's a 10-day Thailand & Vietnam itinerary:

**Bangkok, Thailand (Days 1-3)**
Day 1: Arrive, Grand Palace, Wat Pho
Day 2: Floating markets, Chatuchak Weekend Market
Day 3: Day trip to Ayutthaya

**Phuket, Thailand (Days 4-5)**
Day 4: Fly to Phuket, beach time
Day 5: Island hopping tour

**Ho Chi Minh City, Vietnam (Days 6-7)**
Day 6: Fly to HCMC, Cu Chi Tunnels
Day 7: Mekong Delta tour

**Hanoi & Ha Long Bay (Days 8-10)**
Day 8: Fly to Hanoi, Old Quarter
Day 9: Ha Long Bay overnight cruise
Day 10: Return to Hanoi, departure`,
        source: 'claude'
      };

      const response = await request(server)
        .post('/api/v1/import/parse-simple')
        .set('Authorization', `Bearer ${authToken}`)
        .send(clarificationChat)
        .expect(200);

      expect(response.body.success).toBe(true);
    });
  });

  describe('Chat Format Variations', () => {
    it('should handle WhatsApp-style chat exports', async () => {
      const whatsappChat = {
        content: `[3/15/24, 10:30 AM] User: Hi, I need help planning a weekend in Amsterdam
[3/15/24, 10:32 AM] TravelBot: Hello! I'd be happy to help you plan a weekend in Amsterdam. Here's a great itinerary:

Friday Evening:
- Arrive and check into hotel
- 7 PM: Canal cruise (€16)
- 9 PM: Dinner in Jordaan district

Saturday:
- 9 AM: Anne Frank House (€14, book ahead!)
- 11 AM: Van Gogh Museum (€19)
- 2 PM: Lunch at local café
- 4 PM: Vondelpark walk
- 6 PM: Red Light District tour
- 8 PM: Dinner at De Kas (€45)

Sunday:
- 10 AM: Albert Cuyp Market
- 12 PM: Rijksmuseum (€22.50)
- 3 PM: Bicycle tour (€25)
- 6 PM: Farewell drinks at a brown café

[3/15/24, 10:35 AM] User: Perfect, thank you!`,
        source: 'whatsapp'
      };

      const response = await request(server)
        .post('/api/v1/import/parse-simple')
        .set('Authorization', `Bearer ${authToken}`)
        .send(whatsappChat)
        .expect(200);

      expect(response.body.success).toBe(true);
    });

    it('should handle Discord-style chat formats', async () => {
      const discordChat = {
        content: `TravelHelper — Today at 2:30 PM
@User I can help you plan your London trip! How many days will you be there?

User — Today at 2:31 PM
I have 4 days

TravelHelper — Today at 2:33 PM
Great! Here's a 4-day London itinerary:

**Day 1: Royal London**
• Tower of London (£29.90)
• Tower Bridge
• Borough Market lunch
• Westminster Abbey (£24)
• Big Ben & Parliament

**Day 2: Museums**
• British Museum (free)
• Covent Garden lunch
• National Gallery (free)
• West End show (£40-80)

**Day 3: Modern London**
• London Eye (£27)
• Tate Modern (free)
• Shakespeare's Globe (£17)
• Skyline views from The Shard (£25)

**Day 4: Day Trip**
• Windsor Castle (£26.50)
OR
• Stonehenge & Bath tour (£85)`,
        source: 'discord'
      };

      const response = await request(server)
        .post('/api/v1/import/parse-simple')
        .set('Authorization', `Bearer ${authToken}`)
        .send(discordChat)
        .expect(200);

      expect(response.body.success).toBe(true);
    });
  });

  describe('Error Scenarios in Chat Import', () => {
    it('should handle chats with no itinerary content', async () => {
      const noItineraryChat = {
        content: `user: What's the weather like in Paris?
        
assistant: Paris weather varies by season. Spring (April-May) and fall (September-October) are generally pleasant with mild temperatures. Summer can be warm, and winter is cool with occasional rain. What time of year are you planning to visit?

user: Thanks for the info!`,
        source: 'chat'
      };

      const response = await request(server)
        .post('/api/v1/import/parse-simple')
        .set('Authorization', `Bearer ${authToken}`)
        .send(noItineraryChat)
        .expect(200);

      // Should still accept but parsing might yield minimal results
      expect(response.body.success).toBe(true);
    });

    it('should handle interrupted conversations', async () => {
      const interruptedChat = {
        content: `user: Can you help me plan a trip to Greece?

assistant: Of course! I'd love to help you plan a trip to Greece. To create the best itinerary for you, could you let me know:
- How many days you'll be traveling
- Which islands or cities you'd like to visit
- Your budget range
- Any specific interests

user: I have 10 days and want to see Athens and some islands`,
        source: 'chat'
      };

      const response = await request(server)
        .post('/api/v1/import/parse-simple')
        .set('Authorization', `Bearer ${authToken}`)
        .send(interruptedChat)
        .expect(200);

      // Should handle gracefully even without complete itinerary
      expect(response.body.success).toBe(true);
    });
  });
});