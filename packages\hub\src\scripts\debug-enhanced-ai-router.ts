#!/usr/bin/env npx tsx

import { enhancedAIRouterService } from '../services/enhanced-ai-router.service';
import { modelSelectorService } from '../services/model-selector.service';
import { usageTrackingService } from '../services/usage-tracking.service';
import { logger } from '../utils/logger';

async function debugEnhancedAIRouter() {
  console.log('🔍 Debugging Enhanced AI Router Service...\n');

  try {
    // Test 1: Model Selection
    console.log('1. Testing model selection...');
    const testContent = 'Day 1: Visit Paris\nDay 2: Go to the Eiffel Tower\nDay 3: Return home';
    
    try {
      const modelSelection = await modelSelectorService.selectModel(testContent);
      console.log('✅ Model selection successful:', {
        modelId: modelSelection.modelId,
        provider: modelSelection.provider,
        reason: modelSelection.reason,
        estimatedCost: modelSelection.estimatedCost
      });
    } catch (error) {
      console.error('❌ Model selection failed:', error);
      return;
    }

    // Test 2: Usage Tracking
    console.log('\n2. Testing usage tracking...');
    try {
      const usage = await usageTrackingService.getCurrentUsage('moonshotai/kimi-k2:free');
      console.log('✅ Usage tracking successful:', {
        requestCount: usage.requestCount,
        totalCost: usage.totalCost
      });
    } catch (error) {
      console.error('❌ Usage tracking failed:', error);
      return;
    }

    // Test 3: Enhanced AI Router Service
    console.log('\n3. Testing Enhanced AI Router Service...');
    try {
      console.log('Calling enhancedAIRouterService.parseContent...');
      const result = await enhancedAIRouterService.parseContent(testContent, 'test', 'debug-user');
      console.log('✅ Enhanced AI Router Service successful:', {
        title: result.title,
        activitiesCount: result.activities?.length || 0,
        destination: result.destination
      });
    } catch (error) {
      console.error('❌ Enhanced AI Router Service failed:', error);
      console.error('Error details:', {
        name: error.name,
        message: error.message,
        stack: error.stack?.split('\n').slice(0, 5).join('\n')
      });
    }

  } catch (error) {
    console.error('❌ Debug script failed:', error);
  }
}

// Run the debug script
debugEnhancedAIRouter().catch(console.error);
