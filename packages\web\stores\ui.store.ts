import { create } from 'zustand';

export type ToastType = 'success' | 'error' | 'warning' | 'info';

export interface Toast {
  id: string;
  type: ToastType;
  title: string;
  description?: string;
  duration?: number;
}

export interface Modal {
  id: string;
  component: React.ComponentType<Record<string, unknown>>;
  props?: Record<string, unknown>;
}

export interface UIState {
  // State
  toasts: Toast[];
  modals: Modal[];
  isSidebarOpen: boolean;
  isMobileMenuOpen: boolean;
  isLoading: boolean;
  loadingMessage?: string;

  // Toast actions
  showToast: (toast: Omit<Toast, 'id'>) => void;
  removeToast: (id: string) => void;
  clearToasts: () => void;

  // Modal actions
  openModal: (modal: Omit<Modal, 'id'>) => void;
  closeModal: (id: string) => void;
  closeAllModals: () => void;

  // UI actions
  toggleSidebar: () => void;
  setSidebarOpen: (isOpen: boolean) => void;
  toggleMobileMenu: () => void;
  setMobileMenuOpen: (isOpen: boolean) => void;
  setLoading: (isLoading: boolean, message?: string) => void;

  // Helper actions
  showSuccess: (title: string, description?: string) => void;
  showError: (title: string, description?: string) => void;
  showWarning: (title: string, description?: string) => void;
  showInfo: (title: string, description?: string) => void;
}

let toastIdCounter = 0;
let modalIdCounter = 0;

export const useUIStore = create<UIState>((set, get) => ({
  // Initial state
  toasts: [],
  modals: [],
  isSidebarOpen: true,
  isMobileMenuOpen: false,
  isLoading: false,
  loadingMessage: undefined,

  // Toast actions
  showToast: (toast) => {
    const id = `toast-${++toastIdCounter}`;
    const newToast = { ...toast, id };
    
    set(state => ({
      toasts: [...state.toasts, newToast],
    }));

    // Auto-remove toast after duration
    if (toast.duration !== 0) {
      setTimeout(() => {
        get().removeToast(id);
      }, toast.duration || 5000);
    }
  },

  removeToast: (id) => {
    set(state => ({
      toasts: state.toasts.filter(toast => toast.id !== id),
    }));
  },

  clearToasts: () => {
    set({ toasts: [] });
  },

  // Modal actions
  openModal: (modal) => {
    const id = `modal-${++modalIdCounter}`;
    const newModal = { ...modal, id };
    
    set(state => ({
      modals: [...state.modals, newModal],
    }));
  },

  closeModal: (id) => {
    set(state => ({
      modals: state.modals.filter(modal => modal.id !== id),
    }));
  },

  closeAllModals: () => {
    set({ modals: [] });
  },

  // UI actions
  toggleSidebar: () => {
    set(state => ({
      isSidebarOpen: !state.isSidebarOpen,
    }));
  },

  setSidebarOpen: (isOpen) => {
    set({ isSidebarOpen: isOpen });
  },

  toggleMobileMenu: () => {
    set(state => ({
      isMobileMenuOpen: !state.isMobileMenuOpen,
    }));
  },

  setMobileMenuOpen: (isOpen) => {
    set({ isMobileMenuOpen: isOpen });
  },

  setLoading: (isLoading, message) => {
    set({ isLoading, loadingMessage: message });
  },

  // Helper actions
  showSuccess: (title, description) => {
    get().showToast({
      type: 'success',
      title,
      description,
    });
  },

  showError: (title, description) => {
    get().showToast({
      type: 'error',
      title,
      description,
      duration: 8000, // Errors stay longer
    });
  },

  showWarning: (title, description) => {
    get().showToast({
      type: 'warning',
      title,
      description,
    });
  },

  showInfo: (title, description) => {
    get().showToast({
      type: 'info',
      title,
      description,
    });
  },
}));