"use client";

import { <PERSON><PERSON> } from '@/components/ui/button';
import { MapPin, Plus, Search, Sparkles } from 'lucide-react';

interface EmptyStateProps {
  onCreateTrip: () => void;
  hasTrips: boolean;
  searchQuery: string;
}

export function EmptyState({ onCreateTrip, hasTrips, searchQuery }: EmptyStateProps) {
  if (searchQuery && hasTrips) {
    return (
      <div className="text-center py-12">
        <Search className="h-12 w-12 text-gray-400 mx-auto mb-4" />
        <h3 className="text-lg font-semibold text-gray-900 mb-2">
          No trips found
        </h3>
        <p className="text-gray-600 mb-6">
          Try adjusting your search terms or filters to find what you're looking for.
        </p>
        <Button onClick={() => window.location.reload()} variant="outline">
          Clear Search
        </Button>
      </div>
    );
  }

  return (
    <div className="text-center py-16">
      <div className="relative inline-block mb-8">
        <div className="w-24 h-24 bg-gradient-to-br from-orange-500 to-pink-500 rounded-full flex items-center justify-center mb-4 mx-auto">
          <MapPin className="h-12 w-12 text-white" />
        </div>
        <div className="absolute -top-2 -right-2 w-8 h-8 bg-yellow-400 rounded-full flex items-center justify-center">
          <Sparkles className="h-4 w-4 text-yellow-800" />
        </div>
      </div>
      
      <h3 className="text-2xl font-bold text-gray-900 mb-4">
        Start Your Journey
      </h3>
      <p className="text-gray-600 max-w-md mx-auto mb-8 leading-relaxed">
        Create your first travel plan and transform your ideas into beautiful, 
        interactive itineraries. It only takes a few minutes!
      </p>
      
      <div className="space-y-4">
        <Button onClick={onCreateTrip} size="lg" className="btn-primary">
          <Plus className="h-5 w-5 mr-2" />
          Create Your First Trip
        </Button>
        
        <div className="flex flex-col sm:flex-row items-center justify-center space-y-2 sm:space-y-0 sm:space-x-4 text-sm text-gray-500">
          <span className="flex items-center">
            <div className="w-2 h-2 bg-green-500 rounded-full mr-2"></div>
            3 free trips included
          </span>
          <span className="flex items-center">
            <div className="w-2 h-2 bg-blue-500 rounded-full mr-2"></div>
            AI-powered planning
          </span>
          <span className="flex items-center">
            <div className="w-2 h-2 bg-purple-500 rounded-full mr-2"></div>
            No credit card required
          </span>
        </div>
      </div>
    </div>
  );
}