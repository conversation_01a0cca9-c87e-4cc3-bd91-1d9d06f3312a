import { useAuthStore } from '@/stores/auth.store';
import type { ApiResponse } from '@travelviz/shared';

export interface ApiClientConfig {
  baseURL?: string;
  timeout?: number;
  onUnauthorized?: () => void;
  onError?: (error: ApiError) => void;
  maxRetries?: number;
  retryDelay?: number;
}

export interface ApiError extends Error {
  status?: number;
  code?: string;
  details?: unknown;
}

interface CircuitBreakerState {
  failures: number;
  lastFailureTime: number;
  isOpen: boolean;
}

class ApiClient {
  private baseURL: string;
  private timeout: number;
  private onUnauthorized?: () => void;
  private onError?: (error: ApiError) => void;
  private maxRetries: number;
  private retryDelay: number;
  private circuitBreaker: CircuitBreakerState = {
    failures: 0,
    lastFailureTime: 0,
    isOpen: false,
  };
  private refreshPromise: Promise<boolean> | null = null;

  constructor(config: ApiClientConfig = {}) {
    this.baseURL = config.baseURL || process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001';
    this.timeout = config.timeout || 30000;
    this.onUnauthorized = config.onUnauthorized;
    this.onError = config.onError;
    this.maxRetries = config.maxRetries || 1; // Only 1 retry attempt
    this.retryDelay = config.retryDelay || 1000;
  }

  private async getAuthHeaders(): Promise<HeadersInit> {
    const accessToken = useAuthStore.getState().accessToken;
    
    if (accessToken) {
      return {
        'Authorization': `Bearer ${accessToken}`,
      };
    }
    
    return {};
  }

  private async handleUnauthorized(): Promise<boolean> {
    // Prevent concurrent refresh attempts
    if (this.refreshPromise) {
      return this.refreshPromise;
    }

    this.refreshPromise = (async () => {
      try {
        await useAuthStore.getState().refreshSession();
        this.resetCircuitBreaker();
        return true;
      } catch (error) {
        // Clear authentication state and redirect to login
        useAuthStore.getState().clearAuth();

        // Redirect to login page with current path as redirect parameter
        if (typeof window !== 'undefined') {
          const currentPath = window.location.pathname;
          const loginUrl = `/login${currentPath !== '/login' ? `?redirect=${encodeURIComponent(currentPath)}` : ''}`;
          window.location.href = loginUrl;
        }

        this.onUnauthorized?.();
        return false;
      } finally {
        this.refreshPromise = null;
      }
    })();

    return this.refreshPromise;
  }

  private updateCircuitBreaker(success: boolean) {
    if (success) {
      this.resetCircuitBreaker();
    } else {
      this.circuitBreaker.failures++;
      this.circuitBreaker.lastFailureTime = Date.now();
      
      // Open circuit after 3 consecutive failures
      if (this.circuitBreaker.failures >= 3) {
        this.circuitBreaker.isOpen = true;
      }
    }
  }

  private resetCircuitBreaker() {
    this.circuitBreaker = {
      failures: 0,
      lastFailureTime: 0,
      isOpen: false,
    };
  }

  private isCircuitBreakerOpen(): boolean {
    if (!this.circuitBreaker.isOpen) return false;
    
    // Auto-reset after 60 seconds
    const resetTime = 60000;
    if (Date.now() - this.circuitBreaker.lastFailureTime > resetTime) {
      this.resetCircuitBreaker();
      return false;
    }
    
    return true;
  }

  private createApiError(message: string, status?: number, details?: any): ApiError {
    const error = new Error(message) as ApiError;
    error.status = status;
    error.details = details;
    return error;
  }

  private async request<T = unknown>(
    path: string,
    options: RequestInit = {},
    retryCount = 0
  ): Promise<T> {
    // Check circuit breaker
    if (this.isCircuitBreakerOpen()) {
      throw this.createApiError('Service temporarily unavailable', 503);
    }

    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), this.timeout);

    try {
      const authHeaders = await this.getAuthHeaders();
      const url = `${this.baseURL}${path}`;
      
      const defaultHeaders: HeadersInit = options.body instanceof FormData
        ? { ...authHeaders }
        : { 'Content-Type': 'application/json', ...authHeaders };

      const response = await fetch(url, {
        ...options,
        headers: {
          ...defaultHeaders,
          ...options.headers,
        },
        signal: controller.signal,
      });

      clearTimeout(timeoutId);

      let data: ApiResponse<T>;
      const contentType = response.headers.get('content-type');
      
      if (contentType?.includes('application/json')) {
        data = await response.json();
      } else {
        const text = await response.text();
        data = { success: true, data: text as T };
      }

      // Handle 401 with enhanced error handling
      if (response.status === 401) {
        // Check if this is a token structure/validation error (should not retry)
        const errorMessage = data.error || 'Unauthorized';
        const isTokenError = errorMessage.includes('Invalid token structure') ||
                           errorMessage.includes('Token expired') ||
                           errorMessage.includes('Invalid token');

        if (isTokenError) {
          // Clear auth immediately for token validation errors
          useAuthStore.getState().clearAuth();

          if (typeof window !== 'undefined') {
            const currentPath = window.location.pathname;
            const loginUrl = `/login${currentPath !== '/login' ? `?redirect=${encodeURIComponent(currentPath)}&error=session_expired` : ''}`;
            window.location.href = loginUrl;
          }

          const error = this.createApiError('Session expired. Please log in again.', response.status, data);
          this.onError?.(error);
          throw error;
        }

        // Try token refresh for other 401 errors (with retry limit)
        if (retryCount < this.maxRetries) {
          const refreshed = await this.handleUnauthorized();
          if (refreshed) {
            // Retry with exponential backoff
            await new Promise(resolve => setTimeout(resolve, this.retryDelay * (retryCount + 1)));
            return this.request<T>(path, options, retryCount + 1);
          }
        }
      }

      // Handle rate limiting
      if (response.status === 429) {
        this.updateCircuitBreaker(false);
        const retryAfter = response.headers.get('Retry-After');

        // Log rate limit details for debugging
        if (process.env.NODE_ENV === 'development') {
          console.warn('Rate limit exceeded:', {
            path,
            retryAfter,
            rateLimitRemaining: response.headers.get('RateLimit-Remaining'),
            rateLimitReset: response.headers.get('RateLimit-Reset'),
            rateLimitLimit: response.headers.get('RateLimit-Limit'),
          });
        }

        const error = this.createApiError(
          `Rate limit exceeded. Retry after ${retryAfter || '60'} seconds`,
          429,
          data
        );
        this.onError?.(error);
        throw error;
      }

      // Handle other errors
      if (!response.ok) {
        this.updateCircuitBreaker(false);
        const error = this.createApiError(
          data.error || data.message || 'Request failed',
          response.status,
          data
        );
        this.onError?.(error);
        throw error;
      }

      // Success
      this.updateCircuitBreaker(true);
      return data.data!;
    } catch (error: unknown) {
      clearTimeout(timeoutId);

      // Handle abort
      if (error instanceof Error && error.name === 'AbortError') {
        const timeoutError = this.createApiError('Request timeout', 408);
        this.onError?.(timeoutError);
        throw timeoutError;
      }

      // Handle network errors
      if (error instanceof TypeError && error.message === 'Failed to fetch') {
        this.updateCircuitBreaker(false);
        const networkError = this.createApiError('Network error', 0);
        this.onError?.(networkError);
        throw networkError;
      }

      // Re-throw ApiError
      if (error && typeof error === 'object' && 'status' in error) {
        throw error;
      }

      // Wrap other errors
      const message = error instanceof Error ? error.message : 'Unknown error';
      const wrappedError = this.createApiError(message, 500, error);
      this.onError?.(wrappedError);
      throw wrappedError;
    }
  }

  // HTTP methods remain the same
  async get<T = unknown>(path: string, options?: RequestInit): Promise<T> {
    return this.request<T>(path, {
      ...options,
      method: 'GET',
    });
  }

  async post<T = unknown>(path: string, data?: unknown, options?: RequestInit): Promise<T> {
    return this.request<T>(path, {
      ...options,
      method: 'POST',
      body: data ? JSON.stringify(data) : undefined,
    });
  }

  async put<T = unknown>(path: string, data?: unknown, options?: RequestInit): Promise<T> {
    return this.request<T>(path, {
      ...options,
      method: 'PUT',
      body: data ? JSON.stringify(data) : undefined,
    });
  }

  async patch<T = unknown>(path: string, data?: unknown, options?: RequestInit): Promise<T> {
    return this.request<T>(path, {
      ...options,
      method: 'PATCH',
      body: data ? JSON.stringify(data) : undefined,
    });
  }

  async delete<T = unknown>(path: string, options?: RequestInit): Promise<T> {
    return this.request<T>(path, {
      ...options,
      method: 'DELETE',
    });
  }

  async upload<T = unknown>(path: string, formData: FormData, options?: RequestInit): Promise<T> {
    const authHeaders = await this.getAuthHeaders();
    return this.request<T>(path, {
      ...options,
      method: 'POST',
      body: formData,
      headers: {
        ...authHeaders,
        ...options?.headers,
      },
    });
  }

  // Configuration methods
  setBaseURL(baseURL: string) {
    this.baseURL = baseURL;
  }

  setTimeout(timeout: number) {
    this.timeout = timeout;
  }

  setOnUnauthorized(handler: () => void) {
    this.onUnauthorized = handler;
  }

  setOnError(handler: (error: ApiError) => void) {
    this.onError = handler;
  }

  // Circuit breaker status
  getCircuitBreakerStatus() {
    return {
      isOpen: this.circuitBreaker.isOpen,
      failures: this.circuitBreaker.failures,
      lastFailureTime: this.circuitBreaker.lastFailureTime,
    };
  }
}

// Create default instance with circuit breaker
export const apiClient = new ApiClient({
  onUnauthorized: () => {
    if (typeof window !== 'undefined') {
      window.location.href = '/login';
    }
  },
  onError: (error) => {
    // Log errors in development
    if (process.env.NODE_ENV === 'development') {
      console.error('API Error:', error);
    }
  },
});

// Export class for custom instances
export { ApiClient };

import type { Trip, Activity, User, Profile, PaginatedTripsResult } from '@travelviz/shared';
import type { 
  AuthResponse, 
  ShareResponse, 
  CloneResponse, 
  ImportResponse, 
  PublicTripResponse,
  FlightSearchParams,
  FlightSearchResponse,
  HotelSearchParams,
  HotelSearchResponse,
  WeatherResponse,
  TripAnalytics,
  ProfileUpdateParams
} from './api-types';

// Convenience exports for common API calls
export const api = {
  // Auth
  auth: {
    login: (email: string, password: string) =>
      apiClient.post<AuthResponse>('/api/v1/auth/login', { email, password }),
    signup: (email: string, password: string, name?: string) =>
      apiClient.post<AuthResponse>('/api/v1/auth/signup', { email, password, name }),
    logout: () => apiClient.post<void>('/api/v1/auth/logout'),
    me: () => apiClient.get<User>('/api/v1/auth/me'),
    refresh: (refreshToken: string) =>
      apiClient.post<AuthResponse>('/api/v1/auth/refresh', { refresh_token: refreshToken }),
    resetPassword: (email: string) =>
      apiClient.post<{ success: boolean; message: string }>('/api/v1/auth/reset-password', { email }),
    changePassword: (currentPassword: string, newPassword: string) =>
      apiClient.post<{ success: boolean }>('/api/v1/auth/change-password', { currentPassword, newPassword }),
  },

  // Trips
  trips: {
    list: () => apiClient.get<Trip[]>('/api/v1/trips'),
    listPaginated: (page: number = 1, limit: number = 20) => 
      apiClient.get<PaginatedTripsResult>(`/api/v1/trips?page=${page}&limit=${limit}`),
    get: (id: string) => apiClient.get<Trip>(`/api/v1/trips/${id}`),
    create: (trip: Partial<Trip>) => apiClient.post<Trip>('/api/v1/trips', trip),
    update: (id: string, updates: Partial<Trip>) => apiClient.put<Trip>(`/api/v1/trips/${id}`, updates),
    delete: (id: string) => apiClient.delete<void>(`/api/v1/trips/${id}`),
    
    // Sharing
    makePublic: (id: string) => apiClient.post<ShareResponse>(`/api/v1/trips/${id}/share`),
    makePrivate: (id: string) => apiClient.delete<{ success: boolean }>(`/api/v1/trips/${id}/share`),
    clone: (id: string, title?: string) => apiClient.post<CloneResponse>(`/api/v1/trips/${id}/clone`, { title }),
    
    // Activities
    activities: {
      create: (tripId: string, activity: Partial<Activity>) =>
        apiClient.post<Activity>(`/api/v1/trips/${tripId}/activities`, activity),
      update: (tripId: string, activityId: string, updates: Partial<Activity>) =>
        apiClient.put<Activity>(`/api/v1/trips/${tripId}/activities/${activityId}`, updates),
      delete: (tripId: string, activityId: string) =>
        apiClient.delete<void>(`/api/v1/trips/${tripId}/activities/${activityId}`),
      addFromPlace: (placeId: string, tripId: string) =>
        apiClient.post<Activity>('/api/v1/activities/add-from-place', { place_id: placeId, tripId }),
      reorder: (tripId: string, orderedIds: string[]) =>
        apiClient.patch<{ success: boolean }>(`/api/v1/trips/${tripId}/activities/reorder`, { orderedIds }),
    },
  },

  // Import
  import: {
    parse: (text: string, source: 'chatgpt' | 'claude' | 'gemini') =>
      apiClient.post<ImportResponse>('/api/v1/import/parse', { text, source }),
    upload: (file: File, source: 'chatgpt' | 'claude' | 'gemini') => {
      const formData = new FormData();
      formData.append('file', file);
      formData.append('source', source);
      return apiClient.upload<ImportResponse>('/api/v1/import/upload', formData);
    },
  },

  // Public trips (no auth required)
  public: {
    getTrip: (slug: string) => apiClient.get<PublicTripResponse>(`/api/v1/public/trips/${slug}`),
    getTripPreview: (slug: string) => apiClient.get<PublicTripResponse>(`/api/v1/public/trips/${slug}/preview`),
    checkSlugAvailability: (slug: string) => apiClient.get<{ available: boolean }>(`/api/v1/public/check-slug/${slug}`),
  },

  // Affiliate
  affiliate: {
    searchFlights: (params: FlightSearchParams) => apiClient.post<FlightSearchResponse>('/api/v1/affiliate/flights/search', params),
    
    searchHotels: (params: HotelSearchParams) => apiClient.post<HotelSearchResponse>('/api/v1/affiliate/hotels/search', params),
    
    trackClick: (data: {
      activityId: string;
      url: string;
      affiliateType: 'flight' | 'hotel' | 'car' | 'activity';
    }) => apiClient.post<{ success: boolean }>('/api/v1/affiliate/track', data),
    
    searchAirports: (query: string) => 
      apiClient.get<Array<{ code: string; name: string; city: string; country: string }>>(`/api/v1/affiliate/airports/search?q=${encodeURIComponent(query)}`),
    
    getAnalytics: (days?: number) => 
      apiClient.get<TripAnalytics>(`/api/v1/affiliate/analytics${days ? `?days=${days}` : ''}`),
  },

  // Weather
  weather: {
    getCurrent: (lat: number, lng: number) => 
      apiClient.get<WeatherResponse>(`/api/v1/weather?lat=${lat}&lng=${lng}`),
  },

  // Profile
  profile: {
    get: () => apiClient.get<Profile>('/api/v1/profile'),
    update: (updates: ProfileUpdateParams) => apiClient.put<Profile>('/api/v1/profile', updates),
    uploadAvatar: (file: File) => {
      const formData = new FormData();
      formData.append('avatar', file);
      return apiClient.upload<{ avatar_url: string }>('/api/v1/profile/avatar', formData);
    },
  },
};