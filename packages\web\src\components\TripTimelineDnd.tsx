'use client';

import React, { useState, useCallback, useEffect } from 'react';
import {
  DndContext,
  closestCenter,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
  DragEndEvent,
  DragStartEvent,
  DragOverlay,
} from '@dnd-kit/core';
import {
  arrayMove,
  SortableContext,
  sortableKeyboardCoordinates,
  verticalListSortingStrategy,
} from '@dnd-kit/sortable';
import {
  restrictToVerticalAxis,
  restrictToParentElement,
} from '@dnd-kit/modifiers';
import { useSortable } from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { format, parseISO, isSameDay } from 'date-fns';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { GripVertical, MapPin, Calendar, DollarSign } from 'lucide-react';
import { cn } from '@/lib/utils';
import { useTripStore } from '@/stores/trip.store';
import type { Activity } from '@/stores/trip.store';
import { motion, AnimatePresence } from 'framer-motion';

interface TripTimelineDndProps {
  activities: Activity[];
  tripId: string;
  onReorder?: (newOrder: string[]) => void;
  onReorderError?: (error: Error) => void;
  className?: string;
}

interface DraggableActivityProps {
  activity: Activity;
  isOverlay?: boolean;
}

function DraggableActivity({ activity, isOverlay = false }: DraggableActivityProps) {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({ id: activity.id });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: isDragging && !isOverlay ? 0.5 : 1,
  };

  return (
    <div
      ref={setNodeRef}
      style={style}
      data-sortable-id={activity.id}
      className={cn(
        'relative',
        isDragging && !isOverlay && 'z-50'
      )}
    >
      <Card role="article" className={cn(
        'mb-4 transition-shadow',
        isDragging && !isOverlay && 'shadow-lg'
      )}>
        <CardContent className="p-4">
          <div className="flex items-start gap-3">
            <button
              {...attributes}
              {...listeners}
              className="mt-1 cursor-move touch-none"
              data-testid="drag-handle"
              aria-label="Drag to reorder"
              role="button"
              tabIndex={0}
              aria-disabled={isDragging}
            >
              <GripVertical className="h-5 w-5 text-gray-400" />
            </button>
            
            <div className="flex-1">
              <h4 className="font-semibold text-gray-900">{activity.title}</h4>
              
              <div className="mt-2 flex flex-wrap gap-3 text-sm text-gray-600">
                {activity.start_time && (
                  <div className="flex items-center gap-1">
                    <Calendar className="h-3.5 w-3.5" />
                    {format(parseISO(activity.start_time), 'h:mm a')}
                  </div>
                )}
                
                {activity.location && (
                  <div className="flex items-center gap-1">
                    <MapPin className="h-3.5 w-3.5" />
                    {activity.location}
                  </div>
                )}
                
                {activity.price && (
                  <div className="flex items-center gap-1">
                    <DollarSign className="h-3.5 w-3.5" />
                    {activity.price} {activity.currency}
                  </div>
                )}
              </div>
              
              {activity.description && (
                <p className="mt-2 text-sm text-gray-600 line-clamp-2">
                  {activity.description}
                </p>
              )}
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

export default function TripTimelineDnd({
  activities,
  tripId,
  onReorder,
  onReorderError,
  className,
}: TripTimelineDndProps) {
  const [activeId, setActiveId] = useState<string | null>(null);
  const [items, setItems] = useState<string[]>([]);
  const [isSaving, setIsSaving] = useState(false);
  
  const { reorderActivities } = useTripStore();

  // Initialize items from activities
  useEffect(() => {
    setItems(activities.map(a => a.id));
  }, [activities]);

  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 8,
      },
    }),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );

  const handleDragStart = (event: DragStartEvent) => {
    setActiveId(event.active.id as string);
  };

  const handleDragEnd = async (event: DragEndEvent) => {
    const { active, over } = event;
    
    if (over && active.id !== over.id) {
      const oldIndex = items.indexOf(active.id as string);
      const newIndex = items.indexOf(over.id as string);
      
      // Check if activities are on the same day
      const activeActivity = activities.find(a => a.id === active.id);
      const overActivity = activities.find(a => a.id === over.id);
      
      if (activeActivity?.start_time && overActivity?.start_time) {
        const activeDate = parseISO(activeActivity.start_time);
        const overDate = parseISO(overActivity.start_time);
        
        if (!isSameDay(activeDate, overDate)) {
          // Prevent reordering across days
          setActiveId(null);
          return;
        }
      }
      
      const newItems = arrayMove(items, oldIndex, newIndex);
      setItems(newItems);
      
      // Call the callback
      if (onReorder) {
        onReorder(newItems);
      }
      
      // Save to backend
      setIsSaving(true);
      try {
        await reorderActivities(tripId, newItems);
      } catch (error) {
        // Revert on error
        setItems(items);
        if (onReorderError) {
          onReorderError(error as Error);
        }
      } finally {
        setIsSaving(false);
      }
    }
    
    setActiveId(null);
  };

  // Group activities by day
  const activitiesByDay = activities.reduce((acc, activity) => {
    const date = activity.start_time 
      ? format(parseISO(activity.start_time), 'yyyy-MM-dd')
      : 'unscheduled';
    
    if (!acc[date]) {
      acc[date] = [];
    }
    acc[date].push(activity);
    return acc;
  }, {} as Record<string, Activity[]>);

  const activeActivity = activeId 
    ? activities.find(a => a.id === activeId)
    : null;

  if (activities.length === 0) {
    return (
      <div className={cn('text-center py-8 text-gray-500', className)}>
        No Activities Yet
      </div>
    );
  }

  return (
    <DndContext
      sensors={sensors}
      collisionDetection={closestCenter}
      onDragStart={handleDragStart}
      onDragEnd={handleDragEnd}
      modifiers={[restrictToVerticalAxis, restrictToParentElement]}
    >
      <div className={cn('space-y-6', className)}>
        {Object.entries(activitiesByDay).map(([date, dayActivities]) => (
          <div key={date}>
            {date !== 'unscheduled' && (
              <h3 className="text-lg font-semibold mb-3">
                {format(parseISO(date), 'EEEE, MMMM d, yyyy')}
              </h3>
            )}
            {date === 'unscheduled' && (
              <h3 className="text-lg font-semibold mb-3">
                Unscheduled Activities
              </h3>
            )}
            
            <SortableContext
              items={dayActivities.map(a => a.id)}
              strategy={verticalListSortingStrategy}
            >
              <AnimatePresence>
                {dayActivities.map((activity) => (
                  <motion.div
                    key={activity.id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -20 }}
                    transition={{ duration: 0.2 }}
                  >
                    <DraggableActivity activity={activity} />
                  </motion.div>
                ))}
              </AnimatePresence>
            </SortableContext>
          </div>
        ))}
        
        {isSaving && (
          <div 
            className="fixed bottom-4 right-4 bg-black text-white px-4 py-2 rounded-lg shadow-lg"
            aria-label="Saving changes..."
          >
            Saving...
          </div>
        )}
      </div>
      
      <DragOverlay>
        {activeActivity && (
          <div data-testid="drag-overlay">
            <DraggableActivity activity={activeActivity} isOverlay />
          </div>
        )}
      </DragOverlay>
    </DndContext>
  );
}