import { PlacesService } from '../services/places.service';
import { vi, describe, it, expect, beforeEach, afterEach } from 'vitest';
import { Client } from '@googlemaps/google-maps-services-js';

// Mock the Google Maps client
vi.mock('@googlemaps/google-maps-services-js', () => ({
  Client: vi.fn().mockImplementation(() => ({
    placeAutocomplete: vi.fn(),
    placeDetails: vi.fn(),
  })),
  Language: {
    en: 'en',
  },
  PlaceAutocompleteType: {
    establishment: 'establishment',
  },
}));

describe('PlacesService', () => {
  let placesService: PlacesService;
  let mockGoogleMapsClient: any;

  beforeEach(() => {
    vi.clearAllMocks();
    mockGoogleMapsClient = {
      placeAutocomplete: vi.fn(),
      placeDetails: vi.fn(),
    };
    (vi.mocked(Client) as any).mockImplementation(() => mockGoogleMapsClient);
    placesService = new PlacesService();
  });

  describe('searchPlaces', () => {
    it('should return autocomplete suggestions for a search query', async () => {
      // Arrange
      const query = 'Eiffel Tower';
      const mockResponse = {
        data: {
          predictions: [
            {
              description: 'Eiffel Tower, Champ de Mars, 5 Avenue Anatole France, 75007 Paris, France',
              place_id: 'ChIJLU7jZClu5kcR4PcOOO6p3I0',
              structured_formatting: {
                main_text: 'Eiffel Tower',
                secondary_text: 'Champ de Mars, 5 Avenue Anatole France, 75007 Paris, France',
              },
            },
            {
              description: 'Eiffel Tower Restaurant, Las Vegas, NV, USA',
              place_id: 'ChIJ123456789',
              structured_formatting: {
                main_text: 'Eiffel Tower Restaurant',
                secondary_text: 'Las Vegas, NV, USA',
              },
            },
          ],
        },
      };

      mockGoogleMapsClient.placeAutocomplete.mockResolvedValue(mockResponse);

      // Act
      const result = await placesService.searchPlaces(query);

      // Assert
      expect(mockGoogleMapsClient.placeAutocomplete).toHaveBeenCalledWith({
        params: {
          input: query,
          key: process.env.GOOGLE_PLACES_API_KEY,
          types: 'establishment',
          language: 'en',
        },
      });

      expect(result).toEqual({
        suggestions: [
          {
            description: 'Eiffel Tower, Champ de Mars, 5 Avenue Anatole France, 75007 Paris, France',
            place_id: 'ChIJLU7jZClu5kcR4PcOOO6p3I0',
            main_text: 'Eiffel Tower',
            secondary_text: 'Champ de Mars, 5 Avenue Anatole France, 75007 Paris, France',
          },
          {
            description: 'Eiffel Tower Restaurant, Las Vegas, NV, USA',
            place_id: 'ChIJ123456789',
            main_text: 'Eiffel Tower Restaurant',
            secondary_text: 'Las Vegas, NV, USA',
          },
        ],
      });
    });

    it('should handle empty search results', async () => {
      // Arrange
      const query = 'nonexistent place';
      const mockResponse = {
        data: {
          predictions: [],
        },
      };

      mockGoogleMapsClient.placeAutocomplete.mockResolvedValue(mockResponse);

      // Act
      const result = await placesService.searchPlaces(query);

      // Assert
      expect(result).toEqual({
        suggestions: [],
      });
    });

    it('should handle API errors gracefully', async () => {
      // Arrange
      const query = 'test query';
      const apiError = new Error('Google Places API error');
      mockGoogleMapsClient.placeAutocomplete.mockRejectedValue(apiError);

      // Act & Assert
      await expect(placesService.searchPlaces(query)).rejects.toThrow('Failed to search places: Google Places API error');
    });

    it('should validate query length', async () => {
      // Arrange
      const shortQuery = 'a';

      // Act & Assert
      await expect(placesService.searchPlaces(shortQuery)).rejects.toThrow('Query must be at least 2 characters long');
    });
  });

  describe('getPlaceDetails', () => {
    it('should return detailed place information for a place_id', async () => {
      // Arrange
      const placeId = 'ChIJLU7jZClu5kcR4PcOOO6p3I0';
      const mockResponse = {
        data: {
          result: {
            place_id: placeId,
            name: 'Eiffel Tower',
            formatted_address: 'Champ de Mars, 5 Avenue Anatole France, 75007 Paris, France',
            geometry: {
              location: {
                lat: 48.8583736,
                lng: 2.2944813,
              },
            },
            types: ['tourist_attraction', 'point_of_interest', 'establishment'],
            rating: 4.6,
            user_ratings_total: 158925,
            photos: [
              {
                photo_reference: 'photo_ref_123',
              },
            ],
            opening_hours: {
              weekday_text: [
                'Monday: 9:30 AM – 11:45 PM',
                'Tuesday: 9:30 AM – 11:45 PM',
                // ... other days
              ],
            },
            website: 'https://www.toureiffel.paris/',
            international_phone_number: '+33 8 92 70 12 39',
          },
        },
      };

      mockGoogleMapsClient.placeDetails.mockResolvedValue(mockResponse);

      // Act
      const result = await placesService.getPlaceDetails(placeId);

      // Assert
      expect(mockGoogleMapsClient.placeDetails).toHaveBeenCalledWith({
        params: {
          place_id: placeId,
          key: process.env.GOOGLE_PLACES_API_KEY,
          fields: ['place_id', 'name', 'formatted_address', 'geometry', 'types', 'rating', 'user_ratings_total', 'photos', 'opening_hours', 'website', 'international_phone_number'],
          language: 'en',
        },
      });

      expect(result).toEqual({
        place_id: placeId,
        name: 'Eiffel Tower',
        formatted_address: 'Champ de Mars, 5 Avenue Anatole France, 75007 Paris, France',
        location_lat: 48.8583736,
        location_lng: 2.2944813,
        types: ['tourist_attraction', 'point_of_interest', 'establishment'],
        rating: 4.6,
        user_ratings_total: 158925,
        photo_reference: 'photo_ref_123',
        opening_hours: [
          'Monday: 9:30 AM – 11:45 PM',
          'Tuesday: 9:30 AM – 11:45 PM',
        ],
        website: 'https://www.toureiffel.paris/',
        phone: '+33 8 92 70 12 39',
      });
    });

    it('should handle missing optional fields gracefully', async () => {
      // Arrange
      const placeId = 'ChIJ123456789';
      const mockResponse = {
        data: {
          result: {
            place_id: placeId,
            name: 'Simple Place',
            formatted_address: '123 Simple Street',
            geometry: {
              location: {
                lat: 40.7128,
                lng: -74.0060,
              },
            },
            types: ['establishment'],
            // Missing: rating, photos, opening_hours, website, phone
          },
        },
      };

      mockGoogleMapsClient.placeDetails.mockResolvedValue(mockResponse);

      // Act
      const result = await placesService.getPlaceDetails(placeId);

      // Assert
      expect(result).toEqual({
        place_id: placeId,
        name: 'Simple Place',
        formatted_address: '123 Simple Street',
        location_lat: 40.7128,
        location_lng: -74.0060,
        types: ['establishment'],
        rating: undefined,
        user_ratings_total: undefined,
        photo_reference: undefined,
        opening_hours: undefined,
        website: undefined,
        phone: undefined,
      });
    });

    it('should handle API errors for place details', async () => {
      // Arrange
      const placeId = 'invalid_place_id';
      const apiError = new Error('Invalid place ID');
      mockGoogleMapsClient.placeDetails.mockRejectedValue(apiError);

      // Act & Assert
      await expect(placesService.getPlaceDetails(placeId)).rejects.toThrow('Failed to get place details: Invalid place ID');
    });

    it('should validate place_id format', async () => {
      // Arrange
      const invalidPlaceId = '';

      // Act & Assert
      await expect(placesService.getPlaceDetails(invalidPlaceId)).rejects.toThrow('Place ID is required');
    });
  });

  describe('mapPlaceToActivity', () => {
    it('should map place details to activity format', async () => {
      // Arrange
      const placeDetails = {
        place_id: 'ChIJLU7jZClu5kcR4PcOOO6p3I0',
        name: 'Eiffel Tower',
        formatted_address: 'Champ de Mars, 5 Avenue Anatole France, 75007 Paris, France',
        location_lat: 48.8583736,
        location_lng: 2.2944813,
        types: ['tourist_attraction', 'point_of_interest', 'establishment'],
        rating: 4.6,
        user_ratings_total: 158925,
        photo_reference: 'photo_ref_123',
        opening_hours: ['Monday: 9:30 AM – 11:45 PM'],
        website: 'https://www.toureiffel.paris/',
        phone: '+33 8 92 70 12 39',
      };

      // Act
      const result = placesService.mapPlaceToActivity(placeDetails);

      // Assert
      expect(result).toEqual({
        title: 'Eiffel Tower',
        description: 'Tourist attraction with a rating of 4.6 stars (158925 reviews)',
        type: 'activity',
        location: 'Champ de Mars, 5 Avenue Anatole France, 75007 Paris, France',
        location_lat: 48.8583736,
        location_lng: 2.2944813,
        booking_url: 'https://www.toureiffel.paris/',
        notes: 'Phone: +33 8 92 70 12 39\nHours: Monday: 9:30 AM – 11:45 PM',
      });
    });

    it('should determine activity type based on place types', async () => {
      // Test different place types mapping to activity types
      const testCases = [
        {
          types: ['lodging', 'establishment'],
          expectedType: 'accommodation',
        },
        {
          types: ['restaurant', 'food', 'establishment'],
          expectedType: 'dining',
        },
        {
          types: ['shopping_mall', 'establishment'],
          expectedType: 'shopping',
        },
        {
          types: ['airport', 'establishment'],
          expectedType: 'transport',
        },
        {
          types: ['tourist_attraction', 'establishment'],
          expectedType: 'activity',
        },
        {
          types: ['establishment'],
          expectedType: 'other',
        },
      ];

      testCases.forEach(({ types, expectedType }) => {
        const placeDetails = {
          place_id: 'test',
          name: 'Test Place',
          formatted_address: 'Test Address',
          location_lat: 0,
          location_lng: 0,
          types,
        };

        const result = placesService.mapPlaceToActivity(placeDetails);
        expect(result.type).toBe(expectedType);
      });
    });
  });
});