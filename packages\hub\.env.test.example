# Test Environment Configuration
# Copy this to .env.test and fill in with your test service credentials

# Upstash Redis (create a free test instance at upstash.com)
UPSTASH_REDIS_REST_URL=https://your-test-redis.upstash.io
UPSTASH_REDIS_REST_TOKEN=your-test-token

# Supabase (create a test project at supabase.com)
SUPABASE_URL=https://your-test-project.supabase.co
SUPABASE_ANON_KEY=your-test-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-test-service-role-key

# Other services (use test/sandbox credentials)
OPENROUTER_API_KEY=sk-or-test-key
GOOGLE_PLACES_API_KEY=test-google-places-key

# Test user credentials
TEST_USER_EMAIL=<EMAIL>
TEST_USER_PASSWORD=TestPassword123!

# JWT Secret for tests
JWT_SECRET=test-jwt-secret-key-with-at-least-32-characters-for-tests

# Test data isolation prefix (optional)
TEST_DATA_PREFIX=test_