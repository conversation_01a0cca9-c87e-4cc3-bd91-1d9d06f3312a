#!/usr/bin/env node

/**
 * Quick test script to test a single model with sample data
 */

const axios = require('axios');
const path = require('path');
require('dotenv').config({ path: path.join(__dirname, '../.env.local') });

const MODEL_CONFIGS = {
  'gemini-flash': 'google/gemini-2.0-flash-001',
  'deepseek-free': 'deepseek/deepseek-chat-v3-0324:free',
  'gpt-mini': 'openai/gpt-4o-mini',
  'claude-haiku': 'anthropic/claude-3-haiku'
};

const TEST_TEXT = `
Day 1: Tokyo Adventure
- 9:00 AM: Arrive at Narita Airport
- 12:00 PM: Check into Park Hyatt Tokyo ($350/night)
- 2:00 PM: Visit <PERSON>-<PERSON> Temple
- 7:00 PM: Dinner at Sukiyabashi Jiro ($300)

Day 2: Exploring Tokyo
- 9:00 AM: Tsukiji Fish Market tour
- 1:00 PM: Lunch at local ramen shop ($15)
- 3:00 PM: Shopping in Ginza
- 8:00 PM: Tokyo Tower observation deck ($20)
`;

async function testModel(modelKey) {
  const modelId = MODEL_CONFIGS[modelKey] || modelKey;
  console.log(`\n🧪 Testing model: ${modelId}`);
  console.log('=' .repeat(50));
  
  try {
    const startTime = Date.now();
    
    const prompt = `You are TravelViz, an expert travel itinerary parser.

Extract travel itinerary from this text and return ONLY valid JSON:
${TEST_TEXT}

Return JSON:
{
  "title": "string",
  "destination": "string",
  "activities": [
    {
      "title": "string",
      "type": "flight|accommodation|transport|dining|activity|shopping|car_rental|tour|sightseeing|entertainment|other",
      "startTime": "HH:MM",
      "location": "string",
      "price": number,
      "currency": "string",
      "day": number
    }
  ]
}`;

    const response = await axios.post(
      'https://openrouter.ai/api/v1/chat/completions',
      {
        model: modelId,
        messages: [{ role: 'user', content: prompt }],
        temperature: 0.2,
        max_tokens: 2000
      },
      {
        headers: {
          'Authorization': `Bearer ${process.env.OPENROUTER_API_KEY}`,
          'Content-Type': 'application/json'
        },
        timeout: 30000
      }
    );
    
    const aiResponse = response.data.choices[0]?.message?.content;
    const duration = Date.now() - startTime;
    const tokens = response.data.usage?.total_tokens || 0;
    
    console.log(`✅ Success in ${duration}ms`);
    console.log(`📊 Tokens used: ${tokens}`);
    console.log(`\n📝 Raw Response:\n${aiResponse}\n`);
    
    // Try to parse JSON
    try {
      let jsonString = aiResponse;
      if (aiResponse.includes('```')) {
        jsonString = aiResponse.replace(/```json\s*/, '').replace(/```\s*$/, '');
      }
      const parsed = JSON.parse(jsonString.match(/\{[\s\S]*\}/)[0]);
      console.log(`✅ Valid JSON with ${parsed.activities?.length || 0} activities`);
      console.log(`📍 Destination: ${parsed.destination || 'Not specified'}`);
      console.log(`📋 Title: ${parsed.title || 'Not specified'}`);
    } catch (e) {
      console.log(`❌ Failed to parse JSON: ${e.message}`);
    }
    
  } catch (error) {
    console.log(`❌ Error: ${error.message}`);
    if (error.response?.data) {
      console.log('API Error:', JSON.stringify(error.response.data, null, 2));
    }
  }
}

// Main execution
async function main() {
  if (!process.env.OPENROUTER_API_KEY) {
    console.error('❌ OPENROUTER_API_KEY not found in .env.local');
    process.exit(1);
  }
  
  const modelKey = process.argv[2];
  
  if (!modelKey) {
    console.log('Usage: node quick-model-test.js <model-key>');
    console.log('\nAvailable shortcuts:');
    Object.entries(MODEL_CONFIGS).forEach(([key, value]) => {
      console.log(`  ${key} -> ${value}`);
    });
    console.log('\nOr use full model ID like: google/gemini-2.0-flash-001');
    process.exit(1);
  }
  
  await testModel(modelKey);
}

main().catch(console.error);