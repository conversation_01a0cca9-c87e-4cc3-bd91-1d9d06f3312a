#!/usr/bin/env node

/**
 * Apply AI Model Optimization Database Migration
 * Applies the schema changes for intelligent AI model selection and usage tracking
 */

import { readFileSync } from 'fs';
import { join } from 'path';
import { logger } from '../utils/logger';
import { getSupabaseClient } from '../lib/supabase';

async function applyMigration() {
  console.log('🚀 Applying AI Model Optimization Migration...');
  
  try {
    // Read the migration file
    const migrationPath = join(__dirname, '../../../../supabase/migrations/20250117_ai_model_optimization_schema.sql');
    const migrationSQL = readFileSync(migrationPath, 'utf8');
    
    // Split into individual statements (basic splitting by semicolon)
    const statements = migrationSQL
      .split(';')
      .map(stmt => stmt.trim())
      .filter(stmt => stmt.length > 0 && !stmt.startsWith('--'));
    
    console.log(`📄 Found ${statements.length} SQL statements to execute`);
    
    // Execute each statement
    for (let i = 0; i < statements.length; i++) {
      const statement = statements[i];
      
      // Skip comments and empty statements
      if (statement.startsWith('--') || statement.trim().length === 0) {
        continue;
      }
      
      try {
        console.log(`⏳ Executing statement ${i + 1}/${statements.length}...`);
        
        // For CREATE EXTENSION, SELECT cron.schedule, and other special statements
        if (statement.includes('CREATE EXTENSION') || 
            statement.includes('cron.schedule') ||
            statement.includes('GRANT') ||
            statement.includes('ALTER TABLE') ||
            statement.includes('CREATE TABLE') ||
            statement.includes('CREATE INDEX') ||
            statement.includes('CREATE OR REPLACE FUNCTION') ||
            statement.includes('INSERT INTO') ||
            statement.includes('CREATE POLICY') ||
            statement.includes('CREATE OR REPLACE VIEW')) {
          
          const { error } = await getSupabaseClient().rpc('exec_sql', { 
            sql: statement + ';' 
          });
          
          if (error) {
            // Some errors are expected (like extension already exists)
            if (error.message.includes('already exists') || 
                error.message.includes('duplicate key') ||
                error.message.includes('relation already exists')) {
              console.log(`⚠️  Statement ${i + 1} skipped (already exists): ${error.message}`);
            } else {
              throw error;
            }
          } else {
            console.log(`✅ Statement ${i + 1} executed successfully`);
          }
        }
      } catch (error) {
        console.error(`❌ Error executing statement ${i + 1}:`, error);
        console.error(`Statement: ${statement.substring(0, 100)}...`);
        
        // Continue with other statements unless it's a critical error
        if (statement.includes('CREATE TABLE') || statement.includes('CREATE OR REPLACE FUNCTION')) {
          throw error; // Critical errors should stop execution
        }
      }
    }
    
    console.log('✅ Migration applied successfully!');
    
    // Verify the migration
    await verifyMigration();
    
  } catch (error) {
    console.error('💥 Migration failed:', error);
    process.exit(1);
  }
}

async function verifyMigration() {
  console.log('\n🔍 Verifying migration...');
  
  try {
    // Check if tables exist
    const tables = ['ai_model_usage', 'ai_request_logs', 'ai_model_configs'];
    
    for (const table of tables) {
      const { data, error } = await getSupabaseClient()
        .from(table)
        .select('*')
        .limit(1);
      
      if (error) {
        throw new Error(`Table ${table} verification failed: ${error.message}`);
      }
      
      console.log(`✅ Table ${table} is accessible`);
    }
    
    // Check if enhanced columns exist in ai_import_logs
    const { data: importData, error: importError } = await getSupabaseClient()
      .from('ai_import_logs')
      .select('model_used, input_tokens, output_tokens, processing_cost, fallback_attempts')
      .limit(1);
    
    if (importError) {
      throw new Error(`Enhanced ai_import_logs columns verification failed: ${importError.message}`);
    }
    
    console.log('✅ Enhanced ai_import_logs columns are accessible');
    
    // Check if functions exist
    const { data: functionData, error: functionError } = await getSupabaseClient()
      .rpc('get_model_usage', { model_id_param: 'test' });
    
    if (functionError && !functionError.message.includes('does not exist')) {
      // Function exists but might return empty result
      console.log('✅ Database functions are working');
    }
    
    // Check if model configs were inserted
    const { data: configs, error: configError } = await getSupabaseClient()
      .from('ai_model_configs')
      .select('*');
    
    if (configError) {
      throw new Error(`Model configs verification failed: ${configError.message}`);
    }
    
    console.log(`✅ Model configurations loaded: ${configs?.length || 0} models`);
    
    console.log('✅ Migration verification completed successfully!');
    
  } catch (error) {
    console.error('❌ Migration verification failed:', error);
    throw error;
  }
}

// Run migration if called directly
if (require.main === module) {
  applyMigration().catch(error => {
    console.error('Migration execution failed:', error);
    process.exit(1);
  });
}

export { applyMigration };