import { Router } from 'express';
import { TripsService } from '../services/trips.service';
import { createSuccessResponse, createErrorResponse, HTTP_STATUS, Trip } from '@travelviz/shared';
import { authenticateSupabaseUser, SupabaseAuthenticatedRequest } from '../middleware/supabase-auth.middleware';
import { apiRateLimit } from '../middleware/rate-limit.middleware';
import { logger } from '../utils/logger';
import { getSupabaseClient } from '../lib/supabase';
import DOMPurify from 'isomorphic-dompurify';

// Define the search result interface once at the top
interface SearchResult {
  trip_id: string;
  rank: number;
}

const router: Router = Router();
const tripsService = new TripsService();

// Apply rate limiting to all routes
router.use(apiRateLimit);

// All search routes require authentication
router.use(authenticateSupabaseUser);

/**
 * GET /api/v1/search/trips
 * Search trips by query
 */
router.get('/trips', async (req: SupabaseAuthenticatedRequest, res) => {
  try {
    const rawQuery = typeof req.query.q === 'string' ? req.query.q.trim() : '';
    const userId = req.user?.id;
    
    if (!userId) {
      return res.status(HTTP_STATUS.UNAUTHORIZED).json(
        createErrorResponse('Unauthorized', 'User not authenticated')
      );
    }

    if (!rawQuery) {
      return res.status(HTTP_STATUS.BAD_REQUEST).json(
        createErrorResponse('Search query is required', 'Please provide a search query using the q parameter')
      );
    }

    // Sanitize the search query to prevent injection attacks
    const query = DOMPurify.sanitize(rawQuery, { ALLOWED_TAGS: [] });
    
    // Validate query length
    if (query.length < 2) {
      return res.status(HTTP_STATUS.BAD_REQUEST).json(
        createErrorResponse('Query too short', 'Search query must be at least 2 characters')
      );
    }
    
    if (query.length > 100) {
      return res.status(HTTP_STATUS.BAD_REQUEST).json(
        createErrorResponse('Query too long', 'Search query must be less than 100 characters')
      );
    }

    logger.info('Trip search request', { query: query.substring(0, 50), userId });

    // Use the database's full-text search function
    const supabase = getSupabaseClient();
    const { data: searchResults, error } = await supabase.rpc('search_trips_ranked', {
      p_search_query: query,
      p_user_id: userId,
      p_limit: 50
    });

    if (error) {
      logger.error('Trip search database error', { 
        error: error.message,
        code: error.code,
        // Don't log sensitive details
        userId 
      });
      return res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(
        createErrorResponse('Failed to search trips', 'An error occurred while searching trips')
      );
    }

    // Get full trip details for the search results
    const tripIds = searchResults?.map((r: SearchResult) => r.trip_id) || [];
    let fullTrips: Trip[] = [];
    
    if (tripIds.length > 0) {
      // Get trips with activities using the existing service
      const allTrips = await tripsService.getUserTrips(userId);
      fullTrips = allTrips.filter(trip => tripIds.includes(trip.id));
      
      // Sort by the search rank
      const rankMap = new Map<string, number>(searchResults.map((r: SearchResult) => [r.trip_id, r.rank]));
      fullTrips.sort((a, b) => {
        const rankA = rankMap.get(a.id) || 0;
        const rankB = rankMap.get(b.id) || 0;
        return rankB - rankA;
      });
    }

    res.json(createSuccessResponse({
      query,
      results: fullTrips,
      total: fullTrips.length
    }));
  } catch (error) {
    logger.error('Trip search error', { 
      error: error instanceof Error ? error.message : 'Unknown error',
      // Don't log full error object to avoid exposing sensitive data
    });
    res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(
      createErrorResponse('Failed to search trips', 'An error occurred while searching trips')
    );
  }
});

/**
 * POST /api/v1/search/trips
 * Search trips with filters
 */
router.post('/trips', async (req: SupabaseAuthenticatedRequest, res) => {
  try {
    const { query: rawQuery = '', filters = {} } = req.body;
    const userId = req.user?.id;
    
    if (!userId) {
      return res.status(HTTP_STATUS.UNAUTHORIZED).json(
        createErrorResponse('Unauthorized', 'User not authenticated')
      );
    }

    // Sanitize the search query
    const query = rawQuery ? DOMPurify.sanitize(rawQuery, { ALLOWED_TAGS: [] }) : '';
    
    // Validate query if provided
    if (query && query.length < 2) {
      return res.status(HTTP_STATUS.BAD_REQUEST).json(
        createErrorResponse('Query too short', 'Search query must be at least 2 characters')
      );
    }
    
    if (query && query.length > 100) {
      return res.status(HTTP_STATUS.BAD_REQUEST).json(
        createErrorResponse('Query too long', 'Search query must be less than 100 characters')
      );
    }

    logger.info('Trip search with filters', { 
      query: query ? query.substring(0, 50) : '', 
      filters: {
        status: filters.status,
        hasDateFilter: !!(filters.startDate || filters.endDate)
      }, 
      userId 
    });

    let filteredTrips: Trip[] = [];
    
    if (query) {
      // Use database search if query is provided
      const supabase = getSupabaseClient();
      const { data: searchResults, error } = await supabase.rpc('search_trips_ranked', {
        p_search_query: query,
        p_user_id: userId,
        p_limit: 100
      });

      if (error) {
        logger.error('Trip search database error', { 
          error: error.message,
          code: error.code,
          userId 
        });
        return res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(
          createErrorResponse('Failed to search trips', 'An error occurred while searching trips')
        );
      }

      const tripIds = searchResults?.map((r: SearchResult) => r.trip_id) || [];
      
      if (tripIds.length > 0) {
        // Get trips with activities
        const allTrips = await tripsService.getUserTrips(userId);
        filteredTrips = allTrips.filter(trip => tripIds.includes(trip.id));
        
        // Sort by search rank
        const rankMap = new Map<string, number>(searchResults.map((r: SearchResult) => [r.trip_id, r.rank]));
        filteredTrips.sort((a, b) => {
          const rankA = rankMap.get(a.id) || 0;
          const rankB = rankMap.get(b.id) || 0;
          return rankB - rankA;
        });
      }
    } else {
      // If no query, get all user trips
      filteredTrips = await tripsService.getUserTrips(userId);
    }
    
    // Apply additional filters
    
    // Status filter
    if (filters.status) {
      filteredTrips = filteredTrips.filter(trip => trip.status === filters.status);
    }
    
    // Date range filter
    if (filters.startDate || filters.endDate) {
      filteredTrips = filteredTrips.filter(trip => {
        if (!trip.start_date) return false;
        const tripDate = new Date(trip.start_date);
        
        if (filters.startDate) {
          const filterStart = new Date(filters.startDate);
          if (tripDate < filterStart) return false;
        }
        
        if (filters.endDate) {
          const filterEnd = new Date(filters.endDate);
          if (tripDate > filterEnd) return false;
        }
        
        return true;
      });
    }

    res.json(createSuccessResponse({
      query,
      filters,
      results: filteredTrips,
      total: filteredTrips.length
    }));
  } catch (error) {
    logger.error('Trip search error', { 
      error: error instanceof Error ? error.message : 'Unknown error'
    });
    res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(
      createErrorResponse('Failed to search trips', 'An error occurred while searching trips')
    );
  }
});

export { router as searchRoutes };
export default router;