/**
 * API Test Client with Token Management
 * 
 * Provides a clean interface for making API requests with automatic
 * token management, persistence, and refresh capabilities.
 */

const fs = require('fs');
const path = require('path');
const apiConfig = require('../api.config');

class ApiTestClient {
  constructor() {
    this.baseUrl = apiConfig.baseUrl;
    this.timeout = apiConfig.timeout;
    this.tokenFile = path.join(__dirname, '../.test-tokens.json');
    this.tokens = this.loadStoredTokens();
  }

  // Token Management
  loadStoredTokens() {
    try {
      if (fs.existsSync(this.tokenFile)) {
        const data = fs.readFileSync(this.tokenFile, 'utf8');
        return JSON.parse(data);
      }
    } catch (error) {
      console.warn('Failed to load stored tokens:', error.message);
    }
    return {};
  }

  saveTokens(accessToken, refreshToken, user, expiresAt = null) {
    // Use provided expiration time or calculate our own (55 minutes from now)
    const tokenExpiresAt = expiresAt ? (expiresAt * 1000) : (Date.now() + (55 * 60 * 1000));
    
    this.tokens = {
      accessToken,
      refreshToken,
      user,
      expiresAt: tokenExpiresAt,
      savedAt: Date.now()
    };
    
    try {
      fs.writeFileSync(this.tokenFile, JSON.stringify(this.tokens, null, 2));
    } catch (error) {
      console.warn('Failed to save tokens:', error.message);
    }
  }

  clearTokens() {
    this.tokens = {};
    try {
      if (fs.existsSync(this.tokenFile)) {
        fs.unlinkSync(this.tokenFile);
      }
    } catch (error) {
      console.warn('Failed to clear tokens:', error.message);
    }
  }

  isTokenExpired() {
    if (!this.tokens.accessToken || !this.tokens.expiresAt) {
      return true;
    }
    return Date.now() >= this.tokens.expiresAt;
  }

  getStoredToken() {
    if (this.isTokenExpired()) {
      return null;
    }
    return this.tokens.accessToken;
  }

  // HTTP Request Helper
  async makeRequest(method, endpoint, data = null, headers = {}) {
    const url = `${this.baseUrl}${endpoint}`;
    
    const config = {
      method: method.toUpperCase(),
      headers: {
        'Content-Type': 'application/json',
        ...headers
      }
    };

    if (data) {
      config.body = JSON.stringify(data);
    }

    try {
      const response = await fetch(url, config);
      const responseData = await response.json().catch(() => ({}));
      
      return {
        success: response.ok,
        status: response.status,
        data: responseData,
        headers: Object.fromEntries(response.headers.entries())
      };
    } catch (error) {
      return {
        success: false,
        status: 0,
        error: error.message,
        data: null
      };
    }
  }

  // Authenticated Request with Auto Token Refresh
  async authenticatedRequest(method, endpoint, data = null, headers = {}) {
    // Try to refresh token if expired
    if (this.isTokenExpired() && this.tokens.refreshToken) {
      await this.refreshTokenIfExpired();
    }

    const token = this.getStoredToken();
    if (!token) {
      return {
        success: false,
        status: 401,
        error: 'No valid authentication token available',
        data: null
      };
    }

    const authHeaders = {
      ...headers,
      'Authorization': `Bearer ${token}`
    };

    const response = await this.makeRequest(method, endpoint, data, authHeaders);
    
    // If token is invalid, try to refresh once
    if (response.status === 401 && this.tokens.refreshToken) {
      const refreshResult = await this.refreshTokenIfExpired();
      if (refreshResult.success) {
        const newToken = this.getStoredToken();
        const retryHeaders = {
          ...headers,
          'Authorization': `Bearer ${newToken}`
        };
        return await this.makeRequest(method, endpoint, data, retryHeaders);
      }
    }

    return response;
  }

  // Authentication Methods
  async login(email, password) {
    const response = await this.makeRequest('POST', apiConfig.endpoints.auth.login, {
      email,
      password
    });

    if (response.success && response.data.data) {
      const { access_token, refresh_token, user, expires_at } = response.data.data;
      if (access_token) {
        this.saveTokens(access_token, refresh_token, user, expires_at);
      }
    }

    return response;
  }

  async signup(email, password, name) {
    const response = await this.makeRequest('POST', apiConfig.endpoints.auth.signup, {
      email,
      password,
      name
    });

    if (response.success && response.data.data) {
      const { access_token, refresh_token, user } = response.data.data;
      if (access_token) {
        this.saveTokens(access_token, refresh_token, user);
      }
    }

    return response;
  }

  async logout() {
    const response = await this.authenticatedRequest('POST', apiConfig.endpoints.auth.logout);
    this.clearTokens();
    return response;
  }

  async getMe() {
    return await this.authenticatedRequest('GET', apiConfig.endpoints.auth.me);
  }

  async refreshTokenIfExpired() {
    if (!this.tokens.refreshToken) {
      return { success: false, error: 'No refresh token available' };
    }

    const response = await this.makeRequest('POST', apiConfig.endpoints.auth.refresh, {
      refreshToken: this.tokens.refreshToken
    });

    if (response.success && response.data.data) {
      const { access_token, refresh_token, user } = response.data.data;
      if (access_token) {
        this.saveTokens(access_token, refresh_token, user);
      }
    } else {
      // Refresh failed, clear tokens
      this.clearTokens();
    }

    return response;
  }

  // Trip Management Methods
  async createTrip(tripData) {
    return await this.authenticatedRequest('POST', apiConfig.endpoints.trips.create, tripData);
  }

  async getTrips(params = {}) {
    const queryString = new URLSearchParams(params).toString();
    const endpoint = queryString ? `${apiConfig.endpoints.trips.list}?${queryString}` : apiConfig.endpoints.trips.list;
    return await this.authenticatedRequest('GET', endpoint);
  }

  async getTrip(tripId) {
    return await this.authenticatedRequest('GET', apiConfig.endpoints.trips.get(tripId));
  }

  async updateTrip(tripId, updates) {
    return await this.authenticatedRequest('PUT', apiConfig.endpoints.trips.update(tripId), updates);
  }

  async deleteTrip(tripId) {
    return await this.authenticatedRequest('DELETE', apiConfig.endpoints.trips.delete(tripId));
  }

  // Activity Management Methods
  async createActivity(tripId, activityData) {
    return await this.authenticatedRequest('POST', apiConfig.endpoints.activities.create(tripId), activityData);
  }

  async updateActivity(activityId, updates) {
    return await this.authenticatedRequest('PUT', apiConfig.endpoints.activities.update(activityId), updates);
  }

  async deleteActivity(activityId) {
    return await this.authenticatedRequest('DELETE', apiConfig.endpoints.activities.delete(activityId));
  }

  // Import Methods
  async parseSimple(content, source) {
    return await this.authenticatedRequest('POST', apiConfig.endpoints.import.parseSimple, {
      content,
      source
    });
  }

  async startParse(text, source) {
    return await this.authenticatedRequest('POST', apiConfig.endpoints.import.parse, {
      text,
      source
    });
  }

  // Places Methods
  async autocomplete(query) {
    const endpoint = `${apiConfig.endpoints.places.autocomplete}?input=${encodeURIComponent(query)}`;
    return await this.authenticatedRequest('GET', endpoint);
  }

  async geocode(locations) {
    return await this.authenticatedRequest('POST', apiConfig.endpoints.places.geocode, {
      locations
    });
  }

  // Health Check (no auth required)
  async healthCheck() {
    return await this.makeRequest('GET', apiConfig.endpoints.health);
  }

  // Test Helpers
  expectSuccess(response, message = '') {
    if (!response.success) {
      throw new Error(`Expected success but got failure${message ? ': ' + message : ''}. Status: ${response.status}, Error: ${response.error || JSON.stringify(response.data)}`);
    }
    return response;
  }

  expectError(response, expectedStatus, message = '') {
    if (response.success) {
      throw new Error(`Expected error but got success${message ? ': ' + message : ''}`);
    }
    if (expectedStatus && response.status !== expectedStatus) {
      throw new Error(`Expected status ${expectedStatus} but got ${response.status}${message ? ': ' + message : ''}`);
    }
    return response;
  }

  expectStatus(response, expectedStatus, message = '') {
    if (response.status !== expectedStatus) {
      throw new Error(`Expected status ${expectedStatus} but got ${response.status}${message ? ': ' + message : ''}. Response: ${JSON.stringify(response.data)}`);
    }
    return response;
  }
}

module.exports = ApiTestClient;