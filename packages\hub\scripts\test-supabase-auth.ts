import dotenv from 'dotenv';
dotenv.config({ path: '.env.local' });

import { createClient } from '@supabase/supabase-js';

const supabaseUrl = process.env.SUPABASE_URL!;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!;

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function testAuth() {
  console.log('🧪 Testing Supabase Auth directly...\n');
  
  const testEmail = `test${Date.now()}@example.com`;
  const testPassword = 'Test123@';
  
  console.log(`📧 Attempting to create user: ${testEmail}`);
  
  try {
    // Try to create a user
    const { data, error } = await supabase.auth.admin.createUser({
      email: testEmail,
      password: testPassword,
      email_confirm: true, // Skip email confirmation for testing
      user_metadata: {
        name: 'Test User'
      }
    });
    
    if (error) {
      console.error('❌ Error creating user:', error);
      
      // Check if it's a specific error
      if (error.message?.includes('already exists')) {
        console.log('   User already exists');
      }
      return;
    }
    
    console.log('✅ User created successfully!');
    console.log('   User ID:', data.user?.id);
    console.log('   Email:', data.user?.email);
    
    // Check if profile was created
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', data.user?.id)
      .single();
      
    if (profileError) {
      console.log('❌ Profile not found:', profileError.message);
    } else {
      console.log('✅ Profile exists:', profile);
    }
    
    // Try to sign in
    console.log('\n🔐 Testing sign in...');
    const { data: signInData, error: signInError } = await supabase.auth.signInWithPassword({
      email: testEmail,
      password: testPassword
    });
    
    if (signInError) {
      console.error('❌ Sign in failed:', signInError);
    } else {
      console.log('✅ Sign in successful!');
      console.log('   Session:', signInData.session ? 'Created' : 'Not created');
    }
    
    // Clean up - delete the test user
    console.log('\n🧹 Cleaning up...');
    if (data.user?.id) {
      const { error: deleteError } = await supabase.auth.admin.deleteUser(data.user.id);
      if (deleteError) {
        console.error('❌ Failed to delete test user:', deleteError);
      } else {
        console.log('✅ Test user deleted');
      }
    }
    
  } catch (err) {
    console.error('❌ Unexpected error:', err);
  }
}

testAuth();