import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import morgan from 'morgan';
import rateLimit from 'express-rate-limit';
import compression from 'compression';

// Load environment variables with proper precedence
import { loadEnvironment } from './utils/env-loader';
loadEnvironment();

// Import routes
import authRoutes from './routes/supabase-auth.routes';
import tripRoutes from './routes/trips.routes';
import activityRoutes from './routes/activities.routes';
import importRoutes from './routes/import.routes';
import modelsRoutes from './routes/models.routes';
import affiliateRoutes from './routes/affiliate.routes';
import publicRoutes from './routes/public.routes';
import { placesRoutes } from './routes/places.routes';
import monitoringRoutes from './routes/monitoring.routes';

// Import middleware
import { errorHandler } from './middleware/error-handler.middleware';
import { requestIdMiddleware } from './middleware/request-id.middleware';

export function createServer(): express.Application {
  const app = express();

  // Security middleware
  app.use(helmet());

  // Compression middleware for better performance
  app.use(compression({
    threshold: 1024, // Only compress responses larger than 1KB
    level: 6, // Compression level (1-9, 6 is balanced)
    filter: (req, res) => {
      // Don't compress if client doesn't support it
      if (req.headers['x-no-compression']) {
        return false;
      }
      // Use compression filter for all other cases
      return compression.filter(req, res);
    }
  }));

  // Basic rate limiting middleware
  const limiter = rateLimit({
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 100, // Limit each IP to 100 requests per windowMs
    message: 'Too many requests from this IP, please try again later.',
    standardHeaders: true, // Return rate limit info in the `RateLimit-*` headers
    legacyHeaders: false, // Disable the `X-RateLimit-*` headers
  });

  // Apply rate limiting to all requests
  app.use(limiter);

  // Stricter rate limiting for auth routes
  const authLimiter = rateLimit({
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 5, // Limit each IP to 5 requests per windowMs
    message: 'Too many authentication attempts, please try again later.',
    skipSuccessfulRequests: true, // Don't count successful requests
  });

  // CORS configuration  
  app.use(cors({
    origin: process.env.FRONTEND_URL || 'http://localhost:3000',
    credentials: true
  }));

  // Body parsing middleware
  app.use(express.json({ limit: '10mb' }));
  app.use(express.urlencoded({ extended: true, limit: '10mb' }));

  // Request ID middleware
  app.use(requestIdMiddleware);

  // Morgan HTTP logging
  if (process.env.NODE_ENV === 'development') {
    app.use(morgan('dev', {
      skip: (req) => req.url === '/health'
    }));
  }

  // Routes
  app.get('/health', (req, res) => res.json({ status: 'ok' }));
  app.use('/api/v1/public', publicRoutes); // Public routes (no auth required)
  app.use('/api/v1/auth', authLimiter, authRoutes); // Apply stricter rate limiting to auth routes
  app.use('/api/v1/trips', tripRoutes);
  app.use('/api/v1/activities', activityRoutes);
  app.use('/api/v1/places', placesRoutes);
  app.use('/api/v1/import', importRoutes);
  app.use('/api/v1/models', modelsRoutes);
  app.use('/api/v1/affiliate', affiliateRoutes);
  app.use('/api/v1/monitoring', monitoringRoutes);

  // Error handling middleware (must be last)
  app.use(errorHandler);

  return app;
}