import { describe, it, expect, beforeAll, afterAll, beforeEach, afterEach } from 'vitest';
import request from 'supertest';
import { getSupabaseClient } from '../../../src/lib/supabase';
import { createServer } from '../../../src/server';
import { Express } from 'express';
import path from 'path';
import { randomUUID } from 'crypto';

// Load environment variables from .env.local
import dotenv from 'dotenv';
dotenv.config({ path: path.join(__dirname, '../../../../.env.local') });

const TEST_USER_EMAIL = '<EMAIL>';
const TEST_USER_PASSWORD = 'Flaremmk123!';

/**
 * Trip Sharing Flow Integration Tests
 * Tests the complete trip sharing lifecycle including public access and collaboration
 */
describe('Trip Sharing Flow - Complete Integration Tests', () => {
  let app: Express;
  let server: any;
  let authToken: string;
  let userId: string;
  let testTripId: string;
  let publicSlug: string;

  beforeAll(async () => {
    console.log('🚀 Starting trip sharing flow tests...');
    app = createServer();
    server = app.listen(0);

    // Get auth token
    const supabase = getSupabaseClient();
    const { data, error } = await supabase.auth.signInWithPassword({
      email: TEST_USER_EMAIL,
      password: TEST_USER_PASSWORD
    });

    if (error) {
      throw new Error(`Failed to authenticate: ${error.message}`);
    }

    authToken = data.session?.access_token || '';
    userId = data.user?.id || '';
    console.log('✅ Got auth token');
  });

  afterAll(async () => {
    server?.close();
  });

  beforeEach(async () => {
    // Create a test trip for each test
    const tripResponse = await request(app)
      .post('/api/v1/trips')
      .set('Authorization', `Bearer ${authToken}`)
      .send({
        title: `Test Trip for Sharing ${randomUUID().slice(0, 8)}`,
        description: 'A trip created for testing sharing functionality',
        visibility: 'private',
        start_date: '2024-08-01',
        end_date: '2024-08-07',
        destination: 'Paris, France'
      });

    testTripId = tripResponse.body.data.trip.id;
    
    // Add some activities to make the trip more realistic
    await request(app)
      .post('/api/v1/activities')
      .set('Authorization', `Bearer ${authToken}`)
      .send({
        trip_id: testTripId,
        title: 'Visit Eiffel Tower',
        description: 'Iconic landmark visit',
        type: 'activity',
        start_time: '2024-08-01T10:00:00Z',
        end_time: '2024-08-01T12:00:00Z',
        location: 'Eiffel Tower, Paris',
        position: 1
      });

    console.log(`📝 Created test trip: ${testTripId}`);
  });

  afterEach(async () => {
    // Cleanup test trip
    if (testTripId) {
      await request(app)
        .delete(`/api/v1/trips/${testTripId}`)
        .set('Authorization', `Bearer ${authToken}`);
      
      testTripId = '';
      publicSlug = '';
    }
  });

  describe('Making Trips Public', () => {
    it('should make a private trip public and generate shareable slug', async () => {
      const response = await request(app)
        .post(`/api/v1/trips/${testTripId}/share`)
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          visibility: 'public'
        });

      console.log('Share response:', response.status, response.body);

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveProperty('slug');
      expect(response.body.data).toHaveProperty('public_url');
      
      publicSlug = response.body.data.slug;
      console.log(`✅ Trip made public with slug: ${publicSlug}`);
    });

    it('should make a trip unlisted (shareable but not discoverable)', async () => {
      const response = await request(app)
        .post(`/api/v1/trips/${testTripId}/share`)
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          visibility: 'unlisted'
        });

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveProperty('slug');
      
      publicSlug = response.body.data.slug;
      console.log(`✅ Trip made unlisted with slug: ${publicSlug}`);
    });

    it('should require authentication to make trip public', async () => {
      const response = await request(app)
        .post(`/api/v1/trips/${testTripId}/share`)
        .send({
          visibility: 'public'
        });

      expect(response.status).toBe(401);
      expect(response.body.success).toBe(false);
      console.log('✅ Authentication required for sharing');
    });

    it('should prevent non-owner from making trip public', async () => {
      // Create second user
      const secondUserEmail = `test-sharing-${randomUUID().slice(0, 8)}@mmkdev.com`;
      const secondUserPassword = 'TestShare123!';
      
      // Register and login second user
      await request(app)
        .post('/api/v1/auth/signup')
        .send({
          email: secondUserEmail,
          password: secondUserPassword,
          confirmPassword: secondUserPassword
        });

      const loginResponse = await request(app)
        .post('/api/v1/auth/login')
        .send({
          email: secondUserEmail,
          password: secondUserPassword
        });

      const secondUserToken = loginResponse.body.data.token;
      const secondUserId = loginResponse.body.data.user.id;

      // Try to share first user's trip
      const shareResponse = await request(app)
        .post(`/api/v1/trips/${testTripId}/share`)
        .set('Authorization', `Bearer ${secondUserToken}`)
        .send({
          visibility: 'public'
        });

      expect([403, 404]).toContain(shareResponse.status);
      expect(shareResponse.body.success).toBe(false);

      // Cleanup second user
      const supabase = getSupabaseClient();
      await supabase.auth.admin.deleteUser(secondUserId);
      
      console.log('✅ Non-owner cannot share trip');
    });
  });

  describe('Public Trip Access', () => {
    beforeEach(async () => {
      // Make trip public for these tests
      const shareResponse = await request(app)
        .post(`/api/v1/trips/${testTripId}/share`)
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          visibility: 'public'
        });
      
      publicSlug = shareResponse.body.data.slug;
    });

    it('should access public trip without authentication', async () => {
      const response = await request(app)
        .get(`/api/v1/public/trips/${publicSlug}`);

      console.log('Public access response:', response.status, response.body);

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data.trip).toHaveProperty('title');
      expect(response.body.data.trip).toHaveProperty('description');
      expect(response.body.data.trip.id).toBe(testTripId);
      
      // Should not expose sensitive data
      expect(response.body.data.trip).not.toHaveProperty('user_id');
      
      console.log('✅ Public trip accessible without authentication');
    });

    it('should get public trip preview for social sharing', async () => {
      const response = await request(app)
        .get(`/api/v1/public/trips/${publicSlug}/preview`);

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveProperty('title');
      expect(response.body.data).toHaveProperty('description');
      expect(response.body.data).toHaveProperty('image_url');
      expect(response.body.data).toHaveProperty('public_url');
      
      console.log('✅ Public trip preview generated for social sharing');
    });

    it('should track views for public trips', async () => {
      // Access the public trip multiple times
      await request(app).get(`/api/v1/public/trips/${publicSlug}`);
      await request(app).get(`/api/v1/public/trips/${publicSlug}`);
      await request(app).get(`/api/v1/public/trips/${publicSlug}`);

      // Get trip as owner to check view count
      const tripResponse = await request(app)
        .get(`/api/v1/trips/${testTripId}`)
        .set('Authorization', `Bearer ${authToken}`);

      expect(tripResponse.status).toBe(200);
      expect(tripResponse.body.data.trip.view_count).toBeGreaterThan(0);
      
      console.log(`✅ View tracking working: ${tripResponse.body.data.trip.view_count} views`);
    });

    it('should return 404 for non-existent public slug', async () => {
      const response = await request(app)
        .get('/api/v1/public/trips/non-existent-slug-12345');

      expect(response.status).toBe(404);
      expect(response.body.success).toBe(false);
      console.log('✅ Non-existent slug returns 404');
    });

    it('should not access private trip via public endpoint', async () => {
      // Make trip private again
      await request(app)
        .delete(`/api/v1/trips/${testTripId}/share`)
        .set('Authorization', `Bearer ${authToken}`);

      // Try to access via public endpoint
      const response = await request(app)
        .get(`/api/v1/public/trips/${publicSlug}`);

      expect(response.status).toBe(404);
      expect(response.body.success).toBe(false);
      console.log('✅ Private trip not accessible via public endpoint');
    });
  });

  describe('Trip Cloning', () => {
    beforeEach(async () => {
      // Make trip public for cloning tests
      const shareResponse = await request(app)
        .post(`/api/v1/trips/${testTripId}/share`)
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          visibility: 'public'
        });
      
      publicSlug = shareResponse.body.data.slug;
    });

    it('should clone a public trip for authenticated user', async () => {
      const response = await request(app)
        .post(`/api/v1/trips/${testTripId}/clone`)
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          title: 'My Cloned Paris Trip'
        });

      console.log('Clone response:', response.status, response.body);

      expect(response.status).toBe(201);
      expect(response.body.success).toBe(true);
      expect(response.body.data.trip).toHaveProperty('id');
      expect(response.body.data.trip.title).toBe('My Cloned Paris Trip');
      expect(response.body.data.trip.id).not.toBe(testTripId);

      const clonedTripId = response.body.data.trip.id;

      // Verify activities were cloned
      const activitiesResponse = await request(app)
        .get(`/api/v1/trips/${clonedTripId}/activities`)
        .set('Authorization', `Bearer ${authToken}`);

      expect(activitiesResponse.status).toBe(200);
      expect(activitiesResponse.body.data.activities.length).toBeGreaterThan(0);

      // Cleanup cloned trip
      await request(app)
        .delete(`/api/v1/trips/${clonedTripId}`)
        .set('Authorization', `Bearer ${authToken}`);

      console.log('✅ Trip cloning successful with activities');
    });

    it('should require authentication to clone trip', async () => {
      const response = await request(app)
        .post(`/api/v1/trips/${testTripId}/clone`)
        .send({
          title: 'Unauthorized Clone Attempt'
        });

      expect(response.status).toBe(401);
      expect(response.body.success).toBe(false);
      console.log('✅ Authentication required for cloning');
    });

    it('should not clone private trips', async () => {
      // Make trip private
      await request(app)
        .delete(`/api/v1/trips/${testTripId}/share`)
        .set('Authorization', `Bearer ${authToken}`);

      const response = await request(app)
        .post(`/api/v1/trips/${testTripId}/clone`)
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          title: 'Private Trip Clone Attempt'
        });

      expect([403, 404]).toContain(response.status);
      expect(response.body.success).toBe(false);
      console.log('✅ Private trips cannot be cloned');
    });
  });

  describe('Making Trips Private', () => {
    beforeEach(async () => {
      // Make trip public first
      const shareResponse = await request(app)
        .post(`/api/v1/trips/${testTripId}/share`)
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          visibility: 'public'
        });
      
      publicSlug = shareResponse.body.data.slug;
    });

    it('should make public trip private and remove public access', async () => {
      const response = await request(app)
        .delete(`/api/v1/trips/${testTripId}/share`)
        .set('Authorization', `Bearer ${authToken}`);

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);

      // Verify public access is removed
      const publicAccessResponse = await request(app)
        .get(`/api/v1/public/trips/${publicSlug}`);

      expect(publicAccessResponse.status).toBe(404);
      expect(publicAccessResponse.body.success).toBe(false);

      console.log('✅ Trip made private, public access removed');
    });

    it('should require authentication to make trip private', async () => {
      const response = await request(app)
        .delete(`/api/v1/trips/${testTripId}/share`);

      expect(response.status).toBe(401);
      expect(response.body.success).toBe(false);
      console.log('✅ Authentication required to make trip private');
    });
  });

  describe('Slug Management', () => {
    it('should check slug availability', async () => {
      const uniqueSlug = `paris-trip-${randomUUID().slice(0, 8)}`;
      
      const response = await request(app)
        .get(`/api/v1/public/check-slug/${uniqueSlug}`);

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data.available).toBe(true);
      
      console.log('✅ Slug availability check working');
    });

    it('should detect unavailable slug', async () => {
      // Make trip public to get a slug
      const shareResponse = await request(app)
        .post(`/api/v1/trips/${testTripId}/share`)
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          visibility: 'public'
        });
      
      const usedSlug = shareResponse.body.data.slug;

      // Check if that slug is available
      const checkResponse = await request(app)
        .get(`/api/v1/public/check-slug/${usedSlug}`);

      expect(checkResponse.status).toBe(200);
      expect(checkResponse.body.success).toBe(true);
      expect(checkResponse.body.data.available).toBe(false);
      
      console.log('✅ Used slug properly detected as unavailable');
    });
  });

  describe('Social Sharing Optimization', () => {
    beforeEach(async () => {
      // Make trip public for social sharing tests
      const shareResponse = await request(app)
        .post(`/api/v1/trips/${testTripId}/share`)
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          visibility: 'public'
        });
      
      publicSlug = shareResponse.body.data.slug;
    });

    it('should generate proper Open Graph metadata', async () => {
      const response = await request(app)
        .get(`/api/v1/public/trips/${publicSlug}/preview`);

      expect(response.status).toBe(200);
      expect(response.body.data).toHaveProperty('og_title');
      expect(response.body.data).toHaveProperty('og_description');
      expect(response.body.data).toHaveProperty('og_image');
      expect(response.body.data).toHaveProperty('og_url');
      
      console.log('✅ Open Graph metadata generated');
    });

    it('should handle trips without images gracefully', async () => {
      const response = await request(app)
        .get(`/api/v1/public/trips/${publicSlug}/preview`);

      expect(response.status).toBe(200);
      expect(response.body.data.image_url).toBeDefined();
      // Should have fallback image or placeholder
      
      console.log('✅ Fallback image handling working');
    });
  });
});