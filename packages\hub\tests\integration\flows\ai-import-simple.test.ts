import { describe, it, expect, beforeAll, afterAll } from 'vitest';
import request from 'supertest';
import { getSupabaseClient } from '../../../src/lib/supabase';
import { createServer } from '../../../src/server';
import { Express } from 'express';
import path from 'path';

// Load environment variables from .env.local
import dotenv from 'dotenv';
dotenv.config({ path: path.join(__dirname, '../../../../.env.local') });

// Test configuration
const TEST_USER_EMAIL = '<EMAIL>';
const TEST_USER_PASSWORD = 'Flaremmk123!';

/**
 * Simplified AI Import Flow Integration Test
 * Following the exact pattern of simple-api.test.ts
 * 
 * Tests the critical AI import flow with real server and database
 */
describe('AI Import Flow - Simplified Integration Test', () => {
  let app: Express;
  let server: any;
  let authToken: string;

  beforeAll(async () => {
    console.log('🚀 Starting AI import flow test...');
    app = createServer();
    server = app.listen(0);

    // Get auth token directly from Supabase
    const supabase = getSupabaseClient();
    const { data, error } = await supabase.auth.signInWithPassword({
      email: TEST_USER_EMAIL,
      password: TEST_USER_PASSWORD
    });

    if (error) {
      console.error('Auth error:', error);
      throw new Error(`Failed to authenticate: ${error.message}`);
    }

    authToken = data.session?.access_token || '';
    console.log('✅ Got auth token');
  });

  afterAll(async () => {
    server?.close();
  });

  it('should import a ChatGPT conversation and create a trip', async () => {
    const conversation = `User: Plan a 2-day trip to Paris.
Assistant: Here's your Paris itinerary:

Day 1:
- Morning: Visit the Eiffel Tower
- Afternoon: Explore the Louvre Museum
- Evening: Seine River cruise

Day 2:
- Morning: Trip to Versailles Palace
- Afternoon: Walk through Montmartre
- Evening: Dinner in the Latin Quarter`;

    console.log('📤 Testing AI import...');
    
    const response = await request(app)
      .post('/api/v1/import/parse-simple')
      .set('Authorization', `Bearer ${authToken}`)
      .send({
        content: conversation,
        source: 'chatgpt'
      });

    console.log('Response status:', response.status);
    console.log('Response body:', JSON.stringify(response.body, null, 2));

    expect(response.status).toBe(200);
    expect(response.body.success).toBe(true);
    expect(response.body.data).toHaveProperty('importId');
    expect(response.body.data).toHaveProperty('message');

    const importId = response.body.data.importId;
    console.log(`✅ Import started with ID: ${importId}`);

    // Wait for processing
    await new Promise(resolve => setTimeout(resolve, 3000));

    // Check import status
    const statusResponse = await request(app)
      .get(`/api/v1/import/parse-simple/${importId}`)
      .set('Authorization', `Bearer ${authToken}`);

    console.log('Status response:', JSON.stringify(statusResponse.body, null, 2));
    expect(statusResponse.status).toBe(200);

    // If trip was created, verify it
    if (statusResponse.body.data?.trip) {
      const tripId = statusResponse.body.data.trip.id;
      console.log(`✅ Trip created with ID: ${tripId}`);

      // Verify trip details
      const tripResponse = await request(app)
        .get(`/api/v1/trips/${tripId}`)
        .set('Authorization', `Bearer ${authToken}`);

      expect(tripResponse.status).toBe(200);
      expect(tripResponse.body.data.trip.title).toContain('Paris');

      // Verify activities were created
      const activitiesResponse = await request(app)
        .get(`/api/v1/trips/${tripId}/activities`)
        .set('Authorization', `Bearer ${authToken}`);

      expect(activitiesResponse.status).toBe(200);
      expect(Array.isArray(activitiesResponse.body.data.activities)).toBe(true);
      
      console.log(`✅ Trip has ${activitiesResponse.body.data.activities.length} activities`);

      // Cleanup: Delete the test trip
      await request(app)
        .delete(`/api/v1/trips/${tripId}`)
        .set('Authorization', `Bearer ${authToken}`);
    }
  });

  it('should import a Claude conversation', async () => {
    const conversation = `User: I need a 3-day Rome itinerary.
Assistant: Here's your Rome adventure:

Day 1:
- Morning: Colosseum and Roman Forum
- Afternoon: Pantheon and Trevi Fountain
- Evening: Dinner in Trastevere

Day 2:
- Morning: Vatican Museums and Sistine Chapel
- Afternoon: St. Peter's Basilica
- Evening: Spanish Steps area

Day 3:
- Morning: Villa Borghese and Galleria Borghese
- Afternoon: Castel Sant'Angelo
- Evening: Aperitivo in Campo de' Fiori`;

    const response = await request(app)
      .post('/api/v1/import/parse-simple')
      .set('Authorization', `Bearer ${authToken}`)
      .send({
        content: conversation,
        source: 'claude'
      });

    expect(response.status).toBe(200);
    expect(response.body.success).toBe(true);
    expect(response.body.data).toHaveProperty('importId');

    console.log(`✅ Claude conversation imported with ID: ${response.body.data.importId}`);
  });

  it('should require authentication', async () => {
    const response = await request(app)
      .post('/api/v1/import/parse-simple')
      .send({
        content: 'test conversation',
        source: 'chatgpt'
      });

    expect(response.status).toBe(401);
    console.log('✅ Authentication properly enforced');
  });

  it('should handle invalid conversation content', async () => {
    const response = await request(app)
      .post('/api/v1/import/parse-simple')
      .set('Authorization', `Bearer ${authToken}`)
      .send({
        content: 'This is not a travel conversation at all.',
        source: 'chatgpt'
      });

    // Should either succeed with warning or fail gracefully
    expect([200, 400, 422]).toContain(response.status);
    console.log(`✅ Invalid content handled with status: ${response.status}`);
  });

  it('should handle empty content', async () => {
    const response = await request(app)
      .post('/api/v1/import/parse-simple')
      .set('Authorization', `Bearer ${authToken}`)
      .send({
        content: '',
        source: 'chatgpt'
      });

    expect(response.status).toBe(400);
    expect(response.body.success).toBe(false);
    console.log('✅ Empty content properly rejected');
  });

  it('should support concurrent imports', async () => {
    const conversations = [
      `User: Plan a weekend in San Francisco.
Assistant: Here's your SF weekend:
Day 1: Golden Gate Bridge, Fisherman's Wharf
Day 2: Alcatraz tour, Union Square`,
      
      `User: Quick Seattle trip?
Assistant: Seattle highlights:
Day 1: Pike Place Market, Space Needle
Day 2: Waterfront, museums`
    ];

    // Start both imports simultaneously
    const importPromises = conversations.map(conversation =>
      request(app)
        .post('/api/v1/import/parse-simple')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          content: conversation,
          source: 'chatgpt'
        })
    );

    const responses = await Promise.all(importPromises);

    // Both should succeed
    responses.forEach(response => {
      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
    });

    console.log(`✅ ${responses.length} concurrent imports completed successfully`);
  });
});