#!/usr/bin/env node

/**
 * Comprehensive PDF Import Integration Test
 * Tests the complete PDF import flow with detailed monitoring and analysis
 */

const axios = require('axios');
const fs = require('fs');
const path = require('path');
const FormData = require('form-data');

// Test Configuration
const CONFIG = {
  API_BASE_URL: process.env.API_BASE_URL || 'http://localhost:3001',
  TEST_EMAIL: '<EMAIL>',
  TEST_PASSWORD: process.env.TEST_PASSWORD || 'Flaremmk123!',
  PDF_FILE_PATH: 'C:\\Users\\<USER>\\Travelviz\\Travelviz\\Updated 15-Day European Travel Itinerary_ London, Madrid, Lisbon and Porto.pdf',
  MAX_POLLING_TIME: 5 * 60 * 1000, // 5 minutes
  POLLING_INTERVAL: 2000, // 2 seconds
  TIMEOUT_THRESHOLD: 90000, // 90 seconds - should match backend timeout
};

// Test Results Storage
const testResults = {
  startTime: Date.now(),
  authTime: null,
  uploadTime: null,
  parsingStartTime: null,
  parsingEndTime: null,
  totalTime: null,
  modelSelected: null,
  progressUpdates: [],
  errors: [],
  timeouts: [],
  circuitBreakerEvents: [],
  performanceMetrics: {},
  success: false
};

// Logging utility
function log(level, message, data = {}) {
  const timestamp = new Date().toISOString();
  const logEntry = {
    timestamp,
    level,
    message,
    ...data
  };
  
  console.log(`[${timestamp}] ${level.toUpperCase()}: ${message}`, 
    Object.keys(data).length > 0 ? JSON.stringify(data, null, 2) : '');
  
  // Store in test results
  if (level === 'ERROR') {
    testResults.errors.push(logEntry);
  }
}

// Authentication function
async function authenticate() {
  log('INFO', 'Starting authentication...');
  const startTime = Date.now();
  
  try {
    const response = await axios.post(
      `${CONFIG.API_BASE_URL}/api/v1/auth/login`,
      {
        email: CONFIG.TEST_EMAIL,
        password: CONFIG.TEST_PASSWORD
      },
      {
        timeout: 10000,
        headers: {
          'Content-Type': 'application/json'
        }
      }
    );

    if (!response.data.success) {
      throw new Error(`Login failed: ${response.data.message || 'Unknown error'}`);
    }

    // Extract token from various possible response structures
    const token = response.data.data?.session?.access_token ||
                  response.data.data?.access_token ||
                  response.data.session?.access_token ||
                  response.data.access_token;

    if (!token) {
      throw new Error('No auth token received in response');
    }

    testResults.authTime = Date.now() - startTime;
    log('INFO', 'Authentication successful', { 
      authTime: testResults.authTime,
      tokenLength: token.length 
    });
    
    return token;
  } catch (error) {
    log('ERROR', 'Authentication failed', { 
      error: error.message,
      authTime: Date.now() - startTime 
    });
    throw error;
  }
}

// PDF Upload function
async function uploadPDF(authToken) {
  log('INFO', 'Starting PDF upload...');
  const startTime = Date.now();

  try {
    // Check if file exists
    if (!fs.existsSync(CONFIG.PDF_FILE_PATH)) {
      throw new Error(`PDF file not found: ${CONFIG.PDF_FILE_PATH}`);
    }

    const fileStats = fs.statSync(CONFIG.PDF_FILE_PATH);
    log('INFO', 'PDF file found', { 
      path: CONFIG.PDF_FILE_PATH,
      size: fileStats.size,
      sizeKB: Math.round(fileStats.size / 1024)
    });

    // Create form data
    const formData = new FormData();
    formData.append('file', fs.createReadStream(CONFIG.PDF_FILE_PATH));

    const response = await axios.post(
      `${CONFIG.API_BASE_URL}/api/v1/import/pdf`,
      formData,
      {
        headers: {
          'Authorization': `Bearer ${authToken}`,
          ...formData.getHeaders()
        },
        timeout: 30000, // 30 second timeout for upload
        maxContentLength: 50 * 1024 * 1024, // 50MB max
        maxBodyLength: 50 * 1024 * 1024
      }
    );

    if (!response.data.success) {
      throw new Error(`Upload failed: ${response.data.message || 'Unknown error'}`);
    }

    testResults.uploadTime = Date.now() - startTime;
    const sessionId = response.data.data.sessionId;
    
    log('INFO', 'PDF upload successful', {
      sessionId,
      uploadTime: testResults.uploadTime,
      metadata: response.data.data.metadata
    });

    return {
      sessionId,
      metadata: response.data.data.metadata
    };
  } catch (error) {
    log('ERROR', 'PDF upload failed', {
      error: error.message,
      uploadTime: Date.now() - startTime
    });
    throw error;
  }
}

// Status polling function with detailed monitoring
async function pollStatus(sessionId, authToken) {
  log('INFO', 'Starting status polling...', { sessionId });
  testResults.parsingStartTime = Date.now();
  
  let attempts = 0;
  let lastProgress = -1;
  let lastStatus = '';
  let stuckCounter = 0;
  const STUCK_THRESHOLD = 5; // Consider stuck after 5 polls with no progress

  while (Date.now() - testResults.parsingStartTime < CONFIG.MAX_POLLING_TIME) {
    attempts++;
    const pollStartTime = Date.now();

    try {
      const response = await axios.get(
        `${CONFIG.API_BASE_URL}/api/v1/import/parse-simple/${sessionId}`,
        {
          headers: {
            'Authorization': `Bearer ${authToken}`
          },
          timeout: 10000
        }
      );

      const pollTime = Date.now() - pollStartTime;
      const session = response.data.data;
      
      // Track progress updates
      const progressUpdate = {
        attempt: attempts,
        timestamp: Date.now(),
        status: session.status,
        progress: session.progress,
        currentStep: session.currentStep,
        pollTime,
        elapsedTime: Date.now() - testResults.parsingStartTime
      };
      
      testResults.progressUpdates.push(progressUpdate);

      // Log significant changes
      if (session.progress !== lastProgress || session.status !== lastStatus) {
        log('INFO', 'Progress update', progressUpdate);
        lastProgress = session.progress;
        lastStatus = session.status;
        stuckCounter = 0; // Reset stuck counter on progress
      } else {
        stuckCounter++;
        if (stuckCounter >= STUCK_THRESHOLD) {
          log('WARN', 'Process appears stuck', {
            stuckCounter,
            lastProgress,
            lastStatus,
            elapsedTime: Date.now() - testResults.parsingStartTime
          });
        }
      }

      // Check for completion
      if (session.status === 'completed') {
        testResults.parsingEndTime = Date.now();
        testResults.success = true;
        log('INFO', 'Parsing completed successfully', {
          totalAttempts: attempts,
          totalTime: testResults.parsingEndTime - testResults.parsingStartTime,
          result: session.result
        });
        return session;
      }

      // Check for errors
      if (session.status === 'failed' || session.error) {
        throw new Error(`Parsing failed: ${session.error || 'Unknown error'}`);
      }

      // Check for timeout conditions
      const elapsedTime = Date.now() - testResults.parsingStartTime;
      if (elapsedTime > CONFIG.TIMEOUT_THRESHOLD && session.progress <= 40) {
        log('WARN', 'Potential timeout detected', {
          elapsedTime,
          progress: session.progress,
          status: session.status
        });
        testResults.timeouts.push({
          timestamp: Date.now(),
          elapsedTime,
          progress: session.progress,
          status: session.status
        });
      }

    } catch (error) {
      log('ERROR', 'Polling error', {
        attempt: attempts,
        error: error.message,
        pollTime: Date.now() - pollStartTime
      });
      
      // Don't fail immediately on network errors, but track them
      if (attempts > 3) {
        throw error;
      }
    }

    // Wait before next poll
    await new Promise(resolve => setTimeout(resolve, CONFIG.POLLING_INTERVAL));
  }

  // If we reach here, we've timed out
  throw new Error(`Polling timed out after ${CONFIG.MAX_POLLING_TIME / 1000} seconds`);
}

// Performance analysis function
function analyzePerformance() {
  const totalTime = Date.now() - testResults.startTime;
  testResults.totalTime = totalTime;

  const metrics = {
    totalTime,
    authTime: testResults.authTime,
    uploadTime: testResults.uploadTime,
    parsingTime: testResults.parsingEndTime ? 
      (testResults.parsingEndTime - testResults.parsingStartTime) : null,
    progressUpdateCount: testResults.progressUpdates.length,
    errorCount: testResults.errors.length,
    timeoutCount: testResults.timeouts.length,
    averagePollTime: testResults.progressUpdates.length > 0 ?
      testResults.progressUpdates.reduce((sum, update) => sum + (update.pollTime || 0), 0) / testResults.progressUpdates.length : 0
  };

  testResults.performanceMetrics = metrics;
  
  log('INFO', 'Performance Analysis', metrics);
  return metrics;
}

// Generate test report
function generateReport() {
  const report = {
    testConfiguration: CONFIG,
    testResults,
    summary: {
      success: testResults.success,
      totalTime: testResults.totalTime,
      keyFindings: []
    }
  };

  // Add key findings based on results
  if (testResults.timeouts.length > 0) {
    report.summary.keyFindings.push('Timeout conditions detected during parsing');
  }
  
  if (testResults.errors.length > 0) {
    report.summary.keyFindings.push(`${testResults.errors.length} errors encountered`);
  }
  
  if (testResults.progressUpdates.length > 0) {
    const stuckUpdates = testResults.progressUpdates.filter((update, index) => {
      if (index === 0) return false;
      const prev = testResults.progressUpdates[index - 1];
      return update.progress === prev.progress && update.status === prev.status;
    });
    
    if (stuckUpdates.length > 5) {
      report.summary.keyFindings.push('Process appeared stuck with no progress updates');
    }
  }

  // Save report to file
  const reportPath = path.join(__dirname, `pdf-import-test-report-${Date.now()}.json`);
  fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
  
  log('INFO', 'Test report generated', { reportPath });
  return report;
}

// Main test execution
async function runTest() {
  log('INFO', 'Starting comprehensive PDF import test', CONFIG);

  try {
    // Step 1: Authentication
    const authToken = await authenticate();

    // Step 2: PDF Upload
    const uploadResult = await uploadPDF(authToken);

    // Step 3: Status Polling
    const finalResult = await pollStatus(uploadResult.sessionId, authToken);

    // Step 4: Performance Analysis
    analyzePerformance();

    log('INFO', 'Test completed successfully', {
      sessionId: uploadResult.sessionId,
      finalStatus: finalResult.status,
      totalTime: testResults.totalTime
    });

  } catch (error) {
    log('ERROR', 'Test failed', { error: error.message });
    testResults.success = false;
  } finally {
    // Always generate report
    const report = generateReport();
    
    // Print summary
    console.log('\n=== TEST SUMMARY ===');
    console.log(`Success: ${testResults.success}`);
    console.log(`Total Time: ${testResults.totalTime}ms`);
    console.log(`Errors: ${testResults.errors.length}`);
    console.log(`Timeouts: ${testResults.timeouts.length}`);
    console.log(`Progress Updates: ${testResults.progressUpdates.length}`);
    
    if (testResults.success) {
      console.log('✅ PDF import test PASSED');
      process.exit(0);
    } else {
      console.log('❌ PDF import test FAILED');
      process.exit(1);
    }
  }
}

// Run the test if this file is executed directly
if (require.main === module) {
  runTest().catch(error => {
    console.error('Unhandled test error:', error);
    process.exit(1);
  });
}

module.exports = { runTest, CONFIG, testResults };
