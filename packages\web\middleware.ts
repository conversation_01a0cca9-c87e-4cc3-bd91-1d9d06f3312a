import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';

// Routes that require authentication
const protectedRoutes = [
  '/dashboard',
  '/plan',
  '/settings',
  '/trips',
  '/import',
  '/upload',
  '/ai-chat',
];

// Routes that should redirect to dashboard if authenticated
const authRoutes = ['/login', '/signup'];

export function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;

  // Get auth token from cookies
  const authCookie = request.cookies.get('auth-storage');
  let isAuthenticated = false;

  if (authCookie) {
    try {
      const authData = JSON.parse(authCookie.value);

      // Handle both direct structure and Zustand persist structure
      const authState = authData?.state || authData;
      isAuthenticated = authState?.isAuthenticated || false;

      // Debug logging in development
      if (process.env.NODE_ENV === 'development') {
        console.log('🔍 Middleware auth check:', {
          pathname,
          hasAuthCookie: !!authCookie,
          isAuthenticated,
          hasAccessToken: !!authState?.accessToken,
          cookieValue: authCookie.value.substring(0, 100) + '...',
          authDataKeys: Object.keys(authData || {}),
          authStateKeys: Object.keys(authState || {}),
          authStateIsAuthenticated: authState?.isAuthenticated,
          authStateAccessToken: authState?.accessToken ? 'present' : 'missing'
        });
      }
    } catch (error) {
      // Invalid cookie data
      isAuthenticated = false;
      if (process.env.NODE_ENV === 'development') {
        console.log('❌ Middleware auth cookie parse error:', error);
        console.log('❌ Cookie value:', authCookie.value);
      }
    }
  } else if (process.env.NODE_ENV === 'development') {
    console.log('🔍 Middleware: No auth cookie found for', pathname);
  }
  
  // Check if the current route is protected
  const isProtectedRoute = protectedRoutes.some(route => 
    pathname.startsWith(route)
  );
  
  // Check if the current route is an auth route
  const isAuthRoute = authRoutes.includes(pathname);
  
  // Redirect logic
  if (!isAuthenticated && isProtectedRoute) {
    // Redirect to login with return URL
    if (process.env.NODE_ENV === 'development') {
      console.log('🔒 Redirecting to login:', { pathname, isAuthenticated, isProtectedRoute });
    }
    const loginUrl = new URL('/login', request.url);
    loginUrl.searchParams.set('redirect', pathname);
    return NextResponse.redirect(loginUrl);
  }

  if (isAuthenticated && isAuthRoute) {
    // Redirect to dashboard if trying to access auth pages while logged in
    if (process.env.NODE_ENV === 'development') {
      console.log('🏠 Redirecting to dashboard:', { pathname, isAuthenticated, isAuthRoute });
    }
    return NextResponse.redirect(new URL('/dashboard', request.url));
  }
  
  return NextResponse.next();
}

// Configure which routes the middleware should run on
export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public files
     */
    '/((?!api|_next/static|_next/image|favicon.ico|images|icons).*)',
  ],
};