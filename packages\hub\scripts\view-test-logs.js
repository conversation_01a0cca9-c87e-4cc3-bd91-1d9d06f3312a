#!/usr/bin/env node

/**
 * View test logs from AI import tests
 * 
 * Usage:
 *   npm run test:logs
 *   npm run test:logs -- --errors  # View only errors
 *   npm run test:logs -- --tail 50  # View last 50 lines
 */

const fs = require('fs');
const path = require('path');

const logsDir = path.join(__dirname, '..', 'logs');

if (!fs.existsSync(logsDir)) {
  console.log('No logs directory found. Run tests first with: pnpm test:ai-import');
  process.exit(1);
}

// Get command line arguments
const args = process.argv.slice(2);
const showErrors = args.includes('--errors');
const tailIndex = args.indexOf('--tail');
const tailLines = tailIndex !== -1 && args[tailIndex + 1] ? parseInt(args[tailIndex + 1]) : null;

// Find the most recent log files
const files = fs.readdirSync(logsDir)
  .filter(f => f.startsWith(showErrors ? 'test-errors-' : 'test-run-'))
  .sort()
  .reverse();

if (files.length === 0) {
  console.log('No log files found. Run tests first with: pnpm test:ai-import');
  process.exit(1);
}

const latestLog = path.join(logsDir, files[0]);
console.log(`\nViewing: ${files[0]}\n`);

// Read and display the log
const content = fs.readFileSync(latestLog, 'utf8');
const lines = content.split('\n');

if (tailLines && tailLines > 0) {
  const startLine = Math.max(0, lines.length - tailLines);
  console.log(lines.slice(startLine).join('\n'));
} else {
  console.log(content);
}

// Show summary
console.log('\n=== Log Summary ===');
console.log(`Total lines: ${lines.length}`);
console.log(`File size: ${(fs.statSync(latestLog).size / 1024).toFixed(2)} KB`);

if (!showErrors) {
  // Count test results
  const testStarts = lines.filter(l => l.includes('Test started')).length;
  const testCompletes = lines.filter(l => l.includes('Test completed')).length;
  const errors = lines.filter(l => l.includes('ERROR:')).length;
  
  console.log(`Tests started: ${testStarts}`);
  console.log(`Tests completed: ${testCompletes}`);
  console.log(`Errors logged: ${errors}`);
}

console.log('\nOther log files:');
files.slice(1, 5).forEach(f => console.log(`  - ${f}`));

if (files.length > 5) {
  console.log(`  ... and ${files.length - 5} more`);
}