const { config } = require('dotenv');
const path = require('path');

// Load .env.test
config({ path: path.resolve(__dirname, '.env.test') });

const { Redis } = require('@upstash/redis');

async function testWithRetries() {
  const configs = [
    {
      name: 'REST URL/Token',
      url: process.env.UPSTASH_REDIS_REST_URL,
      token: process.env.UPSTASH_REDIS_REST_TOKEN
    },
    {
      name: 'Non-REST URL/Token',
      url: process.env.UPSTASH_REDIS_URL,
      token: process.env.UPSTASH_REDIS_TOKEN
    }
  ];
  
  for (const config of configs) {
    console.log(`\nTesting with ${config.name}:`);
    console.log('URL:', config.url);
    console.log('Token:', config.token?.substring(0, 20) + '...');
    
    try {
      const redis = new Redis({
        url: config.url,
        token: config.token,
        retry: {
          retries: 3,
          backoff: (attempt) => Math.min(attempt * 100, 3000)
        },
        automaticDeserialization: true
      });
      
      const result = await redis.ping();
      console.log('✅ Success:', result);
    } catch (error) {
      console.error('❌ Error:', error.message);
      if (error.cause) {
        console.error('Cause:', error.cause);
      }
    }
  }
}

testWithRetries();