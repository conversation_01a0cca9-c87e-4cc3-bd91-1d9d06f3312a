import { describe, it, expect, beforeAll, afterAll, afterEach } from 'vitest';
import { BaseIntegrationTest } from '../utils/base-integration-test';

/**
 * Integration test for Activity Management Flow
 * Tests the complete CRUD operations for activities within trips
 * 
 * Critical Flow #3: Activity Management
 * - User adds activities to trips
 * - User updates activity details
 * - User reorders activities within days
 * - User moves activities between days
 * - User deletes activities
 */
class ActivityManagementFlowTest extends BaseIntegrationTest {

  async setupTripForTesting() {
    const trip = await this.createTestTrip({
      ...this.fixtures.trips.basic,
      title: this.cleanData.testTripTitle(),
      startDate: this.cleanData.futureDate(10),
      endDate: this.cleanData.futureDate(15)
    });
    
    return trip.body.data.trip;
  }

  async testCreateActivity() {
    console.log('🎯 Testing activity creation...');
    
    const trip = await this.setupTripForTesting();
    
    const activityData = {
      ...this.fixtures.activities.museum,
      title: `Test Activity ${Date.now()}`,
      day: 1,
      position: 0
    };

    const response = await this.addActivityToTrip(trip.id, activityData);
    
    this.expectResponsePattern(response, this.fixtures.expectedResponses.activityAdded);
    
    const activity = response.body.data.activity;
    expect(activity.title).toBe(activityData.title);
    expect(activity.type).toBe(activityData.type);
    expect(activity.day).toBe(activityData.day);
    expect(activity.tripId).toBe(trip.id);
    
    console.log(`✅ Activity created: ${activity.title} (ID: ${activity.id})`);
    return { trip, activity };
  }

  async testUpdateActivity() {
    console.log('✏️ Testing activity updates...');
    
    const { trip, activity } = await this.testCreateActivity();
    
    const updateData = {
      title: 'Updated ' + activity.title,
      description: 'Updated description for testing',
      startTime: '10:30',
      endTime: '12:30',
      location: 'Updated Location',
      type: 'dining' as const
    };

    const updateResponse = await this.server.put(
      `/api/v1/trips/${trip.id}/activities/${activity.id}`, 
      updateData
    );
    expect(updateResponse.status).toBe(200);
    
    const updatedActivity = updateResponse.body.data.activity;
    expect(updatedActivity.title).toBe(updateData.title);
    expect(updatedActivity.description).toBe(updateData.description);
    expect(updatedActivity.startTime).toBe(updateData.startTime);
    expect(updatedActivity.type).toBe(updateData.type);
    
    console.log('✅ Activity updated successfully');
    return { trip, activity: updatedActivity };
  }

  async testListActivities() {
    console.log('📋 Testing activity listing...');
    
    const trip = await this.setupTripForTesting();
    
    // Create multiple activities
    const activities = await Promise.all([
      this.addActivityToTrip(trip.id, { ...this.fixtures.activities.museum, day: 1, position: 0 }),
      this.addActivityToTrip(trip.id, { ...this.fixtures.activities.restaurant, day: 1, position: 1 }),
      this.addActivityToTrip(trip.id, { ...this.fixtures.activities.transport, day: 2, position: 0 })
    ]);

    const listResponse = await this.server.get(`/api/v1/trips/${trip.id}/activities`);
    expect(listResponse.status).toBe(200);
    
    const activitiesList = listResponse.body.data.activities;
    expect(Array.isArray(activitiesList)).toBe(true);
    expect(activitiesList.length).toBe(3);
    
    // Verify all our test activities are in the list
    const testActivityIds = activities.map(a => a.body.data.activity.id);
    const listedActivityIds = activitiesList.map((a: any) => a.id);
    
    testActivityIds.forEach(id => {
      expect(listedActivityIds).toContain(id);
    });
    
    console.log(`✅ Listed ${activitiesList.length} activities`);
    return { trip, activities: activitiesList };
  }

  async testActivityReordering() {
    console.log('🔄 Testing activity reordering...');
    
    const trip = await this.setupTripForTesting();
    
    // Create activities in specific order
    const activity1 = await this.addActivityToTrip(trip.id, { 
      ...this.fixtures.activities.museum, 
      title: 'First Activity',
      day: 1, 
      position: 0 
    });
    
    const activity2 = await this.addActivityToTrip(trip.id, { 
      ...this.fixtures.activities.restaurant, 
      title: 'Second Activity',
      day: 1, 
      position: 1 
    });
    
    const activity3 = await this.addActivityToTrip(trip.id, { 
      ...this.fixtures.activities.transport, 
      title: 'Third Activity',
      day: 1, 
      position: 2 
    });

    // Reorder activities: move first to last position
    const reorderResponse = await this.server.put(
      `/api/v1/trips/${trip.id}/activities/reorder`,
      {
        activities: [
          { id: activity2.body.data.activity.id, position: 0 },
          { id: activity3.body.data.activity.id, position: 1 },
          { id: activity1.body.data.activity.id, position: 2 }
        ]
      }
    );
    
    if (reorderResponse.status === 200) {
      // Verify new order
      const listResponse = await this.server.get(`/api/v1/trips/${trip.id}/activities?day=1`);
      const activities = listResponse.body.data.activities;
      
      expect(activities[0].title).toBe('Second Activity');
      expect(activities[1].title).toBe('Third Activity');
      expect(activities[2].title).toBe('First Activity');
      
      console.log('✅ Activity reordering working correctly');
    } else {
      console.log('ℹ️ Activity reordering not yet implemented');
    }
  }

  async testActivityMoveBetweenDays() {
    console.log('📅 Testing activity movement between days...');
    
    const trip = await this.setupTripForTesting();
    
    // Create activity on day 1
    const activity = await this.addActivityToTrip(trip.id, {
      ...this.fixtures.activities.museum,
      day: 1,
      position: 0
    });

    // Move to day 2
    const moveResponse = await this.server.put(
      `/api/v1/trips/${trip.id}/activities/${activity.body.data.activity.id}`,
      { day: 2, position: 0 }
    );
    
    expect(moveResponse.status).toBe(200);
    
    const movedActivity = moveResponse.body.data.activity;
    expect(movedActivity.day).toBe(2);
    
    console.log('✅ Activity moved between days successfully');
  }

  async testDeleteActivity() {
    console.log('🗑️ Testing activity deletion...');
    
    const { trip, activity } = await this.testCreateActivity();
    
    // Delete the activity
    const deleteResponse = await this.server.delete(
      `/api/v1/trips/${trip.id}/activities/${activity.id}`
    );
    expect(deleteResponse.status).toBe(200);
    
    // Verify activity is gone
    const getResponse = await this.server.get(
      `/api/v1/trips/${trip.id}/activities/${activity.id}`
    );
    expect(getResponse.status).toBe(404);
    
    console.log('✅ Activity deleted successfully');
  }

  async testActivityFiltering() {
    console.log('🔍 Testing activity filtering...');
    
    const trip = await this.setupTripForTesting();
    
    // Create activities on different days and types
    await this.addActivityToTrip(trip.id, { ...this.fixtures.activities.museum, day: 1, type: 'sightseeing' });
    await this.addActivityToTrip(trip.id, { ...this.fixtures.activities.restaurant, day: 1, type: 'dining' });
    await this.addActivityToTrip(trip.id, { ...this.fixtures.activities.transport, day: 2, type: 'transport' });

    // Filter by day
    const day1Response = await this.server.get(`/api/v1/trips/${trip.id}/activities?day=1`);
    expect(day1Response.status).toBe(200);
    expect(day1Response.body.data.activities).toHaveLength(2);

    // Filter by type
    const diningResponse = await this.server.get(`/api/v1/trips/${trip.id}/activities?type=dining`);
    expect(diningResponse.status).toBe(200);
    expect(diningResponse.body.data.activities).toHaveLength(1);
    expect(diningResponse.body.data.activities[0].type).toBe('dining');
    
    console.log('✅ Activity filtering working correctly');
  }

  async testActivityValidation() {
    console.log('✅ Testing activity data validation...');
    
    const trip = await this.setupTripForTesting();
    
    // Test missing required fields
    const invalidActivity1 = await this.server.post(`/api/v1/trips/${trip.id}/activities`, {
      // Missing title
      description: 'Test description'
    });
    expect(invalidActivity1.status).toBe(400);
    
    // Test invalid time format
    const invalidActivity2 = await this.server.post(`/api/v1/trips/${trip.id}/activities`, {
      title: 'Test Activity',
      startTime: '25:00', // Invalid hour
      endTime: '26:00'
    });
    expect(invalidActivity2.status).toBe(400);
    
    // Test invalid activity type
    const invalidActivity3 = await this.server.post(`/api/v1/trips/${trip.id}/activities`, {
      title: 'Test Activity',
      type: 'invalid_type'
    });
    expect(invalidActivity3.status).toBe(400);
    
    // Test end time before start time
    const invalidActivity4 = await this.server.post(`/api/v1/trips/${trip.id}/activities`, {
      title: 'Test Activity',
      startTime: '14:00',
      endTime: '13:00' // Before start time
    });
    expect(invalidActivity4.status).toBe(400);
    
    console.log('✅ Validation working correctly');
  }

  async testAuthenticationRequired() {
    console.log('🔒 Testing activity authentication requirements...');
    
    const activityData = this.fixtures.activities.museum;
    
    await this.testAuthRequired('/api/v1/trips/fake-id/activities', 'POST', activityData);
    await this.testAuthRequired('/api/v1/trips/fake-id/activities', 'GET');
    await this.testAuthRequired('/api/v1/trips/fake-id/activities/fake-id', 'GET');
    await this.testAuthRequired('/api/v1/trips/fake-id/activities/fake-id', 'PUT', activityData);
    await this.testAuthRequired('/api/v1/trips/fake-id/activities/fake-id', 'DELETE');
    
    console.log('✅ Authentication properly enforced');
  }

  async testBulkActivityOperations() {
    console.log('📦 Testing bulk activity operations...');
    
    const trip = await this.setupTripForTesting();
    
    // Create multiple activities in bulk
    const bulkData = {
      activities: [
        { ...this.fixtures.activities.museum, title: 'Bulk Activity 1', day: 1, position: 0 },
        { ...this.fixtures.activities.restaurant, title: 'Bulk Activity 2', day: 1, position: 1 },
        { ...this.fixtures.activities.transport, title: 'Bulk Activity 3', day: 2, position: 0 }
      ]
    };

    const bulkCreateResponse = await this.server.post(
      `/api/v1/trips/${trip.id}/activities/bulk`, 
      bulkData
    );
    
    if (bulkCreateResponse.status === 200) {
      const createdActivities = bulkCreateResponse.body.data.activities;
      expect(createdActivities).toHaveLength(3);
      
      // Track for cleanup
      createdActivities.forEach((activity: any) => {
        this.cleanup.trackTestData('activities', activity.id);
      });
      
      console.log('✅ Bulk activity creation supported');
    } else {
      console.log('ℹ️ Bulk activity operations not yet implemented');
    }
  }
}

describe('Activity Management Flow - Integration Tests', () => {
  let test: ActivityManagementFlowTest;

  beforeAll(async () => {
    test = new ActivityManagementFlowTest();
    await test.setupTest();
  });

  afterAll(async () => {
    await test.teardownTest();
  });

  afterEach(async () => {
    await test.cleanupBetweenTests();
  });

  it('should create a new activity in a trip', async () => {
    const { activity } = await test.testCreateActivity();
    expect(activity.id).toBeDefined();
    expect(activity.title).toBeDefined();
    expect(activity.tripId).toBeDefined();
  });

  it('should update activity details', async () => {
    const { activity } = await test.testUpdateActivity();
    expect(activity.title).toContain('Updated');
  });

  it('should list activities for a trip', async () => {
    const { activities } = await test.testListActivities();
    expect(activities.length).toBe(3);
  });

  it('should reorder activities within a day', async () => {
    await test.testActivityReordering();
  });

  it('should move activities between days', async () => {
    await test.testActivityMoveBetweenDays();
  });

  it('should delete activities', async () => {
    await test.testDeleteActivity();
  });

  it('should filter activities by day and type', async () => {
    await test.testActivityFiltering();
  });

  it('should validate activity data', async () => {
    await test.testActivityValidation();
  });

  it('should require authentication for all activity operations', async () => {
    await test.testAuthenticationRequired();
  });

  it('should support bulk activity operations', async () => {
    await test.testBulkActivityOperations();
  });

  it('should maintain activity order and position consistency', async () => {
    const trip = await test.setupTripForTesting();
    
    // Create activities with specific positions
    const activities = await Promise.all([
      test.addActivityToTrip(trip.id, { ...test.fixtures.activities.museum, day: 1, position: 0 }),
      test.addActivityToTrip(trip.id, { ...test.fixtures.activities.restaurant, day: 1, position: 1 }),
      test.addActivityToTrip(trip.id, { ...test.fixtures.activities.transport, day: 1, position: 2 })
    ]);

    // Verify positions are maintained
    const listResponse = await test.server.get(`/api/v1/trips/${trip.id}/activities?day=1`);
    const sortedActivities = listResponse.body.data.activities;
    
    expect(sortedActivities[0].position).toBe(0);
    expect(sortedActivities[1].position).toBe(1);
    expect(sortedActivities[2].position).toBe(2);
    
    console.log('✅ Activity positioning maintained correctly');
  });

  it('should handle activity time conflicts gracefully', async () => {
    const trip = await test.setupTripForTesting();
    
    // Create first activity
    await test.addActivityToTrip(trip.id, {
      ...test.fixtures.activities.museum,
      startTime: '10:00',
      endTime: '12:00',
      day: 1
    });

    // Try to create overlapping activity
    const conflictResponse = await test.server.post(`/api/v1/trips/${trip.id}/activities`, {
      title: 'Conflicting Activity',
      startTime: '11:00',
      endTime: '13:00',
      day: 1
    });

    // Should either warn or allow with notification
    expect([200, 409]).toContain(conflictResponse.status);
    
    if (conflictResponse.status === 409) {
      expect(conflictResponse.body.error).toContain('conflict');
      console.log('✅ Time conflict detection working');
    } else {
      console.log('ℹ️ Time conflicts allowed (warning-based approach)');
    }
  });
});