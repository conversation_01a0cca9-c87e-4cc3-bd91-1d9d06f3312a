import { StateStorage } from 'zustand/middleware';
import Cookies from 'js-cookie';

export const cookieStorage: StateStorage = {
  getItem: (name: string): string | null => {
    const value = Cookies.get(name);
    return value ?? null;
  },
  setItem: (name: string, value: string): void => {
    // Set cookie with options for security
    Cookies.set(name, value, {
      expires: 7, // 7 days
      sameSite: 'lax',
      secure: process.env.NODE_ENV === 'production',
    });
  },
  removeItem: (name: string): void => {
    Cookies.remove(name);
  },
};