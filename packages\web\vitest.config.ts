import { defineConfig } from 'vitest/config';
import react from '@vitejs/plugin-react';
import tsconfigPaths from 'vite-tsconfig-paths';
import { resolve } from 'path';

export default defineConfig({
  plugins: [react(), tsconfigPaths()] as any,
  resolve: {
    alias: {
      '@': resolve(__dirname, './src'),
      '@/lib': resolve(__dirname, './src/lib'),
      '@/components': resolve(__dirname, './src/components'),
      '@/hooks': resolve(__dirname, './src/hooks'),
      '@/stores': resolve(__dirname, './stores'),
      '@/app': resolve(__dirname, './app'),
      '@/contexts': resolve(__dirname, './src/contexts'),
      '@test': resolve(__dirname, './test'),
    },
  },
  test: {
    globals: true,
    environment: 'happy-dom',
    setupFiles: ['./test/setup.ts'],
    css: true,
    reporters: process.env.CI === 'true' 
      ? ['github-actions', 'json'] 
      : ['verbose'],
    exclude: [
      '**/node_modules/**',
      '**/dist/**',
      '**/cypress/**',
      '**/.{idea,git,cache,output,temp}/**',
      '**/{karma,rollup,webpack,vite,vitest,jest,ava,babel,nyc,cypress,tsup,build}.config.*',
      '**/tests/**', // Exclude all files in tests directory (Playwright tests)
      '**/*.spec.ts', // Exclude all spec.ts files  
      '**/*.spec.tsx', // Exclude all spec.tsx files
      '**/*.spec.js', // Exclude all spec.js files
      '**/*.e2e.ts', // Exclude e2e test files
      '**/*.e2e.tsx', // Exclude e2e test files
    ],
    // Performance optimizations
    pool: 'forks',
    poolOptions: {
      forks: {
        // Dynamic maxForks: CI can override with VITEST_MAX_FORKS env var
        maxForks: process.env.VITEST_MAX_FORKS 
          ? parseInt(process.env.VITEST_MAX_FORKS, 10) 
          : (process.env.CI === 'true' ? 1 : 2),
        minForks: 1,
      },
    },
    isolate: true, // Isolate tests for better cleanup
    sequence: {
      hooks: 'stack', // Run hooks in reverse order for proper cleanup
    },
    testTimeout: 30000,
    hookTimeout: 30000,
    coverage: {
      reporter: ['text', 'json', 'html', 'lcov'],
      provider: 'v8',
      all: false,
      include: ['src/**/*.{ts,tsx}'],
      exclude: [
        'src/**/*.d.ts',
        'src/**/*.stories.{ts,tsx}',
        'src/**/*.test.{ts,tsx}',
        'src/**/__tests__/**',
        'src/types/**',
        'node_modules/**',
      ],
      thresholds: {
        branches: 80,
        functions: 80,
        lines: 80,
        statements: 80,
      },
    },
  },
});