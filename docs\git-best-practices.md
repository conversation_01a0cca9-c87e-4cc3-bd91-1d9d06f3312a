# Git Best Practices for TravelViz

## 🧹 Keeping GitHub Clean

### What Should Be Committed

✅ **DO Commit:**
- Source code (`.ts`, `.tsx`, `.js`, `.jsx`)
- Configuration files (`package.json`, `tsconfig.json`, etc.)
- Documentation (`.md` files)
- Example environment files (`.env.example`)
- CI/CD workflows (`.github/workflows/`)
- Static assets (images, icons in `public/`)
- Database schemas and migrations
- Test files

❌ **DON'T Commit:**
- `node_modules/` directories
- Build outputs (`dist/`, `.next/`, `build/`)
- Environment files with secrets (`.env`, `.env.local`)
- Log files (`*.log`)
- TypeScript build info (`*.tsbuildinfo`)
- IDE-specific files (`.vscode/`, `.idea/`)
- OS files (`.DS_Store`, `Thumbs.db`)
- Temporary files

### Pre-Commit Checklist

Before committing, always check:

```bash
# 1. Check what files are staged
git status

# 2. Review changes
git diff --cached

# 3. Make sure no secrets are included
grep -r "password\|secret\|key\|token" --include="*.ts" --include="*.js" .

# 4. Run linting and type checking
pnpm lint
pnpm type-check

# 5. Run tests
pnpm test
```

### Environment Files Strategy

**✅ Safe to commit:**
- `.env.example` - Template with placeholder values
- `.env.template` - Another template format

**❌ Never commit:**
- `.env` - Contains real secrets
- `.env.local` - Local development overrides
- `.env.production` - Production secrets

### Git Commands for Clean Repository

```bash
# Check repository status
git status

# See what's ignored
git status --ignored

# Remove accidentally committed files
git rm --cached filename
git rm -r --cached directory/

# Clean untracked files (be careful!)
git clean -fd

# Remove files from history (if secrets were committed)
git filter-branch --force --index-filter \
  'git rm --cached --ignore-unmatch path/to/secret/file' \
  --prune-empty --tag-name-filter cat -- --all
```

### Branch Strategy

```bash
# Main branches
main        # Production-ready code
develop     # Integration branch for features

# Feature branches
feature/ai-import-engine
feature/timeline-component
feature/price-tracking

# Release branches
release/v1.0.0

# Hotfix branches
hotfix/critical-bug-fix
```

### Commit Message Convention

We use [Conventional Commits](https://www.conventionalcommits.org/):

```bash
# Format
<type>[optional scope]: <description>

# Examples
feat(hub): add AI conversation parsing endpoint
fix(web): resolve timeline drag-drop issue
docs: update setup instructions
chore: update dependencies
test(shared): add utility function tests
```

**Types:**
- `feat`: New feature
- `fix`: Bug fix
- `docs`: Documentation changes
- `style`: Code style changes (formatting, etc.)
- `refactor`: Code refactoring
- `test`: Adding or updating tests
- `chore`: Maintenance tasks

### Repository Size Management

```bash
# Check repository size
du -sh .git

# Check large files
git rev-list --objects --all | git cat-file --batch-check='%(objecttype) %(objectname) %(objectsize) %(rest)' | sed -n 's/^blob //p' | sort --numeric-sort --key=2 | tail -10

# Remove large files from history
git filter-branch --tree-filter 'rm -f path/to/large/file' HEAD
```

### Security Checklist

Before pushing:

- [ ] No API keys or secrets in code
- [ ] No hardcoded passwords
- [ ] No database connection strings
- [ ] No private keys or certificates
- [ ] Environment files are in `.gitignore`
- [ ] Sensitive data is in environment variables

### Automated Checks

Our repository has automated checks:

1. **Pre-commit hooks** (Husky):
   - Lint staged files
   - Format code with Prettier
   - Type checking

2. **CI/CD Pipeline**:
   - Build verification
   - Test execution
   - Security scanning

3. **Branch Protection**:
   - Require PR reviews
   - Require status checks
   - No direct pushes to main

### Quick Commands

```bash
# Stage only source files
git add packages/*/src/

# Commit with conventional format
git commit -m "feat(hub): add health check endpoint"

# Push feature branch
git push origin feature/your-feature-name

# Clean up merged branches
git branch --merged | grep -v main | xargs git branch -d
```

### Emergency: Removing Secrets

If you accidentally commit secrets:

```bash
# 1. Remove from working directory
rm .env

# 2. Remove from Git cache
git rm --cached .env

# 3. Commit the removal
git commit -m "chore: remove accidentally committed secrets"

# 4. If already pushed, contact team immediately
# 5. Rotate all exposed secrets
# 6. Consider using git filter-branch for history cleanup
```

---

**Remember: A clean repository is a professional repository! 🧹✨**
