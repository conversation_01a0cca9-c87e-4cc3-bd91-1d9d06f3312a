import rateLimit from 'express-rate-limit';
import { createErrorResponse, HTTP_STATUS } from '@travelviz/shared';
import { Request } from 'express';
import crypto from 'crypto';
import { logger } from '../utils/logger';

/**
 * Secure key generator that uses multiple factors to identify clients
 * This prevents rate limit bypass via header spoofing
 */
function getSecureRateLimitKey(req: Request): string {
  const factors = [
    // Use authenticated user ID if available
    (req as Request & { user?: { id?: string } }).user?.id,
    // Use session ID if available
    (req as Request & { sessionID?: string }).sessionID,
    // Use API key if provided
    req.headers['x-api-key'],
    // Use a combination of headers that are harder to spoof together
    req.headers['user-agent'],
    req.headers['accept-language'],
    req.headers['accept-encoding'],
    // Fall back to IP (but don't rely solely on it)
    req.ip || req.socket.remoteAddress
  ].filter(Boolean);

  // Create a hash of all factors to ensure consistent key length
  const combinedFactors = factors.join('|');
  return crypto.createHash('sha256').update(combinedFactors).digest('hex');
}

// Default rate limit configuration for authentication endpoints
export const authRateLimit = rateLimit({
  windowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS || '900000'), // 15 minutes
  max: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS || '5'), // 5 requests per window for security
  message: 'Too many authentication attempts, please try again later',
  standardHeaders: true, // Return rate limit info in headers
  legacyHeaders: false, // Disable X-RateLimit headers
  keyGenerator: getSecureRateLimitKey, // Use secure key generator
  handler: (req, res) => {
    logger.warn('Auth rate limit exceeded', { ip: req.ip });
    res.status(HTTP_STATUS.TOO_MANY_REQUESTS).json(
      createErrorResponse(
        'Rate limit exceeded',
        'Too many authentication attempts, please try again later'
      )
    );
  },
  skip: () => {
    // Only skip rate limiting in test environment when explicitly testing
    return process.env.NODE_ENV === 'test' && process.env.SKIP_RATE_LIMIT === 'true';
  }
});

// Login-specific rate limit (strict for security)
export const loginRateLimit = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 5, // 5 attempts per 15 minutes for security
  message: 'Too many login attempts, please try again later',
  standardHeaders: true,
  legacyHeaders: false,
  keyGenerator: getSecureRateLimitKey, // Use secure key generator
  handler: (req, res) => {
    logger.warn('Login rate limit exceeded', { ip: req.ip });
    res.status(HTTP_STATUS.TOO_MANY_REQUESTS).json(
      createErrorResponse(
        'Rate limit exceeded',
        'Too many login attempts, please try again later'
      )
    );
  },
  skip: () => {
    // Only skip rate limiting in test environment when explicitly testing
    return process.env.NODE_ENV === 'test' && process.env.SKIP_RATE_LIMIT === 'true';
  }
});

// Import endpoint rate limit - expensive AI operations
export const importRateLimit = rateLimit({
  windowMs: 60 * 60 * 1000, // 1 hour
  max: 10, // 10 imports per hour
  message: 'Import limit reached. Please try again in an hour.',
  standardHeaders: true,
  legacyHeaders: false,
  keyGenerator: getSecureRateLimitKey, // Use secure key generator
  handler: (req, res) => {
    logger.warn('Import rate limit exceeded', { ip: req.ip });
    res.status(HTTP_STATUS.TOO_MANY_REQUESTS).json(
      createErrorResponse(
        'Rate limit exceeded',
        'You have reached the import limit (10 per hour). This limit helps us manage AI processing costs. Please try again later.'
      )
    );
  },
  skip: () => {
    return process.env.NODE_ENV === 'test' && process.env.SKIP_RATE_LIMIT === 'true';
  }
});

// General API rate limit (more permissive)
export const apiRateLimit = rateLimit({
  windowMs: 60 * 1000, // 1 minute
  max: 100, // 100 requests per minute
  message: 'Too many requests, please try again later',
  standardHeaders: true,
  legacyHeaders: false,
  keyGenerator: getSecureRateLimitKey, // Use secure key generator
  handler: (req, res) => {
    logger.warn('API rate limit exceeded', {
      ip: req.ip,
      path: req.path,
      method: req.method,
      userAgent: req.headers['user-agent'],
      userId: (req as any).user?.id
    });
    res.status(HTTP_STATUS.TOO_MANY_REQUESTS).json(
      createErrorResponse(
        'Rate limit exceeded',
        'Too many requests, please try again later'
      )
    );
  },
  skip: () => {
    // Skip rate limiting in test environment
    return process.env.NODE_ENV === 'test';
  }
});

// Strict rate limit for sensitive operations (password reset, etc.)
export const strictRateLimit = rateLimit({
  windowMs: 60 * 60 * 1000, // 1 hour
  max: 3, // 3 requests per hour
  message: 'Too many requests for this operation, please try again later',
  standardHeaders: true,
  legacyHeaders: false,
  keyGenerator: getSecureRateLimitKey, // Use secure key generator
  handler: (req, res) => {
    logger.warn('Request blocked - strict rate limit triggered');
    res.status(HTTP_STATUS.TOO_MANY_REQUESTS).json(
      createErrorResponse(
        'Rate limit exceeded',
        'Too many requests for this operation, please try again later'
      )
    );
  },
  skip: () => {
    // Skip rate limiting in test environment
    const shouldSkip = process.env.NODE_ENV === 'test';
    logger.debug('strictRateLimit skip check', { 
      NODE_ENV: process.env.NODE_ENV, 
      shouldSkip 
    });
    return shouldSkip;
  }
});

// Trips-specific rate limit (more permissive for dashboard loading)
export const tripsRateLimit = rateLimit({
  windowMs: 60 * 1000, // 1 minute
  max: 200, // 200 requests per minute (more permissive for dashboard)
  message: 'Too many trip requests, please try again later',
  standardHeaders: true,
  legacyHeaders: false,
  keyGenerator: getSecureRateLimitKey,
  handler: (req, res) => {
    logger.warn('Trips rate limit exceeded', {
      ip: req.ip,
      path: req.path,
      method: req.method,
      userAgent: req.headers['user-agent'],
      userId: (req as any).user?.id
    });
    res.status(HTTP_STATUS.TOO_MANY_REQUESTS).json(
      createErrorResponse(
        'Rate limit exceeded',
        'Too many trip requests, please try again later'
      )
    );
  },
  skip: () => {
    return process.env.NODE_ENV === 'test';
  }
});

// Create custom rate limiter
export function createRateLimit(options: {
  windowMs: number;
  max: number;
  message?: string;
}) {
  return rateLimit({
    windowMs: options.windowMs,
    max: options.max,
    message: options.message || 'Too many requests',
    standardHeaders: true,
    legacyHeaders: false,
    keyGenerator: getSecureRateLimitKey, // Use secure key generator
    handler: (req, res) => {
      res.status(HTTP_STATUS.TOO_MANY_REQUESTS).json(
        createErrorResponse(
          'Rate limit exceeded',
          options.message || 'Too many requests'
        )
      );
    },
    skip: () => {
      return process.env.NODE_ENV === 'test';
    }
  });
}