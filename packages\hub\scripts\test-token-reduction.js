#!/usr/bin/env node

/**
 * Test script to compare token usage between original and compressed prompts
 */

const axios = require('axios');
const path = require('path');
require('dotenv').config({ path: path.join(__dirname, '../.env.local') });

const TEST_TEXT = `
Day 1: Paris Adventure
- 9:00 AM: Arrive at Charles de Gaulle Airport
- 11:00 AM: Check into Le Meurice Hotel (€500/night)
- 2:00 PM: Visit the Louvre Museum
- 7:00 PM: Dinner cruise on the Seine (€120)

Day 2: Exploring Paris
- 9:00 AM: Eiffel Tower tour
- 1:00 PM: Lunch at a local bistro (€35)
- 3:00 PM: Shopping on Champs-Élysées
- 8:00 PM: Opera at Palais Garnier (€85)
`;

// Original verbose prompt (400+ tokens)
const ORIGINAL_PROMPT = `You are TravelViz Parser, an expert AI system specialized in extracting structured travel itinerary data from conversational text.

<parsing_rules>
1. Extract all travel-related information including dates, locations, activities, and prices
2. Convert relative dates to absolute dates when possible
3. Infer activity types based on keywords
4. Handle various formats (markdown, bullets, numbered lists)
5. Preserve important notes and recommendations
</parsing_rules>

<structured_thinking>
1. Identify the conversation style and format
2. Extract trip metadata (destination, dates)
3. Identify activity blocks by day/date
4. Extract temporal markers and convert to structured times
5. Categorize activities by type
6. Enrich with location data for geocoding
</structured_thinking>

Extract travel itinerary from this chatgpt conversation and return structured JSON.

Text to parse:
${TEST_TEXT}

Return ONLY valid JSON with this exact structure:
{
  "title": "Trip title (create a descriptive title based on destination and duration)",
  "destination": "Primary destination city/country (extract from activities if not explicitly stated)",
  "startDate": "YYYY-MM-DD",
  "endDate": "YYYY-MM-DD",
  "activities": [
    {
      "title": "Activity name",
      "type": "flight|accommodation|transport|dining|activity|shopping|car_rental|tour|sightseeing|entertainment|other",
      "startTime": "HH:MM",
      "location": "Specific place, City",
      "price": 100,
      "currency": "USD",
      "day": 1,
      "description": "Optional details",
      "notes": "Important tips"
    }
  ]
}`;

// Compressed prompt (200 tokens)
const COMPRESSED_PROMPT = `Extract trip from chatgpt text. Return JSON only.

Text:
${TEST_TEXT}

JSON:
{
  "title": "descriptive title",
  "destination": "main location",
  "startDate": "YYYY-MM-DD",
  "endDate": "YYYY-MM-DD",
  "activities": [{
    "title": "activity name",
    "type": "flight|accommodation|transport|dining|activity|shopping|car_rental|tour|sightseeing|entertainment|other",
    "startTime": "HH:MM",
    "location": "place",
    "price": number,
    "currency": "USD/EUR/etc",
    "day": 1
  }]
}`;

async function testPrompt(prompt, promptName) {
  const apiKey = process.env.OPENROUTER_API_KEY;
  
  if (!apiKey || !apiKey.startsWith('sk-or-')) {
    throw new Error('Valid OPENROUTER_API_KEY not found');
  }
  
  console.log(`\n📝 Testing ${promptName}`);
  console.log(`Prompt length: ${prompt.length} characters`);
  
  try {
    const startTime = Date.now();
    
    const response = await axios.post(
      'https://openrouter.ai/api/v1/chat/completions',
      {
        model: 'deepseek/deepseek-chat-v3-0324:free',
        messages: [{ role: 'user', content: prompt }],
        temperature: 0.1,
        max_tokens: 2000
      },
      {
        headers: {
          'Authorization': `Bearer ${apiKey}`,
          'Content-Type': 'application/json'
        },
        timeout: 30000
      }
    );
    
    const duration = Date.now() - startTime;
    const usage = response.data.usage;
    const content = response.data.choices[0]?.message?.content;
    
    console.log(`✅ Success in ${duration}ms`);
    console.log(`📊 Tokens - Prompt: ${usage.prompt_tokens}, Response: ${usage.completion_tokens}, Total: ${usage.total_tokens}`);
    
    // Try to parse JSON
    try {
      let jsonString = content;
      if (content.includes('```')) {
        jsonString = content.replace(/```json\s*/, '').replace(/```\s*$/, '');
      }
      const parsed = JSON.parse(jsonString.match(/\{[\s\S]*\}/)[0]);
      console.log(`✅ Valid JSON with ${parsed.activities?.length || 0} activities`);
    } catch (e) {
      console.log(`❌ Failed to parse JSON: ${e.message}`);
    }
    
    return {
      promptTokens: usage.prompt_tokens,
      totalTokens: usage.total_tokens,
      success: true
    };
    
  } catch (error) {
    console.log(`❌ Error: ${error.message}`);
    return { success: false };
  }
}

async function main() {
  console.log('🧪 Token Reduction Test');
  console.log('=' .repeat(50));
  
  // Test original prompt
  const originalResult = await testPrompt(ORIGINAL_PROMPT, 'Original Prompt');
  
  // Test compressed prompt
  const compressedResult = await testPrompt(COMPRESSED_PROMPT, 'Compressed Prompt');
  
  // Calculate savings
  if (originalResult.success && compressedResult.success) {
    console.log('\n📈 Token Reduction Summary:');
    console.log('=' .repeat(50));
    
    const promptReduction = originalResult.promptTokens - compressedResult.promptTokens;
    const percentReduction = Math.round((promptReduction / originalResult.promptTokens) * 100);
    
    console.log(`Original prompt tokens: ${originalResult.promptTokens}`);
    console.log(`Compressed prompt tokens: ${compressedResult.promptTokens}`);
    console.log(`Tokens saved: ${promptReduction} (${percentReduction}% reduction)`);
    
    // Cost calculation (using OpenRouter pricing)
    const costPer1K = 0.14; // DeepSeek free tier = $0, but showing potential savings
    const monthlySavings = (promptReduction / 1000) * costPer1K * 1000; // Assume 1000 requests/month
    
    console.log(`\n💰 Potential monthly savings: $${monthlySavings.toFixed(2)} (at 1000 requests/month)`);
  }
}

main().catch(console.error);