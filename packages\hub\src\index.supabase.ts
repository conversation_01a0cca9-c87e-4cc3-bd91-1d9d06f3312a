import express, { Application } from 'express';
import cors from 'cors';
import helmet from 'helmet';
import morgan from 'morgan';
import { createSuccessResponse } from '@travelviz/shared';

// Import both auth route implementations
// import authRoutes from './routes/auth.routes'; // Custom JWT implementation (uncomment if needed)
import supabaseAuthRoutes from './routes/supabase-auth.routes'; // Supabase JWT implementation

import tripsRoutes from './routes/trips.routes';
import { errorHandler, notFoundHandler } from './middleware/error-handler.middleware';
import { apiRateLimit } from './middleware/rate-limit.middleware';
import { requestIdMiddleware } from './middleware/request-id.middleware';
import { logger } from './utils/logger';

// Load environment variables with proper precedence
import { loadEnvironment } from './utils/env-loader';
loadEnvironment();

const app: Application = express();
const PORT = process.env.PORT || 3001;

// Security middleware
app.use(helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'"],
      scriptSrc: ["'self'"],
      imgSrc: ["'self'", "data:", "https:"],
      connectSrc: ["'self'"],
      fontSrc: ["'self'"],
      objectSrc: ["'none'"],
      mediaSrc: ["'self'"],
      frameSrc: ["'none'"],
    },
  },
  crossOriginEmbedderPolicy: false,
}));

// CORS configuration
app.use(cors({
  origin: process.env.FRONTEND_URL || 'http://localhost:3000',
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization'],
  exposedHeaders: ['X-RateLimit-Limit', 'X-RateLimit-Remaining', 'X-RateLimit-Reset'],
}));

// Request ID middleware (should be early in the chain)
app.use(requestIdMiddleware);

// Request logging with custom format
app.use(morgan((tokens, req, res) => {
  const responseTime = tokens['response-time'](req, res);
  logger.logRequest(req, res, parseFloat(responseTime || '0'));
  return [
    tokens.method(req, res),
    tokens.url(req, res),
    tokens.status(req, res),
    responseTime ? `${responseTime}ms` : '-',
    req.id || '-'
  ].join(' ');
}));

// Body parsing
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Custom security headers
app.use((_req, res, next) => {
  res.setHeader('X-Content-Type-Options', 'nosniff');
  res.setHeader('X-Frame-Options', 'DENY');
  res.setHeader('X-XSS-Protection', '1; mode=block');
  res.setHeader('Referrer-Policy', 'strict-origin-when-cross-origin');
  res.setHeader('Permissions-Policy', 'camera=(), microphone=(), geolocation=()');
  next();
});

// Health check endpoint
app.get('/health', (_req, res) => {
  res.json(createSuccessResponse({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    service: 'travelviz-hub',
  }));
});

// API routes with global rate limiting
app.get('/api/test', apiRateLimit, (_req, res) => {
  res.json(createSuccessResponse({
    message: 'TravelViz Hub API is working!',
    timestamp: new Date().toISOString(),
  }));
});

// API v1 routes

// OPTION 1: Use Supabase JWT authentication (RECOMMENDED)
// This uses Supabase's native JWT tokens which are more secure and scalable
app.use('/api/v1/auth', supabaseAuthRoutes);

// OPTION 2: Use custom JWT implementation (CURRENT)
// Uncomment this and comment out the line above to use the existing custom JWT implementation
// app.use('/api/v1/auth', authRoutes);

// Note: If you switch to Supabase auth, you'll also need to update the trips routes
// to use the Supabase auth middleware instead of the custom one
app.use('/api/v1/trips', tripsRoutes);

// 404 handler
app.use('*', notFoundHandler);

// Global error handler (must be last)
app.use(errorHandler);

// Start server
const server = app.listen(PORT, () => {
  const actualPort = (server.address() as { port: number })?.port || PORT;
  logger.info(`🚀 TravelViz Hub API server running on port ${actualPort}`);
  logger.info(`📍 Health check: http://localhost:${actualPort}/health`);
  logger.info(`🔧 Environment: ${process.env.NODE_ENV || 'development'}`);
  logger.info(`🌐 CORS enabled for: ${process.env.FRONTEND_URL || 'http://localhost:3000'}`);
  
  // Log which auth implementation is being used
  logger.info(`🔐 Auth implementation: Supabase JWT`);
});

export default app;