# TravelViz Documentation

Welcome to the TravelViz documentation! This directory contains essential guides for developing, deploying, and maintaining the TravelViz platform.

## 📚 Documentation Structure

### Getting Started

- [Setup Guide](./setup.md) - Complete development environment setup
- [Architecture Overview](./architecture.md) - System design and component relationships
- [Development Guide](./development.md) - Development workflow and best practices

### Development & Testing

- [Testing Guide](./testing.md) - Testing strategy and implementation
- [Git Best Practices](./git-best-practices.md) - Version control guidelines
- [CI Optimization](./CI_OPTIMIZATION.md) - CI/CD pipeline optimization

### Deployment & Security

- [Deployment Guide](./deployment.md) - Production deployment instructions
- [Security Guide](./security.md) - Security best practices and implementation
- [Environment Variables](./ENVIRONMENT_VARIABLES.md) - Configuration reference

### API Documentation

- [Hub API Documentation](../packages/hub/API_DOCUMENTATION.md) - Comprehensive API reference

### Additional Resources

- [Project README](../README.md) - Main project overview
- [CLAUDE.md](../CLAUDE.md) - AI assistant instructions
- [Product Requirements](../travelviz-prd-v2.md) - Detailed PRD
- [Documentation Status](./DOCUMENTATION_STATUS.md) - Documentation progress tracking

### Archived Documentation

- [Historical Documents](./archive/) - MVP planning and completed implementation docs

## 🚧 Documentation In Progress

The following guides are planned for future updates:

- Feature documentation (AI import, timeline/maps, sharing, etc.)
- Integration guides (Supabase, OpenRouter, Mapbox, etc.)
- Operations guides (monitoring, performance, troubleshooting)

## 🚀 Quick Start

1. **Setup Development Environment**

   ```bash
   # Clone and install dependencies
   git clone <repository-url>
   cd travelviz
   pnpm install

   # Copy environment files
   cp packages/hub/.env.example packages/hub/.env
   cp packages/web/.env.example packages/web/.env

   # Start development servers
   pnpm dev
   ```

2. **Access Applications**
   - Web Frontend: http://localhost:3000
   - Hub API: http://localhost:3001
   - API Health Check: http://localhost:3001/health

3. **Verify Setup**
   - Check that both services start without errors
   - Verify TypeScript compilation
   - Test API connectivity between web and hub

## 📋 Project Structure

```
travelviz/
├── packages/
│   ├── hub/          # Express.js backend API
│   ├── web/          # Next.js frontend application
│   ├── mobile/       # React Native mobile app
│   └── shared/       # Shared types and utilities
├── docs/             # Documentation
├── .github/          # GitHub workflows and templates
└── package.json      # Monorepo configuration
```

## 🔧 Key Technologies

- **Backend**: Node.js, Express.js, TypeScript
- **Frontend**: Next.js 14, React, TypeScript, Tailwind CSS
- **Mobile**: React Native, Expo
- **Database**: PostgreSQL (Supabase)
- **Maps**: Mapbox GL JS
- **AI**: OpenRouter (Claude, GPT-4, Gemini)
- **Caching**: Redis (Upstash)
- **Deployment**: Render (Hub), Vercel (Web)

## 🤝 Contributing

Please read our [Development Guide](./development.md) for information on our development process, coding standards, and how to submit pull requests.

## 📞 Support

- **Issues**: Create a GitHub issue for bugs or feature requests
- **Documentation**: Check the relevant guide in this docs directory
- **Architecture Questions**: See [Architecture Overview](./architecture.md)

---

_Last updated: December 2024_
