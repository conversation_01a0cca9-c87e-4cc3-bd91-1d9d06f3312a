import dotenv from 'dotenv';
dotenv.config({ path: '.env.local' });

import { getSupabaseClient } from '../src/lib/supabase';

async function verifyTables() {
  console.log('🔍 Verifying database tables...\n');
  
  const supabase = getSupabaseClient();
  const tables = [
    'profiles', 
    'trips', 
    'activities', 
    'trip_shares', 
    'affiliate_clicks',
    'auth_failed_attempts', 
    'auth_account_lockouts'
  ];
  
  const results: {table: string, exists: boolean, count?: number}[] = [];
  
  for (const table of tables) {
    try {
      const { count, error } = await supabase
        .from(table)
        .select('*', { count: 'exact', head: true });
      
      if (error) {
        results.push({ table, exists: false });
        console.log(`❌ Table '${table}' - NOT FOUND`);
      } else {
        results.push({ table, exists: true, count: count || 0 });
        console.log(`✅ Table '${table}' - EXISTS (${count || 0} rows)`);
      }
    } catch (err) {
      results.push({ table, exists: false });
      console.log(`❌ Table '${table}' - ERROR`);
    }
  }
  
  console.log('\n📊 Summary:');
  const existingTables = results.filter(r => r.exists).length;
  console.log(`   ${existingTables}/${tables.length} tables exist`);
  
  if (existingTables < tables.length) {
    console.log('\n⚠️  Missing tables need to be created manually in Supabase dashboard');
    console.log('   Go to: https://app.supabase.com/project/ixjtoikbbjzfegmqdlmc/editor');
  }
}

verifyTables().catch(console.error);