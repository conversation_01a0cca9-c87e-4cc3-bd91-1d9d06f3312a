// User-friendly error message mapping
// Maps backend error messages to user-friendly ones to prevent information leakage

export const ERROR_MESSAGES = {
  // Authentication errors
  'Invalid login credentials': 'Invalid email or password. Please try again.',
  'Invalid email or password': 'Invalid email or password. Please try again.',
  'Email not confirmed': 'Please check your email and confirm your account before logging in.',
  'User with this email already exists': 'An account with this email already exists. Try logging in instead.',
  'Session expired': 'Your session has expired. Please log in again.',
  'Not authenticated': 'Please log in to continue.',
  'Account temporarily locked': 'Account temporarily locked due to multiple failed attempts. Please try again later.',
  
  // Validation errors
  'Invalid email address': 'Please enter a valid email address.',
  'Password must be at least 8 characters': 'Password must be at least 8 characters long.',
  'End date must be after start date': 'End date must be after the start date.',
  'End time must be after start time': 'End time must be after the start time.',
  
  // Resource errors
  'Trip not found': 'Trip not found. It may have been deleted or you may not have access to it.',
  'Activity not found': 'Activity not found. It may have been deleted.',
  'User not found': 'User account not found.',
  'unauthorized': 'You don\'t have permission to perform this action.',
  
  // Network errors
  'Network error': 'Unable to connect to the server. Please check your internet connection and try again.',
  'Request timeout': 'The request took too long to complete. Please try again.',
  'Service temporarily unavailable': 'Service is temporarily unavailable. Please try again in a few minutes.',
  
  // Database errors
  'Database error': 'A technical error occurred. Please try again later.',
  'Connection failed': 'Unable to connect to the service. Please try again later.',
  
  // Rate limiting
  'Too many requests': 'Too many requests. Please wait a moment before trying again.',
  'Rate limit exceeded': 'Too many requests. Please wait a moment before trying again.',
  
  // File/Upload errors
  'File too large': 'The file is too large. Please choose a smaller file.',
  'Invalid file type': 'This file type is not supported. Please choose a different file.',
  
  // Generic fallbacks
  'Internal server error': 'A technical error occurred. Please try again later.',
  'Bad request': 'Invalid request. Please check your input and try again.',
  'Forbidden': 'You don\'t have permission to perform this action.',
} as const;

/**
 * Maps a raw API error message to a user-friendly message
 * @param rawMessage The raw error message from the API
 * @returns A user-friendly error message
 */
export function mapErrorMessage(rawMessage: string): string {
  if (!rawMessage) {
    return 'An unexpected error occurred. Please try again.';
  }

  // Normalize the message for matching
  const normalizedMessage = rawMessage.trim();
  
  // Check for exact matches first
  if (normalizedMessage in ERROR_MESSAGES) {
    return ERROR_MESSAGES[normalizedMessage as keyof typeof ERROR_MESSAGES];
  }
  
  // Check for partial matches (case-insensitive)
  const lowerMessage = normalizedMessage.toLowerCase();
  
  for (const [key, friendlyMessage] of Object.entries(ERROR_MESSAGES)) {
    if (lowerMessage.includes(key.toLowerCase()) || key.toLowerCase().includes(lowerMessage)) {
      return friendlyMessage;
    }
  }
  
  // Check for common patterns
  if (lowerMessage.includes('password')) {
    return 'Invalid email or password. Please try again.';
  }
  
  if (lowerMessage.includes('email')) {
    return 'Please check your email address and try again.';
  }
  
  if (lowerMessage.includes('token') || lowerMessage.includes('session')) {
    return 'Your session has expired. Please log in again.';
  }
  
  if (lowerMessage.includes('permission') || lowerMessage.includes('unauthorized') || lowerMessage.includes('forbidden')) {
    return 'You don\'t have permission to perform this action.';
  }
  
  if (lowerMessage.includes('not found') || lowerMessage.includes('404')) {
    return 'The requested resource was not found.';
  }
  
  if (lowerMessage.includes('connection') || lowerMessage.includes('network')) {
    return 'Unable to connect to the server. Please check your internet connection and try again.';
  }
  
  if (lowerMessage.includes('timeout')) {
    return 'The request took too long to complete. Please try again.';
  }
  
  if (lowerMessage.includes('rate limit') || lowerMessage.includes('too many')) {
    return 'Too many requests. Please wait a moment before trying again.';
  }
  
  // For any unmatched errors, return a generic friendly message
  // This prevents leaking implementation details
  return 'An unexpected error occurred. Please try again later.';
}

/**
 * Maps HTTP status codes to user-friendly messages
 * @param statusCode The HTTP status code
 * @returns A user-friendly error message
 */
export function mapStatusCodeToMessage(statusCode: number): string {
  switch (statusCode) {
    case 400:
      return 'Invalid request. Please check your input and try again.';
    case 401:
      return 'Please log in to continue.';
    case 403:
      return 'You don\'t have permission to perform this action.';
    case 404:
      return 'The requested resource was not found.';
    case 408:
      return 'The request took too long to complete. Please try again.';
    case 429:
      return 'Too many requests. Please wait a moment before trying again.';
    case 500:
      return 'A technical error occurred. Please try again later.';
    case 502:
    case 503:
    case 504:
      return 'Service is temporarily unavailable. Please try again in a few minutes.';
    default:
      return 'An unexpected error occurred. Please try again later.';
  }
}