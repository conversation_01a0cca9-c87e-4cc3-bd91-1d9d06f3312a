/**
 * Trips API Tests
 * 
 * Perfect protected endpoint test that demonstrates:
 * - Reusing saved authentication tokens
 * - Real server connection (requires `pnpm run dev` hub running)
 * - Proper error handling and detailed reporting
 * - Clean setup and teardown
 */

const ApiTestClient = require('./utils/api-client');
const apiConfig = require('./api.config');

// Test results tracking
const testResults = {
  passed: 0,
  failed: 0,
  tests: []
};

// Test data storage
let testTripId = null;
let testActivityId = null;

function logTest(name, result, details = {}) {
  const passed = result.success || result === true;
  testResults.tests.push({ name, passed, details });
  
  if (passed) {
    testResults.passed++;
    console.log(`✅ ${name}`);
  } else {
    testResults.failed++;
    console.log(`❌ ${name}`, details.error || result.error || '');
  }
}

async function ensureAuthenticated(client) {
  console.log('\n🔐 Ensuring Authentication...');
  
  try {
    // Check if we have a valid token
    let token = client.getStoredToken();
    
    if (!token) {
      console.log('   No valid token found, logging in...');
      const loginResponse = await client.login(
        apiConfig.testData.user.email,
        apiConfig.testData.user.password
      );
      
      if (!loginResponse.success) {
        throw new Error(`Login failed: ${loginResponse.error || 'Unknown error'}`);
      }
      
      token = client.getStoredToken();
    }
    
    // Verify token works by getting user info
    const meResponse = await client.getMe();
    if (!meResponse.success) {
      throw new Error(`Token validation failed: ${meResponse.error || 'Unknown error'}`);
    }
    
    logTest('Authentication ready', true, { 
      hasToken: !!token,
      userEmail: meResponse.data.data.email 
    });
    
    return true;
  } catch (error) {
    logTest('Authentication setup', false, { error: error.message });
    return false;
  }
}

async function testCreateTrip(client) {
  console.log('\n✈️ Testing Create Trip...');
  
  try {
    const tripData = {
      ...apiConfig.testData.trip,
      title: `${apiConfig.testData.trip.title} - ${Date.now()}` // Make unique
    };
    
    const response = await client.createTrip(tripData);
    
    logTest('Create trip', response, response);
    
    if (response.success) {
      testTripId = response.data.data.id;
      
      // Verify trip data
      const trip = response.data.data;
      logTest('Trip title matches', trip.title === tripData.title, {
        expected: tripData.title,
        actual: trip.title
      });
      
      logTest('Trip destination matches', trip.destination === tripData.destination, {
        expected: tripData.destination,
        actual: trip.destination
      });
      
      console.log(`   Created trip ID: ${testTripId}`);
      return true;
    }
    
    return false;
  } catch (error) {
    logTest('Create Trip Test', false, { error: error.message });
    return false;
  }
}

async function testGetTrips(client) {
  console.log('\n📋 Testing Get Trips List...');
  
  try {
    const response = await client.getTrips();
    
    logTest('Get trips list', response, response);
    
    if (response.success) {
      const trips = response.data.data.trips || response.data.data;
      logTest('Trips list is array', Array.isArray(trips), { 
        type: typeof trips,
        isArray: Array.isArray(trips)
      });
      
      if (Array.isArray(trips)) {
        logTest('Trips list contains our test trip', 
          trips.some(trip => trip.id === testTripId), {
          testTripId,
          tripIds: trips.map(t => t.id)
        });
        
        console.log(`   Found ${trips.length} trips`);
      }
      return true;
    }
    
    return false;
  } catch (error) {
    logTest('Get Trips Test', false, { error: error.message });
    return false;
  }
}

async function testGetTripsWithPagination(client) {
  console.log('\n📄 Testing Get Trips with Pagination...');
  
  try {
    const response = await client.getTrips({ page: 1, limit: 5 });
    
    logTest('Get trips with pagination', response, response);
    
    if (response.success) {
      const trips = response.data.data.trips || response.data.data;
      logTest('Paginated trips list is array', Array.isArray(trips), {
        type: typeof trips,
        isArray: Array.isArray(trips)
      });
      
      if (Array.isArray(trips)) {
        logTest('Pagination limit respected', trips.length <= 5, {
          count: trips.length,
          limit: 5
        });
        
        console.log(`   Found ${trips.length} trips (paginated)`);
      }
      return true;
    }
    
    return false;
  } catch (error) {
    logTest('Get Trips Pagination Test', false, { error: error.message });
    return false;
  }
}

async function testGetSingleTrip(client) {
  console.log('\n🎯 Testing Get Single Trip...');
  
  if (!testTripId) {
    logTest('Get single trip', false, { error: 'No test trip ID available' });
    return false;
  }
  
  try {
    const response = await client.getTrip(testTripId);
    
    logTest('Get single trip', response, response);
    
    if (response.success) {
      const trip = response.data.data;
      logTest('Trip ID matches', trip.id === testTripId, {
        expected: testTripId,
        actual: trip.id
      });
      
      logTest('Trip has required fields', 
        trip.title && trip.destination && trip.startDate && trip.endDate, {
        hasTitle: !!trip.title,
        hasDestination: !!trip.destination,
        hasStartDate: !!trip.startDate,
        hasEndDate: !!trip.endDate
      });
      
      return true;
    }
    
    return false;
  } catch (error) {
    logTest('Get Single Trip Test', false, { error: error.message });
    return false;
  }
}

async function testUpdateTrip(client) {
  console.log('\n✏️ Testing Update Trip...');
  
  if (!testTripId) {
    logTest('Update trip', false, { error: 'No test trip ID available' });
    return false;
  }
  
  try {
    const updates = {
      title: `${apiConfig.testData.trip.title} - Updated`,
      description: 'Updated via API test'
    };
    
    const response = await client.updateTrip(testTripId, updates);
    
    logTest('Update trip', response, response);
    
    if (response.success) {
      const trip = response.data.data;
      logTest('Trip title updated', trip.title === updates.title, {
        expected: updates.title,
        actual: trip.title
      });
      
      logTest('Trip description updated', trip.description === updates.description, {
        expected: updates.description,
        actual: trip.description
      });
      
      return true;
    }
    
    return false;
  } catch (error) {
    logTest('Update Trip Test', false, { error: error.message });
    return false;
  }
}

async function testCreateActivity(client) {
  console.log('\n🎯 Testing Create Activity...');
  
  if (!testTripId) {
    logTest('Create activity', false, { error: 'No test trip ID available' });
    return false;
  }
  
  try {
    const activityData = {
      ...apiConfig.testData.activity,
      title: `${apiConfig.testData.activity.title} - ${Date.now()}` // Make unique
    };
    
    const response = await client.createActivity(testTripId, activityData);
    
    logTest('Create activity', response, response);
    
    if (response.success) {
      testActivityId = response.data.data.id;
      
      const activity = response.data.data;
      logTest('Activity title matches', activity.title === activityData.title, {
        expected: activityData.title,
        actual: activity.title
      });
      
      console.log(`   Created activity ID: ${testActivityId}`);
      return true;
    }
    
    return false;
  } catch (error) {
    logTest('Create Activity Test', false, { error: error.message });
    return false;
  }
}

async function testUnauthorizedAccess(client) {
  console.log('\n🚫 Testing Unauthorized Access...');
  
  try {
    // Save current token
    const originalTokens = { ...client.tokens };
    
    // Clear tokens to simulate unauthorized access
    client.clearTokens();
    
    // Try to access protected endpoint
    const response = await client.getTrips();
    
    logTest('Unauthorized access fails', !response.success, response);
    logTest('Returns 401 status', response.status === 401, {
      expectedStatus: 401,
      actualStatus: response.status
    });
    
    // Restore tokens
    client.tokens = originalTokens;
    client.saveTokens(originalTokens.accessToken, originalTokens.refreshToken, originalTokens.user);
    
    return true;
  } catch (error) {
    logTest('Unauthorized Access Test', false, { error: error.message });
    return false;
  }
}

async function testCleanup(client) {
  console.log('\n🧹 Testing Cleanup...');
  
  let cleanupSuccess = true;
  
  try {
    // Delete test activity
    if (testActivityId) {
      const deleteActivityResponse = await client.deleteActivity(testActivityId);
      logTest('Delete test activity', deleteActivityResponse, deleteActivityResponse);
      if (!deleteActivityResponse.success) cleanupSuccess = false;
    }
    
    // Delete test trip
    if (testTripId) {
      const deleteTripResponse = await client.deleteTrip(testTripId);
      logTest('Delete test trip', deleteTripResponse, deleteTripResponse);
      if (!deleteTripResponse.success) cleanupSuccess = false;
    }
    
    logTest('Cleanup completed', cleanupSuccess, {
      deletedActivity: !!testActivityId,
      deletedTrip: !!testTripId
    });
    
    return cleanupSuccess;
  } catch (error) {
    logTest('Cleanup Test', false, { error: error.message });
    return false;
  }
}

async function runTests() {
  console.log('🚀 Starting Trips API Tests');
  console.log('===========================');
  console.log(`API Base URL: ${apiConfig.baseUrl}`);
  console.log(`Test User: ${apiConfig.testData.user.email}\n`);

  const client = new ApiTestClient();
  
  try {
    // Ensure we're authenticated first
    const authReady = await ensureAuthenticated(client);
    if (!authReady) {
      console.log('\n❌ Authentication failed - cannot run protected API tests');
      console.log('Make sure:');
      console.log('1. Hub server is running: pnpm dev');
      console.log('2. Test user exists and credentials are correct');
      console.log('3. Database is accessible');
      return testResults;
    }
    
    // Run all trips API tests in sequence
    const tests = [
      () => testCreateTrip(client),
      () => testGetTrips(client),
      () => testGetTripsWithPagination(client),
      () => testGetSingleTrip(client),
      () => testUpdateTrip(client),
      () => testCreateActivity(client),
      () => testUnauthorizedAccess(client),
      () => testCleanup(client),
    ];

    for (const test of tests) {
      await test();
    }
    
  } catch (error) {
    console.error('\n💥 Test execution error:', error.message);
    testResults.failed++;
  }

  // Summary
  console.log('\n===========================');
  console.log('📊 Trips API Test Results');
  console.log('===========================');
  console.log(`✅ Passed: ${testResults.passed}`);
  console.log(`❌ Failed: ${testResults.failed}`);
  console.log(`📋 Total: ${testResults.tests.length}`);
  console.log(`📈 Success Rate: ${Math.round((testResults.passed / testResults.tests.length) * 100)}%`);

  // Detailed failures
  if (testResults.failed > 0) {
    console.log('\n❌ Failed Tests:');
    testResults.tests
      .filter(t => !t.passed)
      .forEach(t => {
        console.log(`\n- ${t.name}`);
        if (t.details.error) {
          console.log('  Error:', t.details.error);
        }
      });
  }

  return testResults;
}

// Export for use by test runner
module.exports = { runTests };

// Run directly if called as script
if (require.main === module) {
  runTests().catch(console.error);
}