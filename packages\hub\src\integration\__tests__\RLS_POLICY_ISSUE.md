# RLS Policy Issue

## Problem
The trips table has an infinite recursion error in its RLS (Row Level Security) policies.

Error: `infinite recursion detected in policy for relation "trips"` (PostgreSQL error code 42P17)

## Impact
- Cannot query trips table with user_id filter
- Trip listing endpoints fail with 500 error
- Trip counting queries fail

## Solution
This needs to be fixed in the Supabase dashboard:
1. Go to the Supabase dashboard
2. Navigate to the trips table policies
3. Check for circular references between policies
4. Common causes:
   - A policy references itself
   - Two policies reference each other
   - A policy uses a subquery that triggers the same policy

## Test Status
- Simple API tests: ✅ Pass (don't query trips table)
- Full API tests: ❌ Fail (due to RLS policy issue)
- Database connection: ✅ Works (can connect to database)

## Temporary Workaround
Tests could be modified to use service role key which bypasses RLS, but this wouldn't test real user scenarios.