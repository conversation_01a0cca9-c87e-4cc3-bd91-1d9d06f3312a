const path = require('path');
const { loadConfig } = require('vite');

async function testPaths() {
  const config = await loadConfig({ command: 'serve', mode: 'test' }, process.cwd());
  console.log('Vite resolve config:', JSON.stringify(config.resolve, null, 2));
  console.log('__dirname:', __dirname);
  console.log('stores path:', path.resolve(__dirname, './stores'));
  console.log('src path:', path.resolve(__dirname, './src'));
}

testPaths();