# 🚨 STOP: Critical Setup Required Before Phase 1

After thorough analysis, we discovered that **the TDD infrastructure documented does not exist**. This document outlines what MUST be done before starting Phase 1 development.

## 🔴 Current Reality vs. Documentation

### What We Documented

- ✅ TDD workflow with Vitest
- ✅ Claude Code hooks for enforcement
- ✅ 90% coverage requirements
- ✅ Automated test-first development

### What Actually Exists

- ❌ **NO testing in web package** (where Phase 1-3 work happens)
- ❌ **NO Vitest** (only Jest in hub package)
- ❌ **NO test scripts** in package.json
- ❌ **NO MSW** for API mocking
- ❌ **NO test utilities**
- ❌ **NO coverage enforcement**
- ❌ **CI/CD doesn't run tests**

## 📋 Mandatory Setup Before Phase 1

### 1. Choose Testing Framework (DECISION REQUIRED)

**Option A: Vitest** (Recommended - 2 hours setup)

```bash
# Aligns with our TDD docs
# Faster, modern, better DX
# Works great with Vite/Next.js
```

**Option B: Jest** (4 hours setup)

```bash
# Already in hub package
# More configuration needed for Next.js
# Slower but mature
```

### 2. Install Test Infrastructure

```bash
# From project root
pnpm add -D vitest @vitest/ui @vitest/coverage-v8 -w

# Web package (Phase 1-3 work)
cd packages/web
pnpm add -D @testing-library/react @testing-library/user-event @testing-library/jest-dom jsdom

# MSW for API mocking
pnpm add -D msw -w
```

### 3. Create Missing Configuration Files

Required files that don't exist:

- `vitest.config.ts` (root)
- `packages/web/vitest.config.ts`
- `packages/web/test/setup.ts`
- `packages/web/test/utils/render.tsx`
- `packages/web/test/mocks/handlers.ts`

### 4. Add TDD Scripts to package.json

These documented scripts don't exist:

```json
{
  "scripts": {
    "test": "vitest run",
    "test:watch": "vitest",
    "test:coverage": "vitest run --coverage",
    "tdd": "concurrently \"pnpm test:watch\" \"pnpm dev\"",
    "test:create": "node scripts/create-test.js"
  }
}
```

### 5. Fix CI/CD Pipeline

Current CI **doesn't run tests**! Add:

```yaml
- name: Run tests
  run: pnpm test:coverage

- name: Check coverage thresholds
  run: pnpm test:check-coverage
```

### 6. Update Claude Hooks

The hooks reference `pnpm test` which doesn't work correctly. Need to update for chosen test runner.

## ⏱️ Time Estimates

### Setup Tasks (Total: 3-4 hours)

1. **Install dependencies**: 30 min
2. **Create config files**: 45 min
3. **Set up test utilities**: 30 min
4. **Update package.json scripts**: 15 min
5. **Create test generator**: 30 min
6. **Fix CI/CD**: 30 min
7. **Test the setup**: 30 min
8. **Documentation updates**: 30 min

### Team Training (Total: 2 hours)

1. **TDD workflow training**: 1 hour
2. **Tool usage session**: 1 hour

## 🎯 Definition of Done for Setup

Before starting Phase 1, verify:

- [ ] Can run `pnpm test:watch` in web package
- [ ] Can run `pnpm test:create` to generate test
- [ ] Claude hooks block implementation without tests
- [ ] CI/CD runs tests and enforces coverage
- [ ] MSW mocks API calls properly
- [ ] Test utilities work for React components
- [ ] Coverage reports generate correctly
- [ ] All developers trained on TDD workflow

## 🚀 Quick Setup Script

Create `scripts/setup-tdd.sh`:

```bash
#!/bin/bash
echo "🔧 Setting up TDD infrastructure..."

# Install dependencies
pnpm add -D vitest @vitest/ui @vitest/coverage-v8 msw -w
cd packages/web && pnpm add -D @testing-library/react @testing-library/user-event jsdom

# Create directories
mkdir -p packages/web/test/{utils,mocks}
mkdir -p scripts

# Copy config files from TDD_SETUP_REQUIREMENTS.md
# ... (implement file creation)

echo "✅ TDD setup complete!"
echo "Run 'pnpm tdd' to start development"
```

## ⚠️ Risks of Starting Without This Setup

1. **Claude hooks won't work** - No TDD enforcement
2. **Can't write tests** - No test infrastructure
3. **Quality issues** - No coverage tracking
4. **CI/CD failures** - Pipeline not configured
5. **Wasted time** - Retrofitting tests later

## 📞 Action Items for PM

1. **Decision**: Vitest vs Jest (recommend Vitest)
2. **Time allocation**: 3-4 hours for setup
3. **Team training**: Schedule 2-hour session
4. **Update timeline**: Phase 1 starts after setup

## 🎬 Next Steps

1. **Get approval** for framework choice
2. **Run setup** (3-4 hours)
3. **Train team** on TDD workflow
4. **Start Phase 1** with proper TDD

---

**DO NOT START PHASE 1 WITHOUT COMPLETING THIS SETUP**

The TDD documentation is ready, but the infrastructure is not. This setup is mandatory for the quality standards we've committed to.
