import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { Response } from 'express';

// Set up environment before any imports
process.env.OPENROUTER_API_KEY = 'test-api-key';
process.env.UPSTASH_REDIS_REST_URL = 'test-url';
process.env.UPSTASH_REDIS_REST_TOKEN = 'test-token';

// Mock dependencies
const mockAIParserService = {
  createParseSession: vi.fn(),
  getSession: vi.fn(),
  createTripFromParse: vi.fn(),
};

vi.mock('../../services/ai-parser.service', () => ({
  getAIParserService: vi.fn(() => mockAIParserService),
  aiParserService: {
    get instance() {
      return mockAIParserService;
    }
  },
}));
vi.mock('../../config/redis', () => ({
  redis: {
    subscribe: vi.fn().mockResolvedValue(undefined),
    unsubscribe: vi.fn().mockResolvedValue(undefined),
    on: vi.fn(),
    publish: vi.fn().mockResolvedValue(1),
    get: vi.fn().mockResolvedValue(null),
    set: vi.fn().mockResolvedValue('OK'),
  },
  supportsPubSub: vi.fn().mockReturnValue(true),
}));
vi.mock('../../services/redis-connection-pool.service', () => ({
  redisConnectionPool: {
    acquire: vi.fn(),
  },
}));
vi.mock('../../services/cache.service', () => ({
  cacheService: {
    get: vi.fn(),
    set: vi.fn(),
  },
}));
vi.mock('../../utils/logger', () => ({
  logger: {
    error: vi.fn(),
    warn: vi.fn(),
    info: vi.fn(),
    debug: vi.fn(),
  },
}));
vi.mock('../../config/ai.config', () => ({
  validateAIConfig: vi.fn(),
  AI_CONFIG: {
    apiKey: 'test-api-key',
    baseURL: 'https://openrouter.ai/api/v1',
    primaryModel: 'test-model',
  },
}));

import { SSEImportController } from './sse-import.controller';
import { getAIParserService } from '../services/ai-parser.service';
import { redisConnectionPool } from '../services/redis-connection-pool.service';
import { cacheService } from '../services/cache.service';
import { logger } from '../utils/logger';
import { redis } from '../config/redis';
import { createSuccessResponse, createErrorResponse } from '@travelviz/shared';

describe('SSEImportController', () => {
  let controller: SSEImportController;
  let mockReq: any;
  let mockRes: any;

  beforeEach(() => {
    vi.clearAllMocks();
    
    controller = new SSEImportController();

    // Mock request
    mockReq = {
      body: {},
      params: {},
      user: { id: 'user-123' },
      on: vi.fn(),
    };

    // Mock response
    mockRes = {
      status: vi.fn().mockReturnThis(),
      json: vi.fn(),
      writeHead: vi.fn(),
      write: vi.fn(),
      end: vi.fn(),
    };


    // Mock logger
    vi.mocked(logger.info).mockReturnValue(undefined);
    vi.mocked(logger.error).mockReturnValue(undefined);
    vi.mocked(logger.debug).mockReturnValue(undefined);
  });

  describe('parseText', () => {
    it('should create session and return success response', async () => {
      mockReq.body = { text: 'Test conversation', source: 'chatgpt' };
      
      vi.mocked(mockAIParserService.createParseSession).mockResolvedValueOnce('session-123');

      await controller.parseText(mockReq, mockRes);

      expect(mockAIParserService.createParseSession).toHaveBeenCalledWith(
        'Test conversation',
        'chatgpt',
        'user-123'
      );

      expect(mockRes.status).toHaveBeenCalledWith(200);
      expect(mockRes.json).toHaveBeenCalledWith(
        createSuccessResponse({
          sessionId: 'session-123',
          sseUrl: '/api/import/progress/session-123',
          estimatedTime: 15,
        })
      );
    });

    it('should return 401 when user is not authenticated', async () => {
      mockReq.user = null;

      await controller.parseText(mockReq, mockRes);

      expect(mockRes.status).toHaveBeenCalledWith(401);
      expect(mockRes.json).toHaveBeenCalledWith(
        createErrorResponse('Unauthorized')
      );
    });

    it('should return 400 for invalid text content', async () => {
      mockReq.body = { text: null };

      await controller.parseText(mockReq, mockRes);

      expect(mockRes.status).toHaveBeenCalledWith(400);
      expect(mockRes.json).toHaveBeenCalledWith(
        createErrorResponse('Invalid text content')
      );
    });

    it('should use unknown source when not provided', async () => {
      mockReq.body = { text: 'Test' };

      vi.mocked(mockAIParserService.createParseSession).mockResolvedValueOnce('session-123');

      await controller.parseText(mockReq, mockRes);

      expect(mockAIParserService.createParseSession).toHaveBeenCalledWith(
        'Test',
        'unknown',
        'user-123'
      );
    });

    it('should handle service errors', async () => {
      mockReq.body = { text: 'Test' };
      
      vi.mocked(mockAIParserService.createParseSession).mockRejectedValueOnce(
        new Error('Service error')
      );

      await controller.parseText(mockReq, mockRes);

      expect(mockRes.status).toHaveBeenCalledWith(500);
      expect(mockRes.json).toHaveBeenCalledWith(
        createErrorResponse('Service error')
      );
      expect(logger.error).toHaveBeenCalled();
    });
  });

  describe('streamProgress', () => {
    beforeEach(() => {
      mockReq.params = { sessionId: 'session-123' };
    });

    it('should set up SSE headers correctly', async () => {
      vi.mocked(mockAIParserService.getSession).mockResolvedValueOnce({
        id: 'session-123',
        status: 'processing',
        progress: 50,
        currentStep: 'parsing',
      } as any);

      await controller.streamProgress(mockReq, mockRes);

      expect(mockRes.writeHead).toHaveBeenCalledWith(200, {
        'Content-Type': 'text/event-stream',
        'Cache-Control': 'no-cache, no-transform',
        'Connection': 'keep-alive',
        'X-Accel-Buffering': 'no',
        'Access-Control-Allow-Origin': 'http://localhost:3000',
        'Access-Control-Allow-Credentials': 'true',
      });
    });

    it('should send initial connection event', async () => {
      vi.mocked(mockAIParserService.getSession).mockResolvedValueOnce({
        id: 'session-123',
        status: 'processing',
      } as any);

      await controller.streamProgress(mockReq, mockRes);

      expect(mockRes.write).toHaveBeenCalledWith(':ok\n\n');
    });

    it('should send cached progress if available', async () => {
      const cachedProgress = {
        step: 'parsing',
        progress: 30,
        message: 'Parsing...',
      };

      vi.mocked(mockAIParserService.getSession).mockResolvedValueOnce({
        id: 'session-123',
        status: 'processing',
      } as any);
      
      vi.mocked(cacheService.get).mockResolvedValueOnce(cachedProgress);

      await controller.streamProgress(mockReq, mockRes);

      expect(cacheService.get).toHaveBeenCalledWith('progress:session-123');
      expect(mockRes.write).toHaveBeenCalledWith(
        expect.stringContaining('"type":"progress"')
      );
    });

    it('should return 404 for non-existent session', async () => {
      vi.mocked(mockAIParserService.getSession).mockResolvedValueOnce(null);

      await controller.streamProgress(mockReq, mockRes);

      expect(mockRes.status).toHaveBeenCalledWith(404);
      expect(mockRes.json).toHaveBeenCalledWith(
        createErrorResponse('Import session not found')
      );
    });

    it('should subscribe to Redis channel', async () => {
      vi.mocked(mockAIParserService.getSession).mockResolvedValueOnce({
        id: 'session-123',
        status: 'processing',
      } as any);

      await controller.streamProgress(mockReq, mockRes);

      expect(redis.subscribe).toHaveBeenCalledWith('parse:progress:session-123');
    });

    it('should handle progress messages from Redis', async () => {
      vi.mocked(mockAIParserService.getSession).mockResolvedValueOnce({
        id: 'session-123',
        status: 'processing',
      } as any);

      let messageHandler: any;
      vi.mocked(redis.on).mockImplementation((event, handler) => {
        if (event === 'message') {
          messageHandler = handler;
        }
      });

      await controller.streamProgress(mockReq, mockRes);

      // Simulate Redis message
      const progressUpdate = {
        step: 'enhancing',
        progress: 60,
        message: 'Enhancing locations...',
      };
      
      if (messageHandler) {
        messageHandler('parse:progress:session-123', JSON.stringify(progressUpdate));
      }

      expect(mockRes.write).toHaveBeenCalledWith(
        expect.stringContaining('"type":"progress"')
      );
      expect(mockRes.write).toHaveBeenCalledWith(
        expect.stringContaining('"step":"enhancing"')
      );
    });

    it('should close connection on complete event', async () => {
      vi.mocked(mockAIParserService.getSession).mockResolvedValueOnce({
        id: 'session-123',
        status: 'processing',
      } as any);

      let messageHandler: any;
      vi.mocked(redis.on).mockImplementation((event, handler) => {
        if (event === 'message') {
          messageHandler = handler;
        }
      });

      await controller.streamProgress(mockReq, mockRes);

      // Simulate complete message
      if (messageHandler) {
        messageHandler('parse:progress:session-123', JSON.stringify({
          step: 'complete',
          progress: 100,
        }));
      }

      expect(mockRes.write).toHaveBeenCalledWith(
        expect.stringContaining('"type":"complete"')
      );
      expect(redis.unsubscribe).toHaveBeenCalled();
      expect(mockRes.end).toHaveBeenCalled();
    });

    it('should handle error events', async () => {
      vi.mocked(mockAIParserService.getSession).mockResolvedValueOnce({
        id: 'session-123',
        status: 'processing',
      } as any);

      let messageHandler: any;
      vi.mocked(redis.on).mockImplementation((event, handler) => {
        if (event === 'message') {
          messageHandler = handler;
        }
      });

      await controller.streamProgress(mockReq, mockRes);

      // Simulate error message
      if (messageHandler) {
        messageHandler('parse:progress:session-123', JSON.stringify({
          step: 'error',
          message: 'Parse failed',
        }));
      }

      expect(mockRes.write).toHaveBeenCalledWith(
        expect.stringContaining('"type":"error"')
      );
      expect(redis.unsubscribe).toHaveBeenCalled();
      expect(mockRes.end).toHaveBeenCalled();
    });

    it('should set up heartbeat interval', async () => {
      vi.useFakeTimers();
      
      vi.mocked(mockAIParserService.getSession).mockResolvedValueOnce({
        id: 'session-123',
        status: 'processing',
      } as any);

      await controller.streamProgress(mockReq, mockRes);

      // Fast forward 30 seconds
      vi.advanceTimersByTime(30000);

      expect(mockRes.write).toHaveBeenCalledWith(':heartbeat\n\n');

      vi.useRealTimers();
    });

    it('should clean up on client disconnect', async () => {
      vi.mocked(mockAIParserService.getSession).mockResolvedValueOnce({
        id: 'session-123',
        status: 'processing',
      } as any);

      let closeHandler: any;
      mockReq.on.mockImplementation((event, handler) => {
        if (event === 'close') {
          closeHandler = handler;
        }
      });

      await controller.streamProgress(mockReq, mockRes);

      // Simulate client disconnect
      closeHandler();

      expect(redis.unsubscribe).toHaveBeenCalled();
      expect(logger.debug).toHaveBeenCalledWith(
        'SSE client disconnected',
        { sessionId: 'session-123' }
      );
    });
  });

  describe('getSessionStatus', () => {
    it('should return session status', async () => {
      mockReq.params = { sessionId: 'session-123' };
      
      const mockSession = {
        id: 'session-123',
        status: 'complete',
        progress: 100,
        currentStep: 'complete',
        error: undefined,
        startedAt: new Date(),
        completedAt: new Date(),
      };

      vi.mocked(mockAIParserService.getSession).mockResolvedValueOnce(mockSession as any);

      await controller.getSessionStatus(mockReq, mockRes);

      expect(mockRes.status).toHaveBeenCalledWith(200);
      expect(mockRes.json).toHaveBeenCalledWith(
        createSuccessResponse(expect.objectContaining({
          sessionId: 'session-123',
          status: 'complete',
          progress: 100,
        }))
      );
    });

    it('should return 404 for non-existent session', async () => {
      mockReq.params = { sessionId: 'non-existent' };
      
      vi.mocked(mockAIParserService.getSession).mockResolvedValueOnce(null);

      await controller.getSessionStatus(mockReq, mockRes);

      expect(mockRes.status).toHaveBeenCalledWith(404);
      expect(mockRes.json).toHaveBeenCalledWith(
        createErrorResponse('Session not found')
      );
    });
  });

  describe('createTripFromImport', () => {
    it('should create trip from parsed session', async () => {
      mockReq.params = { sessionId: 'session-123' };
      mockReq.body = { edits: { title: 'Updated Title' } };

      vi.mocked(mockAIParserService.createTripFromParse).mockResolvedValueOnce('trip-456');

      await controller.createTripFromImport(mockReq, mockRes);

      expect(mockAIParserService.createTripFromParse).toHaveBeenCalledWith(
        'session-123',
        'user-123',
        { title: 'Updated Title' }
      );

      expect(mockRes.status).toHaveBeenCalledWith(201);
      expect(mockRes.json).toHaveBeenCalledWith(
        createSuccessResponse({
          tripId: 'trip-456',
          redirectUrl: '/trips/trip-456',
        })
      );
    });

    it('should handle service errors', async () => {
      mockReq.params = { sessionId: 'session-123' };
      
      vi.mocked(mockAIParserService.createTripFromParse).mockRejectedValueOnce(
        new Error('Creation failed')
      );

      await controller.createTripFromImport(mockReq, mockRes);

      expect(mockRes.status).toHaveBeenCalledWith(500);
      expect(mockRes.json).toHaveBeenCalledWith(
        createErrorResponse('Creation failed')
      );
    });
  });
});