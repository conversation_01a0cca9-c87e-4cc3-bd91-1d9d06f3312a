/**
 * Test Configuration for TravelViz E2E Testing
 * 
 * This configuration loads environment variables from .env.local files
 * and provides centralized test settings for both API and E2E tests.
 */

const path = require('path');
const fs = require('fs');

// Load environment variables from .env.local files
function loadEnvFile(filePath) {
  if (fs.existsSync(filePath)) {
    const envContent = fs.readFileSync(filePath, 'utf8');
    const envVars = {};
    
    envContent.split('\n').forEach(line => {
      const trimmed = line.trim();
      if (trimmed && !trimmed.startsWith('#')) {
        const [key, ...valueParts] = trimmed.split('=');
        if (key && valueParts.length > 0) {
          envVars[key.trim()] = valueParts.join('=').trim().replace(/^["']|["']$/g, '');
        }
      }
    });
    
    return envVars;
  }
  return {};
}

// Load environment variables from both packages
const hubEnv = loadEnvFile(path.join(__dirname, '../packages/hub/.env.local'));
const webEnv = loadEnvFile(path.join(__dirname, '../packages/web/.env.local'));

// Merge environment variables (web takes precedence for conflicts)
const testEnv = { ...hubEnv, ...webEnv };

// Test configuration
const testConfig = {
  // API Testing Configuration
  api: {
    baseUrl: testEnv.API_BASE_URL || 'http://localhost:3001',
    timeout: 30000,
    retries: 2,
  },
  
  // E2E Testing Configuration
  e2e: {
    baseUrl: testEnv.NEXT_PUBLIC_APP_URL || 'http://localhost:3000',
    headless: process.env.CI === 'true',
    viewport: { width: 1280, height: 720 },
    timeout: 60000,
  },
  
  // Test User Credentials
  auth: {
    testUserEmail: testEnv.TEST_USER_EMAIL || '<EMAIL>',
    testUserPassword: testEnv.TEST_USER_PASSWORD || 'Flaremmk123!',
  },
  
  // Database Configuration
  database: {
    supabaseUrl: testEnv.SUPABASE_URL,
    supabaseAnonKey: testEnv.SUPABASE_ANON_KEY,
    cleanupAfterTests: true,
  },
  
  // Environment Variables for Tests
  env: testEnv,
};

module.exports = testConfig;