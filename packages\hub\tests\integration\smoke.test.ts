import { describe, it, expect } from 'vitest';
import request from 'supertest';
import { app } from '../../app';

describe('Smoke Tests - API Health Check', () => {
  let server: any;

  beforeAll(() => {
    server = app.listen(0);
  });

  afterAll(() => {
    server?.close();
  });

  it('should have health endpoint', async () => {
    const response = await request(server)
      .get('/api/health')
      .expect(200);

    expect(response.body).toHaveProperty('status', 'ok');
  });

  it('should have import routes available', async () => {
    // These should return 401 without auth, not 404
    const routes = [
      '/api/v1/import/parse-simple',
      '/api/v1/import/parse',
      '/api/v1/import/upload'
    ];

    for (const route of routes) {
      const response = await request(server)
        .post(route)
        .send({});

      // Should be 401 (unauthorized) not 404 (not found)
      expect([400, 401]).toContain(response.status);
    }
  });
});