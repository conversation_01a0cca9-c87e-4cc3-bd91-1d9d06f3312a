'use client';

import { useEffect, useState } from 'react';
import { usePerformanceMonitor } from '@/hooks/usePerformanceMonitor';
import { useAnalyticsContext } from '@/lib/analytics-provider';

interface PerformanceMetrics {
  lcp?: number;
  fid?: number;
  cls?: number;
  fcp?: number;
  ttfb?: number;
  memoryUsage?: number;
  slowResources: number;
  errorResources: number;
}

/**
 * Development component to display real-time performance metrics
 * Only visible in development mode
 */
export function PerformanceMetrics() {
  const { debug } = useAnalyticsContext();
  const [metrics, setMetrics] = useState<PerformanceMetrics>({
    slowResources: 0,
    errorResources: 0,
  });
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    if (!debug) return;

    // Listen for performance entries
    const observer = new PerformanceObserver((list) => {
      const entries = list.getEntries();
      
      for (const entry of entries) {
        if (entry.entryType === 'largest-contentful-paint') {
          setMetrics(prev => ({ ...prev, lcp: entry.startTime }));
        } else if (entry.entryType === 'first-input') {
          const fid = (entry as any).processingStart - entry.startTime;
          setMetrics(prev => ({ ...prev, fid }));
        } else if (entry.entryType === 'layout-shift') {
          if (!(entry as any).hadRecentInput) {
            setMetrics(prev => ({ 
              ...prev, 
              cls: (prev.cls || 0) + (entry as any).value 
            }));
          }
        } else if (entry.entryType === 'paint' && entry.name === 'first-contentful-paint') {
          setMetrics(prev => ({ ...prev, fcp: entry.startTime }));
        } else if (entry.entryType === 'navigation') {
          const nav = entry as PerformanceNavigationTiming;
          const ttfb = nav.responseStart - nav.requestStart;
          setMetrics(prev => ({ ...prev, ttfb }));
        } else if (entry.entryType === 'resource') {
          const resource = entry as PerformanceResourceTiming;
          if (resource.duration > 2000) {
            setMetrics(prev => ({ 
              ...prev, 
              slowResources: prev.slowResources + 1 
            }));
          }
          if (resource.transferSize === 0 && resource.decodedBodySize === 0) {
            setMetrics(prev => ({ 
              ...prev, 
              errorResources: prev.errorResources + 1 
            }));
          }
        }
      }
    });

    try {
      observer.observe({ entryTypes: ['largest-contentful-paint', 'first-input', 'layout-shift', 'paint', 'navigation', 'resource'] });
    } catch (error) {
      console.warn('Performance observer not supported:', error);
    }

    // Track memory usage
    const memoryInterval = setInterval(() => {
      if ('memory' in performance) {
        const memory = (performance as any).memory;
        setMetrics(prev => ({ 
          ...prev, 
          memoryUsage: memory.usedJSHeapSize / 1024 / 1024 
        }));
      }
    }, 5000);

    return () => {
      observer.disconnect();
      clearInterval(memoryInterval);
    };
  }, [debug]);

  if (!debug) return null;

  const formatMetric = (value: number | undefined, suffix: string) => {
    if (value === undefined) return '-';
    return `${Math.round(value)}${suffix}`;
  };

  const getMetricColor = (metric: keyof PerformanceMetrics, value: number | undefined) => {
    if (value === undefined) return 'text-gray-400';
    
    switch (metric) {
      case 'lcp':
        return value <= 2500 ? 'text-green-500' : value <= 4000 ? 'text-yellow-500' : 'text-red-500';
      case 'fid':
        return value <= 100 ? 'text-green-500' : value <= 300 ? 'text-yellow-500' : 'text-red-500';
      case 'cls':
        return value <= 0.1 ? 'text-green-500' : value <= 0.25 ? 'text-yellow-500' : 'text-red-500';
      case 'fcp':
        return value <= 1800 ? 'text-green-500' : value <= 3000 ? 'text-yellow-500' : 'text-red-500';
      case 'ttfb':
        return value <= 800 ? 'text-green-500' : value <= 1800 ? 'text-yellow-500' : 'text-red-500';
      default:
        return 'text-gray-700';
    }
  };

  return (
    <div className="fixed bottom-4 right-4 z-50">
      <button
        onClick={() => setIsVisible(!isVisible)}
        className="bg-orange-500 text-white px-3 py-2 rounded-lg text-sm font-medium shadow-lg hover:bg-orange-600 transition-colors"
      >
        Perf {isVisible ? '−' : '+'}
      </button>
      
      {isVisible && (
        <div className="absolute bottom-12 right-0 bg-white rounded-lg shadow-xl border p-4 min-w-[300px]">
          <h3 className="font-bold text-sm mb-3 text-gray-800">Performance Metrics</h3>
          
          <div className="space-y-2 text-sm">
            <div className="flex justify-between">
              <span className="text-gray-600">LCP:</span>
              <span className={getMetricColor('lcp', metrics.lcp)}>
                {formatMetric(metrics.lcp, 'ms')}
              </span>
            </div>
            
            <div className="flex justify-between">
              <span className="text-gray-600">FID:</span>
              <span className={getMetricColor('fid', metrics.fid)}>
                {formatMetric(metrics.fid, 'ms')}
              </span>
            </div>
            
            <div className="flex justify-between">
              <span className="text-gray-600">CLS:</span>
              <span className={getMetricColor('cls', metrics.cls)}>
                {formatMetric(metrics.cls, '')}
              </span>
            </div>
            
            <div className="flex justify-between">
              <span className="text-gray-600">FCP:</span>
              <span className={getMetricColor('fcp', metrics.fcp)}>
                {formatMetric(metrics.fcp, 'ms')}
              </span>
            </div>
            
            <div className="flex justify-between">
              <span className="text-gray-600">TTFB:</span>
              <span className={getMetricColor('ttfb', metrics.ttfb)}>
                {formatMetric(metrics.ttfb, 'ms')}
              </span>
            </div>
            
            <div className="flex justify-between">
              <span className="text-gray-600">Memory:</span>
              <span className="text-gray-700">
                {formatMetric(metrics.memoryUsage, 'MB')}
              </span>
            </div>
            
            <div className="border-t pt-2 mt-2">
              <div className="flex justify-between">
                <span className="text-gray-600">Slow Resources:</span>
                <span className={metrics.slowResources > 0 ? 'text-red-500' : 'text-green-500'}>
                  {metrics.slowResources}
                </span>
              </div>
              
              <div className="flex justify-between">
                <span className="text-gray-600">Failed Resources:</span>
                <span className={metrics.errorResources > 0 ? 'text-red-500' : 'text-green-500'}>
                  {metrics.errorResources}
                </span>
              </div>
            </div>
          </div>
          
          <div className="text-xs text-gray-500 mt-3 pt-2 border-t">
            Values update in real-time. Green = Good, Yellow = Needs Improvement, Red = Poor
          </div>
        </div>
      )}
    </div>
  );
}