# MVP Day 09: Viral Sharing Features

**Date**: [Execute Date]  
**Goal**: Make sharing so compelling that users can't help but spread TravelViz  
**Duration**: 8 hours  
**Critical Path**: YES - Viral growth is our primary acquisition strategy

## Context & Viral Strategy

### The Viral Loop

1. **User imports trip** → Creates beautiful itinerary
2. **Shares with friends** → "Check out my Paris trip!"
3. **Friends see trip** → "Wow, how did you make this?"
4. **One-click copy** → Friend creates account
5. **Friend imports their trip** → Loop continues

### Success Metrics

- Share rate: >40% of imported trips
- View-to-copy rate: >15%
- Copy-to-import rate: >50%
- Viral coefficient: >1.2

## Morning: Public Trip Pages (4 hours)

### Task 1: Public Trip View (1.5 hours)

**File**: `packages/web/src/app/t/[shareId]/page.tsx`

```typescript
import { Metadata } from 'next';
import { notFound } from 'next/navigation';
import { PublicTripView } from '@/components/trips/PublicTripView';
import { getPublicTrip, incrementTripViews } from '@/lib/api/public-trips';

interface PublicTripPageProps {
  params: {
    shareId: string;
  };
}

export async function generateMetadata({ params }: PublicTripPageProps): Promise<Metadata> {
  const trip = await getPublicTrip(params.shareId);

  if (!trip) {
    return {
      title: 'Trip Not Found | TravelViz'
    };
  }

  // Rich social media preview
  return {
    title: `${trip.title} | TravelViz`,
    description: `${trip.destination} • ${trip.dayCount} days • ${trip.activityCount} activities`,
    openGraph: {
      title: trip.title,
      description: `Check out this ${trip.dayCount}-day itinerary for ${trip.destination}`,
      images: [
        {
          url: `/api/og?tripId=${trip.id}`,
          width: 1200,
          height: 630,
          alt: trip.title
        }
      ],
      type: 'website'
    },
    twitter: {
      card: 'summary_large_image',
      title: trip.title,
      description: `${trip.destination} • ${trip.dayCount} days`,
      images: [`/api/og?tripId=${trip.id}`]
    }
  };
}

export default async function PublicTripPage({ params }: PublicTripPageProps) {
  const trip = await getPublicTrip(params.shareId);

  if (!trip) {
    notFound();
  }

  // Track view asynchronously
  incrementTripViews(trip.id).catch(console.error);

  return <PublicTripView trip={trip} />;
}
```

**File**: `packages/web/src/components/trips/PublicTripView.tsx`

```typescript
'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { motion, AnimatePresence } from 'framer-motion';
import { Button } from '@/components/ui/button';
import {
  Copy,
  Share2,
  MapPin,
  Calendar,
  Clock,
  DollarSign,
  Sparkles,
  Eye,
  Users,
  ChevronDown,
  Twitter,
  Facebook,
  Link as LinkIcon
} from 'lucide-react';
import { format } from 'date-fns';
import { Timeline } from '@/components/timeline/Timeline';
import { TripMap } from '@/components/map/TripMap';
import { toast } from '@/components/ui/use-toast';

interface PublicTripViewProps {
  trip: PublicTrip;
}

export function PublicTripView({ trip }: PublicTripViewProps) {
  const router = useRouter();
  const [showMap, setShowMap] = useState(false);
  const [copying, setCopying] = useState(false);
  const [viewerCount, setViewerCount] = useState(0);

  // Animate viewer count
  useEffect(() => {
    const target = trip.viewCount;
    const duration = 2000;
    const increment = target / (duration / 16);
    let current = 0;

    const timer = setInterval(() => {
      current += increment;
      if (current >= target) {
        setViewerCount(target);
        clearInterval(timer);
      } else {
        setViewerCount(Math.floor(current));
      }
    }, 16);

    return () => clearInterval(timer);
  }, [trip.viewCount]);

  const handleCopyTrip = async () => {
    setCopying(true);

    try {
      // Check if user is logged in
      const token = localStorage.getItem('token');

      if (!token) {
        // Save intent and redirect to auth
        sessionStorage.setItem('copyTripIntent', trip.id);
        router.push('/auth/signup?intent=copy-trip');
        return;
      }

      // Copy trip
      const response = await fetch(`/api/v1/trips/${trip.id}/copy`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (response.ok) {
        const { tripId } = await response.json();
        toast({
          title: '✨ Trip copied!',
          description: 'You can now customize your version'
        });
        router.push(`/trips/${tripId}/edit`);
      }
    } catch (error) {
      toast({
        title: 'Failed to copy trip',
        description: 'Please try again',
        variant: 'destructive'
      });
    } finally {
      setCopying(false);
    }
  };

  const handleShare = (platform: 'twitter' | 'facebook' | 'link') => {
    const url = window.location.href;
    const text = `Check out my ${trip.dayCount}-day ${trip.destination} itinerary!`;

    switch (platform) {
      case 'twitter':
        window.open(
          `https://twitter.com/intent/tweet?url=${encodeURIComponent(url)}&text=${encodeURIComponent(text)}`,
          '_blank'
        );
        break;
      case 'facebook':
        window.open(
          `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(url)}`,
          '_blank'
        );
        break;
      case 'link':
        navigator.clipboard.writeText(url);
        toast({
          title: 'Link copied!',
          description: 'Share it with your friends'
        });
        break;
    }

    // Track share
    fetch('/api/v1/analytics/track', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        event: 'trip_shared',
        properties: { tripId: trip.id, platform }
      })
    });
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50">
      {/* Hero Section */}
      <div className="relative overflow-hidden bg-white border-b">
        <div className="absolute inset-0 bg-gradient-to-br from-blue-600/5 to-purple-600/5" />

        <div className="relative container mx-auto px-4 py-12">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="max-w-4xl mx-auto text-center"
          >
            {/* Creator Badge */}
            <motion.div
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              transition={{ delay: 0.2 }}
              className="inline-flex items-center gap-2 px-3 py-1 bg-blue-100 rounded-full text-sm text-blue-700 mb-4"
            >
              <Sparkles className="w-4 h-4" />
              Created with TravelViz AI
            </motion.div>

            {/* Title */}
            <h1 className="text-4xl md:text-5xl font-bold mb-4">
              {trip.title}
            </h1>

            {/* Destination & Duration */}
            <div className="flex items-center justify-center gap-6 text-gray-600 mb-8">
              <div className="flex items-center gap-2">
                <MapPin className="w-5 h-5" />
                <span className="text-lg">{trip.destination}</span>
              </div>
              <div className="flex items-center gap-2">
                <Calendar className="w-5 h-5" />
                <span className="text-lg">
                  {format(new Date(trip.startDate), 'MMM d')} -
                  {format(new Date(trip.endDate), 'MMM d, yyyy')}
                </span>
              </div>
            </div>

            {/* Stats */}
            <div className="grid grid-cols-3 gap-4 max-w-md mx-auto mb-8">
              <motion.div
                whileHover={{ scale: 1.05 }}
                className="bg-gray-50 rounded-lg p-4"
              >
                <p className="text-2xl font-bold text-gray-900">
                  {trip.dayCount}
                </p>
                <p className="text-sm text-gray-600">Days</p>
              </motion.div>
              <motion.div
                whileHover={{ scale: 1.05 }}
                className="bg-gray-50 rounded-lg p-4"
              >
                <p className="text-2xl font-bold text-gray-900">
                  {trip.activityCount}
                </p>
                <p className="text-sm text-gray-600">Activities</p>
              </motion.div>
              <motion.div
                whileHover={{ scale: 1.05 }}
                className="bg-gray-50 rounded-lg p-4"
              >
                <p className="text-2xl font-bold text-gray-900">
                  ${trip.estimatedBudget}
                </p>
                <p className="text-sm text-gray-600">Est. Budget</p>
              </motion.div>
            </div>

            {/* CTA Buttons */}
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                <Button
                  size="lg"
                  onClick={handleCopyTrip}
                  disabled={copying}
                  className="min-w-[200px] bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700"
                >
                  {copying ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                      Copying...
                    </>
                  ) : (
                    <>
                      <Copy className="w-4 h-4 mr-2" />
                      Copy to My Account
                    </>
                  )}
                </Button>
              </motion.div>

              <div className="flex gap-2">
                <Button
                  variant="outline"
                  size="icon"
                  onClick={() => handleShare('twitter')}
                >
                  <Twitter className="w-4 h-4" />
                </Button>
                <Button
                  variant="outline"
                  size="icon"
                  onClick={() => handleShare('facebook')}
                >
                  <Facebook className="w-4 h-4" />
                </Button>
                <Button
                  variant="outline"
                  size="icon"
                  onClick={() => handleShare('link')}
                >
                  <LinkIcon className="w-4 h-4" />
                </Button>
              </div>
            </div>

            {/* Social Proof */}
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.5 }}
              className="flex items-center justify-center gap-6 mt-6 text-sm text-gray-500"
            >
              <div className="flex items-center gap-1">
                <Eye className="w-4 h-4" />
                <span>{viewerCount.toLocaleString()} views</span>
              </div>
              {trip.copyCount > 0 && (
                <div className="flex items-center gap-1">
                  <Users className="w-4 h-4" />
                  <span>{trip.copyCount} copied</span>
                </div>
              )}
            </motion.div>
          </motion.div>
        </div>
      </div>

      {/* Timeline Section */}
      <div className="container mx-auto px-4 py-12">
        <div className="max-w-4xl mx-auto">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3 }}
          >
            <h2 className="text-2xl font-bold mb-6">Daily Itinerary</h2>
            <Timeline
              activities={trip.activities}
              editable={false}
              public={true}
            />
          </motion.div>

          {/* Map Toggle */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.5 }}
            className="mt-8"
          >
            <Button
              variant="outline"
              onClick={() => setShowMap(!showMap)}
              className="w-full"
            >
              {showMap ? 'Hide Map' : 'Show Map View'}
              <ChevronDown className={`ml-2 h-4 w-4 transition-transform ${showMap ? 'rotate-180' : ''}`} />
            </Button>

            <AnimatePresence>
              {showMap && (
                <motion.div
                  initial={{ height: 0, opacity: 0 }}
                  animate={{ height: 'auto', opacity: 1 }}
                  exit={{ height: 0, opacity: 0 }}
                  className="overflow-hidden"
                >
                  <div className="mt-4 rounded-lg overflow-hidden shadow-lg">
                    <TripMap
                      activities={trip.activities}
                      height="500px"
                      interactive={true}
                      showRoute={true}
                    />
                  </div>
                </motion.div>
              )}
            </AnimatePresence>
          </motion.div>
        </div>
      </div>

      {/* Bottom CTA */}
      <div className="bg-gradient-to-r from-blue-600 to-purple-600 text-white py-16">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl font-bold mb-4">
            Create Your Own Beautiful Itinerary
          </h2>
          <p className="text-lg mb-8 opacity-90">
            Import your ChatGPT, Claude, or Gemini conversations in seconds
          </p>
          <Button
            size="lg"
            variant="secondary"
            onClick={() => router.push('/import')}
            className="min-w-[200px]"
          >
            Try TravelViz Free
            <Sparkles className="ml-2 h-4 w-4" />
          </Button>
        </div>
      </div>
    </div>
  );
}
```

### Task 2: Open Graph Image Generation (1 hour)

**File**: `packages/web/src/app/api/og/route.tsx`

```tsx
import { ImageResponse } from '@vercel/og';
import { NextRequest } from 'next/server';

export const runtime = 'edge';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const tripId = searchParams.get('tripId');

    if (!tripId) {
      return new Response('Missing tripId', { status: 400 });
    }

    // Fetch trip data
    const trip = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/api/v1/trips/${tripId}/public`).then(res => res.json());

    return new ImageResponse(
      (
        <div
          style={{
            height: '100%',
            width: '100%',
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center',
            backgroundColor: '#f3f4f6',
            backgroundImage: 'linear-gradient(to bottom right, #dbeafe, #fae8ff)',
          }}
        >
          {/* Logo */}
          <div
            style={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              marginBottom: 40,
            }}
          >
            <svg
              width="60"
              height="60"
              viewBox="0 0 24 24"
              fill="none"
              style={{ marginRight: 16 }}
            >
              <path
                d="M12 2L2 7L12 12L22 7L12 2Z"
                fill="#3b82f6"
              />
              <path
                d="M2 17L12 22L22 17"
                stroke="#3b82f6"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
              <path
                d="M2 12L12 17L22 12"
                stroke="#8b5cf6"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
            </svg>
            <h1
              style={{
                fontSize: 48,
                fontWeight: 'bold',
                background: 'linear-gradient(to right, #3b82f6, #8b5cf6)',
                backgroundClip: 'text',
                color: 'transparent',
              }}
            >
              TravelViz
            </h1>
          </div>

          {/* Trip Title */}
          <h2
            style={{
              fontSize: 56,
              fontWeight: 'bold',
              textAlign: 'center',
              maxWidth: '80%',
              marginBottom: 24,
              color: '#111827',
            }}
          >
            {trip.title}
          </h2>

          {/* Destination */}
          <div
            style={{
              display: 'flex',
              alignItems: 'center',
              fontSize: 32,
              color: '#6b7280',
              marginBottom: 40,
            }}
          >
            <svg
              width="32"
              height="32"
              viewBox="0 0 24 24"
              fill="none"
              style={{ marginRight: 12 }}
            >
              <path
                d="M21 10C21 17 12 23 12 23S3 17 3 10C3 5.58172 6.58172 2 12 2C17.4183 2 21 5.58172 21 10Z"
                stroke="#6b7280"
                strokeWidth="2"
              />
              <circle cx="12" cy="10" r="3" fill="#6b7280" />
            </svg>
            {trip.destination}
          </div>

          {/* Stats */}
          <div
            style={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              gap: 60,
              padding: '32px 64px',
              backgroundColor: 'white',
              borderRadius: 16,
              boxShadow: '0 10px 25px rgba(0, 0, 0, 0.1)',
            }}
          >
            <div style={{ textAlign: 'center' }}>
              <div style={{ fontSize: 48, fontWeight: 'bold', color: '#3b82f6' }}>
                {trip.dayCount}
              </div>
              <div style={{ fontSize: 20, color: '#6b7280' }}>Days</div>
            </div>
            <div style={{ textAlign: 'center' }}>
              <div style={{ fontSize: 48, fontWeight: 'bold', color: '#8b5cf6' }}>
                {trip.activityCount}
              </div>
              <div style={{ fontSize: 20, color: '#6b7280' }}>Activities</div>
            </div>
            <div style={{ textAlign: 'center' }}>
              <div style={{ fontSize: 48, fontWeight: 'bold', color: '#10b981' }}>
                ${trip.estimatedBudget}
              </div>
              <div style={{ fontSize: 20, color: '#6b7280' }}>Est. Budget</div>
            </div>
          </div>

          {/* Footer */}
          <div
            style={{
              position: 'absolute',
              bottom: 40,
              display: 'flex',
              alignItems: 'center',
              fontSize: 24,
              color: '#6b7280',
            }}
          >
            Created with AI-powered trip planning
          </div>
        </div>
      ),
      {
        width: 1200,
        height: 630,
      }
    );
      // Generate and cache image
      const imageResponse = await generateOGImage(trip);
      const imageBuffer = await imageResponse.arrayBuffer();

      // Cache for 7 days
      await redis.setex(cacheKey, 604800, Buffer.from(imageBuffer));

      return new Response(imageBuffer, {
        headers: {
          'Content-Type': 'image/png',
          'Cache-Control': 'public, max-age=604800, stale-while-revalidate=86400',
          'X-Cache': 'MISS'
        }
      });
    } catch (fetchError) {
      clearTimeout(timeoutId);
      // Return fallback image
      return new Response(await getFallbackImage(), {
        headers: {
          'Content-Type': 'image/png',
          'Cache-Control': 'public, max-age=3600'
        }
      });
    }
  } catch (error) {
    console.error('OG Image error:', error);
    return new Response('Failed to generate image', { status: 500 });
  }
}

async function generateOGImage(trip: any): Promise<ImageResponse> {
  // Existing ImageResponse code here
  return new ImageResponse(
    <div
      style={{
        height: '100%',
        width: '100%',
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        backgroundColor: '#f3f4f6',
        backgroundImage: 'linear-gradient(to bottom right, #dbeafe, #fae8ff)',
      }}
    >
      {/* ... rest of JSX ... */}
    </div>,
    {
      width: 1200,
      height: 630,
    }
  );
}

async function getFallbackImage(): Promise<ArrayBuffer> {
  // Return pre-generated fallback image
  return fetch('/og-fallback.png').then(res => res.arrayBuffer());
}
```

### Task 3: Share Settings & Analytics (1.5 hours)

**File**: `packages/web/src/components/modals/ShareModal.tsx`

```typescript
'use client';

import { useState, useEffect } from 'react';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Switch } from '@/components/ui/switch';
import { Input } from '@/components/ui/input';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Globe,
  Link,
  Copy,
  Check,
  Twitter,
  Facebook,
  Mail,
  MessageCircle,
  Eye,
  Users,
  TrendingUp,
  Lock
} from 'lucide-react';
import { toast } from '@/components/ui/use-toast';
import { motion } from 'framer-motion';

interface ShareModalProps {
  trip: Trip;
  onClose: () => void;
  onUpdate: (settings: ShareSettings) => void;
}

interface ShareSettings {
  isPublic: boolean;
  shareId?: string;
  allowCopy: boolean;
  hidePrice: boolean;
  customMessage?: string;
}

export function ShareModal({ trip, onClose, onUpdate }: ShareModalProps) {
  const [settings, setSettings] = useState<ShareSettings>({
    isPublic: trip.isPublic || false,
    shareId: trip.shareId,
    allowCopy: true,
    hidePrice: false,
    customMessage: ''
  });
  const [saving, setSaving] = useState(false);
  const [copied, setCopied] = useState(false);
  const [analytics, setAnalytics] = useState<ShareAnalytics | null>(null);

  const shareUrl = settings.shareId
    ? `${window.location.origin}/t/${settings.shareId}`
    : '';

  useEffect(() => {
    if (settings.shareId) {
      // Fetch share analytics
      fetch(`/api/v1/trips/${trip.id}/analytics`)
        .then(res => res.json())
        .then(data => setAnalytics(data))
        .catch(console.error);
    }
  }, [trip.id, settings.shareId]);

  const handleTogglePublic = async (checked: boolean) => {
    setSaving(true);

    try {
      const response = await fetch(`/api/v1/trips/${trip.id}/share`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify({
          isPublic: checked,
          ...settings
        })
      });

      const data = await response.json();

      setSettings({
        ...settings,
        isPublic: checked,
        shareId: data.shareId
      });

      toast({
        title: checked ? 'Trip is now public!' : 'Trip is now private',
        description: checked ? 'Anyone with the link can view' : 'Only you can access'
      });
    } catch (error) {
      toast({
        title: 'Failed to update sharing',
        variant: 'destructive'
      });
    } finally {
      setSaving(false);
    }
  };

  const handleCopyLink = () => {
    navigator.clipboard.writeText(shareUrl);
    setCopied(true);
    setTimeout(() => setCopied(false), 2000);

    // Track share
    fetch('/api/v1/analytics/track', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        event: 'trip_link_copied',
        properties: { tripId: trip.id }
      })
    });
  };

  const handleShare = (platform: string) => {
    const text = settings.customMessage ||
      `Check out my ${trip.destination} itinerary! Created with @TravelViz`;

    const shareUrls = {
      twitter: `https://twitter.com/intent/tweet?url=${encodeURIComponent(shareUrl)}&text=${encodeURIComponent(text)}`,
      facebook: `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(shareUrl)}`,
      whatsapp: `https://wa.me/?text=${encodeURIComponent(text + ' ' + shareUrl)}`,
      email: `mailto:?subject=${encodeURIComponent(trip.title)}&body=${encodeURIComponent(text + '\n\n' + shareUrl)}`
    };

    window.open(shareUrls[platform], '_blank');

    // Track share
    fetch('/api/v1/analytics/track', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        event: 'trip_shared',
        properties: { tripId: trip.id, platform }
      })
    });
  };

  return (
    <Dialog open onOpenChange={onClose}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle>Share Your Trip</DialogTitle>
        </DialogHeader>

        <Tabs defaultValue="share" className="mt-4">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="share">Share Settings</TabsTrigger>
            <TabsTrigger value="analytics" disabled={!settings.isPublic}>
              Analytics
            </TabsTrigger>
          </TabsList>

          <TabsContent value="share" className="space-y-6">
            {/* Public Toggle */}
            <div className="flex items-center justify-between p-4 border rounded-lg">
              <div className="flex items-center gap-3">
                {settings.isPublic ? (
                  <Globe className="w-5 h-5 text-blue-600" />
                ) : (
                  <Lock className="w-5 h-5 text-gray-600" />
                )}
                <div>
                  <p className="font-medium">Make trip public</p>
                  <p className="text-sm text-gray-600">
                    Anyone with the link can view your trip
                  </p>
                </div>
              </div>
              <Switch
                checked={settings.isPublic}
                onCheckedChange={handleTogglePublic}
                disabled={saving}
              />
            </div>

            {/* Share Options */}
            {settings.isPublic && (
              <motion.div
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                className="space-y-4"
              >
                {/* Share Link */}
                <div className="space-y-2">
                  <label className="text-sm font-medium">Share Link</label>
                  <div className="flex gap-2">
                    <Input
                      value={shareUrl}
                      readOnly
                      className="font-mono text-sm"
                    />
                    <Button
                      variant="outline"
                      onClick={handleCopyLink}
                      className="min-w-[100px]"
                    >
                      {copied ? (
                        <>
                          <Check className="w-4 h-4 mr-2" />
                          Copied!
                        </>
                      ) : (
                        <>
                          <Copy className="w-4 h-4 mr-2" />
                          Copy
                        </>
                      )}
                    </Button>
                  </div>
                </div>

                {/* Privacy Options */}
                <div className="space-y-3">
                  <label className="text-sm font-medium">Privacy Options</label>

                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium">Allow copying</p>
                      <p className="text-xs text-gray-600">
                        Viewers can copy this trip to their account
                      </p>
                    </div>
                    <Switch
                      checked={settings.allowCopy}
                      onCheckedChange={(checked) =>
                        setSettings({ ...settings, allowCopy: checked })
                      }
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium">Hide prices</p>
                      <p className="text-xs text-gray-600">
                        Don't show activity prices publicly
                      </p>
                    </div>
                    <Switch
                      checked={settings.hidePrice}
                      onCheckedChange={(checked) =>
                        setSettings({ ...settings, hidePrice: checked })
                      }
                    />
                  </div>
                </div>

                {/* Custom Message */}
                <div className="space-y-2">
                  <label className="text-sm font-medium">
                    Custom Message (optional)
                  </label>
                  <textarea
                    className="w-full p-3 border rounded-lg text-sm"
                    rows={2}
                    placeholder="Add a personal message for social media..."
                    value={settings.customMessage}
                    onChange={(e) =>
                      setSettings({ ...settings, customMessage: e.target.value })
                    }
                  />
                </div>

                {/* Social Share Buttons */}
                <div className="space-y-2">
                  <label className="text-sm font-medium">Share on</label>
                  <div className="grid grid-cols-4 gap-3">
                    <Button
                      variant="outline"
                      onClick={() => handleShare('twitter')}
                      className="flex-col gap-2 h-20"
                    >
                      <Twitter className="w-5 h-5" />
                      <span className="text-xs">Twitter</span>
                    </Button>
                    <Button
                      variant="outline"
                      onClick={() => handleShare('facebook')}
                      className="flex-col gap-2 h-20"
                    >
                      <Facebook className="w-5 h-5" />
                      <span className="text-xs">Facebook</span>
                    </Button>
                    <Button
                      variant="outline"
                      onClick={() => handleShare('whatsapp')}
                      className="flex-col gap-2 h-20"
                    >
                      <MessageCircle className="w-5 h-5" />
                      <span className="text-xs">WhatsApp</span>
                    </Button>
                    <Button
                      variant="outline"
                      onClick={() => handleShare('email')}
                      className="flex-col gap-2 h-20"
                    >
                      <Mail className="w-5 h-5" />
                      <span className="text-xs">Email</span>
                    </Button>
                  </div>
                </div>
              </motion.div>
            )}
          </TabsContent>

          <TabsContent value="analytics" className="space-y-6">
            {analytics ? (
              <>
                {/* Analytics Overview */}
                <div className="grid grid-cols-3 gap-4">
                  <div className="text-center p-4 bg-gray-50 rounded-lg">
                    <Eye className="w-6 h-6 text-gray-600 mx-auto mb-2" />
                    <p className="text-2xl font-bold">{analytics.views}</p>
                    <p className="text-sm text-gray-600">Total Views</p>
                  </div>
                  <div className="text-center p-4 bg-gray-50 rounded-lg">
                    <Users className="w-6 h-6 text-gray-600 mx-auto mb-2" />
                    <p className="text-2xl font-bold">{analytics.uniqueViewers}</p>
                    <p className="text-sm text-gray-600">Unique Viewers</p>
                  </div>
                  <div className="text-center p-4 bg-gray-50 rounded-lg">
                    <Copy className="w-6 h-6 text-gray-600 mx-auto mb-2" />
                    <p className="text-2xl font-bold">{analytics.copies}</p>
                    <p className="text-sm text-gray-600">Copies Made</p>
                  </div>
                </div>

                {/* Referrer Sources */}
                <div className="space-y-2">
                  <h4 className="font-medium">Traffic Sources</h4>
                  <div className="space-y-2">
                    {analytics.referrers.map((referrer, i) => (
                      <div key={i} className="flex items-center justify-between p-2 bg-gray-50 rounded">
                        <span className="text-sm">{referrer.source}</span>
                        <span className="text-sm font-medium">{referrer.count} views</span>
                      </div>
                    ))}
                  </div>
                </div>

                {/* View Graph */}
                <div className="space-y-2">
                  <h4 className="font-medium">Views Over Time</h4>
                  <div className="h-32 bg-gray-50 rounded-lg flex items-center justify-center text-gray-500">
                    <TrendingUp className="w-6 h-6 mr-2" />
                    Graph visualization here
                  </div>
                </div>
              </>
            ) : (
              <div className="text-center py-8 text-gray-500">
                Share your trip to see analytics
              </div>
            )}
          </TabsContent>
        </Tabs>

        {/* Save Button */}
        {settings.isPublic && (
          <div className="flex justify-end mt-6">
            <Button
              onClick={() => {
                onUpdate(settings);
                onClose();
              }}
              disabled={saving}
            >
              Save Settings
            </Button>
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
}
```

## Afternoon: Viral Mechanics & Growth (4 hours)

### Task 4: One-Click Copy & Onboarding (2 hours)

**File**: `packages/web/src/app/auth/signup/page.tsx`

```typescript
'use client';

import { useState, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { motion } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Sparkles,
  Mail,
  Lock,
  User,
  ArrowRight,
  Copy,
  CheckCircle
} from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { toast } from '@/components/ui/use-toast';

export default function SignupPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { signup } = useAuth();

  const intent = searchParams.get('intent');
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [name, setName] = useState('');
  const [loading, setLoading] = useState(false);

  // Track signup source
  useEffect(() => {
    if (intent === 'copy-trip') {
      // Track viral signup
      fetch('/api/v1/analytics/track', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          event: 'viral_signup_started',
          properties: {
            source: 'copy_trip',
            tripId: sessionStorage.getItem('copyTripIntent')
          }
        })
      });
    }
  }, [intent]);

  const handleSignup = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);

    try {
      await signup(email, password, name);

      // Track successful signup
      fetch('/api/v1/analytics/track', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          event: 'signup_completed',
          properties: {
            source: intent || 'direct',
            hasIntent: !!intent
          }
        })
      });

      // Handle post-signup flow
      if (intent === 'copy-trip') {
        const tripId = sessionStorage.getItem('copyTripIntent');
        if (tripId) {
          // Copy the trip
          const response = await fetch(`/api/v1/trips/${tripId}/copy`, {
            method: 'POST',
            headers: {
              'Authorization': `Bearer ${localStorage.getItem('token')}`
            }
          });

          if (response.ok) {
            const { tripId: newTripId } = await response.json();
            toast({
              title: '✨ Trip copied successfully!',
              description: 'Welcome to TravelViz!'
            });
            router.push(`/trips/${newTripId}/edit`);
            return;
          }
        }
      }

      // Default redirect
      router.push('/dashboard');
    } catch (error) {
      toast({
        title: 'Signup failed',
        description: 'Please try again',
        variant: 'destructive'
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50">
      <div className="container mx-auto px-4 py-12">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="max-w-md mx-auto"
        >
          {/* Logo */}
          <div className="text-center mb-8">
            <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full mb-4">
              <Sparkles className="w-8 h-8 text-white" />
            </div>
            <h1 className="text-3xl font-bold">
              {intent === 'copy-trip' ? 'Almost There!' : 'Welcome to TravelViz'}
            </h1>
            <p className="text-gray-600 mt-2">
              {intent === 'copy-trip'
                ? 'Create an account to save the trip'
                : 'Turn AI conversations into visual itineraries'
              }
            </p>
          </div>

          {/* Intent Banner */}
          {intent === 'copy-trip' && (
            <motion.div
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6"
            >
              <div className="flex items-center gap-3">
                <Copy className="w-5 h-5 text-blue-600" />
                <div>
                  <p className="font-medium text-blue-900">
                    You're one step away!
                  </p>
                  <p className="text-sm text-blue-700">
                    Sign up to copy this trip and make it your own
                  </p>
                </div>
              </div>
            </motion.div>
          )}

          {/* Signup Form */}
          <form onSubmit={handleSignup} className="space-y-4">
            <div>
              <label className="block text-sm font-medium mb-2">Name</label>
              <div className="relative">
                <User className="absolute left-3 top-3 w-5 h-5 text-gray-400" />
                <Input
                  type="text"
                  placeholder="Your name"
                  value={name}
                  onChange={(e) => setName(e.target.value)}
                  required
                  className="pl-10"
                />
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium mb-2">Email</label>
              <div className="relative">
                <Mail className="absolute left-3 top-3 w-5 h-5 text-gray-400" />
                <Input
                  type="email"
                  placeholder="<EMAIL>"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  required
                  className="pl-10"
                />
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium mb-2">Password</label>
              <div className="relative">
                <Lock className="absolute left-3 top-3 w-5 h-5 text-gray-400" />
                <Input
                  type="password"
                  placeholder="Create a password"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  required
                  minLength={8}
                  className="pl-10"
                />
              </div>
              <p className="text-xs text-gray-500 mt-1">
                At least 8 characters
              </p>
            </div>

            <Button
              type="submit"
              className="w-full"
              size="lg"
              disabled={loading}
            >
              {loading ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                  Creating account...
                </>
              ) : (
                <>
                  Create Account
                  <ArrowRight className="ml-2 h-4 w-4" />
                </>
              )}
            </Button>
          </form>

          {/* Social Signup */}
          <div className="mt-6">
            <div className="relative">
              <div className="absolute inset-0 flex items-center">
                <div className="w-full border-t border-gray-300" />
              </div>
              <div className="relative flex justify-center text-sm">
                <span className="px-2 bg-white text-gray-500">Or continue with</span>
              </div>
            </div>

            <div className="mt-6 grid grid-cols-2 gap-3">
              <Button variant="outline" onClick={() => {/* Google OAuth */}}>
                <img src="/google.svg" alt="Google" className="w-5 h-5 mr-2" />
                Google
              </Button>
              <Button variant="outline" onClick={() => {/* Facebook OAuth */}}>
                <img src="/facebook.svg" alt="Facebook" className="w-5 h-5 mr-2" />
                Facebook
              </Button>
            </div>
          </div>

          {/* Benefits */}
          {intent === 'copy-trip' && (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.3 }}
              className="mt-8 space-y-3"
            >
              <h3 className="font-medium text-center mb-4">
                After signing up, you can:
              </h3>
              <div className="space-y-2">
                {[
                  'Customize the itinerary',
                  'Add your own activities',
                  'Share with friends',
                  'Import more trips from AI'
                ].map((benefit, i) => (
                  <motion.div
                    key={benefit}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: 0.4 + i * 0.1 }}
                    className="flex items-center gap-2 text-sm text-gray-600"
                  >
                    <CheckCircle className="w-4 h-4 text-green-600" />
                    {benefit}
                  </motion.div>
                ))}
              </div>
            </motion.div>
          )}

          {/* Login Link */}
          <p className="text-center text-sm text-gray-600 mt-6">
            Already have an account?{' '}
            <a href="/auth/login" className="text-blue-600 hover:underline">
              Log in
            </a>
          </p>
        </motion.div>
      </div>
    </div>
  );
}
```

### Task 5: Viral Analytics Dashboard (2 hours)

**File**: `packages/web/src/components/analytics/ViralDashboard.tsx`

```typescript
'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  TrendingUp,
  Users,
  Share2,
  Copy,
  Eye,
  ArrowUp,
  ArrowDown,
  Zap
} from 'lucide-react';
import { Line, Bar } from 'react-chartjs-2';
import { motion } from 'framer-motion';

interface ViralMetrics {
  totalShares: number;
  totalViews: number;
  totalCopies: number;
  viralCoefficient: number;
  topTrips: TopTrip[];
  growthRate: number;
  sharesByPlatform: Record<string, number>;
  conversionFunnel: ConversionStep[];
}

interface TopTrip {
  id: string;
  title: string;
  shares: number;
  views: number;
  copies: number;
  viralScore: number;
}

interface ConversionStep {
  name: string;
  value: number;
  percentage: number;
}

export function ViralDashboard({ userId }: { userId: string }) {
  const [metrics, setMetrics] = useState<ViralMetrics | null>(null);
  const [timeRange, setTimeRange] = useState<'7d' | '30d' | 'all'>('7d');
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchMetrics();
  }, [userId, timeRange]);

  const fetchMetrics = async () => {
    try {
      const response = await fetch(
        `/api/v1/analytics/viral?userId=${userId}&range=${timeRange}`,
        {
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('token')}`
          }
        }
      );
      const data = await response.json();
      setMetrics(data);
    } catch (error) {
      console.error('Failed to fetch viral metrics:', error);
    } finally {
      setLoading(false);
    }
  };

  if (loading || !metrics) {
    return <div>Loading viral analytics...</div>;
  }

  return (
    <div className="space-y-6">
      {/* Viral Coefficient Hero */}
      <motion.div
        initial={{ scale: 0.9, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
      >
        <Card className="bg-gradient-to-r from-purple-500 to-pink-500 text-white">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm opacity-90">Viral Coefficient</p>
                <p className="text-4xl font-bold">{metrics.viralCoefficient.toFixed(2)}</p>
                <p className="text-sm mt-1 opacity-90">
                  {metrics.viralCoefficient > 1
                    ? '🚀 Growing exponentially!'
                    : 'Keep sharing to go viral!'}
                </p>
              </div>
              <Zap className="w-16 h-16 opacity-20" />
            </div>
          </CardContent>
        </Card>
      </motion.div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <MetricCard
          title="Total Shares"
          value={metrics.totalShares}
          icon={Share2}
          trend={metrics.growthRate}
        />
        <MetricCard
          title="Total Views"
          value={metrics.totalViews}
          icon={Eye}
          trend={15}
        />
        <MetricCard
          title="Total Copies"
          value={metrics.totalCopies}
          icon={Copy}
          trend={25}
        />
        <MetricCard
          title="New Users"
          value={Math.floor(metrics.totalCopies * 0.7)}
          icon={Users}
          trend={30}
        />
      </div>

      {/* Conversion Funnel */}
      <Card>
        <CardHeader>
          <CardTitle>Viral Conversion Funnel</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {metrics.conversionFunnel.map((step, index) => (
              <div key={step.name}>
                <div className="flex items-center justify-between mb-2">
                  <span className="text-sm font-medium">{step.name}</span>
                  <span className="text-sm text-gray-600">
                    {step.value} ({step.percentage}%)
                  </span>
                </div>
                <div className="relative h-8 bg-gray-100 rounded-full overflow-hidden">
                  <motion.div
                    initial={{ width: 0 }}
                    animate={{ width: `${step.percentage}%` }}
                    transition={{ duration: 0.5, delay: index * 0.1 }}
                    className="absolute inset-y-0 left-0 bg-gradient-to-r from-blue-500 to-purple-500"
                  />
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Top Viral Trips */}
      <Card>
        <CardHeader>
          <CardTitle>Top Viral Trips</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {metrics.topTrips.map((trip, index) => (
              <motion.div
                key={trip.id}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: index * 0.1 }}
                className="flex items-center justify-between p-4 bg-gray-50 rounded-lg"
              >
                <div>
                  <p className="font-medium">{trip.title}</p>
                  <div className="flex items-center gap-4 mt-1 text-sm text-gray-600">
                    <span>{trip.shares} shares</span>
                    <span>{trip.views} views</span>
                    <span>{trip.copies} copies</span>
                  </div>
                </div>
                <div className="text-right">
                  <p className="text-sm text-gray-600">Viral Score</p>
                  <p className="text-2xl font-bold text-purple-600">
                    {trip.viralScore}
                  </p>
                </div>
              </motion.div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Share Platform Breakdown */}
      <Card>
        <CardHeader>
          <CardTitle>Shares by Platform</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="h-64">
            <Bar
              data={{
                labels: Object.keys(metrics.sharesByPlatform),
                datasets: [{
                  label: 'Shares',
                  data: Object.values(metrics.sharesByPlatform),
                  backgroundColor: [
                    '#1DA1F2', // Twitter
                    '#4267B2', // Facebook
                    '#25D366', // WhatsApp
                    '#EA4335', // Email
                  ]
                }]
              }}
              options={{
                responsive: true,
                maintainAspectRatio: false
              }}
            />
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

function MetricCard({ title, value, icon: Icon, trend }: {
  title: string;
  value: number;
  icon: any;
  trend: number;
}) {
  const isPositive = trend > 0;

  return (
    <Card>
      <CardContent className="p-6">
        <div className="flex items-center justify-between mb-2">
          <Icon className="w-5 h-5 text-gray-600" />
          <div className={`flex items-center gap-1 text-sm ${
            isPositive ? 'text-green-600' : 'text-red-600'
          }`}>
            {isPositive ? <ArrowUp className="w-3 h-3" /> : <ArrowDown className="w-3 h-3" />}
            {Math.abs(trend)}%
          </div>
        </div>
        <p className="text-2xl font-bold">{value.toLocaleString()}</p>
        <p className="text-sm text-gray-600">{title}</p>
      </CardContent>
    </Card>
  );
}
```

## Testing & Optimization

### Viral Testing Checklist

- [ ] Share creates unique URL
- [ ] OG image generates correctly
- [ ] Social previews look good
- [ ] Copy button works for non-users
- [ ] Signup flow preserves intent
- [ ] Trip copies successfully after signup
- [ ] Analytics track all events
- [ ] Viral coefficient calculated correctly
- [ ] Rate limiting prevents abuse
- [ ] Attribution tracking works across sessions
- [ ] Deep links work on mobile
- [ ] Referral codes generate properly

### Performance Optimization

- [ ] Public pages load <2s
- [ ] OG images cache properly
- [ ] Share actions are instant
- [ ] Analytics don't block UI
- [ ] Mobile sharing works perfectly
- [ ] CDN serves static assets
- [ ] Image optimization (WebP with fallback)
- [ ] Critical CSS inlined
- [ ] JavaScript lazy loaded

### Viral Growth Monitoring

```typescript
// Real-time viral metrics dashboard
interface ViralMetrics {
  // Core metrics
  k_factor: number; // Viral coefficient
  cycle_time: number; // Days from share to new user share

  // Funnel metrics
  share_rate: number; // % of users who share
  click_rate: number; // % of shares that get clicked
  view_rate: number; // % of clicks that view full trip
  copy_rate: number; // % of views that copy
  activation_rate: number; // % of copies that become active users

  // Quality metrics
  share_quality_score: number; // Based on engagement
  user_quality_score: number; // Based on LTV prediction

  // Channel performance
  channels: {
    twitter: { shares: number; conversions: number };
    facebook: { shares: number; conversions: number };
    whatsapp: { shares: number; conversions: number };
    direct: { shares: number; conversions: number };
  };
}

// Alert thresholds
const VIRAL_ALERTS = {
  k_factor_drop: 0.8, // Alert if K < 0.8
  conversion_drop: 0.1, // Alert if conversion drops 10%
  abuse_threshold: 100, // Shares per hour
};
```

### A/B Testing Framework

```typescript
// Test variations for viral optimization
const VIRAL_TESTS = {
  copy_button_text: [
    'Copy to My Account',
    'Use This Itinerary',
    'Make It Mine',
    'Customize This Trip',
  ],

  share_incentive: [
    'none',
    'unlock_feature', // Unlock feature after 3 shares
    'discount_code', // 20% off Pro after sharing
    'social_proof', // Show "23 friends saw your trips"
  ],

  signup_flow: [
    'traditional', // Email -> Password -> Verify
    'social_first', // Google/Facebook -> Email
    'magic_link', // Email only -> Magic link
    'progressive', // Copy first -> Sign up later
  ],
};
```

## Extended Thinking Prompts

For viral optimization:

```
Current viral coefficient: [number]
Bottleneck in funnel: [which step]
User behavior: [what they do/don't do]
What's the smallest change for biggest viral impact?
```

For attribution analysis:

```
Top performing channels: [list]
Average shares before conversion: [number]
Time from first view to signup: [duration]
How can we shorten the viral cycle?
```

For security concerns:

```
Current abuse patterns: [list]
Rate limit thresholds: [current settings]
False positive rate: [percentage]
How to balance growth with protection?
```

## Definition of Done

✅ Viral Features Complete:

- Public trip pages beautiful
- Social sharing with previews
- One-click copy working
- Analytics tracking everything
- Viral coefficient >1.0

✅ User Flow Test:

1. User imports trip
2. Shares on Twitter
3. Friend clicks link
4. Sees beautiful trip
5. Clicks "Copy to My Account"
6. Signs up easily
7. Has trip in their account
8. Imports their own trip

## Next Days Preview

Days 10-11: Basic Monetization

- Stripe payment links
- Pro features
- Affiliate integration

## Notes

- Make sharing feel rewarding not pushy
- Public pages must load FAST
- Social previews are marketing
- Reduce friction at every step
- Track everything but respect privacy
- Viral growth compounds quickly
- Every share is a potential revenue event
- Quality > Quantity for sustainable growth
- Monitor for platform policy compliance
- Cache aggressively but purge smartly

## Revenue Impact

### Viral Revenue Model

```
Revenue per viral user =
  (Conversion to Pro rate × Pro LTV) +
  (Affiliate click rate × Avg commission × Lifetime clicks)

Example:
- 5% convert to Pro @ $90 LTV = $4.50
- 30% click affiliates @ $2 avg × 10 lifetime = $6.00
- Total viral user value = $10.50

With K=1.2, each user brings 1.2 new users:
True user value = $10.50 × (1 + 1.2 + 1.44 + ...) = ~$52.50
```

### Growth Hacking Tactics

1. **Incentivized Sharing**:
   - "Share 3 trips, get 1 month Pro free"
   - "Your friends get 50% off first month"
   - "Unlock map themes by sharing"

2. **Social Proof Integration**:
   - "Sarah and 12 others copied this trip"
   - "Trending in Paris itineraries"
   - "Featured by 3 travel bloggers"

3. **Viral Mechanics**:
   - Limited-time trip access (FOMO)
   - Collaborative planning features
   - Trip templates marketplace
