#!/usr/bin/env node

/**
 * Test runner for AI import integration tests
 * 
 * Usage:
 *   npm run test:ai-import
 *   npm run test:ai-import -- --watch
 *   npm run test:ai-import -- --coverage
 */

const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');

// Load .env.local file
const envPath = path.join(__dirname, '..', '.env.local');
if (fs.existsSync(envPath)) {
  require('dotenv').config({ path: envPath });
}

// Check if hub server is running
const checkServerRunning = async () => {
  try {
    const response = await fetch('http://localhost:3001/health');
    return response.ok;
  } catch (error) {
    return false;
  }
};

// Main test runner
const runTests = async () => {
  console.log('🧪 Running AI Import Integration Tests...\n');

  // Check if server is running
  const serverRunning = await checkServerRunning();
  if (!serverRunning) {
    console.error('❌ Hub server is not running on port 3001');
    console.log('Please start the server with: pnpm dev (from hub directory)');
    process.exit(1);
  }

  // Get test file pattern
  const testFiles = [
    'src/integration/__tests__/ai-import-fixed.test.ts',
    'src/integration/__tests__/ai-import-enhanced.test.ts',
    'src/integration/__tests__/pdf-import.test.ts',
    'src/integration/__tests__/ai-chat-itinerary.test.ts'
  ];

  // Build vitest command
  const vitestArgs = [
    'vitest',
    'run',
    ...testFiles,
    '--reporter=verbose',
    ...process.argv.slice(2) // Pass through any additional args
  ];

  // Set environment variables
  const env = {
    ...process.env,
    NODE_ENV: 'test',
    FORCE_COLOR: '1',
    // Ensure test credentials are available
    SUPABASE_URL: process.env.SUPABASE_URL || 'https://ixjtoikbbjzfegmqdlmc.supabase.co',
    SUPABASE_SERVICE_ROLE_KEY: process.env.SUPABASE_SERVICE_ROLE_KEY?.trim(),
    OPENROUTER_API_KEY: process.env.OPENROUTER_API_KEY?.trim(),
    REDIS_URL: process.env.REDIS_URL || process.env.UPSTASH_REDIS_URL,
    REDIS_TOKEN: process.env.REDIS_TOKEN || process.env.UPSTASH_REDIS_TOKEN
  };

  // Check for required env vars
  const requiredEnvVars = [
    'SUPABASE_SERVICE_ROLE_KEY',
    'OPENROUTER_API_KEY'
  ];

  const missingVars = requiredEnvVars.filter(v => !env[v]);
  if (missingVars.length > 0) {
    console.error('❌ Missing required environment variables:');
    missingVars.forEach(v => console.error(`   - ${v}`));
    console.log('\nPlease check your .env.local file');
    process.exit(1);
  }

  // Run tests
  console.log('Running tests with vitest...\n');
  
  const vitest = spawn('pnpm', vitestArgs, {
    cwd: path.resolve(__dirname, '..'),
    env,
    stdio: 'inherit',
    shell: true
  });

  vitest.on('close', (code) => {
    if (code === 0) {
      console.log('\n✅ All AI import tests passed!');
    } else {
      console.log(`\n❌ Tests failed with exit code ${code}`);
    }
    process.exit(code);
  });

  vitest.on('error', (error) => {
    console.error('Failed to start test runner:', error);
    process.exit(1);
  });
};

// Run tests
runTests().catch(error => {
  console.error('Test runner error:', error);
  process.exit(1);
});