import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { renderHook, act } from '@testing-library/react';
import { useAnalytics, usePerformanceTimer, useInteractionTracking, useFeatureTracking } from './useAnalytics';

// Mock Vercel analytics
vi.mock('@vercel/analytics', () => ({
  track: vi.fn(),
}));

vi.mock('@vercel/speed-insights', () => ({
  metric: vi.fn(),
}));

describe('useAnalytics', () => {
  const mockTrack = vi.fn();
  const mockMetric = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
    // Mock window.location
    Object.defineProperty(window, 'location', {
      value: { pathname: '/test-path' },
      writable: true,
    });
    
    const { track } = require('@vercel/analytics');
    const { metric } = require('@vercel/speed-insights');
    track.mockImplementation(mockTrack);
    metric.mockImplementation(mockMetric);
  });

  afterEach(() => {
    vi.resetAllMocks();
  });

  describe('Page view tracking', () => {
    it('should track page view on mount', () => {
      renderHook(() => useAnalytics());

      expect(mockTrack).toHaveBeenCalledWith('page_view', {
        url: '/test-path',
        timestamp: expect.any(String),
      });
    });
  });

  describe('trackEvent', () => {
    it('should track events with properties', () => {
      const { result } = renderHook(() => useAnalytics());

      act(() => {
        result.current.trackEvent('test_event', { key: 'value' });
      });

      expect(mockTrack).toHaveBeenCalledWith('test_event', {
        key: 'value',
        timestamp: expect.any(String),
      });
    });

    it('should track events without properties', () => {
      const { result } = renderHook(() => useAnalytics());

      act(() => {
        result.current.trackEvent('simple_event');
      });

      expect(mockTrack).toHaveBeenCalledWith('simple_event', {
        timestamp: expect.any(String),
      });
    });
  });

  describe('trackImportEvent', () => {
    it('should track import events with correct format', () => {
      const { result } = renderHook(() => useAnalytics());

      act(() => {
        result.current.trackImportEvent('parsing', { source: 'chatgpt' });
      });

      expect(mockTrack).toHaveBeenCalledWith('import_parsing', {
        step: 'parsing',
        source: 'chatgpt',
        timestamp: expect.any(String),
      });
    });

    it('should handle all import steps', () => {
      const { result } = renderHook(() => useAnalytics());
      const steps = ['input', 'parsing', 'preview', 'creating', 'completed'] as const;

      steps.forEach(step => {
        act(() => {
          result.current.trackImportEvent(step);
        });

        expect(mockTrack).toHaveBeenCalledWith(`import_${step}`, {
          step,
          timestamp: expect.any(String),
        });
      });
    });
  });

  describe('trackError', () => {
    it('should track errors with stack traces in development', () => {
      vi.stubEnv('NODE_ENV', 'development');

      const { result } = renderHook(() => useAnalytics());
      const testError = new Error('Test error');
      testError.stack = 'Error: Test error\n    at test.js:1:1';

      act(() => {
        result.current.trackError(testError, { source: 'component' });
      });

      expect(mockTrack).toHaveBeenCalledWith('error_occurred', {
        error: 'Test error',
        stack: 'Error: Test error\n    at test.js:1:1',
        context: { source: 'component' },
        timestamp: expect.any(String),
      });

    });

    it('should redact stack traces in production', () => {
      vi.stubEnv('NODE_ENV', 'production');

      const { result } = renderHook(() => useAnalytics());
      const testError = new Error('Test error');
      testError.stack = 'Error: Test error\n    at sensitive/path.js:1:1';

      act(() => {
        result.current.trackError(testError);
      });

      expect(mockTrack).toHaveBeenCalledWith('error_occurred', {
        error: 'Test error',
        stack: 'REDACTED',
        context: undefined,
        timestamp: expect.any(String),
      });

    });
  });

  describe('trackPerformance', () => {
    it('should track performance metrics', () => {
      const { result } = renderHook(() => useAnalytics());

      act(() => {
        result.current.trackPerformance('load_time', 1500);
      });

      expect(mockMetric).toHaveBeenCalledWith('load_time', 1500, 'ms');
    });

    it('should track metrics with different units', () => {
      const { result } = renderHook(() => useAnalytics());

      act(() => {
        result.current.trackPerformance('bundle_size', 2048, 'bytes');
      });

      expect(mockMetric).toHaveBeenCalledWith('bundle_size', 2048, 'bytes');
    });
  });

  describe('trackConversion', () => {
    it('should track conversion events', () => {
      const { result } = renderHook(() => useAnalytics());

      act(() => {
        result.current.trackConversion('trip_created', { destination: 'Paris' });
      });

      expect(mockTrack).toHaveBeenCalledWith('conversion', {
        type: 'trip_created',
        destination: 'Paris',
        timestamp: expect.any(String),
      });
    });
  });
});

describe('usePerformanceTimer', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    // Mock performance.now()
    let mockTime = 0;
    vi.spyOn(performance, 'now').mockImplementation(() => mockTime++);
  });

  it('should measure component render time', () => {
    const mockMetric = vi.fn();
    const { metric } = require('@vercel/speed-insights');
    metric.mockImplementation(mockMetric);

    const { result } = renderHook(() => usePerformanceTimer('TestComponent'));

    let startTime: number;
    act(() => {
      startTime = result.current.startTimer();
    });

    act(() => {
      result.current.endTimer(startTime);
    });

    expect(mockMetric).toHaveBeenCalledWith('TestComponent_render', expect.any(Number), 'ms');
  });

  it('should use custom label', () => {
    const mockMetric = vi.fn();
    const { metric } = require('@vercel/speed-insights');
    metric.mockImplementation(mockMetric);

    const { result } = renderHook(() => usePerformanceTimer('TestComponent'));

    let startTime: number;
    act(() => {
      startTime = result.current.startTimer();
    });

    act(() => {
      result.current.endTimer(startTime, 'custom_operation');
    });

    expect(mockMetric).toHaveBeenCalledWith('TestComponent_custom_operation', expect.any(Number), 'ms');
  });
});

describe('useInteractionTracking', () => {
  const mockTrack = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
    const { track } = require('@vercel/analytics');
    track.mockImplementation(mockTrack);
  });

  it('should track click events', () => {
    const { result } = renderHook(() => useInteractionTracking());

    act(() => {
      result.current.trackClick('submit-button', { section: 'header' });
    });

    expect(mockTrack).toHaveBeenCalledWith('click', {
      element: 'submit-button',
      section: 'header',
      timestamp: expect.any(String),
    });
  });

  it('should track form submissions', () => {
    const { result } = renderHook(() => useInteractionTracking());

    act(() => {
      result.current.trackFormSubmit('contact-form', true, { fields: 3 });
    });

    expect(mockTrack).toHaveBeenCalledWith('form_submit', {
      form: 'contact-form',
      success: true,
      fields: 3,
      timestamp: expect.any(String),
    });
  });

  it('should track search with query truncation', () => {
    const { result } = renderHook(() => useInteractionTracking());
    const longQuery = 'a'.repeat(150);

    act(() => {
      result.current.trackSearch(longQuery, 5);
    });

    expect(mockTrack).toHaveBeenCalledWith('search', {
      query: 'a'.repeat(100) + '...',
      results: 5,
      timestamp: expect.any(String),
    });
  });

  it('should track short queries without truncation', () => {
    const { result } = renderHook(() => useInteractionTracking());

    act(() => {
      result.current.trackSearch('paris travel', 12);
    });

    expect(mockTrack).toHaveBeenCalledWith('search', {
      query: 'paris travel',
      results: 12,
      timestamp: expect.any(String),
    });
  });
});

describe('useFeatureTracking', () => {
  const mockTrack = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
    const { track } = require('@vercel/analytics');
    track.mockImplementation(mockTrack);
  });

  it('should track feature usage', () => {
    const { result } = renderHook(() => useFeatureTracking());

    act(() => {
      result.current.trackFeatureUsage('drag-drop', 'v2', { user_type: 'premium' });
    });

    expect(mockTrack).toHaveBeenCalledWith('feature_usage', {
      feature: 'drag-drop',
      variant: 'v2',
      user_type: 'premium',
      timestamp: expect.any(String),
    });
  });

  it('should track experiments', () => {
    const { result } = renderHook(() => useFeatureTracking());

    act(() => {
      result.current.trackExperiment('checkout-flow', 'variant-b');
    });

    expect(mockTrack).toHaveBeenCalledWith('experiment_exposure', {
      experiment: 'checkout-flow',
      variant: 'variant-b',
      timestamp: expect.any(String),
    });
  });
});