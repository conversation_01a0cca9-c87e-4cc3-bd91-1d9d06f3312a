import './globals.css';
import type { Metadata, Viewport } from 'next';
import { Inter } from 'next/font/google';
import { Toaster } from 'sonner';
import { AuthProvider } from '@/providers/AuthProvider';
import { QueryProvider } from '@/providers/QueryProvider';
import { PerformanceMonitor } from '@/components/PerformanceMonitor';
import { AnalyticsProvider } from '@/lib/analytics-provider';

const inter = Inter({ subsets: ['latin'] });

export const viewport: Viewport = {
  width: 'device-width',
  initialScale: 1,
  maximumScale: 5,
  userScalable: true,
  viewportFit: 'cover'
};

export const metadata: Metadata = {
  title: {
    default: 'TravelViz travel planner: free AI planner and GPT itinerary',
    template: '%s | TravelViz travel planner: free AI planner and GPT itinerary'
  },
  description: 'Effortlessly transform scattered travel ideas—from ChatGPT outputs to rough notes—into a single, stunning itinerary. Unlock automatic price tracking, manage your budget, and collaborate with your group, all in one place.',
  keywords: [
    'ai travel planner',
    'travel itinerary generator',
    'trip planning app',
    'interactive travel maps',
    'travel visualization',
    'chatgpt travel plans',
    'ai trip planner',
    'travel budget tracker',
    'group travel planning',
    'smart travel recommendations',
    'travel route planner',
    'vacation planner',
    'travel organizer',
    'itinerary builder',
    'travel collaboration',
    'trip sharing',
    'travel timeline',
    'destination planner',
    'travel expense tracker',
    'automated travel planning'
  ],
  authors: [{ name: 'TravelViz', url: 'https://travelviz.ai' }],
  creator: 'TravelViz',
  publisher: 'TravelViz',
  category: 'Travel Technology',
  classification: 'Travel Planning Software',
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  metadataBase: new URL('https://travelviz.ai'),
  alternates: {
    canonical: 'https://travelviz.ai',
    languages: {
      'en-US': 'https://travelviz.ai',
      'x-default': 'https://travelviz.ai'
    }
  },
  openGraph: {
    type: 'website',
    locale: 'en_US',
    url: 'https://travelviz.ai',
    title: 'TravelViz travel planner: free AI planner and GPT itinerary',
    description: 'Effortlessly transform scattered travel ideas—from ChatGPT outputs to rough notes—into a single, stunning itinerary. Unlock automatic price tracking, manage your budget, and collaborate with your group, all in one place.',
    siteName: 'TravelViz',
    images: [{
      url: '/logo.svg',
      width: 1200,
      height: 630,
      alt: 'TravelViz - AI Travel Planning Platform - Transform ChatGPT travel plans into interactive maps',
      type: 'image/svg+xml',
    }],
    emails: ['<EMAIL>'],
    phoneNumbers: [],
    faxNumbers: [],
    countryName: 'United States',
  },
  twitter: {
    card: 'summary_large_image',
    site: '@Travel_viz',
    creator: '@Travel_viz',
    title: 'TravelViz travel planner: free AI planner and GPT itinerary',
    description: 'Effortlessly transform scattered travel ideas—from ChatGPT outputs to rough notes—into a single, stunning itinerary. Unlock automatic price tracking, manage your budget, and collaborate with your group, all in one place.',
    images: {
      url: '/logo.svg',
      alt: 'TravelViz - AI Travel Planning Platform',
    },
  },

  robots: {
    index: true,
    follow: true,
    nocache: false,
    googleBot: {
      index: true,
      follow: true,
      noimageindex: false,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  icons: {
    icon: [
      { url: '/favicon.ico', sizes: 'any' },
      { url: '/icon-192.png', sizes: '192x192', type: 'image/png' },
      { url: '/icon-512.png', sizes: '512x512', type: 'image/png' },
    ],
    apple: [
      { url: '/apple-touch-icon.png', sizes: '180x180', type: 'image/png' },
    ],
    other: [
      { rel: 'mask-icon', url: '/safari-pinned-tab.svg', color: '#f97316' },
    ],
  },
  manifest: '/site.webmanifest',
  verification: {
    google: 'google-site-verification-code',
    yandex: 'yandex-verification-code',
    yahoo: 'yahoo-site-verification-code',
    other: {
      'msvalidate.01': 'bing-site-verification-code',
      'p:domain_verify': 'pinterest-site-verification-code',
    },
  },
  appleWebApp: {
    capable: true,
    title: 'TravelViz',
    statusBarStyle: 'default',
    startupImage: [
      {
        url: '/startup-image-768x1004.png',
        media: '(device-width: 768px) and (device-height: 1024px)',
      },
    ],
  },
  applicationName: 'TravelViz',
  referrer: 'origin-when-cross-origin',
  bookmarks: ['https://travelviz.ai'],
  archives: ['https://travelviz.ai/sitemap.xml'],
  assets: ['https://travelviz.ai'],
  generator: 'Next.js',
  abstract: 'Effortlessly transform scattered travel ideas—from ChatGPT outputs to rough notes—into a single, stunning itinerary. Unlock automatic price tracking, manage your budget, and collaborate with your group, all in one place.',
  appLinks: {
    web: {
      url: 'https://travelviz.ai',
      should_fallback: true,
    },
  },
  other: {
    'mobile-web-app-capable': 'yes',
    'apple-mobile-web-app-capable': 'yes',
    'apple-mobile-web-app-status-bar-style': 'default',
    'format-detection': 'telephone=no',
    'theme-color': '#ffffff',
    'color-scheme': 'light',
    'supported-color-schemes': 'light',
    // Structured Data for Rich Snippets
    'application-name': 'TravelViz',
    'msapplication-TileColor': '#f97316',
    'msapplication-TileImage': '/mstile-144x144.png',
    'msapplication-config': '/browserconfig.xml',
    // Additional SEO tags
    'revisit-after': '7 days',
    'rating': 'general',
    'distribution': 'global',
    'language': 'en',
    'geo.region': 'US',
    'geo.placename': 'United States',
    'ICBM': '39.8283, -98.5795',
    'DC.title': 'TravelViz - AI Travel Planning Platform',
    'DC.creator': 'TravelViz',
    'DC.subject': 'Travel Planning, AI, Interactive Maps, Itinerary Builder',
    'DC.description': 'Transform AI travel plans into visual adventures with interactive maps and smart planning tools',
    'DC.publisher': 'TravelViz',
    'DC.contributor': 'TravelViz Team',
    'DC.date': new Date().toISOString(),
    'DC.type': 'Software',
    'DC.format': 'text/html',
    'DC.identifier': 'https://travelviz.ai',
    'DC.language': 'en',
    'DC.coverage': 'Worldwide',
    'DC.rights': '© 2024 TravelViz. All rights reserved.',
  },
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const structuredData = {
    "@context": "https://schema.org",
    "@type": "WebApplication",
    "name": "TravelViz travel planner: free AI planner and GPT itinerary",
    "description": "Effortlessly transform scattered travel ideas—from ChatGPT outputs to rough notes—into a single, stunning itinerary. Unlock automatic price tracking, manage your budget, and collaborate with your group, all in one place.",
    "url": "https://travelviz.ai",
    "applicationCategory": "TravelApplication",
    "operatingSystem": "Web Browser",
    "offers": {
      "@type": "Offer",
      "price": "0",
      "priceCurrency": "USD",
      "availability": "https://schema.org/InStock"
    },
    "creator": {
      "@type": "Organization",
      "name": "TravelViz",
      "url": "https://travelviz.ai"
    },
    "featureList": [
      "AI-powered travel planning",
      "ChatGPT itinerary import",
      "Interactive travel maps",
      "Budget tracking",
      "Group collaboration",
      "Price monitoring",
      "Travel templates"
    ],
    "screenshot": "https://travelviz.ai/logo.svg",
    "softwareVersion": "1.0",
    "datePublished": "2024-12-19",
    "inLanguage": "en-US",
    "copyrightHolder": {
      "@type": "Organization", 
      "name": "TravelViz"
    },
    "copyrightYear": "2024",
    "license": "https://travelviz.ai/terms"
  };

  return (
    <html lang="en">
      <head>
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData) }}
        />
        <script
          data-noptimize="1"
          data-cfasync="false"
          data-wpfc-render="false"
          dangerouslySetInnerHTML={{
            __html: `
              (function () {
                  var script = document.createElement("script");
                  script.async = 1;
                  script.src = 'https://emrldtp.cc/NDMyMzUx.js?t=432351';
                  document.head.appendChild(script);
              })();
            `
          }}
        />
      </head>
              <body className={inter.className}>
          <AnalyticsProvider debug={process.env.NODE_ENV === 'development'}>
            <QueryProvider>
              <AuthProvider>
                {children}
              </AuthProvider>
            </QueryProvider>
            <Toaster position="top-right" richColors />
            <PerformanceMonitor />
          </AnalyticsProvider>
      </body>
    </html>
  );
}