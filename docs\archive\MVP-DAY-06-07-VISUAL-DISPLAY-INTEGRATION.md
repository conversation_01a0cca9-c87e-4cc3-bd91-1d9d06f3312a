# MVP Days 06-07: Visual Display Integration

**Date**: [Execute Date]  
**Goal**: Connect AI parser backend and create stunning visual displays  
**Duration**: 16 hours (2 days)  
**Critical Path**: YES - Without visuals, we're just another text itinerary

## Context & Vision

### The Visual Magic

After parsing, users see their trip come alive with:

- **Timeline**: Beautiful day-by-day breakdown with times
- **Map**: Interactive pins showing the journey
- **Cards**: Rich activity cards with images
- **Flow**: Smooth transitions between views

### Technical Architecture

```
Frontend (React)          Backend (Express)         AI Service
     │                         │                        │
     ├─[1]─POST /parse────────>├─[2]─Parse Request────>│
     │                         │                        │
     ├─[3]<──SSE Updates───────├<──Progress Events─────┤
     │                         │                        │
     └─[4]<──Parsed Data───────└<──Final Result────────┘
```

### Performance Requirements

#### Map Performance (WebGL Optimization)

- **Target**: 60fps with 1000+ markers
- **Clustering**: Dynamic clustering above 100 markers
- **Lazy Loading**: Load markers in viewport only
- **WebGL Layer**: Custom WebGL layer for complex visualizations
- **Memory Management**: Dispose unused markers, prevent leaks

#### Timeline Architecture (Virtual Scrolling)

- **Target**: Smooth scrolling with 100+ activities
- **Virtual DOM**: Only render visible items ±2 viewport
- **Drag Performance**: RequestAnimationFrame for 60fps drag
- **Optimistic Updates**: Update state before server confirms
- **Batch Updates**: Debounce rapid changes to 16ms intervals

#### Data Synchronization

- **Real-time Protocol**: WebSocket with automatic reconnection
- **Conflict Resolution**: Last-write-wins with version vectors
- **Offline Queue**: IndexedDB for offline changes
- **Sync Strategy**: Binary diff for minimal data transfer
- **State Machine**: Predictable sync states with rollback

#### Mobile Responsiveness

- **Touch Optimization**: 44px minimum touch targets
- **Gesture Handling**: Native momentum scrolling, pinch-zoom map
- **Viewport Management**: Prevent zoom on input focus
- **Performance Budget**: 3s Time to Interactive on 3G
- **Adaptive Quality**: Reduce map detail on low-end devices

#### Accessibility (WCAG 2.1 AA)

- **Screen Reader**: Proper ARIA labels and live regions
- **Keyboard Nav**: Full keyboard navigation with focus indicators
- **Color Contrast**: 4.5:1 minimum contrast ratio
- **Motion Settings**: Respect prefers-reduced-motion
- **Focus Management**: Trap focus in modals, restore on close

## Day 6 Morning: Backend Parser Service (4 hours)

### Task 1: AI Parser Configuration (1 hour)

**File**: `packages/hub/src/config/ai.config.ts`

```typescript
export const AI_CONFIG = {
  provider: 'openrouter',
  apiKey: process.env.OPENROUTER_API_KEY!,
  model: process.env.AI_MODEL || 'anthropic/claude-3-haiku',
  fallbackModel: 'deepseek/deepseek-chat-v3-0324:free',

  parsing: {
    maxTokens: 4000,
    temperature: 0.3, // Lower = more consistent
    timeout: 30000, // 30 seconds
    retryAttempts: 2,
    retryDelay: 1000,
  },

  systemPrompt: `You are an expert travel itinerary parser. Extract structured trip data from conversations.

IMPORTANT RULES:
1. Extract specific dates and times when mentioned
2. Identify activity types: flight, hotel, activity, transport, food
3. Extract prices in original currency
4. Find GPS coordinates for locations when possible
5. Maintain chronological order
6. Mark confidence level for each extracted item

Return JSON in this exact format:
{
  "title": "Trip title",
  "description": "Brief description",
  "startDate": "YYYY-MM-DD",
  "endDate": "YYYY-MM-DD", 
  "destination": "Primary destination",
  "activities": [{
    "name": "Activity name",
    "description": "Details",
    "type": "flight|hotel|activity|transport|food|other",
    "startTime": "YYYY-MM-DDTHH:mm:ss",
    "endTime": "YYYY-MM-DDTHH:mm:ss",
    "location": {
      "address": "Full address",
      "lat": 0.0,
      "lng": 0.0
    },
    "price": 0,
    "currency": "USD",
    "bookingUrl": "url if mentioned",
    "confidence": 0.0-1.0,
    "dayNumber": 1,
    "originalText": "relevant excerpt"
  }],
  "metadata": {
    "source": "chatgpt|claude|gemini|unknown",
    "confidence": 0.0-1.0,
    "warnings": ["list of issues"]
  }
}`,
};

// Validate environment
if (!process.env.OPENROUTER_API_KEY) {
  console.error('OPENROUTER_API_KEY is required for AI parsing');
  process.exit(1);
}
```

### Task 2: Parser Service Implementation (1.5 hours)

**File**: `packages/hub/src/services/ai-parser.service.ts`

````typescript
import { AI_CONFIG } from '../config/ai.config';
import { Redis } from '@upstash/redis';
import { v4 as uuidv4 } from 'uuid';
import OpenAI from 'openai';

const openai = new OpenAI({
  baseURL: 'https://openrouter.ai/api/v1',
  apiKey: AI_CONFIG.apiKey,
  defaultHeaders: {
    'HTTP-Referer': 'https://travelviz.app',
    'X-Title': 'TravelViz',
  },
});

const redis = new Redis({
  url: process.env.UPSTASH_REDIS_URL!,
  token: process.env.UPSTASH_REDIS_TOKEN!,
});

export interface ParseSession {
  id: string;
  status: 'pending' | 'processing' | 'complete' | 'error';
  progress: number;
  currentStep: string;
  result?: any;
  error?: string;
  startedAt: Date;
  completedAt?: Date;
}

export class AIParserService {
  async createParseSession(content: string, source: string): Promise<string> {
    const sessionId = uuidv4();
    const session: ParseSession = {
      id: sessionId,
      status: 'pending',
      progress: 0,
      currentStep: 'initializing',
      startedAt: new Date(),
    };

    // Store session in Redis
    await redis.setex(
      `parse:${sessionId}`,
      3600, // 1 hour TTL
      JSON.stringify(session)
    );

    // Start async parsing
    this.parseAsync(sessionId, content, source);

    return sessionId;
  }

  private async parseAsync(sessionId: string, content: string, source: string) {
    try {
      // Update progress: Starting
      await this.updateProgress(sessionId, 'extracting', 10, 'Analyzing conversation structure...');

      // Pre-process content
      const processedContent = this.preprocessContent(content, source);

      // Update progress: Parsing
      await this.updateProgress(sessionId, 'parsing', 30, 'Extracting trip details...');

      // Call AI API
      const completion = await openai.chat.completions.create({
        model: AI_CONFIG.model,
        messages: [
          { role: 'system', content: AI_CONFIG.systemPrompt },
          { role: 'user', content: `Parse this travel conversation:\n\n${processedContent}` },
        ],
        max_tokens: AI_CONFIG.parsing.maxTokens,
        temperature: AI_CONFIG.parsing.temperature,
        response_format: { type: 'json_object' },
      });

      const parsedData = JSON.parse(completion.choices[0].message.content || '{}');

      // Update progress: Enhancing
      await this.updateProgress(sessionId, 'enhancing', 60, 'Adding location details...');

      // Enhance with geocoding
      const enhanced = await this.enhanceWithGeocoding(parsedData);

      // Update progress: Validating
      await this.updateProgress(sessionId, 'validating', 80, 'Validating dates and times...');

      // Validate and fix data
      const validated = this.validateAndFix(enhanced);

      // Update progress: Finalizing
      await this.updateProgress(sessionId, 'finalizing', 95, 'Creating your itinerary...');

      // Complete session
      await this.completeSession(sessionId, validated);
    } catch (error: any) {
      console.error('Parse error:', error);

      // Try fallback model if primary fails
      if (error.code === 'rate_limit_exceeded' && AI_CONFIG.fallbackModel) {
        console.log('Trying fallback model...');
        // Retry with fallback model
        await this.parseWithFallback(sessionId, content, source);
      } else {
        await this.failSession(sessionId, error.message || 'Failed to parse conversation');
      }
    }
  }

  private preprocessContent(content: string, source: string): string {
    // Remove unnecessary formatting
    let processed = content
      .replace(/```[\s\S]*?```/g, '') // Remove code blocks
      .replace(/\*\*/g, '') // Remove bold
      .replace(/\n{3,}/g, '\n\n'); // Normalize line breaks

    // Add source hint
    if (source !== 'unknown') {
      processed = `[Source: ${source}]\n\n${processed}`;
    }

    // Truncate if too long
    if (processed.length > 10000) {
      processed = processed.substring(0, 10000) + '\n...[truncated]';
    }

    return processed;
  }

  private async enhanceWithGeocoding(data: any): Promise<any> {
    // In production, use a geocoding service
    // For MVP, return with estimated coordinates

    const enhanced = { ...data };

    if (enhanced.activities) {
      enhanced.activities = enhanced.activities.map((activity: any) => {
        if (activity.location && !activity.location.lat) {
          // Estimate coordinates based on destination
          const coords = this.estimateCoordinates(
            activity.location.address || activity.name,
            data.destination
          );

          activity.location = {
            ...activity.location,
            ...coords,
          };
        }
        return activity;
      });
    }

    return enhanced;
  }

  private estimateCoordinates(location: string, destination: string): { lat: number; lng: number } {
    // Basic coordinate estimation for common destinations
    const cityCoords: Record<string, { lat: number; lng: number }> = {
      paris: { lat: 48.8566, lng: 2.3522 },
      rome: { lat: 41.9028, lng: 12.4964 },
      london: { lat: 51.5074, lng: -0.1278 },
      tokyo: { lat: 35.6762, lng: 139.6503 },
      'new york': { lat: 40.7128, lng: -74.006 },
      barcelona: { lat: 41.3851, lng: 2.1734 },
      amsterdam: { lat: 52.3676, lng: 4.9041 },
      bangkok: { lat: 13.7563, lng: 100.5018 },
    };

    const destLower = destination.toLowerCase();
    for (const [city, coords] of Object.entries(cityCoords)) {
      if (destLower.includes(city)) {
        // Add small random offset for different locations
        return {
          lat: coords.lat + (Math.random() - 0.5) * 0.1,
          lng: coords.lng + (Math.random() - 0.5) * 0.1,
        };
      }
    }

    // Default coordinates
    return { lat: 0, lng: 0 };
  }

  private validateAndFix(data: any): any {
    const validated = { ...data };
    const warnings: string[] = [];

    // Ensure dates are valid
    if (!validated.startDate || !validated.endDate) {
      warnings.push('Missing trip dates - using defaults');
      const today = new Date();
      validated.startDate = today.toISOString().split('T')[0];
      validated.endDate = new Date(today.getTime() + 7 * 24 * 60 * 60 * 1000)
        .toISOString()
        .split('T')[0];
    }

    // Sort activities by date/time
    if (validated.activities) {
      validated.activities.sort((a: any, b: any) => {
        return new Date(a.startTime).getTime() - new Date(b.startTime).getTime();
      });

      // Assign day numbers
      const startDate = new Date(validated.startDate);
      validated.activities = validated.activities.map((activity: any, index: number) => {
        const activityDate = new Date(activity.startTime);
        const dayNumber =
          Math.ceil((activityDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24)) + 1;

        return {
          ...activity,
          dayNumber: Math.max(1, dayNumber),
          position: index,
        };
      });
    }

    // Add metadata
    validated.metadata = {
      ...validated.metadata,
      warnings,
      parseDate: new Date().toISOString(),
      version: '1.0',
    };

    return validated;
  }

  private async updateProgress(sessionId: string, step: string, progress: number, message: string) {
    const session = await this.getSession(sessionId);
    if (!session) return;

    session.currentStep = step;
    session.progress = progress;

    await redis.setex(`parse:${sessionId}`, 3600, JSON.stringify(session));

    // Publish SSE event
    await redis.publish(
      `parse:progress:${sessionId}`,
      JSON.stringify({
        type: 'progress',
        step,
        progress,
        message,
      })
    );
  }

  private async completeSession(sessionId: string, result: any) {
    const session = await this.getSession(sessionId);
    if (!session) return;

    session.status = 'complete';
    session.progress = 100;
    session.result = result;
    session.completedAt = new Date();

    await redis.setex(`parse:${sessionId}`, 3600, JSON.stringify(session));

    // Publish completion event
    await redis.publish(
      `parse:progress:${sessionId}`,
      JSON.stringify({
        type: 'complete',
        result,
      })
    );
  }

  private async failSession(sessionId: string, error: string) {
    const session = await this.getSession(sessionId);
    if (!session) return;

    session.status = 'error';
    session.error = error;
    session.completedAt = new Date();

    await redis.setex(`parse:${sessionId}`, 3600, JSON.stringify(session));

    // Publish error event
    await redis.publish(
      `parse:progress:${sessionId}`,
      JSON.stringify({
        type: 'error',
        message: error,
      })
    );
  }

  async getSession(sessionId: string): Promise<ParseSession | null> {
    const data = await redis.get(`parse:${sessionId}`);
    return data ? JSON.parse(data as string) : null;
  }
}
````

### Task 3: Import API Routes (1.5 hours)

**File**: `packages/hub/src/routes/import.routes.ts`

```typescript
import { Router } from 'express';
import { authenticate } from '../middleware/auth.middleware';
import { AIParserService } from '../services/ai-parser.service';
import { supabase } from '../config/supabase';
import { createSuccessResponse, createErrorResponse } from '@travelviz/shared';
import { v4 as uuidv4 } from 'uuid';

const router = Router();
const parserService = new AIParserService();

// POST /api/v1/import/parse
router.post('/parse', authenticate, async (req, res) => {
  try {
    const { content, source = 'unknown' } = req.body;

    if (!content || content.length < 100) {
      return res
        .status(400)
        .json(createErrorResponse('Content too short. Please provide a complete conversation.'));
    }

    if (content.length > 50000) {
      return res
        .status(400)
        .json(createErrorResponse('Content too long. Please limit to 50,000 characters.'));
    }

    // Create parse session
    const sessionId = await parserService.createParseSession(content, source);

    return res.json(
      createSuccessResponse({
        importId: sessionId,
        message: 'Parsing started',
      })
    );
  } catch (error) {
    console.error('Parse initiation error:', error);
    return res.status(500).json(createErrorResponse('Failed to start parsing'));
  }
});

// GET /api/v1/import/:importId/progress (SSE)
router.get('/:importId/progress', authenticate, async (req, res) => {
  const { importId } = req.params;

  // Set SSE headers
  res.setHeader('Content-Type', 'text/event-stream');
  res.setHeader('Cache-Control', 'no-cache');
  res.setHeader('Connection', 'keep-alive');
  res.setHeader('X-Accel-Buffering', 'no'); // Disable Nginx buffering

  // Send initial connection
  res.write(':ok\n\n');

  // Check session exists
  const session = await parserService.getSession(importId);
  if (!session) {
    res.write(
      `event: error\ndata: ${JSON.stringify({
        type: 'error',
        message: 'Invalid import session',
      })}\n\n`
    );
    return res.end();
  }

  // If already complete, send result
  if (session.status === 'complete') {
    res.write(
      `event: message\ndata: ${JSON.stringify({
        type: 'complete',
        result: session.result,
      })}\n\n`
    );
    return res.end();
  }

  // Subscribe to progress updates
  const redis = new Redis({
    url: process.env.UPSTASH_REDIS_URL!,
    token: process.env.UPSTASH_REDIS_TOKEN!,
  });

  const subscription = await redis.subscribe(`parse:progress:${importId}`, message => {
    res.write(`event: message\ndata: ${message}\n\n`);

    // Check if complete
    const data = JSON.parse(message);
    if (data.type === 'complete' || data.type === 'error') {
      subscription.unsubscribe();
      res.end();
    }
  });

  // Clean up on disconnect
  req.on('close', () => {
    subscription.unsubscribe();
    res.end();
  });

  // Heartbeat to keep connection alive
  const heartbeat = setInterval(() => {
    res.write(':heartbeat\n\n');
  }, 30000);

  req.on('close', () => {
    clearInterval(heartbeat);
  });
});

// POST /api/v1/import/:importId/create-trip
router.post('/:importId/create-trip', authenticate, async (req, res) => {
  try {
    const { importId } = req.params;
    const userId = req.user!.id;
    const { edits } = req.body;

    // Get parse session
    const session = await parserService.getSession(importId);
    if (!session || session.status !== 'complete') {
      return res.status(400).json(createErrorResponse('Import session not found or incomplete'));
    }

    const parsedData = session.result;

    // Apply any edits
    const tripData = edits ? { ...parsedData, ...edits } : parsedData;

    // Create trip in database
    const tripId = uuidv4();
    const { error: tripError } = await supabase.from('trips').insert({
      id: tripId,
      user_id: userId,
      title: tripData.title,
      description: tripData.description,
      start_date: tripData.startDate,
      end_date: tripData.endDate,
      destination: tripData.destination,
      is_public: false,
      import_source: parsedData.metadata.source,
      import_id: importId,
    });

    if (tripError) throw tripError;

    // Create activities
    if (tripData.activities && tripData.activities.length > 0) {
      const activities = tripData.activities.map((activity: any) => ({
        id: uuidv4(),
        trip_id: tripId,
        type: activity.type,
        name: activity.name,
        description: activity.description,
        start_time: activity.startTime,
        end_time: activity.endTime,
        location: activity.location,
        price: activity.price,
        currency: activity.currency,
        booking_url: activity.bookingUrl,
        affiliate_url: activity.affiliateUrl,
        position: activity.position,
        day_number: activity.dayNumber,
        confidence_score: activity.confidence,
      }));

      const { error: activitiesError } = await supabase.from('activities').insert(activities);

      if (activitiesError) throw activitiesError;
    }

    return res.json(
      createSuccessResponse({
        tripId,
        message: 'Trip created successfully',
      })
    );
  } catch (error) {
    console.error('Trip creation error:', error);
    return res.status(500).json(createErrorResponse('Failed to create trip'));
  }
});

export default router;
```

## Day 6 Afternoon: Frontend Timeline Component (4 hours)

### Task 4: Virtual Scrolling Timeline Component (2 hours)

**File**: `packages/web/src/components/timeline/VirtualTimeline.tsx`

```typescript
'use client';

import { useState, useRef, useCallback, useEffect, useMemo } from 'react';
import { format } from 'date-fns';
import { cn } from '@/lib/utils';
import {
  Calendar,
  Clock,
  MapPin,
  Plane,
  Hotel,
  Camera,
  Car,
  Utensils,
  DollarSign,
  ChevronDown,
  ChevronUp,
  GripVertical
} from 'lucide-react';
import { ActivityType } from '@travelviz/shared';
import { DndContext, closestCenter, KeyboardSensor, PointerSensor, useSensor, useSensors } from '@dnd-kit/core';
import { SortableContext, sortableKeyboardCoordinates, verticalListSortingStrategy } from '@dnd-kit/sortable';
import { useSortable } from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { useVirtualizer } from '@tanstack/react-virtual';

interface TimelineActivity {
  id: string;
  name: string;
  description?: string;
  type: ActivityType;
  startTime: Date | string;
  endTime?: Date | string;
  location?: {
    address?: string;
    lat?: number;
    lng?: number;
  };
  price?: number;
  currency?: string;
  dayNumber: number;
  position: number;
  confidence?: number;
}

interface VirtualTimelineProps {
  activities: TimelineActivity[];
  onActivityClick?: (activity: TimelineActivity) => void;
  onReorder?: (activities: TimelineActivity[]) => void;
  compact?: boolean;
  editable?: boolean;
}

const ACTIVITY_ICONS: Record<ActivityType, any> = {
  flight: Plane,
  hotel: Hotel,
  activity: Camera,
  transport: Car,
  food: Utensils,
  other: Calendar
};

const ACTIVITY_COLORS: Record<ActivityType, string> = {
  flight: 'bg-blue-100 text-blue-700 border-blue-200',
  hotel: 'bg-purple-100 text-purple-700 border-purple-200',
  activity: 'bg-green-100 text-green-700 border-green-200',
  transport: 'bg-orange-100 text-orange-700 border-orange-200',
  food: 'bg-pink-100 text-pink-700 border-pink-200',
  other: 'bg-gray-100 text-gray-700 border-gray-200'
};

// Sortable Activity Item for drag-and-drop
function SortableActivityItem({
  activity,
  onActivityClick,
  editable,
  compact
}: {
  activity: TimelineActivity;
  onActivityClick?: (activity: TimelineActivity) => void;
  editable?: boolean;
  compact?: boolean;
}) {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({ id: activity.id });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: isDragging ? 0.5 : 1,
  };

  const Icon = ACTIVITY_ICONS[activity.type];
  const isLowConfidence = activity.confidence && activity.confidence < 0.6;

  return (
    <div
      ref={setNodeRef}
      style={style}
      className={cn(
        "relative pl-6 -ml-px cursor-pointer group",
        onActivityClick && "hover:bg-gray-50 rounded-lg transition-colors",
        isDragging && "z-50"
      )}
      onClick={() => onActivityClick?.(activity)}
    >
      {/* Drag Handle */}
      {editable && (
        <div
          {...attributes}
          {...listeners}
          className="absolute -left-8 top-5 p-1 opacity-0 group-hover:opacity-100 transition-opacity cursor-grab"
        >
          <GripVertical className="w-4 h-4 text-gray-400" />
        </div>
      )}

      {/* Timeline Dot */}
      <div className="absolute -left-[9px] top-5 w-4 h-4 bg-white border-2 border-gray-300 rounded-full" />

      {/* Activity Card */}
      <div className="pb-3">
        {/* Time */}
        <div className="flex items-center gap-2 text-sm text-gray-600 mb-1">
          <Clock className="w-3 h-3" />
          <span>{format(new Date(activity.startTime), 'h:mm a')}</span>
          {activity.endTime && (
            <>
              <span>-</span>
              <span>{format(new Date(activity.endTime), 'h:mm a')}</span>
            </>
          )}
        </div>

        {/* Main Content */}
        <div className={cn(
          "p-3 rounded-lg border",
          ACTIVITY_COLORS[activity.type],
          isLowConfidence && "opacity-75"
        )}>
          <div className="flex items-start gap-3">
            <Icon className="w-5 h-5 flex-shrink-0 mt-0.5" />
            <div className="flex-1 min-w-0">
              <h4 className="font-medium">{activity.name}</h4>

              {activity.description && !compact && (
                <p className="text-sm mt-1 opacity-90">
                  {activity.description}
                </p>
              )}

              {activity.location?.address && (
                <div className="flex items-center gap-1 mt-2 text-sm opacity-75">
                  <MapPin className="w-3 h-3" />
                  <span className="truncate">{activity.location.address}</span>
                </div>
              )}

              {activity.price && (
                <div className="flex items-center gap-1 mt-1 text-sm font-medium">
                  <DollarSign className="w-3 h-3" />
                  <span>
                    {activity.price} {activity.currency || 'USD'}
                  </span>
                </div>
              )}

              {isLowConfidence && (
                <p className="text-xs mt-2 italic">
                  Low confidence - please verify
                </p>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export function VirtualTimeline({
  activities,
  onActivityClick,
  onReorder,
  compact = false,
  editable = false
}: VirtualTimelineProps) {
  const [expandedDays, setExpandedDays] = useState<Set<number>>(new Set([1]));
  const parentRef = useRef<HTMLDivElement>(null);

  // Setup drag sensors
  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 8,
      },
    }),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );

  // Group and prepare activities
  const { activitiesByDay, flatActivities, dayHeaders } = useMemo(() => {
    const byDay = activities.reduce((acc, activity) => {
      const day = activity.dayNumber;
      if (!acc[day]) acc[day] = [];
      acc[day].push(activity);
      return acc;
    }, {} as Record<number, TimelineActivity[]>);

    // Sort activities within each day
    Object.keys(byDay).forEach(day => {
      byDay[Number(day)].sort((a, b) => {
        const timeA = new Date(a.startTime).getTime();
        const timeB = new Date(b.startTime).getTime();
        return timeA - timeB || a.position - b.position;
      });
    });

    // Flatten for virtual scrolling
    const flat: (TimelineActivity | { type: 'dayHeader', day: number })[] = [];
    const headers = new Map<number, number>();

    Object.entries(byDay).forEach(([dayStr, dayActivities]) => {
      const day = Number(dayStr);
      headers.set(flat.length, day);
      flat.push({ type: 'dayHeader', day });

      if (expandedDays.has(day)) {
        flat.push(...dayActivities);
      }
    });

    return { activitiesByDay: byDay, flatActivities: flat, dayHeaders: headers };
  }, [activities, expandedDays]);

  // Virtual scrolling setup
  const rowVirtualizer = useVirtualizer({
    count: flatActivities.length,
    getScrollElement: () => parentRef.current,
    estimateSize: (index) => {
      const item = flatActivities[index];
      if ('type' in item && item.type === 'dayHeader') {
        return 60; // Day header height
      }
      return compact ? 100 : 150; // Activity height
    },
    overscan: 5,
  });

  // Handle drag end
  const handleDragEnd = useCallback((event: any) => {
    const { active, over } = event;

    if (active.id !== over.id) {
      const oldIndex = activities.findIndex(a => a.id === active.id);
      const newIndex = activities.findIndex(a => a.id === over.id);

      if (oldIndex !== -1 && newIndex !== -1) {
        const newActivities = [...activities];
        const [movedActivity] = newActivities.splice(oldIndex, 1);
        newActivities.splice(newIndex, 0, movedActivity);

        // Update positions
        newActivities.forEach((activity, index) => {
          activity.position = index;
        });

        onReorder?.(newActivities);
      }
    }
  }, [activities, onReorder]);

  const toggleDay = (day: number) => {
    const newExpanded = new Set(expandedDays);
    if (newExpanded.has(day)) {
      newExpanded.delete(day);
    } else {
      newExpanded.add(day);
    }
    setExpandedDays(newExpanded);
  };

  const getDayDate = (dayNumber: number) => {
    const firstActivity = activities.find(a => a.dayNumber === dayNumber);
    if (firstActivity) {
      return format(new Date(firstActivity.startTime), 'MMM d, yyyy');
    }
    return `Day ${dayNumber}`;
  };

  return (
    <DndContext
      sensors={sensors}
      collisionDetection={closestCenter}
      onDragEnd={handleDragEnd}
    >
      <div
        ref={parentRef}
        className="h-[600px] overflow-auto"
        style={{
          contain: 'strict',
          overscrollBehavior: 'contain'
        }}
      >
        <div
          style={{
            height: `${rowVirtualizer.getTotalSize()}px`,
            width: '100%',
            position: 'relative',
          }}
        >
          <SortableContext
            items={activities.map(a => a.id)}
            strategy={verticalListSortingStrategy}
          >
            {rowVirtualizer.getVirtualItems().map((virtualItem) => {
              const item = flatActivities[virtualItem.index];

              if ('type' in item && item.type === 'dayHeader') {
                const dayActivities = activitiesByDay[item.day];
                const isExpanded = expandedDays.has(item.day);
                const dayTotal = dayActivities.reduce((sum, a) => sum + (a.price || 0), 0);

                return (
                  <div
                    key={`day-${item.day}`}
                    style={{
                      position: 'absolute',
                      top: 0,
                      left: 0,
                      width: '100%',
                      height: `${virtualItem.size}px`,
                      transform: `translateY(${virtualItem.start}px)`,
                    }}
                  >
                    <button
                      onClick={() => toggleDay(item.day)}
                      className="w-full px-4 py-3 flex items-center justify-between hover:bg-gray-50 transition-colors bg-white border rounded-lg"
                    >
                      <div className="flex items-center gap-3">
                        <div className="flex items-center justify-center w-8 h-8 bg-blue-600 text-white rounded-full text-sm font-bold">
                          {item.day}
                        </div>
                        <div className="text-left">
                          <h3 className="font-semibold">Day {item.day}</h3>
                          <p className="text-sm text-gray-600">{getDayDate(item.day)}</p>
                        </div>
                      </div>
                      <div className="flex items-center gap-3">
                        <span className="text-sm text-gray-500">
                          {dayActivities.length} activities
                        </span>
                        {dayTotal > 0 && (
                          <span className="text-sm font-medium text-gray-700">
                            ${dayTotal}
                          </span>
                        )}
                        {isExpanded ? (
                          <ChevronUp className="w-5 h-5 text-gray-400" />
                        ) : (
                          <ChevronDown className="w-5 h-5 text-gray-400" />
                        )}
                      </div>
                    </button>
                  </div>
                );
              } else {
                const activity = item as TimelineActivity;

                return (
                  <div
                    key={activity.id}
                    style={{
                      position: 'absolute',
                      top: 0,
                      left: 0,
                      width: '100%',
                      height: `${virtualItem.size}px`,
                      transform: `translateY(${virtualItem.start}px)`,
                      paddingLeft: '2rem',
                      paddingRight: '1rem',
                    }}
                  >
                    <div className="border-l-2 border-gray-200 ml-4 h-full">
                      <SortableActivityItem
                        activity={activity}
                        onActivityClick={onActivityClick}
                        editable={editable}
                        compact={compact}
                      />
                    </div>
                  </div>
                );
              }
            })}
          </SortableContext>
        </div>
      </div>

      {/* Summary */}
      {!compact && activities.length > 0 && (
        <div className="bg-gray-50 rounded-lg p-4 mt-4">
          <div className="flex items-center justify-between text-sm">
            <span className="text-gray-600">
              Total: {activities.length} activities across {Object.keys(activitiesByDay).length} days
            </span>
            <span className="font-semibold">
              ${activities.reduce((sum, a) => sum + (a.price || 0), 0)} estimated
            </span>
          </div>
        </div>
      )}
    </DndContext>
  );
}

/**
 * Hook for real-time data synchronization
 */
export function useRealtimeSync(tripId: string) {
  const [syncState, setSyncState] = useState<'idle' | 'syncing' | 'error'>('idle');
  const [lastSync, setLastSync] = useState<Date | null>(null);
  const wsRef = useRef<WebSocket | null>(null);
  const reconnectTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const syncQueueRef = useRef<any[]>([]);

  // Connect to WebSocket
  useEffect(() => {
    const connect = () => {
      const ws = new WebSocket(`${process.env.NEXT_PUBLIC_WS_URL}/trips/${tripId}`);

      ws.onopen = () => {
        console.log('WebSocket connected');
        setSyncState('idle');
        // Send any queued updates
        syncQueueRef.current.forEach(update => {
          ws.send(JSON.stringify(update));
        });
        syncQueueRef.current = [];
      };

      ws.onmessage = (event) => {
        const data = JSON.parse(event.data);

        switch (data.type) {
          case 'activity-update':
            // Handle activity update
            break;
          case 'activity-reorder':
            // Handle reorder
            break;
          case 'sync-complete':
            setLastSync(new Date());
            setSyncState('idle');
            break;
        }
      };

      ws.onerror = () => {
        setSyncState('error');
      };

      ws.onclose = () => {
        // Attempt reconnection
        reconnectTimeoutRef.current = setTimeout(connect, 5000);
      };

      wsRef.current = ws;
    };

    connect();

    return () => {
      if (reconnectTimeoutRef.current) {
        clearTimeout(reconnectTimeoutRef.current);
      }
      wsRef.current?.close();
    };
  }, [tripId]);

  // Send update function
  const sendUpdate = useCallback((update: any) => {
    if (wsRef.current?.readyState === WebSocket.OPEN) {
      setSyncState('syncing');
      wsRef.current.send(JSON.stringify(update));
    } else {
      // Queue update if not connected
      syncQueueRef.current.push(update);
    }
  }, []);

  return { syncState, lastSync, sendUpdate };
}
```

### Task 5: High-Performance Map Component with WebGL (2 hours)

**File**: `packages/web/src/components/map/PerformantMap.tsx`

```typescript
'use client';

import { useEffect, useRef, useState, useCallback } from 'react';
import mapboxgl from 'mapbox-gl';
import 'mapbox-gl/dist/mapbox-gl.css';
import { ActivityType } from '@travelviz/shared';
import { useMapCluster } from '@/hooks/useMapCluster';
import { useWebGLRenderer } from '@/hooks/useWebGLRenderer';

// Initialize Mapbox
mapboxgl.accessToken = process.env.NEXT_PUBLIC_MAPBOX_TOKEN || '';

interface MapActivity {
  id: string;
  name: string;
  type: ActivityType;
  location?: {
    lat?: number;
    lng?: number;
    address?: string;
  };
  dayNumber: number;
}

interface PerformantMapProps {
  activities: MapActivity[];
  height?: string;
  interactive?: boolean;
  showRoute?: boolean;
  maxConcurrentMarkers?: number;
}

const ACTIVITY_COLORS: Record<ActivityType, string> = {
  flight: '#3B82F6', // blue
  hotel: '#8B5CF6', // purple
  activity: '#10B981', // green
  transport: '#F97316', // orange
  food: '#EC4899', // pink
  other: '#6B7280' // gray
};

// WebGL Custom Layer for high-performance rendering
class ActivityClusterLayer {
  id = 'activity-clusters';
  type = 'custom' as const;
  renderingMode = '3d' as const;

  constructor(private activities: MapActivity[]) {}

  onAdd(map: mapboxgl.Map, gl: WebGLRenderingContext) {
    // Initialize WebGL shaders for cluster rendering
    const vertexShader = gl.createShader(gl.VERTEX_SHADER)!;
    gl.shaderSource(vertexShader, `
      attribute vec2 a_pos;
      attribute float a_size;
      attribute vec3 a_color;

      uniform mat4 u_matrix;
      varying vec3 v_color;

      void main() {
        gl_Position = u_matrix * vec4(a_pos, 0.0, 1.0);
        gl_PointSize = a_size;
        v_color = a_color;
      }
    `);
    gl.compileShader(vertexShader);

    const fragmentShader = gl.createShader(gl.FRAGMENT_SHADER)!;
    gl.shaderSource(fragmentShader, `
      precision mediump float;
      varying vec3 v_color;

      void main() {
        vec2 coord = gl_PointCoord - vec2(0.5);
        if (length(coord) > 0.5) discard;
        gl_FragColor = vec4(v_color, 1.0);
      }
    `);
    gl.compileShader(fragmentShader);

    // Create and link program
    this.program = gl.createProgram()!;
    gl.attachShader(this.program, vertexShader);
    gl.attachShader(this.program, fragmentShader);
    gl.linkProgram(this.program);
  }

  render(gl: WebGLRenderingContext, matrix: number[]) {
    // Implement WebGL rendering for thousands of markers
    gl.useProgram(this.program);
    // ... rendering implementation
  }

  private program: WebGLProgram | null = null;
}

export function PerformantMap({
  activities,
  height = '400px',
  interactive = true,
  showRoute = true,
  maxConcurrentMarkers = 100
}: PerformantMapProps) {
  const mapContainer = useRef<HTMLDivElement>(null);
  const map = useRef<mapboxgl.Map | null>(null);
  const markersRef = useRef<Map<string, mapboxgl.Marker>>(new Map());
  const [mapLoaded, setMapLoaded] = useState(false);
  const { clusters, supercluster } = useMapCluster(activities);
  const webglRenderer = useWebGLRenderer();

  // Viewport tracking for lazy loading
  const [viewport, setViewport] = useState<mapboxgl.LngLatBounds | null>(null);

  // Memory management: cleanup markers outside viewport
  const cleanupMarkersOutsideViewport = useCallback(() => {
    if (!map.current || !viewport) return;

    let removedCount = 0;
    markersRef.current.forEach((marker, id) => {
      const lngLat = marker.getLngLat();
      if (!viewport.contains(lngLat)) {
        marker.remove();
        markersRef.current.delete(id);
        removedCount++;
      }
    });

    if (removedCount > 0) {
      console.debug(`Cleaned up ${removedCount} markers outside viewport`);
    }
  }, [viewport]);

  // Efficient marker rendering with RequestAnimationFrame
  const renderVisibleMarkers = useCallback(() => {
    if (!map.current || !viewport) return;

    requestAnimationFrame(() => {
      const visibleActivities = activities.filter(activity => {
        if (!activity.location?.lat || !activity.location?.lng) return false;
        const lngLat = new mapboxgl.LngLat(activity.location.lng, activity.location.lat);
        return viewport.contains(lngLat);
      });

      // Use clustering for large datasets
      if (visibleActivities.length > maxConcurrentMarkers) {
        const zoom = map.current!.getZoom();
        const clusteredData = supercluster.getClusters(
          viewport.toArray().flat(),
          Math.floor(zoom)
        );

        // Render clusters instead of individual markers
        clusteredData.forEach(cluster => {
          if (cluster.properties.cluster) {
            renderCluster(cluster);
          } else {
            renderMarker(visibleActivities.find(a => a.id === cluster.properties.id)!);
          }
        });
      } else {
        // Render individual markers for small datasets
        visibleActivities.forEach(renderMarker);
      }

      // Cleanup memory
      cleanupMarkersOutsideViewport();
    });
  }, [activities, viewport, maxConcurrentMarkers, cleanupMarkersOutsideViewport]);

  const renderMarker = useCallback((activity: MapActivity) => {
    if (!map.current || markersRef.current.has(activity.id)) return;

    const el = document.createElement('div');
    el.className = 'marker';
    el.style.cssText = `
      background-color: ${ACTIVITY_COLORS[activity.type]};
      width: 24px;
      height: 24px;
      border-radius: 50%;
      border: 2px solid white;
      box-shadow: 0 2px 4px rgba(0,0,0,0.2);
      cursor: pointer;
      will-change: transform;
      transform: translate3d(0, 0, 0);
    `;

    // Optimize day number rendering
    const dayLabel = document.createElement('div');
    dayLabel.textContent = activity.dayNumber.toString();
    dayLabel.style.cssText = `
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      color: white;
      font-size: 10px;
      font-weight: bold;
      pointer-events: none;
    `;
    el.appendChild(dayLabel);

    const marker = new mapboxgl.Marker(el, {
      anchor: 'center'
    })
      .setLngLat([activity.location!.lng!, activity.location!.lat!])
      .setPopup(
        new mapboxgl.Popup({
          offset: 25,
          closeButton: false,
          maxWidth: '300px'
        }).setHTML(`
          <div class="p-2">
            <h3 class="font-semibold">${activity.name}</h3>
            ${activity.location!.address ? `<p class="text-sm text-gray-600 mt-1">${activity.location!.address}</p>` : ''}
            <p class="text-xs text-gray-500 mt-1">Day ${activity.dayNumber}</p>
          </div>
        `)
      )
      .addTo(map.current!);

    markersRef.current.set(activity.id, marker);
  }, []);

  const renderCluster = useCallback((cluster: any) => {
    if (!map.current) return;

    const [lng, lat] = cluster.geometry.coordinates;
    const count = cluster.properties.point_count;

    const el = document.createElement('div');
    el.className = 'cluster-marker';
    el.style.cssText = `
      background-color: #3B82F6;
      color: white;
      width: ${30 + (count / 10)}px;
      height: ${30 + (count / 10)}px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: bold;
      box-shadow: 0 2px 8px rgba(0,0,0,0.3);
      cursor: pointer;
    `;
    el.textContent = count.toString();

    el.addEventListener('click', () => {
      const zoom = supercluster.getClusterExpansionZoom(cluster.id);
      map.current!.easeTo({
        center: [lng, lat],
        zoom: zoom
      });
    });

    new mapboxgl.Marker(el)
      .setLngLat([lng, lat])
      .addTo(map.current!);
  }, []);

  useEffect(() => {
    if (!mapContainer.current || !mapboxgl.accessToken) return;

    // Initialize map with performance settings
    map.current = new mapboxgl.Map({
      container: mapContainer.current,
      style: 'mapbox://styles/mapbox/light-v11',
      center: [0, 0],
      zoom: 2,
      interactive,
      renderWorldCopies: false,
      preserveDrawingBuffer: false,
      refreshExpiredTiles: false,
      trackResize: true
    });

    map.current.on('load', () => {
      setMapLoaded(true);

      // Add WebGL layer for massive datasets
      if (activities.length > 1000) {
        map.current!.addLayer(new ActivityClusterLayer(activities));
      }
    });

    // Track viewport changes
    const updateViewport = () => {
      if (map.current) {
        setViewport(map.current.getBounds());
      }
    };

    map.current.on('moveend', updateViewport);
    map.current.on('zoomend', updateViewport);

    // Add navigation controls
    if (interactive) {
      map.current.addControl(new mapboxgl.NavigationControl(), 'top-right');
    }

    return () => {
      // Cleanup all markers
      markersRef.current.forEach(marker => marker.remove());
      markersRef.current.clear();
      map.current?.remove();
    };
  }, [interactive, activities.length]);

  useEffect(() => {
    if (!mapLoaded || !map.current) return;

    // Initial viewport
    const bounds = new mapboxgl.LngLatBounds();
    activities.forEach(activity => {
      if (activity.location?.lat && activity.location?.lng) {
        bounds.extend([activity.location.lng, activity.location.lat]);
      }
    });

    if (!bounds.isEmpty()) {
      map.current.fitBounds(bounds, {
        padding: { top: 50, bottom: 50, left: 50, right: 50 },
        maxZoom: 12,
        duration: 1000
      });
    }

    // Render visible markers after map settles
    const renderTimeout = setTimeout(() => {
      renderVisibleMarkers();
    }, 1100);

    return () => clearTimeout(renderTimeout);
  }, [mapLoaded, activities, renderVisibleMarkers]);

  // Render route with optimized line
  useEffect(() => {
    if (!mapLoaded || !map.current || !showRoute) return;

    const validActivities = activities.filter(
      a => a.location?.lat && a.location?.lng
    );

    if (validActivities.length < 2) return;

    // Remove existing route
    if (map.current.getSource('route')) {
      map.current.removeLayer('route');
      map.current.removeSource('route');
    }

    // Create optimized route
    const coordinates = validActivities.map(a => [
      a.location!.lng!,
      a.location!.lat!
    ]);

    map.current.addSource('route', {
      type: 'geojson',
      data: {
        type: 'Feature',
        properties: {},
        geometry: {
          type: 'LineString',
          coordinates
        }
      }
    });

    map.current.addLayer({
      id: 'route',
      type: 'line',
      source: 'route',
      layout: {
        'line-join': 'round',
        'line-cap': 'round'
      },
      paint: {
        'line-color': '#3B82F6',
        'line-width': 2,
        'line-opacity': 0.5,
        'line-dasharray': [2, 2]
      }
    }, 'activity-clusters'); // Place below markers
  }, [mapLoaded, activities, showRoute]);

  // Fallback for missing Mapbox token
  if (!mapboxgl.accessToken) {
    return (
      <div
        className="bg-gray-100 rounded-lg flex items-center justify-center text-gray-500"
        style={{ height }}
      >
        <div className="text-center">
          <MapPin className="w-12 h-12 mx-auto mb-2 text-gray-400" />
          <p>Map preview unavailable</p>
          <p className="text-sm mt-1">Configure Mapbox token to enable</p>
        </div>
      </div>
    );
  }

  return (
    <div className="relative">
      <div
        ref={mapContainer}
        className="rounded-lg overflow-hidden"
        style={{ height }}
      />

      {/* Performance indicator */}
      {activities.length > maxConcurrentMarkers && (
        <div className="absolute top-4 right-4 bg-white rounded-lg shadow-lg p-2 text-xs">
          <span className="text-gray-600">
            Clustering enabled ({activities.length} activities)
          </span>
        </div>
      )}

      {/* Legend */}
      <div className="absolute bottom-4 left-4 bg-white rounded-lg shadow-lg p-3 text-xs">
        <h4 className="font-semibold mb-2">Activity Types</h4>
        <div className="space-y-1">
          {Object.entries(ACTIVITY_COLORS).map(([type, color]) => (
            <div key={type} className="flex items-center gap-2">
              <div
                className="w-3 h-3 rounded-full"
                style={{ backgroundColor: color }}
              />
              <span className="capitalize">{type}</span>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}
```

## Day 7: Complete Integration & Polish (8 hours)

### Task 6: Full Trip Page with Visuals (4 hours)

**File**: `packages/web/src/app/trips/[tripId]/page.tsx`

```typescript
import { Metadata } from 'next';
import { notFound } from 'next/navigation';
import { TripView } from '@/components/trips/TripView';
import { getTripById } from '@/lib/api/trips';

interface TripPageProps {
  params: {
    tripId: string;
  };
}

export async function generateMetadata({ params }: TripPageProps): Promise<Metadata> {
  const trip = await getTripById(params.tripId);

  if (!trip) {
    return {
      title: 'Trip Not Found | TravelViz'
    };
  }

  return {
    title: `${trip.title} | TravelViz`,
    description: trip.description || `View ${trip.title} on TravelViz`,
    openGraph: {
      title: trip.title,
      description: trip.description,
      images: trip.coverImage ? [trip.coverImage] : undefined
    }
  };
}

export default async function TripPage({ params }: TripPageProps) {
  const trip = await getTripById(params.tripId);

  if (!trip) {
    notFound();
  }

  return <TripView trip={trip} />;
}
```

**File**: `packages/web/src/components/trips/TripView.tsx`

```typescript
'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Calendar,
  Map,
  List,
  Share2,
  Edit,
  Download,
  Copy,
  Globe,
  Lock,
  Users
} from 'lucide-react';
import { format } from 'date-fns';
import { Timeline } from '@/components/timeline/Timeline';
import { TripMap } from '@/components/map/TripMap';
import { ActivityList } from '@/components/activities/ActivityList';
import { ShareModal } from '@/components/modals/ShareModal';
import { Trip } from '@travelviz/shared';

interface TripViewProps {
  trip: Trip;
  isOwner?: boolean;
}

export function TripView({ trip, isOwner = false }: TripViewProps) {
  const router = useRouter();
  const [activeView, setActiveView] = useState<'timeline' | 'map' | 'list'>('timeline');
  const [showShareModal, setShowShareModal] = useState(false);

  const handleEdit = () => {
    router.push(`/trips/${trip.id}/edit`);
  };

  const handleExport = async () => {
    // Export as PDF or image
    const response = await fetch(`/api/v1/trips/${trip.id}/export`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${localStorage.getItem('token')}`
      },
      body: JSON.stringify({ format: 'pdf' })
    });

    if (response.ok) {
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `${trip.title.replace(/\s+/g, '-')}.pdf`;
      a.click();
    }
  };

  const handleClone = async () => {
    const response = await fetch(`/api/v1/trips/${trip.id}/clone`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token')}`
      }
    });

    if (response.ok) {
      const { tripId } = await response.json();
      router.push(`/trips/${tripId}/edit`);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b">
        <div className="container mx-auto px-4 py-6">
          <div className="flex flex-col md:flex-row md:items-start md:justify-between gap-4">
            <div>
              <h1 className="text-3xl font-bold">{trip.title}</h1>
              <p className="text-gray-600 mt-1">
                {format(new Date(trip.startDate), 'MMM d')} -
                {format(new Date(trip.endDate), 'MMM d, yyyy')}
              </p>
              {trip.description && (
                <p className="text-gray-700 mt-2 max-w-2xl">{trip.description}</p>
              )}

              {/* Privacy Badge */}
              <div className="flex items-center gap-2 mt-3">
                {trip.isPublic ? (
                  <Badge variant="outline" className="gap-1">
                    <Globe className="w-3 h-3" />
                    Public Trip
                  </Badge>
                ) : (
                  <Badge variant="outline" className="gap-1">
                    <Lock className="w-3 h-3" />
                    Private Trip
                  </Badge>
                )}
                {trip.sharedWith && trip.sharedWith.length > 0 && (
                  <Badge variant="outline" className="gap-1">
                    <Users className="w-3 h-3" />
                    Shared with {trip.sharedWith.length}
                  </Badge>
                )}
              </div>
            </div>

            {/* Actions */}
            <div className="flex flex-wrap gap-2">
              {isOwner ? (
                <>
                  <Button onClick={handleEdit}>
                    <Edit className="w-4 h-4 mr-2" />
                    Edit
                  </Button>
                  <Button variant="outline" onClick={() => setShowShareModal(true)}>
                    <Share2 className="w-4 h-4 mr-2" />
                    Share
                  </Button>
                  <Button variant="outline" onClick={handleExport}>
                    <Download className="w-4 h-4 mr-2" />
                    Export
                  </Button>
                </>
              ) : (
                <Button onClick={handleClone}>
                  <Copy className="w-4 h-4 mr-2" />
                  Copy to My Trips
                </Button>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* View Tabs */}
      <div className="container mx-auto px-4 py-6">
        <Tabs value={activeView} onValueChange={(v) => setActiveView(v as any)}>
          <TabsList className="grid w-full max-w-md grid-cols-3">
            <TabsTrigger value="timeline" className="gap-2">
              <Calendar className="w-4 h-4" />
              Timeline
            </TabsTrigger>
            <TabsTrigger value="map" className="gap-2">
              <Map className="w-4 h-4" />
              Map
            </TabsTrigger>
            <TabsTrigger value="list" className="gap-2">
              <List className="w-4 h-4" />
              List
            </TabsTrigger>
          </TabsList>

          <div className="mt-6">
            <TabsContent value="timeline">
              <Timeline
                activities={trip.activities}
                editable={isOwner}
                onUpdate={(activities) => {
                  // Handle timeline updates
                }}
              />
            </TabsContent>

            <TabsContent value="map">
              <div className="bg-white rounded-lg shadow-sm p-4">
                <TripMap
                  activities={trip.activities}
                  interactive={true}
                  showRoute={true}
                  height="600px"
                />
              </div>
            </TabsContent>

            <TabsContent value="list">
              <ActivityList
                activities={trip.activities}
                editable={isOwner}
                onUpdate={(activities) => {
                  // Handle list updates
                }}
              />
            </TabsContent>
          </div>
        </Tabs>
      </div>

      {/* Share Modal */}
      {showShareModal && (
        <ShareModal
          trip={trip}
          onClose={() => setShowShareModal(false)}
          onShare={(settings) => {
            // Handle share settings update
          }}
        />
      )}
    </div>
  );
}
```

### Task 7: Creating Step Animation (2 hours)

**File**: `packages/web/src/components/import/steps/CreatingStep.tsx`

```typescript
'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Sparkles, CheckCircle } from 'lucide-react';

export function CreatingStep() {
  const router = useRouter();

  useEffect(() => {
    // Auto-redirect after animation
    const timer = setTimeout(() => {
      // This would be set by the preview step
      const tripId = sessionStorage.getItem('newTripId');
      if (tripId) {
        router.push(`/trips/${tripId}`);
      }
    }, 3000);

    return () => clearTimeout(timer);
  }, [router]);

  return (
    <div className="py-16 text-center">
      {/* Success Animation */}
      <div className="relative inline-block">
        <div className="absolute inset-0 animate-ping">
          <div className="w-32 h-32 bg-green-400 rounded-full opacity-20" />
        </div>
        <div className="relative bg-gradient-to-br from-green-500 to-emerald-600 rounded-full p-8">
          <CheckCircle className="w-16 h-16 text-white" />
        </div>
        <div className="absolute -top-2 -right-2">
          <Sparkles className="w-8 h-8 text-yellow-400 animate-spin" />
        </div>
      </div>

      {/* Success Message */}
      <h2 className="text-3xl font-bold mt-8 mb-2">
        Trip Created Successfully!
      </h2>
      <p className="text-gray-600 text-lg">
        Your AI-imported trip is ready to explore
      </p>

      {/* Fun Stats */}
      <div className="mt-8 inline-flex items-center gap-8 text-sm text-gray-600">
        <div>
          <span className="block text-2xl font-bold text-gray-900">2.3s</span>
          <span>Parse time</span>
        </div>
        <div className="w-px h-8 bg-gray-300" />
        <div>
          <span className="block text-2xl font-bold text-gray-900">100%</span>
          <span>Accuracy</span>
        </div>
        <div className="w-px h-8 bg-gray-300" />
        <div>
          <span className="block text-2xl font-bold text-gray-900">✨</span>
          <span>Magic level</span>
        </div>
      </div>

      {/* Loading */}
      <div className="mt-12">
        <div className="inline-flex items-center gap-2 text-gray-600">
          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-gray-600" />
          <span>Redirecting to your trip...</span>
        </div>
      </div>
    </div>
  );
}
```

### Task 8: Performance & Polish (2 hours)

**File**: `packages/web/src/hooks/useImportAnalytics.ts`

```typescript
import { useEffect } from 'react';
import { trackEvent } from '@/lib/analytics';

interface ImportMetrics {
  source: string;
  characterCount: number;
  parseTime: number;
  activitiesFound: number;
  confidence: number;
  editsBeforeCreate: number;
  timeToCreate: number;
}

export function useImportAnalytics() {
  const trackImportStart = (source: string, characterCount: number) => {
    trackEvent('import_started', {
      source,
      characterCount,
      timestamp: Date.now(),
    });
  };

  const trackImportProgress = (step: string, progress: number) => {
    trackEvent('import_progress', {
      step,
      progress,
      timestamp: Date.now(),
    });
  };

  const trackImportComplete = (metrics: ImportMetrics) => {
    trackEvent('import_completed', {
      ...metrics,
      timestamp: Date.now(),
    });

    // Track quality metrics
    if (metrics.confidence < 0.6) {
      trackEvent('import_low_confidence', {
        confidence: metrics.confidence,
        source: metrics.source,
      });
    }

    // Track performance
    if (metrics.parseTime > 10000) {
      trackEvent('import_slow_parse', {
        parseTime: metrics.parseTime,
        characterCount: metrics.characterCount,
      });
    }
  };

  const trackImportError = (error: string, step: string) => {
    trackEvent('import_error', {
      error,
      step,
      timestamp: Date.now(),
    });
  };

  return {
    trackImportStart,
    trackImportProgress,
    trackImportComplete,
    trackImportError,
  };
}
```

## Extended Thinking Prompts

For visual design decisions:

```
The visual goal is: [describe desired outcome]
Current limitations: [technical constraints]
User expectations: [what users want to see]
What's the most impactful visual solution within constraints?
```

For performance optimization:

```
Current bottleneck: [describe slow operation]
Data volume: [expected size]
User tolerance: [acceptable wait time]
What's the optimal balance of performance vs features?
```

## Integration Checklist

### Day 6 Completion

- [ ] AI parser configuration complete
- [ ] Parser service handles all sources
- [ ] Import API routes working
- [ ] SSE progress updates functional
- [ ] Mini timeline component renders
- [ ] Mini map component displays pins
- [ ] Geocoding estimates working
- [ ] Error handling graceful
- [ ] Redis caching configured

### Day 7 Completion

- [ ] Full trip page renders beautifully
- [ ] Timeline/Map/List views toggle smoothly
- [ ] Share functionality works
- [ ] Export generates PDF
- [ ] Clone creates user copy
- [ ] Creating animation delightful
- [ ] Analytics tracking complete
- [ ] Performance under 3s total
- [ ] Mobile responsive

## Performance Requirements

### Parser Performance

- Average parse time: <3 seconds
- Timeout handling: 30 seconds max
- Fallback model switches automatically
- Progress updates every 500ms

### Visual Performance

- Timeline renders instantly (<100ms)
- Map loads progressively
- Smooth animations (60fps)
- No layout shifts

## Error Handling

### Parser Errors

```typescript
// Graceful degradation
if (parseError.code === 'TIMEOUT') {
  // Switch to simpler parsing
} else if (parseError.code === 'RATE_LIMIT') {
  // Use fallback model
} else if (parseError.code === 'INVALID_CONTENT') {
  // Show helpful message
}
```

### Visual Errors

```typescript
// Map fallback
if (!mapboxToken) {
  // Show static image
} else if (noCoordinates) {
  // Show location list
}
```

## Definition of Done

✅ Backend Integration:

```bash
# Test parse endpoint
curl -X POST http://localhost:3001/api/v1/import/parse \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"content": "sample conversation", "source": "chatgpt"}'

# Should return importId and start SSE
```

✅ Visual Components:

1. Timeline shows all activities by day
2. Map displays pins with colors
3. Route lines connect locations
4. Popups show activity details
5. Views switch smoothly

✅ End-to-End Flow:

1. Paste conversation → Parse → Preview → Create → View trip
2. Total time <60 seconds
3. Visual display is stunning
4. Share immediately after import

## Next Days Preview

- Day 8: Testing & Polish
- Day 9: Viral sharing features
- Day 10-11: Basic monetization

## Notes

- Visuals are what sell the product
- Performance perception matters more than actual speed
- Progressive enhancement for slow connections
- Mobile experience must be flawless
- Every animation should feel intentional
- If it's not beautiful, it's not done
