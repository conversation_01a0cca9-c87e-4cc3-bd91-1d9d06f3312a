# TravelViz Monorepo .gitignore
# Only commit source code and configuration - keep GitHub clean!

# Dependencies
node_modules/
packages/*/node_modules/
.pnp
.pnp.js

# Build outputs
dist/
build/
packages/*/dist/
packages/*/build/
packages/web/.next/
packages/web/out/

# Environment files (NEVER commit these!)
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
packages/*/.env
packages/*/.env.local
packages/*/.env.development.local
packages/*/.env.test.local
packages/*/.env.production.local

# TypeScript
*.tsbuildinfo
packages/*/tsconfig.tsbuildinfo
next-env.d.ts

# Testing
coverage/
packages/*/coverage/
.nyc_output

# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# Dependency directories
jspm_packages/

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Optional stylelint cache
.stylelintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# parcel-bundler cache (https://parceljs.org/)
.cache
.parcel-cache

# Next.js build output
.next
out

# Nuxt.js build / generate output
.nuxt

# Gatsby files
.cache/
public

# Storybook build outputs
.out
.storybook-out

# Temporary folders
tmp/
temp/

# Editor directories and files
.vscode/
.idea/
.claude/
.gemini/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Deployment
.vercel
.netlify

# Database
*.db
*.sqlite
*.sqlite3

# Cache
.cache/
.parcel-cache/

# Misc
*.pem
*.key
*.crt

# Test results
test-results/
packages/*/test-results/
