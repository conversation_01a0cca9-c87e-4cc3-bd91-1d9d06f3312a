import { Router } from 'express';
import { SupabaseAuthController } from '../controllers/supabase-auth.controller';
import { authenticateSupabaseUser } from '../middleware/supabase-auth.middleware';
import { authRateLimit, strictRateLimit, loginRateLimit } from '../middleware/rate-limit.middleware';
import { validateRequest } from '../middleware/validation.middleware';
import { validateContentLength, SIZE_LIMITS } from '../middleware/request-size.middleware';
import { 
  signupSchema, 
  loginSchema, 
  refreshTokenSchema,
  changePasswordSchema,
  forgotPasswordSchema,
  resetPasswordSchema
} from '../schemas/auth.schema';

const router: Router = Router();
let authController: SupabaseAuthController | null = null;

// Lazy initialization of controller to ensure environment variables are loaded
const getAuthController = () => {
  if (!authController) {
    authController = new SupabaseAuthController();
  }
  return authController;
};

// Public routes with PROPER RATE LIMITING and SIZE LIMITS to prevent attacks
router.post('/signup', 
  validateContentLength(SIZE_LIMITS.AUTH), // Validate size before parsing
  authRateLimit, // 5 requests per 15 minutes
  validateRequest(signupSchema), // Use strong password validation from schema
  (req, res) => getAuthController().signup(req, res)
);

router.post('/login', 
  validateContentLength(SIZE_LIMITS.AUTH), // Validate size before parsing
  loginRateLimit, // 10 requests per 15 minutes - balanced security
  validateRequest(loginSchema), // Use schema validation
  (req, res) => getAuthController().login(req, res)
);

router.post('/refresh', 
  validateContentLength(SIZE_LIMITS.AUTH),
  authRateLimit,
  validateRequest(refreshTokenSchema),
  (req, res) => getAuthController().refreshToken(req, res)
);

router.post('/reset-password', 
  validateContentLength(SIZE_LIMITS.AUTH),
  strictRateLimit, // Prevent email enumeration
  validateRequest(forgotPasswordSchema),
  (req, res) => getAuthController().resetPasswordRequest(req, res)
);

router.post('/reset-password/confirm', 
  validateContentLength(SIZE_LIMITS.AUTH),
  authRateLimit,
  validateRequest(resetPasswordSchema),
  (req, res) => getAuthController().resetPasswordConfirm(req, res)
);

// Protected routes (authentication required)
router.post('/logout', 
  authenticateSupabaseUser, 
  (req, res) => getAuthController().logout(req, res)
);

router.get('/me', 
  authenticateSupabaseUser, 
  (req, res) => getAuthController().getCurrentUser(req, res)
);

router.post('/change-password',
  validateContentLength(SIZE_LIMITS.AUTH),
  authenticateSupabaseUser,
  authRateLimit, // Prevent password change flooding
  validateRequest(changePasswordSchema),
  (req, res) => getAuthController().changePassword(req, res)
);

export default router;