# Security Guidelines for TravelViz

## Critical Security Incident Response

If you discover exposed credentials:

1. **IMMEDIATELY** rotate all affected keys in Supabase Dashboard
2. Remove credentials from all files and git history
3. Check access logs for unauthorized usage
4. Notify the security team

## Credential Management

### Environment Variables

**NEVER** commit these files to version control:

- `.env`
- `.env.local`
- `.env.development.local`
- `.env.test.local`
- `.env.production.local`

### Supabase Credentials

1. **Service Role Key** (`SUPABASE_SERVICE_ROLE_KEY`)
   - Has FULL database access
   - Use ONLY in backend services
   - NEVER expose to client-side code
   - Rotate regularly

2. **Anonymous Key** (`NEXT_PUBLIC_SUPABASE_ANON_KEY`)
   - Limited access based on RLS policies
   - Safe for client-side usage
   - Still should be rotated if compromised

3. **JWT Secret** (`SUPABASE_JWT_SECRET`)
   - Used for token validation
   - Keep absolutely secret
   - Required for custom JWT operations

4. **Database Password**
   - NEVER hardcode in any file
   - Use connection pooling in production
   - Restrict direct DB access

### How to Get Your Credentials

1. Log into Supabase Dashboard
2. Select your project
3. Go to Settings → API
4. Copy credentials to `.env.local` files
5. NEVER share or commit these values

### Credential Rotation

If credentials were exposed:

1. Go to Supabase Dashboard → Settings → API
2. Click "Roll" next to each key
3. Update all `.env.local` files
4. Restart all services
5. Monitor for unauthorized access

## Security Best Practices

### 1. Code Reviews

- Check for hardcoded credentials
- Verify no `.env` files in commits
- Review API endpoint security

### 2. Development

- Use placeholder values in documentation
- Create `.env.example` files with dummy values
- Use secret management in production

### 3. Git Security

- Use `.gitignore` to exclude sensitive files
- Clean git history if credentials were committed
- Use git-secrets or similar tools

### 4. Access Control

- Implement proper RLS policies
- Use least-privilege principle
- Regular security audits

## Production Security

### Deployment

- Use environment variables in hosting platform
- Enable secret rotation
- Implement proper CORS policies
- Use HTTPS everywhere

### Monitoring

- Enable audit logging
- Monitor for suspicious activity
- Set up alerts for failed auth attempts
- Regular security reviews

## Incident Response Plan

1. **Detection**: Identify the security breach
2. **Containment**: Rotate affected credentials immediately
3. **Investigation**: Check logs for unauthorized access
4. **Recovery**: Update all systems with new credentials
5. **Documentation**: Document the incident and lessons learned
6. **Prevention**: Implement measures to prevent recurrence

## Security Checklist

Before each deployment:

- [ ] No hardcoded credentials in code
- [ ] All sensitive files in `.gitignore`
- [ ] Environment variables properly configured
- [ ] RLS policies enabled and tested
- [ ] API endpoints secured
- [ ] Dependencies updated (no known vulnerabilities)
- [ ] Security headers configured
- [ ] CORS properly restricted

## Contact

For security concerns, contact: <EMAIL>
