/**
 * Tests for security utilities
 */

import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { SecurityUtils } from './security';

describe('SecurityUtils', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    // Reset error handlers
    (SecurityUtils as unknown as { errorLogHandlers: unknown[] }).errorLogHandlers = [];
  });

  describe('sanitizeError', () => {
    it('should return generic message for general errors', () => {
      const error = new Error('Database connection failed');
      const result = SecurityUtils.sanitizeError(error);
      expect(result).toBe('An error occurred. Please try again later.');
    });

    it('should return validation message for validation errors', () => {
      const error = new Error('Invalid email format in validation');
      const result = SecurityUtils.sanitizeError(error);
      expect(result).toBe('Invalid input provided. Please check your data and try again.');
    });

    it('should return auth message for authentication errors', () => {
      const error = new Error('JWT token expired - auth failed');
      const result = SecurityUtils.sanitizeError(error);
      expect(result).toBe('Authentication failed. Please log in and try again.');
    });

    it('should return not found message for not found errors', () => {
      const error = new Error('User not found in database');
      const result = SecurityUtils.sanitizeError(error);
      expect(result).toBe('The requested resource was not found.');
    });

    it('should return rate limit message for rate limit errors', () => {
      const error = new Error('API rate limit exceeded');
      const result = SecurityUtils.sanitizeError(error);
      expect(result).toBe('Too many requests. Please try again later.');
    });

    it('should handle non-Error objects', () => {
      const result = SecurityUtils.sanitizeError('String error');
      expect(result).toBe('An error occurred. Please try again later.');
    });

    it('should call registered error handlers', () => {
      const handler = vi.fn();
      SecurityUtils.addErrorLogHandler(handler);

      const error = new Error('Test error');
      const context = { userId: 'user123', endpoint: '/api/test' };
      SecurityUtils.sanitizeError(error, context);

      expect(handler).toHaveBeenCalledWith(
        expect.objectContaining({
          error,
          context,
          userId: 'user123',
          endpoint: '/api/test',
          timestamp: expect.any(Date)
        })
      );
    });
  });

  describe('sanitizeObjectForLogging', () => {
    it('should redact sensitive keys', () => {
      const obj = {
        username: 'john',
        password: 'redacted',
        apiKey: 'redacted',
        data: {
          token: 'redacted',
          userId: '123'
        }
      };

      const result = SecurityUtils.sanitizeObjectForLogging(obj);

      expect(result).toEqual({
        username: 'john',
        password: '[REDACTED]',
        apiKey: '[REDACTED]',
        data: {
          token: '[REDACTED]',
          userId: '123'
        }
      });
    });

    it('should handle arrays', () => {
      const obj = {
        items: [
          { name: 'item1', secret: 'hidden' },
          { name: 'item2', key: 'apikey' }
        ]
      };

      const result = SecurityUtils.sanitizeObjectForLogging(obj);

      expect(result).toEqual({
        items: [
          { name: 'item1', secret: '[REDACTED]' },
          { name: 'item2', key: '[REDACTED]' }
        ]
      });
    });

    it('should handle custom sensitive keys', () => {
      const obj = {
        username: 'john',
        customSecret: 'hidden',
        apiKey: 'visible'
      };

      const result = SecurityUtils.sanitizeObjectForLogging(obj, ['customSecret']);

      expect(result).toEqual({
        username: 'john',
        customSecret: '[REDACTED]',
        apiKey: 'visible'
      });
    });

    it('should handle null and undefined values', () => {
      const obj = {
        nullValue: null,
        undefinedValue: undefined,
        password: 'secret'
      };

      const result = SecurityUtils.sanitizeObjectForLogging(obj);

      expect(result).toEqual({
        nullValue: null,
        undefinedValue: undefined,
        password: '[REDACTED]'
      });
    });
  });

  describe('createSafeErrorResponse', () => {
    it('should create safe error response', () => {
      const error = new Error('Internal server error');
      const result = SecurityUtils.createSafeErrorResponse(error, 'req-123');

      expect(result).toEqual({
        error: 'An error occurred. Please try again later.',
        requestId: 'req-123',
        timestamp: expect.stringMatching(/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}/)
      });
    });

    it('should create response without requestId', () => {
      const error = new Error('Internal server error');
      const result = SecurityUtils.createSafeErrorResponse(error);

      expect(result).toEqual({
        error: 'An error occurred. Please try again later.',
        timestamp: expect.stringMatching(/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}/)
      });
    });
  });

  describe('getSecureEnvVar', () => {
    const originalEnv = process.env;

    beforeEach(() => {
      process.env = { ...originalEnv };
    });

    afterEach(() => {
      process.env = originalEnv;
    });

    it('should get environment variable', () => {
      process.env.TEST_VAR = 'test-value';
      const result = SecurityUtils.getSecureEnvVar('TEST_VAR');
      expect(result).toBe('test-value');
    });

    it('should return default value when not set', () => {
      const result = SecurityUtils.getSecureEnvVar('MISSING_VAR', {
        defaultValue: 'default'
      });
      expect(result).toBe('default');
    });

    it('should throw when required variable is missing', () => {
      expect(() => {
        SecurityUtils.getSecureEnvVar('MISSING_VAR', { required: true });
      }).toThrow('Required environment variable MISSING_VAR is not set');
    });

    it('should validate environment variable', () => {
      process.env.PORT = '3000';
      const result = SecurityUtils.getSecureEnvVar('PORT', {
        validator: (value) => !isNaN(Number(value)) && Number(value) > 0
      });
      expect(result).toBe('3000');
    });

    it('should throw when validation fails', () => {
      process.env.PORT = 'invalid';
      expect(() => {
        SecurityUtils.getSecureEnvVar('PORT', {
          validator: (value) => !isNaN(Number(value)) && Number(value) > 0
        });
      }).toThrow('Environment variable PORT has invalid value');
    });
  });
});