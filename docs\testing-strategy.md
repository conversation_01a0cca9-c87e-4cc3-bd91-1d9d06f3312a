# Testing Strategy: Runtime-Accurate Tests

## Problem

Previously, our tests were passing even when the code would fail at runtime due to module resolution differences. The vitest configuration used path aliases that bypassed normal Node.js module resolution, creating a false sense of security.

## Solution

We've implemented a multi-layered testing strategy that ensures tests accurately reflect runtime behavior:

### 1. Runtime-Accurate Test Configuration

**File**: `packages/hub/vitest.config.runtime.ts`

This configuration removes the problematic `@travelviz/shared` alias, forcing tests to use the same module resolution as production.

```bash
# Run tests with runtime-accurate configuration
pnpm --filter @travelviz/hub test:runtime
```

### 2. Import Validation Tests

**File**: `packages/hub/test/runtime-validation.test.ts`

These tests validate:
- TypeScript compiles without path aliases
- All imports from `@travelviz/shared` use the package root (no subpaths)
- The shared package is built before running tests

```bash
# Run import validation tests
pnpm --filter @travelviz/hub test:validate
```

### 3. Server Startup Integration Tests

**File**: `packages/hub/test/server-startup.integration.test.ts`

These tests actually start the Express server to catch runtime errors that unit tests miss:
- Validates the server starts without module resolution errors
- Tests the health endpoint responds correctly
- Catches import failures that only appear at runtime

```bash
# Run integration tests
pnpm --filter @travelviz/hub test:integration
```

### 4. Import Validation Script

**File**: `scripts/validate-imports.js`

A pre-test/pre-commit script that scans the codebase for problematic imports:
- Identifies invalid subpath imports from `@travelviz/shared`
- Warns about deep relative imports crossing package boundaries
- Checks if the shared package is built

```bash
# Validate all imports
node scripts/validate-imports.js

# Or from hub package
pnpm --filter @travelviz/hub validate:imports
```

## Usage

### Running All Tests

To run the complete test suite with runtime validation:

```bash
# From hub package
pnpm --filter @travelviz/hub test:all

# This runs:
# 1. Import validation tests
# 2. All tests with runtime configuration
# 3. Server startup integration tests
```

### CI/CD Integration

Update your CI pipeline to use the runtime-accurate tests:

```yaml
- name: Validate Imports
  run: node scripts/validate-imports.js

- name: Build Shared Package
  run: pnpm --filter @travelviz/shared build

- name: Run Tests
  run: pnpm --filter @travelviz/hub test:all
```

### Pre-Commit Hook

The health check script already validates services start correctly. The import validation can be added to catch issues earlier:

```bash
# Add to .husky/pre-commit
node scripts/validate-imports.js
```

## Key Principles

1. **No Path Aliases for External Packages**: Test configurations should not use aliases that bypass normal module resolution.

2. **Build Before Test**: Ensure the shared package is built before running tests, just like in production.

3. **Integration Tests**: Include tests that actually start the server to catch runtime errors.

4. **Continuous Validation**: Use pre-commit hooks and CI checks to prevent problematic imports.

## Migration Guide

To migrate existing tests:

1. Fix any imports using subpaths:
   ```typescript
   // ❌ Wrong
   import { validateDateRange } from '@travelviz/shared/utils/date-validation';
   
   // ✅ Correct
   import { validateDateRange } from '@travelviz/shared';
   ```

2. Run tests with the new configuration:
   ```bash
   pnpm --filter @travelviz/hub test:runtime
   ```

3. Fix any failing tests that were relying on the alias behavior.

## Benefits

- **Early Detection**: Catch module resolution errors during testing, not in production
- **Confidence**: Tests truly reflect runtime behavior
- **Consistency**: Same module resolution in all environments
- **Prevention**: Pre-commit validation prevents bad imports from being committed