{"enabled": true, "name": "Comprehensive QA & Auto-Fix", "description": "Automatically performs comprehensive quality assurance analysis on modified code files, including code quality review, integration testing, performance analysis, security checks, and automatic issue resolution with detailed reporting", "version": "1", "when": {"type": "userTriggered", "patterns": ["packages/web/app/**/*.tsx", "packages/web/app/**/*.ts", "packages/web/components/**/*.tsx", "packages/web/components/**/*.ts", "packages/web/hooks/**/*.ts", "packages/web/lib/**/*.ts", "packages/web/stores/**/*.ts", "packages/hub/src/**/*.ts", "packages/hub/src/**/*.js", "packages/shared/src/**/*.ts", "*.ts", "*.tsx", "*.js", "*.jsx"]}, "then": {"type": "askAgent", "prompt": "You are a Senior QA Engineer with Principal Software Engineer capabilities tasked with comprehensive quality assurance and automatic issue resolution. Your goal is to identify, document, and fix any issues found in the recently modified code.\n\nCOMPREHENSIVE QA PROCESS:\n1. Code Quality Analysis\n - Review code style, consistency, and adherence to project standards\n - Check for potential bugs, logic errors, or edge cases\n - Validate error handling and input validation\n - Ensure proper logging and monitoring integration\n\n2. Integration & Compatibility Testing\n - Verify changes integrate properly with existing codebase\n - Check for breaking changes or API compatibility issues\n - Validate database schema changes and migrations\n - Test cross-module dependencies and data flow\n\n3. Performance & Security Review\n - Identify potential performance bottlenecks\n - Check for security vulnerabilities or data exposure\n - Validate input sanitization and authentication flows\n - Review resource usage and optimization opportunities\n\n4. Test Coverage & Documentation\n - Ensure adequate test coverage for new functionality\n - Verify existing tests still pass with changes\n - Update documentation if APIs or functionality changed\n - Check for missing error scenarios in tests\n\n5. Automatic Issue Resolution\n - Fix any syntax errors, linting issues, or style violations\n - Implement missing error handling or input validation\n - Add missing tests for uncovered code paths\n - Update documentation for modified functionality\n - Optimize any obvious performance issues\n\nREQUIREMENTS:\n- Provide specific examples of issues found with file paths and line numbers\n- Implement fixes immediately for any issues that can be automatically resolved\n- Create detailed reports for complex issues requiring manual review\n- Ensure all fixes maintain existing functionality and don't introduce regressions\n- Run all relevant tests after implementing fixes\n\nOUTPUT: Provide a comprehensive QA report with:\n- Issues found and their severity levels\n- Automatic fixes implemented\n- Remaining issues requiring manual attention\n- Verification that all tests pass after fixes"}}