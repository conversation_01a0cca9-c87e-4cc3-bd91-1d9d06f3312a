import { useState, useCallback } from 'react';
import { useAnalytics } from './useAnalytics';

export interface ErrorState {
  error: Error | null;
  errorType: 'network' | 'validation' | 'timeout' | 'unknown' | null;
  isRetrying: boolean;
  retryCount: number;
  isRecovering: boolean;
  lastRetryAt: Date | null;
}

export interface UseErrorStateOptions {
  maxRetries?: number;
  retryDelay?: number;
  onRetry?: () => Promise<void>;
  onRecovery?: (strategy: string) => Promise<void>;
}

const initialState: ErrorState = {
  error: null,
  errorType: null,
  isRetrying: false,
  retryCount: 0,
  isRecovering: false,
  lastRetryAt: null,
};

/**
 * Simplified error state management hook
 * Replaces complex XState machine with straightforward React state
 */
export function useErrorState(options: UseErrorStateOptions = {}) {
  const { maxRetries = 3, retryDelay = 2000, onRetry, onRecovery } = options;
  const [state, setState] = useState<ErrorState>(initialState);
  const { trackError } = useAnalytics();

  const setError = useCallback((error: Error, errorType?: ErrorState['errorType']) => {
    const type = errorType || classifyError(error);
    
    setState({
      error,
      errorType: type,
      isRetrying: false,
      retryCount: 0,
      isRecovering: false,
      lastRetryAt: null,
    });

    // Track error for analytics
    trackError(error, { errorType: type });
    
    console.error('Error occurred:', error);
  }, [trackError]);

  const retry = useCallback(async () => {
    if (state.retryCount >= maxRetries) {
      console.warn('Maximum retries reached');
      return false;
    }

    setState(prev => ({
      ...prev,
      isRetrying: true,
      retryCount: prev.retryCount + 1,
      lastRetryAt: new Date(),
    }));

    try {
      // Wait for retry delay
      await new Promise(resolve => setTimeout(resolve, retryDelay));
      
      // Execute custom retry logic if provided
      if (onRetry) {
        await onRetry();
      }

      // Reset on successful retry
      setState(initialState);
      return true;
    } catch (error) {
      setState(prev => ({
        ...prev,
        isRetrying: false,
        error: error instanceof Error ? error : new Error(String(error)),
      }));
      return false;
    }
  }, [state.retryCount, maxRetries, retryDelay, onRetry]);

  const recover = useCallback(async (strategy: string) => {
    setState(prev => ({ ...prev, isRecovering: true }));

    try {
      if (onRecovery) {
        await onRecovery(strategy);
      } else {
        // Default recovery strategies
        await executeRecoveryStrategy(strategy, state.error);
      }
      
      setState(initialState);
      return true;
    } catch (error) {
      setState(prev => ({
        ...prev,
        isRecovering: false,
        error: error instanceof Error ? error : new Error(String(error)),
      }));
      return false;
    }
  }, [state.error, onRecovery]);

  const reset = useCallback(() => {
    setState(initialState);
  }, []);

  const canRetry = state.retryCount < maxRetries;
  const hasMaxRetries = state.retryCount >= maxRetries;

  return {
    ...state,
    setError,
    retry,
    recover,
    reset,
    canRetry,
    hasMaxRetries,
  };
}

/**
 * Simplified error classification
 */
function classifyError(error: Error): ErrorState['errorType'] {
  const message = error.message.toLowerCase();
  
  if (message.includes('network') || message.includes('fetch')) {
    return 'network';
  }
  
  if (message.includes('validation') || message.includes('invalid')) {
    return 'validation';
  }
  
  if (message.includes('timeout') || message.includes('aborted')) {
    return 'timeout';
  }
  
  return 'unknown';
}

/**
 * Default recovery strategies
 */
async function executeRecoveryStrategy(strategy: string, error: Error | null) {
  switch (strategy) {
    case 'network':
      // Check for cached data
      const cached = localStorage.getItem('lastSuccessfulData');
      if (cached) {
        try {
          const { data, timestamp } = JSON.parse(cached);
          const isExpired = Date.now() - timestamp > 24 * 60 * 60 * 1000; // 24 hours
          
          if (!isExpired) {
            return data;
          } else {
            localStorage.removeItem('lastSuccessfulData');
          }
        } catch {
          localStorage.removeItem('lastSuccessfulData');
        }
      }
      throw new Error('No cached data available');

    case 'validation':
      // Return empty/default data for validation errors
      return {};

    case 'timeout':
      // Reduce complexity or return cached results
      return null;

    case 'unknown':
    default:
      // Page refresh as last resort
      if (typeof window !== 'undefined') {
        window.location.reload();
      }
      break;
  }
}