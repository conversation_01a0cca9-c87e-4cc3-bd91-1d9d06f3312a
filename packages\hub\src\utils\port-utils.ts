import { createServer } from 'net';
import { logger } from './logger';

/**
 * Check if a port is available
 */
export function isPortAvailable(port: number): Promise<boolean> {
  return new Promise((resolve) => {
    const server = createServer();
    
    server.listen(port, () => {
      server.once('close', () => {
        resolve(true);
      });
      server.close();
    });
    
    server.on('error', () => {
      resolve(false);
    });
  });
}

/**
 * Find an available port starting from the given port
 * @param startPort - The port to start checking from
 * @param maxAttempts - Maximum number of ports to check (default: 10)
 * @returns Promise<number> - The first available port found
 */
export async function findAvailablePort(startPort: number, maxAttempts: number = 10): Promise<number> {
  for (let i = 0; i < maxAttempts; i++) {
    const port = startPort + i;
    const available = await isPortAvailable(port);
    
    if (available) {
      if (port !== startPort) {
        logger.info(`Port ${startPort} is in use, using available port ${port} instead`);
      }
      return port;
    }
  }
  
  throw new Error(`No available port found in range ${startPort}-${startPort + maxAttempts - 1}`);
}

/**
 * Get the port from environment or find an available one
 * @param defaultPort - The default port to use
 * @returns Promise<number> - The port to use
 */
export async function getAvailablePort(defaultPort: number = 3001): Promise<number> {
  const envPort = process.env.PORT;
  
  if (envPort) {
    const port = parseInt(envPort, 10);
    if (isNaN(port)) {
      logger.warn(`Invalid PORT environment variable: ${envPort}, using default ${defaultPort}`);
      return findAvailablePort(defaultPort);
    }
    
    // If PORT is explicitly set, try to use it but fall back if not available
    const available = await isPortAvailable(port);
    if (available) {
      return port;
    } else {
      logger.warn(`Port ${port} (from PORT env var) is in use, finding alternative...`);
      return findAvailablePort(port);
    }
  }
  
  return findAvailablePort(defaultPort);
}
