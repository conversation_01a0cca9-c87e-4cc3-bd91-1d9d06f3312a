import React, { <PERSON>actNode } from 'react';
import { AnalyticsProvider } from '../../src/contexts/analytics.context';
import { MockAnalyticsProvider } from '../../src/lib/analytics/mock-provider';

/**
 * Test wrapper component that provides a mock analytics provider
 */
export function TestAnalyticsProvider({ 
  children,
  mockProvider
}: { 
  children: ReactNode;
  mockProvider?: MockAnalyticsProvider;
}) {
  const provider = mockProvider || new MockAnalyticsProvider();
  
  return (
    <AnalyticsProvider provider={provider}>
      {children}
    </AnalyticsProvider>
  );
}

/**
 * Create a test wrapper with analytics provider
 */
export function createAnalyticsWrapper(mockProvider?: MockAnalyticsProvider) {
  return function AnalyticsWrapper({ children }: { children: ReactNode }) {
    return (
      <TestAnalyticsProvider mockProvider={mockProvider}>
        {children}
      </TestAnalyticsProvider>
    );
  };
}

/**
 * Helper to assert analytics events in tests
 */
export function expectAnalyticsEvent(
  mockProvider: MockAnalyticsProvider,
  event: string,
  properties?: Record<string, any>
) {
  const tracked = mockProvider.wasEventTracked(event);
  
  if (!tracked) {
    const allEvents = mockProvider.calls
      .filter(call => call.method === 'track')
      .map(call => call.args[0]);
    throw new Error(
      `Expected event "${event}" to be tracked. Tracked events: ${JSON.stringify(allEvents)}`
    );
  }

  if (properties) {
    const eventProps = mockProvider.getEventProperties(event);
    
    Object.entries(properties).forEach(([key, value]) => {
      if (eventProps?.[key] !== value) {
        throw new Error(
          `Expected event "${event}" to have property "${key}" = ${JSON.stringify(value)}, ` +
          `but got ${JSON.stringify(eventProps?.[key])}`
        );
      }
    });
  }
}