import { test, expect } from '@playwright/test';

test.describe('TravelViz Smoke Test', () => {
  // Test credentials from .env.local
  const TEST_EMAIL = '<EMAIL>';
  const TEST_PASSWORD = 'Flaremmk123!';

  test('services are running', async ({ page }) => {
    // Check frontend is accessible
    await page.goto('/');
    await expect(page).toHaveTitle(/TravelViz/i);
    
    // Check API is accessible
    const apiResponse = await page.request.get('http://localhost:3001/health');
    expect(apiResponse.ok()).toBeTruthy();
  });

  test('can access homepage and see main elements', async ({ page }) => {
    await page.goto('/');
    
    // Check for key homepage elements
    await expect(page.getByText(/Transform your AI travel conversations/i)).toBeVisible();
    await expect(page.getByRole('button', { name: /Get Started/i })).toBeVisible();
  });

  test('can navigate to import page', async ({ page }) => {
    await page.goto('/');
    
    // Click get started
    await page.getByRole('button', { name: /Get Started/i }).click();
    
    // Should navigate to import page
    await expect(page).toHaveURL(/\/import/);
    await expect(page.getByText(/Import Your Travel Conversation/i)).toBeVisible();
  });

  test('can test AI import with OpenRouter', async ({ page }) => {
    await page.goto('/import');
    
    // Click on AI conversation tab
    await page.getByRole('tab', { name: /AI Conversation/i }).click();
    
    // Generate a simple travel conversation using OpenRouter
    const sampleConversation = `
User: I want to plan a 3-day trip to Paris from March 15-17, 2025.
Assistant: I'd be happy to help you plan a 3-day trip to Paris! Here's a suggested itinerary:

Day 1 (March 15):
- Morning: Arrive at Charles de Gaulle Airport, take RER B to city center
- Afternoon: Check into hotel near the Latin Quarter
- Evening: Visit the Eiffel Tower at sunset, dinner at a nearby bistro

Day 2 (March 16):
- Morning: Visit the Louvre Museum (book tickets in advance)
- Afternoon: Walk through Tuileries Garden to Place de la Concorde
- Evening: Explore Montmartre and see Sacré-Cœur

Day 3 (March 17):
- Morning: Notre-Dame Cathedral and Île de la Cité
- Afternoon: Musée d'Orsay for impressionist art
- Evening: Seine river cruise before departure

Budget estimate: €800-1200 per person including accommodation.`;
    
    // Select ChatGPT format (or any format)
    await page.getByLabel(/Platform/i).click();
    await page.getByRole('option', { name: /ChatGPT/i }).click();
    
    // Paste the conversation
    await page.getByPlaceholder(/Paste your conversation/i).fill(sampleConversation);
    
    // Click analyze button
    await page.getByRole('button', { name: /Analyze Conversation/i }).click();
    
    // Wait for processing
    await expect(page.getByText(/Analyzing conversation/i)).toBeVisible();
    
    // Wait for itinerary to be generated (with longer timeout)
    await expect(page.getByText(/Paris/i)).toBeVisible({ timeout: 30000 });
    await expect(page.getByText(/Eiffel Tower/i)).toBeVisible();
  });

  test('can view map with locations', async ({ page }) => {
    // First, create an itinerary
    await page.goto('/import');
    await page.getByRole('tab', { name: /AI Conversation/i }).click();
    
    const conversation = `
User: Plan a 2-day Tokyo trip for next week.
Assistant: Here's your Tokyo itinerary:
Day 1: Senso-ji Temple in Asakusa, Tokyo Skytree, Akihabara electronics district
Day 2: Meiji Shrine, Shibuya Crossing, Roppongi Hills`;
    
    await page.getByLabel(/Platform/i).click();
    await page.getByRole('option', { name: /ChatGPT/i }).click();
    await page.getByPlaceholder(/Paste your conversation/i).fill(conversation);
    await page.getByRole('button', { name: /Analyze Conversation/i }).click();
    
    // Wait for map to load
    await expect(page.locator('[data-testid="map-container"]')).toBeVisible({ timeout: 30000 });
    
    // Check map has markers
    await expect(page.locator('.mapboxgl-marker')).toHaveCount(6); // 6 locations
  });

  test('can drag and drop activities', async ({ page }) => {
    // Create a simple itinerary first
    await page.goto('/import');
    await page.getByRole('tab', { name: /AI Conversation/i }).click();
    
    const conversation = `
User: 2-day London trip
Assistant: Day 1: Big Ben, Westminster Abbey, Buckingham Palace
Day 2: Tower of London, Tower Bridge, Borough Market`;
    
    await page.getByLabel(/Platform/i).click();
    await page.getByRole('option', { name: /ChatGPT/i }).click();
    await page.getByPlaceholder(/Paste your conversation/i).fill(conversation);
    await page.getByRole('button', { name: /Analyze Conversation/i }).click();
    
    // Wait for activities to load
    await expect(page.getByText(/Big Ben/i)).toBeVisible({ timeout: 30000 });
    
    // Find activity cards
    const bigBenCard = page.locator('[data-activity-name="Big Ben"]');
    const towerBridgeCard = page.locator('[data-activity-name="Tower Bridge"]');
    
    // Drag Big Ben to Day 2
    await bigBenCard.dragTo(towerBridgeCard);
    
    // Verify the order changed
    const day2Activities = page.locator('[data-day="2"] [data-activity-name]');
    await expect(day2Activities.first()).toContainText('Big Ben');
  });
});