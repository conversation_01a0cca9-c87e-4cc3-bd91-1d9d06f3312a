import dynamic from 'next/dynamic';
import { Skeleton } from '@/components/ui/skeleton';
import type { GlobeProps } from './globe';

// Loading component
const GlobeLoader = () => (
  <div className="relative w-full h-full min-h-[400px] flex items-center justify-center bg-gray-50 rounded-lg">
    <div className="text-center space-y-4">
      <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-orange-500 mx-auto" />
      <p className="text-sm text-gray-600">Loading interactive globe...</p>
    </div>
  </div>
);

// Dynamic import with no SSR
export const GlobeDynamic = dynamic<GlobeProps>(
  () => import('./globe').then(mod => mod.Globe as any),
  { 
    ssr: false,
    loading: () => <GlobeLoader />
  }
);

// Re-export the type
export type { GlobeProps } from './globe';