# Design Document

## Overview

The AI Model Optimization system redesigns TravelViz's AI service architecture to implement intelligent model selection, comprehensive usage tracking, and cost optimization. The system prioritizes free tier models, implements smart fallback mechanisms, and provides real-time usage monitoring while maintaining service quality through model-specific optimizations.

## Architecture

### High-Level Architecture

```mermaid
graph TB
    subgraph "Client Layer"
        UI[Web UI]
        API[API Requests]
    end
    
    subgraph "AI Service Layer"
        Router[AI Router Service]
        Selector[Model Selector]
        Tracker[Usage Tracker]
        Limiter[Rate Limiter]
    end
    
    subgraph "Model Providers"
        MS[Moonshot AI<br/>kimi-k2:free]
        GM[Google Gemini<br/>Flash 2.0]
        OR[OpenRouter<br/>gpt-4.1-nano]
    end
    
    subgraph "Data Layer"
        DB[(Supabase DB)]
        Redis[(Redis Cache)]
    end
    
    UI --> API
    API --> Router
    Router --> Selector
    Router --> Tracker
    Router --> Limiter
    
    Selector --> MS
    Selector --> GM
    Selector --> OR
    
    Tracker --> DB
    Tracker --> Redis
    Limiter --> Redis
```

### Model Selection Flow

```mermaid
flowchart TD
    Start[AI Request] --> Estimate[Estimate Tokens]
    Estimate --> Check1{Moonshot<br/>< 1000/day?}
    
    Check1 -->|Yes| MS[Use Moonshot<br/>kimi-k2:free]
    Check1 -->|No| Check2{Google Gemini<br/>within limits?}
    
    Check2 -->|Yes| SelectGemini[Select Gemini Model<br/>Based on RPM/TPM/RPD]
    Check2 -->|No| Fallback[Use OpenAI<br/>gpt-4.1-nano]
    
    SelectGemini --> GM2.5Pro{Gemini 2.5 Pro<br/>RPM: 5, TPM: 250k, RPD: 100}
    SelectGemini --> GM2.5Flash{Gemini 2.5 Flash<br/>RPM: 10, TPM: 250k, RPD: 250}
    SelectGemini --> GM2.0Flash{Gemini 2.0 Flash<br/>RPM: 15, TPM: 1M, RPD: 200}
    
    MS --> Execute[Execute Request]
    GM2.5Pro --> Execute
    GM2.5Flash --> Execute
    GM2.0Flash --> Execute
    Fallback --> Execute
    
    Execute --> Track[Track Usage]
    Track --> Response[Return Response]
```

## Components and Interfaces

### 1. Usage Tracking Service

**Purpose**: Track daily API usage across all models with Pacific Time reset

**Interface**:
```typescript
interface UsageTrackingService {
  // Track a request
  trackRequest(modelId: string, inputTokens: number, outputTokens: number): Promise<void>;
  
  // Get current usage for a model
  getCurrentUsage(modelId: string): Promise<ModelUsage>;
  
  // Get all usage statistics
  getAllUsage(): Promise<Record<string, ModelUsage>>;
  
  // Check if model is available (not rate limited)
  isModelAvailable(modelId: string): Promise<boolean>;
  
  // Reset daily counters (called at midnight PT)
  resetDailyCounters(): Promise<void>;
}

interface ModelUsage {
  modelId: string;
  requestCount: number;
  inputTokens: number;
  outputTokens: number;
  lastReset: Date;
  dailyLimits: {
    requests?: number;
    inputTokens?: number;
    outputTokens?: number;
  };
}
```

### 2. Intelligent Model Selector

**Purpose**: Select optimal model based on usage, content complexity, and cost

**Interface**:
```typescript
interface ModelSelectorService {
  // Select best available model
  selectModel(content: string, requirements?: ModelRequirements): Promise<ModelSelection>;
  
  // Get fallback models for a given primary model
  getFallbackChain(primaryModel: string, complexity: ContentComplexity): string[];
  
  // Estimate token requirements
  estimateTokens(content: string): TokenEstimate;
}

interface ModelSelection {
  modelId: string;
  provider: 'moonshot' | 'google' | 'openrouter';
  reason: string;
  estimatedCost: number;
  fallbackChain: string[];
}

interface TokenEstimate {
  inputTokens: number;
  outputTokens: number;
  complexity: 'simple' | 'medium' | 'complex' | 'very_complex';
}
```

### 3. Enhanced AI Router Service

**Purpose**: Orchestrate model selection, usage tracking, and request execution

**Interface**:
```typescript
interface EnhancedAIRouterService {
  // Main parsing method with intelligent routing
  parseContent(content: string, source: string, userId: string): Promise<ParsedTrip>;
  
  // Execute request with specific model
  executeWithModel(modelId: string, prompt: string, content: string): Promise<AIResponse>;
  
  // Handle rate limits and fallbacks
  handleRateLimit(modelId: string, error: Error): Promise<string>; // Returns next model
  
  // Get usage statistics
  getUsageStats(): Promise<UsageStatistics>;
}
```

### 4. Model-Specific Prompt Manager

**Purpose**: Manage optimized system prompts for each model

**Interface**:
```typescript
interface PromptManager {
  // Get optimized prompt for model
  getSystemPrompt(modelId: string): string;
  
  // Get model-specific formatting instructions
  getFormatInstructions(modelId: string): string;
  
  // Update prompts (for A/B testing)
  updatePrompt(modelId: string, prompt: string): Promise<void>;
}
```

## Data Models

### Usage Tracking Tables

```sql
-- AI model usage tracking
CREATE TABLE ai_model_usage (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    model_id TEXT NOT NULL,
    date DATE NOT NULL DEFAULT CURRENT_DATE,
    request_count INTEGER DEFAULT 0,
    input_tokens BIGINT DEFAULT 0,
    output_tokens BIGINT DEFAULT 0,
    total_cost DECIMAL(10,6) DEFAULT 0,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    
    UNIQUE(model_id, date)
);

-- AI request logs for detailed tracking
CREATE TABLE ai_request_logs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES auth.users(id),
    session_id UUID,
    model_id TEXT NOT NULL,
    input_tokens INTEGER,
    output_tokens INTEGER,
    cost DECIMAL(8,6),
    duration_ms INTEGER,
    success BOOLEAN DEFAULT true,
    error_message TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Model configuration and limits
CREATE TABLE ai_model_configs (
    id TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    provider TEXT NOT NULL,
    daily_request_limit INTEGER,
    rpm_limit INTEGER,
    tpm_limit BIGINT,
    rpd_limit INTEGER,
    cost_per_1k_tokens DECIMAL(8,6),
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);
```

### Enhanced AI Import Logs

```sql
-- Add model tracking to existing ai_import_logs
ALTER TABLE ai_import_logs ADD COLUMN IF NOT EXISTS model_used TEXT;
ALTER TABLE ai_import_logs ADD COLUMN IF NOT EXISTS input_tokens INTEGER;
ALTER TABLE ai_import_logs ADD COLUMN IF NOT EXISTS output_tokens INTEGER;
ALTER TABLE ai_import_logs ADD COLUMN IF NOT EXISTS processing_cost DECIMAL(8,6);
ALTER TABLE ai_import_logs ADD COLUMN IF NOT EXISTS fallback_attempts INTEGER DEFAULT 0;
```

## Error Handling

### Rate Limit Management

1. **Detection**: Monitor HTTP 429 responses and provider-specific rate limit headers
2. **Fallback**: Automatically switch to next available model in priority chain
3. **Backoff**: Implement exponential backoff with jitter for retries
4. **User Communication**: Provide clear error messages with estimated wait times

### Model Failure Handling

```typescript
class ModelFailureHandler {
  async handleFailure(modelId: string, error: Error, content: string): Promise<string> {
    // Log failure details
    await this.logFailure(modelId, error);
    
    // Determine next model based on failure type
    if (this.isRateLimit(error)) {
      return this.getNextAvailableModel(modelId);
    }
    
    if (this.isTokenLimit(error)) {
      return this.getHigherCapacityModel(content);
    }
    
    // Generic fallback
    return this.getNextFallbackModel(modelId);
  }
}
```

## Testing Strategy

### Unit Tests

1. **Usage Tracking**: Test daily reset, counter increments, limit checking
2. **Model Selection**: Test selection logic for different content complexities
3. **Token Estimation**: Validate token counting accuracy
4. **Prompt Management**: Test model-specific prompt retrieval

### Integration Tests

1. **End-to-End Parsing**: Test complete parsing flow with different models
2. **Fallback Chains**: Test automatic fallback when models fail
3. **Rate Limit Simulation**: Test behavior when limits are reached
4. **Database Operations**: Test usage tracking persistence

### Load Tests

1. **Concurrent Requests**: Test system behavior under high load
2. **Rate Limit Boundaries**: Test behavior at usage limits
3. **Model Switching**: Test performance when switching between models

## Model-Specific Optimizations

### Moonshot AI (kimi-k2:free)
- **System Prompt**: Optimized for structured JSON output
- **Temperature**: 0.2 for consistency
- **Max Tokens**: 4000 (conservative for free tier)
- **Retry Logic**: 3 attempts with 2s delay

### Google Gemini Models
- **Gemini 2.5 Pro**: Complex itineraries, high accuracy needs
- **Gemini 2.5 Flash**: Balanced performance for medium complexity
- **Gemini 2.0 Flash**: High-volume processing, simple to medium complexity
- **Rate Limit Handling**: Track RPM, TPM, and RPD separately
- **Reset Time**: Midnight Pacific Time

### OpenAI (gpt-4.1-nano)
- **Usage**: Last resort fallback only
- **System Prompt**: Minimal token usage optimization
- **Cost Tracking**: Precise cost calculation per request

## Performance Considerations

### Caching Strategy

1. **Usage Counters**: Redis with daily TTL
2. **Model Availability**: 5-minute cache for rate limit status
3. **Token Estimates**: Cache estimates for similar content patterns

### Database Optimization

1. **Indexes**: On model_id, date, user_id for fast queries
2. **Partitioning**: Partition usage tables by date for performance
3. **Archival**: Archive old usage data after 90 days
4. **Daily Reset**: Use Supabase pg_cron extension for reliable midnight PT resets

### Daily Reset with pg_cron

```sql
-- Schedule daily reset at midnight Pacific Time
-- pg_cron uses UTC, so midnight PT = 8:00 UTC (PST) or 7:00 UTC (PDT)
SELECT cron.schedule(
    'daily-ai-usage-reset',
    '0 8 * * *', -- 8:00 UTC = midnight PST
    'SELECT reset_daily_ai_usage();'
);

-- Function to reset daily usage counters
CREATE OR REPLACE FUNCTION reset_daily_ai_usage()
RETURNS void AS $$
BEGIN
    -- Archive yesterday's data if needed
    INSERT INTO ai_model_usage_archive 
    SELECT * FROM ai_model_usage 
    WHERE date < CURRENT_DATE;
    
    -- Reset current day counters
    UPDATE ai_model_usage 
    SET request_count = 0, 
        input_tokens = 0, 
        output_tokens = 0,
        total_cost = 0,
        updated_at = NOW()
    WHERE date = CURRENT_DATE;
    
    -- Clear Redis cache for usage counters
    PERFORM pg_notify('ai_usage_reset', 'daily_reset_complete');
END;
$$ LANGUAGE plpgsql;
```

### Monitoring and Alerting

1. **Usage Thresholds**: Alert at 80% and 90% of daily limits
2. **Model Health**: Monitor success rates and response times
3. **Cost Tracking**: Daily cost reports and budget alerts
4. **Performance Metrics**: Track parsing success rates by model

## Security Considerations

1. **API Key Management**: Secure storage and rotation of provider keys
2. **Rate Limit Protection**: Prevent abuse through user-level limits
3. **Data Privacy**: Ensure usage logs don't contain sensitive content
4. **Access Control**: Restrict admin access to usage statistics