-- Complete schema fix for TravelViz
-- Date: 2025-07-07

-- First, ensure all columns exist in trips table
ALTER TABLE public.trips
ADD COLUMN IF NOT EXISTS share_slug TEXT UNIQUE;

-- Now drop all existing RLS policies to start fresh
DROP POLICY IF EXISTS "Users can view their own trips" ON public.trips;
DROP POLICY IF EXISTS "Users can insert their own trips" ON public.trips;
DROP POLICY IF EXISTS "Users can update their own trips" ON public.trips;
DROP POLICY IF EXISTS "Users can delete their own trips" ON public.trips;
DROP POLICY IF EXISTS "Anyone can view public trips" ON public.trips;

-- Simple RLS policies without recursion
CREATE POLICY "Users can view their own trips"
  ON public.trips
  FOR SELECT
  USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own trips"
  ON public.trips
  FOR INSERT
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own trips"
  ON public.trips
  FOR UPDATE
  USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own trips"
  ON public.trips
  FOR DELETE
  USING (auth.uid() = user_id);

-- Public trips policy (separate to avoid OR conditions)
CREATE POLICY "Anyone can view public trips"
  ON public.trips
  FOR SELECT
  USING (visibility = 'public');

-- For activities table
DROP POLICY IF EXISTS "Users can view activities from their trips" ON public.activities;
DROP POLICY IF EXISTS "Users can insert activities to their trips" ON public.activities;
DROP POLICY IF EXISTS "Users can update activities from their trips" ON public.activities;
DROP POLICY IF EXISTS "Users can delete activities from their trips" ON public.activities;

-- Simple activities policies
CREATE POLICY "Users can manage their trip activities"
  ON public.activities
  FOR ALL
  USING (
    trip_id IN (
      SELECT id FROM public.trips WHERE user_id = auth.uid()
    )
  );

-- Ensure RLS is enabled
ALTER TABLE public.trips ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.activities ENABLE ROW LEVEL SECURITY;

-- Create profiles entry for test user if it doesn't exist
INSERT INTO public.profiles (id, email, name)
VALUES (
  '697b40b3-42d7-4b32-ad49-0220c2313643',
  '<EMAIL>',
  'Test User'
)
ON CONFLICT (id) DO NOTHING;