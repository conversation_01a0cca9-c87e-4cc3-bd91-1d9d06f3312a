/**
 * Authentication Flow E2E Test
 * 
 * Perfect E2E test foundation that demonstrates:
 * - Real browser automation with authentication
 * - Environment configuration from .env.local
 * - Screenshot capture on failure
 * - Clean test isolation
 */

const { test, expect } = require('@playwright/test');
const { HomePage, LoginPage, DashboardPage } = require('./utils/page-objects');
const testConfig = require('../test.config');

// Test configuration
const TEST_USER = {
  email: testConfig.auth.testUserEmail,
  password: testConfig.auth.testUserPassword
};

test.describe('Authentication Flow', () => {
  test.beforeEach(async ({ page }) => {
    // Set up test isolation
    await page.context().clearCookies();
    await page.context().clearPermissions();
  });

  test('should complete homepage to login flow successfully', async ({ page }) => {
    console.log('🏠 Starting homepage to login flow test...');
    
    // Step 1: Navigate to homepage
    console.log('1. Navigating to homepage...');
    const homePage = new HomePage(page);
    await homePage.navigate();
    
    // Verify homepage loaded
    const homeLoaded = await homePage.isLoaded();
    expect(homeLoaded).toBe(true);
    console.log('   ✅ Homepage loaded successfully');
    
    // Take screenshot of homepage
    await page.screenshot({ 
      path: 'test-results/01-homepage.png',
      fullPage: true 
    });
    
    // Step 2: Click login button
    console.log('2. Clicking login button...');
    await homePage.clickLogin();
    
    // Step 3: Verify login page loaded
    console.log('3. Verifying login page loaded...');
    const loginPage = new LoginPage(page);
    const loginLoaded = await loginPage.isLoaded();
    expect(loginLoaded).toBe(true);
    console.log('   ✅ Login page loaded successfully');
    
    // Take screenshot of login page
    await page.screenshot({ 
      path: 'test-results/02-login-page.png',
      fullPage: true 
    });
    
    // Step 4: Perform login
    console.log('4. Performing login...');
    console.log(`   Using email: ${TEST_USER.email}`);
    await loginPage.login(TEST_USER.email, TEST_USER.password);
    
    // Step 5: Verify login success
    console.log('5. Verifying login success...');
    const loginSuccess = await loginPage.expectLoginSuccess();
    expect(loginSuccess).toBe(true);
    console.log('   ✅ Login successful - redirected to dashboard');
    
    // Step 6: Verify dashboard loaded
    console.log('6. Verifying dashboard loaded...');
    const dashboardPage = new DashboardPage(page);
    const dashboardLoaded = await dashboardPage.isLoaded();
    expect(dashboardLoaded).toBe(true);
    console.log('   ✅ Dashboard loaded successfully');
    
    // Take screenshot of dashboard
    await page.screenshot({ 
      path: 'test-results/03-dashboard.png',
      fullPage: true 
    });
    
    // Step 7: Verify user is authenticated
    console.log('7. Verifying user authentication...');
    
    // Check URL contains dashboard
    const currentUrl = page.url();
    expect(currentUrl).toContain('/dashboard');
    console.log(`   ✅ Current URL: ${currentUrl}`);
    
    // Optional: Check for welcome message or user-specific content
    try {
      const hasWelcome = await dashboardPage.expectWelcomeMessage();
      if (hasWelcome) {
        console.log('   ✅ Welcome message found');
      } else {
        console.log('   ℹ️  No welcome message found (optional)');
      }
    } catch (error) {
      console.log('   ℹ️  Welcome message check skipped');
    }
    
    // Step 8: Verify trips section exists (even if empty)
    console.log('8. Verifying trips section...');
    const tripsCount = await dashboardPage.getTripsCount();
    expect(tripsCount).toBeGreaterThanOrEqual(0);
    console.log(`   ✅ Found ${tripsCount} trips in dashboard`);
    
    console.log('🎉 Homepage to login flow completed successfully!');
  });

  test('should handle invalid login credentials', async ({ page }) => {
    console.log('🚫 Testing invalid login credentials...');
    
    // Navigate to login page
    const loginPage = new LoginPage(page);
    await loginPage.navigate();
    
    // Verify login page loaded
    const loginLoaded = await loginPage.isLoaded();
    expect(loginLoaded).toBe(true);
    
    // Attempt login with invalid credentials
    console.log('   Attempting login with invalid credentials...');
    await loginPage.login('<EMAIL>', 'wrongpassword');
    
    // Take screenshot of error state
    await page.screenshot({ 
      path: 'test-results/04-login-error.png',
      fullPage: true 
    });
    
    // Verify login failed (should stay on login page)
    await page.waitForTimeout(2000); // Wait for error message
    const currentUrl = page.url();
    expect(currentUrl).toContain('/login');
    console.log('   ✅ Login failed as expected - stayed on login page');
    
    // Optional: Check for error message
    try {
      const hasError = await loginPage.expectLoginError('Invalid');
      if (hasError) {
        console.log('   ✅ Error message displayed');
      } else {
        console.log('   ℹ️  No specific error message found');
      }
    } catch (error) {
      console.log('   ℹ️  Error message check skipped');
    }
    
    console.log('🎉 Invalid login test completed successfully!');
  });

  test('should navigate directly to login page', async ({ page }) => {
    console.log('🔗 Testing direct navigation to login page...');
    
    // Navigate directly to login page
    const loginPage = new LoginPage(page);
    await loginPage.navigate();
    
    // Verify login page loaded
    const loginLoaded = await loginPage.isLoaded();
    expect(loginLoaded).toBe(true);
    console.log('   ✅ Login page loaded via direct navigation');
    
    // Verify URL is correct
    const currentUrl = page.url();
    expect(currentUrl).toContain('/login');
    console.log(`   ✅ URL is correct: ${currentUrl}`);
    
    // Take screenshot
    await page.screenshot({ 
      path: 'test-results/05-direct-login.png',
      fullPage: true 
    });
    
    console.log('🎉 Direct login navigation test completed successfully!');
  });

  test.afterEach(async ({ page }, testInfo) => {
    // Take screenshot on failure
    if (testInfo.status !== testInfo.expectedStatus) {
      const screenshotPath = `test-results/failure-${testInfo.title.replace(/\s+/g, '-')}-${Date.now()}.png`;
      await page.screenshot({ 
        path: screenshotPath,
        fullPage: true 
      });
      console.log(`📸 Failure screenshot saved: ${screenshotPath}`);
    }
    
    // Clean up: logout if authenticated
    try {
      const currentUrl = page.url();
      if (currentUrl.includes('/dashboard') || !currentUrl.includes('/login')) {
        // Try to logout
        const logoutSelectors = [
          'button:has-text("Logout")',
          'a:has-text("Logout")',
          '[data-testid="logout"]',
          '.logout-button'
        ];
        
        for (const selector of logoutSelectors) {
          try {
            const element = await page.locator(selector).first();
            if (await element.isVisible()) {
              await element.click();
              break;
            }
          } catch (error) {
            // Continue to next selector
          }
        }
      }
    } catch (error) {
      // Logout cleanup failed - not critical
      console.log('   ℹ️  Logout cleanup skipped');
    }
  });
});

// Global setup for test results directory
test.beforeAll(async () => {
  const fs = require('fs');
  const path = require('path');
  
  const resultsDir = path.join(__dirname, '../../test-results');
  if (!fs.existsSync(resultsDir)) {
    fs.mkdirSync(resultsDir, { recursive: true });
  }
  
  console.log('🚀 E2E Authentication Tests Starting');
  console.log('====================================');
  console.log(`Base URL: ${testConfig.e2e.baseUrl}`);
  console.log(`Test User: ${TEST_USER.email}`);
  console.log(`Screenshots: ${resultsDir}`);
  console.log('');
});