import { Request, Response } from 'express';
import { createSuccessResponse } from '@travelviz/shared';
import { logger } from '../utils/logger';

interface AIModel {
  value: string;
  label: string;
  description?: string;
  recommended?: boolean;
}

export class ModelsController {
  /**
   * Get available AI models for parsing
   */
  async getAvailableModels(_req: Request, res: Response): Promise<void> {
    try {
      // Define available models with their metadata
      const models: AIModel[] = [
        {
          value: 'anthropic/claude-3-haiku',
          label: 'Claude 3 Haiku',
          description: 'Fast and reliable (3s response)',
          recommended: true
        },
        {
          value: 'deepseek/deepseek-chat-v3-0324:free',
          label: 'DeepSeek Chat v3 (Free)',
          description: 'Free but slower (26s response)'
        }
      ];

      // Check if OpenRouter API key is configured
      const isConfigured = !!process.env.OPENROUTER_API_KEY;

      logger.info('Returning available AI models', { 
        count: models.length,
        isConfigured 
      });

      res.status(200).json(createSuccessResponse({
        models,
        isConfigured,
        defaultModel: 'anthropic/claude-3-haiku'
      }));
    } catch (error) {
      logger.error('Failed to get available models', { error });
      res.status(500).json(createSuccessResponse(
        {
          models: [],
          isConfigured: false,
          defaultModel: 'anthropic/claude-3-haiku'
        },
        'Failed to fetch models, returning defaults'
      ));
    }
  }
}