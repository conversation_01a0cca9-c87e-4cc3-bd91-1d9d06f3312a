// Direct database check to understand the AI processing issue
const { createClient } = require('@supabase/supabase-js');

// Load environment variables
require('dotenv').config({ path: '.env.local' });

const supabaseUrl = process.env.SUPABASE_URL || 'https://your-project.supabase.co';
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY || 'your-service-key-here';

async function checkAIProcessingDatabase() {
  console.log('🔍 CHECKING AI PROCESSING DATABASE STATUS');
  console.log('Time:', new Date().toISOString());
  console.log('');

  try {
    const supabase = createClient(supabaseUrl, supabaseServiceKey);

    // 1. Check the problematic session
    console.log('1. Checking problematic session 58df67d3-99d7-4384-8071-6ac4f63607f4...');
    const { data: problemSession, error: problemError } = await supabase
      .from('ai_import_logs')
      .select('*')
      .eq('id', '58df67d3-99d7-4384-8071-6ac4f63607f4')
      .single();

    if (problemError) {
      console.log('❌ Error fetching problematic session:', problemError);
    } else if (problemSession) {
      console.log('📊 Problematic Session Details:');
      console.log('  ID:', problemSession.id);
      console.log('  Status:', problemSession.import_status);
      console.log('  AI Platform:', problemSession.ai_platform);
      console.log('  Created:', problemSession.created_at);
      console.log('  Updated:', problemSession.updated_at);
      console.log('  Error Message:', problemSession.error_message || 'None');
      console.log('  Has Parsed Data:', !!problemSession.parsed_data);
      console.log('  Content Length:', problemSession.raw_conversation?.length || 0);
    } else {
      console.log('❌ Problematic session not found');
    }
    console.log('');

    // 2. Check all recent sessions to see if this is a pattern
    console.log('2. Checking recent AI import sessions...');
    const { data: recentSessions, error: recentError } = await supabase
      .from('ai_import_logs')
      .select('id, import_status, ai_platform, created_at, updated_at, error_message')
      .order('created_at', { ascending: false })
      .limit(10);

    if (recentError) {
      console.log('❌ Error fetching recent sessions:', recentError);
    } else {
      console.log('📊 Recent Sessions:');
      recentSessions.forEach((session, index) => {
        const timeDiff = new Date() - new Date(session.created_at);
        const minutesAgo = Math.round(timeDiff / 60000);
        console.log(`  ${index + 1}. ${session.id.substring(0, 8)}... | ${session.import_status} | ${session.ai_platform} | ${minutesAgo}m ago | ${session.error_message || 'No error'}`);
      });
    }
    console.log('');

    // 3. Check for stuck sessions (processing for more than 10 minutes)
    console.log('3. Checking for stuck sessions...');
    const tenMinutesAgo = new Date(Date.now() - 10 * 60 * 1000).toISOString();
    const { data: stuckSessions, error: stuckError } = await supabase
      .from('ai_import_logs')
      .select('id, import_status, ai_platform, created_at, updated_at')
      .eq('import_status', 'processing')
      .lt('created_at', tenMinutesAgo);

    if (stuckError) {
      console.log('❌ Error fetching stuck sessions:', stuckError);
    } else {
      console.log(`📊 Found ${stuckSessions.length} stuck sessions (processing > 10 minutes):`);
      stuckSessions.forEach((session, index) => {
        const timeDiff = new Date() - new Date(session.created_at);
        const hoursAgo = Math.round(timeDiff / 3600000);
        console.log(`  ${index + 1}. ${session.id} | ${session.ai_platform} | ${hoursAgo}h ago`);
      });
    }
    console.log('');

    // 4. Create a new test session to see if the issue persists
    console.log('4. Creating new test session...');
    const testSessionId = 'test-' + Date.now();
    const { error: insertError } = await supabase
      .from('ai_import_logs')
      .insert({
        id: testSessionId,
        user_id: '697b40b3-42d7-4b32-ad49-0220c2313643',
        ai_platform: 'test',
        import_status: 'processing',
        raw_conversation: 'Test content for debugging',
        created_at: new Date().toISOString(),
      });

    if (insertError) {
      console.log('❌ Error creating test session:', insertError);
    } else {
      console.log('✅ Test session created:', testSessionId);
      
      // Try to update it to complete
      const { error: updateError } = await supabase
        .from('ai_import_logs')
        .update({
          import_status: 'complete',
          parsed_data: { test: 'data' },
          updated_at: new Date().toISOString(),
        })
        .eq('id', testSessionId);

      if (updateError) {
        console.log('❌ Error updating test session:', updateError);
      } else {
        console.log('✅ Test session updated successfully - database operations work');
      }

      // Clean up test session
      await supabase
        .from('ai_import_logs')
        .delete()
        .eq('id', testSessionId);
    }
    console.log('');

    // 5. Summary and recommendations
    console.log('🎯 ANALYSIS SUMMARY:');
    if (problemSession) {
      const sessionAge = new Date() - new Date(problemSession.created_at);
      const hoursStuck = Math.round(sessionAge / 3600000);
      console.log(`- Problematic session has been stuck in "${problemSession.import_status}" for ${hoursStuck} hours`);
      console.log(`- Last update was only ${Math.round((new Date(problemSession.updated_at) - new Date(problemSession.created_at)) / 1000)} seconds after creation`);
      console.log('- This suggests the AI API call never completed or the completion handler failed');
    }

    if (stuckSessions.length > 0) {
      console.log(`- Found ${stuckSessions.length} other stuck sessions - this is a systemic issue`);
    } else {
      console.log('- No other stuck sessions found - this might be an isolated incident');
    }

    console.log('\n🔧 NEXT STEPS:');
    console.log('1. Check server logs for AI API timeout or error messages');
    console.log('2. Verify OpenRouter API key and model availability');
    console.log('3. Test AI API call manually to see if it hangs');
    console.log('4. Check if circuit breaker is open');

  } catch (error) {
    console.error('❌ Error during database check:', error.message);
    console.error('Stack trace:', error.stack);
  }
}

// Run the check
checkAIProcessingDatabase().catch(console.error);
