# Environment Setup Guide - TravelViz

This guide will help you configure all required environment variables to run TravelViz locally.

## 🚀 Quick Start

The application includes pre-configured `.env.local` files with secure defaults for development. However, you need to replace placeholder values with real credentials for full functionality.

### Required Steps (Critical)

1. **Create a Supabase Project**
   - Visit [Supabase](https://supabase.com) and create a free account
   - Create a new project
   - Go to Settings → API
   - Copy your project URL and keys

2. **Update Hub Environment Variables**

   Edit `/packages/hub/.env.local`:

   ```bash
   # Replace these placeholder values with your Supabase credentials:
   SUPABASE_URL=https://your-actual-project-id.supabase.co
   SUPABASE_SERVICE_ROLE_KEY=your-actual-service-role-key-here
   ```

3. **Update Web Environment Variables**

   Edit `/packages/web/.env.local`:

   ```bash
   # Replace these placeholder values with your Supabase credentials:
   NEXT_PUBLIC_SUPABASE_URL=https://your-actual-project-id.supabase.co
   NEXT_PUBLIC_SUPABASE_ANON_KEY=your-actual-anon-key-here
   ```

4. **Start Development Servers**
   ```bash
   pnpm dev
   ```

## ✅ Environment Status

### Currently Configured

The following environment variables are already set with secure defaults:

#### Hub Package (`packages/hub/.env.local`)

- ✅ `JWT_SECRET` - Randomly generated 64-character secure secret
- ✅ `PORT` - Set to 3001
- ✅ `NODE_ENV` - Set to development
- ✅ `FRONTEND_URL` - Set to http://localhost:3000
- ⚠️ `SUPABASE_URL` - **NEEDS REPLACEMENT** (placeholder value)
- ⚠️ `SUPABASE_SERVICE_ROLE_KEY` - **NEEDS REPLACEMENT** (placeholder value)
- ⚠️ `OPENROUTER_API_KEY` - Optional (for AI features)
- ⚠️ `MAPBOX_ACCESS_TOKEN` - Optional (for maps)
- ⚠️ `GOOGLE_PLACES_API_KEY` - Optional (for place autocomplete)

#### Web Package (`packages/web/.env.local`)

- ✅ `NEXT_PUBLIC_APP_URL` - Set to http://localhost:3000
- ✅ `NEXT_PUBLIC_API_URL` - Set to http://localhost:3001
- ✅ `NEXT_PUBLIC_HUB_URL` - Set to http://localhost:3001
- ✅ `NEXT_PUBLIC_ENVIRONMENT` - Set to development
- ✅ Feature flags enabled for all features
- ⚠️ `NEXT_PUBLIC_SUPABASE_URL` - **NEEDS REPLACEMENT** (placeholder value)
- ⚠️ `NEXT_PUBLIC_SUPABASE_ANON_KEY` - **NEEDS REPLACEMENT** (placeholder value)
- ⚠️ `NEXT_PUBLIC_MAPBOX_ACCESS_TOKEN` - Optional (for maps)

## 🔧 Optional Service Setup

To enable additional features, sign up for these services and add their API keys:

### AI-Powered Import (OpenRouter)

1. Visit [OpenRouter](https://openrouter.ai)
2. Create account and generate API key
3. Add to hub `.env.local`: `OPENROUTER_API_KEY=sk-or-v1-your-key-here`

### Maps Integration (Mapbox)

1. Visit [Mapbox](https://www.mapbox.com)
2. Create account and get access tokens
3. Add secret token to hub `.env.local`: `MAPBOX_ACCESS_TOKEN=sk.your-secret-token`
4. Add public token to web `.env.local`: `NEXT_PUBLIC_MAPBOX_ACCESS_TOKEN=pk.your-public-token`

### Place Autocomplete (Google Places)

1. Visit [Google Cloud Console](https://console.cloud.google.com)
2. Create project and enable Places API
3. Generate API key
4. Add to hub `.env.local`: `GOOGLE_PLACES_API_KEY=AIza-your-key-here`

## 🔍 Validation

The application includes comprehensive environment validation:

### Hub Server Startup Check

When you start the hub server, you'll see validation output:

```
✅ Environment variable JWT_SECRET is set (value hidden)
✅ Environment variable SUPABASE_URL is set
✅ Environment variable SUPABASE_SERVICE_ROLE_KEY is set (value hidden)
✅ JWT_SECRET validation passed
✅ All required environment variables are properly configured
🚀 TravelViz Hub API server running on port 3001
```

### What Gets Validated

- **JWT_SECRET**: Must be at least 32 characters
- **SUPABASE_URL**: Must be valid URL format
- **API Keys**: Format validation for known services
- **Optional Services**: Warnings for missing but recommended services

## 🚨 Security Notes

- ✅ All sensitive values are marked as hidden in logs
- ✅ `.env.local` files are excluded from git
- ✅ Strong JWT secret automatically generated
- ✅ Environment validation prevents weak secrets
- ✅ CORS properly configured for development

## 🛠️ Troubleshooting

### Server Won't Start

1. Check that all required environment variables are set
2. Ensure Supabase credentials are valid
3. Verify JWT_SECRET is at least 32 characters

### Features Not Working

- **No AI Import**: Set `OPENROUTER_API_KEY`
- **No Maps**: Set Mapbox tokens in both packages
- **No Place Autocomplete**: Set `GOOGLE_PLACES_API_KEY`

### Database Connection Issues

1. Verify Supabase URL and keys are correct
2. Check that Supabase project is not paused
3. Ensure service role key has proper permissions

## 📚 Additional Resources

- [Complete Environment Variables Documentation](./docs/ENVIRONMENT_VARIABLES.md)
- [Supabase Setup Guide](https://supabase.com/docs/guides/getting-started)
- [OpenRouter API Documentation](https://openrouter.ai/docs)
- [Mapbox Token Guide](https://docs.mapbox.com/help/getting-started/access-tokens/)

## ⚡ Next Steps

1. Replace Supabase placeholder values with real credentials
2. Test that both servers start without errors: `pnpm dev`
3. Visit http://localhost:3000 to test the application
4. (Optional) Add API keys for additional features as needed

Your environment is now configured for development! 🎉
