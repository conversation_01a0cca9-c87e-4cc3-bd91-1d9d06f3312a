/**
 * Test fixtures for integration tests
 * Provides realistic test data for various scenarios
 */

export const TestFixtures = {
  /**
   * Sample travel conversations for AI parsing
   */
  conversations: {
    paris2Day: `User: Plan a 2-day trip to Paris.
Assistant: Here's your Paris itinerary:

Day 1:
- Morning: Visit the Eiffel Tower
- Afternoon: Explore the Louvre Museum
- Evening: Seine River cruise

Day 2:
- Morning: Trip to Versailles Palace
- Afternoon: Walk through Montmartre
- Evening: Dinner in the Latin Quarter`,

    rome3Day: `User: I need a 3-day Rome itinerary.
Assistant: Here's your Rome adventure:

Day 1:
- Morning: Colosseum and Roman Forum
- Afternoon: Pantheon and Trevi Fountain
- Evening: Dinner in Trastevere

Day 2:
- Morning: Vatican Museums and Sistine Chapel
- Afternoon: St. Peter's Basilica
- Evening: Spanish Steps area

Day 3:
- Morning: Villa Borghese and Galleria Borghese
- Afternoon: Castel Sant'Angelo
- Evening: Aperitivo in Campo de' Fiori`,

    tokyo5Day: `User: Plan a 5-day Tokyo trip focusing on culture and food.
Assistant: Here's your Tokyo cultural and culinary journey:

Day 1:
- Morning: Senso-ji Temple in Asakusa
- Afternoon: Tokyo National Museum
- Evening: Sushi dinner in Ginza

Day 2:
- Morning: Meiji Shrine and Harajuku
- Afternoon: Shibuya Crossing and shopping
- Evening: Ramen in Shibuya

Day 3:
- Morning: Tsukiji Outer Market food tour
- Afternoon: Imperial Palace gardens
- Evening: Traditional kaiseki dinner

Day 4:
- Morning: Ueno Park and museums
- Afternoon: Traditional crafts workshop
- Evening: Izakaya hopping in Shinjuku

Day 5:
- Morning: TeamLab digital art museum
- Afternoon: Odaiba and Tokyo Bay
- Evening: Tempura dinner`,

    shortWeekend: `User: Quick weekend getaway suggestions?
Assistant: Here are some great weekend options:

Option 1: San Francisco
- Day 1: Golden Gate Bridge, Fisherman's Wharf
- Day 2: Alcatraz tour, Union Square shopping

Option 2: Seattle
- Day 1: Pike Place Market, Space Needle
- Day 2: Waterfront, museums`
  },

  /**
   * Test trip data
   */
  trips: {
    basic: {
      title: 'Test Trip to Paris',
      description: 'A test trip for integration testing',
      destination: 'Paris, France',
      startDate: new Date('2024-06-01'),
      endDate: new Date('2024-06-03'),
      visibility: 'private' as const
    },

    public: {
      title: 'Public Test Trip',
      description: 'A public trip for sharing tests',
      destination: 'Rome, Italy',
      startDate: new Date('2024-07-15'),
      endDate: new Date('2024-07-20'),
      visibility: 'public' as const
    }
  },

  /**
   * Test activity data
   */
  activities: {
    museum: {
      title: 'Visit the Louvre Museum',
      description: 'Explore world-famous art collections',
      startTime: '09:00',
      endTime: '12:00',
      location: 'Louvre Museum, Paris',
      type: 'sightseeing' as const
    },

    restaurant: {
      title: 'Dinner at Le Procope',
      description: 'Historic restaurant in Latin Quarter',
      startTime: '19:00',
      endTime: '21:00',
      location: 'Latin Quarter, Paris',
      type: 'dining' as const
    },

    transport: {
      title: 'Metro to Versailles',
      description: 'Take RER C to Versailles',
      startTime: '08:30',
      endTime: '09:30',
      location: 'Châtelet-Les Halles to Versailles',
      type: 'transport' as const
    }
  },

  /**
   * Test import requests
   */
  importRequests: {
    chatgpt: {
      source: 'chatgpt' as const,
      sourceUrl: 'https://chat.openai.com/share/test123'
    },

    claude: {
      source: 'claude' as const,
      sourceUrl: 'https://claude.ai/chat/test456'
    },

    gemini: {
      source: 'gemini' as const,
      sourceUrl: 'https://gemini.google.com/app/test789'
    }
  },

  /**
   * Expected response patterns for validation
   */
  expectedResponses: {
    successfulImport: {
      success: true,
      data: {
        importId: true, // Will be checked for string type
        message: true
      }
    },

    tripCreated: {
      success: true,
      data: {
        trip: {
          id: true,
          title: true,
          destination: true,
          userId: true
        }
      }
    },

    activityAdded: {
      success: true,
      data: {
        activity: {
          id: true,
          title: true,
          tripId: true
        }
      }
    }
  },

  /**
   * Error scenarios for negative testing
   */
  errorScenarios: {
    invalidConversation: 'This is not a travel conversation at all. Just random text.',
    
    emptyContent: '',
    
    malformedJson: '{"invalid": json structure',
    
    unauthorizedAccess: {
      missingAuth: 'No Authorization header',
      invalidToken: 'Bearer invalid_token_here'
    }
  },

  /**
   * Performance test data
   */
  performance: {
    largeConversation: generateLargeConversation(),
    
    multipleActivities: generateMultipleActivities(50),
    
    longTitles: {
      title: 'A'.repeat(500), // Test long title handling
      description: 'B'.repeat(2000) // Test long description handling
    }
  }
};

/**
 * Generate a large conversation for performance testing
 */
function generateLargeConversation(): string {
  const days = [];
  for (let i = 1; i <= 14; i++) {
    const activities = [];
    for (let j = 1; j <= 8; j++) {
      activities.push(`- ${j === 1 ? 'Morning' : j <= 4 ? 'Afternoon' : 'Evening'}: Activity ${j} for day ${i}`);
    }
    days.push(`Day ${i}:\n${activities.join('\n')}`);
  }
  
  return `User: Plan a detailed 2-week itinerary for Europe.
Assistant: Here's your comprehensive European adventure:

${days.join('\n\n')}`;
}

/**
 * Generate multiple activities for testing
 */
function generateMultipleActivities(count: number) {
  return Array.from({ length: count }, (_, i) => ({
    title: `Test Activity ${i + 1}`,
    description: `Description for activity ${i + 1}`,
    startTime: `${9 + (i % 8)}:00`,
    endTime: `${10 + (i % 8)}:00`,
    location: `Location ${i + 1}`,
    type: ['sightseeing', 'dining', 'transport', 'entertainment'][i % 4] as const
  }));
}

/**
 * Clean test data helper
 */
export const cleanTestData = {
  /**
   * Generate unique test identifiers
   */
  uniqueId: () => `test_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
  
  /**
   * Generate test email
   */
  testEmail: () => `test+${Date.now()}@example.com`,
  
  /**
   * Generate test trip title
   */
  testTripTitle: () => `Test Trip ${Date.now()}`,
  
  /**
   * Generate future date for testing
   */
  futureDate: (daysFromNow: number = 30) => {
    const date = new Date();
    date.setDate(date.getDate() + daysFromNow);
    return date;
  }
};