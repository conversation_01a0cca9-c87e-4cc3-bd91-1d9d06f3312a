#!/usr/bin/env node

const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
const path = require('path');
require('dotenv').config({ path: path.resolve(__dirname, '../../.env.local') });

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing SUPABASE_URL or SUPABASE_SERVICE_ROLE_KEY environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function checkDatabaseState() {
  console.log('🔍 Checking database state...\n');
  
  // Check for affiliate_url column
  const { data: columns } = await supabase
    .rpc('exec_sql', { 
      sql_query: `
        SELECT column_name 
        FROM information_schema.columns 
        WHERE table_schema = 'public' 
        AND table_name = 'activities' 
        AND column_name = 'affiliate_url'
      ` 
    })
    .catch(() => ({ data: null }));
  
  const hasAffiliateUrl = columns && columns.length > 0;
  
  // Check for RPC functions
  const { data: functions } = await supabase
    .rpc('exec_sql', { 
      sql_query: `
        SELECT routine_name 
        FROM information_schema.routines 
        WHERE routine_schema = 'public' 
        AND routine_type = 'FUNCTION'
        AND routine_name IN (
          'get_user_dashboard_stats',
          'get_trip_activity_stats',
          'get_activity_counts_batch',
          'get_trip_preview_optimized'
        )
      ` 
    })
    .catch(() => ({ data: null }));
  
  const hasFunctions = functions && functions.length >= 4;
  
  console.log('📊 Database Status:');
  console.log(`   - affiliate_url column: ${hasAffiliateUrl ? '✅ Exists' : '❌ Missing'}`);
  console.log(`   - RPC functions: ${hasFunctions ? '✅ Exist' : '❌ Missing'}`);
  
  return { hasAffiliateUrl, hasFunctions };
}

async function main() {
  console.log('🚀 TravelViz Database Migration Checker\n');
  console.log(`Supabase URL: ${supabaseUrl}`);
  console.log(`Project ID: ${supabaseUrl.match(/https:\/\/([^.]+)/)?.[1]}\n`);
  
  const { hasAffiliateUrl, hasFunctions } = await checkDatabaseState().catch(() => ({
    hasAffiliateUrl: false,
    hasFunctions: false
  }));
  
  if (hasAffiliateUrl && hasFunctions) {
    console.log('\n✅ All migrations appear to be applied!');
    return;
  }
  
  console.log('\n📝 Migrations needed. Please follow these steps:\n');
  console.log('1. Open Supabase SQL Editor:');
  console.log(`   ${supabaseUrl}/project/default/sql\n`);
  
  if (!hasAffiliateUrl) {
    console.log('2. Run Migration 017 (Schema fixes and indexes):');
    console.log('   - Copy the contents of: packages/hub/src/db/migrations/017_schema_fixes_and_missing_indexes.sql');
    console.log('   - Paste into SQL Editor and click "Run"\n');
  }
  
  if (!hasFunctions) {
    console.log('3. Run Migration 018 (RPC functions):');
    console.log('   - Copy the contents of: packages/hub/src/db/migrations/018_rpc_functions.sql');
    console.log('   - Paste into SQL Editor and click "Run"\n');
  }
  
  console.log('4. Verify migrations:');
  console.log('   - Check the "Database" → "Tables" → "activities" table for affiliate_url column');
  console.log('   - Check the "Database" → "Functions" section for the new RPC functions\n');
  
  // Also output the migration files for easy access
  const migration017Path = path.join(__dirname, 'migrations', '017_schema_fixes_and_missing_indexes.sql');
  const migration018Path = path.join(__dirname, 'migrations', '018_rpc_functions.sql');
  
  if (fs.existsSync(migration017Path) && !hasAffiliateUrl) {
    console.log('📄 Migration 017 preview (first 20 lines):');
    const content = fs.readFileSync(migration017Path, 'utf8');
    const lines = content.split('\n').slice(0, 20);
    console.log(lines.join('\n'));
    console.log('... (truncated)\n');
  }
}

main().catch(console.error);