{"Import conversation flow - happy path": {"name": "Import conversation flow - happy path", "file": "", "lastRun": 0, "duration": 0, "failures": 0, "priority": "critical", "tags": ["import", "user-flow", "critical-path"], "dependencies": ["InputStep.tsx", "ParsingStep.tsx", "PreviewStep.tsx", "CreatingStep.tsx"]}, "Homepage loads correctly": {"name": "Homepage loads correctly", "file": "", "lastRun": 0, "duration": 0, "failures": 0, "priority": "high", "tags": ["homepage", "navigation", "smoke"], "dependencies": ["layout.tsx", "page.tsx"]}, "Import page loads and form works": {"name": "Import page loads and form works", "file": "", "lastRun": 0, "duration": 0, "failures": 0, "priority": "high", "tags": ["import", "form-validation", "smoke"], "dependencies": ["InputStep.tsx", "ImportWizard.tsx"]}, "Import page loads within performance budget": {"name": "Import page loads within performance budget", "file": "", "lastRun": 0, "duration": 0, "failures": 0, "priority": "medium", "tags": ["performance", "web-vitals", "performance"], "dependencies": ["ImportWizard.tsx", "layout.tsx"]}, "Error handling works correctly": {"name": "Error handling works correctly", "file": "", "lastRun": 0, "duration": 0, "failures": 0, "priority": "critical", "tags": ["error-handling", "validation", "critical-path"], "dependencies": ["InputStep.tsx", "ImportContext.tsx"]}, "Mobile responsive design works": {"name": "Mobile responsive design works", "file": "", "lastRun": 0, "duration": 0, "failures": 0, "priority": "high", "tags": ["mobile", "responsive", "smoke"], "dependencies": ["ImportWizard.tsx", "ResponsiveContainer.tsx"]}}