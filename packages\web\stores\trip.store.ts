import { create } from 'zustand';
import { api } from '@/lib/api';
import { reorderActivities as reorderActivitiesApi } from '../lib/api/activities';
import type { Trip, Activity, PaginatedTripsResult, TripResponse } from '@travelviz/shared';
import { ActivityType } from '@travelviz/shared';

// Debounce utility to prevent rapid successive calls
function debounce<T extends (...args: any[]) => any>(func: T, wait: number): T {
  let timeout: NodeJS.Timeout;
  return ((...args: any[]) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func.apply(null, args), wait);
  }) as T;
}

// Re-export types for convenience
export type { Trip, Activity };

export interface TripState {
  // State
  trips: TripResponse[];
  currentTrip: TripResponse | null;
  selectedActivity: Activity | null;
  isLoading: boolean;
  error: string | null;
  importInProgress: boolean;
  
  // Pagination state
  currentPage: number;
  totalPages: number;
  totalTrips: number;
  hasNextPage: boolean;
  hasPrevPage: boolean;
  pageSize: number;
  
  // Actions
  setTrips: (trips: Trip[]) => void;
  setCurrentTrip: (trip: Trip | null) => void;
  selectActivity: (activity: Activity | null) => void;
  setLoading: (isLoading: boolean) => void;
  setError: (error: string | null) => void;
  fetchTrips: () => Promise<void>;
  fetchTripsPaginated: (page?: number, limit?: number) => Promise<void>;
  fetchTripById: (id: string) => Promise<void>;
  createTrip: (tripData: Partial<Trip>) => Promise<Trip>;
  updateTrip: (id: string, updates: Partial<Trip>) => Promise<void>;
  deleteTrip: (id: string) => Promise<void>;
  addActivity: (tripId: string, activity: Partial<Activity>) => Promise<Activity>;
  addActivityFromPlace: (placeId: string, tripId: string) => Promise<Activity>;
  updateActivity: (tripId: string, activityId: string, updates: Partial<Activity>) => Promise<void>;
  deleteActivity: (tripId: string, activityId: string) => Promise<void>;
  reorderActivities: (tripId: string, orderedIds: string[]) => Promise<void>;
  parseAndCreateTrip: (text: string, source: 'chatgpt' | 'claude' | 'gemini', model?: string) => Promise<Trip>;
  parseAndCreateTripFromFile: (file: File, source: 'chatgpt' | 'claude' | 'gemini') => Promise<Trip>;
}


// Track ongoing requests to prevent duplicates
let ongoingFetchRequest: Promise<void> | null = null;

export const useTripStore = create<TripState>((set) => ({
  // Initial state
  trips: [],
  currentTrip: null,
  selectedActivity: null,
  isLoading: false,
  error: null,
  importInProgress: false,

  // Pagination initial state
  currentPage: 1,
  totalPages: 0,
  totalTrips: 0,
  hasNextPage: false,
  hasPrevPage: false,
  pageSize: 20,

  // Actions
  setTrips: (trips) => set({ trips: trips as any }),
  setCurrentTrip: (trip) => set({ currentTrip: trip as any }),
  selectActivity: (activity) => set({ selectedActivity: activity }),
  setLoading: (isLoading) => set({ isLoading }),
  setError: (error) => set({ error }),

  fetchTrips: async () => {
    set({ isLoading: true, error: null });
    
    try {
      const trips = await api.trips.list();
      set({ trips: trips as any, isLoading: false });
    } catch (error) {
      set({ error: error instanceof Error ? error.message : 'An error occurred', isLoading: false });
      throw error;
    }
  },

  fetchTripsPaginated: async (page = 1, limit = 20) => {
    // Prevent concurrent requests
    if (ongoingFetchRequest) {
      console.log('Skipping duplicate fetchTripsPaginated call');
      return ongoingFetchRequest;
    }

    const requestPromise = (async () => {
      set({ isLoading: true, error: null });

      try {
        const result = await api.trips.listPaginated(page, limit);
        set({
          trips: result.data as any,
          isLoading: false,
          currentPage: result.pagination.page,
          totalPages: result.pagination.totalPages,
          totalTrips: result.pagination.total,
          hasNextPage: result.pagination.hasNext,
          hasPrevPage: result.pagination.hasPrev,
          pageSize: result.pagination.limit
        });
      } catch (error) {
        set({ error: error instanceof Error ? error.message : 'An error occurred', isLoading: false });
        throw error;
      } finally {
        ongoingFetchRequest = null;
      }
    })();

    ongoingFetchRequest = requestPromise;
    return requestPromise;
  },

  fetchTripById: async (id: string) => {
    set({ isLoading: true, error: null });
    
    try {
      const trip = await api.trips.get(id);
      set({ currentTrip: trip as any, isLoading: false });
    } catch (error) {
      set({ error: error instanceof Error ? error.message : 'An error occurred', isLoading: false });
      throw error;
    }
  },

  createTrip: async (tripData: Partial<Trip>) => {
    set({ isLoading: true, error: null });
    
    try {
      const newTrip = await api.trips.create(tripData);
      set(state => ({
        trips: [...state.trips, newTrip as any],
        isLoading: false,
      }));

      return newTrip;
    } catch (error) {
      set({ error: error instanceof Error ? error.message : 'An error occurred', isLoading: false });
      throw error;
    }
  },

  updateTrip: async (id: string, updates: Partial<Trip>) => {
    set({ isLoading: true, error: null });
    
    try {
      const updatedTrip = await api.trips.update(id, updates);
      set(state => ({
        trips: state.trips.map(trip => 
          trip.id === id ? updatedTrip as any : trip
        ),
        currentTrip: state.currentTrip?.id === id ? updatedTrip as any : state.currentTrip,
        isLoading: false,
      }));
    } catch (error) {
      set({ error: error instanceof Error ? error.message : 'An error occurred', isLoading: false });
      throw error;
    }
  },

  deleteTrip: async (id: string) => {
    set({ isLoading: true, error: null });
    
    try {
      await api.trips.delete(id);
      set(state => ({
        trips: state.trips.filter(trip => trip.id !== id),
        currentTrip: state.currentTrip?.id === id ? null : state.currentTrip,
        isLoading: false,
      }));
    } catch (error) {
      set({ error: error instanceof Error ? error.message : 'An error occurred', isLoading: false });
      throw error;
    }
  },

  addActivity: async (tripId: string, activity: Partial<Activity>) => {
    set({ isLoading: true, error: null });
    
    try {
      // Ensure required fields are present for ActivityCreateInput
      const activityInput = {
        title: activity.title || '',
        type: activity.type || 'other' as ActivityType,
        ...activity
      };
      const newActivity = await api.trips.addActivity(tripId, activityInput as any);
      
      // Update the trip in state
      set(state => {
        const updatedTrips = state.trips.map(trip => {
          if (trip.id === tripId) {
            return {
              ...trip,
              activities: [...(trip.activities || []), newActivity] as Activity[],
            };
          }
          return trip;
        });

        const updatedCurrentTrip = state.currentTrip?.id === tripId
          ? {
              ...state.currentTrip,
              activities: [...(state.currentTrip.activities || []), newActivity] as Activity[],
            }
          : state.currentTrip;

        return {
          trips: updatedTrips,
          currentTrip: updatedCurrentTrip,
          isLoading: false,
        };
      });

      return newActivity;
    } catch (error) {
      set({ error: error instanceof Error ? error.message : 'An error occurred', isLoading: false });
      throw error;
    }
  },

  addActivityFromPlace: async (placeId: string, tripId: string) => {
    // Create optimistic activity
    const optimisticActivity: Activity = {
      id: `temp-${Date.now()}`,
      title: 'Loading...',
      description: null,
      location: null,
      location_lat: null,
      location_lng: null,
      start_time: null,
      end_time: null,
      price: 0,
      currency: 'USD',
      type: ActivityType.activity,
      trip_id: tripId,
      position: 0,
      notes: null,
      booking_reference: null,
      metadata: {},
      attachments: [],
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    };

    // Optimistic update - add temporary activity immediately
    set(state => {
      const updatedTrips = state.trips.map(trip => {
        if (trip.id === tripId) {
          return {
            ...trip,
            activities: [...(trip.activities || []), optimisticActivity],
          };
        }
        return trip;
      });

      const updatedCurrentTrip = state.currentTrip?.id === tripId
        ? {
            ...state.currentTrip,
            activities: [...(state.currentTrip.activities || []), optimisticActivity],
          }
        : state.currentTrip;

      return {
        trips: updatedTrips,
        currentTrip: updatedCurrentTrip,
        isLoading: true,
        error: null,
      };
    });
    
    try {
      const newActivity = await api.trips.activities.addFromPlace(placeId, tripId);
      
      // Replace optimistic activity with real one
      set(state => {
        const updatedTrips = state.trips.map(trip => {
          if (trip.id === tripId) {
            return {
              ...trip,
              activities: (trip.activities || []).map(activity => 
                activity.id === optimisticActivity.id ? newActivity : activity
              ),
            };
          }
          return trip;
        });

        const updatedCurrentTrip = state.currentTrip?.id === tripId
          ? {
              ...state.currentTrip,
              activities: (state.currentTrip.activities || []).map(activity => 
                activity.id === optimisticActivity.id ? newActivity : activity
              ),
            }
          : state.currentTrip;

        return {
          trips: updatedTrips,
          currentTrip: updatedCurrentTrip,
          isLoading: false,
        };
      });

      return newActivity;
    } catch (error) {
      // Remove optimistic activity on error
      set(state => {
        const updatedTrips = state.trips.map(trip => {
          if (trip.id === tripId) {
            return {
              ...trip,
              activities: (trip.activities || []).filter(activity => 
                activity.id !== optimisticActivity.id
              ),
            };
          }
          return trip;
        });

        const updatedCurrentTrip = state.currentTrip?.id === tripId
          ? {
              ...state.currentTrip,
              activities: (state.currentTrip.activities || []).filter(activity => 
                activity.id !== optimisticActivity.id
              ),
            }
          : state.currentTrip;

        return {
          trips: updatedTrips,
          currentTrip: updatedCurrentTrip,
          isLoading: false,
          error: error instanceof Error ? error.message : 'Failed to add activity',
        };
      });
      
      throw error;
    }
  },

  updateActivity: async (tripId: string, activityId: string, updates: Partial<Activity>) => {
    set({ isLoading: true, error: null });
    
    try {
      // Convert null values to undefined for API compatibility
      const apiUpdates = Object.entries(updates).reduce((acc, [key, value]) => {
        acc[key] = value === null ? undefined : value;
        return acc;
      }, {} as any);
      
      const updatedActivity = await api.trips.activities.update(tripId, activityId, apiUpdates);
      
      // Update the activity in state
      set(state => {
        const updatedTrips = state.trips.map(trip => {
          if (trip.id === tripId && trip.activities) {
            return {
              ...trip,
              activities: trip.activities.map(activity =>
                activity.id === activityId ? updatedActivity : activity
              ),
            };
          }
          return trip;
        });

        const updatedCurrentTrip = state.currentTrip?.id === tripId && state.currentTrip.activities
          ? {
              ...state.currentTrip,
              activities: state.currentTrip.activities.map(activity =>
                activity.id === activityId ? updatedActivity : activity
              ),
            }
          : state.currentTrip;

        return {
          trips: updatedTrips,
          currentTrip: updatedCurrentTrip,
          isLoading: false,
        };
      });
    } catch (error) {
      set({ error: error instanceof Error ? error.message : 'An error occurred', isLoading: false });
      throw error;
    }
  },

  deleteActivity: async (tripId: string, activityId: string) => {
    set({ isLoading: true, error: null });
    
    try {
      await api.trips.activities.delete(tripId, activityId);

      // Remove the activity from state
      set(state => {
        const updatedTrips = state.trips.map(trip => {
          if (trip.id === tripId && trip.activities) {
            return {
              ...trip,
              activities: trip.activities.filter(activity => activity.id !== activityId),
            };
          }
          return trip;
        });

        const updatedCurrentTrip = state.currentTrip?.id === tripId && state.currentTrip.activities
          ? {
              ...state.currentTrip,
              activities: state.currentTrip.activities.filter(activity => activity.id !== activityId),
            }
          : state.currentTrip;

        return {
          trips: updatedTrips,
          currentTrip: updatedCurrentTrip,
          isLoading: false,
        };
      });
    } catch (error) {
      set({ error: error instanceof Error ? error.message : 'An error occurred', isLoading: false });
      throw error;
    }
  },

  reorderActivities: async (tripId: string, orderedIds: string[]) => {
    // Store the original order for rollback
    const state = useTripStore.getState();
    const trip = state.trips.find(t => t.id === tripId) || state.currentTrip;
    const originalActivities = trip?.activities ? [...trip.activities] : [];
    
    // Optimistic update - immediately update the UI
    set(state => {
      const reorderedActivities = orderedIds.map((id, index) => {
        const activity = originalActivities.find(a => a.id === id);
        if (!activity) throw new Error(`Activity ${id} not found`);
        return { ...activity, position: index };
      });

      const updatedTrips = state.trips.map(trip => {
        if (trip.id === tripId) {
          return {
            ...trip,
            activities: reorderedActivities,
          };
        }
        return trip;
      });

      const updatedCurrentTrip = state.currentTrip?.id === tripId
        ? {
            ...state.currentTrip,
            activities: reorderedActivities,
          }
        : state.currentTrip;

      return {
        trips: updatedTrips,
        currentTrip: updatedCurrentTrip,
      };
    });
    
    try {
      // Call the API to persist the new order
      const updatedActivities = await reorderActivitiesApi(tripId, orderedIds);
      
      // Update with the server response
      set(state => {
        const updatedTrips = state.trips.map(trip => {
          if (trip.id === tripId) {
            return {
              ...trip,
              activities: updatedActivities,
            };
          }
          return trip;
        });

        const updatedCurrentTrip = state.currentTrip?.id === tripId
          ? {
              ...state.currentTrip,
              activities: updatedActivities,
            }
          : state.currentTrip;

        return {
          trips: updatedTrips,
          currentTrip: updatedCurrentTrip,
        };
      });
    } catch (error) {
      // Rollback to original order on error
      set(state => {
        const updatedTrips = state.trips.map(trip => {
          if (trip.id === tripId) {
            return {
              ...trip,
              activities: originalActivities,
            };
          }
          return trip;
        });

        const updatedCurrentTrip = state.currentTrip?.id === tripId
          ? {
              ...state.currentTrip,
              activities: originalActivities,
            }
          : state.currentTrip;

        return {
          trips: updatedTrips,
          currentTrip: updatedCurrentTrip,
          error: error instanceof Error ? error.message : 'Failed to reorder activities',
        };
      });
      
      throw error;
    }
  },

  parseAndCreateTrip: async (text: string, source: 'chatgpt' | 'claude' | 'gemini') => {
    // Check if import is already in progress
    const state = useTripStore.getState();
    if (state.importInProgress) {
      throw new Error('An import is already in progress. Please wait for it to complete.');
    }
    
    set({ isLoading: true, error: null, importInProgress: true });
    
    try {
      // Call the import API endpoint which creates the trip and activities
      const response = await api.import.parse(text, source);
      const newTrip = response.trip;
      
      // Add the new trip to the state
      set(state => ({
        trips: [...state.trips, newTrip as any],
        currentTrip: newTrip as any,
        isLoading: false,
        importInProgress: false,
      }));

      return newTrip;
    } catch (error) {
      set({ error: error instanceof Error ? error.message : 'Failed to parse and create trip', isLoading: false, importInProgress: false });
      throw error;
    }
  },

  parseAndCreateTripFromFile: async (file: File, source: 'chatgpt' | 'claude' | 'gemini') => {
    // Check if import is already in progress
    const state = useTripStore.getState();
    if (state.importInProgress) {
      throw new Error('An import is already in progress. Please wait for it to complete.');
    }
    
    set({ isLoading: true, error: null, importInProgress: true });
    
    try {
      // Call the file upload API endpoint which creates the trip and activities
      const response = await api.import.upload(file, source);
      const newTrip = response.trip;
      
      // Add the new trip to the state
      set(state => ({
        trips: [...state.trips, newTrip as any],
        currentTrip: newTrip as any,
        isLoading: false,
        importInProgress: false,
      }));

      return newTrip;
    } catch (error) {
      set({ error: error instanceof Error ? error.message : 'Failed to upload and create trip', isLoading: false, importInProgress: false });
      throw error;
    }
  },
}));