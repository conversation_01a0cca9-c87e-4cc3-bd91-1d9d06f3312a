/** @type {import('next').NextConfig} */
const isDev = process.env.NODE_ENV === 'development';

const nextConfig = {
  // Type checking handled separately for faster dev startup
  typescript: {
    ignoreBuildErrors: isDev,
  },

  eslint: {
    ignoreDuringBuilds: true,
  },

  images: {
    unoptimized: isDev, // Faster in dev
    formats: ['image/webp', 'image/avif'],
    deviceSizes: [640, 750, 828, 1080, 1200, 1920, 2048, 3840],
    imageSizes: [16, 32, 48, 64, 96, 128, 256, 384],
  },

  // Optimize production builds
  productionBrowserSourceMaps: false,

  // Turbopack configuration (only enable if available)
  ...(isDev && {
    turbopack: {
      rules: {
        '*.module.css': {
          loaders: ['css-loader'],
          as: '*.css',
        },
      },
    },
  }),

  // Server external packages (moved from experimental)
  serverExternalPackages: [
    '@radix-ui/react-accordion',
    '@radix-ui/react-alert-dialog',
    '@radix-ui/react-avatar',
  ],

  // Experimental features
  experimental: {
    // Optimize package imports
    optimizePackageImports: ['@travelviz/shared', 'framer-motion', 'lucide-react'],
  },

  // Development server optimizations
  onDemandEntries: isDev ? {
    maxInactiveAge: 60 * 1000,
    pagesBufferLength: 5,
  } : undefined,

  webpack: (config, { dev, isServer }) => {
    if (dev) {
      // Skip minimization in development
      config.optimization.minimize = false;

      // Disable performance hints in development
      config.performance = {
        hints: false,
      };

      // Enable filesystem cache for faster rebuilds
      config.cache = {
        type: 'filesystem',
        allowCollectingMemory: true,
        buildDependencies: {
          config: [require.resolve('./next.config.js')],
        },
      };
    } else {
      // Production optimizations
      config.optimization = {
        ...config.optimization,
        usedExports: true,
        sideEffects: false,
      };
    }

    return config;
  },

  async headers() {
    return [
      {
        source: '/:path*',
        headers: [
          {
            key: 'X-Frame-Options',
            value: 'DENY',
          },
          {
            key: 'X-Content-Type-Options',
            value: 'nosniff',
          },
          {
            key: 'Referrer-Policy',
            value: 'origin-when-cross-origin',
          },
          {
            key: 'Permissions-Policy',
            value: 'camera=(), microphone=(), geolocation=()',
          },
        ],
      },
    ];
  },
};

module.exports = nextConfig;