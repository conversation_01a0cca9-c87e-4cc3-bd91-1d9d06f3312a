declare module 'react-map-gl/mapbox' {
  import { FC, ReactNode, CSSProperties } from 'react';
  
  export interface ViewState {
    longitude: number;
    latitude: number;
    zoom: number;
    pitch?: number;
    bearing?: number;
    padding?: {
      top?: number;
      bottom?: number;
      left?: number;
      right?: number;
    };
  }

  export interface MapProps {
    mapStyle?: string;
    mapboxAccessToken?: string;
    initialViewState?: ViewState;
    onMove?: (evt: { viewState: ViewState }) => void;
    reuseMaps?: boolean;
    children?: ReactNode;
    style?: CSSProperties;
    [key: string]: any;
  }

  export interface MarkerProps {
    longitude: number;
    latitude: number;
    anchor?: 'center' | 'left' | 'right' | 'top' | 'bottom' | 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right';
    color?: string;
    scale?: number;
    draggable?: boolean;
    onDragStart?: (event: any) => void;
    onDrag?: (event: any) => void;
    onDragEnd?: (event: any) => void;
    onClick?: (event: any) => void;
    children?: ReactNode;
  }

  export interface PopupProps {
    longitude: number;
    latitude: number;
    anchor?: 'center' | 'left' | 'right' | 'top' | 'bottom' | 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right';
    maxWidth?: string;
    onClose?: () => void;
    closeButton?: boolean;
    closeOnClick?: boolean;
    closeOnMove?: boolean;
    focusAfterOpen?: boolean;
    className?: string;
    children?: ReactNode;
  }

  export const Map: FC<MapProps>;
  export const Marker: FC<MarkerProps>;
  export const Popup: FC<PopupProps>;
  export const NavigationControl: FC<{ position?: 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right' }>;
  export const ScaleControl: FC<{ position?: 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right' }>;
  
  export default Map;
}