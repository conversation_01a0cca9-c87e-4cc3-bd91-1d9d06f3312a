# Supabase Migrations

This directory contains SQL migrations for the TravelViz Hub database schema.

## Migrations

1. **001_initial_schema.sql** - Creates the core tables:
   - `profiles` - User profiles extending auth.users
   - `trips` - User trips
   - `activities` - Trip activities/events
   - `trip_shares` - Tracking for shared trips
   - `affiliate_clicks` - Revenue tracking for affiliate links

2. **002_row_level_security.sql** - Sets up Row Level Security (RLS) policies:
   - Users can only access their own data
   - Public trips are accessible to everyone
   - Service role has full access for admin operations

## Applying Migrations

### Option 1: Using Supabase Dashboard

1. Go to your Supabase project dashboard
2. Navigate to SQL Editor
3. Copy and paste each migration file in order
4. Run each migration

### Option 2: Using Supabase CLI

```bash
# Install Supabase CLI if not already installed
npm install -g supabase

# Link to your project
supabase link --project-ref <your-project-ref>

# Apply migrations
supabase db push
```

### Option 3: Direct PostgreSQL Connection

```bash
# Connect to your database
psql <your-connection-string>

# Run migrations
\i 001_initial_schema.sql
\i 002_row_level_security.sql
```

## Important Notes

1. **Order Matters**: Run migrations in numerical order
2. **Idempotent**: Migrations use `IF NOT EXISTS` where possible
3. **Service Role**: The hub API uses the service role key which bypasses RLS
4. **User Access**: Frontend should use anon key with RLS enabled

## Rollback

If you need to rollback, create a new migration that undoes the changes. Never modify existing migrations that have been applied to production.