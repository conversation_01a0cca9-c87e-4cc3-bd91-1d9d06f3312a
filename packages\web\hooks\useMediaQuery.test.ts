import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { renderHook, act } from '@testing-library/react';
import { useMediaQuery, useBreakpoint, useIsMobile, useIsTablet, useIsDesktop } from './useMediaQuery';

// Mock window.matchMedia
const mockMatchMedia = vi.fn();

beforeEach(() => {
  vi.clearAllMocks();
  
  // Setup matchMedia mock
  Object.defineProperty(window, 'matchMedia', {
    writable: true,
    value: mockMatchMedia,
  });
});

afterEach(() => {
  vi.resetAllMocks();
});

describe('useMediaQuery', () => {
  it('should return false when window is undefined (SSR)', () => {
    // Mock window as undefined for SSR
    const originalWindow = global.window;
    // @ts-ignore
    delete global.window;

    const { result } = renderHook(() => useMediaQuery('(min-width: 768px)'));
    expect(result.current).toBe(false);

    // Restore window
    global.window = originalWindow;
  });

  it('should return initial match state', () => {
    const mockMediaQuery = {
      matches: true,
      addEventListener: vi.fn(),
      removeEventListener: vi.fn(),
    };
    mockMatchMedia.mockReturnValue(mockMediaQuery);

    const { result } = renderHook(() => useMediaQuery('(min-width: 768px)'));
    
    expect(mockMatchMedia).toHaveBeenCalledWith('(min-width: 768px)');
    expect(result.current).toBe(true);
  });

  it('should update when media query changes (modern browsers)', () => {
    const mockMediaQuery = {
      matches: false,
      addEventListener: vi.fn(),
      removeEventListener: vi.fn(),
    };
    mockMatchMedia.mockReturnValue(mockMediaQuery);

    const { result } = renderHook(() => useMediaQuery('(min-width: 768px)'));
    
    expect(result.current).toBe(false);

    // Simulate media query change
    act(() => {
      const changeHandler = mockMediaQuery.addEventListener.mock.calls[0][1];
      changeHandler({ matches: true });
    });

    expect(result.current).toBe(true);
  });

  it('should handle legacy browsers without addEventListener', () => {
    const mockMediaQuery = {
      matches: false,
      addListener: vi.fn(),
      removeListener: vi.fn(),
    };
    mockMatchMedia.mockReturnValue(mockMediaQuery);

    const { result } = renderHook(() => useMediaQuery('(min-width: 768px)'));
    
    expect(result.current).toBe(false);
    expect(mockMediaQuery.addListener).toHaveBeenCalled();

    // Simulate media query change
    act(() => {
      const changeHandler = mockMediaQuery.addListener.mock.calls[0][0];
      changeHandler({ matches: true });
    });

    expect(result.current).toBe(true);
  });

  it('should cleanup event listeners on unmount (modern)', () => {
    const mockMediaQuery = {
      matches: false,
      addEventListener: vi.fn(),
      removeEventListener: vi.fn(),
    };
    mockMatchMedia.mockReturnValue(mockMediaQuery);

    const { unmount } = renderHook(() => useMediaQuery('(min-width: 768px)'));
    
    unmount();
    
    expect(mockMediaQuery.removeEventListener).toHaveBeenCalledWith('change', expect.any(Function));
  });

  it('should cleanup event listeners on unmount (legacy)', () => {
    const mockMediaQuery = {
      matches: false,
      addListener: vi.fn(),
      removeListener: vi.fn(),
    };
    mockMatchMedia.mockReturnValue(mockMediaQuery);

    const { unmount } = renderHook(() => useMediaQuery('(min-width: 768px)'));
    
    unmount();
    
    expect(mockMediaQuery.removeListener).toHaveBeenCalledWith(expect.any(Function));
  });

  it('should update when query changes', () => {
    const mockMediaQuery1 = {
      matches: true,
      addEventListener: vi.fn(),
      removeEventListener: vi.fn(),
    };
    const mockMediaQuery2 = {
      matches: false,
      addEventListener: vi.fn(),
      removeEventListener: vi.fn(),
    };

    mockMatchMedia
      .mockReturnValueOnce(mockMediaQuery1)
      .mockReturnValueOnce(mockMediaQuery2);

    const { result, rerender } = renderHook(
      ({ query }) => useMediaQuery(query),
      { initialProps: { query: '(min-width: 768px)' } }
    );

    expect(result.current).toBe(true);

    rerender({ query: '(min-width: 1024px)' });

    expect(result.current).toBe(false);
    expect(mockMatchMedia).toHaveBeenCalledWith('(min-width: 1024px)');
  });
});

describe('useBreakpoint', () => {
  const setupBreakpointTest = (breakpoints: Record<string, boolean>) => {
    mockMatchMedia.mockImplementation((query: string) => ({
      matches: (() => {
        if (query.includes('1536px')) return breakpoints['2xl'] || false;
        if (query.includes('1280px')) return breakpoints.xl || false;
        if (query.includes('1024px')) return breakpoints.lg || false;
        if (query.includes('768px')) return breakpoints.md || false;
        if (query.includes('640px')) return breakpoints.sm || false;
        return false;
      })(),
      addEventListener: vi.fn(),
      removeEventListener: vi.fn(),
    }));
  };

  it('should return xs for mobile', () => {
    setupBreakpointTest({});
    
    const { result } = renderHook(() => useBreakpoint());
    expect(result.current).toBe('xs');
  });

  it('should return sm for small screens', () => {
    setupBreakpointTest({ sm: true });
    
    const { result } = renderHook(() => useBreakpoint());
    expect(result.current).toBe('sm');
  });

  it('should return md for medium screens', () => {
    setupBreakpointTest({ sm: true, md: true });
    
    const { result } = renderHook(() => useBreakpoint());
    expect(result.current).toBe('md');
  });

  it('should return lg for large screens', () => {
    setupBreakpointTest({ sm: true, md: true, lg: true });
    
    const { result } = renderHook(() => useBreakpoint());
    expect(result.current).toBe('lg');
  });

  it('should return xl for extra large screens', () => {
    setupBreakpointTest({ sm: true, md: true, lg: true, xl: true });
    
    const { result } = renderHook(() => useBreakpoint());
    expect(result.current).toBe('xl');
  });

  it('should return 2xl for extra extra large screens', () => {
    setupBreakpointTest({ sm: true, md: true, lg: true, xl: true, '2xl': true });
    
    const { result } = renderHook(() => useBreakpoint());
    expect(result.current).toBe('2xl');
  });
});

describe('useIsMobile', () => {
  it('should return true for mobile screens', () => {
    mockMatchMedia.mockReturnValue({
      matches: false, // Not md or larger
      addEventListener: vi.fn(),
      removeEventListener: vi.fn(),
    });

    const { result } = renderHook(() => useIsMobile());
    expect(result.current).toBe(true);
  });

  it('should return false for desktop screens', () => {
    mockMatchMedia.mockReturnValue({
      matches: true, // md or larger
      addEventListener: vi.fn(),
      removeEventListener: vi.fn(),
    });

    const { result } = renderHook(() => useIsMobile());
    expect(result.current).toBe(false);
  });
});

describe('useIsTablet', () => {
  it('should return true for tablet screens', () => {
    let callCount = 0;
    mockMatchMedia.mockImplementation(() => {
      callCount++;
      return {
        matches: callCount === 1 ? true : false, // md but not lg
        addEventListener: vi.fn(),
        removeEventListener: vi.fn(),
      };
    });

    const { result } = renderHook(() => useIsTablet());
    expect(result.current).toBe(true);
  });

  it('should return false for mobile screens', () => {
    mockMatchMedia.mockReturnValue({
      matches: false, // Not md
      addEventListener: vi.fn(),
      removeEventListener: vi.fn(),
    });

    const { result } = renderHook(() => useIsTablet());
    expect(result.current).toBe(false);
  });

  it('should return false for desktop screens', () => {
    mockMatchMedia.mockReturnValue({
      matches: true, // Both md and lg
      addEventListener: vi.fn(),
      removeEventListener: vi.fn(),
    });

    const { result } = renderHook(() => useIsTablet());
    expect(result.current).toBe(false);
  });
});

describe('useIsDesktop', () => {
  it('should return true for desktop screens', () => {
    mockMatchMedia.mockReturnValue({
      matches: true, // lg or larger
      addEventListener: vi.fn(),
      removeEventListener: vi.fn(),
    });

    const { result } = renderHook(() => useIsDesktop());
    expect(result.current).toBe(true);
  });

  it('should return false for mobile/tablet screens', () => {
    mockMatchMedia.mockReturnValue({
      matches: false, // Not lg
      addEventListener: vi.fn(),
      removeEventListener: vi.fn(),
    });

    const { result } = renderHook(() => useIsDesktop());
    expect(result.current).toBe(false);
  });
});