import { Redis } from '@upstash/redis';
import { logger } from '../utils/logger';

interface CircuitBreakerConfig {
  failureThreshold: number;
  recoveryTimeout: number;
  monitoringPeriod: number;
}

interface ConnectionPoolConfig {
  maxConnections: number;
  minConnections: number;
  acquireTimeout: number;
  idleTimeout: number;
  retryOptions: {
    retries: number;
    backoff: (attempt: number) => number;
  };
  circuitBreaker: CircuitBreakerConfig;
}

enum CircuitBreakerState {
  CLOSED = 'CLOSED',
  OPEN = 'OPEN',
  HALF_OPEN = 'HALF_OPEN'
}

interface ConnectionMetrics {
  totalConnections: number;
  activeConnections: number;
  idleConnections: number;
  failedOperations: number;
  successfulOperations: number;
  circuitBreakerState: CircuitBreakerState;
}

/**
 * Circuit Breaker implementation for Redis operations
 */
class CircuitBreaker {
  private state: CircuitBreakerState = CircuitBreakerState.CLOSED;
  private failures: number = 0;
  private lastFailureTime: number = 0;
  private nextAttemptTime: number = 0;

  constructor(private config: CircuitBreakerConfig) {}

  async execute<T>(operation: () => Promise<T>): Promise<T> {
    if (this.state === CircuitBreakerState.OPEN) {
      if (Date.now() >= this.nextAttemptTime) {
        this.state = CircuitBreakerState.HALF_OPEN;
        logger.info('Circuit breaker transitioning to HALF_OPEN state');
      } else {
        throw new Error('Circuit breaker is OPEN - Redis operations temporarily disabled');
      }
    }

    try {
      const result = await operation();
      this.onSuccess();
      return result;
    } catch (error) {
      this.onFailure();
      throw error;
    }
  }

  private onSuccess(): void {
    this.failures = 0;
    if (this.state === CircuitBreakerState.HALF_OPEN) {
      this.state = CircuitBreakerState.CLOSED;
      logger.info('Circuit breaker transitioned to CLOSED state');
    }
  }

  private onFailure(): void {
    this.failures++;
    this.lastFailureTime = Date.now();

    if (this.failures >= this.config.failureThreshold) {
      this.state = CircuitBreakerState.OPEN;
      this.nextAttemptTime = Date.now() + this.config.recoveryTimeout;
      logger.warn('Circuit breaker OPENED due to failures', {
        failures: this.failures,
        threshold: this.config.failureThreshold,
        recoveryTime: new Date(this.nextAttemptTime).toISOString()
      });
    }
  }

  getState(): CircuitBreakerState {
    return this.state;
  }

  getMetrics() {
    return {
      state: this.state,
      failures: this.failures,
      lastFailureTime: this.lastFailureTime,
      nextAttemptTime: this.nextAttemptTime
    };
  }
}

/**
 * Redis Connection Pool with Circuit Breaker
 */
class RedisConnectionPool {
  private connections: Redis[] = [];
  private availableConnections: Redis[] = [];
  private circuitBreaker: CircuitBreaker;
  private metrics: ConnectionMetrics;
  private connectionQueue: Array<{
    resolve: (connection: Redis) => void;
    reject: (error: Error) => void;
    timestamp: number;
  }> = [];
  private cleanupInterval?: NodeJS.Timeout;
  private metricsInterval?: NodeJS.Timeout;

  constructor(private config: ConnectionPoolConfig) {
    this.circuitBreaker = new CircuitBreaker(config.circuitBreaker);
    this.metrics = {
      totalConnections: 0,
      activeConnections: 0,
      idleConnections: 0,
      failedOperations: 0,
      successfulOperations: 0,
      circuitBreakerState: CircuitBreakerState.CLOSED
    };

    // Initialize minimum connections
    this.initializePool();
    
    // Setup periodic cleanup
    this.cleanupInterval = setInterval(() => this.cleanupIdleConnections(), 60000); // Every minute
    this.metricsInterval = setInterval(() => this.updateMetrics(), 5000); // Every 5 seconds
  }

  private async initializePool(): Promise<void> {
    try {
      for (let i = 0; i < this.config.minConnections; i++) {
        const connection = await this.createConnection();
        this.connections.push(connection);
        this.availableConnections.push(connection);
      }
      this.metrics.totalConnections = this.connections.length;
      logger.info('Redis connection pool initialized', {
        minConnections: this.config.minConnections,
        totalConnections: this.metrics.totalConnections
      });
    } catch (error) {
      logger.error('Failed to initialize Redis connection pool', { 
        error: error instanceof Error ? error.message : String(error),
        stack: error instanceof Error ? error.stack : undefined
      });
    }
  }

  private async createConnection(): Promise<Redis> {
    // Support both naming conventions for backward compatibility
    const redisUrl = process.env.UPSTASH_REDIS_URL || process.env.UPSTASH_REDIS_REST_URL;
    const redisToken = process.env.UPSTASH_REDIS_TOKEN || process.env.UPSTASH_REDIS_REST_TOKEN;
    
    logger.debug('Creating Redis connection', {
      urlFound: !!redisUrl,
      tokenFound: !!redisToken,
      url: redisUrl?.substring(0, 30) + '...'
    });
    
    if (!redisUrl || !redisToken) {
      throw new Error('Redis credentials not configured. Please set UPSTASH_REDIS_URL/UPSTASH_REDIS_REST_URL and UPSTASH_REDIS_TOKEN/UPSTASH_REDIS_REST_TOKEN');
    }
    
    const redis = new Redis({
      url: redisUrl,
      token: redisToken,
      retry: this.config.retryOptions,
      automaticDeserialization: true
    });

    // Test the connection
    try {
      await redis.ping();
      logger.debug('Redis connection established');
      return redis;
    } catch (error) {
      logger.error('Failed to create Redis connection', { 
        error: error instanceof Error ? error.message : String(error),
        stack: error instanceof Error ? error.stack : undefined,
        redisUrl: redisUrl ? 'Set' : 'Not set',
        tokenSet: redisToken ? 'Set' : 'Not set'
      });
      throw error;
    }
  }

  private async acquireConnection(): Promise<Redis> {
    // Check if we have available connections
    if (this.availableConnections.length > 0) {
      const connection = this.availableConnections.pop()!;
      return connection;
    }

    // Create new connection if under max limit
    if (this.connections.length < this.config.maxConnections) {
      try {
        const connection = await this.createConnection();
        this.connections.push(connection);
        this.metrics.totalConnections = this.connections.length;
        return connection;
      } catch (error) {
        logger.error('Failed to create new Redis connection', { error });
        throw error;
      }
    }

    // Wait for connection to become available
    return new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        const index = this.connectionQueue.findIndex(item => item.resolve === resolve);
        if (index !== -1) {
          this.connectionQueue.splice(index, 1);
        }
        reject(new Error('Connection acquisition timeout'));
      }, this.config.acquireTimeout);

      this.connectionQueue.push({
        resolve: (connection) => {
          clearTimeout(timeout);
          resolve(connection);
        },
        reject: (error) => {
          clearTimeout(timeout);
          reject(error);
        },
        timestamp: Date.now()
      });
    });
  }

  private releaseConnection(connection: Redis): void {
    // Check if someone is waiting for a connection
    if (this.connectionQueue.length > 0) {
      const waiter = this.connectionQueue.shift()!;
      waiter.resolve(connection);
      return;
    }

    // Return to available pool
    this.availableConnections.push(connection);
  }

  async execute<T>(operation: (redis: Redis) => Promise<T>): Promise<T> {
    return this.circuitBreaker.execute(async () => {
      const connection = await this.acquireConnection();
      
      try {
        const result = await operation(connection);
        this.metrics.successfulOperations++;
        return result;
      } catch (error) {
        this.metrics.failedOperations++;
        logger.error('Redis operation failed', { error });
        throw error;
      } finally {
        this.releaseConnection(connection);
      }
    });
  }

  private cleanupIdleConnections(): void {
    const now = Date.now();
    // const idleThreshold = this.config.idleTimeout; // TODO: Implement idle connection cleanup
    
    // Only cleanup if we have more than minimum connections
    if (this.connections.length <= this.config.minConnections) {
      return;
    }

    // Clean up expired queue items
    this.connectionQueue = this.connectionQueue.filter(item => {
      if (now - item.timestamp > this.config.acquireTimeout) {
        item.reject(new Error('Connection acquisition timeout during cleanup'));
        return false;
      }
      return true;
    });

    logger.debug('Redis connection pool cleanup completed', {
      totalConnections: this.connections.length,
      availableConnections: this.availableConnections.length,
      queueLength: this.connectionQueue.length
    });
  }

  private updateMetrics(): void {
    this.metrics.activeConnections = this.connections.length - this.availableConnections.length;
    this.metrics.idleConnections = this.availableConnections.length;
    this.metrics.circuitBreakerState = this.circuitBreaker.getState();
  }

  getMetrics(): ConnectionMetrics {
    this.updateMetrics();
    return { ...this.metrics };
  }

  getCircuitBreakerMetrics() {
    return this.circuitBreaker.getMetrics();
  }

  async healthCheck(): Promise<boolean> {
    try {
      await this.execute(async (redis) => {
        await redis.ping();
      });
      return true;
    } catch (error) {
      logger.error('Redis health check failed', { error });
      return false;
    }
  }

  async shutdown(): Promise<void> {
    logger.info('Shutting down Redis connection pool');
    
    // Clear intervals to prevent memory leaks
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
      this.cleanupInterval = undefined;
    }
    if (this.metricsInterval) {
      clearInterval(this.metricsInterval);
      this.metricsInterval = undefined;
    }
    
    // Reject all queued requests
    for (const waiter of this.connectionQueue) {
      waiter.reject(new Error('Connection pool is shutting down'));
    }
    this.connectionQueue.length = 0;

    // Note: Upstash Redis connections don't need explicit closing
    // as they are HTTP-based connections
    this.connections.length = 0;
    this.availableConnections.length = 0;
    this.metrics.totalConnections = 0;
    
    logger.info('Redis connection pool shutdown complete');
  }
}

// Default configuration
const defaultConfig: ConnectionPoolConfig = {
  maxConnections: 20,
  minConnections: 5,
  acquireTimeout: 5000, // 5 seconds
  idleTimeout: 300000,  // 5 minutes
  retryOptions: {
    retries: 3,
    backoff: (attempt) => Math.min(attempt * 100, 3000)
  },
  circuitBreaker: {
    failureThreshold: 5,
    recoveryTimeout: 30000, // 30 seconds
    monitoringPeriod: 60000  // 1 minute
  }
};

// Lazy-initialized singleton instance
let _redisConnectionPool: RedisConnectionPool | null = null;

/**
 * Get the Redis connection pool instance (lazy initialization)
 * This ensures environment variables are loaded before creating the connection
 */
export function getRedisConnectionPool(): RedisConnectionPool {
  if (!_redisConnectionPool) {
    _redisConnectionPool = new RedisConnectionPool(defaultConfig);
  }
  return _redisConnectionPool;
}

// Export a getter for backward compatibility
export const redisConnectionPool = {
  execute: async <T>(operation: (redis: Redis) => Promise<T>): Promise<T> => {
    return getRedisConnectionPool().execute(operation);
  },
  healthCheck: async (): Promise<boolean> => {
    return getRedisConnectionPool().healthCheck();
  },
  getMetrics: (): ConnectionMetrics => {
    return getRedisConnectionPool().getMetrics();
  },
  getCircuitBreakerMetrics: () => {
    return getRedisConnectionPool().getCircuitBreakerMetrics();
  },
  shutdown: async (): Promise<void> => {
    return getRedisConnectionPool().shutdown();
  }
};

// Graceful shutdown handler
process.on('SIGTERM', async () => {
  if (_redisConnectionPool) {
    await _redisConnectionPool.shutdown();
  }
});

process.on('SIGINT', async () => {
  if (_redisConnectionPool) {
    await _redisConnectionPool.shutdown();
  }
});

export { CircuitBreakerState, type ConnectionMetrics, type CircuitBreakerConfig };