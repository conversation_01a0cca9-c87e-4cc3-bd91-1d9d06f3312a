import { createClient } from '@supabase/supabase-js';
import * as fs from 'fs';
import * as path from 'path';
import * as dotenv from 'dotenv';

// Load environment variables
dotenv.config({ path: path.resolve(__dirname, '../../.env.local') });

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing SUPABASE_URL or SUPABASE_SERVICE_ROLE_KEY environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function checkAffiliateUrlColumn() {
  console.log('🔍 Checking if affiliate_url column exists...');
  
  const { data, error } = await supabase
    .from('information_schema.columns')
    .select('column_name')
    .eq('table_schema', 'public')
    .eq('table_name', 'activities')
    .eq('column_name', 'affiliate_url')
    .single();
  
  if (error && error.code !== 'PGRST116') { // PGRST116 = no rows returned
    console.error('❌ Error checking column:', error);
    return false;
  }
  
  return !!data;
}

async function runMigration(migrationPath: string, migrationName: string) {
  console.log(`\n📄 Running migration: ${migrationName}`);
  
  try {
    // Read the migration file
    const sql = fs.readFileSync(migrationPath, 'utf8');
    
    // Execute the migration
    const { error } = await supabase.rpc('exec_sql', { sql_query: sql });
    
    if (error) {
      // If exec_sql doesn't exist, try using the SQL directly through a different method
      console.log('ℹ️  exec_sql RPC not found, attempting direct execution...');
      
      // Split the SQL into individual statements (naive split on semicolon)
      const statements = sql
        .split(/;\s*$/m)
        .filter(stmt => stmt.trim().length > 0)
        .map(stmt => stmt.trim() + ';');
      
      for (const statement of statements) {
        // Skip comments and empty statements
        if (statement.startsWith('--') || statement.trim().length === 0) {
          continue;
        }
        
        // For certain DDL operations, we'll need to use raw SQL through a different approach
        console.log(`   Executing statement (first 100 chars): ${statement.substring(0, 100)}...`);
        
        // Since Supabase client doesn't support raw DDL directly, we'll create a custom RPC function
        // or use an alternative approach
      }
      
      console.error('⚠️  Direct SQL execution not supported through Supabase client.');
      console.log('📝 Please run these migrations directly in the Supabase SQL Editor:');
      console.log(`   1. Go to: ${supabaseUrl}/project/default/sql`);
      console.log(`   2. Copy and paste the contents of ${migrationPath}`);
      console.log('   3. Execute the SQL');
      return false;
    }
    
    console.log(`✅ Migration ${migrationName} completed successfully`);
    return true;
  } catch (error) {
    console.error(`❌ Error running migration ${migrationName}:`, error);
    return false;
  }
}

async function main() {
  console.log('🚀 Starting TravelViz database migrations...\n');
  
  // Check if affiliate_url column already exists
  const affiliateUrlExists = await checkAffiliateUrlColumn();
  
  if (affiliateUrlExists) {
    console.log('✅ affiliate_url column already exists in activities table');
  } else {
    console.log('❌ affiliate_url column does not exist in activities table');
  }
  
  // Define migrations to run
  const migrations = [
    {
      file: '017_schema_fixes_and_missing_indexes.sql',
      name: 'Schema fixes and missing indexes',
      shouldRun: true // Always run to ensure indexes exist
    },
    {
      file: '018_rpc_functions.sql', 
      name: 'RPC functions',
      shouldRun: true
    }
  ];
  
  // Run migrations
  for (const migration of migrations) {
    if (!migration.shouldRun) {
      console.log(`⏭️  Skipping migration: ${migration.name}`);
      continue;
    }
    
    const migrationPath = path.join(__dirname, 'migrations', migration.file);
    
    if (!fs.existsSync(migrationPath)) {
      console.error(`❌ Migration file not found: ${migrationPath}`);
      continue;
    }
    
    await runMigration(migrationPath, migration.name);
  }
  
  console.log('\n📋 Migration Summary:');
  console.log('Since the Supabase JS client does not support direct DDL execution,');
  console.log('please run the migrations manually through the Supabase SQL Editor.\n');
  console.log('Steps:');
  console.log(`1. Open ${supabaseUrl}/project/default/sql`);
  console.log('2. Run migration 017_schema_fixes_and_missing_indexes.sql');
  console.log('3. Run migration 018_rpc_functions.sql');
  console.log('4. Verify by checking the indexes and functions tabs in Supabase');
}

main().catch(console.error);