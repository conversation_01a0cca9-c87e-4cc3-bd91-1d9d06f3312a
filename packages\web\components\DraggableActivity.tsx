'use client';

import React from 'react';
import { useSortable } from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { GripVertical } from 'lucide-react';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import type { Activity } from '@/stores/trip.store';
import { 
  Clock, 
  MapPin, 
  DollarSign, 
  Plane, 
  Hotel, 
  Car, 
  Utensils, 
  Activity as ActivityIcon,
  ShoppingBag,
  MoreHorizontal,
  Link,
  FileText,
  Map,
  Eye,
  Theater
} from 'lucide-react';
import { getSafeUrl } from '@travelviz/shared';
import { MagicCard, activityGlowColors } from '@/components/magic-ui/magic-card';

interface DraggableActivityProps {
  activity: Activity;
  formatTime: (datetime: string | undefined) => string | undefined;
  formatDuration: (start: string | undefined, end: string | undefined) => string | undefined;
}

const activityIcons: Record<string, any> = {
  flight: Plane,
  accommodation: Hotel,
  transport: Car,
  dining: Utensils,
  activity: ActivityIcon,
  shopping: ShoppingBag,
  car_rental: Car,
  tour: Map,
  sightseeing: Eye,
  entertainment: Theater,
  other: MoreHorizontal
};

const activityColors: Record<string, string> = {
  flight: 'bg-blue-500',
  accommodation: 'bg-green-500',
  transport: 'bg-purple-500',
  dining: 'bg-yellow-500',
  activity: 'bg-red-500',
  shopping: 'bg-pink-500',
  car_rental: 'bg-indigo-500',
  tour: 'bg-teal-500',
  sightseeing: 'bg-orange-500',
  entertainment: 'bg-violet-500',
  other: 'bg-gray-500'
};

export default function DraggableActivity({ 
  activity, 
  formatTime, 
  formatDuration
}: DraggableActivityProps) {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging: isSortableDragging,
  } = useSortable({ id: activity.id });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
  };

  const Icon = activityIcons[activity.type];
  const colorClass = activityColors[activity.type];
  const time = formatTime(activity.start_time || undefined);
  const duration = formatDuration(activity.start_time || undefined, activity.end_time || undefined);

  return (
    <div
      ref={setNodeRef}
      style={{
        ...style,
        opacity: isSortableDragging ? 0.5 : 1,
      }}
      className={`relative ${isSortableDragging ? 'z-50' : ''}`}
      data-sortable-id={activity.id}
    >
      {/* Timeline dot */}
      <div className={`absolute -left-11 w-5 h-5 ${colorClass} rounded-full border-4 border-white shadow-sm`} aria-hidden="true" />
      
      {/* Activity card with Magic Card effect */}
      <MagicCard 
        glowColor={activityGlowColors[activity.type as keyof typeof activityGlowColors] || activityGlowColors.other}
        className="rounded-lg"
        disabled={isSortableDragging}
      >
        <Card 
          className={`p-4 transition-shadow ${isSortableDragging ? 'opacity-50' : ''}`} 
          role="article" 
          aria-label={`${activity.type} activity: ${activity.title}`}
        >
        <div className="flex items-start justify-between mb-2">
          <div className="flex items-start space-x-3 flex-1">
            {/* Drag handle */}
            <button
              {...attributes}
              {...listeners}
              className="mt-1 p-1 hover:bg-gray-100 rounded cursor-grab active:cursor-grabbing"
              data-testid="drag-handle"
              aria-label="Drag to reorder"
            >
              <GripVertical className="h-4 w-4 text-gray-400" />
            </button>
            
            <div className={`p-2 ${colorClass} rounded-lg`} aria-hidden="true">
              <Icon className="h-5 w-5 text-white" aria-hidden="true" />
            </div>
            <div className="flex-1">
              <h4 className="font-semibold text-gray-900">{activity.title}</h4>
              {activity.description && (
                <p className="text-sm text-gray-600 mt-1">{activity.description}</p>
              )}
            </div>
          </div>
          <Badge variant="outline" className="ml-2">
            {activity.type}
          </Badge>
        </div>

        {/* Activity details */}
        <div className="ml-20 space-y-1">
          {time && (
            <div className="flex items-center text-sm text-gray-600">
              <Clock className="h-3 w-3 mr-1" aria-hidden="true" />
              <span aria-label={`Scheduled time: ${time}${duration ? `, duration: ${duration}` : ''}`}>
                {time}
                {duration && <span className="text-gray-400 ml-1">({duration})</span>}
              </span>
            </div>
          )}

          {activity.location && (
            <div className="flex items-center text-sm text-gray-600">
              <MapPin className="h-3 w-3 mr-1" aria-hidden="true" />
              <span aria-label={`Location: ${activity.location}`}>{activity.location}</span>
            </div>
          )}

          {activity.price && (
            <div className="flex items-center text-sm text-gray-600">
              <DollarSign className="h-3 w-3 mr-1" aria-hidden="true" />
              <span aria-label={`Price: ${activity.price} ${activity.currency}`}>
                {activity.price} {activity.currency}
              </span>
            </div>
          )}

          {activity.booking_reference && (
            <div className="flex items-center text-sm text-gray-600">
              <FileText className="h-3 w-3 mr-1" aria-hidden="true" />
              <span aria-label={`Booking reference: ${activity.booking_reference}`}>
                Booking: {activity.booking_reference}
              </span>
            </div>
          )}

          {activity.booking_url && getSafeUrl(activity.booking_url) !== '#' && (
            <a
              href={getSafeUrl(activity.booking_url)}
              target="_blank"
              rel="noopener noreferrer"
              className="inline-flex items-center text-sm text-blue-600 hover:text-blue-800 mt-2"
              aria-label={`View booking for ${activity.title} (opens in new tab)`}
            >
              <Link className="h-3 w-3 mr-1" aria-hidden="true" />
              View Booking
            </a>
          )}
        </div>

        {/* Notes */}
        {activity.notes && (
          <div className="ml-20 mt-3 p-3 bg-gray-50 rounded-lg">
            <p className="text-sm text-gray-700">{activity.notes}</p>
          </div>
        )}
      </Card>
      </MagicCard>
    </div>
  );
}