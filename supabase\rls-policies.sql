-- Enable Row Level Security on all tables
ALTER TABLE trips ENABLE ROW LEVEL SECURITY;
ALTER TABLE activities ENABLE ROW LEVEL SECURITY;
ALTER TABLE ai_import_logs ENABLE ROW LEVEL SECURITY;

-- Drop existing policies if they exist (to allow re-running this script)
DROP POLICY IF EXISTS "Users can view own trips" ON trips;
DROP POLICY IF EXISTS "Users can insert own trips" ON trips;
DROP POLICY IF EXISTS "Users can update own trips" ON trips;
DROP POLICY IF EXISTS "Users can delete own trips" ON trips;

DROP POLICY IF EXISTS "Users can view activities for their trips" ON activities;
DROP POLICY IF EXISTS "Users can insert activities for their trips" ON activities;
DROP POLICY IF EXISTS "Users can update activities for their trips" ON activities;
DROP POLICY IF EXISTS "Users can delete activities for their trips" ON activities;

DROP POLICY IF EXISTS "Users can view own import logs" ON ai_import_logs;
DROP POLICY IF EXISTS "Users can insert own import logs" ON ai_import_logs;
DROP POLICY IF EXISTS "Users can update own import logs" ON ai_import_logs;
DROP POLICY IF EXISTS "Users can delete own import logs" ON ai_import_logs;

-- Trips table policies
-- Users can only view their own trips
CREATE POLICY "Users can view own trips" ON trips
    FOR SELECT USING (user_id = auth.uid());

-- Users can only insert trips for themselves
CREATE POLICY "Users can insert own trips" ON trips
    FOR INSERT WITH CHECK (user_id = auth.uid());

-- Users can only update their own trips
CREATE POLICY "Users can update own trips" ON trips
    FOR UPDATE USING (user_id = auth.uid());

-- Users can only delete their own trips
CREATE POLICY "Users can delete own trips" ON trips
    FOR DELETE USING (user_id = auth.uid());

-- Activities table policies
-- Users can view activities for trips they own
CREATE POLICY "Users can view activities for their trips" ON activities
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM trips 
            WHERE trips.id = activities.trip_id 
            AND trips.user_id = auth.uid()
        )
    );

-- Users can insert activities for trips they own
CREATE POLICY "Users can insert activities for their trips" ON activities
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM trips 
            WHERE trips.id = activities.trip_id 
            AND trips.user_id = auth.uid()
        )
    );

-- Users can update activities for trips they own
CREATE POLICY "Users can update activities for their trips" ON activities
    FOR UPDATE USING (
        EXISTS (
            SELECT 1 FROM trips 
            WHERE trips.id = activities.trip_id 
            AND trips.user_id = auth.uid()
        )
    );

-- Users can delete activities for trips they own
CREATE POLICY "Users can delete activities for their trips" ON activities
    FOR DELETE USING (
        EXISTS (
            SELECT 1 FROM trips 
            WHERE trips.id = activities.trip_id 
            AND trips.user_id = auth.uid()
        )
    );

-- AI Import Logs table policies
-- Users can only view their own import logs
CREATE POLICY "Users can view own import logs" ON ai_import_logs
    FOR SELECT USING (user_id = auth.uid());

-- Users can only insert import logs for themselves
CREATE POLICY "Users can insert own import logs" ON ai_import_logs
    FOR INSERT WITH CHECK (user_id = auth.uid());

-- Users can only update their own import logs
CREATE POLICY "Users can update own import logs" ON ai_import_logs
    FOR UPDATE USING (user_id = auth.uid());

-- Users can only delete their own import logs
CREATE POLICY "Users can delete own import logs" ON ai_import_logs
    FOR DELETE USING (user_id = auth.uid());

-- Add indexes to improve RLS performance
CREATE INDEX IF NOT EXISTS idx_trips_user_id ON trips(user_id);
CREATE INDEX IF NOT EXISTS idx_activities_trip_id ON activities(trip_id);
CREATE INDEX IF NOT EXISTS idx_ai_import_logs_user_id ON ai_import_logs(user_id);

-- Grant necessary permissions to authenticated users
GRANT SELECT, INSERT, UPDATE, DELETE ON trips TO authenticated;
GRANT SELECT, INSERT, UPDATE, DELETE ON activities TO authenticated;
GRANT SELECT, INSERT, UPDATE, DELETE ON ai_import_logs TO authenticated;