# Test Foundation Patterns

## API Test Pattern

### Authentication Pattern
```javascript
const ApiTestClient = require('./utils/api-client');
const client = new ApiTestClient();

// Login and save tokens
const loginResponse = await client.login(email, password);
// Tokens are automatically saved and reused

// Make authenticated requests
const response = await client.authenticatedRequest('GET', '/api/v1/trips');
// Token refresh is automatic if expired
```

### Test Structure Pattern
```javascript
async function runTests() {
  const testResults = { passed: 0, failed: 0, tests: [] };
  
  function logTest(name, result, details = {}) {
    const passed = result.success || result === true;
    testResults.tests.push({ name, passed, details });
    
    if (passed) {
      testResults.passed++;
      console.log(`✅ ${name}`);
    } else {
      testResults.failed++;
      console.log(`❌ ${name}`, details.error || '');
    }
  }
  
  // Run tests...
  
  return testResults;
}
```

## E2E Test Pattern

### Page Object Pattern
```javascript
const { HomePage, LoginPage } = require('./utils/page-objects');

test('user flow', async ({ page }) => {
  const homePage = new HomePage(page);
  await homePage.navigate();
  await homePage.clickLogin();
  
  const loginPage = new LoginPage(page);
  await loginPage.login(email, password);
  await loginPage.expectLoginSuccess();
});
```

### Screenshot Pattern
```javascript
test.afterEach(async ({ page }, testInfo) => {
  if (testInfo.status !== testInfo.expectedStatus) {
    const screenshotPath = `test-results/failure-${testInfo.title.replace(/\s+/g, '-')}-${Date.now()}.png`;
    await page.screenshot({ path: screenshotPath, fullPage: true });
  }
});
```

## Configuration Pattern

### Environment Loading
```javascript
// Load from .env.local files
const hubEnv = loadEnvFile('../packages/hub/.env.local');
const webEnv = loadEnvFile('../packages/web/.env.local');
const testEnv = { ...hubEnv, ...webEnv };
```

### Test Config Structure
```javascript
const testConfig = {
  api: { baseUrl: 'http://localhost:3001', timeout: 30000 },
  e2e: { baseUrl: 'http://localhost:3000', headless: false },
  auth: { testUserEmail: '...', testUserPassword: '...' }
};
```

## Usage Instructions

### Running API Tests
1. Start development server: `pnpm dev`
2. Run API tests: `pnpm test:api`

### Running E2E Tests
1. Start development server: `pnpm dev`
2. Run E2E tests: `pnpm test:e2e:headed`

### Creating New Tests
1. Copy the pattern from foundation tests
2. Follow the same structure and error handling
3. Use the established page objects and API client
4. Add proper cleanup and isolation
