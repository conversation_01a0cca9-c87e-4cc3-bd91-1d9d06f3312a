import { useMemo } from 'react';
import { GlobeDynamic } from '@/components/magic-ui/globe-dynamic';
import type { ParsedActivity } from '@travelviz/shared';
import type { GlobeCity, GlobeArc } from '@/components/magic-ui/globe';

interface TripGlobeProps {
  activities: ParsedActivity[];
  className?: string;
  onCityClick?: (cityName: string) => void;
}

// Sample city coordinates (in production, these would come from a geocoding service)
const CITY_COORDINATES: Record<string, { lat: number; lng: number }> = {
  'Paris': { lat: 48.8566, lng: 2.3522 },
  'Rome': { lat: 41.9028, lng: 12.4964 },
  'London': { lat: 51.5074, lng: -0.1278 },
  'Madrid': { lat: 40.4168, lng: -3.7038 },
  'Lisbon': { lat: 38.7223, lng: -9.1393 },
  'Berlin': { lat: 52.5200, lng: 13.4050 },
  'Amsterdam': { lat: 52.3676, lng: 4.9041 },
  'Barcelona': { lat: 41.3851, lng: 2.1734 },
  'Vienna': { lat: 48.2082, lng: 16.3738 },
  'Prague': { lat: 50.0755, lng: 14.4378 },
  'Tokyo': { lat: 35.6762, lng: 139.6503 },
  'Kyoto': { lat: 35.0116, lng: 135.7681 },
  'New York': { lat: 40.7128, lng: -74.0060 },
  'San Francisco': { lat: 37.7749, lng: -122.4194 },
  'Los Angeles': { lat: 34.0522, lng: -118.2437 },
};

export function TripGlobe({ activities, className, onCityClick }: TripGlobeProps) {
  // Extract unique cities from activities
  const { cities, arcs } = useMemo(() => {
    const cityMap = new Map<string, GlobeCity>();
    const cityOrder: string[] = [];
    
    // Process activities to find unique cities
    activities.forEach((activity, index) => {
      if (activity.location?.address) {
        // Extract city name from address (simplified - in production use proper parsing)
        const cityName = activity.location.address.split(',')[0].trim();
        
        if (!cityMap.has(cityName) && CITY_COORDINATES[cityName]) {
          const coords = CITY_COORDINATES[cityName];
          cityMap.set(cityName, {
            name: cityName,
            lat: coords.lat,
            lng: coords.lng,
            isOrigin: index === 0
          });
          cityOrder.push(cityName);
        }
      }
    });
    
    // Create arcs between consecutive cities
    const arcs: GlobeArc[] = [];
    for (let i = 0; i < cityOrder.length - 1; i++) {
      const fromCity = cityMap.get(cityOrder[i])!;
      const toCity = cityMap.get(cityOrder[i + 1])!;
      
      arcs.push({
        from: fromCity,
        to: toCity,
        order: i
      });
    }
    
    return {
      cities: Array.from(cityMap.values()),
      arcs
    };
  }, [activities]);
  
  // Handle city click
  const handleCityClick = (city: GlobeCity) => {
    onCityClick?.(city.name);
  };
  
  // Don't render if no cities found
  if (cities.length === 0) {
    return (
      <div className={className}>
        <div className="text-center text-gray-500 py-8">
          No location data available for globe visualization
        </div>
      </div>
    );
  }
  
  return (
    <GlobeDynamic
      cities={cities}
      arcs={arcs}
      className={className}
      autoRotate={true}
      autoRotateSpeed={0.5}
      showAtmosphere={true}
      quality="medium"
      onCityClick={handleCityClick}
    />
  );
}