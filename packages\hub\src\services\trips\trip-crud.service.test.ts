import { describe, it, expect, vi, beforeEach } from 'vitest';
import { TripCrudService } from './trip-crud.service';
import { 
  getSupabaseClient, 
  handleSupabaseError, 
  TABLES, 
  TripSchema, 
  ActivitySchema,
  validateDatabaseResponse 
} from '../../lib/supabase';
import { getErrorMessage } from '../../utils/error-handler';
import { sanitizeTripData } from '../../utils/sanitizer';
import { logger } from '../../utils/logger';
import type { TripData, TripWithActivities } from '../types';

// Mock dependencies
vi.mock('../../../lib/supabase', () => ({
  getSupabaseClient: vi.fn(),
  handleSupabaseError: vi.fn(),
  validateDatabaseResponse: vi.fn(),
  TripSchema: {},
  ActivitySchema: {},
  TABLES: {
    TRIPS: 'trips',
    ACTIVITIES: 'activities',
  },
}));

vi.mock('../../../utils/error-handler', () => ({
  getErrorMessage: vi.fn(),
}));

vi.mock('../../../utils/sanitizer', () => ({
  sanitizeTripData: vi.fn(),
}));

vi.mock('../../../utils/logger', () => ({
  logger: {
    info: vi.fn(),
    error: vi.fn(),
    debug: vi.fn(),
  },
}));

describe('TripCrudService', () => {
  let service: TripCrudService;
  let mockSupabase: any;

  const mockTripData: TripData = {
    userId: 'user123',
    title: 'Test Trip',
    description: 'Test description',
    startDate: '2024-01-01',
    endDate: '2024-01-07',
    destination: 'Paris',
    status: 'draft',
    visibility: 'private',
    coverImage: 'cover.jpg',
    tags: ['travel', 'europe'],
    budgetAmount: 1000,
    budgetCurrency: 'EUR',
  };

  const mockTrip = {
    id: 'trip123',
    user_id: 'user123',
    title: 'Test Trip',
    description: 'Test description',
    start_date: '2024-01-01',
    end_date: '2024-01-07',
    destination: 'Paris',
    status: 'draft',
    visibility: 'private',
    cover_image: 'cover.jpg',
    tags: ['travel', 'europe'],
    budget_amount: 1000,
    budget_currency: 'EUR',
    metadata: {},
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z',
  };

  const mockActivity = {
    id: 'activity123',
    trip_id: 'trip123',
    title: 'Test Activity',
    description: 'Activity description',
    type: 'activity',
    currency: 'EUR',
    metadata: {},
    attachments: [],
  };

  beforeEach(() => {
    vi.clearAllMocks();
    service = new TripCrudService();
    
    // Create a proper mock chain for Supabase operations
    mockSupabase = {
      from: vi.fn(() => mockSupabase),
      select: vi.fn(() => mockSupabase),
      insert: vi.fn(() => mockSupabase),
      update: vi.fn(() => mockSupabase),
      delete: vi.fn(() => mockSupabase),
      eq: vi.fn(() => mockSupabase),
      order: vi.fn(() => mockSupabase),
      single: vi.fn(() => Promise.resolve({ data: null, error: null })),
      rpc: vi.fn(() => Promise.resolve({ error: null })),
    };

    vi.mocked(getSupabaseClient).mockReturnValue(mockSupabase);
    vi.mocked(handleSupabaseError).mockImplementation((error) => ({
      message: error?.message || 'Unknown error',
      code: error?.code,
    }));
    vi.mocked(sanitizeTripData).mockImplementation((data) => data);
    vi.mocked(validateDatabaseResponse).mockImplementation((schema, data) => data);
    vi.mocked(getErrorMessage).mockImplementation((error) => error.message);
  });

  describe('createTrip', () => {
    it('should successfully create a trip', async () => {
      // Mock trip creation
      mockSupabase.single.mockResolvedValueOnce({
        data: mockTrip,
        error: null,
      });

      const result = await service.createTrip(mockTripData);

      expect(result).toEqual({
        ...mockTrip,
        activities: [],
      });
      expect(mockSupabase.insert).toHaveBeenCalledWith(
        expect.objectContaining({
          user_id: mockTripData.userId,
          title: mockTripData.title,
          description: mockTripData.description,
          start_date: mockTripData.startDate,
          end_date: mockTripData.endDate,
          destination: mockTripData.destination,
          status: 'draft',
          visibility: 'private',
          budget_currency: 'EUR',
        })
      );
      expect(sanitizeTripData).toHaveBeenCalledWith(mockTripData);
    });

    it('should apply default values for optional fields', async () => {
      const minimalTripData: TripData = {
        userId: 'user123',
        title: 'Minimal Trip',
      };

      mockSupabase.single.mockResolvedValueOnce({
        data: { ...mockTrip, title: 'Minimal Trip' },
        error: null,
      });

      await service.createTrip(minimalTripData);

      expect(mockSupabase.insert).toHaveBeenCalledWith(
        expect.objectContaining({
          user_id: 'user123',
          title: 'Minimal Trip',
          description: null,
          status: 'draft',
          visibility: 'private',
          budget_currency: 'USD',
        })
      );
    });

    it('should validate end date is after start date', async () => {
      const invalidTripData = {
        ...mockTripData,
        startDate: '2024-01-07',
        endDate: '2024-01-01', // before start date
      };

      await expect(
        service.createTrip(invalidTripData)
      ).rejects.toThrow('End date must be after or equal to start date');
    });

    it('should handle creation error', async () => {
      mockSupabase.single.mockResolvedValueOnce({
        data: null,
        error: { message: 'Creation failed' },
      });

      await expect(
        service.createTrip(mockTripData)
      ).rejects.toThrow('Failed to create trip: Creation failed');
    });

    it('should allow same start and end dates', async () => {
      const sameDayTrip = {
        ...mockTripData,
        startDate: '2024-01-01',
        endDate: '2024-01-01',
      };

      mockSupabase.single.mockResolvedValueOnce({
        data: mockTrip,
        error: null,
      });

      await expect(
        service.createTrip(sameDayTrip)
      ).resolves.toBeTruthy();
    });
  });

  describe('getUserTrips', () => {
    it('should successfully get user trips with activities', async () => {
      const tripsWithActivities = [
        {
          ...mockTrip,
          activities: [mockActivity],
        },
      ];

      mockSupabase.order.mockResolvedValueOnce({
        data: tripsWithActivities,
        error: null,
      });

      const result = await service.getUserTrips('user123');

      expect(result).toHaveLength(1);
      expect(result[0]).toEqual({
        ...mockTrip,
        activities: [mockActivity],
      });
      expect(mockSupabase.select).toHaveBeenCalledWith(
        expect.stringContaining('activities')
      );
      expect(mockSupabase.eq).toHaveBeenCalledWith('user_id', 'user123');
      expect(mockSupabase.order).toHaveBeenCalledWith('created_at', { ascending: false });
    });

    it('should handle empty trips list', async () => {
      mockSupabase.order.mockResolvedValueOnce({
        data: [],
        error: null,
      });

      const result = await service.getUserTrips('user123');

      expect(result).toEqual([]);
    });

    it('should handle trips without activities', async () => {
      const tripWithoutActivities = {
        ...mockTrip,
        activities: null,
      };

      mockSupabase.order.mockResolvedValueOnce({
        data: [tripWithoutActivities],
        error: null,
      });

      const result = await service.getUserTrips('user123');

      expect(result[0].activities).toEqual([]);
    });

    it('should handle query error', async () => {
      mockSupabase.order.mockResolvedValueOnce({
        data: null,
        error: { message: 'Query failed' },
      });

      await expect(
        service.getUserTrips('user123')
      ).rejects.toThrow('Failed to get trips: Query failed');
    });
  });

  describe('getTripById', () => {
    it('should successfully get trip by ID with activities', async () => {
      const tripWithActivities = {
        ...mockTrip,
        activities: [mockActivity],
      };

      mockSupabase.single.mockResolvedValueOnce({
        data: tripWithActivities,
        error: null,
      });

      const result = await service.getTripById('trip123', 'user123');

      expect(result).toEqual({
        ...mockTrip,
        activities: [mockActivity],
      });
      expect(mockSupabase.eq).toHaveBeenCalledWith('id', 'trip123');
      expect(mockSupabase.eq).toHaveBeenCalledWith('user_id', 'user123');
    });

    it('should return null if trip not found', async () => {
      mockSupabase.single.mockResolvedValueOnce({
        data: null,
        error: { code: 'PGRST116' }, // Not found error code
      });

      const result = await service.getTripById('nonexistent', 'user123');

      expect(result).toBeNull();
    });

    it('should handle authorization error', async () => {
      mockSupabase.single.mockResolvedValueOnce({
        data: null,
        error: { message: 'Unauthorized' },
      });

      await expect(
        service.getTripById('trip123', 'user123')
      ).rejects.toThrow('Failed to get trip: Unauthorized');
    });

    it('should increment view count asynchronously', async () => {
      const tripWithActivities = {
        ...mockTrip,
        activities: [],
      };

      mockSupabase.single.mockResolvedValueOnce({
        data: tripWithActivities,
        error: null,
      });

      // Mock incrementTripViews RPC call
      mockSupabase.rpc.mockResolvedValueOnce({
        error: null,
      });

      const result = await service.getTripById('trip123', 'user123');

      expect(result).toBeTruthy();
      
      // Wait a bit for async call
      await new Promise(resolve => setTimeout(resolve, 0));
      
      expect(mockSupabase.rpc).toHaveBeenCalledWith('increment_trip_views', {
        trip_id: 'trip123',
      });
    });
  });

  describe('updateTrip', () => {
    const updateData: Partial<TripData> = {
      title: 'Updated Trip',
      description: 'Updated description',
      status: 'confirmed',
    };

    it('should successfully update trip', async () => {
      // Mock the complex update chain: update().eq().eq().select().single()
      const updatedTripWithActivities = {
        ...mockTrip,
        ...updateData,
        [TABLES.ACTIVITIES]: [mockActivity], // Activities come nested in the response
      };
      
      mockSupabase.update.mockReturnValueOnce({
        eq: vi.fn(() => ({
          eq: vi.fn(() => ({
            select: vi.fn(() => ({
              single: vi.fn(() => Promise.resolve({
                data: updatedTripWithActivities,
                error: null,
              })),
            })),
          })),
        })),
      });

      const result = await service.updateTrip('trip123', 'user123', updateData);

      expect(result).toEqual({
        ...mockTrip,
        ...updateData,
        activities: [mockActivity],
      });
      expect(mockSupabase.update).toHaveBeenCalledWith(
        expect.objectContaining({
          title: 'Updated Trip',
          description: 'Updated description',
          status: 'confirmed',
        })
      );
    });

    it('should return null if trip not found', async () => {
      mockSupabase.single.mockResolvedValueOnce({
        data: null,
        error: { code: 'PGRST116' },
      });

      const result = await service.updateTrip('nonexistent', 'user123', updateData);

      expect(result).toBeNull();
    });

    it('should validate date updates', async () => {
      const invalidDateUpdate = {
        startDate: '2024-01-07',
        endDate: '2024-01-01', // before start date
      };

      await expect(
        service.updateTrip('trip123', 'user123', invalidDateUpdate)
      ).rejects.toThrow('End date must be after start date');
    });

    it('should handle partial updates', async () => {
      const partialUpdate = { title: 'New Title Only' };

      mockSupabase.update.mockReturnValueOnce({
        eq: vi.fn(() => ({
          eq: vi.fn(() => ({
            select: vi.fn(() => ({
              single: vi.fn(() => Promise.resolve({
                data: { ...mockTrip, title: 'New Title Only' },
                error: null,
              })),
            })),
          })),
        })),
      });

      mockSupabase.select.mockResolvedValueOnce({
        data: [],
        error: null,
      });

      await service.updateTrip('trip123', 'user123', partialUpdate);

      expect(mockSupabase.update).toHaveBeenCalledWith({
        title: 'New Title Only',
      });
    });

    it('should handle update error', async () => {
      mockSupabase.single.mockResolvedValueOnce({
        data: null,
        error: { message: 'Update failed' },
      });

      await expect(
        service.updateTrip('trip123', 'user123', updateData)
      ).rejects.toThrow('Failed to update trip: Update failed');
    });
  });

  describe('deleteTrip', () => {
    it('should successfully delete trip', async () => {
      mockSupabase.delete.mockReturnValueOnce({
        eq: vi.fn(() => ({
          eq: vi.fn(() => Promise.resolve({ error: null })),
        })),
      });

      const result = await service.deleteTrip('trip123', 'user123');

      expect(result).toBe(true);
      expect(mockSupabase.delete).toHaveBeenCalled();
    });

    it('should return false if trip not found', async () => {
      mockSupabase.delete.mockReturnValueOnce({
        eq: vi.fn(() => ({
          eq: vi.fn(() => Promise.resolve({ error: { code: 'PGRST116' } })),
        })),
      });

      const result = await service.deleteTrip('nonexistent', 'user123');

      expect(result).toBe(false);
    });

    it('should handle delete error', async () => {
      mockSupabase.delete.mockReturnValueOnce({
        eq: vi.fn(() => ({
          eq: vi.fn(() => Promise.resolve({ error: { message: 'Delete failed' } })),
        })),
      });

      await expect(
        service.deleteTrip('trip123', 'user123')
      ).rejects.toThrow('Failed to delete trip: Delete failed');
    });
  });

  describe('incrementTripViews', () => {
    it('should successfully increment trip views', async () => {
      mockSupabase.rpc.mockResolvedValueOnce({
        error: null,
      });

      await service.incrementTripViews('trip123');

      expect(mockSupabase.rpc).toHaveBeenCalledWith('increment_trip_views', {
        trip_id: 'trip123',
      });
    });

    it('should handle RPC error gracefully', async () => {
      mockSupabase.rpc.mockResolvedValueOnce({
        error: { message: 'RPC failed' },
      });

      // Should not throw
      await expect(
        service.incrementTripViews('trip123')
      ).resolves.not.toThrow();

      expect(logger.error).toHaveBeenCalledWith(
        'Failed to increment trip views:',
        expect.objectContaining({ error: expect.any(Object) })
      );
    });

    it('should handle RPC exception gracefully', async () => {
      mockSupabase.rpc.mockRejectedValueOnce(new Error('Network error'));

      // Should not throw
      await expect(
        service.incrementTripViews('trip123')
      ).resolves.not.toThrow();

      expect(logger.error).toHaveBeenCalledWith(
        'Failed to increment trip views:',
        expect.objectContaining({ error: expect.any(Error) })
      );
    });
  });

  describe('data validation and sanitization', () => {
    it('should sanitize input data for creation', async () => {
      const unsanitizedData = {
        ...mockTripData,
        title: '<script>alert("xss")</script>',
        description: '<img src=x onerror=alert(1)>',
      };

      const sanitizedData = {
        ...unsanitizedData,
        title: 'Clean Title',
        description: 'Clean Description',
      };

      vi.mocked(sanitizeTripData).mockReturnValueOnce(sanitizedData);

      mockSupabase.single.mockResolvedValueOnce({
        data: mockTrip,
        error: null,
      });

      await service.createTrip(unsanitizedData);

      expect(sanitizeTripData).toHaveBeenCalledWith(unsanitizedData);
      expect(mockSupabase.insert).toHaveBeenCalledWith(
        expect.objectContaining({
          title: 'Clean Title',
          description: 'Clean Description',
        })
      );
    });

    it('should sanitize input data for updates', async () => {
      const unsanitizedUpdate = {
        title: '<script>evil()</script>',
      };

      const sanitizedUpdate = {
        title: 'Clean Update',
      };

      vi.mocked(sanitizeTripData).mockReturnValueOnce(sanitizedUpdate);

      mockSupabase.update.mockReturnValueOnce({
        eq: vi.fn(() => ({
          eq: vi.fn(() => ({
            select: vi.fn(() => ({
              single: vi.fn(() => Promise.resolve({
                data: { ...mockTrip, title: 'Clean Update' },
                error: null,
              })),
            })),
          })),
        })),
      });

      mockSupabase.select.mockResolvedValueOnce({
        data: [],
        error: null,
      });

      await service.updateTrip('trip123', 'user123', unsanitizedUpdate);

      expect(sanitizeTripData).toHaveBeenCalledWith(unsanitizedUpdate);
      expect(mockSupabase.update).toHaveBeenCalledWith({
        title: 'Clean Update',
      });
    });

    it('should validate database responses', async () => {
      mockSupabase.single.mockResolvedValueOnce({
        data: mockTrip,
        error: null,
      });

      await service.createTrip(mockTripData);

      expect(validateDatabaseResponse).toHaveBeenCalledWith(
        TripSchema,
        mockTrip,
        'trip'
      );
    });

    it('should provide default values for required fields', async () => {
      const incompleteTrip = {
        id: 'trip123',
        user_id: 'user123',
        title: 'Test Trip',
        // Missing status, visibility, etc.
      };

      mockSupabase.single.mockResolvedValueOnce({
        data: incompleteTrip,
        error: null,
      });

      const result = await service.createTrip(mockTripData);

      expect(result).toEqual(
        expect.objectContaining({
          status: 'draft',
          visibility: 'private',
          metadata: {},
          tags: [],
          budget_currency: 'USD',
          activities: [],
        })
      );
    });
  });
});