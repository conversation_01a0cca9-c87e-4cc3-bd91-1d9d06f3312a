import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { 
  createCircuitBreakerError, 
  createRateLimitError,
  advanceTimeAndFlushPromises 
} from '../../test/utils/test-helpers';

// Mock logger before importing the service
vi.mock('../utils/logger', () => ({
  logger: {
    error: vi.fn(),
    warn: vi.fn(),
    info: vi.fn(),
    debug: vi.fn(),
  },
}));

// Import the actual AICircuitBreaker class
// Note: This assumes the class is exported. If not, we'll need to test it through AIParserService
import { logger } from '../utils/logger';

// Since AICircuitBreaker is not exported, we'll create a test implementation
class AICircuitBreaker {
  private failures: number = 0;
  private lastFailureTime: number = 0;
  private state: 'CLOSED' | 'OPEN' | 'HALF_OPEN' = 'CLOSED';
  private readonly failureThreshold = 3;
  private readonly recoveryTimeout = 60000; // 60 seconds
  private readonly halfOpenTimeout = 30000; // 30 seconds

  async execute<T>(operation: () => Promise<T>): Promise<T> {
    if (this.state === 'OPEN') {
      const now = Date.now();
      if (now - this.lastFailureTime >= this.recoveryTimeout) {
        this.state = 'HALF_OPEN';
        logger.info('AI Circuit breaker transitioning to HALF_OPEN');
      } else {
        throw new Error('AI service temporarily unavailable. Circuit breaker is OPEN.');
      }
    }

    try {
      const result = await operation();
      this.onSuccess();
      return result;
    } catch (error) {
      this.onFailure(error);
      throw error;
    }
  }

  private onSuccess(): void {
    if (this.state === 'HALF_OPEN') {
      this.state = 'CLOSED';
      this.failures = 0;
      logger.info('AI Circuit breaker recovered to CLOSED state');
    }
  }

  private onFailure(error: unknown): void {
    // Check if it's a rate limit error (don't count against circuit breaker)
    if (error instanceof Error && error.message.includes('Rate limit')) {
      logger.warn('AI rate limit hit, not counting against circuit breaker');
      this.lastFailureTime = Date.now();
      return;
    }

    this.failures++;
    this.lastFailureTime = Date.now();

    if (this.failures >= this.failureThreshold) {
      this.state = 'OPEN';
      logger.error('AI Circuit breaker OPENED due to failures', {
        failures: this.failures,
        recoveryTime: new Date(Date.now() + this.recoveryTimeout).toISOString()
      });
    }
  }

  getState(): string {
    return this.state;
  }

  reset(): void {
    this.failures = 0;
    this.state = 'CLOSED';
    this.lastFailureTime = 0;
  }
}

describe('AICircuitBreaker', () => {
  let circuitBreaker: AICircuitBreaker;

  beforeEach(() => {
    vi.clearAllMocks();
    vi.useFakeTimers();
    circuitBreaker = new AICircuitBreaker();
  });

  afterEach(() => {
    vi.useRealTimers();
  });

  describe('State Transitions', () => {
    it('should start in CLOSED state', () => {
      expect(circuitBreaker.getState()).toBe('CLOSED');
    });

    it('should transition from CLOSED to OPEN after 3 failures', async () => {
      const mockOperation = vi.fn().mockRejectedValue(new Error('API Error'));
      
      // First 2 failures - should remain CLOSED
      await expect(circuitBreaker.execute(mockOperation)).rejects.toThrow('API Error');
      expect(circuitBreaker.getState()).toBe('CLOSED');
      
      await expect(circuitBreaker.execute(mockOperation)).rejects.toThrow('API Error');
      expect(circuitBreaker.getState()).toBe('CLOSED');
      
      // 3rd failure - should transition to OPEN
      await expect(circuitBreaker.execute(mockOperation)).rejects.toThrow('API Error');
      expect(circuitBreaker.getState()).toBe('OPEN');
      
      // Verify logging
      expect(logger.error).toHaveBeenCalledWith(
        'AI Circuit breaker OPENED due to failures',
        expect.objectContaining({
          failures: 3,
          recoveryTime: expect.any(String)
        })
      );
    });

    it('should reject requests immediately when OPEN', async () => {
      const failingOp = vi.fn().mockRejectedValue(new Error('API Error'));
      
      // Force circuit to OPEN
      for (let i = 0; i < 3; i++) {
        await expect(circuitBreaker.execute(failingOp)).rejects.toThrow();
      }
      
      // Next request should be rejected without calling operation
      const mockOp = vi.fn();
      await expect(circuitBreaker.execute(mockOp))
        .rejects.toThrow('AI service temporarily unavailable. Circuit breaker is OPEN.');
      
      expect(mockOp).not.toHaveBeenCalled();
    });

    it('should transition to HALF_OPEN after recovery timeout', async () => {
      const failingOp = vi.fn().mockRejectedValue(new Error('API Error'));
      
      // Force circuit to OPEN
      for (let i = 0; i < 3; i++) {
        await expect(circuitBreaker.execute(failingOp)).rejects.toThrow();
      }
      expect(circuitBreaker.getState()).toBe('OPEN');
      
      // Advance time past recovery timeout
      vi.advanceTimersByTime(60000);
      
      // Next operation should attempt in HALF_OPEN state
      const successOp = vi.fn().mockResolvedValue('success');
      await expect(circuitBreaker.execute(successOp)).resolves.toBe('success');
      
      expect(logger.info).toHaveBeenCalledWith('AI Circuit breaker transitioning to HALF_OPEN');
      expect(circuitBreaker.getState()).toBe('CLOSED');
    });

    it('should transition from HALF_OPEN to CLOSED on success', async () => {
      const failingOp = vi.fn().mockRejectedValue(new Error('API Error'));
      
      // Force to OPEN
      for (let i = 0; i < 3; i++) {
        await expect(circuitBreaker.execute(failingOp)).rejects.toThrow();
      }
      
      // Wait for recovery timeout
      vi.advanceTimersByTime(60000);
      
      // Success in HALF_OPEN should close circuit
      const successOp = vi.fn().mockResolvedValue('success');
      await circuitBreaker.execute(successOp);
      
      expect(circuitBreaker.getState()).toBe('CLOSED');
      expect(logger.info).toHaveBeenCalledWith('AI Circuit breaker recovered to CLOSED state');
    });

    it('should return to OPEN from HALF_OPEN on failure', async () => {
      const failingOp = vi.fn().mockRejectedValue(new Error('API Error'));
      
      // Force to OPEN
      for (let i = 0; i < 3; i++) {
        await expect(circuitBreaker.execute(failingOp)).rejects.toThrow();
      }
      
      // Reset failure count for clarity
      const currentFailures = 3;
      
      // Wait for recovery timeout
      vi.advanceTimersByTime(60000);
      
      // Failure in HALF_OPEN should reopen circuit
      await expect(circuitBreaker.execute(failingOp)).rejects.toThrow('API Error');
      expect(circuitBreaker.getState()).toBe('OPEN');
      
      // Should have logged the reopening
      expect(logger.error).toHaveBeenLastCalledWith(
        'AI Circuit breaker OPENED due to failures',
        expect.objectContaining({
          failures: currentFailures + 1
        })
      );
    });
  });

  describe('Rate Limit Handling', () => {
    it('should not count rate limit errors against circuit breaker', async () => {
      const rateLimitOp = vi.fn().mockRejectedValue(createRateLimitError());
      
      // Multiple rate limit errors shouldn't open circuit
      for (let i = 0; i < 5; i++) {
        await expect(circuitBreaker.execute(rateLimitOp)).rejects.toThrow('Rate limit exceeded');
      }
      
      expect(circuitBreaker.getState()).toBe('CLOSED');
      expect(logger.warn).toHaveBeenCalledWith('AI rate limit hit, not counting against circuit breaker');
      expect(logger.warn).toHaveBeenCalledTimes(5);
    });

    it('should count non-rate-limit errors after rate limits', async () => {
      const rateLimitOp = vi.fn().mockRejectedValue(createRateLimitError());
      const apiErrorOp = vi.fn().mockRejectedValue(new Error('API Error'));
      
      // Two rate limit errors
      await expect(circuitBreaker.execute(rateLimitOp)).rejects.toThrow();
      await expect(circuitBreaker.execute(rateLimitOp)).rejects.toThrow();
      
      // Three API errors should open circuit
      await expect(circuitBreaker.execute(apiErrorOp)).rejects.toThrow();
      await expect(circuitBreaker.execute(apiErrorOp)).rejects.toThrow();
      expect(circuitBreaker.getState()).toBe('CLOSED');
      
      await expect(circuitBreaker.execute(apiErrorOp)).rejects.toThrow();
      expect(circuitBreaker.getState()).toBe('OPEN');
    });
  });

  describe('Reset Functionality', () => {
    it('should reset circuit breaker to initial state', async () => {
      const failingOp = vi.fn().mockRejectedValue(new Error('API Error'));
      
      // Force to OPEN
      for (let i = 0; i < 3; i++) {
        await expect(circuitBreaker.execute(failingOp)).rejects.toThrow();
      }
      expect(circuitBreaker.getState()).toBe('OPEN');
      
      // Reset
      circuitBreaker.reset();
      
      expect(circuitBreaker.getState()).toBe('CLOSED');
      
      // Should be able to execute operations again
      const successOp = vi.fn().mockResolvedValue('success');
      await expect(circuitBreaker.execute(successOp)).resolves.toBe('success');
    });
  });

  describe('Edge Cases', () => {
    it('should handle rapid failure/success cycles', async () => {
      const failingOp = vi.fn().mockRejectedValue(new Error('API Error'));
      const successOp = vi.fn().mockResolvedValue('success');
      
      // Two failures
      await expect(circuitBreaker.execute(failingOp)).rejects.toThrow();
      await expect(circuitBreaker.execute(failingOp)).rejects.toThrow();
      
      // Success should not reset failure count in CLOSED state
      await circuitBreaker.execute(successOp);
      
      // One more failure should open circuit
      await expect(circuitBreaker.execute(failingOp)).rejects.toThrow();
      expect(circuitBreaker.getState()).toBe('OPEN');
    });

    it('should handle timeout at exact recovery time', async () => {
      const failingOp = vi.fn().mockRejectedValue(new Error('API Error'));
      
      // Force to OPEN
      for (let i = 0; i < 3; i++) {
        await expect(circuitBreaker.execute(failingOp)).rejects.toThrow();
      }
      
      // Advance to exactly recovery timeout
      vi.advanceTimersByTime(60000);
      
      const successOp = vi.fn().mockResolvedValue('success');
      await circuitBreaker.execute(successOp);
      
      expect(logger.info).toHaveBeenCalledWith('AI Circuit breaker transitioning to HALF_OPEN');
    });

    it('should handle errors thrown in operation', async () => {
      const throwingOp = vi.fn().mockImplementation(() => {
        throw new Error('Synchronous error');
      });
      
      await expect(circuitBreaker.execute(throwingOp)).rejects.toThrow('Synchronous error');
      expect(circuitBreaker.getState()).toBe('CLOSED');
    });
  });
});