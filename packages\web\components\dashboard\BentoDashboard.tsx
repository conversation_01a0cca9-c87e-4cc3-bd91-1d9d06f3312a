'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { 
  Plus, 
  MapPin, 
  Calendar, 
  DollarSign, 
  TrendingUp,
  Plane,
  Upload,
  Activity,
  Globe2,
  Sparkles
} from 'lucide-react';
import { motion } from 'framer-motion';
import { useAuthStore } from '@/stores/auth.store';
import { useTripStore } from '@/stores/trip.store';
import { BentoGrid, BentoCard, BentoCardSizes } from '@/components/magic-ui/bento-grid';
import { DynamicNumberTicker } from '@/components/magic-ui/number-ticker-dynamic';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { format } from 'date-fns';
import { cn } from '@/lib/utils';

interface DashboardStats {
  totalTrips: number;
  countriesVisited: number;
  totalActivities: number;
  totalSpent: number;
  upcomingTrips: number;
  completedTrips: number;
}

export function BentoDashboard() {
  const router = useRouter();
  const { user } = useAuthStore();
  const { trips, fetchTripsPaginated, isLoading } = useTripStore();
  const [stats, setStats] = useState<DashboardStats>({
    totalTrips: 0,
    countriesVisited: 0,
    totalActivities: 0,
    totalSpent: 0,
    upcomingTrips: 0,
    completedTrips: 0,
  });

  // Remove the duplicate fetchTripsPaginated call since the parent dashboard page already fetches trips
  // useEffect(() => {
  //   fetchTripsPaginated(1, 20);
  // }, []);

  useEffect(() => {
    // Calculate stats from trips
    if (trips.length > 0) {
      const countries = new Set(trips.map(t => t.destination).filter(Boolean));
      const upcoming = trips.filter(t => ['draft', 'planning', 'confirmed'].includes(t.status));
      const completed = trips.filter(t => t.status === 'completed');
      
      setStats({
        totalTrips: trips.length,
        countriesVisited: countries.size,
        totalActivities: trips.reduce((sum, t) => sum + (t.activities?.length || 0), 0),
        totalSpent: trips.reduce((sum, t) => sum + (t.budget_amount || 0), 0),
        upcomingTrips: upcoming.length,
        completedTrips: completed.length,
      });
    }
  }, [trips]);

  const recentTrips = trips.slice(0, 3);
  const upcomingTrips = trips
    .filter(t => ['draft', 'planning', 'confirmed'].includes(t.status))
    .slice(0, 2);

  return (
    <BentoGrid className="max-w-7xl mx-auto">
      {/* Welcome Card - Wide */}
      <BentoCard 
        {...BentoCardSizes.wide} 
        index={0}
        className="bg-gradient-to-br from-orange-500 to-pink-500 text-white"
      >
        <CardContent className="p-6 h-full flex flex-col justify-between">
          <div>
            <h2 className="text-2xl font-bold mb-2">
              Welcome back, {user?.name?.split(' ')[0] || 'Traveler'}!
            </h2>
            <p className="text-white/90">
              Ready to plan your next adventure? Your travel journey awaits.
            </p>
          </div>
          <div className="flex gap-3 mt-4">
            <Button 
              variant="secondary" 
              size="sm"
              onClick={() => router.push('/import')}
              className="bg-white/20 hover:bg-white/30 text-white border-0"
            >
              <Upload className="w-4 h-4 mr-2" />
              Import Trip
            </Button>
            <Button 
              variant="secondary" 
              size="sm"
              onClick={() => router.push('/plan/new')}
              className="bg-white/20 hover:bg-white/30 text-white border-0"
            >
              <Plus className="w-4 h-4 mr-2" />
              Create Trip
            </Button>
          </div>
        </CardContent>
      </BentoCard>

      {/* Trip Stats - Small */}
      <BentoCard {...BentoCardSizes.small} index={1}>
        <CardContent className="p-6 text-center">
          <Globe2 className="w-8 h-8 text-orange-500 mx-auto mb-3" />
          <div className="text-3xl font-bold">
            <DynamicNumberTicker 
              value={stats.totalTrips} 
              springConfig="bounce"
            />
          </div>
          <p className="text-sm text-muted-foreground mt-1">Total Trips</p>
        </CardContent>
      </BentoCard>

      {/* Countries Visited - Small */}
      <BentoCard {...BentoCardSizes.small} index={2}>
        <CardContent className="p-6 text-center">
          <MapPin className="w-8 h-8 text-blue-500 mx-auto mb-3" />
          <div className="text-3xl font-bold">
            <DynamicNumberTicker 
              value={stats.countriesVisited} 
              springConfig="bounce"
            />
          </div>
          <p className="text-sm text-muted-foreground mt-1">Countries</p>
        </CardContent>
      </BentoCard>

      {/* Recent Trips - Large */}
      <BentoCard 
        {...BentoCardSizes.large} 
        index={3}
        onClick={() => router.push('/dashboard')}
      >
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center justify-between">
            <span className="flex items-center gap-2">
              <Calendar className="w-5 h-5" />
              Recent Trips
            </span>
            <Badge variant="secondary" className="text-xs">
              View All
            </Badge>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-3">
          {recentTrips.length > 0 ? (
            recentTrips.map((trip) => (
              <motion.div
                key={trip.id}
                className="flex items-center justify-between p-3 rounded-lg bg-muted/50 hover:bg-muted transition-colors cursor-pointer"
                onClick={(e) => {
                  e.stopPropagation();
                  router.push(`/plan/${trip.id}`);
                }}
                whileHover={{ x: 4 }}
              >
                <div className="flex items-center gap-3">
                  <div className={cn(
                    "w-10 h-10 rounded-full flex items-center justify-center",
                    trip.status === 'completed' ? 'bg-green-100' : 'bg-blue-100'
                  )}>
                    <Plane className={cn(
                      "w-5 h-5",
                      trip.status === 'completed' ? 'text-green-600' : 'text-blue-600'
                    )} />
                  </div>
                  <div>
                    <p className="font-medium">{trip.title}</p>
                    <p className="text-sm text-muted-foreground">
                      {trip.destination || 'No destination'}
                    </p>
                  </div>
                </div>
                <Badge variant="outline" className="text-xs">
                  {trip.status}
                </Badge>
              </motion.div>
            ))
          ) : (
            <div className="text-center py-8 text-muted-foreground">
              <p>No trips yet. Start planning your first adventure!</p>
            </div>
          )}
        </CardContent>
      </BentoCard>

      {/* Budget Overview - Wide */}
      <BentoCard {...BentoCardSizes.wide} index={4}>
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center gap-2">
            <DollarSign className="w-5 h-5" />
            Budget Overview
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div>
              <div className="flex justify-between text-sm mb-2">
                <span>Total Spent</span>
                <span className="font-semibold">
                  <DynamicNumberTicker 
                    value={stats.totalSpent} 
                    format="currency"
                    currency="USD"
                    springConfig="smooth"
                  />
                </span>
              </div>
              <Progress value={65} className="h-2" />
            </div>
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <p className="text-muted-foreground">Avg per trip</p>
                <p className="font-semibold">
                  <DynamicNumberTicker 
                    value={stats.totalTrips > 0 ? stats.totalSpent / stats.totalTrips : 0} 
                    format="currency"
                    currency="USD"
                    springConfig="smooth"
                  />
                </p>
              </div>
              <div>
                <p className="text-muted-foreground">This month</p>
                <p className="font-semibold">$1,250</p>
              </div>
            </div>
          </div>
        </CardContent>
      </BentoCard>

      {/* Upcoming Trips - Tall */}
      <BentoCard 
        {...BentoCardSizes.tall} 
        index={5}
        className="bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-blue-950 dark:to-indigo-950"
      >
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center gap-2">
            <TrendingUp className="w-5 h-5" />
            Upcoming Adventures
          </CardTitle>
        </CardHeader>
        <CardContent>
          {upcomingTrips.length > 0 ? (
            <div className="space-y-3">
              {upcomingTrips.map((trip) => (
                <motion.div
                  key={trip.id}
                  className="p-4 rounded-lg bg-white/50 dark:bg-gray-900/50 cursor-pointer"
                  onClick={() => router.push(`/plan/${trip.id}`)}
                  whileHover={{ scale: 1.02 }}
                >
                  <h4 className="font-semibold">{trip.title}</h4>
                  <p className="text-sm text-muted-foreground mt-1">
                    {trip.start_date ? format(new Date(trip.start_date), 'MMM d, yyyy') : 'Date TBD'}
                  </p>
                  <div className="flex items-center gap-2 mt-2">
                    <Badge variant="secondary" className="text-xs">
                      {trip.activities?.length || 0} activities
                    </Badge>
                  </div>
                </motion.div>
              ))}
            </div>
          ) : (
            <div className="flex flex-col items-center justify-center h-full text-center">
              <Sparkles className="w-12 h-12 text-muted-foreground/50 mb-3" />
              <p className="text-muted-foreground">
                No upcoming trips yet
              </p>
              <Button 
                variant="link" 
                size="sm" 
                className="mt-2"
                onClick={() => router.push('/plan/new')}
              >
                Plan your next trip
              </Button>
            </div>
          )}
        </CardContent>
      </BentoCard>

      {/* Activity Heatmap - Small */}
      <BentoCard {...BentoCardSizes.small} index={6}>
        <CardContent className="p-6 text-center">
          <Activity className="w-8 h-8 text-purple-500 mx-auto mb-3" />
          <div className="text-3xl font-bold">
            <DynamicNumberTicker 
              value={stats.totalActivities} 
              springConfig="bounce"
            />
          </div>
          <p className="text-sm text-muted-foreground mt-1">Activities</p>
        </CardContent>
      </BentoCard>

      {/* Quick Stats - Small */}
      <BentoCard {...BentoCardSizes.small} index={7}>
        <CardContent className="p-6">
          <div className="space-y-3">
            <div className="flex justify-between items-center">
              <span className="text-sm text-muted-foreground">Upcoming</span>
              <span className="font-semibold">
                <DynamicNumberTicker 
                  value={stats.upcomingTrips} 
                  springConfig="quick"
                />
              </span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm text-muted-foreground">Completed</span>
              <span className="font-semibold">
                <DynamicNumberTicker 
                  value={stats.completedTrips} 
                  springConfig="quick"
                />
              </span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm text-muted-foreground">In Progress</span>
              <span className="font-semibold">
                <DynamicNumberTicker 
                  value={trips.filter(t => t.status === 'in_progress').length} 
                  springConfig="quick"
                />
              </span>
            </div>
          </div>
        </CardContent>
      </BentoCard>
    </BentoGrid>
  );
}