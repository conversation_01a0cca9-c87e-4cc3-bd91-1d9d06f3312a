import { v4 as uuidv4 } from 'uuid';
import axios from 'axios';
import crypto from 'crypto';
import { logger } from '../utils/logger';
import { aiRouter, AIRouterService } from './aiRouter.service';
import { enhancedAIRouterService } from './enhanced-ai-router.service';
import { usageTrackingService } from './usage-tracking.service';
import { geocodingService } from './geocoding.service';
import { getSupabaseClient } from '../lib/supabase';
import { cacheService } from './cache.service';
import { redis } from '../config/redis';
import { redisConnectionPool } from './redis-connection-pool.service';
import {
  ParsedTrip,
  ParseSession,
  ParsedTripSchema,
  parsedTripToDbFormat,
  parsedActivityToDbFormat,
  ActivityType,
  normalizeActivityType,
} from '@travelviz/shared';
import { AI_CONFIG, getModelConfig, getFallbackModels } from '../config/ai.config';
import { GeminiService } from './gemini.service';

/**
 * Circuit Breaker for AI API calls
 */
class AICircuitBreaker {
  private failures: number = 0;
  private lastFailureTime: number = 0;
  private state: 'CLOSED' | 'OPEN' | 'HALF_OPEN' = 'CLOSED';
  private readonly failureThreshold = 3;
  private readonly recoveryTimeout = 60000; // 60 seconds
  private readonly halfOpenTimeout = 30000; // 30 seconds

  async execute<T>(operation: () => Promise<T>): Promise<T> {
    if (this.state === 'OPEN') {
      const now = Date.now();
      if (now - this.lastFailureTime >= this.recoveryTimeout) {
        this.state = 'HALF_OPEN';
        logger.info('AI Circuit breaker transitioning to HALF_OPEN');
      } else {
        throw new Error('AI service temporarily unavailable. Circuit breaker is OPEN.');
      }
    }

    try {
      const result = await operation();
      this.onSuccess();
      return result;
    } catch (error) {
      this.onFailure(error);
      throw error;
    }
  }

  private onSuccess(): void {
    if (this.state === 'HALF_OPEN') {
      this.state = 'CLOSED';
      this.failures = 0;
      logger.info('AI Circuit breaker recovered to CLOSED state');
    }
  }

  private onFailure(error: unknown): void {
    this.failures++;
    this.lastFailureTime = Date.now();

    // Check if it's a rate limit error (don't count against circuit breaker)
    if (error instanceof Error && error.message.includes('Rate limit')) {
      logger.warn('AI rate limit hit, not counting against circuit breaker');
      return;
    }

    if (this.failures >= this.failureThreshold) {
      this.state = 'OPEN';
      logger.error('AI Circuit breaker OPENED due to failures', {
        failures: this.failures,
        recoveryTime: new Date(Date.now() + this.recoveryTimeout).toISOString()
      });
    }
  }

  getState(): string {
    return this.state;
  }

  reset(): void {
    this.failures = 0;
    this.state = 'CLOSED';
    this.lastFailureTime = 0;
  }
}

/**
 * Enhanced AI Parser Service with SSE Progress Updates
 * Provides real-time parsing progress through Redis pub/sub
 */
export class AIParserService {
  private aiRouter: AIRouterService;
  private progressChannel = 'parse:progress:';
  private aiCircuitBreaker: AICircuitBreaker;
  private geminiService: GeminiService;

  constructor() {
    this.aiRouter = aiRouter;
    this.aiCircuitBreaker = new AICircuitBreaker();
    this.geminiService = GeminiService.getInstance();
    // Enhanced AI Router Service handles all AI configuration and model selection
    
    // Log circuit breaker initial state
    logger.info('AIParserService initialized', {
      circuitBreakerState: this.aiCircuitBreaker.getState(),
      geminiAvailable: this.geminiService.isAvailable()
    });
  }

  /**
   * Validate AI source type
   */
  private validateAISource(source: string): 'chatgpt' | 'claude' | 'gemini' | 'unknown' {
    const validSources = ['chatgpt', 'claude', 'gemini', 'unknown'];
    return validSources.includes(source) 
      ? source as 'chatgpt' | 'claude' | 'gemini' | 'unknown'
      : 'unknown';
  }

  /**
   * Create a parse session and start parsing
   */
  async createParseSession(content: string, source: string, userId: string): Promise<string> {
    // Check for duplicate request
    const existingSessionId = await this.deduplicateRequest(content, userId);
    if (existingSessionId) {
      logger.info('Duplicate request detected, returning existing session', { 
        sessionId: existingSessionId,
        userId 
      });
      return existingSessionId;
    }

    const sessionId = uuidv4();
    
    // Store initial session in database with proper timestamp handling
    const now = new Date().toISOString();
    const { error } = await getSupabaseClient()
      .from('ai_import_logs')
      .insert({
        id: sessionId,
        user_id: userId,
        ai_platform: source,
        import_status: 'processing',
        raw_conversation: content.substring(0, 5000), // Limit stored content
        created_at: now,
        updated_at: now, // FIXED: Ensure updated_at is not before created_at
      });

    if (error) {
      logger.error('Failed to create parse session', { error, sessionId, userId });
      throw new Error('Failed to create parse session');
    }

    logger.info('Parse session created successfully', { 
      sessionId, 
      userId, 
      source,
      contentLength: content.length 
    });

    // Update deduplication cache with actual session ID
    await this.updateDedupeCache(content, userId, sessionId);

    // Start async parsing - don't await to return immediately
    // Use setTimeout to ensure it runs in the background
    setTimeout(() => {
      logger.info('Starting background parse', { sessionId, source });
      this.parseAsync(sessionId, content, source).catch(error => {
        logger.error('Background parsing failed', { sessionId, error });
      });
    }, 100); // Small delay to ensure response is sent first

    // Publish initial progress
    await this.publishProgress(sessionId, 'initializing', 0, AI_CONFIG.progressMessages.initializing);

    return sessionId;
  }

  /**
   * Get parse session status
   */
  async getSession(sessionId: string): Promise<ParseSession | null> {
    const { data, error } = await getSupabaseClient()
      .from('ai_import_logs')
      .select('*')
      .eq('id', sessionId)
      .single();

    if (error || !data) {
      return null;
    }

    // Check for orphaned sessions (processing for more than 5 minutes)
    if (data.import_status === 'processing') {
      const sessionAge = Date.now() - new Date(data.created_at).getTime();
      const maxProcessingTime = 5 * 60 * 1000; // 5 minutes

      if (sessionAge > maxProcessingTime) {
        logger.warn('Detected orphaned session, marking as failed', {
          sessionId: data.id,
          ageMinutes: Math.round(sessionAge / 60000),
          created: data.created_at,
          updated: data.updated_at
        });

        // Mark as failed due to timeout
        try {
          await getSupabaseClient()
            .from('ai_import_logs')
            .update({
              import_status: 'failed',
              error_message: 'Session timeout - processing took too long or server was restarted',
              updated_at: new Date().toISOString()
            })
            .eq('id', sessionId);

          // Return the failed session
          return {
            id: data.id,
            status: 'error',
            progress: 0,
            currentStep: 'error',
            result: undefined,
            error: 'Session timeout - processing took too long or server was restarted',
            startedAt: new Date(data.created_at),
            completedAt: new Date(),
          };
        } catch (updateError) {
          logger.error('Failed to mark orphaned session as failed', { sessionId, updateError });
        }
      }
    }

    // Map database fields to ParseSession type
    return {
      id: data.id,
      status: (data.import_status === 'success' || data.import_status === 'complete') ? 'complete' :
              data.import_status === 'failed' ? 'error' :
              data.import_status === 'processing' ? 'processing' : 'pending',
      progress: (data.import_status === 'success' || data.import_status === 'complete') ? 100 : 50,
      currentStep: data.import_status === 'processing' ? 'parsing' : 'complete',
      result: data.parsed_data as ParsedTrip | undefined,
      error: data.error_message || undefined,
      startedAt: new Date(data.created_at),
      completedAt: (data.import_status === 'success' || data.import_status === 'complete' || data.import_status === 'failed') ? new Date() : undefined,
    };
  }

  /**
   * Check for duplicate requests using content-based fingerprinting
   * Returns existing sessionId if found, null otherwise
   */
  private async deduplicateRequest(content: string, userId: string): Promise<string | null> {
    // Create content hash
    const hash = crypto.createHash('sha256')
      .update(content)
      .update(userId)
      .digest('hex');
    
    const dedupeKey = `parse:dedupe:${hash}`;
    
    return redisConnectionPool.execute(async (redis) => {
      // Try to get existing session ID
      const existingSessionId = await redis.get(dedupeKey);
      
      if (existingSessionId && typeof existingSessionId === 'string') {
        // Check if session is still valid
        const session = await this.getSession(existingSessionId);
        if (session && session.status !== 'error') {
          // Check if session is too old (more than 10 minutes)
          const sessionAge = Date.now() - session.startedAt.getTime();
          const maxAge = 10 * 60 * 1000; // 10 minutes

          if (sessionAge > maxAge) {
            logger.info('Found old session, marking as failed and creating new one', {
              sessionId: existingSessionId,
              status: session.status,
              ageMinutes: Math.round(sessionAge / 60000)
            });

            // Mark old session as failed
            try {
              await getSupabaseClient()
                .from('ai_import_logs')
                .update({
                  import_status: 'failed',
                  error_message: 'Session timeout - too old',
                  updated_at: new Date().toISOString()
                })
                .eq('id', existingSessionId);
            } catch (error) {
              logger.error('Failed to mark old session as failed', { sessionId: existingSessionId, error });
            }

            // Remove from Redis cache
            await redis.del(dedupeKey);
            return null; // Create new session
          }

          logger.info('Found existing parse session', {
            sessionId: existingSessionId,
            status: session.status
          });
          return existingSessionId;
        }
      }
      
      // No valid existing session found
      // Store placeholder to prevent race conditions
      const tempSessionId = `pending_${uuidv4()}`;
      const result = await redis.set(dedupeKey, tempSessionId, {
        nx: true,  // Only set if not exists
        ex: 500    // 500ms TTL for race condition prevention
      });
      
      if (result === null) {
        // Another request set the key, wait and retry
        await new Promise(resolve => setTimeout(resolve, 100));
        const retrySessionId = await redis.get(dedupeKey);
        if (retrySessionId && typeof retrySessionId === 'string' && !retrySessionId.startsWith('pending_')) {
          return retrySessionId;
        }
      }
      
      // We won the race, will create new session
      // Update the key with longer TTL after session is created
      return null;
    });
  }
  
  /**
   * Update deduplication cache with actual session ID
   */
  private async updateDedupeCache(content: string, userId: string, sessionId: string): Promise<void> {
    const hash = crypto.createHash('sha256')
      .update(content)
      .update(userId)
      .digest('hex');
    
    const dedupeKey = `parse:dedupe:${hash}`;
    
    await redisConnectionPool.execute(async (redis) => {
      // Store actual session ID with 5 minute TTL
      await redis.set(dedupeKey, sessionId, {
        ex: 300  // 5 minutes
      });
    });
  }

  /**
   * Parse content asynchronously with progress updates
   */
  private async parseAsync(
    sessionId: string,
    content: string,
    source: string
  ): Promise<void> {
    let currentStep = 'initializing';

    try {
      logger.info('parseAsync started', { sessionId, contentLength: content.length, source });
      
      // Step 1: Initializing (10%)
      currentStep = 'initializing';
      await this.updateSessionStatus(sessionId, 'processing', 10, 'initializing', 'Setting up AI parsing session...');

      // Enhanced AI Router Service will handle all model selection and logging
      logger.info('Starting AI parse with Enhanced AI Router Service', {
        sessionId,
        contentLength: content.length,
        source,
        hasGeminiKey: !!process.env.GOOGLE_GEMINI_API_KEY,
        hasOpenRouterKey: !!process.env.OPENROUTER_API_KEY
      });

      // Step 2: Extracting (20%)
      currentStep = 'extracting';
      await this.updateSessionStatus(sessionId, 'processing', 20, 'extracting', 'Reading and processing your content...');

      // Step 3: AI Parsing (40%) - Using Enhanced AI Router Service
      currentStep = 'parsing';
      await this.updateSessionStatus(sessionId, 'processing', 40, 'parsing', 'AI is analyzing your travel plans...');

      logger.info('Using Enhanced AI Router for intelligent model selection', { 
        source, 
        contentLength: content.length, 
        complexity,
        sessionId 
      });

      // Add intermediate progress update during AI call
      const progressUpdateInterval = setInterval(async () => {
        await this.updateSessionStatus(sessionId, 'processing', 45, 'parsing', 'AI is still analyzing your content...');
      }, 15000); // Update every 15 seconds during AI call

      let parsedData;
      try {
        // Use Enhanced AI Router Service with intelligent model selection and usage tracking
        // Requirements 2.1, 2.2, 5.1, 5.2: Prioritize free tier, handle rate limits, fallbacks
        parsedData = await this.aiCircuitBreaker.execute(
          () => enhancedAIRouterService.parseContent(content, source, sessionId)
        );
      } catch (enhancedRouterError) {
        logger.warn('Enhanced AI Router Service failed, falling back to legacy AI system', {
          error: enhancedRouterError instanceof Error ? enhancedRouterError.message : String(enhancedRouterError),
          sessionId
        });

        // Fallback to legacy AI system when Enhanced AI Router Service fails
        try {
          parsedData = await this.aiCircuitBreaker.execute(
            () => this.callAIAPIWithFallback(content, sessionId)
          );
        } catch (legacyError) {
          logger.error('Both Enhanced AI Router Service and legacy AI system failed', {
            enhancedError: enhancedRouterError instanceof Error ? enhancedRouterError.message : String(enhancedRouterError),
            legacyError: legacyError instanceof Error ? legacyError.message : String(legacyError),
            sessionId
          });
          throw legacyError;
        }
      } finally {
        clearInterval(progressUpdateInterval);
      }

      logger.info('AI API returned', {
        hasData: !!parsedData,
        metadataSource: parsedData?.metadata?.source,
        activities: parsedData?.activities?.length || 0
      });

      // Step 4: Finding Locations (60%)
      currentStep = 'locations';
      await this.updateSessionStatus(sessionId, 'processing', 60, 'locations', 'Identifying destinations and places...');

      // Enhance with geocoding
      const enhancedData = await this.enhanceWithGeocoding(parsedData);

      // Step 5: Processing Dates (80%)
      currentStep = 'dates';
      await this.updateSessionStatus(sessionId, 'processing', 80, 'dates', 'Organizing timeline and schedule...');

      // Validate parsed data
      const validatedData = ParsedTripSchema.parse(enhancedData);

      // Step 6: Finalizing (95%)
      currentStep = 'finalizing';
      await this.updateSessionStatus(sessionId, 'processing', 95, 'finalizing', 'Completing your itinerary...');

      // Store success result and update status in single atomic operation
      // Include model usage tracking per design.md enhanced ai_import_logs
      try {
        const { error: updateError } = await getSupabaseClient()
          .from('ai_import_logs')
          .update({
            import_status: 'complete',
            parsed_data: validatedData,
            model_used: parsedData?.metadata?.modelUsed || 'unknown',
            input_tokens: parsedData?.metadata?.inputTokens || 0,
            output_tokens: parsedData?.metadata?.outputTokens || 0,
            processing_cost: parsedData?.metadata?.cost || 0,
            fallback_attempts: parsedData?.metadata?.fallbackAttempts || 0,
            updated_at: new Date().toISOString(),
          })
          .eq('id', sessionId);

        if (updateError) {
          logger.error('Failed to update parse session to complete', { sessionId, updateError });
          // Try to mark as failed instead of leaving in processing state
          await this.updateSessionStatus(sessionId, 'failed', 95, 'error', 'Failed to save parsing results');
          throw new Error(`Failed to update parse session: ${updateError.message}`);
        }

        logger.info('Parse session updated to complete in database', { sessionId });
      } catch (dbError) {
        logger.error('Failed to update parse session to complete', { sessionId, dbError });
        // Ensure session doesn't remain in processing state
        try {
          await this.updateSessionStatus(sessionId, 'failed', 95, 'error', 'Database save error');
        } catch (fallbackError) {
          logger.error('Failed to mark session as failed after database error', { sessionId, fallbackError });
        }
        throw new Error('Failed to save parsing results to database');
      }

      // Publish final progress update
      await this.publishProgress(sessionId, 'complete', 100, 'Your itinerary is ready!');

      logger.info('Parse completed successfully', { sessionId });

    } catch (error) {
      // Log detailed error information
      logger.error('Parse failed - updating status', { 
        sessionId, 
        currentStep,
        error: error instanceof Error ? {
          message: error.message,
          stack: error.stack,
          name: error.name
        } : error 
      });

      // Determine error message
      let errorMessage = 'Unknown error occurred during parsing';
      if (error instanceof Error) {
        errorMessage = error.message;

        if (error.message.includes('Rate limit')) {
          errorMessage = 'AI service rate limit reached. Please try again in a few minutes.';
        } else if (error.message.includes('timeout') || error.message.includes('timed out')) {
          errorMessage = 'AI processing timed out. Your content may be too complex. Try breaking it into smaller sections.';
        } else if (error.message.includes('Invalid')) {
          errorMessage = 'Invalid format detected. Please check your input and try again.';
        } else if (error.message.includes('Circuit breaker')) {
          errorMessage = 'AI service is temporarily unavailable. Please try again in a few minutes.';
        } else if (error.message.includes('ECONNABORTED') || error.message.includes('ENOTFOUND')) {
          errorMessage = 'Network connection error. Please check your internet connection and try again.';
        } else if (error.message.includes('401') || error.message.includes('Unauthorized')) {
          errorMessage = 'AI service authentication error. Please contact support.';
        } else if (error.message.includes('429')) {
          errorMessage = 'Too many requests. Please wait a moment and try again.';
        } else if (error.message.includes('500') || error.message.includes('502') || error.message.includes('503')) {
          errorMessage = 'AI service is temporarily down. Please try again later.';
        }
      }

      // Always update session status on error - CRITICAL FIX
      try {
        await this.publishProgress(sessionId, 'error', 0, errorMessage);
        
        // Use the improved updateSessionStatus method for consistency
        await this.updateSessionStatus(sessionId, 'failed', 0, 'error', errorMessage);
        
        logger.info('Session marked as failed successfully', { 
          sessionId, 
          errorMessage,
          currentStep 
        });
      } catch (statusUpdateError) {
        logger.error('Critical: Could not update parse session status after error', {
          sessionId,
          statusUpdateError,
          originalError: errorMessage
        });
        
        // Last resort: direct database update
        try {
          await getSupabaseClient()
            .from('ai_import_logs')
            .update({
              import_status: 'failed',
              error_message: `Critical error: ${errorMessage}`,
              updated_at: new Date().toISOString()
            })
            .eq('id', sessionId);
        } catch (finalError) {
          logger.error('Final attempt to update session failed', { sessionId, finalError });
        }
      }
      
      // Don't re-throw - we've handled the error by updating the session
      // This prevents unhandled promise rejections in the background job
    }
  }

  /**
   * Publish progress update via Redis pub/sub
   */
  private async publishProgress(
    sessionId: string,
    step: string,
    progress: number,
    message: string
  ): Promise<void> {
    try {
      const channel = `${this.progressChannel}${sessionId}`;
      
      const update = {
        sessionId,
        step,
        progress,
        message,
        timestamp: new Date().toISOString(),
      };

      // Publish to Redis channel
      await redis.publish(channel, JSON.stringify(update));
      
      // Also store latest progress in cache for fallback
      await cacheService.set(
        `progress:${sessionId}`,
        update,
        { ttl: 300 } // 5 minutes TTL
      );

      logger.debug('Published progress update', { sessionId, step, progress });
    } catch (error) {
      logger.error('Failed to publish progress', { sessionId, error });
      // Don't throw - progress updates are not critical
    }
  }

  /**
   * Call AI API for parsing with fallback chain
   */
  private async callAIAPI(
    content: string,
    source: string,
    modelId: string
  ): Promise<ParsedTrip> {
    const prompt = this.buildPrompt(content, source);
    const modelConfig = getModelConfig(modelId);

    // Log detailed model selection info
    logger.info('AI API call details', {
      requestedModelId: modelId,
      modelConfig: modelConfig ? {
        id: modelConfig.id,
        name: modelConfig.name,
        provider: modelConfig.provider,
        maxTokens: modelConfig.maxTokens
      } : null,
      fallbackToModelId: modelConfig?.id || modelId
    });

    // Handle Gemini Flash 2.0 via native Google API
    if (modelId === 'gemini-flash-2.0') {
      if (this.geminiService.isAvailable()) {
        try {
          logger.info('Using Gemini Flash 2.0 via Google API (free)');
          const fullPrompt = `${AI_CONFIG.systemPrompt}\n\n${prompt}`;
          const geminiResult = await this.geminiService.parseWithGemini(content, fullPrompt);
          
          // Convert Gemini result to expected format with metadata
          const parsedTrip: ParsedTrip = {
            title: geminiResult.title || 'Untitled Trip',
            description: geminiResult.description,
            destination: geminiResult.destination || '',
            startDate: geminiResult.startDate || new Date().toISOString().split('T')[0],
            endDate: geminiResult.endDate || new Date().toISOString().split('T')[0],
            activities: (geminiResult.activities || []).map((activity) => ({
              name: activity.title || 'Activity',
              type: normalizeActivityType(activity.type || ActivityType.activity),
              startTime: activity.startTime || new Date().toISOString(),
              endTime: activity.endTime,
              location: activity.location ? {
                address: activity.location,
                lat: 0,
                lng: 0,
                confidence: 0.8
              } : undefined,
              price: activity.price,
              currency: activity.currency || 'USD',
              dayNumber: activity.day || 1,
              confidence: 0.9
            })),
            metadata: {
              source: this.validateAISource(source),
              confidence: 0.9,
              warnings: [],
              parseDate: new Date().toISOString(),
              version: '1.0'
            }
          };
          
          return parsedTrip;
        } catch (error) {
          logger.warn('Gemini API failed, falling back to next model', { error });
          // Continue to fallback logic
          if (AI_CONFIG.fallbackModels.length > 0) {
            return this.callAIAPIWithFallback(content, source, 0);
          }
          throw error;
        }
      } else {
        // Gemini not available, use fallback immediately
        logger.info('Gemini API key not configured, using fallback models');
        if (AI_CONFIG.fallbackModels.length > 0) {
          return this.callAIAPIWithFallback(content, source, 0);
        }
        throw new Error('No AI models available for parsing');
      }
    }

    // Regular OpenRouter API call
    const actualModelId = modelConfig?.id || modelId;
    logger.info(`Making OpenRouter API call with model: ${actualModelId}`);

    try {
      // Add aggressive timeout wrapper for problematic models
      const API_TIMEOUT = 45000; // 45 seconds - more aggressive than config
      const response = await Promise.race([
        axios.post(
          'https://openrouter.ai/api/v1/chat/completions',
          {
            model: actualModelId,
            messages: [
              {
                role: 'system',
                content: AI_CONFIG.systemPrompt
              },
              {
                role: 'user',
                content: prompt
              }
            ],
            temperature: 0.3,
            max_tokens: 4000,
            response_format: { type: 'json_object' }
          },
          {
            headers: {
              'Authorization': `Bearer ${process.env.OPENROUTER_API_KEY}`,
              'Content-Type': 'application/json',
              'HTTP-Referer': 'https://travelviz.app',
              'X-Title': 'TravelViz'
            },
            timeout: API_TIMEOUT // Use aggressive timeout
          }
        ),
        new Promise<never>((_, reject) => {
          setTimeout(() => {
            reject(new Error(`OpenRouter API call timed out after ${API_TIMEOUT / 1000}s for model ${actualModelId}. This model may be unavailable or overloaded.`));
          }, API_TIMEOUT);
        })
      ]);

      const aiResponse = response.data.choices[0]?.message?.content;
      if (!aiResponse) {
        throw new Error('No response from AI');
      }

      // Log model usage
      logger.info('AI parsing completed', { 
        model: modelConfig?.name || modelId,
        cost: modelConfig?.costPer1kTokens === 0 ? 'FREE' : `$${modelConfig?.costPer1kTokens || 0}/1k tokens`
      });

      // Parse JSON response
      const parsed = JSON.parse(aiResponse);

      // Ensure metadata has correct source value
      const validSource = ['chatgpt', 'claude', 'gemini', 'unknown'].includes(source)
        ? source as 'chatgpt' | 'claude' | 'gemini' | 'unknown'
        : 'unknown';

      // Normalize activity types to handle AI inconsistencies
      if (parsed.activities && Array.isArray(parsed.activities)) {
        parsed.activities = parsed.activities.map((activity: any) => ({
          ...activity,
          type: normalizeActivityType(activity.type || 'activity')
        }));
      }

      // Prepare parsed data
      const parsedData: ParsedTrip = {
        ...parsed,
        metadata: {
          source: validSource,
          confidence: 0.9,
          warnings: [],
          parseDate: new Date().toISOString(),
          version: '1.0'
        }
      };

      return parsedData;

    } catch (error) {
      if (axios.isAxiosError(error)) {
        if (error.response?.status === 429) {
          throw new Error('Rate limit exceeded. Please try again later.');
        }
        if (error.code === 'ECONNABORTED') {
          throw new Error('Request timed out. Please try with a shorter conversation.');
        }
      }
      throw error;
    }
  }

  /**
   * Call AI API with complexity-aware fallback
   */
  private async callAIAPIWithComplexityFallback(
    content: string,
    source: string,
    primaryModelId: string,
    complexity: 'simple' | 'medium' | 'complex' | 'very_complex'
  ): Promise<ParsedTrip> {
    try {
      logger.info(`Trying primary model for ${complexity} content: ${primaryModelId}`);
      return await this.callAIAPI(content, source, primaryModelId);
    } catch (error) {
      // Enhanced error logging for debugging model selection issues
      const errorDetails = {
        primaryModelId,
        complexity,
        errorType: error instanceof Error ? error.constructor.name : 'Unknown',
        errorMessage: error instanceof Error ? error.message : String(error),
        isTimeout: error instanceof Error && (error.message.includes('timeout') || error.message.includes('ECONNABORTED')),
        isRateLimit: error instanceof Error && (error.message.includes('429') || error.message.includes('rate limit')),
        isModelNotFound: error instanceof Error && (error.message.includes('404') || error.message.includes('not found')),
      };

      logger.warn(`Primary model ${primaryModelId} failed for ${complexity} content`, errorDetails);

      // Provide specific error context for debugging
      if (errorDetails.isModelNotFound) {
        logger.warn(`Model ${primaryModelId} may not be available on OpenRouter`);
      } else if (errorDetails.isRateLimit) {
        logger.warn(`Rate limit hit for model ${primaryModelId}, trying fallback`);
      } else if (errorDetails.isTimeout) {
        logger.warn(`Timeout occurred for model ${primaryModelId}, trying fallback`);
      }

      // Get complexity-specific fallback models
      const fallbackModels = getFallbackModels(complexity);
      logger.info(`Falling back to models: ${fallbackModels.join(', ')}`);
      return this.callAIAPIWithFallback(content, source, 0, fallbackModels);
    }
  }

  /**
   * Call AI API with fallback models
   */
  private async callAIAPIWithFallback(
    content: string,
    source: string,
    fallbackIndex: number,
    fallbackModels: string[] = AI_CONFIG.fallbackModels
  ): Promise<ParsedTrip> {
    if (fallbackIndex >= fallbackModels.length) {
      throw new Error('All AI models failed. Please try again later.');
    }

    const fallbackModelKey = fallbackModels[fallbackIndex];
    const fallbackModel = getModelConfig(fallbackModelKey);

    if (!fallbackModel) {
      return this.callAIAPIWithFallback(content, source, fallbackIndex + 1, fallbackModels);
    }

    try {
      logger.info(`Trying fallback model: ${fallbackModel.name} (${fallbackModel.maxTokens} max tokens)`);
      return await this.callAIAPI(content, source, fallbackModelKey);
    } catch (error) {
      logger.warn(`Fallback model ${fallbackModel.name} failed`, { error });
      return this.callAIAPIWithFallback(content, source, fallbackIndex + 1, fallbackModels);
    }
  }

  /**
   * Build AI prompt (simplified for token reduction)
   */
  private buildPrompt(content: string, source: string): string {
    // Limit content to 8000 chars to stay within token limits
    const truncatedContent = content.substring(0, 8000);
    return `Extract trip from ${source} chat:\n${truncatedContent}`;
  }

  /**
   * Enhance parsed data with real geocoding
   */
  private async enhanceWithGeocoding(data: ParsedTrip): Promise<ParsedTrip> {
    if (!geocodingService.isAvailable()) {
      logger.warn('Geocoding service not available, using fallback coordinates');
      return this.addFallbackCoordinates(data);
    }

    try {
      // Collect all unique locations to geocode
      const locationsToGeocode = new Set<string>();
      
      // Add destination
      if (data.destination) {
        locationsToGeocode.add(data.destination);
      }

      // Add activity locations
      data.activities.forEach(activity => {
        if (activity.location?.address) {
          locationsToGeocode.add(activity.location.address);
        }
      });

      // Geocode locations in batches to avoid timeouts
      const GEOCODE_BATCH_SIZE = 10;
      const locations = Array.from(locationsToGeocode);
      const geocodeResults = new Map<string, { lat: number; lng: number; formatted: string }>();
      
      // Process in batches
      for (let i = 0; i < locations.length; i += GEOCODE_BATCH_SIZE) {
        const batch = locations.slice(i, i + GEOCODE_BATCH_SIZE);
        const batchResults = await geocodingService.geocodeBatch(batch);
        
        // Merge results
        batchResults.forEach((value, key) => {
          if (value !== null) {
            geocodeResults.set(key, value);
          }
        });
        
        // Log progress
        logger.info('Geocoding progress', {
          batchIndex: Math.floor(i / GEOCODE_BATCH_SIZE) + 1,
          totalBatches: Math.ceil(locations.length / GEOCODE_BATCH_SIZE),
          locationsProcessed: Math.min(i + GEOCODE_BATCH_SIZE, locations.length),
          totalLocations: locations.length
        });
      }

      // Apply geocoding results to activities
      const enhancedActivities = data.activities.map(activity => {
        if (!activity.location?.address) {
          // No address, use destination as fallback
          const destResult = geocodeResults.get(data.destination);
          if (destResult) {
            return {
              ...activity,
              location: {
                address: data.destination,
                lat: destResult.lat + (Math.random() - 0.5) * 0.01, // Small variation
                lng: destResult.lng + (Math.random() - 0.5) * 0.01,
                confidence: 0.7
              }
            };
          }
          return activity;
        }

        // Has address, try to geocode it
        const result = geocodeResults.get(activity.location.address);
        if (result) {
          return {
            ...activity,
            location: {
              address: result.formatted,
              lat: result.lat,
              lng: result.lng,
              confidence: 0.9
            }
          };
        }

        // Geocoding failed, keep original or add destination coords
        const destResult = geocodeResults.get(data.destination);
        if (destResult) {
          return {
            ...activity,
            location: {
              ...activity.location,
              lat: destResult.lat + (Math.random() - 0.5) * 0.02,
              lng: destResult.lng + (Math.random() - 0.5) * 0.02,
              confidence: 0.5
            }
          };
        }

        return activity;
      });

      return {
        ...data,
        activities: enhancedActivities
      };

    } catch (error) {
      logger.error('Geocoding enhancement failed', { error });
      return this.addFallbackCoordinates(data);
    }
  }

  /**
   * Add fallback coordinates when geocoding fails
   */
  private addFallbackCoordinates(data: ParsedTrip): ParsedTrip {
    // Common city coordinates for fallback
    const cityCoords: Record<string, { lat: number; lng: number }> = {
      'paris': { lat: 48.8566, lng: 2.3522 },
      'london': { lat: 51.5074, lng: -0.1278 },
      'tokyo': { lat: 35.6762, lng: 139.6503 },
      'new york': { lat: 40.7128, lng: -74.0060 },
      'rome': { lat: 41.9028, lng: 12.4964 },
      'barcelona': { lat: 41.3851, lng: 2.1734 },
      'amsterdam': { lat: 52.3676, lng: 4.9041 },
      'bangkok': { lat: 13.7563, lng: 100.5018 },
      'singapore': { lat: 1.3521, lng: 103.8198 },
      'dubai': { lat: 25.2048, lng: 55.2708 },
    };

    const destLower = data.destination.toLowerCase();
    let baseCoords = { lat: 0, lng: 0 };

    // Find matching city
    for (const [city, coords] of Object.entries(cityCoords)) {
      if (destLower.includes(city)) {
        baseCoords = coords;
        break;
      }
    }

    const enhancedActivities = data.activities.map(activity => {
      if (!activity.location || !activity.location.lat) {
        return {
          ...activity,
          location: {
            address: activity.location?.address || data.destination,
            lat: baseCoords.lat + (Math.random() - 0.5) * 0.1,
            lng: baseCoords.lng + (Math.random() - 0.5) * 0.1,
            confidence: 0.6
          }
        };
      }
      return activity;
    });

    return {
      ...data,
      activities: enhancedActivities
    };
  }

  /**
   * Create trip from parsed data
   */
  async createTripFromParse(
    sessionId: string, 
    userId: string,
    edits?: Partial<ParsedTrip>
  ): Promise<string> {
    // Get parse session
    const session = await this.getSession(sessionId);
    if (!session || session.status !== 'complete' || !session.result) {
      throw new Error('Parse session not found or incomplete');
    }

    // Apply any edits
    const tripData = edits ? { ...session.result, ...edits } : session.result;

    // Create trip in database
    const tripId = uuidv4();
    const dbTrip = parsedTripToDbFormat(tripData, userId);

    const { error: tripError } = await getSupabaseClient()
      .from('trips')
      .insert({
        id: tripId,
        ...dbTrip,
        visibility: 'private',
        status: 'planning',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      });

    if (tripError) {
      logger.error('Failed to create trip', { tripError });
      throw new Error('Failed to create trip');
    }

    // Create activities
    if (tripData.activities.length > 0) {
      const activities = tripData.activities.map((activity, index) => ({
        id: uuidv4(),
        ...parsedActivityToDbFormat(activity, tripId, index),
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      }));

      const { error: activitiesError } = await getSupabaseClient()
        .from('activities')
        .insert(activities);

      if (activitiesError) {
        logger.error('Failed to create activities', { activitiesError });
        // Continue anyway - trip is created
      }
    }

    // Update import log with trip ID
    await getSupabaseClient()
      .from('ai_import_logs')
      .update({
        trip_id: tripId,
      })
      .eq('id', sessionId);

    return tripId;
  }

  /**
   * Update session status in database
   */
  private async updateSessionStatus(
    sessionId: string,
    status: string,
    progress: number,
    currentStep: string,
    message: string
  ): Promise<void> {
    try {
      // FIXED: Always update status to prevent orphaned sessions
      // Use atomic update with proper timestamp handling
      const updateData: any = {
        import_status: status,
        updated_at: new Date().toISOString(),
      };

      // Add error message for failed status
      if (status === 'failed' && message) {
        updateData.error_message = message;
      }

      // Add parsed data for complete status
      if (status === 'complete' && currentStep === 'complete') {
        // This will be handled separately in the main completion logic
        logger.debug('Complete status update - parsed data handled separately', { sessionId });
      }

      const { error } = await getSupabaseClient()
        .from('ai_import_logs')
        .update(updateData)
        .eq('id', sessionId);

      if (error) {
        logger.error('Failed to update session status', { sessionId, status, error });
        // Don't throw - continue with progress publishing
      } else {
        logger.info('Session status updated successfully', { 
          sessionId, 
          status, 
          progress,
          currentStep 
        });
      }

      // Always publish progress for real-time updates
      await this.publishProgress(sessionId, currentStep, progress, message);
    } catch (error) {
      logger.error('Error updating session status', { sessionId, status, error });
      // Don't throw - this is a background operation
    }
  }

  /**
   * Get AI circuit breaker status
   */
  getCircuitBreakerStatus(): { state: string; isAvailable: boolean } {
    const state = this.aiCircuitBreaker.getState();
    return {
      state,
      isAvailable: state !== 'OPEN'
    };
  }
}

// Export singleton instance with lazy initialization
let _aiParserService: AIParserService | null = null;

export const getAIParserService = (): AIParserService => {
  if (!_aiParserService) {
    logger.info('Creating AIParserService', {
      hasGeminiKey: !!process.env.GOOGLE_GEMINI_API_KEY,
      hasOpenRouterKey: !!process.env.OPENROUTER_API_KEY,
      nodeEnv: process.env.NODE_ENV
    });
    _aiParserService = new AIParserService();
  }
  return _aiParserService;
};

// For backward compatibility, provide a getter that lazily initializes
export const aiParserService = {
  get instance(): AIParserService {
    return getAIParserService();
  }
};

