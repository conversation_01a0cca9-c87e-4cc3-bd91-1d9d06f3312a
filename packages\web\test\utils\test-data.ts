import type { Activity, Trip } from '@travelviz/shared';
import { ActivityType } from '@travelviz/shared';

export function createMockActivity(overrides: Partial<Activity> = {}): Activity {
  return {
    id: '1',
    trip_id: 'trip-1',
    title: 'Test Activity',
    description: null,
    type: ActivityType.activity,
    position: 0,
    start_time: null,
    end_time: null,
    location: null,
    location_lat: null,
    location_lng: null,
    price: null,
    currency: 'USD',
    booking_reference: null,
    notes: null,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
    metadata: {},
    attachments: [],
    ...overrides,
  };
}

export function createMockTrip(overrides: Partial<Trip> = {}): Trip {
  return {
    id: 'trip-1',
    user_id: 'user-1',
    title: 'Test Trip',
    description: null,
    destination: null,
    start_date: null,
    end_date: null,
    status: 'draft',
    visibility: 'private',
    cover_image: null,
    budget_amount: null,
    budget_currency: 'USD',
    views: 0,
    deleted_at: null,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
    metadata: {},
    tags: [],
    ...overrides,
  };
}