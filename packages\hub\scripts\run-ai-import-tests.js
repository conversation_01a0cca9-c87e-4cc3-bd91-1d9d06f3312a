#!/usr/bin/env node

/**
 * Run AI import tests with proper environment setup
 */

const { spawn } = require('child_process');
const path = require('path');

// Set up environment
process.env.NODE_ENV = 'test';
process.env.NODE_OPTIONS = '--max-old-space-size=4096';

console.log('🧪 Running AI Import Tests...\n');

// Run the tests
const testProcess = spawn(
  'vitest',
  [
    'run',
    'src/integration/__tests__/ai-import-fixed.test.ts',
    '--reporter=verbose',
    '--no-coverage'
  ],
  {
    cwd: path.join(__dirname, '..'),
    stdio: 'inherit',
    shell: true
  }
);

testProcess.on('close', (code) => {
  if (code === 0) {
    console.log('\n✅ All tests passed!');
    
    // Show log file locations
    const logsDir = path.join(__dirname, '..', 'logs');
    console.log('\n📁 Test logs available at:');
    console.log(`   ${logsDir}`);
    console.log('\n💡 View logs with:');
    console.log('   pnpm test:logs');
    console.log('   pnpm test:logs:errors');
  } else {
    console.log(`\n❌ Tests failed with code ${code}`);
    console.log('\n💡 Debug with:');
    console.log('   pnpm test:logs:errors');
  }
  
  process.exit(code);
});

testProcess.on('error', (err) => {
  console.error('Failed to run tests:', err);
  process.exit(1);
});