/**
 * Sanitization utilities for preventing injection attacks and XSS
 */

import DOMPurify from 'isomorphic-dompurify';

/**
 * Sanitizes text input to prevent prompt injection attacks
 * Removes or escapes potentially dangerous patterns
 */
export function sanitizeForAIPrompt(input: string): string {
  if (!input || typeof input !== 'string') {
    return '';
  }

  // Remove potential prompt injection patterns
  let sanitized = input
    // Remove attempts to break out of context
    .replace(/```[\s\S]*?```/g, '[CODE REMOVED]')
    .replace(/\{\{[\s\S]*?\}\}/g, '[TEMPLATE REMOVED]')
    .replace(/<\|[\s\S]*?\|>/g, '[INSTRUCTION REMOVED]')
    // Remove potential system prompts
    .replace(/(?:system|assistant|user)\s*:/gi, '[ROLE REMOVED]:')
    .replace(/\[INST\][\s\S]*?\[\/INST\]/g, '[INSTRUCTION REMOVED]')
    // Remove control characters except newlines and tabs
    // eslint-disable-next-line no-control-regex
    .replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/g, '')
    // Limit consecutive newlines
    .replace(/\n{3,}/g, '\n\n')
    // Remove potential command injections
    .replace(/(?:ignore|forget|disregard).*(?:previous|above|prior).*(?:instructions?|prompts?)/gi, '[INJECTION ATTEMPT REMOVED]')
    .replace(/(?:new|override|change).*(?:instructions?|prompts?|rules?)/gi, '[INJECTION ATTEMPT REMOVED]');

  // Truncate to reasonable length to prevent DoS
  const MAX_LENGTH = 50000;
  if (sanitized.length > MAX_LENGTH) {
    sanitized = sanitized.substring(0, MAX_LENGTH) + '... [TRUNCATED]';
  }

  return sanitized.trim();
}

/**
 * Validates and sanitizes URLs to prevent XSS attacks
 * Returns null if URL is invalid or potentially dangerous
 */
export function sanitizeURL(url: string): string | null {
  if (!url || typeof url !== 'string') {
    return null;
  }

  try {
    const parsed = new URL(url);
    
    // Only allow http(s) protocols
    if (!['http:', 'https:'].includes(parsed.protocol)) {
      return null;
    }

    // Prevent javascript: and data: URLs
    if (url.toLowerCase().includes('javascript:') || url.toLowerCase().includes('data:')) {
      return null;
    }

    // Reconstruct URL to ensure it's clean
    return parsed.toString();
  } catch {
    // Invalid URL
    return null;
  }
}

/**
 * Escapes HTML special characters to prevent XSS
 */
export function escapeHTML(text: string): string {
  if (!text || typeof text !== 'string') {
    return '';
  }

  const map: Record<string, string> = {
    '&': '&amp;',
    '<': '&lt;',
    '>': '&gt;',
    '"': '&quot;',
    "'": '&#39;',
    '/': '&#x2F;'
  };

  return text.replace(/[&<>"'/]/g, (char) => map[char] || char);
}

/**
 * Sanitizes HTML content to prevent XSS attacks
 * Removes dangerous tags and attributes while preserving safe content
 */
export function sanitizeHTML(html: string, options?: {
  allowedTags?: string[];
  allowedAttributes?: string[];
}): string {
  if (!html || typeof html !== 'string') {
    return '';
  }

  // Default allowed tags for basic formatting
  const defaultAllowedTags = [
    'p', 'br', 'strong', 'em', 'u', 'i', 'b',
    'ul', 'ol', 'li', 'a', 'span', 'div',
    'h1', 'h2', 'h3', 'h4', 'h5', 'h6'
  ];

  // Default allowed attributes
  const defaultAllowedAttributes = ['href', 'title', 'class'];

  const config = {
    ALLOWED_TAGS: options?.allowedTags || defaultAllowedTags,
    ALLOWED_ATTR: options?.allowedAttributes || defaultAllowedAttributes,
    KEEP_CONTENT: true,
    // Prevent data URIs
    ALLOWED_URI_REGEXP: /^(?:(?:(?:f|ht)tps?|mailto|tel|callto|cid|xmpp):|[^a-z]|[a-z+.-]+(?:[^a-z+.:-]|$))/i
  };

  return DOMPurify.sanitize(html, config);
}

/**
 * Strictly sanitizes user input by removing all HTML tags
 * Use this for inputs that should never contain HTML
 */
export function sanitizeUserInput(input: string): string {
  if (!input || typeof input !== 'string') {
    return '';
  }

  // Remove all HTML tags and attributes
  const sanitized = DOMPurify.sanitize(input, {
    ALLOWED_TAGS: [],
    ALLOWED_ATTR: []
  });

  // Additional cleanup for common injection patterns
  return sanitized
    // Remove zero-width characters
    .replace(/[\u200B-\u200D\uFEFF]/g, '')
    // Normalize whitespace
    .replace(/\s+/g, ' ')
    // Trim
    .trim();
}

/**
 * Sanitizes content before sending to AI APIs
 * Combines prompt injection prevention with HTML sanitization
 */
export function sanitizeForAI(content: string): string {
  if (!content || typeof content !== 'string') {
    return '';
  }

  // First remove all HTML to prevent any HTML-based injections
  const htmlSanitized = sanitizeUserInput(content);
  
  // Then apply prompt injection sanitization
  return sanitizeForAIPrompt(htmlSanitized);
}

/**
 * ISO 4217 Currency Codes - Complete list
 * Source: https://www.iso.org/iso-4217-currency-codes.html
 */
const ISO_CURRENCY_CODES = new Set([
  'AED', 'AFN', 'ALL', 'AMD', 'ANG', 'AOA', 'ARS', 'AUD', 'AWG', 'AZN',
  'BAM', 'BBD', 'BDT', 'BGN', 'BHD', 'BIF', 'BMD', 'BND', 'BOB', 'BOV',
  'BRL', 'BSD', 'BTN', 'BWP', 'BYN', 'BZD', 'CAD', 'CDF', 'CHE', 'CHF',
  'CHW', 'CLF', 'CLP', 'CNY', 'COP', 'COU', 'CRC', 'CUC', 'CUP', 'CVE',
  'CZK', 'DJF', 'DKK', 'DOP', 'DZD', 'EGP', 'ERN', 'ETB', 'EUR', 'FJD',
  'FKP', 'GBP', 'GEL', 'GHS', 'GIP', 'GMD', 'GNF', 'GTQ', 'GYD', 'HKD',
  'HNL', 'HRK', 'HTG', 'HUF', 'IDR', 'ILS', 'INR', 'IQD', 'IRR', 'ISK',
  'JMD', 'JOD', 'JPY', 'KES', 'KGS', 'KHR', 'KMF', 'KPW', 'KRW', 'KWD',
  'KYD', 'KZT', 'LAK', 'LBP', 'LKR', 'LRD', 'LSL', 'LYD', 'MAD', 'MDL',
  'MGA', 'MKD', 'MMK', 'MNT', 'MOP', 'MRU', 'MUR', 'MVR', 'MWK', 'MXN',
  'MXV', 'MYR', 'MZN', 'NAD', 'NGN', 'NIO', 'NOK', 'NPR', 'NZD', 'OMR',
  'PAB', 'PEN', 'PGK', 'PHP', 'PKR', 'PLN', 'PYG', 'QAR', 'RON', 'RSD',
  'RUB', 'RWF', 'SAR', 'SBD', 'SCR', 'SDG', 'SEK', 'SGD', 'SHP', 'SLE',
  'SLL', 'SOS', 'SRD', 'SSP', 'STN', 'SYP', 'SZL', 'THB', 'TJS', 'TMT',
  'TND', 'TOP', 'TRY', 'TTD', 'TVD', 'TWD', 'TZS', 'UAH', 'UGX', 'USD',
  'USN', 'UYI', 'UYU', 'UYW', 'UZS', 'VED', 'VES', 'VND', 'VUV', 'WST',
  'XAF', 'XAG', 'XAU', 'XBA', 'XBB', 'XBC', 'XBD', 'XCD', 'XDR', 'XOF',
  'XPD', 'XPF', 'XPT', 'XSU', 'XTS', 'XUA', 'XXX', 'YER', 'ZAR', 'ZMW',
  'ZWL'
]);

/**
 * Validates and sanitizes currency codes using ISO 4217 validation
 * Returns the valid currency code or null if invalid
 */
export function sanitizeCurrency(currency: string | null | undefined): string | null {
  if (!currency || typeof currency !== 'string') {
    return null;
  }

  const uppercased = currency.trim().toUpperCase();
  
  // Check if it's a valid ISO code
  if (ISO_CURRENCY_CODES.has(uppercased)) {
    return uppercased;
  }
  
  // Common mappings for user input
  const commonMappings: Record<string, string> = {
    '$': 'USD',
    '€': 'EUR',
    '£': 'GBP',
    '¥': 'JPY',
    'DOLLAR': 'USD',
    'DOLLARS': 'USD',
    'EURO': 'EUR',
    'EUROS': 'EUR',
    'POUND': 'GBP',
    'POUNDS': 'GBP',
    'YEN': 'JPY',
    'YUAN': 'CNY',
    'RENMINBI': 'CNY',
    'RMB': 'CNY'
  };
  
  const mapped = commonMappings[uppercased];
  if (mapped && ISO_CURRENCY_CODES.has(mapped)) {
    return mapped;
  }
  
  // If it's close to a valid code (e.g., "US$" -> "USD")
  const withoutSymbols = uppercased.replace(/[^A-Z]/g, '');
  if (withoutSymbols.length === 3 && ISO_CURRENCY_CODES.has(withoutSymbols)) {
    return withoutSymbols;
  }
  
  return null;
}

/**
 * Sanitizes file names to prevent directory traversal attacks
 */
export function sanitizeFileName(fileName: string): string {
  if (!fileName || typeof fileName !== 'string') {
    return 'unnamed';
  }

  return fileName
    // Remove directory traversal attempts
    .replace(/\.\./g, '')
    .replace(/[/\\]/g, '')
    // Remove control characters
    // eslint-disable-next-line no-control-regex
    .replace(/[\x00-\x1F\x7F]/g, '')
    // Remove potentially dangerous characters
    .replace(/[<>:"|?*]/g, '')
    // Limit length
    .substring(0, 255)
    .trim() || 'unnamed';
}