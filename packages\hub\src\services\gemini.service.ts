import axios from 'axios';
import { logger } from '../utils/logger';
import { z } from 'zod';
import { ActivityType, ActivityTypeWithAliases, normalizeActivityType } from '@travelviz/shared';

interface GeminiResponse {
  candidates: Array<{
    content: {
      parts: Array<{
        text: string;
      }>;
    };
  }>;
  usageMetadata?: {
    promptTokenCount: number;
    candidatesTokenCount: number;
    totalTokenCount: number;
  };
}

interface ParsedActivity {
  title: string;
  description?: string;
  type?: ActivityType;
  startTime?: string;
  endTime?: string;
  location?: string;
  price?: number;
  currency?: string;
  day?: number;
}

interface ParsedTrip {
  title: string;
  description?: string;
  destination?: string;
  startDate?: string;
  endDate?: string;
  activities: ParsedActivity[];
}

export class GeminiService {
  private static instance: GeminiService;
  private apiKey: string | undefined;
  private baseUrl = 'https://generativelanguage.googleapis.com/v1beta/models';
  private model = 'gemini-2.0-flash-exp';
  private requestCount = 0;
  private lastRequestTime = 0;
  private readonly RATE_LIMIT_DELAY = 4000; // 4 seconds between requests (Free tier: 15 RPM = 4s intervals)
  private readonly MAX_RETRIES = 3;
  private readonly INITIAL_RETRY_DELAY = 2000; // Start with 2 seconds for 503 errors

  private constructor() {
    this.apiKey = process.env.GOOGLE_GEMINI_API_KEY;
    
    if (!this.apiKey) {
      logger.warn('GOOGLE_GEMINI_API_KEY not set - Gemini parsing will not be available');
    } else {
      logger.info('GeminiService initialized with native API');
    }
  }

  static getInstance(): GeminiService {
    if (!GeminiService.instance) {
      GeminiService.instance = new GeminiService();
    }
    return GeminiService.instance;
  }

  /**
   * Check if Gemini service is available
   */
  isAvailable(): boolean {
    return !!this.apiKey;
  }

  /**
   * Wait for rate limit if needed
   */
  private async waitForRateLimit(): Promise<void> {
    const now = Date.now();
    const timeSinceLastRequest = now - this.lastRequestTime;
    
    if (timeSinceLastRequest < this.RATE_LIMIT_DELAY) {
      const waitTime = this.RATE_LIMIT_DELAY - timeSinceLastRequest;
      logger.debug(`Rate limiting: waiting ${waitTime}ms before next request`);
      await new Promise(resolve => setTimeout(resolve, waitTime));
    }
    
    this.lastRequestTime = Date.now();
    this.requestCount++;
  }

  /**
   * Retry with exponential backoff
   */
  private async retryWithBackoff<T>(
    fn: () => Promise<T>,
    attempt = 1
  ): Promise<T> {
    try {
      return await fn();
    } catch (error) {
      if (attempt >= this.MAX_RETRIES) {
        throw error;
      }

      const isRetryable = axios.isAxiosError(error) && 
        (error.response?.status === 429 || 
         error.response?.status === 503 ||
         error.response?.status === 500);

      if (!isRetryable) {
        throw error;
      }

      // Use longer delays for 503 errors (service unavailable)
      const baseDelay = axios.isAxiosError(error) && error.response?.status === 503
        ? this.INITIAL_RETRY_DELAY * 2 // 4 seconds base for 503
        : this.INITIAL_RETRY_DELAY;

      const delay = baseDelay * Math.pow(2, attempt - 1);
      logger.warn(`Gemini API error, retrying in ${delay}ms (attempt ${attempt}/${this.MAX_RETRIES})`, {
        status: axios.isAxiosError(error) ? error.response?.status : 'unknown',
        attempt,
        errorType: axios.isAxiosError(error) && error.response?.status === 503 ? 'service_unavailable' : 'other'
      });

      await new Promise(resolve => setTimeout(resolve, delay));
      return this.retryWithBackoff(fn, attempt + 1);
    }
  }

  /**
   * Parse travel text using native Gemini API
   */
  async parseWithGemini(text: string, prompt: string): Promise<ParsedTrip> {
    if (!this.isAvailable()) {
      throw new Error('Gemini API key not configured');
    }

    try {
      // Apply rate limiting
      await this.waitForRateLimit();

      const startTime = Date.now();
      
      // Make request with retry logic
      const response = await this.retryWithBackoff(async () => {
        return await axios.post(
          `${this.baseUrl}/${this.model}:generateContent`,
          {
            contents: [
              {
                parts: [
                  {
                    text: prompt
                  }
                ]
              }
            ],
            generationConfig: {
              temperature: 0.2,
              maxOutputTokens: 8192, // Increased for complex PDF itineraries
              topP: 0.8,
              topK: 10
            }
          },
          {
            headers: {
              'Content-Type': 'application/json',
              'x-goog-api-key': this.apiKey
            },
            timeout: 60000 // Increased timeout for PDF processing
          }
        );
      });

      const duration = Date.now() - startTime;
      const geminiResponse = response.data as GeminiResponse;
      
      // Extract text from response
      const generatedText = geminiResponse.candidates?.[0]?.content?.parts?.[0]?.text;
      if (!generatedText) {
        throw new Error('No content in Gemini response');
      }

      // Log token usage
      if (geminiResponse.usageMetadata) {
        logger.info('Gemini API usage', {
          promptTokens: geminiResponse.usageMetadata.promptTokenCount,
          responseTokens: geminiResponse.usageMetadata.candidatesTokenCount,
          totalTokens: geminiResponse.usageMetadata.totalTokenCount,
          duration: `${duration}ms`,
          cost: 'FREE'
        });
      }

      // Parse the JSON response
      return this.parseGeminiResponse(generatedText);
    } catch (error) {
      logger.error('Gemini parsing failed', { error });
      
      if (axios.isAxiosError(error)) {
        if (error.response?.status === 429) {
          throw new Error('Gemini API rate limit exceeded. Free tier: 5-15 RPM. Consider upgrading to paid tier.');
        } else if (error.response?.status === 403) {
          throw new Error('Invalid Gemini API key. Check your GOOGLE_GEMINI_API_KEY in environment variables.');
        } else if (error.response?.status === 503) {
          throw new Error('Gemini API service unavailable. Google servers may be overloaded. Try again in a few minutes or upgrade to paid tier for better availability.');
        }
      }
      
      throw error;
    }
  }

  /**
   * Parse Gemini's response into structured data
   */
  private parseGeminiResponse(response: string): ParsedTrip {
    try {
      // Extract JSON from the response (Gemini might include extra text or code blocks)
      let jsonString = response;

      // Remove markdown code blocks if present
      if (response.includes('```json')) {
        jsonString = response.replace(/```json\s*/g, '').replace(/```\s*/g, '');
      } else if (response.includes('```')) {
        jsonString = response.replace(/```\s*/g, '').replace(/```\s*/g, '');
      }

      // Extract JSON object
      const jsonMatch = jsonString.match(/\{[\s\S]*\}/);
      if (!jsonMatch) {
        throw new Error('No JSON found in Gemini response');
      }

      let jsonText = jsonMatch[0];

      // Check for truncated response (incomplete JSON)
      if (this.isJsonTruncated(jsonText)) {
        logger.warn('Detected truncated Gemini response, attempting to fix', {
          responseLength: response.length,
          jsonLength: jsonText.length
        });
        jsonText = this.attemptJsonFix(jsonText);
      }

      const parsed = JSON.parse(jsonText);
      
      // Handle different response formats
      if (parsed.activities && Array.isArray(parsed.activities)) {
        // Check if activities have nested structure (day groups)
        const firstActivity = parsed.activities[0];
        if (firstActivity && firstActivity.activities && !firstActivity.title) {
          // Flatten nested day structure
          parsed.activities = parsed.activities.flatMap((day: { activities: unknown[]; day?: number; dayNumber?: number }) => 
            (day.activities as Array<Record<string, unknown>>).map((act) => ({
              ...act,
              day: day.day || day.dayNumber || 1
            }))
          );
        }
        
        // Transform field names to match expected format
        parsed.activities = parsed.activities.map((activity: Record<string, unknown>) => ({
          title: activity.title || activity.name,
          type: activity.type,
          startTime: activity.startTime || activity.start_time,
          location: typeof activity.location === 'object' && activity.location !== null
            ? (activity.location as { address: string }).address 
            : activity.location,
          price: activity.price,
          currency: activity.currency,
          day: activity.day || activity.dayNumber
        }));
      }
      
      // Validate with Zod
      const schema = z.object({
        title: z.string().default('Untitled Trip'),
        destination: z.string().nullable().optional(),
        startDate: z.string().nullable().optional(),
        endDate: z.string().nullable().optional(),
        activities: z.array(z.object({
          title: z.string(),
          type: z.string().transform((val) => normalizeActivityType(val as ActivityTypeWithAliases)).default(ActivityType.activity),
          startTime: z.string().nullable().optional(),
          location: z.string().nullable().optional(),
          price: z.number().nullable().optional(),
          currency: z.string().nullable().optional(),
          day: z.number().nullable().optional()
        })).default([])
      });

      const validated = schema.parse(parsed);
      
      return {
        title: validated.title,
        destination: validated.destination || undefined,
        startDate: validated.startDate || undefined,
        endDate: validated.endDate || undefined,
        activities: validated.activities.map(activity => ({
          title: activity.title,
          type: activity.type,
          startTime: activity.startTime || undefined,
          location: activity.location || undefined,
          price: activity.price || undefined,
          currency: activity.currency || undefined,
          day: activity.day || undefined
        }))
      };
    } catch (error) {
      logger.error('Failed to parse Gemini response', { error, response: response.substring(0, 500) });

      let errorMessage = 'Failed to parse Gemini response';
      if (error instanceof z.ZodError) {
        const issues = error.issues.map(i => `${i.path.join('.')}: ${i.message}`).join(', ');
        errorMessage = `Invalid trip data format from Gemini: ${issues}`;
      } else if (error instanceof SyntaxError) {
        // Check if this looks like a truncation issue
        if (response.length > 1000 && !response.trim().endsWith('}')) {
          errorMessage = 'Gemini response was truncated. The itinerary may be too complex for a single request. Please try with a shorter conversation or contact support.';
        } else {
          errorMessage = 'Gemini returned invalid JSON format. Please try again.';
        }
      } else if (error instanceof Error) {
        errorMessage = `Failed to parse Gemini response: ${error.message}`;
      }

      throw new Error(errorMessage);
    }
  }

  /**
   * Check if JSON response appears to be truncated
   */
  private isJsonTruncated(jsonText: string): boolean {
    const trimmed = jsonText.trim();

    // Check for incomplete JSON structure
    if (!trimmed.endsWith('}') && !trimmed.endsWith(']')) {
      return true;
    }

    // Count braces to detect incomplete nesting
    let braceCount = 0;
    let bracketCount = 0;

    for (const char of trimmed) {
      if (char === '{') braceCount++;
      else if (char === '}') braceCount--;
      else if (char === '[') bracketCount++;
      else if (char === ']') bracketCount--;
    }

    return braceCount !== 0 || bracketCount !== 0;
  }

  /**
   * Attempt to fix truncated JSON by closing incomplete structures
   */
  private attemptJsonFix(jsonText: string): string {
    let fixed = jsonText.trim();

    // Count unclosed braces and brackets
    let braceCount = 0;
    let bracketCount = 0;
    let inString = false;
    let escapeNext = false;

    for (const char of fixed) {
      if (escapeNext) {
        escapeNext = false;
        continue;
      }

      if (char === '\\') {
        escapeNext = true;
        continue;
      }

      if (char === '"') {
        inString = !inString;
        continue;
      }

      if (!inString) {
        if (char === '{') braceCount++;
        else if (char === '}') braceCount--;
        else if (char === '[') bracketCount++;
        else if (char === ']') bracketCount--;
      }
    }

    // Remove incomplete trailing content (likely cut off mid-property)
    if (fixed.endsWith(',') || fixed.endsWith(':') || fixed.endsWith('"')) {
      // Find the last complete property
      const lastCompleteIndex = this.findLastCompleteProperty(fixed);
      if (lastCompleteIndex > 0) {
        fixed = fixed.substring(0, lastCompleteIndex);
      }
    }

    // Close unclosed brackets first, then braces
    while (bracketCount > 0) {
      fixed += ']';
      bracketCount--;
    }

    while (braceCount > 0) {
      fixed += '}';
      braceCount--;
    }

    return fixed;
  }

  /**
   * Find the last complete property in a JSON string
   */
  private findLastCompleteProperty(jsonText: string): number {
    let lastCompleteIndex = -1;
    let braceDepth = 0;
    let inString = false;
    let escapeNext = false;

    for (let i = 0; i < jsonText.length; i++) {
      const char = jsonText[i];

      if (escapeNext) {
        escapeNext = false;
        continue;
      }

      if (char === '\\') {
        escapeNext = true;
        continue;
      }

      if (char === '"') {
        inString = !inString;
        continue;
      }

      if (!inString) {
        if (char === '{') {
          braceDepth++;
        } else if (char === '}') {
          braceDepth--;
          if (braceDepth === 1) { // Back to root level
            lastCompleteIndex = i + 1;
          }
        } else if (char === ',' && braceDepth === 1) {
          lastCompleteIndex = i;
        }
      }
    }

    return lastCompleteIndex;
  }
}