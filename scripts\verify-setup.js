#!/usr/bin/env node

/**
 * TravelViz Setup Verification Script
 * Verifies that the monorepo is properly configured and all packages work together
 */

const fs = require('fs');
const path = require('path');

console.log('🔍 TravelViz Setup Verification\n');

// Check if we're in the right directory
const packageJsonPath = path.join(process.cwd(), 'package.json');
if (!fs.existsSync(packageJsonPath)) {
  console.error('❌ Not in TravelViz root directory');
  process.exit(1);
}

const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
if (packageJson.name !== 'travelviz') {
  console.error('❌ Not in TravelViz root directory');
  process.exit(1);
}

console.log('✅ In TravelViz root directory');

// Check workspace configuration
const workspaceConfig = path.join(process.cwd(), 'pnpm-workspace.yaml');
if (fs.existsSync(workspaceConfig)) {
  console.log('✅ pnpm workspace configured');
} else {
  console.error('❌ pnpm workspace not configured');
}

// Check packages
const packages = ['hub', 'web', 'shared', 'mobile'];
const packagesDir = path.join(process.cwd(), 'packages');

packages.forEach(pkg => {
  const pkgPath = path.join(packagesDir, pkg);
  const pkgJsonPath = path.join(pkgPath, 'package.json');
  
  if (fs.existsSync(pkgJsonPath)) {
    const pkgJson = JSON.parse(fs.readFileSync(pkgJsonPath, 'utf8'));
    console.log(`✅ Package @travelviz/${pkg} exists (v${pkgJson.version})`);
  } else {
    console.error(`❌ Package @travelviz/${pkg} missing`);
  }
});

// Check shared package build
const sharedDist = path.join(packagesDir, 'shared', 'dist');
if (fs.existsSync(sharedDist)) {
  console.log('✅ Shared package built');
} else {
  console.warn('⚠️  Shared package not built - run: pnpm --filter @travelviz/shared build');
}

// Check environment files
const envFiles = [
  'packages/hub/.env',
  'packages/web/.env.local'
];

envFiles.forEach(envFile => {
  if (fs.existsSync(envFile)) {
    console.log(`✅ Environment file: ${envFile}`);
  } else {
    console.warn(`⚠️  Missing environment file: ${envFile}`);
  }
});

// Check TypeScript configuration
const tsConfig = path.join(process.cwd(), 'tsconfig.json');
if (fs.existsSync(tsConfig)) {
  console.log('✅ TypeScript configuration exists');
} else {
  console.error('❌ TypeScript configuration missing');
}

// Check documentation
const docsDir = path.join(process.cwd(), 'docs');
if (fs.existsSync(docsDir)) {
  console.log('✅ Documentation directory exists');
} else {
  console.warn('⚠️  Documentation directory missing');
}

console.log('\n🎯 Next Steps:');
console.log('1. Run: pnpm dev');
console.log('2. Check hub health: http://localhost:[hub-port]/health');
console.log('3. Check web app: http://localhost:3001');
console.log('4. Verify shared package imports work');

console.log('\n✨ Setup verification complete!');
