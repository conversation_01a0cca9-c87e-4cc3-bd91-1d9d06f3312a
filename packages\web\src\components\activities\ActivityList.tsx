'use client';

import React, { useState, useMemo } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { format } from 'date-fns';
import { 
  Search, 
  Filter, 
  MoreVertical, 
  Edit2, 
  Trash2,
  Calendar,
  Clock,
  MapPin,
  DollarSign,
  CheckSquare,
  Square
} from 'lucide-react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Checkbox } from '@/components/ui/checkbox';
import { Activity, ActivityType } from '@travelviz/shared';
import { cn } from '@/lib/utils';
import { 
  Plane, 
  Hotel, 
  Car, 
  Coffee, 
  ShoppingBag, 
  Activity as ActivityIcon 
} from 'lucide-react';

interface ActivityListProps {
  activities: Activity[];
  onActivityClick?: (activity: Activity) => void;
  onActivityUpdate?: (activityId: string, updates: Partial<Activity>) => void;
  onActivityDelete?: (activityId: string) => void;
  className?: string;
}

const activityIcons: Record<ActivityType, React.ReactNode> = {
  [ActivityType.flight]: <Plane className="w-4 h-4" />,
  [ActivityType.accommodation]: <Hotel className="w-4 h-4" />,
  [ActivityType.activity]: <MapPin className="w-4 h-4" />,
  [ActivityType.transport]: <Car className="w-4 h-4" />,
  [ActivityType.dining]: <Coffee className="w-4 h-4" />,
  [ActivityType.shopping]: <ShoppingBag className="w-4 h-4" />,
  [ActivityType.car_rental]: <Car className="w-4 h-4" />,
  [ActivityType.tour]: <ActivityIcon className="w-4 h-4" />,
  [ActivityType.sightseeing]: <ActivityIcon className="w-4 h-4" />,
  [ActivityType.entertainment]: <ActivityIcon className="w-4 h-4" />,
  [ActivityType.other]: <ActivityIcon className="w-4 h-4" />,
};

const activityColors: Record<ActivityType, string> = {
  [ActivityType.flight]: 'bg-blue-50 text-blue-700 border-blue-200',
  [ActivityType.accommodation]: 'bg-purple-50 text-purple-700 border-purple-200',
  [ActivityType.activity]: 'bg-green-50 text-green-700 border-green-200',
  [ActivityType.transport]: 'bg-orange-50 text-orange-700 border-orange-200',
  [ActivityType.dining]: 'bg-pink-50 text-pink-700 border-pink-200',
  [ActivityType.shopping]: 'bg-indigo-50 text-indigo-700 border-indigo-200',
  [ActivityType.car_rental]: 'bg-orange-50 text-orange-700 border-orange-200',
  [ActivityType.tour]: 'bg-green-50 text-green-700 border-green-200',
  [ActivityType.sightseeing]: 'bg-green-50 text-green-700 border-green-200',
  [ActivityType.entertainment]: 'bg-indigo-50 text-indigo-700 border-indigo-200',
  [ActivityType.other]: 'bg-gray-50 text-gray-700 border-gray-200',
};

export function ActivityList({
  activities,
  onActivityClick,
  onActivityUpdate,
  onActivityDelete,
  className,
}: ActivityListProps) {
  const [searchQuery, setSearchQuery] = useState('');
  const [filterType, setFilterType] = useState<ActivityType | 'all'>('all');
  const [selectedIds, setSelectedIds] = useState<Set<string>>(new Set());
  const [isSelectMode, setIsSelectMode] = useState(false);

  // Filter and search activities
  const filteredActivities = useMemo(() => {
    return activities.filter(activity => {
      // Type filter
      if (filterType !== 'all' && activity.type !== filterType) {
        return false;
      }

      // Search filter
      if (searchQuery) {
        const query = searchQuery.toLowerCase();
        return (
          activity.title.toLowerCase().includes(query) ||
          activity.description?.toLowerCase().includes(query) ||
          activity.location?.toLowerCase().includes(query)
        );
      }

      return true;
    });
  }, [activities, filterType, searchQuery]);

  const handleSelectAll = () => {
    if (selectedIds.size === filteredActivities.length) {
      setSelectedIds(new Set());
    } else {
      setSelectedIds(new Set(filteredActivities.map(a => a.id)));
    }
  };

  const handleSelectActivity = (activityId: string) => {
    const newSelected = new Set(selectedIds);
    if (newSelected.has(activityId)) {
      newSelected.delete(activityId);
    } else {
      newSelected.add(activityId);
    }
    setSelectedIds(newSelected);
  };

  const handleBulkDelete = () => {
    selectedIds.forEach(id => {
      onActivityDelete?.(id);
    });
    setSelectedIds(new Set());
    setIsSelectMode(false);
  };

  return (
    <div className={cn("flex flex-col h-full", className)}>
      {/* Header */}
      <div className="border-b bg-white px-4 py-3">
        <div className="flex items-center gap-4">
          {/* Search */}
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 -translate-y-1/2 w-4 h-4 text-gray-400" />
            <Input
              placeholder="Search activities..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-9"
            />
          </div>

          {/* Filter */}
          <Select value={filterType} onValueChange={(value) => setFilterType(value as ActivityType | 'all')}>
            <SelectTrigger className="w-40">
              <Filter className="w-4 h-4 mr-2" />
              <SelectValue placeholder="All types" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All types</SelectItem>
              <SelectItem value={ActivityType.flight}>Flights</SelectItem>
              <SelectItem value={ActivityType.accommodation}>Hotels</SelectItem>
              <SelectItem value={ActivityType.activity}>Activities</SelectItem>
              <SelectItem value={ActivityType.transport}>Transport</SelectItem>
              <SelectItem value={ActivityType.dining}>Food</SelectItem>
              <SelectItem value={ActivityType.shopping}>Shopping</SelectItem>
              <SelectItem value={ActivityType.other}>Other</SelectItem>
            </SelectContent>
          </Select>

          {/* Bulk Actions */}
          {isSelectMode ? (
            <>
              <Button
                variant="ghost"
                size="sm"
                onClick={handleSelectAll}
              >
                {selectedIds.size === filteredActivities.length ? (
                  <>
                    <CheckSquare className="w-4 h-4 mr-1" />
                    Deselect All
                  </>
                ) : (
                  <>
                    <Square className="w-4 h-4 mr-1" />
                    Select All
                  </>
                )}
              </Button>
              {selectedIds.size > 0 && (
                <Button
                  variant="destructive"
                  size="sm"
                  onClick={handleBulkDelete}
                >
                  Delete ({selectedIds.size})
                </Button>
              )}
              <Button
                variant="ghost"
                size="sm"
                onClick={() => {
                  setIsSelectMode(false);
                  setSelectedIds(new Set());
                }}
              >
                Cancel
              </Button>
            </>
          ) : (
            <Button
              variant="outline"
              size="sm"
              onClick={() => setIsSelectMode(true)}
            >
              Select
            </Button>
          )}
        </div>

        {/* Results count */}
        <div className="mt-2 text-sm text-gray-500">
          {filteredActivities.length} activities
          {searchQuery && ` matching "${searchQuery}"`}
        </div>
      </div>

      {/* Activity Cards */}
      <div className="flex-1 overflow-auto p-4 space-y-3">
        <AnimatePresence>
          {filteredActivities.map((activity, index) => (
            <motion.div
              key={activity.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ delay: index * 0.05 }}
              className={cn(
                "relative p-4 rounded-lg border transition-all cursor-pointer hover:shadow-md",
                activityColors[activity.type],
                selectedIds.has(activity.id) && "ring-2 ring-blue-500"
              )}
              onClick={() => {
                if (isSelectMode) {
                  handleSelectActivity(activity.id);
                } else {
                  onActivityClick?.(activity);
                }
              }}
            >
              <div className="flex items-start gap-3">
                {/* Checkbox in select mode */}
                {isSelectMode && (
                  <Checkbox
                    checked={selectedIds.has(activity.id)}
                    onCheckedChange={() => handleSelectActivity(activity.id)}
                    onClick={(e) => e.stopPropagation()}
                  />
                )}

                {/* Icon */}
                <div className="flex-shrink-0 w-8 h-8 rounded-full bg-white/80 flex items-center justify-center">
                  {activityIcons[activity.type]}
                </div>

                {/* Content */}
                <div className="flex-1 min-w-0">
                  <div className="flex items-start justify-between">
                    <h3 className="font-medium text-gray-900">{activity.title}</h3>
                    
                    {!isSelectMode && (
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button
                            variant="ghost"
                            size="icon"
                            className="h-8 w-8"
                            onClick={(e) => e.stopPropagation()}
                          >
                            <MoreVertical className="w-4 h-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem onClick={() => onActivityClick?.(activity)}>
                            <Edit2 className="w-4 h-4 mr-2" />
                            Edit
                          </DropdownMenuItem>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem 
                            className="text-red-600"
                            onClick={() => onActivityDelete?.(activity.id)}
                          >
                            <Trash2 className="w-4 h-4 mr-2" />
                            Delete
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    )}
                  </div>

                  {activity.description && (
                    <p className="text-sm text-gray-600 mt-1 line-clamp-2">
                      {activity.description}
                    </p>
                  )}

                  <div className="flex flex-wrap items-center gap-3 mt-2 text-xs text-gray-500">
                    {activity.start_time && (
                      <div className="flex items-center gap-1">
                        <Calendar className="w-3 h-3" />
                        <span>{format(new Date(activity.start_time), 'MMM d')}</span>
                      </div>
                    )}
                    
                    {activity.start_time && (
                      <div className="flex items-center gap-1">
                        <Clock className="w-3 h-3" />
                        <span>{format(new Date(activity.start_time), 'HH:mm')}</span>
                      </div>
                    )}
                    
                    {activity.location && (
                      <div className="flex items-center gap-1 truncate max-w-[200px]">
                        <MapPin className="w-3 h-3" />
                        <span className="truncate">{activity.location}</span>
                      </div>
                    )}
                    
                    {activity.price && (
                      <div className="flex items-center gap-1 font-medium">
                        <DollarSign className="w-3 h-3" />
                        <span>
                          {activity.price} {activity.currency || 'USD'}
                        </span>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </motion.div>
          ))}
        </AnimatePresence>

        {filteredActivities.length === 0 && (
          <div className="text-center py-12">
            <p className="text-gray-500">
              {searchQuery 
                ? `No activities found matching "${searchQuery}"`
                : 'No activities found'
              }
            </p>
          </div>
        )}
      </div>
    </div>
  );
}