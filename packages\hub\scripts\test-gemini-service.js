#!/usr/bin/env node

/**
 * Test Gemini Service Integration
 * Tests the actual GeminiService class with travel parsing
 */

require('dotenv').config({ path: '.env.local' });

// Simple test without complex dependencies
async function testGeminiService() {
  console.log('🧪 Testing Gemini Service Integration');
  console.log('=' .repeat(50));
  
  const { GeminiService } = require('../dist/services/gemini.service.js');
  
  try {
    const geminiService = GeminiService.getInstance();
    
    if (!geminiService.isAvailable()) {
      console.error('❌ Gemini service not available - check API key');
      process.exit(1);
    }
    
    console.log('✅ Gemini service initialized');
    
    // Test travel content parsing
    const testContent = `
      Day 1: Paris
      - 10:00 AM: Visit Eiffel Tower
      - 1:00 PM: Lunch at Café de Flore  
      - 3:00 PM: Louvre Museum
      - 7:00 PM: Seine River cruise
    `;
    
    const prompt = `Parse this travel itinerary and return a JSON object with title, activities array. Each activity should have: title, type, startTime, location, day. Return only valid JSON.`;
    
    console.log('🔄 Testing travel content parsing...');
    console.log(`📝 Content: ${testContent.trim()}`);
    
    const startTime = Date.now();
    const result = await geminiService.parseWithGemini(testContent, prompt);
    const duration = Date.now() - startTime;
    
    console.log(`✅ Parsing successful! (${duration}ms)`);
    console.log('📊 Result:', JSON.stringify(result, null, 2));
    
    // Validate result structure
    if (result && result.activities && Array.isArray(result.activities)) {
      console.log(`✅ Found ${result.activities.length} activities`);
      console.log('✅ Result structure is valid');
    } else {
      console.log('⚠️  Result structure may be incomplete');
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    
    if (error.message.includes('rate limit')) {
      console.log('💡 Rate limit hit - this is expected on free tier');
      console.log('   Try again in a few minutes or upgrade to paid tier');
    } else if (error.message.includes('503')) {
      console.log('💡 Service unavailable - Google servers may be overloaded');
      console.log('   This was the original issue - try again later');
    }
  }
}

// Build the service first, then test
const { exec } = require('child_process');

console.log('🔨 Building TypeScript files...');
exec('npx tsc', (error, stdout, stderr) => {
  if (error) {
    console.error('❌ Build failed:', error.message);
    process.exit(1);
  }
  
  if (stderr) {
    console.log('⚠️  Build warnings:', stderr);
  }
  
  console.log('✅ Build complete');
  testGeminiService().catch(console.error);
});
