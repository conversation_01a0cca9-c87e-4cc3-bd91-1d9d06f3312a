/**
 * URL validation utilities to prevent XSS attacks
 */

const DANGEROUS_PROTOCOLS = [
  'javascript:',
  'data:',
  'vbscript:',
  'file:',
  'about:',
  'chrome:',
  'chrome-extension:',
];

// Common safe protocols (currently not used but kept for reference)
// const SAFE_PROTOCOLS = ['http:', 'https:', 'mailto:', 'tel:'];

/**
 * Validates and sanitizes a URL to prevent XSS attacks
 * @param url - The URL to validate
 * @param allowedProtocols - List of allowed protocols (defaults to http/https)
 * @returns Sanitized URL or null if invalid
 */
export function sanitizeUrl(
  url: string | null | undefined,
  allowedProtocols: string[] = ['http:', 'https:']
): string | null {
  if (!url) return null;

  try {
    // Trim whitespace
    const trimmedUrl = url.trim();
    if (!trimmedUrl) return null;

    // Parse the URL
    const parsedUrl = new URL(trimmedUrl);
    
    // Check if protocol is allowed
    if (!allowedProtocols.includes(parsedUrl.protocol)) {
      // Blocked potentially dangerous URL
      return null;
    }

    // Additional check for dangerous protocols
    const lowerUrl = trimmedUrl.toLowerCase();
    for (const dangerousProtocol of DANGEROUS_PROTOCOLS) {
      if (lowerUrl.startsWith(dangerousProtocol)) {
        // Blocked dangerous protocol
        return null;
      }
    }

    // Return the normalized URL
    return parsedUrl.toString();
  } catch (error) {
    // If URL parsing fails, check if it's a relative URL
    if (url.startsWith('/') || url.startsWith('./') || url.startsWith('../')) {
      // Relative URLs are generally safe
      return url;
    }
    
    // Invalid URL
    // Invalid URL format
    return null;
  }
}

/**
 * Checks if a URL is safe for use in an anchor tag
 * @param url - The URL to check
 * @returns boolean indicating if the URL is safe
 */
export function isSafeUrl(url: string | null | undefined): boolean {
  return sanitizeUrl(url) !== null;
}

/**
 * Gets a safe URL for use in anchor tags, with fallback
 * @param url - The URL to sanitize
 * @param fallback - Fallback URL if the original is unsafe (default: '#')
 * @returns Safe URL or fallback
 */
export function getSafeUrl(
  url: string | null | undefined,
  fallback: string = '#'
): string {
  const sanitized = sanitizeUrl(url);
  return sanitized || fallback;
}