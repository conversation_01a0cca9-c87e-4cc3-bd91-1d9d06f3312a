import { describe, it, expect, beforeAll, afterAll, beforeEach, vi } from 'vitest';
import request from 'supertest';
import { createServer } from '../../src/server';
import { createSupabaseClient } from '../../src/config/supabase';
import { redis } from '../../src/config/redis';
import FormData from 'form-data';
import fs from 'fs';
import path from 'path';

// Skip these tests in CI
const isCI = process.env.CI === 'true';
const describeIntegration = isCI ? describe.skip : describe;

// Test credentials
const TEST_USER_EMAIL = '<EMAIL>';
const TEST_USER_PASSWORD = 'TestPassword123!';
const API_TIMEOUT = 60000; // 60 seconds for AI operations

// Test conversations
const TEST_CONVERSATIONS = {
  tokyo: {
    content: `User: I'm planning a 5-day trip to Tokyo from March 15-20, 2024. Can you help me create an itinerary?

AI: I'd be happy to help you create a 5-day Tokyo itinerary! Here's a comprehensive plan:

Day 1 - March 15: Arrival & Shinjuku
- Morning: Arrive at Narita Airport, take Narita Express to Tokyo
- 2:00 PM: Check into hotel in Shinjuku
- 3:00 PM: Explore Shinjuku Gyoen National Garden (¥500)
- 6:00 PM: Dinner at Omoide Yokocho
- 8:00 PM: Tokyo Metropolitan Building observation deck (free)

Day 2 - March 16: Traditional Tokyo
- 9:00 AM: Senso-ji Temple in Asakusa
- 11:00 AM: Nakamise Shopping Street
- 1:00 PM: Lunch - tempura in Asakusa
- 3:00 PM: Tokyo Skytree (¥3,100)
- 7:00 PM: Dinner in Ginza

Day 3 - March 17: Pop Culture
- 10:00 AM: Meiji Shrine
- 12:00 PM: Takeshita Street in Harajuku
- 2:00 PM: Lunch - crepes in Harajuku
- 4:00 PM: Shibuya Crossing
- 6:00 PM: Conveyor belt sushi dinner

Day 4 - March 18: Day Trip
- Full day trip to Mount Fuji and Hakone

Day 5 - March 19: Modern Tokyo
- 10:00 AM: teamLab Borderless (¥3,200)
- 1:00 PM: Lunch in Odaiba
- 3:00 PM: Akihabara shopping
- 6:00 PM: Final dinner in Roppongi`,
    source: 'chatgpt'
  },
  barcelona: {
    content: `Human: Plan a weekend trip to Barcelona. Friday evening to Sunday night.

Assistant: Perfect! Here's your Barcelona weekend itinerary:

Friday Evening:
- 7:00 PM: Check into hotel (Gothic Quarter recommended)
- 8:30 PM: Walk down Las Ramblas
- 9:30 PM: Tapas dinner in Gothic Quarter
- 11:00 PM: Evening stroll through Gothic streets

Saturday:
- 9:00 AM: Sagrada Familia (book ahead - €26)
- 11:00 AM: Park Güell (€10)
- 1:00 PM: Lunch in Gràcia
- 3:00 PM: Casa Batlló & La Pedrera
- 5:00 PM: Gothic Cathedral
- 7:00 PM: Cable car to Montjuïc
- 9:00 PM: Paella at Barceloneta beach

Sunday:
- 10:00 AM: Brunch
- 11:30 AM: Boqueria Market
- 1:00 PM: El Born neighborhood
- 3:00 PM: Ciutadella Park
- 5:00 PM: Arc de Triomf
- 6:00 PM: Shopping
- 8:00 PM: Airport`,
    source: 'claude'
  },
  rome: {
    content: `Create a 3-day Rome itinerary for next month.

Day 1: Ancient Rome
* 9:00 AM: Colosseum (€16 + €2 booking)
* 11:00 AM: Roman Forum and Palatine Hill
* 1:00 PM: Lunch near Forum
* 3:00 PM: Pantheon (free)
* 5:00 PM: Trevi Fountain
* 7:00 PM: Spanish Steps
* 8:00 PM: Dinner in Trastevere

Day 2: Vatican City
* 8:00 AM: Vatican Museums (€17)
* 10:00 AM: Sistine Chapel
* 11:30 AM: St. Peter's Basilica
* 1:00 PM: Climb the dome (€10)
* 3:00 PM: Lunch in Borgo Pio
* 5:00 PM: Castel Sant'Angelo (€15)
* 8:00 PM: Dinner near Piazza Navona

Day 3: Hidden Gems
* 9:00 AM: Borghese Gallery (€15)
* 12:00 PM: Villa Borghese Gardens
* 2:00 PM: Campo de' Fiori market
* 4:00 PM: Trastevere exploration
* 6:00 PM: Gianicolo Hill sunset
* 8:00 PM: Jewish Ghetto dinner`,
    source: 'gemini'
  }
};

// Helper functions
async function getAuthToken(): Promise<string> {
  const supabase = createSupabaseClient();
  const { data, error } = await supabase.auth.signInWithPassword({
    email: TEST_USER_EMAIL,
    password: TEST_USER_PASSWORD
  });

  if (error) throw new Error(`Auth failed: ${error.message}`);
  return data.session?.access_token || '';
}

async function waitForParseCompletion(
  server: any,
  importId: string,
  authToken: string,
  maxWaitTime: number = 30000
): Promise<any> {
  const startTime = Date.now();
  
  while (Date.now() - startTime < maxWaitTime) {
    const response = await request(server)
      .get(`/api/v1/import/parse-simple/${importId}`)
      .set('Authorization', `Bearer ${authToken}`);

    if (response.body.data.status === 'complete') {
      return response.body.data.result;
    } else if (response.body.data.status === 'error') {
      throw new Error(response.body.data.error || 'Parse failed');
    }

    await new Promise(resolve => setTimeout(resolve, 1000));
  }
  
  throw new Error('Parse timeout');
}

describeIntegration('Comprehensive AI Import Tests', () => {
  let authToken: string;
  let server: any;
  let app: any;

  beforeAll(async () => {
    authToken = await getAuthToken();
    app = createServer();
    server = app.listen(0);
  }, API_TIMEOUT);

  afterAll(async () => {
    server?.close();
  });

  beforeEach(async () => {
    await redis.flushdb();
  });

  describe('AI Conversation Parsing', () => {
    it('should parse ChatGPT Tokyo conversation correctly', async () => {
      const response = await request(server)
        .post('/api/v1/import/parse-simple')
        .set('Authorization', `Bearer ${authToken}`)
        .send(TEST_CONVERSATIONS.tokyo)
        .expect(200);

      expect(response.body.success).toBe(true);
      const importId = response.body.data.importId;

      const result = await waitForParseCompletion(server, importId, authToken);
      
      expect(result).toMatchObject({
        title: expect.stringContaining('Tokyo'),
        startDate: '2024-03-15',
        endDate: '2024-03-20',
        destination: 'Tokyo',
        activities: expect.arrayContaining([
          expect.objectContaining({
            title: expect.any(String),
            type: expect.stringMatching(/^(flight|accommodation|activity|transport|dining|shopping|other)$/),
            startTime: expect.any(String),
            dayNumber: expect.any(Number)
          })
        ])
      });

      expect(result.activities.length).toBeGreaterThan(10);
      expect(result.metadata.confidence).toBeGreaterThan(0.7);
    }, API_TIMEOUT);

    it('should parse Claude Barcelona conversation correctly', async () => {
      const response = await request(server)
        .post('/api/v1/import/parse-simple')
        .set('Authorization', `Bearer ${authToken}`)
        .send(TEST_CONVERSATIONS.barcelona)
        .expect(200);

      const importId = response.body.data.importId;
      const result = await waitForParseCompletion(server, importId, authToken);

      expect(result.title).toContain('Barcelona');
      expect(result.activities).toHaveLength(expect.any(Number));
      
      // Verify specific Barcelona activities
      const sagradaActivity = result.activities.find((a: any) => 
        a.title.includes('Sagrada Familia')
      );
      expect(sagradaActivity).toBeDefined();
      expect(sagradaActivity.price).toBe(26);
      expect(sagradaActivity.currency).toBe('EUR');
    }, API_TIMEOUT);

    it('should handle different conversation formats', async () => {
      const conversations = Object.values(TEST_CONVERSATIONS);
      
      const results = await Promise.all(
        conversations.map(async (conv) => {
          const response = await request(server)
            .post('/api/v1/import/parse-simple')
            .set('Authorization', `Bearer ${authToken}`)
            .send(conv);
          
          return response.body.data.importId;
        })
      );

      expect(results).toHaveLength(3);
      results.forEach(id => expect(id).toBeDefined());
    });
  });

  describe('Trip Creation from Parse', () => {
    it('should create trip with all activities', async () => {
      // First parse
      const parseResponse = await request(server)
        .post('/api/v1/import/parse-simple')
        .set('Authorization', `Bearer ${authToken}`)
        .send(TEST_CONVERSATIONS.rome)
        .expect(200);

      const importId = parseResponse.body.data.importId;
      await waitForParseCompletion(server, importId, authToken);

      // Then create trip
      const createResponse = await request(server)
        .post(`/api/v1/import/parse-simple/${importId}/create-trip`)
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          edits: {
            title: 'My Rome Adventure',
            description: 'A perfect 3-day Rome itinerary'
          }
        })
        .expect(200);

      expect(createResponse.body.success).toBe(true);
      const tripId = createResponse.body.data.tripId;

      // Verify trip details
      const tripResponse = await request(server)
        .get(`/api/v1/trips/${tripId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      const trip = tripResponse.body.data;
      expect(trip.title).toBe('My Rome Adventure');
      expect(trip.activities).toBeInstanceOf(Array);
      expect(trip.activities.some((a: any) => a.title.includes('Colosseum'))).toBe(true);
      expect(trip.activities.some((a: any) => a.title.includes('Vatican'))).toBe(true);
    }, API_TIMEOUT);
  });

  describe('Error Handling', () => {
    it('should reject content that is too short', async () => {
      const response = await request(server)
        .post('/api/v1/import/parse-simple')
        .set('Authorization', `Bearer ${authToken}`)
        .send({ content: 'too short', source: 'test' })
        .expect(400);

      expect(response.body.error).toContain('too short');
    });

    it('should handle invalid import IDs', async () => {
      const response = await request(server)
        .get('/api/v1/import/parse-simple/invalid-id')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(404);

      expect(response.body.error).toContain('not found');
    });

    it('should prevent duplicate trip creation', async () => {
      const parseResponse = await request(server)
        .post('/api/v1/import/parse-simple')
        .set('Authorization', `Bearer ${authToken}`)
        .send(TEST_CONVERSATIONS.barcelona)
        .expect(200);

      const importId = parseResponse.body.data.importId;
      await waitForParseCompletion(server, importId, authToken);

      // Create trip first time
      await request(server)
        .post(`/api/v1/import/parse-simple/${importId}/create-trip`)
        .set('Authorization', `Bearer ${authToken}`)
        .send({ edits: {} })
        .expect(200);

      // Try to create again
      const duplicateResponse = await request(server)
        .post(`/api/v1/import/parse-simple/${importId}/create-trip`)
        .set('Authorization', `Bearer ${authToken}`)
        .send({ edits: {} })
        .expect(400);

      expect(duplicateResponse.body.error).toContain('already created');
    }, API_TIMEOUT);
  });

  describe('Performance Tests', () => {
    it('should handle concurrent parse requests', async () => {
      const requests = Object.values(TEST_CONVERSATIONS).map(conv =>
        request(server)
          .post('/api/v1/import/parse-simple')
          .set('Authorization', `Bearer ${authToken}`)
          .send(conv)
      );

      const responses = await Promise.all(requests);
      responses.forEach(response => {
        expect(response.status).toBe(200);
        expect(response.body.data.importId).toBeDefined();
      });
    });

    it('should complete parsing within reasonable time', async () => {
      const startTime = Date.now();
      
      const response = await request(server)
        .post('/api/v1/import/parse-simple')
        .set('Authorization', `Bearer ${authToken}`)
        .send(TEST_CONVERSATIONS.tokyo)
        .expect(200);

      const importId = response.body.data.importId;
      await waitForParseCompletion(server, importId, authToken);
      
      const duration = Date.now() - startTime;
      expect(duration).toBeLessThan(30000); // Should complete within 30 seconds
    }, API_TIMEOUT);
  });

  describe('SSE Progress Streaming', () => {
    it('should stream progress updates', async () => {
      const response = await request(server)
        .post('/api/v1/import/parse')
        .set('Authorization', `Bearer ${authToken}`)
        .send(TEST_CONVERSATIONS.barcelona)
        .expect(200);

      const importId = response.body.data.importId;
      
      // Monitor SSE progress
      const progressUrl = `http://localhost:${server.address().port}/api/v1/import/${importId}/progress`;
      const events: any[] = [];
      
      // Note: EventSource implementation would go here
      // For now, we'll just verify the endpoint exists
      const progressResponse = await request(server)
        .get(`/api/v1/import/${importId}/progress`)
        .set('Authorization', `Bearer ${authToken}`)
        .set('Accept', 'text/event-stream')
        .expect(200);

      expect(progressResponse.headers['content-type']).toContain('text/event-stream');
    });
  });
});

describeIntegration('PDF Import Tests', () => {
  let authToken: string;
  let server: any;
  let app: any;

  beforeAll(async () => {
    authToken = await getAuthToken();
    app = createServer();
    server = app.listen(0);
  }, API_TIMEOUT);

  afterAll(async () => {
    server?.close();
  });

  describe('PDF Upload and Parsing', () => {
    it('should reject non-PDF files', async () => {
      const form = new FormData();
      form.append('file', Buffer.from('not a pdf'), {
        filename: 'test.txt',
        contentType: 'text/plain'
      });
      form.append('source', 'pdf');

      const response = await request(server)
        .post('/api/v1/import/upload')
        .set('Authorization', `Bearer ${authToken}`)
        .type('multipart/form-data')
        .field('source', 'pdf')
        .attach('file', Buffer.from('not a pdf'), 'test.txt')
        .expect(400);

      expect(response.body.error).toBeDefined();
    });

    it('should enforce file size limits', async () => {
      const largeBuffer = Buffer.alloc(11 * 1024 * 1024); // 11MB
      
      const response = await request(server)
        .post('/api/v1/import/upload')
        .set('Authorization', `Bearer ${authToken}`)
        .type('multipart/form-data')
        .field('source', 'pdf')
        .attach('file', largeBuffer, 'large.pdf')
        .expect(413);
    });
  });
});

describeIntegration('End-to-End Import Flow', () => {
  let authToken: string;
  let server: any;
  let app: any;

  beforeAll(async () => {
    authToken = await getAuthToken();
    app = createServer();
    server = app.listen(0);
  }, API_TIMEOUT);

  afterAll(async () => {
    server?.close();
  });

  it('should complete full flow from conversation to viewable trip', async () => {
    // Step 1: Parse conversation
    const parseResponse = await request(server)
      .post('/api/v1/import/parse-simple')
      .set('Authorization', `Bearer ${authToken}`)
      .send(TEST_CONVERSATIONS.tokyo)
      .expect(200);

    const importId = parseResponse.body.data.importId;

    // Step 2: Wait for completion
    const parseResult = await waitForParseCompletion(server, importId, authToken);
    expect(parseResult.activities).toHaveLength(expect.any(Number));

    // Step 3: Create trip
    const createResponse = await request(server)
      .post(`/api/v1/import/parse-simple/${importId}/create-trip`)
      .set('Authorization', `Bearer ${authToken}`)
      .send({ edits: {} })
      .expect(200);

    const tripId = createResponse.body.data.tripId;

    // Step 4: Verify trip is viewable
    const tripResponse = await request(server)
      .get(`/api/v1/trips/${tripId}`)
      .set('Authorization', `Bearer ${authToken}`)
      .expect(200);

    const trip = tripResponse.body.data;
    
    // Verify trip structure
    expect(trip).toMatchObject({
      id: tripId,
      title: expect.stringContaining('Tokyo'),
      start_date: expect.any(String),
      end_date: expect.any(String),
      activities: expect.arrayContaining([
        expect.objectContaining({
          id: expect.any(String),
          title: expect.any(String),
          type: expect.any(String),
          start_time: expect.any(String)
        })
      ])
    });

    // Verify activities are properly structured for display
    trip.activities.forEach((activity: any) => {
      expect(activity.position).toBeDefined();
      expect(activity.day_number).toBeDefined();
      if (activity.location) {
        expect(activity.location).toHaveProperty('address');
      }
    });
  }, API_TIMEOUT * 2);
});