"use client";

import { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Play, MapPin, Calendar, Clock, DollarSign, ChevronRight, Copy, Sparkles, ArrowRight, FileText, Bot, Zap, Bell, TrendingDown, Users } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';

const demoSteps = [
  {
    id: 1,
    title: "Paste AI Conversation",
    description: "Copy any travel plan from ChatGPT, Claude, or your favorite AI tool",
    icon: FileText,
    color: "from-blue-500 to-blue-600"
  },
  {
    id: 2,
    title: "AI Magic Processing",
    description: "Our AI instantly extracts locations, times, activities, and costs",
    icon: Bot,
    color: "from-purple-500 to-purple-600"
  },
  {
    id: 3,
    title: "Visual Timeline",
    description: "See your trip organized in a beautiful, interactive timeline with budget tracking",
    icon: Calendar,
    color: "from-green-500 to-green-600"
  },
  {
    id: 4,
    title: "Smart Money Features",
    description: "Automatic price alerts, budget tracking, and cost sharing with your group",
    icon: DollarSign,
    color: "from-orange-500 to-orange-600"
  }
];

const chatGPTText = `Plan a 5-day Tokyo trip for 2 people, budget $2000:

Day 1: Arrival & Shibuya
- 2:00 PM: Arrive at Haneda Airport ($0)
- 4:00 PM: Check into Shibuya hotel ($120/night)
- 6:00 PM: Visit Shibuya Crossing ($0)
- 8:00 PM: Dinner at traditional izakaya ($45/person)

Day 2: Traditional Tokyo
- 9:00 AM: Senso-ji Temple in Asakusa ($5 entrance)
- 12:00 PM: Lunch at Tsukiji Market ($35/person)
- 3:00 PM: Imperial Palace Gardens ($0)
- 7:00 PM: Dinner in Ginza district ($80/person)

Day 3: Modern Tokyo & Shopping
- 10:00 AM: TeamLab Borderless ($32/person)
- 1:00 PM: Lunch in Harajuku ($25/person)
- 3:00 PM: Meiji Shrine ($0)
- 6:00 PM: Tokyo Skytree observation deck ($18/person)
- 8:00 PM: Dinner with city views ($65/person)`;

const extractedData = {
  title: "5 Days in Tokyo",
  destination: "Tokyo, Japan",
  duration: 5,
  totalActivities: 15,
  estimatedCost: 1847,
  savings: 153,
  days: [
    {
      day: 1,
      date: "2024-04-15",
      theme: "Arrival & Shibuya",
      dailyCost: 285,
      items: [
        {
          time: "14:00",
          title: "Arrive at Haneda Airport",
          location: "Haneda Airport",
          category: "transport",
          cost: 0,
          priceAlert: false
        },
        {
          time: "16:00",
          title: "Check into Shibuya hotel",
          location: "Shibuya District",
          category: "accommodation",
          cost: 120,
          priceAlert: true,
          savings: 15
        },
        {
          time: "18:00",
          title: "Visit Shibuya Crossing",
          location: "Shibuya Crossing",
          category: "activity",
          cost: 0,
          priceAlert: false
        },
        {
          time: "20:00",
          title: "Dinner at traditional izakaya",
          location: "Shibuya Izakaya",
          category: "restaurant",
          cost: 90,
          priceAlert: false
        }
      ]
    }
  ]
};

export function DemoSection() {
  const [activeStep, setActiveStep] = useState(1);
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentView, setCurrentView] = useState('input'); // 'input', 'processing', 'timeline', 'money'

  const startDemo = () => {
    setIsPlaying(true);
    setCurrentView('input');
    
    // Simulate the transformation process
    setTimeout(() => setCurrentView('processing'), 1000);
    setTimeout(() => setCurrentView('timeline'), 3000);
    setTimeout(() => setCurrentView('money'), 5000);
    setTimeout(() => {
      setIsPlaying(false);
      setCurrentView('input');
    }, 8000);
  };

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'activity': return 'bg-blue-500';
      case 'restaurant': return 'bg-red-500';
      case 'accommodation': return 'bg-green-500';
      case 'transport': return 'bg-purple-500';
      default: return 'bg-gray-500';
    }
  };

  return (
    <section className="py-20 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
          className="text-center mb-16"
        >
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            From AI Conversation to Money-Saving Trip in 30 Seconds
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto mb-8">
            Watch how a simple ChatGPT travel plan transforms into a budget-tracking, price-alerting, shareable masterpiece
          </p>
          
          <Button
            onClick={startDemo}
            disabled={isPlaying}
            className="inline-flex items-center px-8 py-4 bg-orange-500 text-white rounded-xl hover:bg-orange-600 transition-colors text-lg font-semibold shadow-lg hover:shadow-xl"
          >
            <Play className="h-6 w-6 mr-3" />
            {isPlaying ? 'Watch the Magic Happen...' : 'See Live Demo'}
          </Button>
        </motion.div>

        {/* Process Steps */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-12">
          {demoSteps.map((step, index) => (
            <motion.div
              key={step.id}
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ delay: index * 0.1, duration: 0.6 }}
              className={`text-center cursor-pointer transition-all duration-300 ${
                activeStep === step.id || isPlaying ? 'scale-105' : 'hover:scale-102'
              }`}
              onClick={() => !isPlaying && setActiveStep(step.id)}
            >
              <div className={`w-16 h-16 mx-auto mb-4 rounded-full flex items-center justify-center text-white font-bold text-xl transition-all duration-300 bg-gradient-to-r ${step.color} ${
                activeStep === step.id || (isPlaying && index + 1 <= getCurrentStepIndex()) 
                  ? 'shadow-lg scale-110' 
                  : 'opacity-60'
              }`}>
                <step.icon className="h-8 w-8" />
              </div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                {step.title}
              </h3>
              <p className="text-gray-600 text-sm">
                {step.description}
              </p>
              {index < demoSteps.length - 1 && (
                <ChevronRight className="h-5 w-5 text-gray-400 mx-auto mt-4 hidden md:block" />
              )}
            </motion.div>
          ))}
        </div>

        {/* Demo Container */}
        <div className="bg-gray-50 rounded-2xl p-8 overflow-hidden">
          <AnimatePresence mode="wait">
            {/* Input View */}
            {currentView === 'input' && (
              <motion.div
                key="input"
                initial={{ opacity: 0, x: -50 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: 50 }}
                transition={{ duration: 0.5 }}
                className="grid grid-cols-1 lg:grid-cols-2 gap-8"
              >
                <div>
                  <div className="flex items-center mb-4">
                    <div className="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center mr-3">
                      <Bot className="h-5 w-5 text-white" />
                    </div>
                    <h3 className="text-xl font-semibold text-gray-900">ChatGPT Travel Plan</h3>
                  </div>
                  <Card className="p-6 bg-white border-2 border-dashed border-gray-300">
                    <pre className="text-sm text-gray-700 whitespace-pre-wrap font-mono leading-relaxed">
                      {chatGPTText}
                    </pre>
                    <div className="mt-4 flex items-center justify-between">
                      <span className="text-xs text-gray-500">Raw AI output with budget info</span>
                      <Button size="sm" variant="outline">
                        <Copy className="h-4 w-4 mr-2" />
                        Copy Text
                      </Button>
                    </div>
                  </Card>
                </div>

                <div className="flex items-center justify-center">
                  <div className="text-center">
                    <div className="w-24 h-24 bg-orange-100 rounded-full flex items-center justify-center mx-auto mb-4">
                      <ArrowRight className="h-12 w-12 text-orange-500" />
                    </div>
                    <h3 className="text-xl font-semibold text-gray-900 mb-2">
                      Paste into TravelViz
                    </h3>
                    <p className="text-gray-600">
                      Simply paste your AI conversation and watch it transform into a money-saving travel plan
                    </p>
                  </div>
                </div>
              </motion.div>
            )}

            {/* Processing View */}
            {currentView === 'processing' && (
              <motion.div
                key="processing"
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                exit={{ opacity: 0, scale: 1.1 }}
                transition={{ duration: 0.5 }}
                className="text-center py-16"
              >
                <div className="w-24 h-24 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-6">
                  <Sparkles className="h-12 w-12 text-purple-500 animate-pulse" />
                </div>
                <h3 className="text-2xl font-semibold text-gray-900 mb-4">
                  AI is Analyzing Your Trip
                </h3>
                <p className="text-gray-600 mb-8 max-w-2xl mx-auto">
                  Our intelligent system is extracting locations, parsing costs, setting up price alerts, 
                  and preparing your money-saving travel experience...
                </p>
                <div className="max-w-md mx-auto">
                  <div className="bg-gray-200 rounded-full h-3">
                    <motion.div 
                      className="bg-purple-500 h-3 rounded-full"
                      initial={{ width: "0%" }}
                      animate={{ width: "100%" }}
                      transition={{ duration: 2, ease: "easeInOut" }}
                    />
                  </div>
                  <div className="flex justify-between text-xs text-gray-500 mt-2">
                    <span>Extracting costs...</span>
                    <span>Setting price alerts...</span>
                    <span>Creating timeline...</span>
                  </div>
                </div>
              </motion.div>
            )}

            {/* Timeline View */}
            {currentView === 'timeline' && (
              <motion.div
                key="timeline"
                initial={{ opacity: 0, y: 50 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -50 }}
                transition={{ duration: 0.5 }}
              >
                <div className="text-center mb-8">
                  <h3 className="text-2xl font-semibold text-gray-900 mb-2">
                    Beautiful Visual Timeline with Budget Tracking
                  </h3>
                  <p className="text-gray-600">
                    Your messy text is now organized with automatic cost tracking and price alerts
                  </p>
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                  {/* Trip Overview */}
                  <Card className="p-6 bg-white">
                    <h4 className="font-semibold text-gray-900 mb-4">Trip Overview</h4>
                    <div className="space-y-3">
                      <div className="flex justify-between">
                        <span className="text-gray-600">Duration</span>
                        <span className="font-medium">{extractedData.duration} days</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Activities</span>
                        <span className="font-medium">{extractedData.totalActivities}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Est. Cost</span>
                        <span className="font-medium">${extractedData.estimatedCost}</span>
                      </div>
                      <div className="flex justify-between text-green-600">
                        <span>Potential Savings</span>
                        <span className="font-bold">-${extractedData.savings}</span>
                      </div>
                    </div>
                  </Card>

                  {/* Timeline */}
                  <div className="lg:col-span-2">
                    <Card className="p-6 bg-white">
                      <h4 className="font-semibold text-gray-900 mb-4">Day 1: Arrival & Shibuya</h4>
                      <div className="space-y-3">
                        {extractedData.days[0].items.map((item, index) => (
                          <motion.div
                            key={index}
                            initial={{ opacity: 0, x: -20 }}
                            animate={{ opacity: 1, x: 0 }}
                            transition={{ delay: index * 0.1 }}
                            className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg"
                          >
                            <div className={`w-8 h-8 ${getCategoryColor(item.category)} rounded-full flex items-center justify-center text-white text-xs font-bold`}>
                              {index + 1}
                            </div>
                            <div className="flex-1">
                              <div className="flex items-center justify-between">
                                <h5 className="font-medium text-gray-900">{item.title}</h5>
                                <div className="flex items-center space-x-2">
                                  <span className="text-sm text-gray-500">${item.cost}</span>
                                  {item.priceAlert && (
                                    <Bell className="h-3 w-3 text-orange-500" />
                                  )}
                                  {item.savings && (
                                    <span className="text-xs text-green-600 font-medium">-${item.savings}</span>
                                  )}
                                </div>
                              </div>
                              <div className="flex items-center space-x-2 text-xs text-gray-500">
                                <Clock className="h-3 w-3" />
                                <span>{item.time}</span>
                                <span>•</span>
                                <span>{item.location}</span>
                              </div>
                            </div>
                          </motion.div>
                        ))}
                      </div>
                    </Card>
                  </div>
                </div>
              </motion.div>
            )}

            {/* Money Features View */}
            {currentView === 'money' && (
              <motion.div
                key="money"
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                exit={{ opacity: 0, scale: 1.1 }}
                transition={{ duration: 0.5 }}
              >
                <div className="text-center mb-8">
                  <h3 className="text-2xl font-semibold text-gray-900 mb-2">
                    Smart Money Features Activated
                  </h3>
                  <p className="text-gray-600">
                    Automatic price tracking, budget alerts, and cost sharing ready to save you money
                  </p>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <Card className="p-6 bg-gradient-to-br from-green-50 to-emerald-50 border-green-200">
                    <div className="flex items-center mb-4">
                      <TrendingDown className="h-6 w-6 text-green-600 mr-3" />
                      <h4 className="font-semibold text-green-900">Price Alerts Active</h4>
                    </div>
                    <div className="space-y-3">
                      <div className="flex justify-between items-center">
                        <span className="text-sm text-green-800">Shibuya Hotel</span>
                        <div className="flex items-center space-x-2">
                          <span className="text-xs line-through text-gray-500">$135</span>
                          <span className="text-sm font-bold text-green-600">$120</span>
                          <Badge className="bg-green-500 text-white text-xs">-$15</Badge>
                        </div>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-sm text-green-800">Flight to Tokyo</span>
                        <div className="flex items-center space-x-2">
                          <Bell className="h-3 w-3 text-orange-500" />
                          <span className="text-xs text-gray-600">Tracking</span>
                        </div>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-sm text-green-800">TeamLab Tickets</span>
                        <div className="flex items-center space-x-2">
                          <Bell className="h-3 w-3 text-orange-500" />
                          <span className="text-xs text-gray-600">Tracking</span>
                        </div>
                      </div>
                    </div>
                  </Card>

                  <Card className="p-6 bg-gradient-to-br from-blue-50 to-indigo-50 border-blue-200">
                    <div className="flex items-center mb-4">
                      <DollarSign className="h-6 w-6 text-blue-600 mr-3" />
                      <h4 className="font-semibold text-blue-900">Budget Tracking</h4>
                    </div>
                    <div className="space-y-3">
                      <div className="flex justify-between">
                        <span className="text-sm text-blue-800">Daily Average</span>
                        <span className="font-medium text-blue-900">$369</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm text-blue-800">Total Budget</span>
                        <span className="font-medium text-blue-900">$2,000</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm text-blue-800">Projected Cost</span>
                        <span className="font-medium text-blue-900">$1,847</span>
                      </div>
                      <div className="flex justify-between text-green-600">
                        <span className="text-sm font-medium">Under Budget</span>
                        <span className="font-bold">$153</span>
                      </div>
                    </div>
                  </Card>
                </div>

                <div className="mt-6 text-center">
                  <div className="bg-orange-50 rounded-lg p-4 border border-orange-200">
                    <p className="text-orange-800 font-medium">
                      🎉 Your trip is optimized! You're saving $153 and have 5 price alerts monitoring for better deals.
                    </p>
                  </div>
                </div>
              </motion.div>
            )}
          </AnimatePresence>
        </div>

        {/* Benefits */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ delay: 0.8, duration: 0.6 }}
          className="mt-16 text-center"
        >
          <h3 className="text-2xl font-bold text-gray-900 mb-8">
            Why TravelViz Saves You Money & Time
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="text-center">
              <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <DollarSign className="h-8 w-8 text-green-600" />
              </div>
              <h4 className="text-lg font-semibold text-gray-900 mb-2">Automatic Money Saving</h4>
              <p className="text-gray-600">Price alerts, budget tracking, and deal finding built into every trip</p>
            </div>
            <div className="text-center">
              <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Zap className="h-8 w-8 text-blue-600" />
              </div>
              <h4 className="text-lg font-semibold text-gray-900 mb-2">30-Second Setup</h4>
              <p className="text-gray-600">Paste AI conversation, get visual itinerary with price tracking instantly</p>
            </div>
            <div className="text-center">
              <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Users className="h-8 w-8 text-purple-600" />
              </div>
              <h4 className="text-lg font-semibold text-gray-900 mb-2">Group Collaboration</h4>
              <p className="text-gray-600">Share costs, coordinate bookings, and plan together in real-time</p>
            </div>
          </div>
        </motion.div>

        {/* CTA */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ delay: 1, duration: 0.6 }}
          className="mt-16 text-center"
        >
          <div className="bg-gradient-to-r from-orange-500 to-pink-500 rounded-2xl p-8 text-white">
            <h3 className="text-2xl font-bold mb-4">Ready to Save Money on Your Next Trip?</h3>
            <p className="text-xl mb-6 text-white/90">
              Join travelers who've discovered the smartest way to plan and save on travel
            </p>
            <Button size="lg" className="bg-white text-orange-500 hover:bg-gray-100 px-8 py-4 text-lg font-semibold">
              Start Saving Money Free
              <ArrowRight className="h-5 w-5 ml-2" />
            </Button>
            <p className="text-sm text-white/80 mt-4">
              No credit card required • Average savings: $400 per trip
            </p>
          </div>
        </motion.div>
      </div>
    </section>
  );

  function getCurrentStepIndex() {
    switch (currentView) {
      case 'input': return 1;
      case 'processing': return 2;
      case 'timeline': return 3;
      case 'money': return 4;
      default: return 1;
    }
  }
}