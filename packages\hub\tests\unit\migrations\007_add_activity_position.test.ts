import { readFileSync } from 'fs';
import { vi, describe, it, expect, beforeEach, afterEach, beforeAll } from 'vitest';
import { join } from 'path';

describe('Migration 007_add_activity_position', () => {
  let migrationSql: string;

  beforeAll(() => {
    const migrationPath = join(__dirname, '../../../supabase/migrations/007_add_activity_position.sql');
    migrationSql = readFileSync(migrationPath, 'utf8');
  });

  describe('Migration SQL validation', () => {
    it('should contain ALTER TABLE statement to add position column', () => {
      expect(migrationSql).toContain('ALTER TABLE activities');
      expect(migrationSql).toContain('ADD COLUMN IF NOT EXISTS position INTEGER NOT NULL DEFAULT 0');
    });

    it('should update existing activities with sequential positions', () => {
      expect(migrationSql).toContain('WITH ordered_activities AS');
      expect(migrationSql).toContain('ROW_NUMBER() OVER (PARTITION BY trip_id ORDER BY start_time');
      expect(migrationSql).toContain('UPDATE activities');
    });

    it('should create index on trip_id and position', () => {
      expect(migrationSql).toContain('CREATE INDEX');
      expect(migrationSql).toContain('idx_activities_trip_position');
      expect(migrationSql).toContain('ON activities(trip_id, position)');
    });

    it('should add comment to explain the position column', () => {
      expect(migrationSql).toContain('COMMENT ON COLUMN activities.position');
    });
  });

  describe('Migration logic validation', () => {
    it('should use ROW_NUMBER() - 1 to ensure positions start at 0', () => {
      expect(migrationSql).toMatch(/ROW_NUMBER\(\).*- 1 as new_position/s);
    });

    it('should order by start_time and created_at for deterministic ordering', () => {
      expect(migrationSql).toContain('ORDER BY start_time, created_at');
    });

    it('should partition by trip_id to ensure independent positioning per trip', () => {
      expect(migrationSql).toContain('PARTITION BY trip_id');
    });
  });
});