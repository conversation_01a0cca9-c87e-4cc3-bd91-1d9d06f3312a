#!/usr/bin/env node

/**
 * Prompt Optimization Test
 * Tests and optimizes prompts for each AI model to maximize performance
 */

// Load environment variables first
import { loadEnvironment } from '../utils/env-loader';
loadEnvironment();

import { enhancedAIRouterService } from '../services/enhanced-ai-router.service';
import { promptManager } from '../services/prompt-manager.service';
import { getSupabaseClient } from '../lib/supabase';
import { logger } from '../utils/logger';

// Test scenarios for prompt optimization
const OPTIMIZATION_TEST_CASES = [
  {
    name: 'Simple Itinerary',
    content: `
      Day 1: Tokyo
      - Visit Senso-ji Temple at 9:00 AM
      - Lunch in Asakusa at 12:00 PM
      - Tokyo Skytree at 3:00 PM
    `,
    expectedElements: ['activities', 'locations', 'times', 'days']
  },
  {
    name: 'Multi-City Trip',
    content: `
      March 15-20: Tokyo, Japan
      - Senso-ji Temple, Shibuya Crossing, Tsukiji Market
      
      March 21-25: Kyoto, Japan
      - Fushimi Inari Shrine, Kinkaku-ji Temple, Arashiyama Bamboo Grove
      
      March 26-30: Osaka, Japan
      - Osaka Castle, Dotonbori, Universal Studios Japan
    `,
    expectedElements: ['multiple_cities', 'dates', 'activities', 'locations']
  },
  {
    name: 'Complex European Tour',
    content: `
      European Grand Tour - 21 Days
      
      Days 1-5: London, UK
      Accommodation: The Savoy Hotel (£450/night)
      Activities:
      - Day 1: Arrival, Westminster Abbey, Big Ben
      - Day 2: British Museum, Tower of London
      - Day 3: Buckingham Palace, Hyde Park, West End show
      - Day 4: Day trip to Windsor Castle
      - Day 5: Tate Modern, Borough Market, Thames cruise
      
      Days 6-10: Paris, France
      Transport: Eurostar London-Paris (£89)
      Accommodation: Hotel Le Meurice (€420/night)
      Activities:
      - Day 6: Eiffel Tower, Champs-Élysées
      - Day 7: Louvre Museum, Seine river cruise
      - Day 8: Versailles day trip
      - Day 9: Montmartre, Sacré-Cœur
      - Day 10: Latin Quarter, Notre-Dame area
      
      Total Budget: £8,500 for 2 people
      Best time: April-June
    `,
    expectedElements: ['accommodation', 'transport', 'budget', 'detailed_itinerary', 'multiple_cities']
  }
];

// Model-specific prompt variations to test
const PROMPT_VARIATIONS = {
  'moonshotai/kimi-k2:free': [
    {
      name: 'Structured JSON Focus',
      systemPrompt: `You are a travel itinerary parser. Extract travel information and return ONLY valid JSON.

CRITICAL: Your response must be valid JSON that can be parsed by JSON.parse(). No markdown, no explanations, no additional text.

Return this exact structure:
{
  "title": "Trip title",
  "description": "Brief description",
  "startDate": "YYYY-MM-DD",
  "endDate": "YYYY-MM-DD",
  "activities": [
    {
      "name": "Activity name",
      "description": "Activity description",
      "location": "Location name",
      "startTime": "HH:MM",
      "endTime": "HH:MM",
      "date": "YYYY-MM-DD",
      "type": "sightseeing|dining|transport|accommodation|entertainment",
      "cost": number or null,
      "currency": "USD|EUR|GBP|JPY|etc"
    }
  ],
  "metadata": {
    "totalCost": number or null,
    "currency": "USD",
    "source": "user_input"
  }
}`
    },
    {
      name: 'Step-by-Step Processing',
      systemPrompt: `You are an expert travel itinerary parser. Process the input step by step:

1. Identify the trip title and dates
2. Extract each activity with location, time, and type
3. Calculate costs if mentioned
4. Format as valid JSON

IMPORTANT: Return ONLY valid JSON. No explanations or markdown.

JSON Structure:
{
  "title": "string",
  "description": "string", 
  "startDate": "YYYY-MM-DD",
  "endDate": "YYYY-MM-DD",
  "activities": [...],
  "metadata": {...}
}`
    }
  ],
  'google/gemini-2.0-flash': [
    {
      name: 'Detailed Analysis',
      systemPrompt: `You are an advanced travel itinerary analyzer. Use your reasoning capabilities to:

1. Analyze the travel content comprehensively
2. Identify implicit information (likely costs, travel times, etc.)
3. Structure the information logically
4. Return precise JSON output

Focus on accuracy and completeness. Extract every detail possible.

Return valid JSON only:`
    },
    {
      name: 'Context-Aware Processing',
      systemPrompt: `You are a context-aware travel assistant. Consider:

- Geographic relationships between locations
- Logical time sequences
- Cultural and practical constraints
- Cost estimations based on location and activity type

Extract and structure all travel information into valid JSON format.`
    }
  ],
  'google/gemini-2.5-pro': [
    {
      name: 'High-Quality Analysis',
      systemPrompt: `You are a premium travel itinerary expert. Provide the highest quality analysis:

1. Deep understanding of travel patterns
2. Accurate location and timing extraction
3. Intelligent cost estimation
4. Comprehensive activity categorization

Return perfectly structured JSON with maximum detail and accuracy.`
    }
  ],
  'openai/gpt-4.1-nano': [
    {
      name: 'Efficient Processing',
      systemPrompt: `Travel itinerary parser. Extract key information efficiently:

- Trip dates and title
- Activities with times and locations  
- Costs and currency
- Activity types

Return valid JSON only. Be concise but complete.`
    }
  ]
};

async function testPromptOptimization() {
  console.log('🎯 Starting Prompt Optimization Test...\n');

  const results: any[] = [];

  try {
    // Get active models
    const { data: modelConfigs } = await getSupabaseClient()
      .from('ai_model_configs')
      .select('*')
      .eq('is_active', true);

    if (!modelConfigs || modelConfigs.length === 0) {
      throw new Error('No active model configurations found');
    }

    console.log(`Testing ${modelConfigs.length} models with ${OPTIMIZATION_TEST_CASES.length} test cases...\n`);

    // Test each model with each prompt variation
    for (const model of modelConfigs) {
      console.log(`\n🤖 Testing Model: ${model.id} (${model.name})`);
      console.log(`Provider: ${model.provider}`);
      
      const modelVariations = PROMPT_VARIATIONS[model.id as keyof typeof PROMPT_VARIATIONS] || [];
      
      if (modelVariations.length === 0) {
        console.log(`⚠️  No prompt variations defined for ${model.id}`);
        
        // Test with current default prompt
        console.log(`\n   Testing with current default prompt...`);
        const currentPrompt = promptManager.getSystemPrompt(model.id);
        console.log(`   Current prompt length: ${currentPrompt.length} characters`);
        console.log(`   Current prompt preview: ${currentPrompt.substring(0, 150)}...`);
        
        continue;
      }

      // Test each prompt variation
      for (const variation of modelVariations) {
        console.log(`\n   📝 Testing Variation: ${variation.name}`);
        console.log(`   Prompt length: ${variation.systemPrompt.length} characters`);
        
        // Test with each test case
        for (const testCase of OPTIMIZATION_TEST_CASES) {
          console.log(`\n     🧪 Test Case: ${testCase.name}`);
          
          try {
            // This would be a real test in a full implementation
            // For now, we'll analyze the prompt structure
            const promptAnalysis = analyzePromptQuality(variation.systemPrompt, testCase);
            
            console.log(`     📊 Prompt Analysis:`);
            console.log(`       - Clarity Score: ${promptAnalysis.clarityScore}/10`);
            console.log(`       - Specificity Score: ${promptAnalysis.specificityScore}/10`);
            console.log(`       - JSON Focus Score: ${promptAnalysis.jsonFocusScore}/10`);
            console.log(`       - Overall Score: ${promptAnalysis.overallScore}/10`);
            
            results.push({
              modelId: model.id,
              modelName: model.name,
              provider: model.provider,
              variationName: variation.name,
              testCaseName: testCase.name,
              promptLength: variation.systemPrompt.length,
              analysis: promptAnalysis
            });
            
          } catch (error) {
            console.log(`     ❌ Test failed: ${error instanceof Error ? error.message : String(error)}`);
          }
        }
      }
    }

    // Generate optimization recommendations
    console.log('\n\n📈 PROMPT OPTIMIZATION RECOMMENDATIONS\n');
    generateOptimizationRecommendations(results);

    return true;

  } catch (error) {
    console.error('❌ Prompt optimization test failed:', error);
    return false;
  }
}

function analyzePromptQuality(prompt: string, testCase: any) {
  let clarityScore = 0;
  let specificityScore = 0;
  let jsonFocusScore = 0;

  // Clarity analysis
  if (prompt.includes('step by step') || prompt.includes('process')) clarityScore += 2;
  if (prompt.includes('IMPORTANT') || prompt.includes('CRITICAL')) clarityScore += 2;
  if (prompt.includes('example') || prompt.includes('structure')) clarityScore += 2;
  if (prompt.length > 200 && prompt.length < 800) clarityScore += 2;
  if (prompt.includes('You are')) clarityScore += 2;

  // Specificity analysis
  if (prompt.includes('travel') || prompt.includes('itinerary')) specificityScore += 2;
  if (prompt.includes('activity') || prompt.includes('location')) specificityScore += 2;
  if (prompt.includes('date') || prompt.includes('time')) specificityScore += 2;
  if (prompt.includes('cost') || prompt.includes('currency')) specificityScore += 2;
  if (prompt.includes('type') || prompt.includes('category')) specificityScore += 2;

  // JSON focus analysis
  if (prompt.includes('JSON') || prompt.includes('json')) jsonFocusScore += 3;
  if (prompt.includes('valid JSON') || prompt.includes('JSON.parse')) jsonFocusScore += 3;
  if (prompt.includes('No markdown') || prompt.includes('ONLY')) jsonFocusScore += 2;
  if (prompt.includes('{') && prompt.includes('}')) jsonFocusScore += 2;

  const overallScore = Math.round((clarityScore + specificityScore + jsonFocusScore) / 3);

  return {
    clarityScore: Math.min(clarityScore, 10),
    specificityScore: Math.min(specificityScore, 10),
    jsonFocusScore: Math.min(jsonFocusScore, 10),
    overallScore: Math.min(overallScore, 10)
  };
}

function generateOptimizationRecommendations(results: any[]) {
  // Group results by model
  const groupedResults: Record<string, any[]> = results.reduce((acc, result) => {
    if (!acc[result.modelId]) acc[result.modelId] = [];
    acc[result.modelId].push(result);
    return acc;
  }, {} as Record<string, any[]>);

  for (const [modelId, modelResultsArray] of Object.entries(groupedResults)) {
    console.log(`\n🎯 ${modelId} Recommendations:`);

    const avgScore = modelResultsArray.reduce((sum: number, r: any) => sum + r.analysis.overallScore, 0) / modelResultsArray.length;
    const bestVariation = modelResultsArray.reduce((best: any, current: any) =>
      current.analysis.overallScore > best.analysis.overallScore ? current : best
    );
    
    console.log(`   Average Score: ${avgScore.toFixed(1)}/10`);
    console.log(`   Best Variation: ${bestVariation.variationName} (${bestVariation.analysis.overallScore}/10)`);

    // Specific recommendations
    if (avgScore < 7) {
      console.log(`   🔧 Needs Improvement:`);
      if (bestVariation.analysis.jsonFocusScore < 8) {
        console.log(`      - Add stronger JSON formatting requirements`);
      }
      if (bestVariation.analysis.clarityScore < 8) {
        console.log(`      - Improve instruction clarity and structure`);
      }
      if (bestVariation.analysis.specificityScore < 8) {
        console.log(`      - Add more travel-specific terminology`);
      }
    } else {
      console.log(`   ✅ Performing well - consider minor refinements`);
    }
  }
}

// Run the test if this script is executed directly
if (require.main === module) {
  testPromptOptimization()
    .then((success) => {
      if (success) {
        console.log('\n🎉 Prompt optimization test completed!');
      }
      process.exit(success ? 0 : 1);
    })
    .catch((error) => {
      console.error('Test execution failed:', error);
      process.exit(1);
    });
}

export { testPromptOptimization };
