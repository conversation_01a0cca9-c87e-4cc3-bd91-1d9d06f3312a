#!/usr/bin/env node

const { spawn } = require('child_process');
const path = require('path');
const http = require('http');

// Colors for output
const colors = {
  hub: '\x1b[34m', // blue
  web: '\x1b[32m', // green
  warn: '\x1b[33m', // yellow
  error: '\x1b[31m', // red
  success: '\x1b[92m', // bright green
  reset: '\x1b[0m'
};

// Service status tracking
const services = {
  hub: {
    ready: false,
    port: 3001,
    actualPort: null, // Will be detected from server output
    startTime: Date.now()
  },
  web: {
    ready: false,
    port: 3000,
    actualPort: null, // Will be detected from server output
    startTime: Date.now()
  }
};

// Check if service is ready by polling health endpoint
function checkServiceReady(service, port, callback) {
  const options = {
    hostname: 'localhost',
    port: port,
    path: service === 'hub' ? '/health' : '/',
    method: 'GET',
    timeout: 1000
  };

  const req = http.request(options, (res) => {
    if (res.statusCode === 200 || res.statusCode === 404) {
      callback(true);
    } else {
      callback(false);
    }
  });

  req.on('error', () => {
    callback(false);
  });

  req.on('timeout', () => {
    req.destroy();
    callback(false);
  });

  req.end();
}

// Monitor service startup with dynamic port detection
function monitorService(serviceName, defaultPort) {
  let currentPort = defaultPort;

  const checkInterval = setInterval(() => {
    // Use actual port if detected, otherwise use current port
    const portToCheck = services[serviceName].actualPort || currentPort;

    checkServiceReady(serviceName, portToCheck, (isReady) => {
      if (isReady && !services[serviceName].ready) {
        services[serviceName].ready = true;
        services[serviceName].actualPort = portToCheck;
        const startupTime = ((Date.now() - services[serviceName].startTime) / 1000).toFixed(1);

        console.log(`${colors.success}✅ [${serviceName}] Ready in ${startupTime}s${colors.reset} - http://localhost:${portToCheck}`);

        clearInterval(checkInterval);

        // Check if all services are ready
        if (services.hub.ready && services.web.ready) {
          const totalTime = ((Date.now() - Math.min(services.hub.startTime, services.web.startTime)) / 1000).toFixed(1);
          console.log(`\n${colors.success}🚀 All services ready in ${totalTime}s!${colors.reset}`);
          console.log(`${colors.hub}[hub]${colors.reset} API: http://localhost:${services.hub.actualPort || 3001}`);
          console.log(`${colors.web}[web]${colors.reset} App: http://localhost:${services.web.actualPort || 3000}`);
          console.log(`\n${colors.warn}Press Ctrl+C to stop all services${colors.reset}\n`);
        }
      }
    });
  }, 500); // Check every 500ms

  // Timeout after 120 seconds for Next.js compilation
  setTimeout(() => {
    if (!services[serviceName].ready) {
      console.log(`${colors.error}❌ [${serviceName}] Failed to start within 120s${colors.reset}`);
      clearInterval(checkInterval);
    }
  }, 120000);
}

console.log('Starting TravelViz development servers...');
console.log(`${colors.hub}[hub]${colors.reset} Starting API server...`);
console.log(`${colors.web}[web]${colors.reset} Starting Next.js app...`);
console.log('');

// Start hub service
const hubProcess = spawn('pnpm', ['--filter', '@travelviz/hub', 'dev'], {
  cwd: path.join(__dirname, '..'),
  stdio: ['inherit', 'pipe', 'pipe'],
  shell: true
});

// Start web service
const webProcess = spawn('pnpm', ['--filter', '@travelviz/web', 'dev'], {
  cwd: path.join(__dirname, '..'),
  stdio: ['inherit', 'pipe', 'pipe'],
  shell: true
});

// Start monitoring services
monitorService('hub', 3001);
monitorService('web', 3000);

// Handle hub output
let hubCompiling = false;
hubProcess.stdout.on('data', (data) => {
  const lines = data.toString().split('\n');
  lines.forEach(line => {
    if (line.trim()) {
      // Add progress indicators for hub
      if (line.includes('tsx watch')) {
        console.log(`${colors.hub}[hub] 🔄 Compiling TypeScript...${colors.reset}`);
        hubCompiling = true;
      } else if (line.includes('server running') && hubCompiling) {
        hubCompiling = false;
      }

      // Detect actual port from server output
      const portMatch = line.match(/server running on port (\d+)/);
      if (portMatch) {
        const detectedPort = parseInt(portMatch[1]);
        services.hub.actualPort = detectedPort;
        if (detectedPort !== 3001) {
          console.log(`${colors.warn}[hub] 📝 Using port ${detectedPort} (3001 was in use)${colors.reset}`);
        }
      }

      // Also check for port change messages
      const portChangeMatch = line.match(/Port 3001 is in use, using available port (\d+)/);
      if (portChangeMatch) {
        services.hub.actualPort = parseInt(portChangeMatch[1]);
      }

      console.log(`${colors.hub}[hub]${colors.reset} ${line}`);
    }
  });
});

hubProcess.stderr.on('data', (data) => {
  const lines = data.toString().split('\n');
  lines.forEach(line => {
    if (line.trim()) {
      console.error(`${colors.hub}[hub]${colors.reset} ${line}`);
    }
  });
});

// Handle web output with enhanced progress tracking
let webCompiling = false;
let webBuildStarted = false;
webProcess.stdout.on('data', (data) => {
  const lines = data.toString().split('\n');
  lines.forEach(line => {
    if (line.trim()) {
      // Add progress indicators for Next.js
      if (line.includes('Starting...')) {
        console.log(`${colors.web}[web] 🔄 Initializing Next.js...${colors.reset}`);
        webBuildStarted = true;
      } else if (line.includes('Compiling')) {
        console.log(`${colors.web}[web] 🔨 Compiling pages...${colors.reset}`);
        webCompiling = true;
      } else if (line.includes('Compiled') && webCompiling) {
        console.log(`${colors.web}[web] ✨ Compilation complete${colors.reset}`);
        webCompiling = false;
      }

      // Detect port changes for web service
      const portChangeMatch = line.match(/Port 3000 is in use, using available port (\d+)/);
      if (portChangeMatch) {
        const detectedPort = parseInt(portChangeMatch[1]);
        services.web.actualPort = detectedPort;
        console.log(`${colors.warn}[web] 📝 Using port ${detectedPort} (3000 was in use)${colors.reset}`);
      }

      // Also check for Next.js port announcement
      const nextPortMatch = line.match(/Local:\s+http:\/\/localhost:(\d+)/);
      if (nextPortMatch) {
        const detectedPort = parseInt(nextPortMatch[1]);
        services.web.actualPort = detectedPort;
        if (detectedPort !== 3000) {
          console.log(`${colors.warn}[web] 📝 Running on port ${detectedPort}${colors.reset}`);
        }
      }

      // Don't show redundant "Starting..." message
      if (!line.includes('✓ Starting...') || !webBuildStarted) {
        console.log(`${colors.web}[web]${colors.reset} ${line}`);
      }
    }
  });
});

webProcess.stderr.on('data', (data) => {
  const lines = data.toString().split('\n');
  lines.forEach(line => {
    if (line.trim()) {
      console.error(`${colors.web}[web]${colors.reset} ${line}`);
    }
  });
});

// Handle process exit
let exitCode = 0;

hubProcess.on('exit', (code) => {
  console.log(`${colors.hub}[hub]${colors.reset} Process exited with code ${code}`);
  exitCode = code || exitCode;
  webProcess.kill();
  process.exit(exitCode);
});

webProcess.on('exit', (code) => {
  console.log(`${colors.web}[web]${colors.reset} Process exited with code ${code}`);
  exitCode = code || exitCode;
  hubProcess.kill();
  process.exit(exitCode);
});

// Handle Ctrl+C
process.on('SIGINT', () => {
  console.log('\n\nShutting down services...');
  hubProcess.kill('SIGINT');
  webProcess.kill('SIGINT');
  setTimeout(() => {
    process.exit(0);
  }, 1000);
});

// Show initial loading animation
let loadingInterval;
let loadingChars = ['⠋', '⠙', '⠹', '⠸', '⠼', '⠴', '⠦', '⠧', '⠇', '⠏'];
let loadingIndex = 0;

function showLoading() {
  if (!services.hub.ready || !services.web.ready) {
    process.stdout.write(`\r${loadingChars[loadingIndex]} Waiting for services to start...`);
    loadingIndex = (loadingIndex + 1) % loadingChars.length;
  } else {
    clearInterval(loadingInterval);
    process.stdout.write('\r                                    \r'); // Clear loading line
  }
}

loadingInterval = setInterval(showLoading, 100);