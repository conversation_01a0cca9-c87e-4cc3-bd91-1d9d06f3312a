'use client';

import { usePerformanceMonitor } from '@/hooks/usePerformanceMonitor';
import { usePerformanceObserver, useResourceTiming } from '@/lib/analytics-provider';
import { useAnalyticsContext } from '@/lib/analytics-provider';
import { PerformanceMetrics } from '@/components/analytics/PerformanceMetrics';

export function PerformanceMonitor() {
  const { debug } = useAnalyticsContext();
  
  // Initialize all performance monitoring
  usePerformanceMonitor();
  usePerformanceObserver();
  useResourceTiming();

  if (debug) {
    console.log('Performance monitoring initialized');
  }

  return <PerformanceMetrics />;
}