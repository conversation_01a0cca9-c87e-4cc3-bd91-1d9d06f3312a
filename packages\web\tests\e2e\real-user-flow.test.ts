import { test, expect, Page } from '@playwright/test';
import { AuthHelpers, testUser } from './auth-helpers';

// Test configuration
const API_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001';
const APP_URL = 'http://localhost:3000';

// Network monitoring helper
class NetworkMonitor {
  private requests: Array<{ url: string, status?: number, method: string, timestamp: number }> = [];
  
  constructor(private page: Page) {}
  
  async start() {
    this.requests = [];
    
    this.page.on('request', request => {
      const url = request.url();
      if (url.includes('/api/')) {
        this.requests.push({
          url,
          method: request.method(),
          timestamp: Date.now()
        });
      }
    });
    
    this.page.on('response', response => {
      const url = response.url();
      if (url.includes('/api/')) {
        const req = this.requests.find(r => r.url === url && !r.status);
        if (req) {
          req.status = response.status();
        }
      }
    });
  }
  
  getAuthRequests() {
    return this.requests.filter(r => 
      r.url.includes('/auth/refresh') || 
      r.url.includes('/auth/login') ||
      r.url.includes('/auth/me')
    );
  }
  
  getFailedRequests() {
    return this.requests.filter(r => r.status && r.status >= 400);
  }
  
  getApiRequests() {
    return this.requests.filter(r => r.url.includes('/api/'));
  }
  
  hasInfiniteLoop() {
    const authRequests = this.getAuthRequests();
    // Check if we have more than 3 refresh attempts (circuit breaker threshold)
    const refreshCount = authRequests.filter(r => r.url.includes('/refresh')).length;
    return refreshCount > 3;
  }
  
  hasRapidRetries() {
    const authRequests = this.getAuthRequests();
    if (authRequests.length < 2) return false;
    
    // Check if requests happened within 100ms of each other (too fast)
    for (let i = 1; i < authRequests.length; i++) {
      const timeDiff = authRequests[i].timestamp - authRequests[i-1].timestamp;
      if (timeDiff < 100) return true;
    }
    return false;
  }
}

test.describe('Real User Flow - Authentication Cascade Prevention', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to the app first to ensure we have proper context
    await page.goto(APP_URL);
    
    // Clear all cookies and storage
    await page.context().clearCookies();
    await page.evaluate(() => {
      localStorage.clear();
      sessionStorage.clear();
    });
  });

  test('should complete full user journey: home → login → import → display', async ({ page }) => {
    const monitor = new NetworkMonitor(page);
    await monitor.start();
    
    // Step 1: Visit home page
    await page.goto(APP_URL);
    await expect(page).toHaveURL(APP_URL);
    
    // Verify home page elements
    await expect(page.locator('h1')).toContainText(/Transform|Convert|Import/i);
    
    // Step 2: Navigate to login
    await page.click('a[href="/login"], button:has-text("Login"), button:has-text("Sign in")');
    await expect(page).toHaveURL(`${APP_URL}/login`);
    
    // Step 3: Login with real credentials
    await page.fill('input[type="email"]', testUser.email);
    await page.fill('input[type="password"]', testUser.password);
    await page.click('button[type="submit"]');
    
    // Wait for successful login redirect
    await expect(page).toHaveURL(/\/(dashboard|import)/, { timeout: 10000 });
    
    // Step 4: Navigate to import
    if (!page.url().includes('/import')) {
      await page.goto(`${APP_URL}/import`);
    }
    await expect(page).toHaveURL(`${APP_URL}/import`);
    
    // Step 5: Complete import flow
    // Wait for import wizard to load
    await expect(page.locator('h1')).toContainText(/Import/i);
    
    // Paste sample conversation
    const textarea = page.locator('textarea[placeholder*="Paste your"], textarea[placeholder*="conversation"]');
    await textarea.fill(`User: I want to plan a 3-day trip to Tokyo next month.
AI: I'd be happy to help you plan a 3-day trip to Tokyo! Here's a suggested itinerary:

Day 1: Traditional Tokyo
- Morning: Visit Senso-ji Temple in Asakusa
- Afternoon: Explore Tokyo National Museum
- Evening: Dinner in Shinjuku

Day 2: Modern Tokyo  
- Morning: TeamLab Borderless Digital Art Museum
- Afternoon: Shopping in Shibuya and Harajuku
- Evening: Tokyo Skytree for sunset views

Day 3: Cultural Experience
- Morning: Tsukiji Outer Market for breakfast
- Afternoon: Meiji Shrine and Yoyogi Park
- Evening: Ginza district for upscale dining

Would you like me to add more details about hotels or transportation?

User: Yes, please add hotel recommendations and how to get around.`);
    
    // Select AI source (ChatGPT)
    await page.click('button:has-text("ChatGPT"), label:has-text("ChatGPT")');
    
    // Click continue/parse button
    await page.click('button:has-text("Continue"), button:has-text("Parse"), button:has-text("Next")');
    
    // Wait for parsing to complete
    await expect(page.locator('text=/Processing|Parsing|Analyzing/i')).toBeVisible();
    await expect(page.locator('text=/Processing|Parsing|Analyzing/i')).toBeHidden({ timeout: 30000 });
    
    // Step 6: Preview and create trip
    await expect(page.locator('h2, h3')).toContainText(/Preview|Review/i);
    
    // Verify parsed content
    await expect(page.locator('text=/Tokyo|Senso-ji|Shibuya/i')).toBeVisible();
    
    // Create the trip
    await page.click('button:has-text("Create Trip"), button:has-text("Create"), button:has-text("Finish")');
    
    // Wait for creation
    await expect(page.locator('text=/Creating|Generating/i')).toBeVisible();
    
    // Step 7: Verify redirect to dashboard/trip view
    await expect(page).toHaveURL(/\/(dashboard|plan|trips)/, { timeout: 15000 });
    
    // Verify trip was created
    await expect(page.locator('h1, h2')).toContainText(/Tokyo|Your Trip|Dashboard/i);
    
    // Verify no authentication cascade occurred
    expect(monitor.hasInfiniteLoop()).toBe(false);
    expect(monitor.hasRapidRetries()).toBe(false);
    
    const authRequests = monitor.getAuthRequests();
    const refreshCount = authRequests.filter(r => r.url.includes('/refresh')).length;
    expect(refreshCount).toBeLessThanOrEqual(1); // At most 1 refresh during the flow
  });

  test('should handle expired token with circuit breaker', async ({ page }) => {
    const monitor = new NetworkMonitor(page);
    await monitor.start();
    
    // Login first
    await AuthHelpers.loginViaUI(page);
    
    // Simulate expired token by manipulating localStorage
    await page.evaluate(() => {
      const storageKeys = Object.keys(localStorage);
      const authKey = storageKeys.find(key => key.includes('auth-token') || key.includes('auth-storage'));
      if (authKey) {
        const authData = JSON.parse(localStorage.getItem(authKey) || '{}');
        // Set token to expired
        if (authData.state) {
          authData.state.accessToken = 'expired-token';
        } else {
          authData.accessToken = 'expired-token';
        }
        localStorage.setItem(authKey, JSON.stringify(authData));
      }
    });
    
    // Try to access protected route
    await page.goto(`${APP_URL}/dashboard`);
    
    // Should attempt refresh but not cascade
    await page.waitForTimeout(2000); // Wait for potential retries
    
    // Check for authentication cascade
    expect(monitor.hasInfiniteLoop()).toBe(false);
    
    const authRequests = monitor.getAuthRequests();
    const refreshCount = authRequests.filter(r => r.url.includes('/refresh')).length;
    expect(refreshCount).toBeLessThanOrEqual(3); // Circuit breaker limit
    
    // Should redirect to login after failed refresh
    await expect(page).toHaveURL(/\/login/, { timeout: 10000 });
  });

  test('should handle 429 rate limiting gracefully', async ({ page }) => {
    const monitor = new NetworkMonitor(page);
    await monitor.start();
    
    // Intercept API calls to simulate rate limiting
    await page.route('**/api/v1/**', async (route, request) => {
      // Simulate rate limit on the 3rd request
      const apiRequests = monitor.getApiRequests();
      if (apiRequests.length >= 2) {
        await route.fulfill({
          status: 429,
          headers: {
            'Retry-After': '60'
          },
          body: JSON.stringify({
            error: 'Rate limit exceeded',
            message: 'Too many requests'
          })
        });
      } else {
        await route.continue();
      }
    });
    
    // Login
    await AuthHelpers.loginViaUI(page);
    
    // Make multiple requests quickly
    await page.goto(`${APP_URL}/dashboard`);
    
    // Try to create multiple trips quickly
    for (let i = 0; i < 5; i++) {
      page.click('button:has-text("New Trip")').catch(() => {});
      await page.waitForTimeout(100);
    }
    
    // Wait for rate limit to trigger
    await page.waitForTimeout(2000);
    
    // Verify circuit breaker activated
    const failedRequests = monitor.getFailedRequests();
    const rateLimitedRequests = failedRequests.filter(r => r.status === 429);
    expect(rateLimitedRequests.length).toBeGreaterThan(0);
    
    // Verify no infinite cascade
    expect(monitor.hasInfiniteLoop()).toBe(false);
    
    // Should show error message
    await expect(page.locator('text=/Rate limit|Too many requests/i')).toBeVisible();
  });

  test('should handle network errors with circuit breaker', async ({ page }) => {
    const monitor = new NetworkMonitor(page);
    await monitor.start();
    
    // Login first
    await AuthHelpers.loginViaUI(page);
    
    // Simulate network failure
    await page.route('**/api/v1/trips**', route => {
      route.abort('failed');
    });
    
    // Try to access dashboard
    await page.goto(`${APP_URL}/dashboard`);
    
    // Wait for retries
    await page.waitForTimeout(3000);
    
    // Verify circuit breaker prevents cascading failures
    const apiRequests = monitor.getApiRequests();
    const tripRequests = apiRequests.filter(r => r.url.includes('/trips'));
    
    // Should not exceed max retries (1 retry = 2 total requests)
    expect(tripRequests.length).toBeLessThanOrEqual(2);
    
    // Should show error state
    await expect(page.locator('text=/Failed to load|Network error|Something went wrong/i')).toBeVisible();
  });

  test('should maintain auth state across page refreshes', async ({ page }) => {
    const monitor = new NetworkMonitor(page);
    await monitor.start();
    
    // Login
    await AuthHelpers.loginViaUI(page);
    
    // Navigate to dashboard
    await page.goto(`${APP_URL}/dashboard`);
    await expect(page.locator('h1')).toContainText(/Dashboard|Welcome/i);
    
    // Refresh the page
    await page.reload();
    
    // Should still be authenticated
    await expect(page).toHaveURL(`${APP_URL}/dashboard`);
    await expect(page.locator('h1')).toContainText(/Dashboard|Welcome/i);
    
    // Verify no unnecessary auth requests
    const authRequests = monitor.getAuthRequests();
    const loginRequests = authRequests.filter(r => r.url.includes('/login'));
    expect(loginRequests.length).toBe(0); // Should not attempt login again
  });
});