import axios from 'axios';
import { TripsService } from './trips.service';
import { logger } from '../utils/logger';
import { z } from 'zod';
import { sanitizeForAIPrompt, sanitizeCurrency, ActivityType } from '@travelviz/shared';
import { PromptManager } from './prompt-manager';
import { GeminiService } from './gemini.service';
import { CacheService } from './cache.service';
import { MonitoringService } from './monitoring.service';
import { AI_CONFIG } from '../config/ai.config';
import * as crypto from 'crypto';

// Custom error class for validation errors
export class ValidationError extends Error {
  constructor(message: string) {
    super(message);
    this.name = 'ValidationError';
  }
}


interface ParsedActivity {
  title: string;
  description?: string;
  type?: ActivityType;
  startTime?: string;
  endTime?: string;
  location?: string;
  price?: number;
  currency?: string;
  day?: number;
}

interface ParsedTrip {
  title: string;
  description?: string;
  destination?: string;
  startDate?: string;
  endDate?: string;
  activities: ParsedActivity[];
}

export class ParserService {
  private tripsService: TripsService;
  private openRouterApiKey: string | undefined;
  private isAIParsingAvailable: boolean;
  private promptManager: PromptManager;
  private geminiService: GeminiService;
  private cacheService: CacheService;
  private monitoringService: MonitoringService;
  
  constructor(tripsService: TripsService) {
    this.tripsService = tripsService;
    this.openRouterApiKey = process.env.OPENROUTER_API_KEY;
    this.isAIParsingAvailable = false;
    this.promptManager = new PromptManager();
    this.geminiService = GeminiService.getInstance();
    this.cacheService = CacheService.getInstance();
    this.monitoringService = MonitoringService.getInstance();
    
    // Validate API keys at initialization
    const hasGemini = this.geminiService.isAvailable();
    const hasOpenRouter = this.openRouterApiKey && this.openRouterApiKey.startsWith('sk-or-');
    
    if (!hasGemini && !hasOpenRouter) {
      logger.warn('No AI parsing APIs configured - parsing will not be available');
    } else {
      this.isAIParsingAvailable = true;
      const available = [];
      if (hasGemini) available.push('Gemini (FREE)');
      if (hasOpenRouter) available.push('OpenRouter');
      logger.info('ParserService initialized with AI parsing', { available });
    }
  }

  /**
   * Main method to parse text into a trip
   */
  async parseTextToTrip(text: string, source: 'chatgpt' | 'claude' | 'gemini', model?: string): Promise<ParsedTrip> {
    try {
      // Validate input is not empty
      if (!text || text.trim().length === 0) {
        throw new ValidationError('Input text cannot be empty');
      }

      // Sanitize input to prevent prompt injection
      const sanitizedText = sanitizeForAIPrompt(text);
      
      // Check if sanitization resulted in empty text
      if (!sanitizedText || sanitizedText.trim().length === 0) {
        throw new ValidationError('Input text is invalid or contains only restricted content');
      }
      
      // Check minimum length for meaningful content
      if (sanitizedText.trim().length < 20) {
        throw new ValidationError('Input text is too short. Please provide a complete travel itinerary.');
      }
      
      // Log if sanitization removed content
      if (sanitizedText !== text) {
        logger.warn('Input text was sanitized for security', { 
          originalLength: text.length, 
          sanitizedLength: sanitizedText.length 
        });
      }

      // Check if AI parsing is available
      if (!this.isAIParsingAvailable) {
        logger.error('AI parsing not available', { source });
        throw new ValidationError('AI parsing service is not configured. Please contact support.');
      }

      // Check cache first (skip for very long texts)
      if (sanitizedText.length < 5000) {
        const cacheKey = this.generateCacheKey(sanitizedText, source);
        const cached = await this.cacheService.get<ParsedTrip>(cacheKey, { namespace: 'parser' });
        
        if (cached) {
          logger.info('Cache hit for trip parsing', { source, textLength: sanitizedText.length });
          
          // Log cache hit for monitoring
          await this.monitoringService.logUsage('cache', {
            cacheHit: true,
            responseTime: 0
          });
          
          return cached;
        }
      }

      // Use smart routing to select best model
      const result = await this.parseWithSmartRouting(sanitizedText, source, model);
      
      // Cache the result (skip for very long texts)
      if (sanitizedText.length < 5000 && result.activities.length > 0) {
        await this.cacheService.set(
          this.generateCacheKey(sanitizedText, source), 
          result, 
          { namespace: 'parser', ttl: 86400 } // 24 hours
        );
      }
      
      return result;
    } catch (error) {
      logger.error('Failed to parse trip text', { error, source, model });
      
      // Re-throw validation errors directly
      if (error instanceof ValidationError) {
        throw error;
      }
      
      // Provide specific error messages based on the type of failure
      let errorMessage = 'Failed to parse trip text';
      
      if (error instanceof Error) {
        if (error.message.includes('timeout')) {
          errorMessage = 'Parsing timed out. The text might be too long or complex. Please try with a shorter itinerary.';
        } else if (error.message.includes('network')) {
          errorMessage = 'Network error while parsing. Please check your connection and try again.';
        } else if (error.message.includes('API key')) {
          errorMessage = 'AI parsing service is not configured. Please contact support.';
        } else if (error.message.includes('rate limit')) {
          errorMessage = 'Too many parsing requests. Please wait a moment and try again.';
        } else if (error.message.includes('Invalid') || error.message.includes('validation')) {
          errorMessage = `Invalid ${source} format. Please ensure you copied the complete conversation.`;
        } else {
          errorMessage = `Failed to parse ${source} conversation: ${error.message}`;
        }
      }
      
      throw new Error(errorMessage);
    }
  }



  /**
   * Generate cache key for parsed trip
   */
  private generateCacheKey(text: string, source: string): string {
    const hash = crypto.createHash('sha256');
    hash.update(text + source);
    return hash.digest('hex');
  }

  /**
   * Track usage for model quotas
   */
  private async trackUsage(model: string): Promise<void> {
    const today = new Date().toISOString().split('T')[0];
    const key = `${model}_usage_${today}`;
    
    const current = await this.cacheService.get<number>(key, { namespace: 'usage' }) || 0;
    await this.cacheService.set(key, current + 1, { namespace: 'usage', ttl: 86400 });
    
    logger.info('Model usage tracked', { model, date: today, count: current + 1 });
  }

  /**
   * Get current usage for a model
   */
  private async getUsage(model: string): Promise<number> {
    const today = new Date().toISOString().split('T')[0];
    const key = `${model}_usage_${today}`;
    return await this.cacheService.get<number>(key, { namespace: 'usage' }) || 0;
  }

  /**
   * Smart routing to select best model based on complexity and quotas
   */
  private async parseWithSmartRouting(text: string, source: string, requestedModel?: string): Promise<ParsedTrip> {
    // If specific model requested, try to use it
    if (requestedModel) {
      this.promptManager.switchModel(requestedModel);
      return this.parseWithAI(text, source, requestedModel);
    }

    // Check current usage
    const geminiUsage = await this.getUsage('gemini');
    const deepseekUsage = await this.getUsage('deepseek');
    
    // Smart routing logic
    let selectedModel: string;
    
    // For short texts (< 500 chars), prefer DeepSeek if quota available
    if (text.length < 500 && deepseekUsage < 900) {
      selectedModel = 'deepseek-chat-v3-0324:free';
    }
    // For complex texts or when DeepSeek quota is low, use Gemini
    else if (this.geminiService.isAvailable()) {
      selectedModel = 'gemini-native';
    }
    // Fallback to DeepSeek if still has quota
    else if (deepseekUsage < 1000) {
      selectedModel = 'deepseek-chat-v3-0324:free';
    }
    // Final fallback to paid models via OpenRouter
    else {
      selectedModel = 'gpt-4o-mini';
      logger.warn('Free quotas exhausted, using paid model', { geminiUsage, deepseekUsage });
    }

    logger.info('Smart routing selected model', { 
      selectedModel, 
      textLength: text.length,
      geminiUsage,
      deepseekUsage 
    });

    // Alert if approaching quota limits
    if (deepseekUsage >= 900) {
      logger.warn('DeepSeek quota warning', { usage: deepseekUsage, limit: 1000 });
    }
    
    // Check usage alerts
    await this.monitoringService.checkUsageAlerts();

    // Use native Gemini if selected
    if (selectedModel === 'gemini-native') {
      const startTime = Date.now();
      
      try {
        await this.trackUsage('gemini');
        const prompt = this.promptManager.getPrompt(text, source);
        const result = await this.geminiService.parseWithGemini(text, prompt);
        
        // Log successful usage
        await this.monitoringService.logUsage('gemini', {
          model: 'gemini-native',
          responseTime: Date.now() - startTime,
          cacheHit: false
        });
        
        return result;
      } catch (error) {
        // Log error
        await this.monitoringService.logUsage('gemini', {
          model: 'gemini-native',
          responseTime: Date.now() - startTime,
          error: error instanceof Error ? error.message : 'Unknown error'
        });
        throw error;
      }
    }

    // Otherwise use OpenRouter
    const startTime = Date.now();
    
    try {
      this.promptManager.switchModel(selectedModel);
      await this.trackUsage('deepseek');
      const result = await this.parseWithAI(text, source, selectedModel);
      
      // Log successful usage
      await this.monitoringService.logUsage('deepseek', {
        model: selectedModel,
        responseTime: Date.now() - startTime,
        cacheHit: false
      });
      
      return result;
    } catch (error) {
      // Log error
      await this.monitoringService.logUsage('deepseek', {
        model: selectedModel,
        responseTime: Date.now() - startTime,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      throw error;
    }
  }

  /**
   * Retry a function with exponential backoff
   */
  private async retryWithBackoff<T>(
    fn: () => Promise<T>,
    maxRetries: number = 3,
    initialDelay: number = 1000
  ): Promise<T> {
    let lastError: Error;
    
    for (let attempt = 0; attempt < maxRetries; attempt++) {
      try {
        return await fn();
      } catch (error) {
        lastError = error instanceof Error ? error : new Error(String(error));
        
        // Don't retry on certain errors
        const errorMessage = lastError.message.toLowerCase();
        if (
          errorMessage.includes('api key') ||
          errorMessage.includes('not configured') ||
          errorMessage.includes('invalid model') ||
          errorMessage.includes('insufficient quota')
        ) {
          throw lastError;
        }
        
        // Last attempt, throw the error
        if (attempt === maxRetries - 1) {
          throw lastError;
        }
        
        // Calculate delay with exponential backoff and jitter
        const delay = initialDelay * Math.pow(2, attempt) + Math.random() * 1000;
        logger.warn(`AI API call failed, retrying in ${Math.round(delay)}ms`, {
          attempt: attempt + 1,
          maxRetries,
          error: lastError.message
        });
        
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }
    
    throw lastError!;
  }

  /**
   * Parse unstructured text using AI (OpenRouter)
   */
  private async parseWithAI(text: string, source: string, model?: string): Promise<ParsedTrip> {
    if (!this.isAIParsingAvailable || !this.openRouterApiKey) {
      logger.error('AI parsing attempted but not available');
      throw new Error('AI parsing is not available - OPENROUTER_API_KEY not configured or invalid');
    }

    // Use model from request or let PromptManager use its default
    if (model) {
      this.promptManager.switchModel(model);
    }
    
    const modelConfig = this.promptManager.getModelConfig();
    logger.info('Using AI model for parsing', { 
      model: modelConfig.name,
      id: modelConfig.id,
      strategy: modelConfig.strategy 
    });

    try {
      // Get optimized prompt based on model and strategy
      const prompt = this.promptManager.getPrompt(text, source);
      
      // Use retry logic for the API call
      const response = await this.retryWithBackoff(async () => {
        return await axios.post(
          'https://openrouter.ai/api/v1/chat/completions',
          {
            model: modelConfig.id,
            messages: [
              {
                role: 'user',
                content: prompt
              }
            ],
            temperature: modelConfig.temperature,
            max_tokens: modelConfig.maxTokens
          },
          {
            headers: {
              'Authorization': `Bearer ${this.openRouterApiKey}`,
              'Content-Type': 'application/json'
            },
            timeout: AI_CONFIG.parsing.timeout // Use configurable timeout (now 60 seconds)
          }
        );
      });

      const aiResponse = response.data.choices[0]?.message?.content;
      if (!aiResponse) {
        throw new Error('No response from AI');
      }

      // Parse the JSON response
      const parsedData = this.parseAIResponse(aiResponse);
      
      // Validate that we got meaningful content
      if (!parsedData.activities || parsedData.activities.length === 0) {
        throw new ValidationError('AI was unable to extract any activities from the conversation. Please ensure your text contains travel-related activities.');
      }
      
      // Log cost estimate
      const tokensUsed = response.data.usage?.total_tokens || 0;
      const estimatedCost = (tokensUsed / 1000) * (modelConfig.costPer1K || 0);
      logger.info('AI parsing complete', {
        model: modelConfig.name,
        tokensUsed,
        estimatedCost: `$${estimatedCost.toFixed(4)}`,
        activitiesFound: parsedData.activities.length
      });
      
      return parsedData;
    } catch (error) {
      logger.error('AI parsing failed', { 
        error,
        model: modelConfig.id,
        source 
      });
      
      // Provide a more informative fallback
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      
      // If it's an API key issue, throw immediately
      if (errorMessage.includes('API key') || errorMessage.includes('not configured')) {
        throw error;
      }
      
      // For other errors, throw them up
      throw new Error(`AI parsing failed: ${errorMessage}`);
    }
  }


  /**
   * Parse AI response into structured data
   */
  private parseAIResponse(response: string): ParsedTrip {
    try {
      // Extract JSON from the response (AI might include extra text or code blocks)
      let jsonString = response;
      
      // Remove markdown code blocks if present
      if (response.includes('```json')) {
        jsonString = response.replace(/```json\s*/, '').replace(/```\s*$/, '');
      } else if (response.includes('```')) {
        jsonString = response.replace(/```\s*/, '').replace(/```\s*$/, '');
      }
      
      // Extract JSON object
      const jsonMatch = jsonString.match(/\{[\s\S]*\}/);
      if (!jsonMatch) {
        throw new Error('No JSON found in AI response');
      }

      const parsed = JSON.parse(jsonMatch[0]);
      
      // Handle different AI response formats
      if (parsed.activities && Array.isArray(parsed.activities)) {
        // Check if activities have nested structure (day groups)
        const firstActivity = parsed.activities[0];
        if (firstActivity && firstActivity.activities && !firstActivity.title) {
          // Flatten nested day structure
          parsed.activities = parsed.activities.flatMap((day: { activities: unknown[]; day?: number; dayNumber?: number }) => 
            (day.activities as Array<Record<string, unknown>>).map((act) => ({
              ...act,
              day: day.day || day.dayNumber || 1
            }))
          );
        }
        
        // Transform field names to match expected format
        parsed.activities = parsed.activities.map((activity: Record<string, unknown>) => ({
          title: activity.title || activity.name,
          type: activity.type,
          startTime: activity.startTime,
          location: typeof activity.location === 'object' && activity.location !== null
            ? (activity.location as { address: string }).address 
            : activity.location,
          price: activity.price,
          currency: activity.currency,
          day: activity.day || activity.dayNumber
        }));
      }
      
      // Validate with Zod
      const schema = z.object({
        title: z.string().default('Untitled Trip'),
        destination: z.string().nullable().optional(),
        startDate: z.string().nullable().optional(),
        endDate: z.string().nullable().optional(),
        activities: z.array(z.object({
          title: z.string(),
          type: z.nativeEnum(ActivityType).default(ActivityType.activity),
          startTime: z.string().nullable().optional(),
          location: z.string().nullable().optional(),
          price: z.number().nullable().optional(),
          currency: z.string().nullable().optional(),
          day: z.number().nullable().optional()
        })).default([])
      });

      const validated = schema.parse(parsed);
      
      return {
        title: validated.title,
        destination: validated.destination || undefined,
        startDate: validated.startDate || undefined,
        endDate: validated.endDate || undefined,
        activities: validated.activities.map(activity => ({
          title: activity.title,
          type: activity.type,
          startTime: activity.startTime || undefined,
          location: activity.location || undefined,
          price: activity.price || undefined,
          currency: activity.currency ? sanitizeCurrency(activity.currency) || activity.currency : undefined,
          day: activity.day || undefined
        }))
      };
    } catch (error) {
      logger.error('Failed to parse AI response', { error, response });
      
      let errorMessage = 'Failed to parse AI response';
      if (error instanceof z.ZodError) {
        const issues = error.issues.map(i => `${i.path.join('.')}: ${i.message}`).join(', ');
        errorMessage = `Invalid trip data format from AI: ${issues}`;
      } else if (error instanceof SyntaxError) {
        errorMessage = 'AI returned invalid JSON format. Please try again.';
      } else if (error instanceof Error) {
        errorMessage = `Failed to parse AI response: ${error.message}`;
      }
      
      throw new Error(errorMessage);
    }
  }



}