import { describe, it, expect, beforeAll } from 'vitest';
import { getSupabaseClient } from '../../src/lib/supabase';
import { getAIParserService } from '../../src/services/ai-parser.service';
import { logger } from '../../src/utils/logger';

// Enable info logging
process.env.LOG_LEVEL = 'INFO';

describe('Debug Parse Issues', () => {
  let userId: string;

  beforeAll(async () => {
    const supabase = getSupabaseClient();
    
    // Authenticate as test user
    const { data: authData, error: authError } = await supabase.auth.signInWithPassword({
      email: '<EMAIL>',
      password: 'Flaremmk123!'
    });

    if (authError) throw authError;
    
    userId = authData.user?.id || '';
    console.log('Authenticated as user:', userId);
  });

  it('should test AI parsing directly', async () => {
    const aiParser = getAIParserService();
    
    const conversation = `User: Plan a 2-day trip to Paris.
Assistant: Here's a 2-day Paris itinerary:

Day 1:
- Morning: Visit the Eiffel Tower
- Afternoon: Explore the Louvre Museum
- Evening: Dinner cruise on the Seine

Day 2:
- Morning: Notre-Dame Cathedral
- Afternoon: Walk through Montmartre
- Evening: Watch sunset at Sacré-Cœur`;

    console.log('Starting parse session...');
    
    try {
      const sessionId = await aiParser.createParseSession(conversation, 'chatgpt', userId);
      console.log('Parse session created:', sessionId);
      
      // Wait and check status multiple times
      for (let i = 0; i < 10; i++) {
        await new Promise(resolve => setTimeout(resolve, 3000));
        
        const session = await aiParser.getSession(sessionId);
        console.log(`Session status after ${(i+1)*3}s:`, {
          status: session?.status,
          progress: session?.progress,
          error: session?.error,
          currentStep: session?.currentStep
        });
        
        if (session?.status === 'complete' || session?.status === 'error') {
          console.log('Parse completed with status:', session.status);
          if (session.status === 'error') {
            console.error('Parse error:', session.error);
          }
          break;
        }
      }
      
      // Check database directly
      const supabase = getSupabaseClient();
      const { data: dbSession, error: dbError } = await supabase
        .from('ai_import_logs')
        .select('*')
        .eq('id', sessionId)
        .single();
        
      console.log('Database session:', {
        id: dbSession?.id,
        status: dbSession?.import_status,
        error: dbSession?.error_message,
        hasData: !!dbSession?.parsed_data
      });
      
      if (dbError) {
        console.error('DB error:', dbError);
      }
      
    } catch (error) {
      console.error('Parse error:', error);
      throw error;
    }
  }, 60000); // 60 second timeout
});