# TravelViz Architecture Overview

## System Architecture

TravelViz implements a **hub-centric architecture** where all system components communicate exclusively through a single, intelligent hub service. This design ensures consistency, simplifies maintenance, and enables rapid feature development.

```
┌─────────────────────────────────────────────────┐
│                   CLIENTS                       │
├─────────────────┬───────────────────────────────┤
│   Next.js Web   │   React Native Mobile         │
│   (Port 3000)   │   (Future Implementation)     │
└────────┬────────┴────────────┬──────────────────┘
         │                     │
         ↓                     ↓
┌─────────────────────────────────────────────────┐
│           THE HUB (Node.js/Express)             │
│               Port 3001                         │
│                                                 │
│  ┌─────────────────────────────────────────┐   │
│  │          API Gateway Layer               │   │
│  │  • Supabase JWT Authentication          │   │
│  │  • Rate Limiting (Per User/IP)          │   │
│  │  • Request Validation (Zod)             │   │
│  │  • Error Handling & Monitoring          │   │
│  │  • Request ID Tracking                  │   │
│  └─────────────────────────────────────────┘   │
│                                                 │
│  ┌─────────────────────────────────────────┐   │
│  │         Business Logic Core              │   │
│  │  • Trip Management Service               │   │
│  │  • AI Parsing Engine (Multi-Model)       │   │
│  │  • Public Trip Sharing System            │   │
│  │  • Activity Management                   │   │
│  │  • Places & Geocoding Service            │   │
│  │  • Affiliate Link Generator              │   │
│  │  • Search History Tracking               │   │
│  └─────────────────────────────────────────┘   │
│                                                 │
│  ┌─────────────────────────────────────────┐   │
│  │         Service Layer                    │   │
│  │  • Supabase Database Client              │   │
│  │  • Cache Management (Upstash Redis)      │   │
│  │  • External API Orchestration            │   │
│  │  • Performance Monitoring                │   │
│  │  • Audit Logging                         │   │
│  └─────────────────────────────────────────┘   │
└─────────────────────────────────────────────────┘
         │                     │
         ↓                     ↓
┌─────────────────┐   ┌───────────────────────┐
│   PostgreSQL    │   │   External APIs       │
│   (Supabase)    │   │   • Google Gemini     │
│   with RLS      │   │   • OpenRouter AI     │
│                 │   │   • Travelpayouts     │
│                 │   │   • Google Maps        │
│                 │   │   • Mapbox             │
└─────────────────┘   └───────────────────────┘
```

## Package Structure

### @travelviz/hub (Backend API)

- **Technology**: Node.js, Express.js, TypeScript
- **Purpose**: Central API server handling all business logic
- **Port**: 3001
- **Key Features**:
  - AI conversation parsing with multi-model support
  - Trip CRUD operations with optimistic updates
  - Activity management with drag-and-drop support
  - Public trip sharing with slug generation
  - Places and geocoding services
  - Affiliate link injection
  - Authentication via Supabase JWT
  - Performance monitoring and audit logging

### @travelviz/web (Frontend)

- **Technology**: Next.js 14 (App Router), React 18, TypeScript, Tailwind CSS
- **Purpose**: Progressive web application interface
- **Port**: 3000
- **Key Features**:
  - Server-side rendering with React Server Components
  - Interactive timeline with drag-and-drop (@dnd-kit)
  - Dynamic map visualization (Mapbox GL)
  - AI chat import wizard
  - Trip sharing and templates
  - User dashboard with virtual scrolling
  - Shadcn/UI component library
  - Zustand state management
  - React Query for data fetching

### @travelviz/mobile (Mobile App)

- **Technology**: React Native
- **Purpose**: Mobile application (placeholder for future implementation)
- **Status**: Not yet implemented

### @travelviz/shared (Shared Utilities)

- **Technology**: TypeScript
- **Purpose**: Common types, utilities, and constants
- **Exports**:
  - Type definitions (Trip, Activity, User, etc.)
  - DTOs for API communication
  - API response helpers (`createSuccessResponse`, `createErrorResponse`)
  - Zod validation schemas
  - Utility functions (sanitization, security, currency)
  - Date validation utilities

## Data Flow

### 1. AI Import Flow

```
User Input → Web Frontend → Hub API (SSE) → AI Service Selection → Parser → Validation → Database → Response
```

- Supports chat conversations, PDF files, and text input
- Multi-model AI support with automatic fallback
- Server-sent events (SSE) for real-time progress updates
- Optimistic UI updates during processing

### 2. Trip Management Flow

```
User Action → Frontend → Hub API → Authorization → Business Logic → Database → Cache Invalidation → Response
```

- Row-level security (RLS) in Supabase
- Optimistic updates for better UX
- Activity reordering with position tracking
- Public trip sharing via unique slugs

### 3. Activity Management Flow

```
Drag & Drop → Frontend → Optimistic Update → Hub API → Position Update → Database → Broadcast → UI Sync
```

- Real-time activity reordering
- Batch position updates for performance
- Conflict resolution for concurrent edits

## API Routes (v1)

All API routes are prefixed with `/api/v1/`:

### Public Endpoints (No Auth)

- `GET /health` - Health check
- `GET /public/:slug` - View public trip
- `GET /models` - List available AI models

### Authentication

- `POST /auth/login` - User login
- `POST /auth/register` - User registration
- `POST /auth/logout` - User logout
- `GET /auth/profile` - Get user profile
- `POST /auth/refresh` - Refresh JWT token

### Trip Management (Auth Required)

- `GET /trips` - List user trips
- `POST /trips` - Create new trip
- `GET /trips/:id` - Get trip details
- `PUT /trips/:id` - Update trip
- `DELETE /trips/:id` - Delete trip
- `POST /trips/:id/clone` - Clone a trip
- `POST /trips/:id/share` - Make trip public

### Activity Management (Auth Required)

- `GET /activities` - List activities
- `POST /activities` - Create activity
- `PUT /activities/:id` - Update activity
- `DELETE /activities/:id` - Delete activity
- `POST /activities/reorder` - Batch reorder activities
- `POST /activities/add-from-place` - Add activity from place

### Import & AI (Auth Required)

- `POST /import/chat` - Import from AI chat (SSE)
- `POST /import/file` - Import from file
- `GET /import/status/:importId` - Check import status

### Places & Search

- `GET /places/search` - Search places
- `GET /places/geocode` - Geocode address
- `GET /search/history` - Get search history

### Affiliate Integration

- `POST /affiliate/inject` - Inject affiliate links into content

### Monitoring (Admin Only)

- `GET /monitoring/usage` - AI usage metrics
- `GET /monitoring/performance` - Performance metrics
- `GET /monitoring/alerts` - Usage alerts

## Key Design Principles

### 1. Single Source of Truth

- All business logic resides in the hub
- No direct database access from clients
- Centralized validation and security

### 2. API-First Design

- RESTful API with consistent patterns
- Frontend is a pure consumer of the hub API
- Clear separation of concerns

### 3. Performance Optimization

- Multi-layer caching with Upstash Redis
- Database query optimization with indexes
- Connection pooling for Supabase
- Request batching where applicable

### 4. Security First

- JWT-based authentication via Supabase
- Row-level security (RLS) policies
- Input sanitization with DOMPurify
- Rate limiting per user/IP
- Request size limits
- CORS configuration

## External Integrations

### AI Services (Cascading Model Strategy)

1. **Primary**: Google Gemini Flash 2.0 (Direct API)
   - Model: `gemini-2.0-flash-exp`
   - Cost: $0 (free tier)
   - Speed: Very fast
   - Quality: Excellent

2. **Fallback 1**: DeepSeek Chat v3 (OpenRouter)
   - Model: `deepseek/deepseek-chat-v3-0324:free`
   - Cost: $0 (free tier)
   - Speed: Good
   - Quality: Very good

3. **Fallback 2**: Gemini Flash 2.0 (OpenRouter)
   - Model: `google/gemini-2.0-flash-001`
   - Cost: $0.05 per million tokens
   - Speed: Fast
   - Quality: Excellent

### Travel APIs

- **Travelpayouts**: Flight affiliate program
- **Strategy**: Automatic affiliate link injection

### Maps & Location

- **Google Maps**: Geocoding and place search
- **Mapbox GL**: Interactive map visualization
- **Features**: Custom markers, clustering, offline support

### Infrastructure

- **Supabase**:
  - PostgreSQL database with Row Level Security
  - JWT-based authentication
  - Real-time subscriptions
  - Storage for file uploads
- **Upstash Redis**:
  - Serverless Redis for caching
  - Session management
  - Rate limiting storage
- **DOMPurify**: Input sanitization

## Security Architecture

### Authentication Flow

```
User Login → Supabase Auth → JWT Token → Hub Middleware → Access Granted
```

### API Security Layers

1. **Authentication**: Supabase JWT validation
2. **Authorization**: User ownership verification
3. **Rate Limiting**:
   - Authenticated: 100 requests/minute
   - Unauthenticated: 20 requests/minute
   - Import endpoints: 10 requests/hour
4. **Input Validation**: Zod schemas on all endpoints
5. **Request Size Limits**:
   - General: 1MB
   - File uploads: 10MB
   - Auth endpoints: 50KB
6. **CORS**: Configured for specific frontend domains

### Security Headers

- `X-Content-Type-Options: nosniff`
- `X-Frame-Options: DENY`
- `X-XSS-Protection: 1; mode=block`
- `Referrer-Policy: strict-origin-when-cross-origin`
- `Permissions-Policy: camera=(), microphone=(), geolocation=()`
- Content Security Policy (CSP) configured

### Data Protection

- Input sanitization with DOMPurify
- SQL injection prevention via parameterized queries
- Environment variables for sensitive configuration
- Account lockout after failed login attempts
- Audit logging for security events

## Deployment Architecture

### Development Environment

- **Hub**: Local Node.js server (port 3001)
- **Web**: Next.js dev server (port 3000)
- **Database**: Supabase cloud instance
- **Redis**: Upstash cloud instance
- **Environment**: `.env.local` files per package

### Production Infrastructure

- **Frontend**: Vercel
  - Automatic deployments from GitHub
  - Edge functions for API routes
  - Built-in analytics and monitoring
  - Global CDN distribution

- **Backend**: Render
  - Node.js hosting with auto-scaling
  - Built-in health checks
  - Zero-downtime deployments
  - Native monitoring tools

- **Database**: Supabase
  - PostgreSQL with automatic backups
  - Row Level Security (RLS)
  - Real-time subscriptions
  - Built-in authentication

- **Cache**: Upstash Redis
  - Serverless Redis
  - Connection pooling
  - Automatic failover

## Performance Optimization

### Database Performance

- Optimized indexes on frequently queried columns
- Connection pooling for Supabase client
- Query optimization functions (RPC)
- Batch operations for bulk updates
- Pagination with cursor-based navigation

### Caching Strategy

1. **Browser Cache**: Static assets with long TTL
2. **CDN Cache**: Vercel Edge Network
3. **Application Cache**:
   - Trip data: 5 minutes
   - User profiles: 10 minutes
   - Public trips: 1 hour
4. **Redis Cache**: Distributed caching for:
   - Session data
   - Rate limit counters
   - Frequently accessed data

### Frontend Performance

- Next.js App Router with RSC (React Server Components)
- Dynamic imports for code splitting
- Image optimization with next/image
- Virtual scrolling for large lists
- Optimistic UI updates
- Progressive Web App (PWA) capabilities
- Bundle size monitoring

### API Performance

- Request/Response compression (gzip)
- Connection keep-alive
- Request batching for related operations
- Server-sent events (SSE) for long operations
- Performance monitoring middleware
- Request ID tracking for debugging

### Monitoring & Observability

- Request logging with correlation IDs
- Performance metrics tracking
- Error tracking and alerting
- AI usage monitoring
- Database query performance logging
- Custom dashboards for key metrics

---

_This architecture is designed for scalability, security, and cost-effectiveness while maintaining the simplicity needed for rapid development and iteration._
