import { logger } from '../utils/logger';
import { redisConnectionPool } from './redis-connection-pool.service';

interface CacheOptions {
  ttl?: number; // Time to live in seconds
  namespace?: string; // Cache key namespace
}

interface CacheStats {
  hits: number;
  misses: number;
  errors: number;
}

/**
 * Cache service using Redis Connection Pool
 * Provides resilient caching capabilities with circuit breaker pattern
 */
export class CacheService {
  private static instance: CacheService;
  private stats: CacheStats = {
    hits: 0,
    misses: 0,
    errors: 0
  };
  private isEnabled = false;
  private initialized = false;

  constructor() {
    // Delay initialization until first use
  }

  /**
   * Ensure the service is initialized
   */
  private async ensureInitialized(): Promise<void> {
    if (!this.initialized) {
      await this.initialize();
      this.initialized = true;
    }
  }

  static getInstance(): CacheService {
    if (!CacheService.instance) {
      CacheService.instance = new CacheService();
    }
    return CacheService.instance;
  }

  /**
   * Initialize connection pool
   */
  private async initialize(): Promise<void> {
    // Support both naming conventions for backward compatibility
    const redisUrl = process.env.UPSTASH_REDIS_URL || process.env.UPSTASH_REDIS_REST_URL;
    const redisToken = process.env.UPSTASH_REDIS_TOKEN || process.env.UPSTASH_REDIS_REST_TOKEN;

    if (!redisUrl || !redisToken) {
      logger.warn('[Cache] Upstash Redis credentials not configured - caching disabled');
      return;
    }

    try {
      // Test connection pool health
      const isHealthy = await redisConnectionPool.healthCheck();
      if (isHealthy) {
        this.isEnabled = true;
        logger.info('[Cache] Redis connection pool cache initialized');
      } else {
        logger.error('[Cache] Redis connection pool health check failed');
      }
    } catch (error) {
      logger.error('[Cache] Failed to initialize Redis connection pool', { error });
    }
  }

  /**
   * Generate cache key with optional namespace
   */
  private generateKey(key: string, namespace?: string): string {
    const prefix = namespace ? `${namespace}:` : '';
    return `travelviz:${prefix}${key}`;
  }

  /**
   * Get value from cache using connection pool
   */
  async get<T>(key: string, options?: CacheOptions): Promise<T | null> {
    await this.ensureInitialized();
    if (!this.isEnabled) {
      return null;
    }

    const cacheKey = this.generateKey(key, options?.namespace);

    try {
      const value = await redisConnectionPool.execute(async (redis) => {
        return await redis.get(cacheKey);
      });
      
      if (value !== null) {
        this.stats.hits++;
        logger.debug('[Cache] Hit', { keyLength: cacheKey.length });
        return value as T;
      }
      
      this.stats.misses++;
      logger.debug('[Cache] Miss', { keyLength: cacheKey.length });
      return null;
    } catch (error) {
      this.stats.errors++;
      logger.error('[Cache] Get error', { keyLength: cacheKey.length, error });
      return null;
    }
  }

  /**
   * Set value in cache using connection pool
   */
  async set<T>(key: string, value: T, options?: CacheOptions): Promise<boolean> {
    await this.ensureInitialized();
    if (!this.isEnabled) {
      return false;
    }

    const cacheKey = this.generateKey(key, options?.namespace);
    const ttl = options?.ttl || 3600; // Default 1 hour

    try {
      await redisConnectionPool.execute(async (redis) => {
        return await redis.set(cacheKey, JSON.stringify(value), {
          ex: ttl,
        });
      });
      
      logger.debug('[Cache] Set', { keyLength: cacheKey.length, ttl });
      return true;
    } catch (error) {
      this.stats.errors++;
      logger.error('[Cache] Set error', { keyLength: cacheKey.length, error });
      return false;
    }
  }

  /**
   * Delete value from cache using connection pool
   */
  async del(key: string, namespace?: string): Promise<boolean> {
    await this.ensureInitialized();
    if (!this.isEnabled) {
      return false;
    }

    const cacheKey = this.generateKey(key, namespace);

    try {
      await redisConnectionPool.execute(async (redis) => {
        return await redis.del(cacheKey);
      });
      logger.debug('[Cache] Deleted', { keyLength: cacheKey.length });
      return true;
    } catch (error) {
      this.stats.errors++;
      logger.error('[Cache] Delete error', { keyLength: cacheKey.length, error });
      return false;
    }
  }

  /**
   * Clear all cache entries with a specific namespace
   * Implements pattern-based deletion using scan operations
   */
  async clearNamespace(namespace: string): Promise<number> {
    await this.ensureInitialized();
    if (!this.isEnabled) {
      return 0;
    }

    try {
      return await redisConnectionPool.execute(async (redis) => {
        const pattern = this.generateKey('*', namespace);
        let deletedCount = 0;
        let cursor = 0;
        
        do {
          // Use scan to find keys matching the pattern
          const result = await redis.scan(cursor, {
            match: pattern,
            count: 100 // Process in batches
          });
          
          cursor = Number(result[0]);
          const keys = result[1];
          
          if (keys.length > 0) {
            await redis.del(...keys);
            deletedCount += keys.length;
          }
        } while (cursor !== 0);
        
        logger.debug('[Cache] Cleared namespace', { namespace, deletedCount });
        return deletedCount;
      });
    } catch (error) {
      this.stats.errors++;
      logger.error('[Cache] Clear namespace error', { namespace, error });
      return 0;
    }
  }

  /**
   * Get or set cache value with a factory function
   */
  async getOrSet<T>(
    key: string,
    factory: () => Promise<T>,
    options?: CacheOptions
  ): Promise<T> {
    await this.ensureInitialized();
    // Try to get from cache first
    const cached = await this.get<T>(key, options);
    if (cached !== null) {
      return cached;
    }

    // Generate fresh value
    const value = await factory();

    // Store in cache (don't await to avoid blocking)
    this.set(key, value, options).catch(error => {
      logger.error('[Cache] Failed to cache value', { key, error });
    });

    return value;
  }

  /**
   * Get cache statistics including connection pool metrics
   */
  getStats(): CacheStats & { connectionPool?: unknown; circuitBreaker?: unknown } {
    const baseStats = { ...this.stats };
    
    if (this.isEnabled) {
      return {
        ...baseStats,
        connectionPool: redisConnectionPool.getMetrics(),
        circuitBreaker: redisConnectionPool.getCircuitBreakerMetrics()
      };
    }
    
    return baseStats;
  }

  /**
   * Reset cache statistics
   */
  resetStats(): void {
    this.stats = {
      hits: 0,
      misses: 0,
      errors: 0
    };
  }

  /**
   * Check if caching is enabled and healthy
   */
  isAvailable(): boolean {
    return this.isEnabled;
  }

  /**
   * Perform health check on the cache system
   */
  async healthCheck(): Promise<boolean> {
    await this.ensureInitialized();
    if (!this.isEnabled) {
      return false;
    }

    try {
      return await redisConnectionPool.healthCheck();
    } catch (error) {
      logger.error('[Cache] Health check failed', { error });
      return false;
    }
  }
}

// Lazy-initialized singleton
let _cacheService: CacheService | null = null;

/**
 * Get the cache service instance (lazy initialization)
 */
export function getCacheService(): CacheService {
  if (!_cacheService) {
    _cacheService = CacheService.getInstance();
  }
  return _cacheService;
}

// Export a proxy for backward compatibility
export const cacheService = new Proxy({} as CacheService, {
  get(target, prop, receiver) {
    const instance = getCacheService();
    const value = instance[prop as keyof CacheService];
    if (typeof value === 'function') {
      return value.bind(instance);
    }
    return value;
  }
});