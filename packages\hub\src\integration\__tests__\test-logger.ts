import fs from 'fs';
import path from 'path';
import { format } from 'util';

// Create logs directory if it doesn't exist
const logsDir = path.join(__dirname, '../../../logs');
if (!fs.existsSync(logsDir)) {
  fs.mkdirSync(logsDir, { recursive: true });
}

// Generate log file name with timestamp
const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
const logFile = path.join(logsDir, `test-run-${timestamp}.log`);
const errorLogFile = path.join(logsDir, `test-errors-${timestamp}.log`);

// Create write streams
const logStream = fs.createWriteStream(logFile, { flags: 'a' });
const errorStream = fs.createWriteStream(errorLogFile, { flags: 'a' });

// Write initial header
logStream.write(`=== Test Run Started at ${new Date().toISOString()} ===\n\n`);
errorStream.write(`=== Test Errors Started at ${new Date().toISOString()} ===\n\n`);

export class TestLogger {
  private testName: string;
  private startTime: number;

  constructor(testName: string) {
    this.testName = testName;
    this.startTime = Date.now();
    this.log('Test started');
  }

  log(...args: any[]) {
    const message = format(...args);
    const logEntry = `[${new Date().toISOString()}] [${this.testName}] ${message}\n`;
    logStream.write(logEntry);
    console.log(logEntry.trim());
  }

  error(...args: any[]) {
    const message = format(...args);
    const errorEntry = `[${new Date().toISOString()}] [${this.testName}] ERROR: ${message}\n`;
    
    // Write to both streams
    logStream.write(errorEntry);
    errorStream.write(errorEntry);
    
    // Also log stack trace if available
    if (args[0] instanceof Error) {
      const stackTrace = `Stack: ${args[0].stack}\n`;
      logStream.write(stackTrace);
      errorStream.write(stackTrace);
    }
    
    console.error(errorEntry.trim());
  }

  debug(label: string, data: any) {
    const debugEntry = `[${new Date().toISOString()}] [${this.testName}] DEBUG ${label}: ${JSON.stringify(data, null, 2)}\n`;
    logStream.write(debugEntry);
    console.debug(debugEntry.trim());
  }

  response(response: any) {
    const responseLog = {
      status: response.status,
      statusText: response.statusText || '',
      headers: response.headers,
      body: response.body,
      text: response.text
    };
    
    const logEntry = `[${new Date().toISOString()}] [${this.testName}] HTTP Response:\n${JSON.stringify(responseLog, null, 2)}\n`;
    logStream.write(logEntry);
    
    if (response.status >= 400) {
      errorStream.write(logEntry);
    }
  }

  duration() {
    const duration = Date.now() - this.startTime;
    this.log(`Test completed in ${duration}ms`);
    return duration;
  }

  static getSummary() {
    return {
      logFile,
      errorLogFile,
      logsDir
    };
  }

  static close() {
    logStream.end('\n=== Test Run Completed ===\n');
    errorStream.end('\n=== Test Errors Completed ===\n');
  }
}

// Global error handler
process.on('unhandledRejection', (reason, promise) => {
  const errorEntry = `[${new Date().toISOString()}] [UNHANDLED] Unhandled Rejection: ${reason}\n`;
  errorStream.write(errorEntry);
  console.error(errorEntry);
});

// Export helper function for test setup
export function setupTestLogging(testName: string): TestLogger {
  return new TestLogger(testName);
}