#!/usr/bin/env node

/**
 * Test Environment Validation Script
 * Validates that all prerequisites are met for running PDF import tests
 */

const fs = require('fs');
const path = require('path');
const axios = require('axios');

// Configuration
const CONFIG = {
  BACKEND_URL: 'http://localhost:3001',
  FRONTEND_URL: 'http://localhost:3000',
  PDF_FILE_PATH: 'C:\\Users\\<USER>\\Travelviz\\Travelviz\\Updated 15-Day European Travel Itinerary_ London, Madrid, Lisbon and Porto.pdf',
  TEST_EMAIL: '<EMAIL>',
  REQUIRED_ENV_VARS: [
    'OPENROUTER_API_KEY',
    'GOOGLE_GEMINI_API_KEY',
    'NEXT_PUBLIC_SUPABASE_URL',
    'NEXT_PUBLIC_SUPABASE_ANON_KEY'
  ],
  OPTIONAL_ENV_VARS: [
    'TEST_PASSWORD',
    'SUPABASE_SERVICE_ROLE_KEY'
  ]
};

// Colors for output
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
};

function colorLog(color, message) {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

// Validation functions
async function validateBackendServer() {
  colorLog('blue', '🔍 Checking backend server...');
  
  try {
    const response = await axios.get(`${CONFIG.BACKEND_URL}/health`, { 
      timeout: 5000 
    });
    
    if (response.status === 200) {
      colorLog('green', '✅ Backend server is running and healthy');
      return true;
    } else {
      colorLog('yellow', `⚠️  Backend server responded with status ${response.status}`);
      return false;
    }
  } catch (error) {
    colorLog('red', '❌ Backend server is not accessible');
    colorLog('yellow', '   Start with: cd packages/hub && pnpm dev');
    return false;
  }
}

async function validateFrontendServer() {
  colorLog('blue', '🔍 Checking frontend server...');
  
  try {
    const response = await axios.get(CONFIG.FRONTEND_URL, { 
      timeout: 5000,
      validateStatus: () => true // Accept any status code
    });
    
    if (response.status === 200) {
      colorLog('green', '✅ Frontend server is running');
      return true;
    } else {
      colorLog('yellow', `⚠️  Frontend server responded with status ${response.status}`);
      return false;
    }
  } catch (error) {
    colorLog('red', '❌ Frontend server is not accessible');
    colorLog('yellow', '   Start with: cd packages/web && pnpm dev');
    return false;
  }
}

function validatePDFFile() {
  colorLog('blue', '🔍 Checking PDF test file...');
  
  if (!fs.existsSync(CONFIG.PDF_FILE_PATH)) {
    colorLog('red', '❌ PDF test file not found');
    colorLog('yellow', `   Expected: ${CONFIG.PDF_FILE_PATH}`);
    colorLog('yellow', '   Update PDF_FILE_PATH in test configuration');
    return false;
  }
  
  const stats = fs.statSync(CONFIG.PDF_FILE_PATH);
  const sizeKB = Math.round(stats.size / 1024);
  
  if (stats.size === 0) {
    colorLog('red', '❌ PDF file is empty');
    return false;
  }
  
  if (stats.size > 50 * 1024 * 1024) { // 50MB
    colorLog('yellow', `⚠️  PDF file is very large (${sizeKB}KB)`);
    colorLog('yellow', '   This may cause upload timeouts');
  }
  
  colorLog('green', `✅ PDF file found (${sizeKB}KB)`);
  return true;
}

function validateEnvironmentVariables() {
  colorLog('blue', '🔍 Checking environment variables...');
  
  let allRequired = true;
  let hasAnyAI = false;
  
  // Check required variables
  CONFIG.REQUIRED_ENV_VARS.forEach(envVar => {
    if (process.env[envVar]) {
      colorLog('green', `✅ ${envVar} is set`);
      if (envVar.includes('API_KEY')) {
        hasAnyAI = true;
      }
    } else {
      colorLog('red', `❌ ${envVar} is missing`);
      allRequired = false;
    }
  });
  
  // Check optional variables
  CONFIG.OPTIONAL_ENV_VARS.forEach(envVar => {
    if (process.env[envVar]) {
      colorLog('green', `✅ ${envVar} is set (optional)`);
    } else {
      colorLog('yellow', `⚠️  ${envVar} not set (optional)`);
    }
  });
  
  if (!hasAnyAI) {
    colorLog('red', '❌ No AI API keys found');
    colorLog('yellow', '   At least one AI API key is required for testing');
    allRequired = false;
  }
  
  return allRequired;
}

function validateTestFiles() {
  colorLog('blue', '🔍 Checking test files...');
  
  const testFiles = [
    'tests/integration/pdf-import-comprehensive-test.js',
    'scripts/run-pdf-import-test.js',
    'tests/manual/PDF_IMPORT_MANUAL_TEST_PROCEDURE.md'
  ];
  
  let allFound = true;
  
  testFiles.forEach(filePath => {
    if (fs.existsSync(filePath)) {
      colorLog('green', `✅ ${filePath}`);
    } else {
      colorLog('red', `❌ ${filePath} not found`);
      allFound = false;
    }
  });
  
  return allFound;
}

async function validateAuthentication() {
  colorLog('blue', '🔍 Testing authentication...');
  
  try {
    const response = await axios.post(
      `${CONFIG.BACKEND_URL}/api/v1/auth/login`,
      {
        email: CONFIG.TEST_EMAIL,
        password: process.env.TEST_PASSWORD || 'Flaremmk123!'
      },
      {
        timeout: 10000,
        headers: {
          'Content-Type': 'application/json'
        }
      }
    );
    
    if (response.data.success) {
      const token = response.data.data?.session?.access_token ||
                    response.data.data?.access_token ||
                    response.data.session?.access_token ||
                    response.data.access_token;
      
      if (token) {
        colorLog('green', '✅ Authentication successful');
        return true;
      } else {
        colorLog('red', '❌ No auth token in response');
        return false;
      }
    } else {
      colorLog('red', `❌ Authentication failed: ${response.data.message}`);
      return false;
    }
  } catch (error) {
    colorLog('red', '❌ Authentication test failed');
    colorLog('yellow', `   Error: ${error.message}`);
    colorLog('yellow', '   Check test credentials and backend server');
    return false;
  }
}

function validateNodeModules() {
  colorLog('blue', '🔍 Checking dependencies...');
  
  const requiredPackages = ['axios', 'form-data'];
  let allFound = true;
  
  requiredPackages.forEach(pkg => {
    try {
      require.resolve(pkg);
      colorLog('green', `✅ ${pkg} is available`);
    } catch (error) {
      colorLog('red', `❌ ${pkg} not found`);
      colorLog('yellow', `   Install with: pnpm install ${pkg}`);
      allFound = false;
    }
  });
  
  return allFound;
}

// Main validation function
async function runValidation() {
  colorLog('cyan', '🧪 PDF Import Test Environment Validation');
  colorLog('cyan', '=========================================');
  
  const results = [];
  
  // Run all validations
  results.push(await validateBackendServer());
  results.push(await validateFrontendServer());
  results.push(validatePDFFile());
  results.push(validateEnvironmentVariables());
  results.push(validateTestFiles());
  results.push(validateNodeModules());
  results.push(await validateAuthentication());
  
  // Summary
  const passed = results.filter(r => r).length;
  const total = results.length;
  
  console.log('\n' + '='.repeat(50));
  
  if (passed === total) {
    colorLog('green', `🎉 All validations passed (${passed}/${total})`);
    colorLog('green', 'Environment is ready for PDF import testing!');
    console.log('\nRun tests with:');
    colorLog('cyan', '  pnpm test:pdf-import');
    colorLog('cyan', '  node scripts/run-pdf-import-test.js');
    return true;
  } else {
    colorLog('red', `❌ ${total - passed} validation(s) failed (${passed}/${total} passed)`);
    colorLog('yellow', 'Please fix the issues above before running tests.');
    
    if (passed >= total - 2) {
      colorLog('yellow', '\nYou can try running tests anyway with:');
      colorLog('cyan', '  pnpm test:pdf-import-force');
    }
    return false;
  }
}

// Help function
function showHelp() {
  console.log(`
${colors.cyan}PDF Import Test Environment Validator${colors.reset}

This script validates that your environment is properly configured for running
PDF import tests.

${colors.yellow}Usage:${colors.reset}
  node scripts/validate-test-environment.js [options]

${colors.yellow}Options:${colors.reset}
  --help, -h     Show this help message

${colors.yellow}What it checks:${colors.reset}
  ✓ Backend server is running (http://localhost:3001)
  ✓ Frontend server is running (http://localhost:3000)
  ✓ PDF test file exists and is readable
  ✓ Required environment variables are set
  ✓ Test files are present
  ✓ Node.js dependencies are installed
  ✓ Authentication works with test account

${colors.yellow}Environment Variables:${colors.reset}
  Required:
    - OPENROUTER_API_KEY
    - GOOGLE_GEMINI_API_KEY  
    - NEXT_PUBLIC_SUPABASE_URL
    - NEXT_PUBLIC_SUPABASE_ANON_KEY
  
  Optional:
    - TEST_PASSWORD (defaults to 'Flaremmk123!')
    - SUPABASE_SERVICE_ROLE_KEY

${colors.yellow}Test Configuration:${colors.reset}
  Test Email: ${CONFIG.TEST_EMAIL}
  PDF File: ${CONFIG.PDF_FILE_PATH}
  Backend: ${CONFIG.BACKEND_URL}
  Frontend: ${CONFIG.FRONTEND_URL}
`);
}

// Main execution
async function main() {
  const args = process.argv.slice(2);
  
  if (args.includes('--help') || args.includes('-h')) {
    showHelp();
    return;
  }
  
  try {
    const success = await runValidation();
    process.exit(success ? 0 : 1);
  } catch (error) {
    colorLog('red', `💥 Validation error: ${error.message}`);
    process.exit(1);
  }
}

// Run if executed directly
if (require.main === module) {
  main();
}

module.exports = { runValidation, CONFIG };
