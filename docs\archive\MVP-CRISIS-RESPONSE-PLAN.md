# MVP Crisis Response Plan

**Purpose**: When things go wrong (and they will), here's how to recover  
**For**: Development team during 15-day sprint

---

## 🚨 Common Crises & Solutions

### 1. "TypeScript errors keep multiplying" (Day 1-2)

**Symptoms**: Fix 5 errors, 10 new ones appear  
**Root Cause**: Cascading type dependencies

**Quick Fix**:

```typescript
// Temporarily add to problem files:
// @ts-nocheck

// Or for specific lines:
// @ts-ignore
```

**Proper Fix**:

1. Fix shared package types FIRST
2. Then hub types
3. Then web types
4. Run `pnpm type-check` after each step

---

### 2. "AI Import isn't working" (Day 4-8)

**Symptoms**: <PERSON>rser returns empty/wrong data  
**Root Cause**: AI API issues or parsing logic

**Quick Fix**:

```typescript
// Fallback to simple regex parsing
if (!aiResult || aiResult.confidence < 0.5) {
  return simpleRegexParse(text);
}
```

**Proper Fix**:

1. Test with known-good conversations first
2. Add logging to see what AI returns
3. Adjust prompts based on failures
4. Have manual trip creation as backup

---

### 3. "Database migrations failed" (Day 2-3)

**Symptoms**: Foreign key errors, RLS policies not working  
**Root Cause**: Existing data conflicts

**Quick Fix**:

```sql
-- Disable constraints temporarily
ALTER TABLE trips DISABLE TRIGGER ALL;
-- Run migration
-- Re-enable
ALTER TABLE trips ENABLE TRIGGER ALL;
```

**Proper Fix**:

1. Backup database first
2. Test migrations on copy
3. Write rollback migrations
4. Document manual steps needed

---

### 4. "Performance is terrible" (Day 13-14)

**Symptoms**: Pages take >5 seconds to load  
**Root Cause**: Missing indexes, N+1 queries

**Quick Fix**:

```typescript
// Add simple caching
const cacheKey = `trips:${userId}`;
const cached = cache.get(cacheKey);
if (cached) return cached;
```

**Proper Fix**:

1. Run EXPLAIN on slow queries
2. Add missing indexes
3. Implement pagination
4. Use DataLoader pattern

---

### 5. "Stripe isn't working" (Day 10-11)

**Symptoms**: Payments fail, webhooks don't fire  
**Root Cause**: Configuration issues

**Quick Fix**:

```typescript
// Use Stripe Payment Links instead
const paymentLink = 'https://buy.stripe.com/your-link';
// Just track manually who paid
```

**Proper Fix**:

1. Verify webhook endpoint URL
2. Check webhook signing secret
3. Use Stripe CLI for testing
4. Start with test mode

---

## 🔥 When to Pull the Emergency Brake

### STOP and reassess if:

1. **Day 3**: Security fixes not complete
   - Why: Can't launch with security holes
   - Decision: Delay launch by X days

2. **Day 8**: AI import <50% success rate
   - Why: Core feature broken
   - Decision: Simplify or pivot approach

3. **Day 12**: Can't handle 10 concurrent users
   - Why: Will die on launch
   - Decision: Fix performance or limit beta

4. **Day 14**: >10 critical bugs
   - Why: Poor user experience
   - Decision: Delay to fix top 5

---

## 🚀 Shortcuts When Behind Schedule

### Day 1-3 Behind? Cut:

- Perfect TypeScript types (use `any` temporarily)
- Comprehensive tests (test critical paths only)
- Code cleanup (ship messy, refactor later)

### Day 4-8 Behind? Cut:

- PDF upload (paste only)
- Fancy animations (basic loading spinner)
- Edit functionality (create new trip instead)

### Day 9-12 Behind? Cut:

- Payment automation (manual Stripe invoices)
- Analytics (just count in database)
- Multiple demo trips (one good one)

### Day 13-15 Behind? Cut:

- Performance optimization (limit to 10 users)
- Monitoring setup (check logs manually)
- Polish (ship rough edges)

---

## 💊 Quick Fixes Toolbox

### Frontend Crashes

```typescript
// Wrap everything in error boundary
<ErrorBoundary fallback={<div>Something went wrong. Refresh page.</div>}>
  <YourComponent />
</ErrorBoundary>
```

### Backend Timeouts

```typescript
// Increase timeout for slow operations
app.use('/api/v1/import', (req, res, next) => {
  req.setTimeout(300000); // 5 minutes for import
  next();
});
```

### Database Locks

```sql
-- Kill long-running queries
SELECT pg_terminate_backend(pid)
FROM pg_stat_activity
WHERE query_time > interval '5 minutes';
```

### Memory Issues

```typescript
// Stream large responses
res.writeHead(200, { 'Content-Type': 'application/json' });
res.write('[');
data.forEach((item, i) => {
  if (i > 0) res.write(',');
  res.write(JSON.stringify(item));
});
res.end(']');
```

---

## 📞 Escalation Path

### Level 1: Try quick fix (30 min)

- Use shortcuts above
- Check Discord/Slack for similar issues
- Review error logs

### Level 2: Phone a friend (2 hours)

- Post in developer community
- Check GitHub issues
- Try alternative approach

### Level 3: Scope reduction (4 hours)

- What can we cut?
- What's truly essential?
- Can we simplify?

### Level 4: Timeline adjustment (8 hours)

- Add 1-2 days to timeline
- Communicate to stakeholders
- Adjust expectations

---

## 🎯 Daily Health Check

Ask these every morning:

1. **Are we on track for Day 15?**
   - Yes → Continue
   - No → What can we cut?

2. **Is the core feature (AI import) working?**
   - Yes → Polish it
   - No → Drop everything else

3. **Can 10 users use it without support?**
   - Yes → Ready for beta
   - No → Fix top issues

4. **Would I use this myself?**
   - Yes → Ship it
   - No → What's missing?

---

## 🏁 Launch Day Disaster Recovery

If everything breaks on Day 15:

1. **Rollback Plan**

   ```bash
   git checkout last-known-good
   npm run deploy
   ```

2. **Minimum Viable Launch**
   - Just the import feature
   - Manual everything else
   - Email support for issues

3. **Communication**
   - "We're experiencing high demand"
   - "Rolling out gradually"
   - "You're in the first wave"

---

## Remember

**Perfect is the enemy of shipped.**

When in crisis:

1. Protect the core feature (AI import)
2. Cut scope, not quality
3. Communicate transparently
4. Learn and iterate

The goal isn't perfection.  
The goal is learning if people want this.

Ship it. Fix it. Ship it again.

---

_"In the midst of chaos, there is also opportunity." - Sun Tzu_
