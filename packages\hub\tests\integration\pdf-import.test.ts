import { describe, it, expect, beforeAll, afterAll } from 'vitest';
import request from 'supertest';
import { createServer } from '../../src/server';
import { getSupabaseClient } from '../../src/lib/supabase';
import fs from 'fs';
import path from 'path';

// Test configuration
const TEST_USER_EMAIL = '<EMAIL>';
const TEST_USER_PASSWORD = 'TestPassword123!';
const API_TIMEOUT = 60000;

// Helper to get auth token
async function getAuthToken(): Promise<string> {
  const supabase = getSupabaseClient();
  const { data, error } = await supabase.auth.signInWithPassword({
    email: TEST_USER_EMAIL,
    password: TEST_USER_PASSWORD
  });

  if (error) throw new Error(`Auth failed: ${error.message}`);
  return data.session?.access_token || '';
}

// Helper to create a test PDF buffer
function createTestPDFBuffer(): Buffer {
  // This is a minimal valid PDF structure
  const pdfContent = `%PDF-1.4
1 0 obj
<< /Type /Catalog /Pages 2 0 R >>
endobj
2 0 obj
<< /Type /Pages /Kids [3 0 R] /Count 1 >>
endobj
3 0 obj
<< /Type /Page /Parent 2 0 R /Resources << /Font << /F1 << /Type /Font /Subtype /Type1 /BaseFont /Arial >> >> >> /MediaBox [0 0 612 792] /Contents 4 0 R >>
endobj
4 0 obj
<< /Length 44 >>
stream
BT
/F1 12 Tf
100 700 Td
(Test Itinerary) Tj
ET
endstream
endobj
xref
0 5
0000000000 65535 f 
0000000009 00000 n 
0000000058 00000 n 
0000000115 00000 n 
0000000229 00000 n 
trailer
<< /Size 5 /Root 1 0 R >>
startxref
344
%%EOF`;

  return Buffer.from(pdfContent);
}

describe('PDF Import API Tests', () => {
  let authToken: string;
  let server: any;
  let app: any;

  beforeAll(async () => {
    authToken = await getAuthToken();
    app = createServer();
    server = app.listen(0);
  }, API_TIMEOUT);

  afterAll(async () => {
    server?.close();
  });

  describe('PDF Upload Validation', () => {
    it('should require authentication for PDF upload', async () => {
      const response = await request(server)
        .post('/api/v1/import/upload')
        .attach('file', createTestPDFBuffer(), 'test.pdf')
        .field('source', 'pdf')
        .expect(401);

      expect(response.body.success).toBe(false);
    });

    it('should reject non-PDF files', async () => {
      const textBuffer = Buffer.from('This is not a PDF file');
      
      const response = await request(server)
        .post('/api/v1/import/upload')
        .set('Authorization', `Bearer ${authToken}`)
        .attach('file', textBuffer, 'test.txt')
        .field('source', 'pdf')
        .expect(400);

      expect(response.body).toMatchObject({
        success: false,
        error: expect.stringContaining('PDF')
      });
    });

    it('should reject files without .pdf extension', async () => {
      const response = await request(server)
        .post('/api/v1/import/upload')
        .set('Authorization', `Bearer ${authToken}`)
        .attach('file', createTestPDFBuffer(), 'test.doc')
        .field('source', 'pdf')
        .expect(400);

      expect(response.body.success).toBe(false);
    });

    it('should enforce file size limits', async () => {
      // Create a buffer larger than 10MB limit
      const largeBuffer = Buffer.alloc(11 * 1024 * 1024, 'a');
      
      const response = await request(server)
        .post('/api/v1/import/upload')
        .set('Authorization', `Bearer ${authToken}`)
        .attach('file', largeBuffer, 'large.pdf')
        .field('source', 'pdf')
        .expect(413);

      expect(response.body).toMatchObject({
        success: false,
        error: expect.stringContaining('size')
      });
    });

    it('should reject missing file', async () => {
      const response = await request(server)
        .post('/api/v1/import/upload')
        .set('Authorization', `Bearer ${authToken}`)
        .field('source', 'pdf')
        .expect(400);

      expect(response.body.success).toBe(false);
    });
  });

  describe('PDF Processing', () => {
    it('should accept valid PDF upload', async () => {
      const response = await request(server)
        .post('/api/v1/import/upload')
        .set('Authorization', `Bearer ${authToken}`)
        .attach('file', createTestPDFBuffer(), 'itinerary.pdf')
        .field('source', 'pdf')
        .expect(200);

      expect(response.body).toMatchObject({
        success: true,
        data: {
          importId: expect.any(String),
          message: expect.stringContaining('upload')
        }
      });
    });

    it('should handle PDF with multiple pages', async () => {
      // Create a multi-page PDF buffer (simplified)
      const multiPagePDF = createTestPDFBuffer(); // In real scenario, this would be a proper multi-page PDF
      
      const response = await request(server)
        .post('/api/v1/import/upload')
        .set('Authorization', `Bearer ${authToken}`)
        .attach('file', multiPagePDF, 'multi-page-itinerary.pdf')
        .field('source', 'pdf')
        .expect(200);

      expect(response.body.success).toBe(true);
    });

    it('should extract text from PDF itineraries', async () => {
      // In a real test, we would use a PDF with actual itinerary content
      const itineraryPDF = createTestPDFBuffer();
      
      const response = await request(server)
        .post('/api/v1/import/upload')
        .set('Authorization', `Bearer ${authToken}`)
        .attach('file', itineraryPDF, 'paris-itinerary.pdf')
        .field('source', 'pdf')
        .expect(200);

      expect(response.body.success).toBe(true);
      
      // In a complete implementation, we would check the parsed content
      // const importId = response.body.data.importId;
      // const result = await waitForParseCompletion(server, importId, authToken);
      // expect(result.activities).toBeDefined();
    });
  });

  describe('PDF Error Handling', () => {
    it('should handle corrupted PDF files gracefully', async () => {
      const corruptedPDF = Buffer.from('%PDF-1.4 corrupted content');
      
      const response = await request(server)
        .post('/api/v1/import/upload')
        .set('Authorization', `Bearer ${authToken}`)
        .attach('file', corruptedPDF, 'corrupted.pdf')
        .field('source', 'pdf');

      // Should either process with error or reject as invalid
      expect([200, 400]).toContain(response.status);
      
      if (response.status === 200) {
        // If accepted, parsing should eventually fail
        const importId = response.body.data.importId;
        // Would check for parse error in real implementation
      }
    });

    it('should handle password-protected PDFs', async () => {
      // This would use a real password-protected PDF in production
      const protectedPDF = createTestPDFBuffer();
      
      const response = await request(server)
        .post('/api/v1/import/upload')
        .set('Authorization', `Bearer ${authToken}`)
        .attach('file', protectedPDF, 'protected.pdf')
        .field('source', 'pdf');

      // Should handle gracefully - either reject or process with limited content
      expect([200, 400, 422]).toContain(response.status);
    });
  });

  describe('PDF to Trip Creation', () => {
    it('should create trip from PDF import', async () => {
      // Upload PDF
      const uploadResponse = await request(server)
        .post('/api/v1/import/upload')
        .set('Authorization', `Bearer ${authToken}`)
        .attach('file', createTestPDFBuffer(), 'london-trip.pdf')
        .field('source', 'pdf')
        .expect(200);

      const importId = uploadResponse.body.data.importId;

      // In real implementation, wait for processing and create trip
      // await waitForParseCompletion(server, importId, authToken);
      
      // Create trip from parsed PDF
      // const createResponse = await request(server)
      //   .post(`/api/v1/import/${importId}/create-trip`)
      //   .set('Authorization', `Bearer ${authToken}`)
      //   .send({ edits: { title: 'London Trip from PDF' } })
      //   .expect(200);

      expect(uploadResponse.body.success).toBe(true);
    });
  });
});