import { Client, PlaceAutocompleteType, Language } from '@googlemaps/google-maps-services-js';
import { ActivityType } from '@travelviz/shared';

export interface PlaceSuggestion {
  description: string;
  place_id: string;
  main_text: string;
  secondary_text: string;
}

export interface PlaceSearchResult {
  suggestions: PlaceSuggestion[];
}

export interface PlaceDetails {
  place_id: string;
  name: string;
  formatted_address: string;
  location_lat: number;
  location_lng: number;
  types: string[];
  rating?: number;
  user_ratings_total?: number;
  photo_reference?: string;
  opening_hours?: string[];
  website?: string;
  phone?: string;
}

export interface ActivityFromPlace {
  title: string;
  description?: string;
  type: ActivityType;
  location: string;
  location_lat: number;
  location_lng: number;
  booking_url?: string;
  notes?: string;
  [key: string]: unknown;
}

/**
 * Service for interacting with Google Places API
 */
export class PlacesService {
  private client: Client;

  constructor() {
    this.client = new Client({});
  }

  /**
   * Search for places using Google Places Autocomplete API
   */
  async searchPlaces(query: string): Promise<PlaceSearchResult> {
    if (query.length < 2) {
      throw new Error('Query must be at least 2 characters long');
    }

    if (!process.env.GOOGLE_MAPS_API_KEY && !process.env.GOOGLE_PLACES_API_KEY) {
      throw new Error('Google Maps/Places API key not configured');
    }

    try {
      const response = await this.client.placeAutocomplete({
        params: {
          input: query,
          key: process.env.GOOGLE_MAPS_API_KEY || process.env.GOOGLE_PLACES_API_KEY || '',
          types: PlaceAutocompleteType.establishment,
          language: Language.en,
        },
      });

      const suggestions = response.data.predictions.map((prediction) => ({
        description: prediction.description,
        place_id: prediction.place_id,
        main_text: prediction.structured_formatting.main_text,
        secondary_text: prediction.structured_formatting.secondary_text,
      }));

      return { suggestions };
    } catch (error) {
      // Type guard for axios errors
      interface AxiosError {
        response?: {
          data?: {
            error_message?: string;
          };
          status?: number;
        };
      }
      
      const isAxiosError = (e: unknown): e is AxiosError => {
        return e !== null && typeof e === 'object' && 'response' in e;
      };
      
      // Log sanitized error for debugging
      const errorMessage = isAxiosError(error) && error.response?.data?.error_message 
        ? error.response.data.error_message 
        : error instanceof Error ? error.message : 'Unknown error';
      const statusCode = isAxiosError(error) ? error.response?.status : undefined;
      
      console.error('Google Places API error:', {
        message: errorMessage,
        status: statusCode,
        // Don't log the full error object or response data
      });
      
      // Check for specific Google API errors
      if (isAxiosError(error) && error.response?.data?.error_message) {
        throw new Error(`Google Places API: ${error.response.data.error_message}`);
      }
      
      throw new Error(`Failed to search places: ${errorMessage}`);
    }
  }

  /**
   * Get detailed information about a place using Google Places Details API
   */
  async getPlaceDetails(placeId: string): Promise<PlaceDetails> {
    if (!placeId) {
      throw new Error('Place ID is required');
    }

    try {
      const response = await this.client.placeDetails({
        params: {
          place_id: placeId,
          key: process.env.GOOGLE_MAPS_API_KEY || process.env.GOOGLE_PLACES_API_KEY!,
          fields: ['place_id', 'name', 'formatted_address', 'geometry', 'types', 'rating', 'user_ratings_total', 'photos', 'opening_hours', 'website', 'international_phone_number'],
          language: Language.en,
        },
      });

      const place = response.data.result;

      if (!place) {
        throw new Error('Place not found');
      }

      return {
        place_id: place.place_id || '',
        name: place.name || '',
        formatted_address: place.formatted_address || '',
        location_lat: place.geometry?.location?.lat || 0,
        location_lng: place.geometry?.location?.lng || 0,
        types: place.types?.map(type => String(type)) || [],
        rating: place.rating,
        user_ratings_total: place.user_ratings_total,
        photo_reference: place.photos?.[0]?.photo_reference,
        opening_hours: place.opening_hours?.weekday_text,
        website: place.website,
        phone: place.international_phone_number,
      };
    } catch (error) {
      throw new Error(`Failed to get place details: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Map place details to activity format
   */
  mapPlaceToActivity(placeDetails: PlaceDetails): ActivityFromPlace {
    const activityType = this.determineActivityType(placeDetails.types);

    let description = '';
    if (placeDetails.rating && placeDetails.user_ratings_total) {
      description = `${this.getPlaceTypeDescription(activityType)} with a rating of ${placeDetails.rating} stars (${placeDetails.user_ratings_total} reviews)`;
    }

    let notes = '';
    if (placeDetails.phone) {
      notes += `Phone: ${placeDetails.phone}`;
    }
    if (placeDetails.opening_hours && placeDetails.opening_hours.length > 0) {
      if (notes) notes += '\n';
      notes += `Hours: ${placeDetails.opening_hours[0]}`;
    }

    return {
      title: placeDetails.name,
      description: description || undefined,
      type: activityType,
      location: placeDetails.formatted_address,
      location_lat: placeDetails.location_lat,
      location_lng: placeDetails.location_lng,
      booking_url: placeDetails.website,
      notes: notes || undefined,
    };
  }

  /**
   * Determine the activity type based on Google Places types
   */
  private determineActivityType(types: string[]): ActivityFromPlace['type'] {
    // Priority order for type mapping
    if (types.includes('lodging')) return ActivityType.accommodation;
    if (types.includes('restaurant') || types.includes('food')) return ActivityType.dining;
    if (types.includes('shopping_mall') || types.includes('store')) return ActivityType.shopping;
    if (types.includes('airport') || types.includes('bus_station') || types.includes('train_station')) return ActivityType.transport;
    if (types.includes('tourist_attraction') || types.includes('museum') || types.includes('amusement_park')) return ActivityType.activity;
    
    return ActivityType.other;
  }

  /**
   * Get user-friendly description for activity type
   */
  private getPlaceTypeDescription(type: ActivityFromPlace['type']): string {
    const descriptions: Record<ActivityType, string> = {
      [ActivityType.accommodation]: 'Accommodation',
      [ActivityType.dining]: 'Restaurant',
      [ActivityType.shopping]: 'Shopping location',
      [ActivityType.transport]: 'Transportation hub',
      [ActivityType.activity]: 'Tourist attraction',
      [ActivityType.flight]: 'Flight',
      [ActivityType.car_rental]: 'Car rental',
      [ActivityType.tour]: 'Tour',
      [ActivityType.sightseeing]: 'Sightseeing',
      [ActivityType.entertainment]: 'Entertainment',
      [ActivityType.other]: 'Location',
    };

    return descriptions[type];
  }
}