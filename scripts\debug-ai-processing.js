const axios = require('axios');

// Load environment variables
require('dotenv').config({ path: '.env.local' });

// Get JWT token from environment or use placeholder
const JWT_TOKEN = process.env.TEST_JWT_TOKEN || 'YOUR_JWT_TOKEN_HERE';

// Test the AI processing issue with session 58df67d3-99d7-4384-8071-6ac4f63607f4
async function debugAIProcessing() {
  console.log('🔍 DEBUGGING AI PROCESSING ISSUE');
  console.log('Session ID: 58df67d3-99d7-4384-8071-6ac4f63607f4');
  console.log('Time:', new Date().toISOString());
  console.log('');

  try {
    // First, check the current status of the problematic session
    console.log('1. Checking current session status...');
    const statusResponse = await axios.get(
      'http://localhost:3001/api/import/parse-simple/58df67d3-99d7-4384-8071-6ac4f63607f4',
      {
        headers: {
          'Authorization': `Bear<PERSON> ${JWT_TOKEN}`
        }
      }
    );
    
    console.log('Current status:', statusResponse.data);
    console.log('');

    // Now create a new session with the same content to see if it reproduces the issue
    console.log('2. Creating new session with same content...');
    
    const testContent = `Your Complete 15-Day European Adventure: London, Madrid, Lisbon & Porto

Pre-Trip Essential Information

Why This Route Makes Sense
Your journey follows a logical progression from London's historic grandeur through Madrid's vibrant culture, continuing to Lisbon's coastal charm, and culminating in Porto's authentic Portuguese soul. The route maximizes your hotel points while avoiding backtracking, though the Madrid-Lisbon connection requires creative solutions due to discontinued direct trains.

Weather Reality Check
London (July 23-25): 20-22°C, occasional rain - perfect walking weather
Madrid (July 25-29): 35-38°C peak heat - locals flee, you'll need strategic planning
Lisbon (July 29-Aug 1): 28°C with coastal breezes - ideal conditions
Porto (Aug 1-6): 25°C, busiest tourist season - early starts essential

LONDON: July 23-25 (2 Nights)

Day 1 - Thursday, July 23: Arrival & Sky Garden Sunset

Why Sky Garden Over London Eye
While tourists pay £30 for the slow-moving London Eye, locals know Sky Garden offers superior 360° views absolutely FREE. This tropical garden atop the "Walkie-Talkie" building combines panoramic vistas with cocktails and dining options - essentially London's best-kept open secret.

Getting There from Hotel
Walking route (15 mins): Exit hotel → Cross Blackfriars Bridge → Turn right on Upper Thames Street → Left on Fish Street Hill → Arrive at 20 Fenchurch Street

Sky Garden Experience
Levels: 35th floor main garden, 36th floor restaurant, 37th floor viewing gallery
Best photo spots: South terrace for Tower Bridge, North for St Paul's
Drinks: City Garden Bar - beer £7-9, wine £12-15, cocktails £14-18
Time needed: 60-90 minutes minimum

Day 2 - Friday, July 24: Markets & Culture

Morning: Borough Market Masters Class
Why Borough Market Matters: London's oldest food market (1014 AD) isn't just about eating - it's about understanding British food culture's evolution from traditional pies to international fusion.

Strategic Eating Route:
1. Start: Monmouth Coffee - flat white £3.50
2. Breakfast: Maria's Market Cafe - full English £12
3. Must-buys: Kappacasein toasted cheese sandwich £7

Afternoon: South Bank Culture Walk
11:30am: Exit market via cathedral exit
11:45am: Southwark Cathedral (free, stunning Gothic)
12:15pm: Globe Theatre tour (£17)
1:30pm: Tate Modern (free, world-class contemporary art)
2:30pm: Walk Millennium Bridge for St Paul's views

MADRID: July 25-29 (4 Nights)

Day 3 - Saturday, July 25: Travel & Arrival
Morning departure from London to Madrid
Afternoon: Check into hotel, explore nearby area
Evening: Traditional tapas dinner

Day 4 - Sunday, July 26: Art Triangle
Morning: Prado Museum (€15)
Afternoon: Reina Sofia Museum (€12)
Evening: Retiro Park sunset

Day 5 - Monday, July 27: Local Madrid
Morning: Royal Palace tour (€13)
Afternoon: Mercado de San Miguel
Evening: Flamenco show (€25)

Day 6 - Tuesday, July 28: Day Trip
Full day trip to Toledo (€30 train + €10 cathedral)
Return to Madrid evening

LISBON: July 29-Aug 1 (3 Nights)

Day 7 - Tuesday, July 29: Travel & Arrival
Travel from Madrid to Lisbon
Afternoon: Explore Alfama district
Evening: Fado dinner (€45)

Day 8 - Wednesday, July 30: Historic Lisbon
Morning: Jerónimos Monastery (€10)
Afternoon: Belém Tower (€6)
Evening: Sunset at Miradouro da Senhora do Monte

Day 9 - Thursday, July 31: Sintra Day Trip
Full day in Sintra
Pena Palace (€14)
Quinta da Regaleira (€11)
Return to Lisbon evening

PORTO: Aug 1-6 (5 Nights)

Day 10 - Friday, Aug 1: Travel & Arrival
Travel from Lisbon to Porto
Afternoon: Explore Ribeira district
Evening: Port wine tasting (€20)

Day 11 - Saturday, Aug 2: Porto Highlights
Morning: Livraria Lello bookstore (€5)
Afternoon: Clérigos Tower (€6)
Evening: Sunset at Dom Luís I Bridge

Day 12 - Sunday, Aug 3: Douro Valley
Full day Douro Valley tour (€85)
Wine tastings and river cruise
Return to Porto evening

Day 13 - Monday, Aug 4: Cultural Porto
Morning: Serralves Museum (€10)
Afternoon: Azulejo tiles workshop (€25)
Evening: Traditional Portuguese dinner

Day 14 - Tuesday, Aug 5: Beach Day
Day trip to Matosinhos Beach
Fresh seafood lunch (€30)
Relaxing beach afternoon

Day 15 - Wednesday, Aug 6: Departure
Morning: Last-minute shopping
Afternoon: Departure from Porto

Budget Summary:
- Accommodation: €1,200 (using hotel points where possible)
- Transportation: €400
- Food & Dining: €800
- Activities & Tours: €350
- Miscellaneous: €250
Total Estimated Budget: €3,000`;

    const createResponse = await axios.post(
      'http://localhost:3001/api/import/parse-simple',
      {
        content: testContent,
        source: 'gemini'
      },
      {
        headers: {
          'Authorization': `Bearer ${JWT_TOKEN}`,
          'Content-Type': 'application/json'
        }
      }
    );

    console.log('New session created:', createResponse.data);
    const newSessionId = createResponse.data.data.importId;
    console.log('New Session ID:', newSessionId);
    console.log('');

    // Monitor the new session for 2 minutes to see if it completes or gets stuck
    console.log('3. Monitoring new session for 2 minutes...');
    const startTime = Date.now();
    const maxWaitTime = 120000; // 2 minutes
    let lastStatus = null;

    while (Date.now() - startTime < maxWaitTime) {
      try {
        const checkResponse = await axios.get(
          `http://localhost:3001/api/import/parse-simple/${newSessionId}`,
          {
            headers: {
              'Authorization': `Bearer ${JWT_TOKEN}`
            }
          }
        );

        const currentStatus = checkResponse.data.data.status;
        if (currentStatus !== lastStatus) {
          console.log(`[${new Date().toISOString()}] Status changed: ${lastStatus} → ${currentStatus}`);
          lastStatus = currentStatus;
          
          if (currentStatus === 'complete' || currentStatus === 'failed') {
            console.log('✅ Session completed with status:', currentStatus);
            console.log('Final data:', checkResponse.data);
            break;
          }
        }

        // Wait 5 seconds before next check
        await new Promise(resolve => setTimeout(resolve, 5000));
      } catch (error) {
        console.log(`[${new Date().toISOString()}] Error checking status:`, error.response?.data || error.message);
        await new Promise(resolve => setTimeout(resolve, 5000));
      }
    }

    if (Date.now() - startTime >= maxWaitTime) {
      console.log('❌ Session did not complete within 2 minutes - ISSUE REPRODUCED');
      console.log('Last known status:', lastStatus);
    }

  } catch (error) {
    console.error('❌ Error during debugging:', error.response?.data || error.message);
  }
}

// Run the debug test
debugAIProcessing().catch(console.error);
