import { describe, it, expect, beforeAll, afterAll, afterEach } from 'vitest';
import { BaseIntegrationTest } from '../utils/base-integration-test';

/**
 * Integration test for Trip Management Flow
 * Tests the complete CRUD operations for trips
 * 
 * Critical Flow #2: Trip Creation and Management
 * - User creates new trip manually
 * - User updates trip details
 * - User manages trip visibility (private/public)
 * - User deletes trip
 * - User lists and filters their trips
 */
class TripManagementFlowTest extends BaseIntegrationTest {

  async testCreateTrip() {
    console.log('📝 Testing trip creation...');
    
    const tripData = {
      ...this.fixtures.trips.basic,
      title: this.cleanData.testTripTitle(),
      startDate: this.cleanData.futureDate(30),
      endDate: this.cleanData.futureDate(35)
    };

    const response = await this.createTestTrip(tripData);
    
    this.expectResponsePattern(response, this.fixtures.expectedResponses.tripCreated);
    
    const trip = response.body.data.trip;
    expect(trip.title).toBe(tripData.title);
    expect(trip.destination).toBe(tripData.destination);
    expect(trip.visibility).toBe(tripData.visibility);
    
    console.log(`✅ Trip created: ${trip.title} (ID: ${trip.id})`);
    return trip;
  }

  async testUpdateTrip() {
    console.log('✏️ Testing trip updates...');
    
    // Create initial trip
    const trip = await this.testCreateTrip();
    
    const updateData = {
      title: 'Updated ' + trip.title,
      description: 'Updated description for testing',
      destination: 'Updated Destination, Country',
      visibility: 'public' as const
    };

    const updateResponse = await this.server.put(`/api/v1/trips/${trip.id}`, updateData);
    expect(updateResponse.status).toBe(200);
    
    const updatedTrip = updateResponse.body.data.trip;
    expect(updatedTrip.title).toBe(updateData.title);
    expect(updatedTrip.description).toBe(updateData.description);
    expect(updatedTrip.destination).toBe(updateData.destination);
    expect(updatedTrip.visibility).toBe(updateData.visibility);
    
    console.log('✅ Trip updated successfully');
    return updatedTrip;
  }

  async testListTrips() {
    console.log('📋 Testing trip listing...');
    
    // Create multiple trips
    const trips = await Promise.all([
      this.testCreateTrip(),
      this.testCreateTrip(),
      this.testCreateTrip()
    ]);

    const listResponse = await this.server.get('/api/v1/trips');
    expect(listResponse.status).toBe(200);
    
    const tripsList = listResponse.body.data.trips;
    expect(Array.isArray(tripsList)).toBe(true);
    expect(tripsList.length).toBeGreaterThanOrEqual(3);
    
    // Verify all our test trips are in the list
    const testTripIds = trips.map(t => t.id);
    const listedTripIds = tripsList.map((t: any) => t.id);
    
    testTripIds.forEach(id => {
      expect(listedTripIds).toContain(id);
    });
    
    console.log(`✅ Listed ${tripsList.length} trips`);
    return tripsList;
  }

  async testTripVisibility() {
    console.log('👁️ Testing trip visibility controls...');
    
    // Create private trip
    const privateTrip = await this.createTestTrip({
      ...this.fixtures.trips.basic,
      title: 'Private ' + this.cleanData.testTripTitle(),
      visibility: 'private'
    });

    // Create public trip  
    const publicTrip = await this.createTestTrip({
      ...this.fixtures.trips.public,
      title: 'Public ' + this.cleanData.testTripTitle(),
      visibility: 'public'
    });

    // Private trip should only be visible to owner
    const privateResponse = await this.server.get(`/api/v1/trips/${privateTrip.body.data.trip.id}`);
    expect(privateResponse.status).toBe(200);
    
    // Public trip should be accessible via public endpoint
    const publicResponse = await this.server.get(`/api/v1/public/trips/${publicTrip.body.data.trip.id}`);
    expect(publicResponse.status).toBe(200);
    
    console.log('✅ Visibility controls working correctly');
  }

  async testDeleteTrip() {
    console.log('🗑️ Testing trip deletion...');
    
    const trip = await this.testCreateTrip();
    
    // Add some activities to test cascade deletion
    await this.addActivityToTrip(trip.id, this.fixtures.activities.museum);
    await this.addActivityToTrip(trip.id, this.fixtures.activities.restaurant);
    
    // Delete the trip
    const deleteResponse = await this.server.delete(`/api/v1/trips/${trip.id}`);
    expect(deleteResponse.status).toBe(200);
    
    // Verify trip is gone
    const getResponse = await this.server.get(`/api/v1/trips/${trip.id}`);
    expect(getResponse.status).toBe(404);
    
    // Verify activities are also gone (cascade delete)
    const activitiesResponse = await this.server.get(`/api/v1/trips/${trip.id}/activities`);
    expect(activitiesResponse.status).toBe(404);
    
    console.log('✅ Trip and related data deleted successfully');
  }

  async testTripPagination() {
    console.log('📄 Testing trip pagination...');
    
    // Create multiple trips for pagination testing
    const trips = [];
    for (let i = 0; i < 15; i++) {
      const trip = await this.createTestTrip({
        ...this.fixtures.trips.basic,
        title: `Pagination Test Trip ${i + 1}`,
        startDate: this.cleanData.futureDate(i * 5)
      });
      trips.push(trip.body.data.trip);
    }

    // Test first page
    const page1Response = await this.server.get('/api/v1/trips?page=1&limit=10');
    expect(page1Response.status).toBe(200);
    
    const page1Data = page1Response.body.data;
    expect(page1Data.trips).toHaveLength(10);
    expect(page1Data.pagination.page).toBe(1);
    expect(page1Data.pagination.totalPages).toBeGreaterThanOrEqual(2);
    
    // Test second page
    const page2Response = await this.server.get('/api/v1/trips?page=2&limit=10');
    expect(page2Response.status).toBe(200);
    
    const page2Data = page2Response.body.data;
    expect(page2Data.trips.length).toBeGreaterThan(0);
    expect(page2Data.pagination.page).toBe(2);
    
    console.log('✅ Pagination working correctly');
  }

  async testTripFiltering() {
    console.log('🔍 Testing trip filtering...');
    
    // Create trips with different destinations
    const parisTrip = await this.createTestTrip({
      ...this.fixtures.trips.basic,
      title: 'Paris Filter Test',
      destination: 'Paris, France'
    });

    const romeTrip = await this.createTestTrip({
      ...this.fixtures.trips.basic, 
      title: 'Rome Filter Test',
      destination: 'Rome, Italy'
    });

    // Filter by destination
    const parisResponse = await this.server.get('/api/v1/trips?destination=Paris');
    expect(parisResponse.status).toBe(200);
    
    const parisTrips = parisResponse.body.data.trips;
    expect(parisTrips.some((t: any) => t.id === parisTrip.body.data.trip.id)).toBe(true);
    
    // Filter by date range
    const futureDate = this.cleanData.futureDate(20);
    const dateResponse = await this.server.get(`/api/v1/trips?startDate=${futureDate.toISOString().split('T')[0]}`);
    expect(dateResponse.status).toBe(200);
    
    console.log('✅ Filtering working correctly');
  }

  async testAuthenticationRequired() {
    console.log('🔒 Testing trip authentication requirements...');
    
    const tripData = this.fixtures.trips.basic;
    
    await this.testAuthRequired('/api/v1/trips', 'POST', tripData);
    await this.testAuthRequired('/api/v1/trips', 'GET');
    await this.testAuthRequired('/api/v1/trips/fake-id', 'GET');
    await this.testAuthRequired('/api/v1/trips/fake-id', 'PUT', tripData);
    await this.testAuthRequired('/api/v1/trips/fake-id', 'DELETE');
    
    console.log('✅ Authentication properly enforced');
  }

  async testTripValidation() {
    console.log('✅ Testing trip data validation...');
    
    // Test missing required fields
    const invalidTrip1 = await this.server.post('/api/v1/trips', {
      // Missing title
      description: 'Test description'
    });
    expect(invalidTrip1.status).toBe(400);
    
    // Test invalid date range
    const invalidTrip2 = await this.server.post('/api/v1/trips', {
      title: 'Test Trip',
      startDate: this.cleanData.futureDate(10),
      endDate: this.cleanData.futureDate(5) // End before start
    });
    expect(invalidTrip2.status).toBe(400);
    
    // Test invalid visibility value
    const invalidTrip3 = await this.server.post('/api/v1/trips', {
      title: 'Test Trip',
      visibility: 'invalid'
    });
    expect(invalidTrip3.status).toBe(400);
    
    console.log('✅ Validation working correctly');
  }
}

describe('Trip Management Flow - Integration Tests', () => {
  let test: TripManagementFlowTest;

  beforeAll(async () => {
    test = new TripManagementFlowTest();
    await test.setupTest();
  });

  afterAll(async () => {
    await test.teardownTest();
  });

  afterEach(async () => {
    await test.cleanupBetweenTests();
  });

  it('should create a new trip with all required fields', async () => {
    const trip = await test.testCreateTrip();
    expect(trip.id).toBeDefined();
    expect(trip.title).toBeDefined();
    expect(trip.destination).toBeDefined();
  });

  it('should update trip details', async () => {
    const updatedTrip = await test.testUpdateTrip();
    expect(updatedTrip.title).toContain('Updated');
  });

  it('should list user trips with pagination', async () => {
    const trips = await test.testListTrips();
    expect(trips.length).toBeGreaterThanOrEqual(3);
  });

  it('should handle trip visibility (private/public)', async () => {
    await test.testTripVisibility();
  });

  it('should delete trip and cascade to activities', async () => {
    await test.testDeleteTrip();
  });

  it('should support pagination for trip lists', async () => {
    await test.testTripPagination();
  });

  it('should filter trips by destination and date', async () => {
    await test.testTripFiltering();
  });

  it('should require authentication for all trip operations', async () => {
    await test.testAuthenticationRequired();
  });

  it('should validate trip data on create and update', async () => {
    await test.testTripValidation();
  });

  it('should handle trip ownership correctly', async () => {
    // Create trip
    const trip = await test.testCreateTrip();
    
    // Verify user can access their own trip
    const ownTripResponse = await test.server.get(`/api/v1/trips/${trip.id}`);
    expect(ownTripResponse.status).toBe(200);
    expect(ownTripResponse.body.data.trip.userId).toBeDefined();
  });

  it('should support trip cloning', async () => {
    // Create original trip with activities
    const originalTrip = await test.testCreateTrip();
    await test.addActivityToTrip(originalTrip.id, test.fixtures.activities.museum);
    await test.addActivityToTrip(originalTrip.id, test.fixtures.activities.restaurant);
    
    // Clone the trip
    const cloneResponse = await test.server.post(`/api/v1/trips/${originalTrip.id}/clone`);
    
    if (cloneResponse.status === 200) {
      const clonedTrip = cloneResponse.body.data.trip;
      expect(clonedTrip.id).not.toBe(originalTrip.id);
      expect(clonedTrip.title).toContain('Copy');
      
      // Track for cleanup
      test.cleanup.trackTestData('trips', clonedTrip.id);
      
      console.log('✅ Trip cloning supported');
    } else {
      console.log('ℹ️ Trip cloning not yet implemented');
    }
  });
});