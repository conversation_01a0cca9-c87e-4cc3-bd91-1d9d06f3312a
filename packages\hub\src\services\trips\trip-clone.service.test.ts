import { describe, it, expect, vi, beforeEach } from 'vitest';
import { TripCloneService } from './trip-clone.service';
import { getSupabaseClient, handleSupabaseError, TABLES, TripSchema, validateDatabaseResponse } from '../../lib/supabase';
import { logger } from '../../utils/logger';
import type { CloneTripResult } from '../types';

// Mock dependencies
vi.mock('../../../lib/supabase', () => ({
  getSupabaseClient: vi.fn(),
  handleSupabaseError: vi.fn(),
  validateDatabaseResponse: vi.fn(),
  TripSchema: {},
  TABLES: {
    TRIPS: 'trips',
    ACTIVITIES: 'activities',
  },
}));

vi.mock('../../../utils/logger', () => ({
  logger: {
    info: vi.fn(),
    error: vi.fn(),
    debug: vi.fn(),
  },
}));

describe('TripCloneService', () => {
  let service: TripCloneService;
  let mockSupabase: any;

  const mockSourceTrip = {
    id: 'source123',
    user_id: 'author123',
    title: 'Original Trip',
    description: 'Original description',
    destination: 'Paris',
    start_date: '2024-01-01',
    end_date: '2024-01-07',
    status: 'completed',
    visibility: 'public',
    cover_image: 'cover.jpg',
    metadata: { key: 'value' },
    tags: ['travel', 'europe'],
    budget_amount: 1000,
    budget_currency: 'EUR',
  };

  const mockSourceActivities = [
    {
      id: 'activity1',
      trip_id: 'source123',
      title: 'Visit Eiffel Tower',
      description: 'See the tower',
      type: 'activity',
      start_time: '2024-01-01T10:00:00Z',
      end_time: '2024-01-01T12:00:00Z',
      location: 'Paris',
      location_lat: 48.8566,
      location_lng: 2.3522,
      price: 25,
      currency: 'EUR',
      booking_reference: 'personal-ref-123',
      booking_url: 'https://example.com',
      notes: 'Bring camera',
      attachments: ['photo1.jpg'],
      position: 0,
    },
    {
      id: 'activity2',
      trip_id: 'source123',
      title: 'Louvre Museum',
      description: 'Art museum',
      type: 'activity',
      position: 1,
    },
  ];

  const mockClonedTrip = {
    id: 'cloned123',
    user_id: 'user123',
    title: 'Copy of Original Trip',
    description: 'Original description',
    destination: 'Paris',
    start_date: '2024-01-01',
    end_date: '2024-01-07',
    status: 'draft',
    visibility: 'private',
    cover_image: 'cover.jpg',
    metadata: { key: 'value' },
    tags: ['travel', 'europe'],
    budget_amount: 1000,
    budget_currency: 'EUR',
    cloned_from_trip_id: 'source123',
    cloned_from_user_id: 'author123',
  };

  beforeEach(() => {
    vi.clearAllMocks();
    service = new TripCloneService();
    
    // Create a comprehensive mock for Supabase client
    mockSupabase = {
      from: vi.fn().mockReturnThis(),
      select: vi.fn().mockReturnThis(),
      insert: vi.fn().mockReturnThis(),
      eq: vi.fn().mockReturnThis(),
      order: vi.fn().mockReturnThis(),
      single: vi.fn().mockResolvedValue({ data: null, error: null }),
      rpc: vi.fn().mockResolvedValue({ data: null, error: null }),
    };

    // Create a factory for method chains
    const createMethodChain = (terminal: any) => {
      const chain = {
        from: vi.fn().mockReturnThis(),
        select: vi.fn().mockReturnThis(),
        insert: vi.fn().mockReturnThis(),
        eq: vi.fn().mockReturnThis(),
        order: vi.fn().mockReturnValue(terminal),
        single: vi.fn().mockReturnValue(terminal),
      };
      
      // Make each method return the chain
      Object.keys(chain).forEach(method => {
        if (method !== 'order' && method !== 'single') {
          chain[method].mockReturnValue(chain);
        }
      });
      
      return chain;
    };

    // Store for custom implementations
    mockSupabase._createMethodChain = createMethodChain;

    vi.mocked(getSupabaseClient).mockReturnValue(mockSupabase);
    vi.mocked(handleSupabaseError).mockImplementation((error) => ({
      message: error?.message || 'Unknown error',
      code: error?.code,
    }));
    vi.mocked(validateDatabaseResponse).mockImplementation((schema, data) => data);
  });

  describe('cloneTrip', () => {
    it('should successfully clone a public trip with activities', async () => {
      // Setup sequence of calls
      let callIndex = 0;
      const mockCalls = [
        // First call: get source trip - ends with .single()
        { terminal: 'single', data: mockSourceTrip, error: null },
        // Second call: get activities - ends with .order()
        { terminal: 'order', data: mockSourceActivities, error: null },
        // Third call: create new trip - ends with .single()
        { terminal: 'single', data: mockClonedTrip, error: null },
        // Fourth call: clone activities - .insert().select() (select is terminal)
        { terminal: 'select', data: [
          { id: 'new_activity1', trip_id: 'cloned123' },
          { id: 'new_activity2', trip_id: 'cloned123' },
        ], error: null },
        // Fifth call: track clone - .insert() (insert is terminal)
        { terminal: 'insert', error: null }
      ];

      // Override from() to return appropriate chains for each call
      mockSupabase.from.mockImplementation((table: string) => {
        const callData = mockCalls[callIndex++];
        if (!callData) return mockSupabase;
        
        const chain = mockSupabase._createMethodChain(
          Promise.resolve({ data: callData.data, error: callData.error })
        );
        
        // Special handling for terminal methods
        if (callData.terminal === 'select' && table === TABLES.ACTIVITIES) {
          // For activities insert, select is terminal
          chain.select.mockReturnValue(
            Promise.resolve({ data: callData.data, error: callData.error })
          );
        } else if (callData.terminal === 'insert' && table === 'trip_clones') {
          // For trip_clones, insert is terminal
          chain.insert.mockReturnValue(
            Promise.resolve({ error: callData.error })
          );
        }
        
        return chain;
      });

      const result = await service.cloneTrip('source123', 'user123');

      expect(result.trip).toEqual(mockClonedTrip);
      expect(result.activitiesCloned).toBe(2);
      expect(result.sourceTrip).toEqual({
        id: 'source123',
        title: 'Original Trip',
        authorId: 'author123',
      });

      // Verify trip was created with correct data
      expect(mockSupabase.from).toHaveBeenCalledWith(TABLES.TRIPS);
      expect(mockSupabase.from).toHaveBeenCalledWith(TABLES.ACTIVITIES);
    });

    it('should use custom title when provided', async () => {
      // Setup mock returns
      mockSupabase.single
        .mockResolvedValueOnce({ data: mockSourceTrip, error: null })
        .mockResolvedValueOnce({ data: { ...mockClonedTrip, title: 'My Custom Trip' }, error: null });
      
      mockSupabase.order.mockResolvedValueOnce({ data: [], error: null });
      
      // For trip_clones insert
      let fromCount = 0;
      mockSupabase.from.mockImplementation((table: string) => {
        fromCount++;
        if (fromCount === 4 && table === 'trip_clones') {
          return {
            ...mockSupabase,
            insert: vi.fn(() => Promise.resolve({ error: null }))
          };
        }
        return mockSupabase;
      });

      await service.cloneTrip('source123', 'user123', 'My Custom Trip');

      // Find the insert call that has the title
      const insertCalls = mockSupabase.insert.mock.calls;
      const titleInsert = insertCalls.find(call => call[0]?.title === 'My Custom Trip');
      expect(titleInsert).toBeTruthy();
      expect(titleInsert[0]).toMatchObject({
        title: 'My Custom Trip',
      });
    });

    it('should throw error if source trip is not public', async () => {
      // Mock source trip query (private trip)
      mockSupabase.single.mockResolvedValueOnce({
        data: null,
        error: { message: 'Not found' },
      });

      await expect(
        service.cloneTrip('source123', 'user123')
      ).rejects.toThrow('Source trip not found or not public');
    });

    it('should throw error if source trip does not exist', async () => {
      // Mock source trip query (not found)
      mockSupabase.single.mockResolvedValueOnce({
        data: null,
        error: { message: 'Not found' },
      });

      await expect(
        service.cloneTrip('source123', 'user123')
      ).rejects.toThrow('Source trip not found or not public');
    });

    it('should handle activities query error', async () => {
      // Mock source trip query (success)
      mockSupabase.single.mockResolvedValueOnce({
        data: mockSourceTrip,
        error: null,
      });

      // Mock activities query (error)
      mockSupabase.order.mockResolvedValueOnce({
        data: null,
        error: { message: 'Activities query failed' },
      });

      await expect(
        service.cloneTrip('source123', 'user123')
      ).rejects.toThrow('Failed to clone trip');
    });

    it('should handle trip creation error', async () => {
      // Mock source trip query (success)
      mockSupabase.single.mockResolvedValueOnce({
        data: mockSourceTrip,
        error: null,
      });

      // Mock activities query (success)
      mockSupabase.order.mockResolvedValueOnce({
        data: mockSourceActivities,
        error: null,
      });

      // Mock trip creation (error)
      mockSupabase.single.mockResolvedValueOnce({
        data: null,
        error: { message: 'Creation failed' },
      });

      await expect(
        service.cloneTrip('source123', 'user123')
      ).rejects.toThrow('Failed to create cloned trip');
    });

    it.skip('should handle partial activity cloning failure gracefully', async () => {
      // Clear all mocks to ensure clean state
      vi.clearAllMocks();
      
      // Mock source trip query
      mockSupabase.single
        .mockResolvedValueOnce({ data: mockSourceTrip, error: null })
        .mockResolvedValueOnce({ data: mockClonedTrip, error: null });
      
      mockSupabase.order.mockResolvedValueOnce({ data: mockSourceActivities, error: null });
      
      // Mock activities cloning failure
      let fromCount = 0;
      mockSupabase.from.mockImplementation((table: string) => {
        fromCount++;
        if (fromCount === 3 && table === TABLES.ACTIVITIES) {
          return {
            ...mockSupabase,
            insert: vi.fn().mockReturnValue({
              select: vi.fn(() => Promise.resolve({ 
                data: null, 
                error: { 
                  message: 'Some activities failed', 
                  code: 'PARTIAL_FAIL', 
                  details: 'Details' 
                } 
              }))
            })
          };
        }
        if (fromCount === 4 && table === 'trip_clones') {
          return {
            ...mockSupabase,
            insert: vi.fn(() => Promise.resolve({ error: null }))
          };
        }
        return mockSupabase;
      });

      const result = await service.cloneTrip('source123', 'user123');

      expect(result.trip).toEqual(mockClonedTrip);
      expect(result.activitiesCloned).toBe(0);
      
      // Check that logger.error was called
      // The error is logged synchronously, so no need to wait
      const loggerCalls = vi.mocked(logger.error).mock.calls;
      console.log('Logger calls:', loggerCalls.length);
      console.log('Logger calls content:', loggerCalls);
      
      expect(logger.error).toHaveBeenCalledWith(
        'Failed to clone some activities:', 
        expect.objectContaining({
          error: 'Some activities failed',
          code: 'PARTIAL_FAIL',
          details: 'Details'
        })
      );
    });

    it.skip('should handle clone tracking failure gracefully', async () => {
      // Clear all mocks to ensure clean state
      vi.clearAllMocks();
      
      mockSupabase.single
        .mockResolvedValueOnce({ data: mockSourceTrip, error: null })
        .mockResolvedValueOnce({ data: mockClonedTrip, error: null });
      
      mockSupabase.order.mockResolvedValueOnce({ data: [], error: null });
      
      // Mock clone tracking failure
      let fromCount = 0;
      mockSupabase.from.mockImplementation((table: string) => {
        fromCount++;
        if (fromCount === 3 && table === 'trip_clones') {
          return {
            ...mockSupabase,
            insert: vi.fn(() => Promise.reject(new Error('Tracking failed')))
          };
        }
        return mockSupabase;
      });

      const result = await service.cloneTrip('source123', 'user123');

      expect(result.trip).toEqual(mockClonedTrip);
      
      // Wait a tick for logger to be called
      await new Promise(resolve => setImmediate(resolve));
      
      // Check that logger.error was called
      expect(logger.error).toHaveBeenCalled();
      const errorCall = vi.mocked(logger.error).mock.calls[0];
      expect(errorCall[0]).toBe('Failed to track clone event:');
      expect(errorCall[1]).toMatchObject({ error: 'Tracking failed' });
    });

    it('should clone trip without activities', async () => {
      mockSupabase.single
        .mockResolvedValueOnce({ data: mockSourceTrip, error: null })
        .mockResolvedValueOnce({ data: mockClonedTrip, error: null });
      
      mockSupabase.order.mockResolvedValueOnce({ data: [], error: null });
      
      // Mock clone tracking
      let fromCount = 0;
      mockSupabase.from.mockImplementation((table: string) => {
        fromCount++;
        if (fromCount === 3 && table === 'trip_clones') {
          return {
            ...mockSupabase,
            insert: vi.fn(() => Promise.resolve({ error: null }))
          };
        }
        return mockSupabase;
      });

      const result = await service.cloneTrip('source123', 'user123');

      expect(result.trip).toEqual(mockClonedTrip);
      expect(result.activitiesCloned).toBe(0);
    });

    it('should strip personal data from cloned activities', async () => {
      mockSupabase.single
        .mockResolvedValueOnce({ data: mockSourceTrip, error: null })
        .mockResolvedValueOnce({ data: mockClonedTrip, error: null });
      
      mockSupabase.order.mockResolvedValueOnce({ data: mockSourceActivities, error: null });
      
      // Capture the insert call for activities
      let activitiesInsertData: any = null;
      
      // Override insert to capture data before mocking  
      const originalInsert = mockSupabase.insert;
      mockSupabase.insert = vi.fn((data) => {
        // Capture data if it's an array (activities)
        if (Array.isArray(data)) {
          activitiesInsertData = data;
        }
        return originalInsert.call(mockSupabase, data);
      });
      
      let fromCount = 0;
      mockSupabase.from.mockImplementation((table: string) => {
        fromCount++;
        if (fromCount === 3 && table === TABLES.ACTIVITIES) {
          return {
            ...mockSupabase,
            insert: vi.fn((data) => {
              activitiesInsertData = data;
              return {
                select: vi.fn(() => Promise.resolve({ 
                  data: data.map((d: any, i: number) => ({ ...d, id: `new_activity${i}` })), 
                  error: null 
                }))
              };
            })
          };
        }
        if (fromCount === 4 && table === 'trip_clones') {
          return {
            ...mockSupabase,
            insert: vi.fn(() => Promise.resolve({ error: null }))
          };
        }
        return mockSupabase;
      });

      await service.cloneTrip('source123', 'user123');

      expect(activitiesInsertData).toBeTruthy();
      expect(activitiesInsertData[0]).toMatchObject({
        trip_id: 'cloned123',
        title: 'Visit Eiffel Tower',
        booking_reference: null, // Should be null
        attachments: [], // Should be empty
      });
    });

    it('should preserve non-personal activity data', async () => {
      mockSupabase.single
        .mockResolvedValueOnce({ data: mockSourceTrip, error: null })
        .mockResolvedValueOnce({ data: mockClonedTrip, error: null });
      
      mockSupabase.order.mockResolvedValueOnce({ data: mockSourceActivities, error: null });
      
      // Capture the insert call for activities
      let activitiesInsertData: any = null;
      
      // Override insert to capture data before mocking  
      const originalInsert = mockSupabase.insert;
      mockSupabase.insert = vi.fn((data) => {
        // Capture data if it's an array (activities)
        if (Array.isArray(data)) {
          activitiesInsertData = data;
        }
        return originalInsert.call(mockSupabase, data);
      });
      
      let fromCount = 0;
      mockSupabase.from.mockImplementation((table: string) => {
        fromCount++;
        if (fromCount === 3 && table === TABLES.ACTIVITIES) {
          return {
            ...mockSupabase,
            insert: vi.fn((data) => {
              activitiesInsertData = data;
              return {
                select: vi.fn(() => Promise.resolve({ 
                  data: data.map((d: any, i: number) => ({ ...d, id: `new_activity${i}` })), 
                  error: null 
                }))
              };
            })
          };
        }
        if (fromCount === 4 && table === 'trip_clones') {
          return {
            ...mockSupabase,
            insert: vi.fn(() => Promise.resolve({ error: null }))
          };
        }
        return mockSupabase;
      });

      await service.cloneTrip('source123', 'user123');

      expect(activitiesInsertData).toBeTruthy();
      expect(activitiesInsertData[0]).toMatchObject({
        title: 'Visit Eiffel Tower',
        description: 'See the tower',
        type: 'activity',
        location: 'Paris',
        location_lat: 48.8566,
        location_lng: 2.3522,
        price: 25,
        currency: 'EUR',
        booking_url: 'https://example.com',
        notes: 'Bring camera',
        position: 0,
      });
    });
  });
});