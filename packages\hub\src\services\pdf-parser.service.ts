import { logger } from '../utils/logger';
import { getAIParserService } from './ai-parser.service';
import { cacheService } from './cache.service';

export interface TravelItinerary {
  title: string;
  days: number;
  locations: string[];
  activities: Array<{
    day: number;
    title: string;
    location?: string;
  }>;
}

export interface PDFParseResult {
  sessionId: string;
  content: string;
  source: 'chatgpt' | 'claude' | 'gemini' | 'unknown';
  metadata: {
    pageCount: number;
    title?: string;
    author?: string;
    subject?: string;
    keywords?: string;
    creationDate?: Date;
    modificationDate?: Date;
  };
}

interface PDFData {
  text: string;
  pages?: number;
  numpages?: number;
  info?: Record<string, unknown>;
}

export class PDFParserService {
  private readonly aiParserService = getAIParserService();
  /**
   * Validates if a buffer is a valid PDF
   */
  validatePDF(buffer: Buffer): boolean {
    try {
      if (!buffer || buffer.length === 0) {
        return false;
      }

      // Check PDF header
      const header = buffer.toString('utf8', 0, 5);
      return header === '%PDF-';
    } catch (error) {
      logger.error('PDF validation error', { error });
      return false;
    }
  }

  /**
   * Extracts text content from PDF buffer
   */
  async extractText(buffer: Buffer): Promise<string> {
    try {
      if (!this.validatePDF(buffer)) {
        throw new Error('Invalid PDF format');
      }

      // Create a wrapper to handle the path issue
      const pdfParse = await this.loadPdfParse();
      const data = await pdfParse(buffer) as PDFData;
      const text = data.text || '';

      // Clean up the text
      const cleanedText = this.cleanText(text);
      logger.info('PDF text extracted successfully', { 
        pages: data.pages || data.numpages || undefined,
        textLength: cleanedText.length 
      });

      return cleanedText;
    } catch (error) {
      logger.error('PDF extraction error', { error });
      throw new Error(`Failed to extract text from PDF: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Extracts structured travel itinerary from PDF buffer
   */
  async extractTravelItinerary(buffer: Buffer): Promise<TravelItinerary> {
    const text = await this.extractText(buffer);
    
    // Extract title
    const titleMatch = text.match(/([^\n]+(?:itinerary|trip|travel|tour)[^\n]*)/i);
    const title = titleMatch ? titleMatch[1].trim() : 'Imported Trip';

    // Extract days
    const dayMatches = [...text.matchAll(/day\s*(\d+)/gi)];
    const days = dayMatches.length > 0 
      ? Math.max(...dayMatches.map(m => parseInt(m[1], 10)))
      : 0;

    // Extract locations
    const locationMatches = new Set<string>();
    const locationPatterns = [
      /(?:in|at|to|visit)\s+([A-Z][a-zA-Z\s]+?)(?:[,.\n])/g,
      /([A-Z][a-zA-Z]+(?:\s+[A-Z][a-zA-Z]+)*),\s*[A-Z]{2,}/g, // City, Country/State
    ];

    locationPatterns.forEach(pattern => {
      const matches = [...text.matchAll(pattern)];
      matches.forEach(match => {
        const location = match[1].trim();
        if (location.length > 2 && location.length < 50) {
          locationMatches.add(location);
        }
      });
    });

    const locations = Array.from(locationMatches);

    // Extract activities
    const activities: TravelItinerary['activities'] = [];
    const activityPatterns = [
      /day\s*(\d+)[:\s]+([^\n]+)/gi,
      /(\d+)[:\s]*(?:AM|PM|am|pm)?[:\s]+([^\n]+)/gi,
      /(?:morning|afternoon|evening)[:\s]+([^\n]+)/gi,
    ];

    activityPatterns.forEach(pattern => {
      const matches = [...text.matchAll(pattern)];
      matches.forEach(match => {
        const day = match[1] ? parseInt(match[1], 10) : 1;
        const title = (match[2] || match[1]).trim();
        
        if (title && title.length > 3) {
          activities.push({ day, title });
        }
      });
    });

    return {
      title,
      days,
      locations,
      activities
    };
  }

  /**
   * Cleans extracted text by removing extra whitespace and formatting
   */
  private cleanText(text: string): string {
    return text
      // Replace multiple spaces with single space
      .replace(/\s+/g, ' ')
      // Replace multiple newlines with double newline
      .replace(/\n{3,}/g, '\n\n')
      // Trim each line
      .split('\n')
      .map(line => line.trim())
      .join('\n')
      // Remove empty lines at start and end
      .trim();
  }

  /**
   * Parse PDF buffer and extract conversation content
   */
  async parsePDF(
    buffer: Buffer,
    userId: string,
    filename?: string
  ): Promise<PDFParseResult> {
    try {
      // Validate PDF
      if (!this.validatePDF(buffer)) {
        throw new Error('Invalid PDF format');
      }

      // Extract text
      const text = await this.extractText(buffer);
      
      if (!text || text.trim().length === 0) {
        throw new Error('PDF contains no extractable text');
      }

      // Detect source platform
      const source = this.detectAISource(text);
      
      // Extract conversation
      const content = this.extractConversation(text);
      
      // Create parse session
      const sessionId = await this.aiParserService.createParseSession(
        content,
        source,
        userId
      );

      // Get PDF metadata
      const pdfParse = await this.loadPdfParse();
      const data = await pdfParse(buffer) as PDFData;

      // Cache PDF metadata
      await cacheService.set(`pdf:metadata:${sessionId}`, {
        filename,
        pageCount: data.numpages || data.pages || 0,
        source,
        uploadedAt: new Date().toISOString()
      }, { ttl: 3600 }); // 1 hour

      return {
        sessionId,
        content,
        source,
        metadata: {
          pageCount: data.numpages || data.pages || 0,
          title: data.info?.Title as string | undefined,
          author: data.info?.Author as string | undefined,
          subject: data.info?.Subject as string | undefined,
          keywords: data.info?.Keywords as string | undefined,
          creationDate: data.info?.CreationDate ? new Date(data.info.CreationDate as string) : undefined,
          modificationDate: data.info?.ModificationDate ? new Date(data.info.ModificationDate as string) : undefined
        }
      };
    } catch (error) {
      logger.error('PDF parsing failed', { error, filename });
      throw new Error(
        error instanceof Error 
          ? `PDF parsing failed: ${error.message}`
          : 'Failed to parse PDF file'
      );
    }
  }

  /**
   * Detect AI platform based on content patterns
   */
  private detectAISource(text: string): 'chatgpt' | 'claude' | 'gemini' | 'unknown' {
    const lowerText = text.toLowerCase();
    
    // ChatGPT patterns
    if (
      lowerText.includes('chatgpt') ||
      lowerText.includes('openai') ||
      /user:|assistant:/i.test(text) ||
      /\bchatgpt\b/i.test(text)
    ) {
      return 'chatgpt';
    }
    
    // Claude patterns
    if (
      lowerText.includes('claude') ||
      lowerText.includes('anthropic') ||
      /human:|assistant:/i.test(text) ||
      /\bclaude\b/i.test(text)
    ) {
      return 'claude';
    }
    
    // Gemini patterns
    if (
      lowerText.includes('gemini') ||
      lowerText.includes('bard') ||
      lowerText.includes('google ai') ||
      /user:|gemini:/i.test(text) ||
      /\bgemini\b/i.test(text)
    ) {
      return 'gemini';
    }
    
    return 'unknown';
  }

  /**
   * Extract conversation from PDF text
   */
  private extractConversation(text: string): string {
    // Clean up common PDF artifacts
    let cleaned = text
      .replace(/\f/g, '\n') // Form feeds to newlines
      .replace(/\r\n/g, '\n') // Normalize line endings
      .replace(/\n{3,}/g, '\n\n') // Reduce excessive newlines
      .trim();
    
    // Try to identify conversation boundaries
    const conversationPatterns = [
      // ChatGPT patterns
      /(?:User|You):\s*.+?(?=(?:Assistant|ChatGPT):|$)/gis,
      /(?:Assistant|ChatGPT):\s*.+?(?=(?:User|You):|$)/gis,
      // Claude patterns
      /Human:\s*.+?(?=Assistant:|$)/gis,
      /Assistant:\s*.+?(?=Human:|$)/gis,
      // Gemini patterns
      /User:\s*.+?(?=Gemini:|$)/gis,
      /Gemini:\s*.+?(?=User:|$)/gis,
      // Generic Q&A patterns
      /Q:\s*.+?(?=A:|$)/gis,
      /A:\s*.+?(?=Q:|$)/gis,
    ];
    
    // Check if text contains conversation markers
    const hasConversationMarkers = conversationPatterns.some(pattern => 
      pattern.test(cleaned)
    );
    
    if (!hasConversationMarkers) {
      // If no clear conversation markers, try to infer structure
      // Look for alternating paragraphs that might be a conversation
      const paragraphs = cleaned.split(/\n\n+/);
      
      if (paragraphs.length > 2) {
        // Assume alternating paragraphs are user/assistant
        const reconstructed = paragraphs.map((para, index) => {
          const role = index % 2 === 0 ? 'User' : 'Assistant';
          return `${role}: ${para.trim()}`;
        }).join('\n\n');
        
        cleaned = reconstructed;
      }
    }
    
    // Final cleanup
    cleaned = cleaned
      .replace(/\s*\[?Page \d+\]?\s*/gi, '') // Remove page numbers
      .replace(/\s*\d+\s*$/, '') // Remove trailing numbers
      .trim();
    
    return cleaned;
  }

  /**
   * Get maximum allowed file size (10MB)
   */
  getMaxFileSize(): number {
    return 10 * 1024 * 1024; // 10MB
  }

  /**
   * Load pdf-parse module with proper error handling
   */
  private async loadPdfParse(): Promise<(buffer: Buffer) => Promise<{ text: string; pages: number; info: Record<string, unknown> }>> {
    try {
      // Load pdf-parse module without directory manipulation
      
      // Load the module
      const pdf = await import('pdf-parse');
      
      // Create a wrapper function without changing directory
      return async (buffer: Buffer) => {
        try {
          // Pass buffer directly without changing directory
          const result = await pdf.default(buffer, {
            // pdf-parse options can be passed here if needed
            version: 'v1.10.100'
          });
          // Normalize the result to have consistent structure
          const pdfResult = result as PDFData;
          return {
            text: pdfResult.text,
            pages: pdfResult.numpages || pdfResult.pages || 0,
            info: pdfResult.info || {}
          };
        } catch (error) {
          logger.error('PDF parsing failed', { error });
          throw new Error('Failed to parse PDF content');
        }
      };
    } catch (error) {
      logger.error('Failed to load pdf-parse module', { error });
      throw new Error('PDF parsing module not available');
    }
  }
}

// Export singleton instance with lazy initialization
let _pdfParserService: PDFParserService | null = null;

export const getPDFParserService = (): PDFParserService => {
  if (!_pdfParserService) {
    _pdfParserService = new PDFParserService();
  }
  return _pdfParserService;
};

// For backward compatibility
export const pdfParserService = {
  get instance(): PDFParserService {
    return getPDFParserService();
  },
  // Proxy the main methods - these match the import routes expectations
  async parsePDF(buffer: Buffer, filename?: string): Promise<PDFParseResult> {
    // The parsePDF method requires userId, but import route doesn't have it at this point
    // We'll use a placeholder since the sessionId is what matters
    return getPDFParserService().parsePDF(buffer, 'import-user', filename);
  },
  validatePDF(buffer: Buffer): boolean {
    return getPDFParserService().validatePDF(buffer);
  }
};