import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { ImportController } from './import.controller';
import { ParserService, ValidationError } from '../services/parser.service';
import { TripsService } from '../services/trips.service';
import { GeocodingService } from '../services/geocoding.service';
import { ImportLockService } from '../services/import-lock.service';
import { aiRouter } from '../services/aiRouter.service';
import { Response } from 'express';
import { AuthenticatedRequest } from '../middleware/auth.middleware';
import { 
  createMockAuthRequest, 
  createMockParsedTrip,
  advanceTimeAndFlushPromises 
} from '../../test/utils/test-helpers';

vi.mock('../services/parser.service');
vi.mock('../services/trips.service');
vi.mock('../services/geocoding.service');
vi.mock('../services/import-lock.service', () => ({
  ImportLockService: {
    getInstance: vi.fn().mockReturnValue({
      acquireLock: vi.fn().mockReturnValue(true),
      releaseLock: vi.fn()
    })
  }
}));
vi.mock('../services/pdf-parser.service');
vi.mock('../services/aiRouter.service', () => ({
  aiRouter: {
    selectModel: vi.fn()
  }
}));
vi.mock('../utils/logger', () => ({
  logger: {
    error: vi.fn(),
    warn: vi.fn(),
    info: vi.fn(),
    debug: vi.fn(),
  }
}));

describe('ImportController - Enhanced Tests', () => {
  let importController: ImportController;
  let mockReq: Partial<AuthenticatedRequest>;
  let mockRes: Partial<Response>;
  let mockParserService: any;
  let mockTripsService: any;
  let mockGeocodingService: any;
  let mockImportLockService: any;

  beforeEach(() => {
    vi.clearAllMocks();
    vi.useFakeTimers();
    
    // Setup mocks
    mockParserService = {
      parseTextToTrip: vi.fn()
    };
    
    mockTripsService = {
      createTrip: vi.fn(),
      addActivityToTrip: vi.fn()
    };
    
    mockGeocodingService = {
      isAvailable: vi.fn().mockReturnValue(true),
      geocode: vi.fn(),
      geocodeBatch: vi.fn()
    };

    mockImportLockService = ImportLockService.getInstance();

    // Mock the service constructors
    vi.mocked(ParserService).mockImplementation(() => mockParserService as any);
    vi.mocked(TripsService).mockImplementation(() => mockTripsService as any);
    vi.mocked(GeocodingService).mockImplementation(() => mockGeocodingService as any);

    importController = new ImportController();

    mockReq = {
      user: { id: 'user-123' },
      body: {
        text: 'Trip to Paris\\nDay 1: Visit Eiffel Tower',
        source: 'chatgpt'
      }
    };

    mockRes = {
      status: vi.fn().mockReturnThis(),
      json: vi.fn()
    };
  });

  afterEach(() => {
    vi.useRealTimers();
  });

  describe('AI Router Integration', () => {
    it('should use AI Router for model selection with quality preference', async () => {
      const mockModelSelection = {
        modelId: 'gemini-2.0-flash-exp',
        reasoning: 'Free tier available with high quality',
        config: {
          id: 'gemini-2.0-flash-exp',
          name: 'Gemini Flash 2.0',
          provider: 'google'
        }
      };
      
      vi.mocked(aiRouter.selectModel).mockReturnValue(mockModelSelection);
      
      const parsedTrip = createMockParsedTrip();
      mockParserService.parseTextToTrip.mockResolvedValue(parsedTrip);
      mockTripsService.createTrip.mockResolvedValue({ id: 'trip-123' });
      mockTripsService.addActivityToTrip.mockResolvedValue({ id: 'activity-1' });
      mockGeocodingService.geocodeBatch.mockResolvedValue(new Map());

      await importController.parseAndCreateTrip(mockReq as AuthenticatedRequest, mockRes as Response);

      // Verify AI Router was called with correct parameters
      expect(aiRouter.selectModel).toHaveBeenCalledWith('itinerary', {
        preferQuality: true
      });

      // Verify parser was called with selected model
      expect(mockParserService.parseTextToTrip).toHaveBeenCalledWith(
        mockReq.body.text,
        'chatgpt',
        'gemini-2.0-flash-exp'
      );

      // Verify logging
      expect(vi.mocked(console.info)).toHaveBeenCalledWith(
        expect.stringContaining('Starting AI import'),
        expect.objectContaining({
          selectedModel: 'gemini-2.0-flash-exp',
          reasoning: 'Free tier available with high quality'
        })
      );
    });

    it('should handle AI Router errors gracefully', async () => {
      vi.mocked(aiRouter.selectModel).mockImplementation(() => {
        throw new Error('Model selection failed');
      });

      await importController.parseAndCreateTrip(mockReq as AuthenticatedRequest, mockRes as Response);

      expect(mockRes.status).toHaveBeenCalledWith(500);
      expect(mockRes.json).toHaveBeenCalledWith(
        expect.objectContaining({
          success: false,
          error: 'Model selection failed'
        })
      );
    });
  });

  describe('Batch Geocoding Edge Cases', () => {
    it('should handle partial geocoding failures gracefully', async () => {
      const parsedTrip = createMockParsedTrip({
        activities: [
          {
            title: 'Visit Eiffel Tower',
            location: 'Eiffel Tower, Paris',
            day: 1,
            type: 'activity'
          },
          {
            title: 'Mystery Location',
            location: 'InvalidLocation123XYZ',
            day: 1,
            type: 'activity'
          },
          {
            title: 'Louvre Museum',
            location: 'Louvre Museum, Paris',
            day: 2,
            type: 'activity'
          }
        ]
      });

      const geocodingMap = new Map([
        ['Eiffel Tower, Paris', { lat: 48.8584, lng: 2.2945 }],
        ['InvalidLocation123XYZ', null], // Failed to geocode
        ['Louvre Museum, Paris', { lat: 48.8606, lng: 2.3376 }]
      ]);

      mockParserService.parseTextToTrip.mockResolvedValue(parsedTrip);
      mockTripsService.createTrip.mockResolvedValue({ id: 'trip-123' });
      mockTripsService.addActivityToTrip.mockResolvedValue({ id: 'activity-1' });
      mockGeocodingService.geocodeBatch.mockResolvedValue(geocodingMap);
      vi.mocked(aiRouter.selectModel).mockReturnValue({ modelId: 'test-model' });

      await importController.parseAndCreateTrip(mockReq as AuthenticatedRequest, mockRes as Response);

      // Verify all activities were created
      expect(mockTripsService.addActivityToTrip).toHaveBeenCalledTimes(3);

      // Verify activities with successful geocoding have coordinates
      expect(mockTripsService.addActivityToTrip).toHaveBeenCalledWith(
        'trip-123',
        'user-123',
        expect.objectContaining({
          title: 'Visit Eiffel Tower',
          locationLat: 48.8584,
          locationLng: 2.2945
        })
      );

      // Verify failed geocoding results in undefined coordinates
      expect(mockTripsService.addActivityToTrip).toHaveBeenCalledWith(
        'trip-123',
        'user-123',
        expect.objectContaining({
          title: 'Mystery Location',
          location: 'InvalidLocation123XYZ',
          locationLat: undefined,
          locationLng: undefined
        })
      );

      // Verify logging
      expect(vi.mocked(console.info)).toHaveBeenCalledWith(
        expect.stringContaining('Batch geocoding completed'),
        expect.objectContaining({
          total: 3,
          successful: 2
        })
      );
    });

    it('should handle geocoding service failures without blocking import', async () => {
      const parsedTrip = createMockParsedTrip();
      
      mockParserService.parseTextToTrip.mockResolvedValue(parsedTrip);
      mockTripsService.createTrip.mockResolvedValue({ id: 'trip-123' });
      mockTripsService.addActivityToTrip.mockResolvedValue({ id: 'activity-1' });
      mockGeocodingService.geocodeBatch.mockRejectedValue(new Error('Geocoding service unavailable'));
      vi.mocked(aiRouter.selectModel).mockReturnValue({ modelId: 'test-model' });

      await importController.parseAndCreateTrip(mockReq as AuthenticatedRequest, mockRes as Response);

      // Import should succeed despite geocoding failure
      expect(mockRes.status).toHaveBeenCalledWith(201);
      
      // Activities should be created without coordinates
      expect(mockTripsService.addActivityToTrip).toHaveBeenCalledWith(
        'trip-123',
        'user-123',
        expect.objectContaining({
          locationLat: undefined,
          locationLng: undefined
        })
      );

      // Error should be logged
      expect(vi.mocked(console.error)).toHaveBeenCalledWith(
        expect.stringContaining('Batch geocoding failed'),
        expect.objectContaining({ error: expect.any(Error) })
      );
    });

    it('should deduplicate locations for batch geocoding', async () => {
      const parsedTrip = createMockParsedTrip({
        activities: [
          { title: 'Morning at Eiffel', location: 'Eiffel Tower, Paris', day: 1 },
          { title: 'Evening at Eiffel', location: 'Eiffel Tower, Paris', day: 1 }, // Duplicate
          { title: 'Louvre Visit', location: 'Louvre Museum, Paris', day: 2 },
          { title: 'Another Louvre', location: 'Louvre Museum, Paris', day: 2 } // Duplicate
        ]
      });

      mockParserService.parseTextToTrip.mockResolvedValue(parsedTrip);
      mockTripsService.createTrip.mockResolvedValue({ id: 'trip-123' });
      mockTripsService.addActivityToTrip.mockResolvedValue({ id: 'activity-1' });
      mockGeocodingService.geocodeBatch.mockResolvedValue(new Map());
      vi.mocked(aiRouter.selectModel).mockReturnValue({ modelId: 'test-model' });

      await importController.parseAndCreateTrip(mockReq as AuthenticatedRequest, mockRes as Response);

      // Should only geocode unique locations
      expect(mockGeocodingService.geocodeBatch).toHaveBeenCalledWith([
        'Eiffel Tower, Paris',
        'Louvre Museum, Paris'
      ]);
    });
  });

  describe('Activity Deduplication', () => {
    it('should detect and skip exact duplicate activities', async () => {
      const parsedTrip = createMockParsedTrip({
        activities: [
          { title: 'Visit Eiffel Tower', day: 1, startTime: '9:00 AM', type: 'activity' },
          { title: 'Visit Eiffel Tower', day: 1, startTime: '9:00 AM', type: 'activity' }, // Exact duplicate
          { title: 'Louvre Museum', day: 2, startTime: '10:00 AM', type: 'activity' }
        ]
      });

      mockParserService.parseTextToTrip.mockResolvedValue(parsedTrip);
      mockTripsService.createTrip.mockResolvedValue({ id: 'trip-123' });
      mockTripsService.addActivityToTrip.mockResolvedValue({ id: 'activity-1' });
      mockGeocodingService.geocodeBatch.mockResolvedValue(new Map());
      vi.mocked(aiRouter.selectModel).mockReturnValue({ modelId: 'test-model' });

      await importController.parseAndCreateTrip(mockReq as AuthenticatedRequest, mockRes as Response);

      // Should only create 2 activities (skipping the duplicate)
      expect(mockTripsService.addActivityToTrip).toHaveBeenCalledTimes(2);

      // Verify logging of duplicate
      expect(vi.mocked(console.info)).toHaveBeenCalledWith(
        expect.stringContaining('Skipping duplicate activity'),
        expect.objectContaining({
          title: 'Visit Eiffel Tower',
          day: 1,
          startTime: '9:00 AM'
        })
      );
    });

    it('should handle case-insensitive duplicate detection', async () => {
      const parsedTrip = createMockParsedTrip({
        activities: [
          { title: 'Visit Eiffel Tower', day: 1, startTime: '9:00 AM' },
          { title: 'VISIT EIFFEL TOWER', day: 1, startTime: '9:00 AM' }, // Case variation
          { title: 'visit eiffel tower', day: 1, startTime: '9:00 AM' }, // Another case variation
        ]
      });

      mockParserService.parseTextToTrip.mockResolvedValue(parsedTrip);
      mockTripsService.createTrip.mockResolvedValue({ id: 'trip-123' });
      mockTripsService.addActivityToTrip.mockResolvedValue({ id: 'activity-1' });
      mockGeocodingService.geocodeBatch.mockResolvedValue(new Map());
      vi.mocked(aiRouter.selectModel).mockReturnValue({ modelId: 'test-model' });

      await importController.parseAndCreateTrip(mockReq as AuthenticatedRequest, mockRes as Response);

      // Should only create 1 activity
      expect(mockTripsService.addActivityToTrip).toHaveBeenCalledTimes(1);
    });

    it('should allow same activity on different days', async () => {
      const parsedTrip = createMockParsedTrip({
        activities: [
          { title: 'Morning Yoga', day: 1, startTime: '7:00 AM' },
          { title: 'Morning Yoga', day: 2, startTime: '7:00 AM' }, // Same activity, different day
          { title: 'Morning Yoga', day: 3, startTime: '7:00 AM' }, // Same activity, another day
        ]
      });

      mockParserService.parseTextToTrip.mockResolvedValue(parsedTrip);
      mockTripsService.createTrip.mockResolvedValue({ id: 'trip-123' });
      mockTripsService.addActivityToTrip.mockResolvedValue({ id: 'activity-1' });
      mockGeocodingService.geocodeBatch.mockResolvedValue(new Map());
      vi.mocked(aiRouter.selectModel).mockReturnValue({ modelId: 'test-model' });

      await importController.parseAndCreateTrip(mockReq as AuthenticatedRequest, mockRes as Response);

      // Should create all 3 activities (different days)
      expect(mockTripsService.addActivityToTrip).toHaveBeenCalledTimes(3);
    });
  });

  describe('Time Parsing Edge Cases', () => {
    it('should correctly parse 12-hour time formats', async () => {
      const testCases = [
        { input: '12:00 AM', expectedHour: 0 },    // Midnight
        { input: '12:30 AM', expectedHour: 0 },    // After midnight
        { input: '12:00 PM', expectedHour: 12 },   // Noon
        { input: '12:30 PM', expectedHour: 12 },   // After noon
        { input: '11:59 PM', expectedHour: 23 },   // Before midnight
        { input: '1:00 AM', expectedHour: 1 },     // Early morning
        { input: '1:00 PM', expectedHour: 13 },    // Afternoon
      ];

      for (const testCase of testCases) {
        vi.clearAllMocks();

        const parsedTrip = createMockParsedTrip({
          startDate: '2024-01-01',
          activities: [{
            title: 'Test Activity',
            day: 1,
            startTime: testCase.input,
            type: 'activity'
          }]
        });

        mockParserService.parseTextToTrip.mockResolvedValue(parsedTrip);
        mockTripsService.createTrip.mockResolvedValue({ id: 'trip-123' });
        mockTripsService.addActivityToTrip.mockResolvedValue({ id: 'activity-1' });
        mockGeocodingService.geocodeBatch.mockResolvedValue(new Map());
        vi.mocked(aiRouter.selectModel).mockReturnValue({ modelId: 'test-model' });

        await importController.parseAndCreateTrip(mockReq as AuthenticatedRequest, mockRes as Response);

        // Extract the startTime from the call
        const call = mockTripsService.addActivityToTrip.mock.calls[0];
        const activityData = call[2];
        const startTime = new Date(activityData.startTime);

        expect(startTime.getHours()).toBe(testCase.expectedHour);
      }
    });

    it('should handle activities without time', async () => {
      const parsedTrip = createMockParsedTrip({
        activities: [{
          title: 'All day event',
          day: 1,
          // No startTime specified
          type: 'activity'
        }]
      });

      mockParserService.parseTextToTrip.mockResolvedValue(parsedTrip);
      mockTripsService.createTrip.mockResolvedValue({ id: 'trip-123' });
      mockTripsService.addActivityToTrip.mockResolvedValue({ id: 'activity-1' });
      mockGeocodingService.geocodeBatch.mockResolvedValue(new Map());
      vi.mocked(aiRouter.selectModel).mockReturnValue({ modelId: 'test-model' });

      await importController.parseAndCreateTrip(mockReq as AuthenticatedRequest, mockRes as Response);

      expect(mockTripsService.addActivityToTrip).toHaveBeenCalledWith(
        'trip-123',
        'user-123',
        expect.objectContaining({
          startTime: undefined
        })
      );
    });

    it('should handle malformed time formats gracefully', async () => {
      const parsedTrip = createMockParsedTrip({
        activities: [{
          title: 'Activity with bad time',
          day: 1,
          startTime: 'invalid-time-format',
          type: 'activity'
        }]
      });

      mockParserService.parseTextToTrip.mockResolvedValue(parsedTrip);
      mockTripsService.createTrip.mockResolvedValue({ id: 'trip-123' });
      mockTripsService.addActivityToTrip.mockResolvedValue({ id: 'activity-1' });
      mockGeocodingService.geocodeBatch.mockResolvedValue(new Map());
      vi.mocked(aiRouter.selectModel).mockReturnValue({ modelId: 'test-model' });

      await importController.parseAndCreateTrip(mockReq as AuthenticatedRequest, mockRes as Response);

      // Should still create the activity
      expect(mockTripsService.addActivityToTrip).toHaveBeenCalled();
      
      // Should succeed despite time parsing issue
      expect(mockRes.status).toHaveBeenCalledWith(201);
    });
  });

  describe('Import Lock Edge Cases', () => {
    it('should handle lock acquisition timeout', async () => {
      // Simulate slow lock acquisition
      mockImportLockService.acquireLock.mockImplementation(() => {
        // Simulate delay
        return false;
      });

      await importController.parseAndCreateTrip(mockReq as AuthenticatedRequest, mockRes as Response);

      expect(mockRes.status).toHaveBeenCalledWith(429);
      expect(mockRes.json).toHaveBeenCalledWith(
        expect.objectContaining({
          success: false,
          error: expect.stringContaining('import is already in progress')
        })
      );
    });

    it('should always release lock even if import fails', async () => {
      mockParserService.parseTextToTrip.mockRejectedValue(new Error('Parse failed'));
      vi.mocked(aiRouter.selectModel).mockReturnValue({ modelId: 'test-model' });

      await importController.parseAndCreateTrip(mockReq as AuthenticatedRequest, mockRes as Response);

      // Lock should be released
      expect(mockImportLockService.releaseLock).toHaveBeenCalledWith('user-123');
    });
  });

  describe('Error Response Formatting', () => {
    it('should provide user-friendly timeout error message', async () => {
      mockParserService.parseTextToTrip.mockRejectedValue(new Error('timeout of 30000ms exceeded'));
      vi.mocked(aiRouter.selectModel).mockReturnValue({ modelId: 'test-model' });

      await importController.parseAndCreateTrip(mockReq as AuthenticatedRequest, mockRes as Response);

      expect(mockRes.status).toHaveBeenCalledWith(500);
      expect(mockRes.json).toHaveBeenCalledWith(
        expect.objectContaining({
          error: 'Parsing timed out. The text might be too long or complex. Please try with a shorter itinerary.'
        })
      );
    });

    it('should provide specific rate limit error message', async () => {
      mockParserService.parseTextToTrip.mockRejectedValue(new Error('rate limit exceeded'));
      vi.mocked(aiRouter.selectModel).mockReturnValue({ modelId: 'test-model' });

      await importController.parseAndCreateTrip(mockReq as AuthenticatedRequest, mockRes as Response);

      expect(mockRes.json).toHaveBeenCalledWith(
        expect.objectContaining({
          error: 'Too many parsing requests. Please wait a moment and try again.'
        })
      );
    });
  });
});