import { useAuthStore } from '@/stores/auth.store';

/**
 * JWT payload interface for token validation
 */
interface JWTPayload {
  exp?: number;
  iat?: number;
  sub?: string;
  [key: string]: any;
}

/**
 * Decode JWT token without verification (client-side only)
 */
function decodeJWT(token: string): JWTPayload | null {
  try {
    const parts = token.split('.');
    if (parts.length !== 3) {
      return null;
    }

    const payload = parts[1];
    const decoded = JSON.parse(atob(payload.replace(/-/g, '+').replace(/_/g, '/')));
    return decoded;
  } catch (error) {
    console.debug('Failed to decode JWT:', error);
    return null;
  }
}

/**
 * Check if a token is expired (client-side validation)
 */
export function isTokenExpired(token: string): boolean {
  const decoded = decodeJWT(token);
  
  if (!decoded || !decoded.exp) {
    return true;
  }
  
  const expirationTime = decoded.exp * 1000;
  const currentTime = Date.now();
  const bufferTime = 30000; // 30 second buffer for client-side checks
  
  return currentTime > (expirationTime - bufferTime);
}

/**
 * Get time until token expires (in milliseconds)
 */
export function getTimeUntilExpiry(token: string): number {
  const decoded = decodeJWT(token);
  
  if (!decoded || !decoded.exp) {
    return 0;
  }
  
  const expirationTime = decoded.exp * 1000;
  const currentTime = Date.now();
  
  return Math.max(0, expirationTime - currentTime);
}

/**
 * Validate current authentication state and logout if invalid
 */
export async function validateAuthState(): Promise<boolean> {
  const authStore = useAuthStore.getState();
  const { accessToken, isAuthenticated } = authStore;
  
  // If not authenticated, nothing to validate
  if (!isAuthenticated || !accessToken) {
    return false;
  }
  
  // Check if token is expired
  if (isTokenExpired(accessToken)) {
    console.log('Token expired, attempting refresh...');
    
    try {
      // Try to refresh the session
      await authStore.refreshSession();
      return true;
    } catch (error) {
      console.log('Token refresh failed, logging out:', error);
      authStore.clearAuth();
      
      // Redirect to login if we're in the browser
      if (typeof window !== 'undefined') {
        const currentPath = window.location.pathname;
        const loginUrl = `/login${currentPath !== '/login' ? `?redirect=${encodeURIComponent(currentPath)}` : ''}`;
        window.location.href = loginUrl;
      }
      
      return false;
    }
  }
  
  return true;
}

/**
 * Set up automatic token validation and refresh
 */
export function setupTokenMonitoring(): () => void {
  let intervalId: NodeJS.Timeout | null = null;
  
  const checkToken = async () => {
    const authStore = useAuthStore.getState();
    const { accessToken, isAuthenticated } = authStore;
    
    if (!isAuthenticated || !accessToken) {
      return;
    }
    
    const timeUntilExpiry = getTimeUntilExpiry(accessToken);
    
    // If token expires in less than 5 minutes, try to refresh
    if (timeUntilExpiry < 5 * 60 * 1000 && timeUntilExpiry > 0) {
      console.log('Token expiring soon, refreshing...');
      try {
        await authStore.refreshSession();
      } catch (error) {
        console.error('Failed to refresh token:', error);
        authStore.clearAuth();
        
        if (typeof window !== 'undefined') {
          window.location.href = '/login';
        }
      }
    }
  };
  
  // Check immediately
  checkToken();
  
  // Set up periodic checks every minute
  intervalId = setInterval(checkToken, 60 * 1000);
  
  // Return cleanup function
  return () => {
    if (intervalId) {
      clearInterval(intervalId);
      intervalId = null;
    }
  };
}

/**
 * Initialize authentication validation on app start
 */
export async function initializeAuthValidation(): Promise<void> {
  // Validate current auth state
  await validateAuthState();
  
  // Set up monitoring
  setupTokenMonitoring();
}
