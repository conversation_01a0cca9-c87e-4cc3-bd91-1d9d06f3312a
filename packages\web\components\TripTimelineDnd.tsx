'use client';

import { useMemo, useCallback, useRef, useEffect, useState } from 'react';
import { Card } from '@/components/ui/card';
import { groupByCurrency, formatCurrencyAmounts } from '@travelviz/shared';
import { Calendar } from 'lucide-react';
import { format, isSameDay, parseISO } from 'date-fns';
import { motion } from 'framer-motion';
import type { Activity } from '@/stores/trip.store';
import { useTripStore } from '@/stores/trip.store';
import {
  DndContext,
  closestCenter,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
  DragEndEvent,
  DragStartEvent,
  DragOverlay,
} from '@dnd-kit/core';
import {
  arrayMove,
  SortableContext,
  sortableKeyboardCoordinates,
  verticalListSortingStrategy,
} from '@dnd-kit/sortable';
import DraggableActivity from './DraggableActivity';
import { toast } from '@/hooks/use-toast';

interface TripTimelineDndProps {
  activities: Activity[];
  tripId: string;
  onReorder?: (orderedIds: string[]) => void;
  onReorderError?: (error: Error) => void;
}

interface GroupedActivities {
  date: string;
  dayNumber: number;
  activities: Activity[];
}

export default function TripTimelineDnd({ 
  activities, 
  tripId,
  onReorder,
  onReorderError 
}: TripTimelineDndProps) {
  const reorderActivities = useTripStore(state => state.reorderActivities);
  const [activeId, setActiveId] = useState<string | null>(null);
  const [isSaving, setIsSaving] = useState(false);
  
  // Use a ref to track if component is mounted to prevent state updates on unmounted component
  const isMountedRef = useRef(true);
  
  // Clean up on unmount to prevent memory leaks
  useEffect(() => {
    return () => {
      isMountedRef.current = false;
    };
  }, []);

  const sensors = useSensors(
    useSensor(PointerSensor),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );

  // Group activities by day
  const groupedActivities = useMemo(() => {
    // Separate scheduled and unscheduled activities
    const scheduled: Activity[] = [];
    const unscheduled: Activity[] = [];
    
    activities.forEach(activity => {
      if (activity.start_time) {
        scheduled.push(activity);
      } else {
        unscheduled.push(activity);
      }
    });
    
    // Sort scheduled activities by position
    scheduled.sort((a, b) => a.position - b.position);

    // Group scheduled activities by day
    const groups: GroupedActivities[] = [];
    let currentGroup: GroupedActivities | null = null;
    let dayNumber = 1;
    let lastDate: Date | null = null;

    scheduled.forEach(activity => {
      const activityDate = parseISO(activity.start_time!);
      
      if (!currentGroup || (lastDate && !isSameDay(activityDate, lastDate))) {
        // Start new group
        currentGroup = {
          date: format(activityDate, 'EEEE, MMMM d, yyyy'),
          dayNumber: dayNumber++,
          activities: []
        };
        groups.push(currentGroup);
        lastDate = activityDate;
      }
      
      currentGroup.activities.push(activity);
    });

    // Group unscheduled activities by type
    if (unscheduled.length > 0) {
      // Sort unscheduled activities by position
      unscheduled.sort((a, b) => a.position - b.position);
      
      groups.push({
        date: 'Unscheduled Activities',
        dayNumber: 0,
        activities: unscheduled
      });
    }

    return groups;
  }, [activities]);

  const formatTime = useCallback((datetime: string | undefined) => {
    if (!datetime) return undefined;
    try {
      return format(parseISO(datetime), 'h:mm a');
    } catch {
      return undefined;
    }
  }, []);

  const formatDuration = useCallback((start: string | undefined, end: string | undefined) => {
    if (!start || !end) return undefined;
    try {
      const startTime = parseISO(start);
      const endTime = parseISO(end);
      const durationMs = endTime.getTime() - startTime.getTime();
      const hours = Math.floor(durationMs / (1000 * 60 * 60));
      const minutes = Math.floor((durationMs % (1000 * 60 * 60)) / (1000 * 60));
      
      if (hours > 0 && minutes > 0) return `${hours}h ${minutes}m`;
      if (hours > 0) return `${hours}h`;
      if (minutes > 0) return `${minutes}m`;
      return undefined;
    } catch {
      return undefined;
    }
  }, []);

  // Memoize summary statistics
  const summaryStats = useMemo(() => ({
    totalActivities: activities.length,
    daysPlanned: groupedActivities.filter(g => g.dayNumber > 0).length,
    uniqueLocations: new Set(activities.map(a => a.location).filter(Boolean)).size,
    totalBudget: formatCurrencyAmounts(groupByCurrency(activities.map(a => ({ 
      price: a.price || undefined, 
      currency: a.currency || undefined 
    }))))
  }), [activities, groupedActivities]);

  const handleDragStart = (event: DragStartEvent) => {
    setActiveId(event.active.id as string);
  };

  const handleDragEnd = async (event: DragEndEvent) => {
    const { active, over } = event;

    if (!over || active.id === over.id) {
      setActiveId(null);
      return;
    }

    // Find which group the active and over items belong to
    let activeGroup: GroupedActivities | undefined;
    let overGroup: GroupedActivities | undefined;
    let activeIndex = -1;
    let overIndex = -1;

    for (const group of groupedActivities) {
      const activeIdx = group.activities.findIndex(a => a.id === active.id);
      const overIdx = group.activities.findIndex(a => a.id === over.id);
      
      if (activeIdx !== -1) {
        activeGroup = group;
        activeIndex = activeIdx;
      }
      if (overIdx !== -1) {
        overGroup = group;
        overIndex = overIdx;
      }
    }

    // Don't allow dragging between different days
    if (!activeGroup || !overGroup || activeGroup !== overGroup) {
      setActiveId(null);
      return;
    }

    // Update the order within the group
    const newActivities: Activity[] = arrayMove(activeGroup.activities, activeIndex, overIndex) as Activity[];
    
    // Get all activity IDs in the new order
    const orderedIds = activities
      .map(a => {
        // Find the activity in the reordered group
        const reorderedActivity = newActivities.find((na: Activity) => na.id === a.id);
        if (reorderedActivity) {
          // This activity was part of the reordered group
          return { id: a.id, order: newActivities.indexOf(reorderedActivity) };
        } else {
          // This activity wasn't reordered, keep its original position
          return { id: a.id, order: a.position };
        }
      })
      .sort((a, b) => a.order - b.order)
      .map(a => a.id);

    setActiveId(null);
    setIsSaving(true);

    try {
      // Call the store method which handles optimistic updates
      await reorderActivities(tripId, orderedIds);
      
      // Call optional callback
      onReorder?.(orderedIds);
      
      toast({
        title: "Activities reordered",
        description: "Your activities have been successfully reordered.",
      });
    } catch (error) {
      const err = error as Error;
      onReorderError?.(err);
      
      toast({
        title: "Failed to reorder activities",
        description: err.message || "Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsSaving(false);
    }
  };

  const activeActivity = activities.find(a => a.id === activeId);

  if (activities.length === 0) {
    return (
      <Card className="p-8 text-center" role="region" aria-label="Timeline area">
        <Calendar className="h-12 w-12 text-gray-400 mx-auto mb-4" aria-hidden="true" />
        <h3 className="text-lg font-semibold text-gray-900 mb-2">No Activities Yet</h3>
        <p className="text-gray-600">Start adding activities to see your timeline.</p>
      </Card>
    );
  }

  return (
    <DndContext
      sensors={sensors}
      collisionDetection={closestCenter}
      onDragStart={handleDragStart}
      onDragEnd={handleDragEnd}
    >
      <div className="space-y-8" role="region" aria-label="Trip timeline">
        {isSaving && (
          <div className="fixed top-4 right-4 bg-white shadow-lg rounded-lg p-4 z-50">
            <div className="flex items-center space-x-2">
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-gray-900"></div>
              <span className="text-sm" aria-label="Saving changes...">Saving changes...</span>
            </div>
          </div>
        )}

        {groupedActivities.map((group, groupIndex) => (
          <motion.div
            key={`${group.dayNumber}-${group.date}`}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: groupIndex * 0.1 }}
          >
            {/* Day Header */}
            <div className="flex items-center space-x-4 mb-6">
              <div className="flex-shrink-0">
                {group.dayNumber > 0 ? (
                  <div className="w-12 h-12 bg-orange-500 rounded-full flex items-center justify-center text-white font-bold" aria-label={`Day ${group.dayNumber}`}>
                    {group.dayNumber}
                  </div>
                ) : (
                  <div className="w-12 h-12 bg-gray-400 rounded-full flex items-center justify-center" aria-label="Unscheduled activities">
                    <Calendar className="h-6 w-6 text-white" aria-hidden="true" />
                  </div>
                )}
              </div>
              <div>
                <h3 className="text-xl font-bold text-gray-900">
                  {group.dayNumber > 0 ? `Day ${group.dayNumber}` : group.date}
                </h3>
                {group.dayNumber > 0 && (
                  <p className="text-gray-600">{group.date}</p>
                )}
                {group.dayNumber === 0 && (
                  <p className="text-gray-600">{group.activities.length} {group.activities.length === 1 ? 'activity' : 'activities'}</p>
                )}
              </div>
            </div>

            {/* Activities for this day */}
            <div className="ml-6 border-l-2 border-gray-200 pl-10 space-y-6">
              <SortableContext
                items={group.activities.map(a => a.id)}
                strategy={verticalListSortingStrategy}
              >
                {group.activities.map((activity) => (
                  <DraggableActivity
                    key={activity.id}
                    activity={activity}
                    formatTime={formatTime}
                    formatDuration={formatDuration}
                  />
                ))}
              </SortableContext>
            </div>
          </motion.div>
        ))}

        {/* Summary Stats */}
        <Card className="p-6 bg-gray-50" role="region" aria-label="Trip summary statistics">
          <h3 className="font-semibold text-gray-900 mb-4">Trip Summary</h3>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-gray-900" aria-label={`${summaryStats.totalActivities} total activities`}>{summaryStats.totalActivities}</div>
              <div className="text-sm text-gray-600">Total Activities</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-gray-900" aria-label={`${summaryStats.daysPlanned} days planned`}>{summaryStats.daysPlanned}</div>
              <div className="text-sm text-gray-600">Days Planned</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-gray-900" aria-label={`${summaryStats.uniqueLocations} unique locations`}>{summaryStats.uniqueLocations}</div>
              <div className="text-sm text-gray-600">Unique Locations</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-gray-900" aria-label={`Total budget: ${summaryStats.totalBudget}`}>{summaryStats.totalBudget}</div>
              <div className="text-sm text-gray-600">Total Budget</div>
            </div>
          </div>
        </Card>
      </div>

      <DragOverlay>
        {activeActivity ? (
          <div className="opacity-80" data-testid="drag-overlay">
            <Card className="p-4 shadow-lg">
              <h4 className="font-semibold">{activeActivity.title}</h4>
              {activeActivity.location && (
                <p className="text-sm text-gray-600">{activeActivity.location}</p>
              )}
            </Card>
          </div>
        ) : null}
      </DragOverlay>
    </DndContext>
  );
}