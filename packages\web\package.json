{"name": "@travelviz/web", "version": "1.0.0", "private": true, "sideEffects": false, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint --max-warnings 500", "type-check": "tsc --noEmit", "test": "vitest run", "test:watch": "vitest", "test:coverage": "vitest run --coverage", "test:e2e": "playwright test", "test:e2e:ui": "playwright test --ui", "test:visual": "playwright test tests/visual/", "test:visual:update": "playwright test tests/visual/ --update-snapshots", "clean": "rm -rf .next", "analyze": "ANALYZE=true next build", "bundle-size": "next build && node scripts/check-bundle-size.js", "performance-check": "npm run bundle-size && npm run lighthouse-ci", "lighthouse-ci": "lhci collect --config=lighthouserc.js", "build:production": "NODE_ENV=production next build"}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/modifiers": "^9.0.0", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@hookform/resolvers": "^3.10.0", "@radix-ui/react-accordion": "^1.2.0", "@radix-ui/react-alert-dialog": "^1.1.1", "@radix-ui/react-aspect-ratio": "^1.1.0", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "^1.1.0", "@radix-ui/react-context-menu": "^2.2.1", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-hover-card": "^1.1.1", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-menubar": "^1.1.1", "@radix-ui/react-navigation-menu": "^1.2.0", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.2.0", "@radix-ui/react-scroll-area": "^1.1.0", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slider": "^1.2.0", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.2.14", "@radix-ui/react-toggle": "^1.1.9", "@radix-ui/react-toggle-group": "^1.1.0", "@radix-ui/react-tooltip": "^1.1.2", "@react-spring/web": "^10.0.1", "@react-three/drei": "^10.5.0", "@react-three/fiber": "^9.2.0", "@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "^2.50.2", "@tanstack/react-query": "^5.82.0", "@tanstack/react-query-devtools": "^5.83.0", "@tanstack/react-virtual": "^3.13.12", "@travelviz/shared": "workspace:*", "@types/canvas-confetti": "^1.9.0", "@types/js-cookie": "^3.0.6", "@types/lodash.debounce": "^4.0.9", "@types/mapbox-gl": "^3.4.1", "@types/node": "^20.14.0", "@types/react": "^18.3.0", "@types/react-dom": "^18.3.0", "@types/react-window": "^1.8.8", "@vercel/analytics": "^1.5.0", "@vercel/speed-insights": "^1.2.0", "autoprefixer": "10.4.15", "canvas-confetti": "^1.9.3", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "cmdk": "^1.0.0", "date-fns": "^3.6.0", "embla-carousel-react": "^8.3.0", "framer-motion": "^10.16.4", "input-otp": "^1.2.4", "js-cookie": "^3.0.5", "lodash.debounce": "^4.0.8", "lucide-react": "^0.446.0", "mammoth": "^1.9.1", "mapbox-gl": "^3.13.0", "next": "^15.1.0", "next-themes": "^0.3.0", "pdfjs-dist": "^5.3.93", "postcss": "8.4.49", "posthog-js": "^1.257.0", "react": "^18.3.0", "react-day-picker": "^9.8.0", "react-dom": "^18.3.0", "react-dropzone": "^14.3.8", "react-hook-form": "^7.59.0", "react-hot-toast": "^2.5.2", "react-map-gl": "^8.0.4", "react-resizable-panels": "^2.1.3", "react-window": "^1.8.11", "react-window-infinite-loader": "^1.0.10", "recharts": "^2.12.7", "sonner": "^1.7.4", "tailwind-merge": "^2.5.2", "tailwindcss": "3.3.3", "tailwindcss-animate": "^1.0.7", "three": "^0.178.0", "vaul": "^0.9.9", "web-vitals": "^5.0.3", "zod": "^4.0.5", "zustand": "^4.4.7"}, "devDependencies": {"@next/bundle-analyzer": "^15.3.5", "@playwright/test": "^1.53.2", "@storybook/test-runner": "^0.23.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/pngjs": "^6.0.5", "@types/react-window-infinite-loader": "^1.0.9", "@types/three": "^0.178.1", "@vitejs/plugin-react": "^4.6.0", "critters": "^0.0.25", "dotenv": "^16.4.5", "eslint-config-next": "^15.1.0", "gzip-size": "^7.0.0", "pixelmatch": "^7.1.0", "pngjs": "^7.0.0", "vite-tsconfig-paths": "^5.1.4"}}