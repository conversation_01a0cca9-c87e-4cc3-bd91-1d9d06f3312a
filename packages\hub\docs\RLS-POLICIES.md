# Row Level Security (RLS) Policies Documentation

## Overview

TravelViz implements comprehensive Row Level Security (RLS) policies to ensure data privacy and security. Users can only access their own data, with the exception of trips that have been explicitly shared publicly.

## Database Tables and RLS Policies

### 1. **profiles** Table

**Purpose**: Stores user profile information extending auth.users

**RLS Policies**:
- ✅ Users can VIEW their own profile
- ✅ Users can UPDATE their own profile  
- ❌ Users cannot view or modify other users' profiles
- ✅ Service role has full access (for admin operations)

### 2. **trips** Table

**Purpose**: Stores trip information created by users

**RLS Policies**:
- ✅ Users can VIEW their own trips
- ✅ Anyone can VIEW trips marked as `is_public = true`
- ✅ Users can CREATE trips (automatically linked to their user_id)
- ✅ Users can UPDATE their own trips
- ✅ Users can DELETE their own trips
- ❌ Users cannot modify other users' trips
- ✅ Service role has full access

### 3. **activities** Table

**Purpose**: Stores activities/events within trips

**RLS Policies**:
- ✅ Users can VIEW activities for trips they own
- ✅ Anyone can VIEW activities for public trips
- ✅ Users can CREATE activities for their own trips
- ✅ Users can UPDATE activities for their own trips
- ✅ Users can DELETE activities for their own trips
- ❌ Users cannot access activities for other users' private trips
- ✅ Service role has full access

### 4. **trip_shares** Table

**Purpose**: Tracks when trips are shared publicly

**RLS Policies**:
- ✅ Anyone can VIEW share records for public trips
- ✅ Users can CREATE share records for their own trips
- ❌ Users cannot create share records for other users' trips
- ✅ Service role has full access

### 5. **affiliate_clicks** Table

**Purpose**: Tracks affiliate link clicks for revenue generation

**RLS Policies**:
- ✅ Anyone can CREATE affiliate click records (for tracking)
- ✅ Users can VIEW their own affiliate clicks
- ❌ Users cannot view other users' affiliate clicks
- ✅ Service role has full access

### 6. **auth_failed_attempts** & **auth_account_lockouts** Tables

**Purpose**: Security tables for tracking failed login attempts and account lockouts

**RLS Policies**:
- ❌ Only service role can access these tables
- ❌ Regular users have no access (security measure)

## Helper Functions

### `user_owns_trip(trip_id UUID)`
Returns TRUE if the current authenticated user owns the specified trip.

### `trip_is_public(trip_id UUID)`
Returns TRUE if the specified trip is marked as public.

## Security Principles

1. **Principle of Least Privilege**: Users only have access to data they explicitly own or has been shared publicly.

2. **Fail-Safe Defaults**: All tables have RLS enabled by default, meaning no access unless explicitly granted by a policy.

3. **Service Role Access**: The service role (used by the backend API) can bypass RLS for administrative operations.

4. **Public Sharing**: The `is_public` flag on trips enables controlled sharing without compromising security.

## Testing RLS Policies

To test RLS policies, run:

```bash
cd packages/hub
pnpm tsx src/tests/test-rls-policies.ts
```

This will verify:
- Users can only access their own data
- Public trips are accessible to everyone
- Private data remains private
- All CRUD operations respect ownership

## Implementation Notes

1. **Authentication**: RLS policies use `auth.uid()` to identify the current user, which comes from Supabase Auth JWT tokens.

2. **Performance**: Indexes are created on foreign keys and commonly queried fields to ensure RLS checks don't impact performance.

3. **Cascading Deletes**: When a user is deleted, all their data is automatically removed through CASCADE constraints.

4. **Audit Trail**: While not implemented in the current schema, consider adding audit tables for sensitive operations.

## Future Considerations

1. **Sharing Permissions**: Add granular sharing (view-only, edit, etc.)
2. **Team/Organization Support**: Add policies for team-based access
3. **Time-based Access**: Add temporary sharing with expiration
4. **IP Restrictions**: Add location-based access controls