import { AnalyticsProvider } from '@travelviz/shared';

/**
 * Mock implementation of AnalyticsProvider for testing
 */
export class MockAnalyticsProvider implements AnalyticsProvider {
  public calls: Array<{
    method: string;
    args: any[];
    timestamp: number;
  }> = [];

  private trackCalls: Array<[string, Record<string, any> | undefined]> = [];
  private identifyCalls: Array<[string, Record<string, any> | undefined]> = [];
  private resetCalls: number = 0;
  private setSuperPropertiesCalls: Array<Record<string, any>> = [];

  // Create mock functions
  public track = (event: string, properties?: Record<string, any>) => {
    this.trackCalls.push([event, properties]);
    this.calls.push({
      method: 'track',
      args: [event, properties],
      timestamp: Date.now(),
    });
  };

  public identify = (userId: string, traits?: Record<string, any>) => {
    this.identifyCalls.push([userId, traits]);
    this.calls.push({
      method: 'identify',
      args: [userId, traits],
      timestamp: Date.now(),
    });
  };

  public reset = () => {
    this.resetCalls++;
    this.calls.push({
      method: 'reset',
      args: [],
      timestamp: Date.now(),
    });
  };

  public setSuperProperties = (properties: Record<string, any>) => {
    this.setSuperPropertiesCalls.push(properties);
    this.calls.push({
      method: 'setSuperProperties',
      args: [properties],
      timestamp: Date.now(),
    });
  };

  public getDistinctId = () => 'mock-distinct-id';

  /**
   * Clear all recorded calls
   */
  public clear(): void {
    this.calls = [];
    this.trackCalls = [];
    this.identifyCalls = [];
    this.resetCalls = 0;
    this.setSuperPropertiesCalls = [];
  }

  /**
   * Get calls for a specific method
   */
  public getCallsForMethod(method: string): Array<any[]> {
    return this.calls
      .filter(call => call.method === method)
      .map(call => call.args);
  }

  /**
   * Check if a specific event was tracked
   */
  public wasEventTracked(event: string): boolean {
    return this.calls.some(
      call => call.method === 'track' && call.args[0] === event
    );
  }

  /**
   * Get properties for a specific tracked event
   */
  public getEventProperties(event: string): Record<string, any> | undefined {
    const call = this.calls.find(
      call => call.method === 'track' && call.args[0] === event
    );
    return call?.args[1];
  }

  // Test helpers to check if methods were called
  public get wasTrackCalled(): boolean {
    return this.trackCalls.length > 0;
  }

  public get wasIdentifyCalled(): boolean {
    return this.identifyCalls.length > 0;
  }

  public get wasResetCalled(): boolean {
    return this.resetCalls > 0;
  }

  public get trackCallCount(): number {
    return this.trackCalls.length;
  }

  public get identifyCallCount(): number {
    return this.identifyCalls.length;
  }

  public get resetCallCount(): number {
    return this.resetCalls;
  }
}

/**
 * Factory function to create a mock analytics provider
 */
export function createMockAnalyticsProvider(): MockAnalyticsProvider {
  return new MockAnalyticsProvider();
}