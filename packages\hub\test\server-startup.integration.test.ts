import { describe, it, expect, beforeAll, afterAll } from 'vitest';
import { spawn, ChildProcess } from 'child_process';
import { resolve } from 'path';
import http from 'http';

/**
 * Integration tests that validate the server can actually start
 * This catches module resolution errors that unit tests miss
 */
describe('Server Startup Integration', () => {
  let serverProcess: ChildProcess | null = null;
  const serverPort = 3099; // Use a different port to avoid conflicts
  const startupTimeout = 15000; // 15 seconds

  beforeAll(() => {
    // Set test port
    process.env.PORT = String(serverPort);
  });

  afterAll(async () => {
    // Clean up server process
    if (serverProcess) {
      serverProcess.kill('SIGTERM');
      await new Promise(resolve => setTimeout(resolve, 1000));
      if (!serverProcess.killed) {
        serverProcess.kill('SIGKILL');
      }
    }
  });

  it('should start the server without module resolution errors', async () => {
    const serverStarted = new Promise<void>((resolve, reject) => {
      const timeout = setTimeout(() => {
        reject(new Error('Server failed to start within timeout'));
      }, startupTimeout);

      // Start the server
      serverProcess = spawn('npx', ['tsx', 'src/index.ts'], {
        cwd: resolve(__dirname, '..'),
        env: {
          ...process.env,
          PORT: String(serverPort),
          NODE_ENV: 'test'
        }
      });

      let output = '';
      let errorOutput = '';

      serverProcess.stdout?.on('data', (data) => {
        output += data.toString();
        console.log('[Server]', data.toString());
        
        // Check for successful startup
        if (data.toString().includes('server running on port')) {
          clearTimeout(timeout);
          resolve();
        }
      });

      serverProcess.stderr?.on('data', (data) => {
        errorOutput += data.toString();
        console.error('[Server Error]', data.toString());
        
        // Check for module resolution errors
        if (data.toString().includes('Cannot find module')) {
          clearTimeout(timeout);
          reject(new Error(`Module resolution error: ${data.toString()}`));
        }
      });

      serverProcess.on('error', (error) => {
        clearTimeout(timeout);
        reject(new Error(`Failed to start server: ${error.message}`));
      });

      serverProcess.on('exit', (code) => {
        if (code !== 0 && code !== null) {
          clearTimeout(timeout);
          reject(new Error(`Server exited with code ${code}\nOutput: ${output}\nError: ${errorOutput}`));
        }
      });
    });

    await expect(serverStarted).resolves.toBeUndefined();
  }, startupTimeout + 5000);

  it('should respond to health check endpoint', async () => {
    // Wait a bit for server to be fully ready
    await new Promise(resolve => setTimeout(resolve, 2000));

    const healthCheck = new Promise<number>((resolve, reject) => {
      const req = http.get(`http://localhost:${serverPort}/health`, (res) => {
        resolve(res.statusCode || 0);
      });

      req.on('error', (err) => {
        reject(new Error(`Health check failed: ${err.message}`));
      });

      req.setTimeout(5000, () => {
        req.destroy();
        reject(new Error('Health check timeout'));
      });
    });

    const statusCode = await healthCheck;
    expect(statusCode).toBe(200);
  });
});