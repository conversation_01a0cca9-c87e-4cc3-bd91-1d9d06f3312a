#!/usr/bin/env node

const { spawn } = require('child_process');
const http = require('http');

const COLORS = {
  RED: '\x1b[31m',
  GREEN: '\x1b[32m',
  YELLOW: '\x1b[33m',
  BLUE: '\x1b[34m',
  RESET: '\x1b[0m'
};

const HUB_PORT = 3001;
let WEB_PORT = 3000;
const STARTUP_TIMEOUT = 30000; // 30 seconds
const HEALTH_CHECK_RETRIES = 10;
const HEALTH_CHECK_INTERVAL = 1000; // 1 second

function log(message, color = COLORS.RESET) {
  console.log(`${color}${message}${COLORS.RESET}`);
}

function checkHealth(port, serviceName) {
  return new Promise((resolve) => {
    const options = {
      hostname: 'localhost',
      port: port,
      path: '/health',
      method: 'GET',
      timeout: 5000
    };

    const req = http.request(options, (res) => {
      if (res.statusCode === 200) {
        resolve(true);
      } else {
        resolve(false);
      }
    });

    req.on('error', () => {
      resolve(false);
    });

    req.on('timeout', () => {
      req.destroy();
      resolve(false);
    });

    req.end();
  });
}

async function waitForService(port, serviceName, retries = HEALTH_CHECK_RETRIES) {
  for (let i = 0; i < retries; i++) {
    if (await checkHealth(port, serviceName)) {
      return true;
    }
    if (i < retries - 1) {
      await new Promise(resolve => setTimeout(resolve, HEALTH_CHECK_INTERVAL));
    }
  }
  return false;
}

async function runHealthCheck() {
  log('🏥 Running pre-commit health checks...', COLORS.BLUE);
  
  // Start services
  log('🚀 Starting services for health check...', COLORS.YELLOW);
  
  const devProcess = spawn('pnpm', ['run', 'dev'], {
    stdio: 'pipe',
    shell: true
  });

  let hubReady = false;
  let webReady = false;
  let output = '';

  // Capture output
  devProcess.stdout.on('data', (data) => {
    output += data.toString();
    if (data.toString().includes('Hub API server running on port')) {
      hubReady = true;
    }
    if (data.toString().includes('Ready in') && data.toString().includes('[web]')) {
      webReady = true;
    }
    // Check for port change
    const portMatch = data.toString().match(/Port 3000 is in use, using available port (\d+)/);
    if (portMatch) {
      WEB_PORT = parseInt(portMatch[1]);
      log(`📝 Web service using port ${WEB_PORT}`, COLORS.YELLOW);
    }
    // Also check for Next.js port announcement
    const nextPortMatch = data.toString().match(/Local:\s+http:\/\/localhost:(\d+)/);
    if (nextPortMatch && data.toString().includes('[web]')) {
      WEB_PORT = parseInt(nextPortMatch[1]);
      log(`📝 Web service running on port ${WEB_PORT}`, COLORS.YELLOW);
    }
  });

  devProcess.stderr.on('data', (data) => {
    output += data.toString();
  });

  // Wait for services to start
  const startTime = Date.now();
  while ((!hubReady || !webReady) && (Date.now() - startTime < STARTUP_TIMEOUT)) {
    await new Promise(resolve => setTimeout(resolve, 100));
  }

  let exitCode = 0;

  try {
    // Check if services started
    if (!hubReady || !webReady) {
      log('❌ Services failed to start within timeout', COLORS.RED);
      log('Output:', COLORS.YELLOW);
      console.log(output);
      exitCode = 1;
      return exitCode;
    }

    // Perform health checks
    log('🔍 Checking hub health...', COLORS.BLUE);
    const hubHealthy = await waitForService(HUB_PORT, 'Hub');
    
    log('🔍 Checking web health...', COLORS.BLUE);
    const webHealthy = await waitForService(WEB_PORT, 'Web');

    if (hubHealthy && webHealthy) {
      log('✅ All health checks passed!', COLORS.GREEN);
      exitCode = 0;
    } else {
      if (!hubHealthy) {
        log('❌ Hub health check failed', COLORS.RED);
      }
      if (!webHealthy) {
        log('❌ Web health check failed', COLORS.RED);
      }
      exitCode = 1;
    }
  } finally {
    // Clean up
    log('🧹 Cleaning up...', COLORS.YELLOW);
    devProcess.kill('SIGTERM');
    
    // Give processes time to cleanup
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // Force kill if still running
    try {
      process.kill(devProcess.pid, 'SIGKILL');
    } catch (e) {
      // Process already dead
    }
  }

  process.exit(exitCode);
}

// Run if called directly
if (require.main === module) {
  runHealthCheck().catch(err => {
    log(`❌ Health check error: ${err.message}`, COLORS.RED);
    process.exit(1);
  });
}

module.exports = { runHealthCheck };