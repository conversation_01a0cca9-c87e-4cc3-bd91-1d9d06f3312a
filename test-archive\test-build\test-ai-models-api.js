"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const axios_1 = __importDefault(require("axios"));
const fs = __importStar(require("fs"));
const TEST_MODELS = [
    'openrouter/cypher-alpha:free',
    'deepseek/deepseek-chat-v3-0324:free',
    'deepseek/deepseek-r1-0528:free',
    'google/gemini-2.0-flash-001',
    'google/gemini-2.5-flash-preview-05-20'
];
const TEST_USER = {
    email: '<EMAIL>',
    password: 'Flaremmk123!'
};
const HUB_URL = 'http://localhost:3001';
class AIModelAPITester {
    constructor() {
        this.authToken = '';
        this.results = [];
    }
    async login() {
        try {
            console.log('🔐 Logging in with test user...');
            const response = await axios_1.default.post(`${HUB_URL}/api/v1/auth/login`, {
                email: TEST_USER.email,
                password: TEST_USER.password
            });
            this.authToken = response.data.data.session.access_token;
            console.log('✅ Login successful');
        }
        catch (error) {
            console.error('❌ Login failed:', error);
            throw error;
        }
    }
    async testModel(model, text) {
        console.log(`\n🧪 Testing model: ${model}`);
        const startTime = Date.now();
        try {
            const response = await axios_1.default.post(`${HUB_URL}/api/v1/import/parse`, {
                text,
                source: 'chatgpt',
                model
            }, {
                headers: {
                    'Authorization': `Bearer ${this.authToken}`,
                    'Content-Type': 'application/json'
                },
                timeout: 60000 // 60 second timeout
            });
            const parseTime = Date.now() - startTime;
            const tripData = response.data.data;
            const activitiesCount = tripData.activities?.length || 0;
            console.log(`✅ Model ${model} completed: ${activitiesCount} activities in ${parseTime}ms`);
            return {
                model,
                success: true,
                activitiesCount,
                parseTime,
                parsedData: tripData
            };
        }
        catch (error) {
            const parseTime = Date.now() - startTime;
            console.error(`❌ Model ${model} failed:`, error.response?.data || error.message);
            return {
                model,
                success: false,
                activitiesCount: 0,
                parseTime,
                error: error.response?.data?.error || error.message
            };
        }
    }
    getTestText() {
        // Sample 15-day European itinerary
        return `
15-Day European Travel Itinerary: London, Madrid, Lisbon and Porto

Day 1: Arrival in London
- 10:00 AM: Arrive at Heathrow Airport
- 12:00 PM: Check-in at The Zetter Townhouse (Marylebone)
- 2:00 PM: Lunch at Dishoom (Indian cuisine)
- 4:00 PM: Visit British Museum
- 7:00 PM: Dinner at Hawksmoor (Steakhouse)

Day 2: London Exploration
- 9:00 AM: Breakfast at The Breakfast Club
- 10:00 AM: Tour Tower of London
- 1:00 PM: Lunch at Borough Market
- 3:00 PM: Walk along South Bank
- 5:00 PM: Visit Tate Modern
- 8:00 PM: West End Theater show

Day 3: London to Madrid
- 9:00 AM: Check out from hotel
- 11:00 AM: Flight from London to Madrid (BA458)
- 3:00 PM: Arrive in Madrid
- 4:00 PM: Check-in at Hotel Urban
- 6:00 PM: Walk around Gran Via
- 8:30 PM: Tapas dinner at Mercado de San Miguel

Day 4: Madrid Museums
- 9:00 AM: Breakfast at Café de Oriente
- 10:00 AM: Visit Prado Museum
- 1:00 PM: Lunch at Sobrino de Botín
- 3:00 PM: Explore Retiro Park
- 5:00 PM: Visit Reina Sofia Museum
- 9:00 PM: Dinner at Casa Lucio

Day 5: Madrid Day Trip
- 8:00 AM: Day trip to Toledo
- 10:00 AM: Toledo Cathedral
- 1:00 PM: Lunch in Toledo
- 4:00 PM: Return to Madrid
- 8:00 PM: Flamenco show and dinner

Day 6: Madrid to Lisbon
- 10:00 AM: Check out from hotel
- 12:00 PM: Flight to Lisbon (IB3118)
- 1:30 PM: Arrive in Lisbon
- 3:00 PM: Check-in at Memmo Alfama Hotel
- 5:00 PM: Explore Alfama district
- 8:00 PM: Fado dinner at Clube de Fado

Day 7: Lisbon Highlights
- 9:00 AM: Pastéis de Belém breakfast
- 10:00 AM: Visit Jerónimos Monastery
- 12:00 PM: Explore Belém Tower
- 2:00 PM: Lunch at Time Out Market
- 4:00 PM: Ride Tram 28
- 7:00 PM: Sunset at Miradouro da Senhora do Monte
- 9:00 PM: Dinner at Ramiro (seafood)

Day 8: Sintra Day Trip
- 8:00 AM: Train to Sintra
- 10:00 AM: Visit Pena Palace
- 1:00 PM: Lunch in Sintra
- 3:00 PM: Explore Quinta da Regaleira
- 6:00 PM: Return to Lisbon
- 8:00 PM: Dinner at Cervejaria Trindade

Day 9: Lisbon to Porto
- 10:00 AM: Check out from hotel
- 11:00 AM: Train to Porto (3 hours)
- 2:30 PM: Arrive in Porto
- 3:30 PM: Check-in at The Yeatman Hotel
- 5:00 PM: Port wine tasting in Vila Nova de Gaia
- 8:00 PM: Dinner at DOP Restaurant

Day 10: Porto Old Town
- 9:00 AM: Breakfast at Majestic Café
- 10:00 AM: Visit São Bento Station
- 11:00 AM: Tour Porto Cathedral
- 1:00 PM: Lunch at Cantinho do Avillez
- 3:00 PM: Explore Ribeira district
- 5:00 PM: Visit Livraria Lello bookstore
- 8:00 PM: Dinner at O Paparico

Day 11: Douro Valley
- 8:00 AM: Day trip to Douro Valley
- 10:00 AM: Vineyard tour and tasting
- 1:00 PM: Lunch with valley views
- 4:00 PM: River cruise
- 7:00 PM: Return to Porto
- 9:00 PM: Light dinner at local tasca

Day 12: Porto to Lisbon
- 10:00 AM: Check out from hotel
- 11:00 AM: Train back to Lisbon
- 2:30 PM: Arrive in Lisbon
- 3:30 PM: Check-in at Avenida Palace
- 5:00 PM: Shopping in Chiado
- 8:00 PM: Dinner at Belcanto (Michelin star)

Day 13: Lisbon Coast
- 9:00 AM: Day trip to Cascais
- 11:00 AM: Beach time at Praia do Guincho
- 1:00 PM: Seafood lunch in Cascais
- 4:00 PM: Return to Lisbon
- 6:00 PM: Explore Bairro Alto
- 9:00 PM: Dinner and drinks in Bairro Alto

Day 14: Final Day in Lisbon
- 10:00 AM: Visit Gulbenkian Museum
- 1:00 PM: Lunch at A Cevicheria
- 3:00 PM: Last-minute shopping
- 5:00 PM: Relax at hotel spa
- 8:00 PM: Farewell dinner at Alma

Day 15: Departure
- 9:00 AM: Check out from hotel
- 11:00 AM: Depart for airport
- 2:00 PM: Flight home

Budget Estimate:
- Flights: €600
- Hotels: €2,100 (€150/night average)
- Meals: €1,050 (€70/day)
- Activities: €400
- Transport: €300
- Total: €4,450 per person
    `;
    }
    async runAllTests() {
        console.log('🏁 Starting AI Model API Testing Suite');
        console.log(`📋 Testing ${TEST_MODELS.length} models`);
        try {
            // Login first
            await this.login();
            // Get test text
            const testText = this.getTestText();
            console.log(`📝 Test text length: ${testText.length} characters`);
            // Create results directory
            if (!fs.existsSync('test-results')) {
                fs.mkdirSync('test-results');
            }
            // Test each model
            for (const model of TEST_MODELS) {
                const result = await this.testModel(model, testText);
                this.results.push(result);
                // Save individual result
                fs.writeFileSync(`test-results/model-${model.replace(/[/:]/g, '-')}.json`, JSON.stringify(result.parsedData || { error: result.error }, null, 2));
                // Wait between tests to avoid rate limiting
                await new Promise(resolve => setTimeout(resolve, 2000));
            }
            // Generate report
            await this.generateReport();
        }
        catch (error) {
            console.error('❌ Test suite failed:', error);
            throw error;
        }
    }
    async generateReport() {
        console.log('\n📊 Generating Test Report...');
        // Sort results by activities count (descending)
        const sortedResults = [...this.results].sort((a, b) => b.activitiesCount - a.activitiesCount);
        let report = `# AI Model API Testing Report
Generated: ${new Date().toISOString()}

## Summary
- Models Tested: ${TEST_MODELS.length}
- Successful: ${this.results.filter(r => r.success).length}
- Failed: ${this.results.filter(r => !r.success).length}

## Expected Activities Count
Based on the itinerary, we expect approximately:
- 6-8 activities per day × 15 days = 90-120 activities
- Minimum acceptable: 45 activities (50% detection rate)

## Results by Performance

| Rank | Model | Success | Activities | Parse Time | Detection Rate |
|------|-------|---------|------------|------------|----------------|
`;
        sortedResults.forEach((result, index) => {
            const detectionRate = ((result.activitiesCount / 90) * 100).toFixed(1);
            report += `| ${index + 1} | ${result.model} | ${result.success ? '✅' : '❌'} | ${result.activitiesCount} | ${result.parseTime}ms | ${detectionRate}% |\n`;
        });
        report += `\n## Detailed Analysis\n\n`;
        // Analyze each model
        sortedResults.forEach((result) => {
            report += `### ${result.model}\n`;
            report += `- **Status**: ${result.success ? 'Success' : 'Failed'}\n`;
            report += `- **Activities Detected**: ${result.activitiesCount}\n`;
            report += `- **Parse Time**: ${result.parseTime}ms\n`;
            report += `- **Detection Rate**: ${((result.activitiesCount / 90) * 100).toFixed(1)}%\n`;
            if (result.error) {
                report += `- **Error**: ${result.error}\n`;
            }
            if (result.success && result.parsedData) {
                report += `- **Trip Title**: ${result.parsedData.title || 'N/A'}\n`;
                report += `- **Destination**: ${result.parsedData.destination || 'N/A'}\n`;
                // Analyze activity types
                if (result.parsedData.activities) {
                    const typeCount = {};
                    result.parsedData.activities.forEach((activity) => {
                        typeCount[activity.type || 'other'] = (typeCount[activity.type || 'other'] || 0) + 1;
                    });
                    report += `- **Activity Types**:\n`;
                    Object.entries(typeCount).forEach(([type, count]) => {
                        report += `  - ${type}: ${count}\n`;
                    });
                }
            }
            report += `\n`;
        });
        // Quality Analysis
        report += `## Quality Analysis\n\n`;
        const successfulResults = sortedResults.filter(r => r.success);
        if (successfulResults.length > 0) {
            const avgActivities = successfulResults.reduce((sum, r) => sum + r.activitiesCount, 0) / successfulResults.length;
            const avgParseTime = successfulResults.reduce((sum, r) => sum + r.parseTime, 0) / successfulResults.length;
            report += `**Average Performance (Successful Models)**:\n`;
            report += `- Average activities detected: ${avgActivities.toFixed(1)}\n`;
            report += `- Average parse time: ${avgParseTime.toFixed(0)}ms\n`;
            report += `- Average detection rate: ${((avgActivities / 90) * 100).toFixed(1)}%\n\n`;
        }
        // Recommendations
        report += `## Recommendations\n\n`;
        const bestModel = sortedResults.find(r => r.success);
        if (bestModel) {
            report += `**Best Performing Model**: ${bestModel.model}\n`;
            report += `- Detected ${bestModel.activitiesCount} activities (${((bestModel.activitiesCount / 90) * 100).toFixed(1)}% detection rate)\n`;
            report += `- Completed in ${bestModel.parseTime}ms\n\n`;
            if (bestModel.activitiesCount >= 45) {
                report += `✅ This model meets the minimum detection threshold and is recommended for production use.\n`;
            }
            else {
                report += `⚠️ This model falls below the minimum detection threshold. Consider:\n`;
                report += `- Testing with more specific prompts\n`;
                report += `- Using a more capable model\n`;
                report += `- Implementing post-processing to enhance results\n`;
            }
        }
        else {
            report += `❌ No models succeeded. Check:\n`;
            report += `- OpenRouter API key is valid\n`;
            report += `- Models are accessible with your API key\n`;
            report += `- Network connectivity\n`;
        }
        // Save report
        fs.writeFileSync('test-results/ai-model-api-test-report.md', report);
        console.log('✅ Report saved to test-results/ai-model-api-test-report.md');
        // Save raw results as JSON
        fs.writeFileSync('test-results/ai-model-api-test-results.json', JSON.stringify(this.results, null, 2));
    }
}
// Run the tests
async function main() {
    const tester = new AIModelAPITester();
    try {
        await tester.runAllTests();
        console.log('\n✅ All tests completed!');
        process.exit(0);
    }
    catch (error) {
        console.error('\n❌ Test suite failed:', error);
        process.exit(1);
    }
}
// Check if hub server is running
async function checkHubServer() {
    try {
        const response = await axios_1.default.get(`${HUB_URL}/health`);
        return response.status === 200;
    }
    catch {
        return false;
    }
}
// Entry point
(async () => {
    console.log('🔍 Checking if hub server is running...');
    const isRunning = await checkHubServer();
    if (!isRunning) {
        console.error('❌ Hub server is not running at port 3001. Please run "pnpm dev" first.');
        process.exit(1);
    }
    console.log('✅ Hub server is running');
    await main();
})();
