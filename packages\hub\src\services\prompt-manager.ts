/**
 * @deprecated This class is deprecated in favor of simplified prompts in AI_CONFIG.
 * New implementations should use the simplified systemPrompt in ai.config.ts
 * which reduces token usage from ~500 to ~150 tokens.
 * Kept for backward compatibility with parser.service.ts
 */
import { logger } from '../utils/logger';

export enum PromptStrategy {
  STRUCTURED = 'structured',
  FEW_SHOT = 'few_shot',
  CHAIN_OF_THOUGHT = 'chain_of_thought'
}

export interface ModelConfig {
  id: string;
  name: string;
  costPer1K: number;
  maxTokens: number;
  temperature: number;
  strategy: PromptStrategy;
}

export const MODEL_CONFIGS: Record<string, ModelConfig> = {
  'gemini-2.0-flash-001': {
    id: 'google/gemini-2.0-flash-001',
    name: 'Gemini 2.0 Flash',
    costPer1K: 0.075,
    maxTokens: 3000,
    temperature: 0.2,
    strategy: PromptStrategy.CHAIN_OF_THOUGHT
  },
  'gemini-2.5-flash-preview-05-20': {
    id: 'google/gemini-2.5-flash-preview-05-20',
    name: 'Gemini 2.5 Flash Preview',
    costPer1K: 0.075,
    maxTokens: 3000,
    temperature: 0.2,
    strategy: PromptStrategy.CHAIN_OF_THOUGHT
  },
  'deepseek-chat-v3-0324:free': {
    id: 'deepseek/deepseek-chat-v3-0324:free',
    name: 'DeepSeek Chat V3 (Free)',
    costPer1K: 0,
    maxTokens: 2500,
    temperature: 0.1,
    strategy: PromptStrategy.STRUCTURED
  },
  'deepseek-r1-0528-qwen3-8b:free': {
    id: 'deepseek/deepseek-r1-0528-qwen3-8b:free',
    name: 'DeepSeek R1 Qwen (Free)',
    costPer1K: 0,
    maxTokens: 2500,
    temperature: 0.1,
    strategy: PromptStrategy.STRUCTURED
  },
  'gpt-4o-mini': {
    id: 'openai/gpt-4o-mini',
    name: 'GPT-4 Optimized Mini',
    costPer1K: 0.15,
    maxTokens: 3000,
    temperature: 0.3,
    strategy: PromptStrategy.FEW_SHOT
  },
  'gpt-4.1-nano': {
    id: 'openai/gpt-4.1-nano',
    name: 'GPT-4.1 Nano',
    costPer1K: 0.05,
    maxTokens: 2000,
    temperature: 0.2,
    strategy: PromptStrategy.STRUCTURED
  },
  'claude-3-haiku': {
    id: 'anthropic/claude-3-haiku',
    name: 'Claude 3 Haiku',
    costPer1K: 0.25,
    maxTokens: 3000,
    temperature: 0.3,
    strategy: PromptStrategy.FEW_SHOT
  }
};

export class PromptManager {
  private currentModel: ModelConfig;

  constructor(modelId?: string) {
    const configKey = modelId || 'gemini-2.0-flash-001';
    this.currentModel = MODEL_CONFIGS[configKey] || MODEL_CONFIGS['gemini-2.0-flash-001'];
    logger.info('PromptManager initialized', { model: this.currentModel.name });
  }

  /**
   * Get the appropriate prompt based on the current model and strategy
   */
  getPrompt(text: string, source: string): string {
    const strategy = this.currentModel.strategy;
    
    switch (strategy) {
      case PromptStrategy.STRUCTURED:
        return this.getStructuredPrompt(text, source);
      case PromptStrategy.FEW_SHOT:
        return this.getFewShotPrompt(text, source);
      case PromptStrategy.CHAIN_OF_THOUGHT:
        return this.getChainOfThoughtPrompt(text, source);
      default:
        return this.getStructuredPrompt(text, source);
    }
  }

  /**
   * Structured prompt - Direct JSON output (fast, cheap)
   */
  private getStructuredPrompt(text: string, source: string): string {
    return `Extract trip from ${source} text. Return JSON only.

Text:
${text}

JSON:
{
  "title": "descriptive title",
  "destination": "main location",
  "startDate": "YYYY-MM-DD",
  "endDate": "YYYY-MM-DD",
  "activities": [{
    "title": "activity name",
    "type": "flight|accommodation|transport|dining|activity|shopping|car_rental|tour|sightseeing|entertainment|other",
    "startTime": "HH:MM",
    "location": "place",
    "price": number,
    "currency": "USD/EUR/etc",
    "day": 1
  }]
}`;
  }

  /**
   * Few-shot prompt - With examples (accurate, reliable)
   */
  private getFewShotPrompt(text: string, source: string): string {
    return `You are TravelViz, an expert travel itinerary parser with 20 years of experience organizing trips.

<examples>
Example 1 - Chat conversation:
Input: "Day 1: Arrive in Tokyo at 9am, check into Park Hyatt ($300/night). Afternoon: Visit Senso-ji Temple. Evening: Dinner at Sukiyabashi Jiro ($200)"
Output: {
  "title": "Tokyo Cultural Experience",
  "destination": "Tokyo",
  "activities": [
    {"title": "Arrive in Tokyo", "type": "transport", "startTime": "09:00", "day": 1},
    {"title": "Park Hyatt Tokyo", "type": "accommodation", "price": 300, "currency": "USD", "day": 1},
    {"title": "Visit Senso-ji Temple", "type": "sightseeing", "location": "Senso-ji Temple", "day": 1},
    {"title": "Dinner at Sukiyabashi Jiro", "type": "dining", "price": 200, "currency": "USD", "day": 1}
  ]
}

Example 2 - PDF extract:
Input: "Updated 15-Day European Travel Itinerary London, Madrid, Lisbon and Porto March 15-30 Budget $5000 Flight London Heathrow arrive 10:30am Hotel near British Museum check-in 3pm"
Output: {
  "title": "15-Day European Journey: London, Madrid, Lisbon & Porto",
  "destination": "Europe",
  "startDate": "2024-03-15",
  "endDate": "2024-03-30",
  "activities": [
    {"title": "Arrival at London Heathrow", "type": "flight", "startTime": "10:30", "location": "London Heathrow", "day": 1},
    {"title": "Hotel near British Museum", "type": "accommodation", "startTime": "15:00", "location": "British Museum area", "day": 1}
  ]
}

Example 3 - Incomplete data:
Input: "Barcelona trip next week. Sagrada Familia morning, Park Güell afternoon, tapas dinner"
Output: {
  "title": "Barcelona Highlights Tour",
  "destination": "Barcelona",
  "activities": [
    {"title": "Visit Sagrada Familia", "type": "sightseeing", "location": "Sagrada Familia", "startTime": "09:00", "day": 1},
    {"title": "Explore Park Güell", "type": "sightseeing", "location": "Park Güell", "startTime": "14:00", "day": 1},
    {"title": "Tapas Dinner", "type": "dining", "startTime": "19:00", "day": 1}
  ]
}
</examples>

Now parse this ${source} conversation:
${text}

Return ONLY valid JSON matching the examples above.`;
  }

  /**
   * Chain-of-thought prompt - Step-by-step reasoning (complex inputs)
   */
  private getChainOfThoughtPrompt(text: string, source: string): string {
    return `Parse ${source} travel text step-by-step:

1. Find destination & dates
2. Group activities by day
3. Extract times & prices
4. Infer missing info

Text:
${text}

Return JSON:
{
  "title": "descriptive title",
  "destination": "main location",
  "startDate": "YYYY-MM-DD",
  "endDate": "YYYY-MM-DD",
  "activities": [{
    "title": "activity",
    "type": "flight|accommodation|transport|dining|activity|shopping|car_rental|tour|sightseeing|entertainment|other",
    "startTime": "HH:MM",
    "location": "place",
    "price": number,
    "currency": "USD/EUR/etc",
    "day": 1
  }]
}`;
  }

  /**
   * Get model configuration
   */
  getModelConfig(): ModelConfig {
    return this.currentModel;
  }

  /**
   * Switch to a different model
   */
  switchModel(modelId: string): void {
    const config = MODEL_CONFIGS[modelId];
    if (config) {
      this.currentModel = config;
      logger.info('Switched model', { model: this.currentModel.name });
    } else {
      logger.warn('Model not found, keeping current', { requestedModel: modelId });
    }
  }

  /**
   * Get all available models
   */
  static getAvailableModels(): ModelConfig[] {
    return Object.values(MODEL_CONFIGS);
  }
}