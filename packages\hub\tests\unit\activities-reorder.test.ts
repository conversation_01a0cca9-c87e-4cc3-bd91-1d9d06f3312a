import { Request, Response } from 'express';
import { vi, describe, it, expect, beforeEach, afterEach } from 'vitest';
import { ActivitiesController } from '../controllers/activities.controller';
import { TripsService } from '../services/trips.service';
import { createSuccessResponse, createErrorResponse } from '@travelviz/shared';

// Mock dependencies
vi.mock('../services/trips.service');
vi.mock('../services/places.service');
vi.mock('../utils/logger', () => ({
  logger: {
    error: vi.fn(),
    info: vi.fn(),
    warn: vi.fn()
  }
}));

describe('ActivitiesController - reorderActivities', () => {
  let controller: ActivitiesController;
  let mockTripsService: any;
  let mockRequest: Partial<Request>;
  let mockResponse: Partial<Response>;
  let responseJson: ReturnType<typeof vi.fn>;
  let responseStatus: ReturnType<typeof vi.fn>;

  beforeEach(() => {
    // Reset mocks
    vi.clearAllMocks();

    // Create controller instance
    controller = new ActivitiesController();

    // Get mocked service
    mockTripsService = (controller as any).tripsService;

    // Setup response mocks
    responseJson = vi.fn();
    responseStatus = vi.fn().mockReturnValue({ json: responseJson });

    mockResponse = {
      json: responseJson,
      status: responseStatus
    };

    // Setup default request
    mockRequest = {
      params: { tripId: 'trip-123' },
      body: { orderedIds: ['activity-1', 'activity-2', 'activity-3'] },
      user: { id: 'user-123' }
    };
  });

  it('should successfully reorder activities', async () => {
    const mockReorderedActivities = [
      { id: 'activity-1', position: 0 },
      { id: 'activity-2', position: 1 },
      { id: 'activity-3', position: 2 }
    ];

    mockTripsService.reorderActivities.mockResolvedValue(mockReorderedActivities as any);

    await controller.reorderActivities(mockRequest as Request, mockResponse as Response);

    expect(mockTripsService.reorderActivities).toHaveBeenCalledWith(
      'trip-123',
      'user-123',
      ['activity-1', 'activity-2', 'activity-3']
    );
    expect(responseJson).toHaveBeenCalledWith(
      createSuccessResponse(mockReorderedActivities)
    );
  });

  it('should return 400 if orderedIds is not provided', async () => {
    mockRequest.body = {};

    await controller.reorderActivities(mockRequest as Request, mockResponse as Response);

    expect(responseStatus).toHaveBeenCalledWith(400);
    expect(responseJson).toHaveBeenCalledWith(
      createErrorResponse('orderedIds array is required')
    );
    expect(mockTripsService.reorderActivities).not.toHaveBeenCalled();
  });

  it('should return 400 if orderedIds is not an array', async () => {
    mockRequest.body = { orderedIds: 'not-an-array' };

    await controller.reorderActivities(mockRequest as Request, mockResponse as Response);

    expect(responseStatus).toHaveBeenCalledWith(400);
    expect(responseJson).toHaveBeenCalledWith(
      createErrorResponse('orderedIds must be an array')
    );
    expect(mockTripsService.reorderActivities).not.toHaveBeenCalled();
  });

  it('should return 401 if user is not authenticated', async () => {
    mockRequest.user = undefined;

    await controller.reorderActivities(mockRequest as Request, mockResponse as Response);

    expect(responseStatus).toHaveBeenCalledWith(401);
    expect(responseJson).toHaveBeenCalledWith(
      createErrorResponse('Unauthorized')
    );
    expect(mockTripsService.reorderActivities).not.toHaveBeenCalled();
  });

  it('should return 404 if trip not found', async () => {
    mockTripsService.reorderActivities.mockRejectedValue(new Error('Trip not found'));

    await controller.reorderActivities(mockRequest as Request, mockResponse as Response);

    expect(responseStatus).toHaveBeenCalledWith(404);
    expect(responseJson).toHaveBeenCalledWith(
      createErrorResponse('Trip not found')
    );
  });

  it('should return 403 if user is unauthorized', async () => {
    mockTripsService.reorderActivities.mockRejectedValue(
      new Error('Unauthorized to modify this trip')
    );

    await controller.reorderActivities(mockRequest as Request, mockResponse as Response);

    expect(responseStatus).toHaveBeenCalledWith(403);
    expect(responseJson).toHaveBeenCalledWith(
      createErrorResponse('Unauthorized to modify this trip')
    );
  });

  it('should return 400 for invalid activity IDs', async () => {
    mockTripsService.reorderActivities.mockRejectedValue(
      new Error('Invalid activity IDs provided')
    );

    await controller.reorderActivities(mockRequest as Request, mockResponse as Response);

    expect(responseStatus).toHaveBeenCalledWith(400);
    expect(responseJson).toHaveBeenCalledWith(
      createErrorResponse('Invalid activity IDs provided')
    );
  });

  it('should return 500 for database errors', async () => {
    mockTripsService.reorderActivities.mockRejectedValue(
      new Error('Failed to update activity positions')
    );

    await controller.reorderActivities(mockRequest as Request, mockResponse as Response);

    expect(responseStatus).toHaveBeenCalledWith(500);
    expect(responseJson).toHaveBeenCalledWith(
      createErrorResponse('Failed to update activity positions')
    );
  });

  it('should handle generic errors', async () => {
    mockTripsService.reorderActivities.mockRejectedValue(new Error('Unknown error'));

    await controller.reorderActivities(mockRequest as Request, mockResponse as Response);

    expect(responseStatus).toHaveBeenCalledWith(500);
    expect(responseJson).toHaveBeenCalledWith(
      createErrorResponse('Internal server error')
    );
  });
});