#!/usr/bin/env node

/**
 * Google Gemini API Connectivity Test
 * Tests API key, rate limits, and basic functionality
 */

require('dotenv').config({ path: '.env.local' });
const axios = require('axios');

const API_KEY = process.env.GOOGLE_GEMINI_API_KEY;
const BASE_URL = 'https://generativelanguage.googleapis.com/v1beta/models';

// Test models in order of preference
const TEST_MODELS = [
  'gemini-2.0-flash-exp',
  'gemini-2.5-flash',
  'gemini-2.5-pro'
];

async function testGeminiConnectivity() {
  console.log('🧪 Google Gemini API Connectivity Test');
  console.log('=' .repeat(50));
  
  // Check API key
  if (!API_KEY) {
    console.error('❌ GOOGLE_GEMINI_API_KEY not found in .env.local');
    console.log('📝 Please add your API key to .env.local:');
    console.log('   GOOGLE_GEMINI_API_KEY=your-api-key-here');
    console.log('🔗 Get API key: https://aistudio.google.com/app/apikey');
    process.exit(1);
  }
  
  console.log('✅ API key found');
  console.log(`🔑 Key preview: ${API_KEY.substring(0, 10)}...${API_KEY.substring(API_KEY.length - 4)}`);
  
  // Test each model
  for (const model of TEST_MODELS) {
    console.log(`\n🤖 Testing ${model}...`);
    
    try {
      const startTime = Date.now();
      
      const response = await axios.post(
        `${BASE_URL}/${model}:generateContent`,
        {
          contents: [{
            parts: [{
              text: 'Hello! Please respond with "API test successful" and nothing else.'
            }]
          }],
          generationConfig: {
            temperature: 0.1,
            maxOutputTokens: 50
          }
        },
        {
          headers: {
            'Content-Type': 'application/json',
            'x-goog-api-key': API_KEY
          },
          timeout: 30000
        }
      );
      
      const duration = Date.now() - startTime;
      const text = response.data.candidates?.[0]?.content?.parts?.[0]?.text;
      
      console.log(`✅ Success! (${duration}ms)`);
      console.log(`📝 Response: ${text?.trim()}`);
      
      // Log usage metadata if available
      if (response.data.usageMetadata) {
        const usage = response.data.usageMetadata;
        console.log(`📊 Tokens: ${usage.promptTokenCount} in, ${usage.candidatesTokenCount} out, ${usage.totalTokenCount} total`);
      }
      
    } catch (error) {
      console.log(`❌ Failed: ${error.message}`);
      
      if (axios.isAxiosError(error)) {
        const status = error.response?.status;
        const statusText = error.response?.statusText;
        
        console.log(`🔍 HTTP ${status}: ${statusText}`);
        
        // Specific error guidance
        switch (status) {
          case 400:
            console.log('💡 Bad Request - Check your request format');
            break;
          case 401:
            console.log('💡 Unauthorized - Check your API key');
            break;
          case 403:
            console.log('💡 Forbidden - API key may be invalid or restricted');
            break;
          case 429:
            console.log('💡 Rate Limited - You\'re making requests too quickly');
            console.log('   Free tier limits: 5-15 requests per minute');
            break;
          case 503:
            console.log('💡 Service Unavailable - Google\'s servers are overloaded');
            console.log('   Try again in a few minutes or upgrade to paid tier');
            break;
          default:
            console.log('💡 Unexpected error - Check Google AI status page');
        }
        
        // Show response details if available
        if (error.response?.data) {
          console.log('📄 Error details:', JSON.stringify(error.response.data, null, 2));
        }
      }
    }
    
    // Rate limiting delay
    console.log('⏳ Waiting 3 seconds to respect rate limits...');
    await new Promise(resolve => setTimeout(resolve, 3000));
  }
  
  console.log('\n📋 Rate Limit Information:');
  console.log('Free Tier Limits (per minute):');
  console.log('  • Gemini 2.0 Flash: 15 RPM, 1M TPM');
  console.log('  • Gemini 2.5 Flash: 10 RPM, 250K TPM');
  console.log('  • Gemini 2.5 Pro: 5 RPM, 250K TPM');
  console.log('\n💰 To increase limits:');
  console.log('  1. Enable billing in Google Cloud Console');
  console.log('  2. Upgrade to Tier 1+ in AI Studio');
  console.log('  3. Tier 1: 1000-4000 RPM (requires billing)');
  console.log('  4. Tier 2: 2000-20000 RPM (requires $250+ spend)');
}

// Run the test
testGeminiConnectivity().catch(console.error);
