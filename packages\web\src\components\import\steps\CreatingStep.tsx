'use client';

import React, { useEffect, useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useRouter } from 'next/navigation';
import { CheckCircle2, Calendar, MapPin, Activity, Clock, Sparkles } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';
import confetti from 'canvas-confetti';

interface CreatingStepProps {
  tripId?: string;
  stats?: {
    totalActivities: number;
    totalDays: number;
    destination: string;
    startDate?: string;
  };
  className?: string;
}

interface Particle {
  id: number;
  x: number;
  y: number;
  size: number;
  color: string;
  delay: number;
}

export function CreatingStep({ tripId, stats, className }: CreatingStepProps) {
  const router = useRouter();
  const [countdown, setCountdown] = useState(5);
  const [particles, setParticles] = useState<Particle[]>([]);

  // Generate particles for animation
  useEffect(() => {
    const newParticles: Particle[] = [];
    const colors = ['#3B82F6', '#8B5CF6', '#10B981', '#F59E0B', '#EC4899'];
    
    for (let i = 0; i < 50; i++) {
      newParticles.push({
        id: i,
        x: Math.random() * 100,
        y: Math.random() * 100,
        size: Math.random() * 4 + 2,
        color: colors[Math.floor(Math.random() * colors.length)],
        delay: Math.random() * 2,
      });
    }
    
    setParticles(newParticles);
  }, []);

  // Confetti celebration
  useEffect(() => {
    if (!tripId) return;
    
    // Fire confetti from multiple locations
    const duration = 3 * 1000;
    const animationEnd = Date.now() + duration;
    const defaults = { startVelocity: 30, spread: 360, ticks: 60, zIndex: 0 };

      function randomInRange(min: number, max: number) {
        return Math.random() * (max - min) + min;
      }

      const interval: any = setInterval(function() {
        const timeLeft = animationEnd - Date.now();

        if (timeLeft <= 0) {
          clearInterval(interval);
          return;
        }

        const particleCount = 50 * (timeLeft / duration);
        
        // Fire from left
        confetti({
          ...defaults,
          particleCount,
          origin: { x: randomInRange(0.1, 0.3), y: Math.random() - 0.2 }
        });
        
        // Fire from right
        confetti({
          ...defaults,
          particleCount,
          origin: { x: randomInRange(0.7, 0.9), y: Math.random() - 0.2 }
        });
      }, 250);

      return () => clearInterval(interval);
  }, [tripId]);

  // Countdown timer
  useEffect(() => {
    if (tripId && countdown > 0) {
      const timer = setTimeout(() => {
        setCountdown(countdown - 1);
      }, 1000);
      return () => clearTimeout(timer);
    } else if (tripId && countdown === 0) {
      router.push(`/trips/${tripId}`);
    }
    return undefined;
  }, [tripId, countdown, router]);

  const handleViewTrip = () => {
    if (tripId) {
      router.push(`/trips/${tripId}`);
    }
  };

  return (
    <div className={cn("relative flex flex-col items-center justify-center min-h-[500px] p-8", className)}>
      {/* Background particles */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        {particles.map((particle) => (
          <motion.div
            key={particle.id}
            className="absolute rounded-full"
            style={{
              left: `${particle.x}%`,
              top: `${particle.y}%`,
              width: particle.size,
              height: particle.size,
              backgroundColor: particle.color,
            }}
            animate={{
              y: [-20, -100],
              opacity: [0, 1, 0],
            }}
            transition={{
              duration: 3,
              delay: particle.delay,
              repeat: Infinity,
              ease: "easeOut",
            }}
          />
        ))}
      </div>

      <AnimatePresence mode="wait">
        {!tripId ? (
          // Creating animation
          <motion.div
            key="creating"
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.9 }}
            className="text-center z-10"
          >
            <motion.div
              animate={{ rotate: 360 }}
              transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
              className="inline-block mb-6"
            >
              <Sparkles className="w-16 h-16 text-blue-500" />
            </motion.div>
            
            <h2 className="text-2xl font-bold text-gray-900 mb-2">
              Creating Your Itinerary
            </h2>
            <p className="text-gray-600">
              We're organizing your trip into a beautiful visual timeline...
            </p>
          </motion.div>
        ) : (
          // Success animation
          <motion.div
            key="success"
            initial={{ opacity: 0, scale: 0.5 }}
            animate={{ opacity: 1, scale: 1 }}
            className="text-center z-10"
          >
            <motion.div
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              transition={{ type: "spring", stiffness: 200, damping: 10 }}
              className="inline-block mb-6"
            >
              <CheckCircle2 className="w-20 h-20 text-green-500" />
            </motion.div>
            
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              Your Trip is Ready!
            </h2>
            
            {stats && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.2 }}
                className="grid grid-cols-2 gap-4 max-w-md mx-auto mb-8"
              >
                <div className="bg-blue-50 rounded-lg p-4">
                  <div className="flex items-center justify-center gap-2 text-blue-700">
                    <Activity className="w-5 h-5" />
                    <span className="text-2xl font-bold">{stats.totalActivities}</span>
                  </div>
                  <p className="text-sm text-blue-600 mt-1">Activities</p>
                </div>
                
                <div className="bg-purple-50 rounded-lg p-4">
                  <div className="flex items-center justify-center gap-2 text-purple-700">
                    <Calendar className="w-5 h-5" />
                    <span className="text-2xl font-bold">{stats.totalDays}</span>
                  </div>
                  <p className="text-sm text-purple-600 mt-1">Days</p>
                </div>
                
                <div className="bg-green-50 rounded-lg p-4 col-span-2">
                  <div className="flex items-center justify-center gap-2 text-green-700">
                    <MapPin className="w-5 h-5" />
                    <span className="text-lg font-bold">{stats.destination}</span>
                  </div>
                  <p className="text-sm text-green-600 mt-1">Destination</p>
                </div>
              </motion.div>
            )}
            
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.4 }}
              className="space-y-4"
            >
              <Button
                size="lg"
                onClick={handleViewTrip}
                className="min-w-[200px]"
              >
                View Your Trip
              </Button>
              
              <div className="flex items-center justify-center gap-2 text-sm text-gray-500">
                <Clock className="w-4 h-4" />
                <span>Redirecting in {countdown} seconds...</span>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
}