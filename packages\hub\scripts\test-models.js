#!/usr/bin/env node

/**
 * Script to test and compare different AI models for travel itinerary parsing
 * Usage: node scripts/test-models.js [--model=<model-id>] [--test=<test-id>]
 */

const path = require('path');
const { spawn } = require('child_process');
require('dotenv').config({ path: path.join(__dirname, '../.env.local') });

// Parse command line arguments
const args = process.argv.slice(2);
const modelFilter = args.find(arg => arg.startsWith('--model='))?.split('=')[1];
const testFilter = args.find(arg => arg.startsWith('--test='))?.split('=')[1];

// Check for API key
if (!process.env.OPENROUTER_API_KEY) {
  console.error('❌ Error: OPENROUTER_API_KEY not found in .env.local');
  console.error('Please add your OpenRouter API key to continue.');
  process.exit(1);
}

console.log('🚀 Starting AI Model Comparison Tests');
console.log('=====================================\n');

if (modelFilter) {
  console.log(`📍 Testing specific model: ${modelFilter}`);
}
if (testFilter) {
  console.log(`📍 Testing specific case: ${testFilter}`);
}

console.log('\nAvailable models:');
console.log('- gemini-2.0-flash-001 (Gemini 2.0 Flash)');
console.log('- gemini-2.5-flash-preview-05-20 (Gemini 2.5 Preview)');
console.log('- deepseek-chat-v3-0324:free (DeepSeek Chat - Free)');
console.log('- deepseek-r1-0528-qwen3-8b:free (DeepSeek R1 - Free)');
console.log('- gpt-4o-mini (GPT-4 Optimized Mini)');
console.log('- gpt-4.1-nano (GPT-4.1 Nano)');
console.log('- claude-3-haiku (Claude 3 Haiku)\n');

// Build test command
let testCommand = 'pnpm vitest run src/integration/__tests__/model-comparison.test.ts';

if (modelFilter || testFilter) {
  const filters = [];
  if (modelFilter) filters.push(modelFilter);
  if (testFilter) filters.push(testFilter);
  testCommand += ` -t "${filters.join('.*')}"`;
}

console.log(`Running: ${testCommand}\n`);

// Run tests
const testProcess = spawn('pnpm', ['exec', 'vitest', 'run', 'src/integration/__tests__/model-comparison.test.ts'], {
  cwd: path.join(__dirname, '..'),
  stdio: 'inherit',
  shell: true,
  env: {
    ...process.env,
    NODE_ENV: 'test'
  }
});

testProcess.on('close', (code) => {
  if (code === 0) {
    console.log('\n✅ Model comparison tests completed successfully!');
    console.log('Check the test-results directory for detailed reports.');
  } else {
    console.error(`\n❌ Tests failed with code ${code}`);
  }
  process.exit(code);
});