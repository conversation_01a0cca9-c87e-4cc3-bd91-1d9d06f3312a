-- Create table for tracking failed login attempts
CREATE TABLE IF NOT EXISTS auth_failed_attempts (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  email TEXT NOT NULL,
  ip_address INET,
  user_agent TEXT,
  attempt_time TIMESTAMPTZ DEFAULT NOW(),
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create index for efficient lookups
CREATE INDEX idx_failed_attempts_email ON auth_failed_attempts(email);
CREATE INDEX idx_failed_attempts_time ON auth_failed_attempts(attempt_time);

-- Create table for account lockouts
CREATE TABLE IF NOT EXISTS auth_account_lockouts (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  email TEXT NOT NULL UNIQUE,
  locked_at TIMESTAMPTZ DEFAULT NOW(),
  locked_until TIMESTAMPTZ NOT NULL,
  unlock_token UUID DEFAULT gen_random_uuid(),
  reason TEXT,
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create index for lockout lookups
CREATE INDEX idx_lockouts_email ON auth_account_lockouts(email);
CREATE INDEX idx_lockouts_until ON auth_account_lockouts(locked_until);

-- Function to clean up old failed attempts (older than 24 hours)
CREATE OR REPLACE FUNCTION cleanup_old_failed_attempts()
RETURNS void AS $$
BEGIN
  DELETE FROM auth_failed_attempts 
  WHERE attempt_time < NOW() - INTERVAL '24 hours';
END;
$$ LANGUAGE plpgsql;

-- Function to check if account should be locked
CREATE OR REPLACE FUNCTION check_account_lockout(p_email TEXT)
RETURNS TABLE(should_lock BOOLEAN, attempt_count INT) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    COUNT(*) >= 5 AS should_lock,
    COUNT(*)::INT AS attempt_count
  FROM auth_failed_attempts
  WHERE email = p_email
  AND attempt_time > NOW() - INTERVAL '15 minutes';
END;
$$ LANGUAGE plpgsql;

-- RLS Policies
ALTER TABLE auth_failed_attempts ENABLE ROW LEVEL SECURITY;
ALTER TABLE auth_account_lockouts ENABLE ROW LEVEL SECURITY;

-- Only service role can access these tables
CREATE POLICY "Service role only" ON auth_failed_attempts
  FOR ALL USING (auth.role() = 'service_role');

CREATE POLICY "Service role only" ON auth_account_lockouts
  FOR ALL USING (auth.role() = 'service_role');

-- Create scheduled job to cleanup old attempts (if pg_cron is available)
-- Note: This requires pg_cron extension to be enabled
-- SELECT cron.schedule('cleanup-failed-attempts', '0 */6 * * *', 'SELECT cleanup_old_failed_attempts();');