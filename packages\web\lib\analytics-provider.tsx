'use client';

import { Analytics } from '@vercel/analytics/react';
import { SpeedInsights } from '@vercel/speed-insights/react';
import { createContext, useContext, useEffect, useRef } from 'react';

interface AnalyticsContextType {
  isInitialized: boolean;
  debug: boolean;
}

const AnalyticsContext = createContext<AnalyticsContextType>({
  isInitialized: false,
  debug: false,
});

interface AnalyticsProviderProps {
  children: React.ReactNode;
  debug?: boolean;
  beforeSend?: (event: any) => any | null;
}

/**
 * Analytics provider that wraps the app with Vercel Analytics
 * Includes performance monitoring and error tracking
 */
export function AnalyticsProvider({ 
  children, 
  debug = false,
  beforeSend 
}: AnalyticsProviderProps) {
  const initRef = useRef(false);

  useEffect(() => {
    if (initRef.current) {
      return;
    }
    
    initRef.current = true;
    
    if (debug) {
      console.log('Analytics initialized');
    }

    // Set up global error tracking
    const handleError = (event: ErrorEvent) => {
      if (typeof window !== 'undefined' && window.gtag) {
        window.gtag('event', 'exception', {
          description: event.error?.message || event.message,
          fatal: false,
        });
      }
    };

    const handleUnhandledRejection = (event: PromiseRejectionEvent) => {
      if (typeof window !== 'undefined' && window.gtag) {
        window.gtag('event', 'exception', {
          description: `Unhandled Promise Rejection: ${event.reason}`,
          fatal: false,
        });
      }
    };

    window.addEventListener('error', handleError);
    window.addEventListener('unhandledrejection', handleUnhandledRejection);

    return () => {
      window.removeEventListener('error', handleError);
      window.removeEventListener('unhandledrejection', handleUnhandledRejection);
    };
  }, [debug]);

  return (
    <AnalyticsContext.Provider value={{ isInitialized: true, debug }}>
      {children}
      <Analytics 
        debug={debug}
        beforeSend={beforeSend}
      />
      <SpeedInsights 
        debug={debug}
      />
    </AnalyticsContext.Provider>
  );
}

export function useAnalyticsContext() {
  return useContext(AnalyticsContext);
}

/**
 * Performance observer for Web Vitals and custom metrics
 */
export function usePerformanceObserver() {
  useEffect(() => {
    if (typeof window === 'undefined') return;

    // Observe Largest Contentful Paint (LCP)
    const lcpObserver = new PerformanceObserver((list) => {
      for (const entry of list.getEntries()) {
        if (window.gtag) {
          window.gtag('event', 'web_vitals', {
            event_category: 'Web Vitals',
            event_label: 'LCP',
            value: Math.round(entry.startTime),
            non_interaction: true,
          });
        }
      }
    });

    // Observe First Input Delay (FID)
    const fidObserver = new PerformanceObserver((list) => {
      for (const entry of list.getEntries()) {
        if (window.gtag) {
          window.gtag('event', 'web_vitals', {
            event_category: 'Web Vitals',
            event_label: 'FID',
            value: Math.round((entry as any).processingStart - entry.startTime),
            non_interaction: true,
          });
        }
      }
    });

    // Observe Cumulative Layout Shift (CLS)
    let clsValue = 0;
    const clsObserver = new PerformanceObserver((list) => {
      for (const entry of list.getEntries()) {
        if (!(entry as any).hadRecentInput) {
          clsValue += (entry as any).value;
        }
      }
    });

    // Report CLS on page visibility change
    const reportCLS = () => {
      if (window.gtag && clsValue > 0) {
        window.gtag('event', 'web_vitals', {
          event_category: 'Web Vitals',
          event_label: 'CLS',
          value: Math.round(clsValue * 1000),
          non_interaction: true,
        });
      }
    };

    try {
      lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] });
      fidObserver.observe({ entryTypes: ['first-input'] });
      clsObserver.observe({ entryTypes: ['layout-shift'] });

      document.addEventListener('visibilitychange', reportCLS);
    } catch (error) {
      // Performance Observer not supported
      console.warn('Performance Observer not supported:', error);
    }

    return () => {
      lcpObserver.disconnect();
      fidObserver.disconnect();
      clsObserver.disconnect();
      document.removeEventListener('visibilitychange', reportCLS);
    };
  }, []);
}

/**
 * Resource timing observer for loading performance
 */
export function useResourceTiming() {
  useEffect(() => {
    if (typeof window === 'undefined') return;

    const observer = new PerformanceObserver((list) => {
      for (const entry of list.getEntries()) {
        const resource = entry as PerformanceResourceTiming;
        
        // Track slow resources (>1s)
        if (resource.duration > 1000) {
          if (window.gtag) {
            window.gtag('event', 'slow_resource', {
              event_category: 'Performance',
              event_label: resource.name,
              value: Math.round(resource.duration),
              non_interaction: true,
            });
          }
        }

        // Track failed resources
        if (resource.transferSize === 0 && resource.decodedBodySize === 0) {
          if (window.gtag) {
            window.gtag('event', 'resource_failed', {
              event_category: 'Performance',
              event_label: resource.name,
              non_interaction: true,
            });
          }
        }
      }
    });

    try {
      observer.observe({ entryTypes: ['resource'] });
    } catch (error) {
      console.warn('Resource timing observer not supported:', error);
    }

    return () => observer.disconnect();
  }, []);
}