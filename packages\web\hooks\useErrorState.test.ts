import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { renderHook, act, waitFor } from '@testing-library/react';
import { useErrorState } from './useErrorState';

// Mock useAnalytics
vi.mock('./useAnalytics', () => ({
  useAnalytics: () => ({
    trackError: vi.fn(),
  }),
}));

// Mock localStorage
const mockLocalStorage = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
};

Object.defineProperty(window, 'localStorage', {
  value: mockLocalStorage,
});

// Mock window.location.reload
const mockReload = vi.fn();
Object.defineProperty(window.location, 'reload', {
  value: mockReload,
  writable: true,
});

describe('useErrorState', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    vi.useFakeTimers();
  });

  afterEach(() => {
    vi.useRealTimers();
  });

  it('should initialize with clean state', () => {
    const { result } = renderHook(() => useErrorState());

    expect(result.current.error).toBeNull();
    expect(result.current.errorType).toBeNull();
    expect(result.current.isRetrying).toBe(false);
    expect(result.current.retryCount).toBe(0);
    expect(result.current.isRecovering).toBe(false);
    expect(result.current.lastRetryAt).toBeNull();
    expect(result.current.canRetry).toBe(true);
    expect(result.current.hasMaxRetries).toBe(false);
  });

  describe('setError', () => {
    it('should set error with auto-classified type', () => {
      const { result } = renderHook(() => useErrorState());
      const networkError = new Error('Network fetch failed');

      act(() => {
        result.current.setError(networkError);
      });

      expect(result.current.error).toBe(networkError);
      expect(result.current.errorType).toBe('network');
      expect(result.current.retryCount).toBe(0);
    });

    it('should set error with explicit type', () => {
      const { result } = renderHook(() => useErrorState());
      const error = new Error('Something went wrong');

      act(() => {
        result.current.setError(error, 'validation');
      });

      expect(result.current.error).toBe(error);
      expect(result.current.errorType).toBe('validation');
    });

    it('should classify different error types correctly', () => {
      const { result } = renderHook(() => useErrorState());

      // Network error
      act(() => {
        result.current.setError(new Error('fetch request failed'));
      });
      expect(result.current.errorType).toBe('network');

      // Validation error
      act(() => {
        result.current.setError(new Error('invalid input provided'));
      });
      expect(result.current.errorType).toBe('validation');

      // Timeout error
      act(() => {
        result.current.setError(new Error('request timeout exceeded'));
      });
      expect(result.current.errorType).toBe('timeout');

      // Unknown error
      act(() => {
        result.current.setError(new Error('mysterious error'));
      });
      expect(result.current.errorType).toBe('unknown');
    });
  });

  describe('retry', () => {
    it('should handle successful retry', async () => {
      const mockOnRetry = vi.fn().mockResolvedValue(undefined);
      const { result } = renderHook(() => useErrorState({ onRetry: mockOnRetry }));

      // Set initial error
      act(() => {
        result.current.setError(new Error('test error'));
      });

      // Retry
      let retryPromise: Promise<boolean>;
      act(() => {
        retryPromise = result.current.retry();
      });

      expect(result.current.isRetrying).toBe(true);
      expect(result.current.retryCount).toBe(1);

      // Fast-forward timers
      act(() => {
        vi.advanceTimersByTime(2000);
      });

      await waitFor(() => {
        expect(mockOnRetry).toHaveBeenCalled();
      });

      // Wait for retry completion
      await waitFor(() => {
        expect(result.current.error).toBeNull();
        expect(result.current.isRetrying).toBe(false);
      });
    });

    it('should handle failed retry', async () => {
      const mockOnRetry = vi.fn().mockRejectedValue(new Error('retry failed'));
      const { result } = renderHook(() => useErrorState({ onRetry: mockOnRetry }));

      act(() => {
        result.current.setError(new Error('test error'));
      });

      act(() => {
        result.current.retry();
      });

      act(() => {
        vi.advanceTimersByTime(2000);
      });

      await waitFor(() => {
        expect(result.current.isRetrying).toBe(false);
        expect(result.current.error?.message).toBe('retry failed');
      });
    });

    it('should respect max retries limit', async () => {
      const { result } = renderHook(() => useErrorState({ maxRetries: 2 }));

      act(() => {
        result.current.setError(new Error('test error'));
      });

      // First retry
      act(() => {
        result.current.retry();
      });
      act(() => {
        vi.advanceTimersByTime(2000);
      });

      // Second retry
      act(() => {
        result.current.retry();
      });
      act(() => {
        vi.advanceTimersByTime(2000);
      });

      await waitFor(() => {
        expect(result.current.retryCount).toBe(2);
        expect(result.current.canRetry).toBe(false);
        expect(result.current.hasMaxRetries).toBe(true);
      });

      // Third retry should be blocked
      let retryResult: Promise<boolean> | undefined;
      act(() => {
        retryResult = result.current.retry();
      });

      expect(retryResult).toBe(undefined); // Blocked
    });

    it('should use custom retry delay', () => {
      const { result } = renderHook(() => useErrorState({ retryDelay: 5000 }));

      act(() => {
        result.current.setError(new Error('test error'));
      });

      act(() => {
        result.current.retry();
      });

      // Should still be retrying after 2 seconds
      act(() => {
        vi.advanceTimersByTime(2000);
      });
      expect(result.current.isRetrying).toBe(true);

      // Should complete after 5 seconds
      act(() => {
        vi.advanceTimersByTime(3000);
      });
    });
  });

  describe('recover', () => {
    it('should handle successful recovery with custom handler', async () => {
      const mockOnRecovery = vi.fn().mockResolvedValue(undefined);
      const { result } = renderHook(() => useErrorState({ onRecovery: mockOnRecovery }));

      act(() => {
        result.current.setError(new Error('test error'));
      });

      act(() => {
        result.current.recover('custom-strategy');
      });

      expect(result.current.isRecovering).toBe(true);

      await waitFor(() => {
        expect(mockOnRecovery).toHaveBeenCalledWith('custom-strategy');
        expect(result.current.error).toBeNull();
        expect(result.current.isRecovering).toBe(false);
      });
    });

    it('should handle network recovery with cached data', async () => {
      const cachedData = { data: 'cached', timestamp: Date.now() };
      mockLocalStorage.getItem.mockReturnValue(JSON.stringify(cachedData));

      const { result } = renderHook(() => useErrorState());

      act(() => {
        result.current.setError(new Error('network error'));
      });

      act(() => {
        result.current.recover('network');
      });

      await waitFor(() => {
        expect(mockLocalStorage.getItem).toHaveBeenCalledWith('lastSuccessfulData');
        expect(result.current.error).toBeNull();
      });
    });

    it('should handle network recovery with expired cache', async () => {
      const expiredData = { 
        data: 'cached', 
        timestamp: Date.now() - 25 * 60 * 60 * 1000 // 25 hours ago
      };
      mockLocalStorage.getItem.mockReturnValue(JSON.stringify(expiredData));

      const { result } = renderHook(() => useErrorState());

      act(() => {
        result.current.setError(new Error('network error'));
      });

      act(() => {
        result.current.recover('network');
      });

      await waitFor(() => {
        expect(mockLocalStorage.removeItem).toHaveBeenCalledWith('lastSuccessfulData');
        expect(result.current.error?.message).toBe('No cached data available');
      });
    });

    it('should handle validation recovery', async () => {
      const { result } = renderHook(() => useErrorState());

      act(() => {
        result.current.setError(new Error('validation error'));
      });

      act(() => {
        result.current.recover('validation');
      });

      await waitFor(() => {
        expect(result.current.error).toBeNull();
      });
    });

    it('should handle unknown recovery with page reload', async () => {
      const { result } = renderHook(() => useErrorState());

      act(() => {
        result.current.setError(new Error('unknown error'));
      });

      act(() => {
        result.current.recover('unknown');
      });

      await waitFor(() => {
        expect(mockReload).toHaveBeenCalled();
      });
    });
  });

  describe('reset', () => {
    it('should reset all state to initial values', () => {
      const { result } = renderHook(() => useErrorState());

      // Set some error state
      act(() => {
        result.current.setError(new Error('test error'));
      });

      expect(result.current.error).not.toBeNull();

      // Reset
      act(() => {
        result.current.reset();
      });

      expect(result.current.error).toBeNull();
      expect(result.current.errorType).toBeNull();
      expect(result.current.isRetrying).toBe(false);
      expect(result.current.retryCount).toBe(0);
      expect(result.current.isRecovering).toBe(false);
      expect(result.current.lastRetryAt).toBeNull();
    });
  });

  describe('state flags', () => {
    it('should correctly calculate canRetry flag', () => {
      const { result } = renderHook(() => useErrorState({ maxRetries: 2 }));

      expect(result.current.canRetry).toBe(true);

      act(() => {
        result.current.setError(new Error('test error'));
      });

      // After setting error, should still be able to retry
      expect(result.current.canRetry).toBe(true);

      // Simulate reaching max retries
      act(() => {
        result.current.retry();
      });
      act(() => {
        vi.advanceTimersByTime(2000);
      });

      act(() => {
        result.current.retry();
      });
      act(() => {
        vi.advanceTimersByTime(2000);
      });

      expect(result.current.canRetry).toBe(false);
      expect(result.current.hasMaxRetries).toBe(true);
    });
  });
});