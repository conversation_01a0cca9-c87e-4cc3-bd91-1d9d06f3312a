#!/usr/bin/env node

/**
 * Environment Validation Test
 * 
 * Validates environment configuration for different deployment environments
 * Checks for required variables, validates formats, and ensures security
 */

const fs = require('fs');
const path = require('path');
const crypto = require('crypto');

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function logHeader(message) {
  log('\n' + '='.repeat(50), 'cyan');
  log(`  ${message}`, 'bright');
  log('='.repeat(50), 'cyan');
}

function logSuccess(message) {
  log(`✅ ${message}`, 'green');
}

function logError(message) {
  log(`❌ ${message}`, 'red');
}

function logWarning(message) {
  log(`⚠️  ${message}`, 'yellow');
}

function logInfo(message) {
  log(`ℹ️  ${message}`, 'blue');
}

// Test results tracking
const testResults = {
  passed: 0,
  failed: 0,
  warnings: 0,
  tests: []
};

function logTest(name, result, details = {}) {
  const passed = result === true || (typeof result === 'object' && result.success);
  testResults.tests.push({ name, passed, details });
  
  if (passed) {
    testResults.passed++;
    logSuccess(name);
  } else {
    testResults.failed++;
    logError(`${name} - ${details.error || 'Failed'}`);
  }
}

function logWarningTest(name, details = {}) {
  testResults.warnings++;
  testResults.tests.push({ name, passed: true, warning: true, details });
  logWarning(`${name} - ${details.warning || 'Warning'}`);
}

// Load environment variables from .env files
function loadEnvFile(filePath) {
  if (fs.existsSync(filePath)) {
    const envContent = fs.readFileSync(filePath, 'utf8');
    const envVars = {};
    
    envContent.split('\n').forEach(line => {
      const trimmed = line.trim();
      if (trimmed && !trimmed.startsWith('#')) {
        const [key, ...valueParts] = trimmed.split('=');
        if (key && valueParts.length > 0) {
          envVars[key.trim()] = valueParts.join('=').trim().replace(/^["']|["']$/g, '');
        }
      }
    });
    
    return envVars;
  }
  return {};
}

async function validateSupabaseConfiguration() {
  logHeader('Supabase Configuration Validation');

  const hubEnvPath = path.join(__dirname, '../../packages/hub/.env.local');
  const webEnvPath = path.join(__dirname, '../../packages/web/.env.local');
  
  const hubEnv = loadEnvFile(hubEnvPath);
  const webEnv = loadEnvFile(webEnvPath);
  const allEnv = { ...process.env, ...hubEnv, ...webEnv };

  // Validate Supabase URL
  const supabaseUrl = allEnv.SUPABASE_URL;
  if (supabaseUrl) {
    const isValidUrl = supabaseUrl.startsWith('https://') && supabaseUrl.includes('.supabase.co');
    logTest('Supabase URL format valid', isValidUrl, {
      url: supabaseUrl.substring(0, 30) + '...',
      error: isValidUrl ? null : 'URL should be https://*.supabase.co format'
    });
  } else {
    logTest('Supabase URL present', false, { error: 'SUPABASE_URL not found' });
  }

  // Validate Supabase Anon Key
  const supabaseAnonKey = allEnv.SUPABASE_ANON_KEY;
  if (supabaseAnonKey) {
    const isValidKey = supabaseAnonKey.length > 100 && supabaseAnonKey.startsWith('eyJ');
    logTest('Supabase Anon Key format valid', isValidKey, {
      keyLength: supabaseAnonKey.length,
      error: isValidKey ? null : 'Key should be a JWT token starting with eyJ'
    });
  } else {
    logTest('Supabase Anon Key present', false, { error: 'SUPABASE_ANON_KEY not found' });
  }

  // Validate Supabase Service Role Key (if present)
  const supabaseServiceKey = allEnv.SUPABASE_SERVICE_ROLE_KEY;
  if (supabaseServiceKey) {
    const isValidServiceKey = supabaseServiceKey.length > 100 && supabaseServiceKey.startsWith('eyJ');
    logTest('Supabase Service Key format valid', isValidServiceKey, {
      keyLength: supabaseServiceKey.length,
      error: isValidServiceKey ? null : 'Service key should be a JWT token starting with eyJ'
    });
    
    // Security warning for service key
    if (process.env.NODE_ENV === 'production') {
      logWarningTest('Service key in production', {
        warning: 'Service role key detected in production - ensure it\'s properly secured'
      });
    }
  } else {
    logInfo('Supabase Service Key not present (optional)');
  }

  return true;
}

async function validateJWTConfiguration() {
  logHeader('JWT Configuration Validation');

  const hubEnvPath = path.join(__dirname, '../../packages/hub/.env.local');
  const hubEnv = loadEnvFile(hubEnvPath);
  const allEnv = { ...process.env, ...hubEnv };

  // Validate JWT Secret
  const jwtSecret = allEnv.JWT_SECRET;
  if (jwtSecret) {
    const isStrongSecret = jwtSecret.length >= 32;
    logTest('JWT Secret length adequate', isStrongSecret, {
      length: jwtSecret.length,
      error: isStrongSecret ? null : 'JWT Secret should be at least 32 characters'
    });

    // Check if it's a default/weak secret
    const weakSecrets = ['secret', 'jwt-secret', 'your-secret-key', '123456'];
    const isWeakSecret = weakSecrets.some(weak => jwtSecret.toLowerCase().includes(weak));
    logTest('JWT Secret is not weak/default', !isWeakSecret, {
      error: isWeakSecret ? 'JWT Secret appears to be a default/weak value' : null
    });

    // Check entropy (randomness)
    const entropy = calculateEntropy(jwtSecret);
    const hasGoodEntropy = entropy > 3.5;
    logTest('JWT Secret has good entropy', hasGoodEntropy, {
      entropy: entropy.toFixed(2),
      error: hasGoodEntropy ? null : 'JWT Secret has low entropy - consider using a more random value'
    });
  } else {
    logTest('JWT Secret present', false, { error: 'JWT_SECRET not found' });
  }

  return true;
}

async function validateAPIConfiguration() {
  logHeader('API Configuration Validation');

  const hubEnvPath = path.join(__dirname, '../../packages/hub/.env.local');
  const webEnvPath = path.join(__dirname, '../../packages/web/.env.local');
  
  const hubEnv = loadEnvFile(hubEnvPath);
  const webEnv = loadEnvFile(webEnvPath);
  const allEnv = { ...process.env, ...hubEnv, ...webEnv };

  // Validate Google Places API Key
  const googlePlacesKey = allEnv.GOOGLE_PLACES_API_KEY;
  if (googlePlacesKey) {
    const isValidFormat = googlePlacesKey.length > 30 && googlePlacesKey.startsWith('AIza');
    logTest('Google Places API Key format valid', isValidFormat, {
      keyLength: googlePlacesKey.length,
      error: isValidFormat ? null : 'Google API Key should start with AIza and be longer than 30 chars'
    });
  } else {
    logWarningTest('Google Places API Key missing', {
      warning: 'Places functionality will not work without this key'
    });
  }

  // Validate OpenAI API Key
  const openaiKey = allEnv.OPENAI_API_KEY;
  if (openaiKey) {
    const isValidFormat = openaiKey.startsWith('sk-') && openaiKey.length > 40;
    logTest('OpenAI API Key format valid', isValidFormat, {
      keyLength: openaiKey.length,
      error: isValidFormat ? null : 'OpenAI API Key should start with sk- and be longer than 40 chars'
    });
  } else {
    logWarningTest('OpenAI API Key missing', {
      warning: 'AI import functionality may be limited without this key'
    });
  }

  // Validate URLs
  const appUrl = allEnv.NEXT_PUBLIC_APP_URL;
  if (appUrl) {
    const isValidUrl = appUrl.startsWith('http') && !appUrl.endsWith('/');
    logTest('App URL format valid', isValidUrl, {
      url: appUrl,
      error: isValidUrl ? null : 'App URL should start with http and not end with /'
    });
  } else {
    logWarningTest('App URL not set', {
      warning: 'NEXT_PUBLIC_APP_URL not set - may cause issues with redirects'
    });
  }

  const apiBaseUrl = allEnv.API_BASE_URL;
  if (apiBaseUrl) {
    const isValidUrl = apiBaseUrl.startsWith('http') && !apiBaseUrl.endsWith('/');
    logTest('API Base URL format valid', isValidUrl, {
      url: apiBaseUrl,
      error: isValidUrl ? null : 'API Base URL should start with http and not end with /'
    });
  } else {
    logInfo('API Base URL not set (will use default)');
  }

  return true;
}

async function validateSecurityConfiguration() {
  logHeader('Security Configuration Validation');

  const hubEnvPath = path.join(__dirname, '../../packages/hub/.env.local');
  const webEnvPath = path.join(__dirname, '../../packages/web/.env.local');
  
  const hubEnv = loadEnvFile(hubEnvPath);
  const webEnv = loadEnvFile(webEnvPath);
  const allEnv = { ...process.env, ...hubEnv, ...webEnv };

  // Check NODE_ENV
  const nodeEnv = allEnv.NODE_ENV || process.env.NODE_ENV;
  const isProduction = nodeEnv === 'production';
  logTest('NODE_ENV is set', !!nodeEnv, {
    nodeEnv,
    error: nodeEnv ? null : 'NODE_ENV should be set'
  });

  if (isProduction) {
    logInfo('Production environment detected - running additional security checks');
    
    // In production, certain values should not be defaults
    const productionChecks = [
      { key: 'JWT_SECRET', shouldNotContain: ['secret', 'jwt', 'default'] },
    ];

    productionChecks.forEach(check => {
      const value = allEnv[check.key];
      if (value) {
        const hasInsecureValue = check.shouldNotContain.some(insecure => 
          value.toLowerCase().includes(insecure)
        );
        logTest(`${check.key} is production-ready`, !hasInsecureValue, {
          error: hasInsecureValue ? `${check.key} contains insecure default values` : null
        });
      }
    });
  }

  // Check for common security issues
  const allEnvKeys = Object.keys(allEnv);
  const sensitiveKeys = allEnvKeys.filter(key => 
    key.includes('SECRET') || 
    key.includes('KEY') || 
    key.includes('PASSWORD') || 
    key.includes('TOKEN')
  );

  logInfo(`Found ${sensitiveKeys.length} sensitive environment variables`);
  
  // Check if sensitive values are properly secured (not too short or obvious)
  sensitiveKeys.forEach(key => {
    const value = allEnv[key];
    if (value && value.length < 16) {
      logWarningTest(`${key} length`, {
        warning: `${key} is quite short (${value.length} chars) - consider using a longer value`
      });
    }
  });

  return true;
}

async function validateTestConfiguration() {
  logHeader('Test Configuration Validation');

  const hubEnvPath = path.join(__dirname, '../../packages/hub/.env.local');
  const webEnvPath = path.join(__dirname, '../../packages/web/.env.local');
  
  const hubEnv = loadEnvFile(hubEnvPath);
  const webEnv = loadEnvFile(webEnvPath);
  const allEnv = { ...process.env, ...hubEnv, ...webEnv };

  // Validate test user credentials
  const testUserEmail = allEnv.TEST_USER_EMAIL;
  const testUserPassword = allEnv.TEST_USER_PASSWORD;

  if (testUserEmail) {
    const isValidEmail = testUserEmail.includes('@') && testUserEmail.includes('.');
    logTest('Test user email format valid', isValidEmail, {
      email: testUserEmail,
      error: isValidEmail ? null : 'Test user email format invalid'
    });
  } else {
    logWarningTest('Test user email missing', {
      warning: 'TEST_USER_EMAIL not set - tests may fail'
    });
  }

  if (testUserPassword) {
    const isStrongPassword = testUserPassword.length >= 8;
    logTest('Test user password adequate', isStrongPassword, {
      length: testUserPassword.length,
      error: isStrongPassword ? null : 'Test user password should be at least 8 characters'
    });
  } else {
    logWarningTest('Test user password missing', {
      warning: 'TEST_USER_PASSWORD not set - tests may fail'
    });
  }

  return true;
}

// Helper function to calculate entropy
function calculateEntropy(str) {
  const freq = {};
  for (let char of str) {
    freq[char] = (freq[char] || 0) + 1;
  }
  
  let entropy = 0;
  const len = str.length;
  
  for (let char in freq) {
    const p = freq[char] / len;
    entropy -= p * Math.log2(p);
  }
  
  return entropy;
}

async function generateEnvironmentReport() {
  logHeader('Generating Environment Report');

  const report = {
    timestamp: new Date().toISOString(),
    environment: process.env.NODE_ENV || 'development',
    ci: !!process.env.CI,
    platform: process.platform,
    nodeVersion: process.version,
    validation: {
      total: testResults.tests.length,
      passed: testResults.passed,
      failed: testResults.failed,
      warnings: testResults.warnings,
      successRate: Math.round((testResults.passed / testResults.tests.length) * 100)
    },
    tests: testResults.tests.map(test => ({
      name: test.name,
      passed: test.passed,
      warning: test.warning || false,
      error: test.details.error || null
    }))
  };

  const reportPath = path.join(__dirname, '../../test-results/env-validation-report.json');
  
  // Ensure directory exists
  const reportDir = path.dirname(reportPath);
  if (!fs.existsSync(reportDir)) {
    fs.mkdirSync(reportDir, { recursive: true });
  }

  fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
  logSuccess(`Environment report generated: ${reportPath}`);

  return report;
}

async function main() {
  logHeader('TravelViz Environment Validation');
  
  log('This script validates environment configuration for security and correctness', 'bright');
  log('It checks API keys, secrets, URLs, and other configuration values\n');

  try {
    // Run all validation steps
    await validateSupabaseConfiguration();
    await validateJWTConfiguration();
    await validateAPIConfiguration();
    await validateSecurityConfiguration();
    await validateTestConfiguration();
    
    // Generate report
    const report = await generateEnvironmentReport();
    
    // Summary
    logHeader('Environment Validation Results');
    
    if (testResults.failed === 0) {
      logSuccess('🎉 All environment validations passed!');
      
      if (testResults.warnings > 0) {
        logWarning(`⚠️  ${testResults.warnings} warnings found - review recommended`);
      }
      
      log('\nEnvironment is ready for:', 'bright');
      log('• Development and testing');
      log('• Production deployment');
      log('• CI/CD pipelines');
      
      process.exit(0);
    } else {
      logError(`❌ ${testResults.failed} environment validations failed`);
      
      if (testResults.warnings > 0) {
        logWarning(`⚠️  ${testResults.warnings} warnings also found`);
      }
      
      log('\nFailed validations:', 'bright');
      testResults.tests
        .filter(t => !t.passed)
        .forEach(t => {
          log(`• ${t.name}: ${t.details.error || 'Unknown error'}`);
        });
      
      log('\nTroubleshooting:', 'bright');
      log('• Check .env.local files in packages/hub and packages/web');
      log('• Ensure all API keys are valid and properly formatted');
      log('• Use strong, random values for secrets');
      log('• Verify Supabase configuration');
      
      process.exit(1);
    }
    
  } catch (error) {
    logError(`Fatal error: ${error.message}`);
    console.error(error.stack);
    process.exit(1);
  }
}

// Handle process termination
process.on('SIGINT', () => {
  log('\nEnvironment validation interrupted by user', 'yellow');
  process.exit(1);
});

// Run the validation
main();