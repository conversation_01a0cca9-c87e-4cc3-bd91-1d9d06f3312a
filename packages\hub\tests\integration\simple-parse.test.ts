import { describe, it, expect, beforeAll } from 'vitest';
import path from 'path';
import dotenv from 'dotenv';

// Load environment variables from .env.local BEFORE importing anything else
dotenv.config({ path: path.resolve(__dirname, '../../../.env.local') });

console.log('Test ENV CHECK:', {
  GOOGLE_GEMINI_API_KEY: !!process.env.GOOGLE_GEMINI_API_KEY,
  OPENROUTER_API_KEY: !!process.env.OPENROUTER_API_KEY,
});

import { getAIParserService } from '../../src/services/ai-parser.service';
import { logger } from '../../src/utils/logger';

describe('Simple Parse Test', () => {
  beforeAll(() => {
    // Enable all logs - force it by setting underlying private field
    (logger as any).logLevel = 0; // DEBUG = 0
    console.log('Logger level set to:', (logger as any).logLevel);
  });

  it('should create AIParserService', async () => {
    console.log('Creating AI parser service...');
    const service = getAIParserService();
    console.log('Service created:', !!service);
    
    const status = service.getCircuitBreakerStatus();
    console.log('Circuit breaker status:', status);
    
    expect(service).toBeDefined();
    expect(status.state).toBe('CLOSED');
  });

  it('should create parse session', async () => {
    console.log('Creating parse session...');
    const service = getAIParserService();
    
    // Use unique content to avoid deduplication
    const content = `User: Plan a trip to Paris ${Date.now()}\nAssistant: Here is your Paris trip...`;
    const sessionId = await service.createParseSession(content, 'test', '697b40b3-42d7-4b32-ad49-0220c2313643');
    
    console.log('Session created:', sessionId);
    expect(sessionId).toBeDefined();
    
    // Wait a bit
    await new Promise(resolve => setTimeout(resolve, 5000));
    
    // Check session
    const session = await service.getSession(sessionId);
    console.log('Session status:', session?.status, 'Error:', session?.error);
  });
});