#!/usr/bin/env node
/**
 * <PERSON><PERSON><PERSON> to fix common TypeScript errors in the codebase
 */

import { readFileSync, writeFileSync, readdirSync, statSync } from 'fs';
import { join } from 'path';

// Function to fix error handling in a file
function fixErrorHandling(filePath: string): boolean {
  let content = readFileSync(filePath, 'utf-8');
  let modified = false;

  // Fix pattern: error.message where error is unknown
  const errorPatterns = [
    // Pattern 1: if (error.message
    {
      pattern: /if\s*\(\s*error\.message/g,
      replacement: 'if (error && typeof error === \'object\' && \'message\' in error && (error as any).message'
    },
    // Pattern 2: error.message in catch blocks
    {
      pattern: /catch\s*\(error[:\s]*unknown\)\s*{\s*([^}]*?)error\.message/g,
      replacement: 'catch (error: unknown) { $1getErrorMessage(error)'
    },
    // Pattern 3: error.code access
    {
      pattern: /error\.code/g,
      replacement: '(error as any).code'
    },
    // Pattern 4: error.status access
    {
      pattern: /error\.status/g,
      replacement: '(error as any).status'
    }
  ];

  errorPatterns.forEach(({ pattern, replacement }) => {
    if (pattern.test(content)) {
      content = content.replace(pattern, replacement);
      modified = true;
    }
  });

  // Add import for getErrorMessage if needed and not already present
  if (modified && content.includes('getErrorMessage') && !content.includes('import { getErrorMessage')) {
    const importPattern = /^(import .* from .*;\n)+/m;
    const match = content.match(importPattern);
    if (match) {
      const lastImportEnd = match.index! + match[0].length;
      content = content.slice(0, lastImportEnd) + 
        "import { getErrorMessage } from '../utils/error-handler';\n" +
        content.slice(lastImportEnd);
    }
  }

  if (modified) {
    writeFileSync(filePath, content);
    console.log(`✅ Fixed ${filePath}`);
  }

  return modified;
}

// Function to recursively process directory
function processDirectory(dir: string): void {
  const entries = readdirSync(dir);
  
  entries.forEach(entry => {
    const fullPath = join(dir, entry);
    const stat = statSync(fullPath);
    
    if (stat.isDirectory() && !entry.includes('node_modules') && !entry.startsWith('.')) {
      processDirectory(fullPath);
    } else if (stat.isFile() && entry.endsWith('.ts') && !entry.endsWith('.d.ts')) {
      fixErrorHandling(fullPath);
    }
  });
}

// Main execution
console.log('🔧 Fixing TypeScript errors...\n');

const testsDir = join(__dirname, '..', 'src', 'tests');
const servicesDir = join(__dirname, '..', 'src', 'services');

console.log('Processing test files...');
processDirectory(testsDir);

console.log('\nProcessing service files...');
processDirectory(servicesDir);

console.log('\n✨ Done!');