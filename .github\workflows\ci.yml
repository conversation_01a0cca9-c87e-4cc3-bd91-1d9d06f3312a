name: CI

on:
  push:
    branches: [main, dev]
  pull_request:
    branches: [main, dev]

env:
  NODE_VERSION: '20'
  PNPM_VERSION: '9.15.9'

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

jobs:
  checks:
    name: Lint & Build
    runs-on: ubuntu-latest
    timeout-minutes: 15
    
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Setup Environment
        uses: ./.github/actions/setup-env
        with:
          node-version: ${{ env.NODE_VERSION }}
          pnpm-version: ${{ env.PNPM_VERSION }}

      - name: Type Check
        run: pnpm type-check

      - name: Lint
        run: pnpm lint
        continue-on-error: true  # Don't fail on lint warnings

      - name: Build
        run: pnpm build

      - name: Performance Budget Check
        run: |
          cd packages/web
          npm run bundle-size
        continue-on-error: false  # Fail on performance budget violations

      - name: Bundle Analysis
        if: github.event_name == 'pull_request'
        run: |
          cd packages/web
          ANALYZE=true npm run build
          echo "## 📊 Bundle Analysis" >> $GITHUB_STEP_SUMMARY
          echo "Bundle analysis completed. Check logs for detailed breakdown." >> $GITHUB_STEP_SUMMARY

      - name: Upload Build Artifacts
        if: github.ref == 'refs/heads/main'
        uses: actions/upload-artifact@v4
        with:
          name: build-artifacts
          path: |
            packages/*/dist
            packages/web/.next
          retention-days: 7

  test-unit:
    name: Unit Tests
    runs-on: ubuntu-latest
    timeout-minutes: 10
    
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Setup Environment
        uses: ./.github/actions/setup-env
        with:
          node-version: ${{ env.NODE_VERSION }}
          pnpm-version: ${{ env.PNPM_VERSION }}

      - name: Run Unit Tests
        run: pnpm test:unit
        env:
          CI: true
          NODE_ENV: test
          NODE_OPTIONS: '--max-old-space-size=6144 --expose-gc'
          JWT_SECRET: 'test-jwt-secret-key-with-at-least-32-characters-for-tests'

  test-integration-smoke:
    name: Integration Tests (Smoke)
    if: github.event_name == 'pull_request'
    runs-on: ubuntu-latest
    timeout-minutes: 10
    
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Setup Environment
        uses: ./.github/actions/setup-env
        with:
          node-version: ${{ env.NODE_VERSION }}
          pnpm-version: ${{ env.PNPM_VERSION }}

      - name: Run Smoke Integration Tests
        run: pnpm test:integration:smoke
        env:
          CI: true
          NODE_ENV: test
          NODE_OPTIONS: '--max-old-space-size=6144 --expose-gc'
          JWT_SECRET: 'test-jwt-secret-key-with-at-least-32-characters-for-tests'
          # Supabase test environment
          SUPABASE_URL: ${{ secrets.SUPABASE_TEST_URL }}
          SUPABASE_ANON_KEY: ${{ secrets.SUPABASE_TEST_ANON_KEY }}
          SUPABASE_SERVICE_ROLE_KEY: ${{ secrets.SUPABASE_TEST_SERVICE_ROLE_KEY }}
          # Redis test environment
          UPSTASH_REDIS_REST_URL: ${{ secrets.UPSTASH_TEST_REDIS_REST_URL }}
          UPSTASH_REDIS_REST_TOKEN: ${{ secrets.UPSTASH_TEST_REDIS_REST_TOKEN }}
          # AI Services (using free tiers for testing)
          OPENROUTER_API_KEY: ${{ secrets.OPENROUTER_API_KEY }}
          GOOGLE_AI_API_KEY: ${{ secrets.GOOGLE_AI_API_KEY }}

  test-integration-full:
    name: Integration Tests (Full Suite)
    if: github.ref == 'refs/heads/main' || github.ref == 'refs/heads/dev'
    runs-on: ubuntu-latest
    timeout-minutes: 25
    
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Setup Environment
        uses: ./.github/actions/setup-env
        with:
          node-version: ${{ env.NODE_VERSION }}
          pnpm-version: ${{ env.PNPM_VERSION }}

      - name: Run Full Integration Test Suite
        run: pnpm test:integration:full
        env:
          CI: true
          NODE_ENV: test
          NODE_OPTIONS: '--max-old-space-size=6144 --expose-gc'
          JWT_SECRET: 'test-jwt-secret-key-with-at-least-32-characters-for-tests'
          # Supabase test environment
          SUPABASE_URL: ${{ secrets.SUPABASE_TEST_URL }}
          SUPABASE_ANON_KEY: ${{ secrets.SUPABASE_TEST_ANON_KEY }}
          SUPABASE_SERVICE_ROLE_KEY: ${{ secrets.SUPABASE_TEST_SERVICE_ROLE_KEY }}
          # Redis test environment
          UPSTASH_REDIS_REST_URL: ${{ secrets.UPSTASH_TEST_REDIS_REST_URL }}
          UPSTASH_REDIS_REST_TOKEN: ${{ secrets.UPSTASH_TEST_REDIS_REST_TOKEN }}
          # AI Services
          OPENROUTER_API_KEY: ${{ secrets.OPENROUTER_API_KEY }}
          GOOGLE_AI_API_KEY: ${{ secrets.GOOGLE_AI_API_KEY }}

      - name: Upload Integration Test Results
        if: always()
        uses: actions/upload-artifact@v4
        with:
          name: integration-test-results
          path: |
            packages/hub/test-results/
            packages/hub/coverage/
          retention-days: 3

  test-performance:
    name: Performance Tests
    if: github.ref == 'refs/heads/main'
    runs-on: ubuntu-latest
    timeout-minutes: 15
    
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Setup Environment
        uses: ./.github/actions/setup-env
        with:
          node-version: ${{ env.NODE_VERSION }}
          pnpm-version: ${{ env.PNPM_VERSION }}

      - name: Run Performance Tests
        run: pnpm test:performance
        env:
          CI: true
          NODE_ENV: test
          NODE_OPTIONS: '--max-old-space-size=6144 --expose-gc'
          JWT_SECRET: 'test-jwt-secret-key-with-at-least-32-characters-for-tests'
          # Supabase test environment
          SUPABASE_URL: ${{ secrets.SUPABASE_TEST_URL }}
          SUPABASE_ANON_KEY: ${{ secrets.SUPABASE_TEST_ANON_KEY }}
          SUPABASE_SERVICE_ROLE_KEY: ${{ secrets.SUPABASE_TEST_SERVICE_ROLE_KEY }}
          # Redis test environment  
          UPSTASH_REDIS_REST_URL: ${{ secrets.UPSTASH_TEST_REDIS_REST_URL }}
          UPSTASH_REDIS_REST_TOKEN: ${{ secrets.UPSTASH_TEST_REDIS_REST_TOKEN }}

      - name: Upload Performance Results
        if: always()
        uses: actions/upload-artifact@v4
        with:
          name: performance-test-results
          path: |
            packages/hub/performance-results/
          retention-days: 7

  deploy-ready:
    name: Deploy Ready
    if: github.ref == 'refs/heads/main' && github.event_name == 'push'
    needs: [checks, test-unit, test-integration-full, test-performance]  # All tests must pass
    runs-on: ubuntu-latest
    
    steps:
      - name: Summary
        run: |
          echo "## ✅ Ready to Deploy" >> $GITHUB_STEP_SUMMARY
          echo "Build and type checks passed. Deployment will be triggered automatically." >> $GITHUB_STEP_SUMMARY