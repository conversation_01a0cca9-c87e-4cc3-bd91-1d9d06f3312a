#!/usr/bin/env node

/**
 * Test all AI models and routing logic
 */

const path = require('path');
require('dotenv').config({ path: path.join(__dirname, '../.env.local') });

// Import the parser service
const { ParserService } = require('../dist/services/parser.service');
const { TripsService } = require('../dist/services/trips.service');

const TEST_CASES = [
  {
    name: 'Short Simple Trip (< 500 chars)',
    text: `Weekend in Rome:
- Saturday: Colosseum tour at 10am, lunch at Campo de' Fiori
- Sunday: Vatican Museums, Sistine Chapel`,
    expectedModel: 'DeepSeek (free)'
  },
  {
    name: 'Complex Multi-Day Trip (> 500 chars)',
    text: `15-Day European Adventure

Day 1-3: London
- Day 1: Arrive Heathrow, check into The Savoy ($450/night), afternoon tea at Fortnum & Mason
- Day 2: Tower of London, Tower Bridge, Borough Market lunch, West End show
- Day 3: British Museum, shopping in Covent Garden, departure to Paris

Day 4-6: Paris  
- Day 4: Eurostar arrival, check into Le Meurice (€500/night), Eiffel Tower
- Day 5: Louvre, Musée d'Orsay, Seine river cruise
- Day 6: Versailles day trip, evening at Moulin Rouge

Day 7-9: Barcelona
- Day 7: Flight to Barcelona, check into Hotel Casa Fuster, Las Ramblas
- Day 8: Sagrada Familia, Park Güell, Gothic Quarter
- Day 9: Beach day at Barceloneta, tapas tour

Day 10-12: Rome
- Day 10: Train to Rome, check into Hotel de Russie, Trevi Fountain
- Day 11: Colosseum, Roman Forum, Palatine Hill
- Day 12: Vatican City, Sistine Chapel, St. Peter's Basilica

Day 13-15: Florence
- Day 13: Train to Florence, Uffizi Gallery, Ponte Vecchio
- Day 14: Day trip to Tuscany wine country
- Day 15: Duomo climb, shopping, departure`,
    expectedModel: 'Gemini (free)'
  }
];

async function testModels() {
  console.log('🧪 Testing All Models and Routing Logic');
  console.log('=' .repeat(50));
  
  // Initialize parser service
  const tripsService = {} as any; // Mock trips service
  const parserService = new ParserService(tripsService);
  
  for (const testCase of TEST_CASES) {
    console.log(`\n📝 Test: ${testCase.name}`);
    console.log(`Text length: ${testCase.text.length} chars`);
    console.log(`Expected routing: ${testCase.expectedModel}`);
    
    try {
      const startTime = Date.now();
      
      // Parse without specifying model (let smart routing decide)
      const result = await parserService.parseTextToTrip(
        testCase.text,
        'chatgpt'
      );
      
      const duration = Date.now() - startTime;
      
      console.log(`✅ Success in ${duration}ms`);
      console.log(`📋 Title: ${result.title}`);
      console.log(`📍 Destination: ${result.destination || 'Not specified'}`);
      console.log(`🎯 Activities: ${result.activities.length}`);
      
      // Check logs to see which model was actually used
      console.log(`\n`);
      
    } catch (error) {
      console.log(`❌ Error: ${error.message}`);
    }
  }
  
  // Test usage tracking
  console.log('\n📊 Testing Usage Tracking');
  console.log('=' .repeat(50));
  
  try {
    // Make a few more requests to test quota tracking
    for (let i = 0; i < 3; i++) {
      await parserService.parseTextToTrip(
        'Quick trip to Paris for the weekend',
        'chatgpt'
      );
    }
    
    console.log('✅ Usage tracking working correctly');
    console.log('Check logs above for usage counts');
    
  } catch (error) {
    console.log(`❌ Usage tracking error: ${error.message}`);
  }
  
  // Test caching
  console.log('\n💾 Testing Cache');
  console.log('=' .repeat(50));
  
  const cacheTestText = 'London trip: Visit Big Ben, Tower Bridge, British Museum';
  
  // First request (should miss cache)
  console.log('First request...');
  const start1 = Date.now();
  await parserService.parseTextToTrip(cacheTestText, 'chatgpt');
  const time1 = Date.now() - start1;
  console.log(`Time: ${time1}ms (cache miss expected)`);
  
  // Second request (should hit cache)
  console.log('\nSecond request (same text)...');
  const start2 = Date.now();
  await parserService.parseTextToTrip(cacheTestText, 'chatgpt');
  const time2 = Date.now() - start2;
  console.log(`Time: ${time2}ms (cache hit expected)`);
  
  if (time2 < time1 / 2) {
    console.log('✅ Cache is working! Second request was much faster');
  } else {
    console.log('⚠️  Cache might not be working properly');
  }
}

// First compile TypeScript
console.log('📦 Compiling TypeScript...');
const { execSync } = require('child_process');

try {
  execSync('npm run build', { 
    cwd: path.join(__dirname, '..'),
    stdio: 'inherit'
  });
  
  console.log('\n');
  
  // Then run tests
  testModels().catch(console.error);
  
} catch (error) {
  console.error('Failed to compile:', error.message);
}