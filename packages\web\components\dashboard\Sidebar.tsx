"use client";

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { 
  Calendar, 
  MapPin, 
  Clock, 
  CheckCircle, 
  Users, 
  Crown,
  TrendingUp,
  Star
} from 'lucide-react';

interface SidebarProps {
  selectedCategory: string;
  onCategoryChange: (category: string) => void;
}

const categories = [
  { id: 'all', label: 'All Trips', icon: MapPin, count: 8 },
  { id: 'upcoming', label: 'Upcoming', icon: Calendar, count: 3 },
  { id: 'in-progress', label: 'In Progress', icon: Clock, count: 1 },
  { id: 'completed', label: 'Completed', icon: CheckCircle, count: 4 },
  { id: 'shared', label: 'Shared with <PERSON>', icon: Users, count: 2 }
];

const quickStats = [
  { label: 'Countries Visited', value: 12, icon: MapPin },
  { label: 'Cities Explored', value: 47, icon: TrendingUp },
  { label: 'Total Days', value: 156, icon: Calendar },
  { label: 'Avg Rating', value: 4.8, icon: Star }
];

export function Sidebar({ selectedCategory, onCategoryChange }: SidebarProps) {
  return (
    <div className="space-y-6">
      {/* Categories */}
      <div className="bg-white rounded-xl shadow-sm p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Trip Categories</h3>
        <nav className="space-y-2">
          {categories.map((category) => (
            <button
              key={category.id}
              onClick={() => onCategoryChange(category.id)}
              className={`w-full flex items-center justify-between px-3 py-2 rounded-lg text-sm font-medium transition-colors ${
                selectedCategory === category.id
                  ? 'bg-orange-50 text-orange-600 border border-orange-200'
                  : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
              }`}
            >
              <div className="flex items-center">
                <category.icon className="h-4 w-4 mr-3" />
                {category.label}
              </div>
              <Badge variant="secondary" className="text-xs">
                {category.count}
              </Badge>
            </button>
          ))}
        </nav>
      </div>

      {/* Usage Meter */}
      <div className="bg-white rounded-xl shadow-sm p-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-gray-900">Usage This Month</h3>
          <Badge variant="outline" className="text-xs">Free Plan</Badge>
        </div>
        
        <div className="space-y-4">
          <div>
            <div className="flex justify-between text-sm text-gray-600 mb-2">
              <span>Trips Created</span>
              <span>2/3</span>
            </div>
            <Progress value={67} className="h-2" />
          </div>
          
          <div>
            <div className="flex justify-between text-sm text-gray-600 mb-2">
              <span>AI Generations</span>
              <span>7/10</span>
            </div>
            <Progress value={70} className="h-2" />
          </div>
          
          <div>
            <div className="flex justify-between text-sm text-gray-600 mb-2">
              <span>Document Uploads</span>
              <span>3/5</span>
            </div>
            <Progress value={60} className="h-2" />
          </div>
        </div>
      </div>

      {/* Upgrade Card */}
      <div className="bg-gradient-to-br from-orange-500 to-pink-500 rounded-xl p-6 text-white">
        <div className="flex items-center mb-3">
          <Crown className="h-6 w-6 text-yellow-300 mr-2" />
          <h3 className="text-lg font-semibold">Upgrade to Premium</h3>
        </div>
        <p className="text-white/90 text-sm mb-4">
          Unlock unlimited trips, AI generations, and premium features.
        </p>
        <ul className="space-y-1 text-xs text-white/80 mb-4">
          <li>• Unlimited everything</li>
          <li>• Clean PDF exports</li>
          <li>• Priority support</li>
        </ul>
        <Button 
          size="sm" 
          className="w-full bg-white text-orange-500 hover:bg-gray-100"
        >
          Upgrade Now - $6.99/mo
        </Button>
      </div>

      {/* Quick Stats */}
      <div className="bg-white rounded-xl shadow-sm p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Your Travel Stats</h3>
        <div className="grid grid-cols-2 gap-4">
          {quickStats.map((stat, index) => (
            <div key={index} className="text-center">
              <div className="inline-flex items-center justify-center w-8 h-8 bg-gray-100 rounded-lg mb-2">
                <stat.icon className="h-4 w-4 text-gray-600" />
              </div>
              <div className="text-lg font-bold text-gray-900">{stat.value}</div>
              <div className="text-xs text-gray-600">{stat.label}</div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}