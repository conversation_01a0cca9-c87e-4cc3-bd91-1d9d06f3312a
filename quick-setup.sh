#!/bin/bash

# Quick Setup Script for Claude Code + Next.js/TypeScript
# Run this script in your Next.js project root to configure optimal AI coding workflow

set -euo pipefail

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m'

print_header() {
    echo -e "${PURPLE}"
    echo "╔══════════════════════════════════════════════════════════════════╗"
    echo "║               Claude Code + Next.js/TypeScript Setup            ║"
    echo "║                   Automated AI Workflow Configuration           ║"
    echo "╚══════════════════════════════════════════════════════════════════╝"
    echo -e "${NC}"
}

print_info() { echo -e "${BLUE}ℹ${NC} $1"; }
print_success() { echo -e "${GREEN}✓${NC} $1"; }
print_warning() { echo -e "${YELLOW}⚠${NC} $1"; }
print_error() { echo -e "${RED}✗${NC} $1"; }

# Check dependencies
check_dependencies() {
    print_info "Checking dependencies..."

    local missing_deps=()

    if ! command -v jq >/dev/null 2>&1; then
        missing_deps+=("jq")
    fi

    if ! command -v node >/dev/null 2>&1; then
        missing_deps+=("node")
    fi

    if [[ ${#missing_deps[@]} -gt 0 ]]; then
        print_error "Missing dependencies: ${missing_deps[*]}"
        print_info "Install them with:"
        for dep in "${missing_deps[@]}"; do
            case $dep in
                "jq") echo "  • macOS: brew install jq" ;;
                "node") echo "  • Visit: https://nodejs.org" ;;
            esac
        done
        exit 1
    fi

    print_success "All dependencies found"
}

# Detect project configuration  
detect_project() {
    print_info "Detecting project configuration..."

    if [[ ! -f "package.json" ]]; then
        print_error "No package.json found. Are you in a Node.js project?"
        exit 1
    fi

    PROJECT_TYPE="nodejs"

    # Detect Next.js
    if jq -e '.dependencies.next or .devDependencies.next' package.json >/dev/null 2>&1 || [[ -f "next.config.js" ]] || [[ -f "next.config.ts" ]]; then
        FRAMEWORK="nextjs"
        print_success "Next.js project detected"
    else
        print_warning "Next.js not detected. Configuration will be generic."
        FRAMEWORK="react"
    fi

    # Detect TypeScript
    if [[ -f "tsconfig.json" ]] || jq -e '.devDependencies.typescript' package.json >/dev/null 2>&1; then
        LANGUAGE="typescript"
        print_success "TypeScript detected"
    else
        LANGUAGE="javascript"
        print_info "JavaScript project detected"
    fi

    # Detect testing framework
    if jq -e '.devDependencies.vitest' package.json >/dev/null 2>&1; then
        TESTING="vitest"
        print_success "Vitest detected"
    elif jq -e '.devDependencies.jest' package.json >/dev/null 2>&1; then
        TESTING="jest"
        print_success "Jest detected"
    else
        TESTING="none"
        print_warning "No testing framework detected"
    fi

    # Detect package manager
    if [[ -f "package-lock.json" ]]; then
        PKG_MANAGER="npm"
    elif [[ -f "yarn.lock" ]]; then
        PKG_MANAGER="yarn"
    elif [[ -f "pnpm-lock.yaml" ]]; then
        PKG_MANAGER="pnpm"
    else
        PKG_MANAGER="npm"
    fi

    print_success "Package manager: $PKG_MANAGER"
}

# Create directory structure
create_structure() {
    print_info "Creating Claude Code directory structure..."

    mkdir -p .claude/hooks
    mkdir -p .claude/templates
    mkdir -p .claude/scripts

    print_success "Directory structure created"
}

# Generate optimized CLAUDE.md
generate_claude_md() {
    print_info "Generating optimized CLAUDE.md..."

    cat > CLAUDE.md << EOF
# ${PWD##*/} - ${FRAMEWORK^} Development Guide

## Project Configuration
- **Framework**: $FRAMEWORK
- **Language**: $LANGUAGE
- **Testing**: $TESTING
- **Package Manager**: $PKG_MANAGER

## TDD Workflow - MANDATORY

### Phase 1: RED (Write Failing Tests)
Always start with failing tests that define the expected behavior.

### Phase 2: GREEN (Minimal Implementation)  
Write the minimal code needed to make tests pass.

### Phase 3: REFACTOR (Improve Code)
Optimize code while keeping all tests green.

## Commands
- **Dev**: \`$PKG_MANAGER run dev\`
- **Build**: \`$PKG_MANAGER run build\`
- **Test**: \`$PKG_MANAGER run test\`
- **Lint**: \`$PKG_MANAGER run lint\`

## Code Standards
- Follow TypeScript strict mode
- Use functional programming patterns
- Implement responsive design (mobile-first)
- Maintain 80%+ test coverage
- Document all public APIs

## Architecture Guidelines
- Components: \`src/components/\` or \`components/\`
- Pages: \`src/app/\` (App Router) or \`pages/\`
- Types: \`src/types/\` or co-located
- Utils: \`src/lib/\` or \`lib/\`

## Security Requirements
- Validate all inputs
- Sanitize user data
- Use HTTPS in production
- Implement CSRF protection
- Follow OWASP guidelines

## Performance Standards
- Core Web Vitals compliance
- Tree shaking optimization
- Lazy loading for routes
- Image optimization
- Bundle size monitoring

## Never Do These:
- ❌ Write code without tests
- ❌ Commit failing tests
- ❌ Skip type annotations
- ❌ Ignore accessibility
- ❌ Hardcode sensitive data

## AI Coding Guidelines
When working with AI assistants:
1. Always specify the exact Next.js version
2. Request TypeScript implementations by default  
3. Ask for tests before implementation
4. Verify all AI suggestions against documentation
5. Test generated code thoroughly
EOF

    print_success "CLAUDE.md generated with project-specific context"
}

# Generate hooks configuration
generate_hooks() {
    print_info "Generating Claude Code hooks configuration..."

    cat > .claude/settings.json << EOF
{
  "hooks": {
    "PreToolUse": [
      {
        "matcher": "Edit|Write|MultiEdit",
        "hooks": [
          {
            "type": "command",
            "command": "bash .claude/hooks/pre-validation.sh",
            "timeout": 30
          }
        ]
      }
    ],
    "PostToolUse": [
      {
        "matcher": "Edit|Write|MultiEdit", 
        "hooks": [
          {
            "type": "command",
            "command": "bash .claude/hooks/post-format.sh",
            "timeout": 60
          }
        ]
      }
    ],
    "Stop": [
      {
        "matcher": "",
        "hooks": [
          {
            "type": "command",
            "command": "bash .claude/hooks/session-summary.sh"
          }
        ]
      }
    ]
  }
}
EOF

    print_success "Hooks configuration generated"
}

# Generate hook scripts
generate_hook_scripts() {
    print_info "Generating hook scripts..."

    # Pre-validation hook
    cat > .claude/hooks/pre-validation.sh << 'EOF'
#!/bin/bash
set -euo pipefail

INPUT=$(cat)
TOOL_NAME=$(echo "$INPUT" | jq -r '.tool_name // "unknown"')
FILE_PATH=$(echo "$INPUT" | jq -r '.tool_input.file_path // .tool_input.file // ""')

# Skip non-file operations
[[ "$TOOL_NAME" =~ ^(Edit|Write|MultiEdit)$ ]] || exit 0
[[ -n "$FILE_PATH" && "$FILE_PATH" != "null" ]] || exit 0

echo "🔍 Validating $FILE_PATH..."

# TypeScript/JavaScript file validation
if [[ "$FILE_PATH" =~ \.(ts|tsx|js|jsx)$ ]]; then
    # Check for test files when editing implementation
    if [[ ! "$FILE_PATH" =~ (test|spec) ]]; then
        BASE_NAME=$(basename "$FILE_PATH" | sed 's/\.[^.]*$//')
        DIR_NAME=$(dirname "$FILE_PATH")

        if [[ ! -f "$DIR_NAME/$BASE_NAME.test.ts" && ! -f "$DIR_NAME/$BASE_NAME.test.tsx" && ! -f "$DIR_NAME/__tests__/$BASE_NAME.test.ts" ]]; then
            echo "⚠️  No test file found for $FILE_PATH" >&2
            echo "Consider following TDD: write tests first!" >&2
        fi
    fi
fi

exit 0
EOF

    # Post-formatting hook
    cat > .claude/hooks/post-format.sh << 'EOF'
#!/bin/bash
set -euo pipefail

INPUT=$(cat)
FILE_PATH=$(echo "$INPUT" | jq -r '.tool_input.file_path // .tool_input.file // ""')

[[ -n "$FILE_PATH" && "$FILE_PATH" != "null" && -f "$FILE_PATH" ]] || exit 0

echo "🔧 Processing $FILE_PATH..."

# Format TypeScript/JavaScript files
if [[ "$FILE_PATH" =~ \.(ts|tsx|js|jsx)$ ]]; then
    if command -v prettier >/dev/null 2>&1; then
        prettier --write "$FILE_PATH" 2>/dev/null || echo "⚠️  Prettier formatting failed"
    fi

    if command -v eslint >/dev/null 2>&1; then
        eslint --fix "$FILE_PATH" 2>/dev/null || echo "⚠️  ESLint issues found"
    fi
fi

echo "✅ Processed $FILE_PATH"
exit 0
EOF

    # Session summary hook
    cat > .claude/hooks/session-summary.sh << 'EOF'
#!/bin/bash
set -euo pipefail

echo "📊 Session Summary"
echo "=================="

# Count modified files
if [[ -f .claude/session_start ]]; then
    TS_FILES=$(find . -name "*.ts" -newer .claude/session_start 2>/dev/null | wc -l || echo "0")
    TSX_FILES=$(find . -name "*.tsx" -newer .claude/session_start 2>/dev/null | wc -l || echo "0")
    TEST_FILES=$(find . -name "*.test.*" -newer .claude/session_start 2>/dev/null | wc -l || echo "0")

    echo "TypeScript files: $TS_FILES"
    echo "React components: $TSX_FILES"
    echo "Test files: $TEST_FILES"
fi

# Quality check
if [[ -f "package.json" ]]; then
    echo ""
    echo "Quality Check:"
    echo "=============="

    if command -v npm >/dev/null 2>&1; then
        if npm run type-check >/dev/null 2>&1 || npx tsc --noEmit >/dev/null 2>&1; then
            echo "✅ TypeScript: PASSED"
        else
            echo "❌ TypeScript: ERRORS"
        fi

        if npm run lint >/dev/null 2>&1; then
            echo "✅ Linting: PASSED"
        else
            echo "⚠️  Linting: WARNINGS"
        fi
    fi
fi

echo ""
echo "🎯 Remember: Test-driven development for quality code!"
exit 0
EOF

    # Make scripts executable
    chmod +x .claude/hooks/*.sh

    print_success "Hook scripts generated and made executable"
}

# Generate useful templates
generate_templates() {
    print_info "Generating development templates..."

    # TDD Component Template
    cat > .claude/templates/component-tdd.md << 'EOF'
# TDD Component Development Template

## 1. Write Test First (RED)
```typescript
// __tests__/MyComponent.test.tsx
import { render, screen } from '@testing-library/react';
import { MyComponent } from '../MyComponent';

describe('MyComponent', () => {
  it('should render with required props', () => {
    render(<MyComponent title="Test" />);
    expect(screen.getByText('Test')).toBeInTheDocument();
  });
});
```

## 2. Implement Component (GREEN)
```typescript
// MyComponent.tsx
interface MyComponentProps {
  title: string;
}

export function MyComponent({ title }: MyComponentProps) {
  return <h1>{title}</h1>;
}
```

## 3. Refactor and Enhance (REFACTOR)
Add styling, accessibility, error handling, etc.
EOF

    print_success "Development templates created"
}

# Create session tracking
create_session_tracking() {
    touch .claude/session_start
    print_success "Session tracking initialized"
}

# Main setup function
main() {
    print_header

    check_dependencies
    detect_project
    create_structure
    generate_claude_md
    generate_hooks
    generate_hook_scripts
    generate_templates
    create_session_tracking

    echo ""
    echo -e "${GREEN}🎉 Setup Complete! 🎉${NC}"
    echo ""
    echo "Your Next.js/TypeScript project is now optimized for AI-assisted development with:"
    echo ""
    echo "✅ Auto-formatting and linting"
    echo "✅ TDD workflow enforcement"  
    echo "✅ Project-specific prompts"
    echo "✅ Quality gate validation"
    echo "✅ Session tracking and summaries"
    echo ""
    echo -e "${BLUE}Next Steps:${NC}"
    echo "1. Start Claude Code in your project directory"
    echo "2. Follow the TDD workflow in CLAUDE.md"
    echo "3. Use structured prompts for better AI responses"
    echo "4. Check .claude/hooks/ for customization options"
    echo ""
    echo -e "${YELLOW}Pro Tip:${NC} Run 'claude --debug' to see hook execution details"
}

# Run main function
main "$@"
