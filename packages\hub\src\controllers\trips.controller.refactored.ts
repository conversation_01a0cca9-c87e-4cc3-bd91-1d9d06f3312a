import { Request, Response } from 'express';
import { 
  CreateTripRequest,
  UpdateTripRequest,
  TripResponse,
  TripListResponse,
  CloneTripRequest,
  CloneTripResponse,
  CreateActivityRequest,
  UpdateActivityRequest,
  ActivityResponse,
  ReorderActivitiesRequest,
  TripFilters
} from '@travelviz/shared';
import { BaseController } from './base.controller';
import { TripsService } from '../services/trips.service';
import { Trip, Activity } from '../lib/supabase';
import { authorizationService } from '../services/authorization.service';

export class TripsController extends BaseController {
  private tripsService: TripsService;

  constructor() {
    super();
    this.tripsService = new TripsService();
  }

  /**
   * Create a new trip
   */
  createTrip = this.asyncHandler(async (req: Request, res: Response): Promise<void> => {
    const user = this.requireAuth(req);
    const createRequest: CreateTripRequest = req.body; // Already validated by middleware

    const trip = await this.tripsService.createTrip({
      ...createRequest,
      userId: user.id,
      startDate: createRequest.startDate,
      endDate: createRequest.endDate,
      tags: createRequest.tags || [],
      budgetCurrency: createRequest.budgetCurrency || 'USD',
    });

    const response: TripResponse = this.mapTripToResponse(trip, user.id);
    this.sendCreated(res, response);
  });

  /**
   * Get user's trips with optional filters
   */
  getUserTrips = this.asyncHandler(async (req: Request, res: Response): Promise<void> => {
    const user = this.requireAuth(req);
    const { page, limit } = this.getPagination(req);
    const filters: TripFilters = req.query as TripFilters; // Validated by middleware

    const trips = await this.tripsService.getUserTrips(user.id);
    
    // Apply filters
    const filteredTrips = this.filterTrips(trips, filters);
    
    // Paginate
    const start = (page - 1) * limit;
    const paginatedTrips = filteredTrips.slice(start, start + limit);
    
    const response: TripListResponse = {
      trips: paginatedTrips.map(trip => this.mapTripToResponse(trip, user.id)),
      ...this.getPaginationMeta(filteredTrips.length, page, limit),
    };
    
    this.sendSuccess(res, response);
  });

  /**
   * Get trip by ID
   */
  getTripById = this.asyncHandler(async (req: Request, res: Response): Promise<void> => {
    const { id } = req.params;
    const user = this.getAuthUser(req);
    
    // Check permissions
    const access = await authorizationService.getTripAccess(user?.id || null, id);
    if (!access.canView) {
      throw new Error('Trip not found or you do not have permission to view it');
    }

    const trip = await this.tripsService.getTripById(id, user?.id || '');
    if (!trip) {
      throw new Error('Trip not found');
    }

    const response: TripResponse = this.mapTripToResponse(trip, user?.id, access);
    this.sendSuccess(res, response);
  });

  /**
   * Update a trip
   */
  updateTrip = this.asyncHandler(async (req: Request, res: Response): Promise<void> => {
    const { id } = req.params;
    const user = this.requireAuth(req);
    const updateRequest: UpdateTripRequest = req.body;

    // Check permissions
    await authorizationService.ensurePermission(user.id, 'trip', id, 'edit');

    const trip = await this.tripsService.updateTrip(id, user.id, updateRequest);
    if (!trip) {
      throw new Error('Trip not found');
    }

    const response: TripResponse = this.mapTripToResponse(trip, user.id);
    this.sendSuccess(res, response);
  });

  /**
   * Delete a trip
   */
  deleteTrip = this.asyncHandler(async (req: Request, res: Response): Promise<void> => {
    const { id } = req.params;
    const user = this.requireAuth(req);

    // Check permissions
    await authorizationService.ensurePermission(user.id, 'trip', id, 'delete');

    const success = await this.tripsService.deleteTrip(id, user.id);
    if (!success) {
      throw new Error('Trip not found');
    }

    this.sendSuccess(res, { message: 'Trip deleted successfully' });
  });

  /**
   * Add activity to trip
   */
  addActivity = this.asyncHandler(async (req: Request, res: Response): Promise<void> => {
    const { tripId } = req.params;
    const user = this.requireAuth(req);
    const createRequest: CreateActivityRequest = req.body;

    // Check permissions
    await authorizationService.ensurePermission(user.id, 'trip', tripId, 'edit');

    const activity = await this.tripsService.addActivityToTrip(tripId, user.id, createRequest);
    
    const response: ActivityResponse = this.mapActivityToResponse(activity);
    this.sendCreated(res, response);
  });

  /**
   * Update activity
   */
  updateActivity = this.asyncHandler(async (req: Request, res: Response): Promise<void> => {
    const { id } = req.params;
    const user = this.requireAuth(req);
    const updateRequest: UpdateActivityRequest = req.body;

    // Check permissions
    await authorizationService.ensurePermission(user.id, 'activity', id, 'edit');

    // Validate time constraints
    if (updateRequest.startTime && updateRequest.endTime) {
      const start = new Date(updateRequest.startTime);
      const end = new Date(updateRequest.endTime);
      if (end <= start) {
        throw new Error('End time must be after start time');
      }
    }

    const activity = await this.tripsService.updateActivity(id, user.id, updateRequest);
    if (!activity) {
      throw new Error('Activity not found');
    }

    const response: ActivityResponse = this.mapActivityToResponse(activity);
    this.sendSuccess(res, response);
  });

  /**
   * Delete activity
   */
  deleteActivity = this.asyncHandler(async (req: Request, res: Response): Promise<void> => {
    const { id } = req.params;
    const user = this.requireAuth(req);

    // Check permissions
    await authorizationService.ensurePermission(user.id, 'activity', id, 'delete');

    const success = await this.tripsService.deleteActivity(id, user.id);
    if (!success) {
      throw new Error('Activity not found');
    }

    this.sendSuccess(res, { message: 'Activity deleted successfully' });
  });

  /**
   * Reorder activities
   */
  reorderActivities = this.asyncHandler(async (req: Request, res: Response): Promise<void> => {
    const { tripId } = req.params;
    const user = this.requireAuth(req);
    const { activityIds }: ReorderActivitiesRequest = req.body;

    // Check permissions
    await authorizationService.ensurePermission(user.id, 'trip', tripId, 'edit');

    const activities = await this.tripsService.reorderActivities(tripId, user.id, activityIds);
    
    const response = {
      activities: activities.map(activity => this.mapActivityToResponse(activity)),
      message: 'Activities reordered successfully',
    };
    
    this.sendSuccess(res, response);
  });

  /**
   * Clone a trip
   */
  cloneTrip = this.asyncHandler(async (req: Request, res: Response): Promise<void> => {
    const { id } = req.params;
    const user = this.requireAuth(req);
    const { title }: CloneTripRequest = req.body;

    // Check permissions
    const canClone = await authorizationService.canCloneTrip(user.id, id);
    if (!canClone) {
      throw new Error('Trip not found or not public');
    }

    const result = await this.tripsService.cloneTrip(id, user.id, title);
    
    const response: CloneTripResponse = {
      trip: this.mapTripToResponse(result.trip, user.id),
      activitiesCloned: result.activitiesCloned,
      message: `Successfully cloned trip with ${result.activitiesCloned} activities`,
      sourceTrip: result.sourceTrip,
    };
    
    this.sendCreated(res, response);
  });

  /**
   * Map internal trip model to API response
   */
  private mapTripToResponse(
    trip: Trip,
    userId?: string,
    access?: { canView: boolean; canEdit: boolean; canDelete: boolean; isOwner: boolean }
  ): TripResponse {
    return {
      id: trip.id,
      userId: trip.user_id,
      title: trip.title,
      description: trip.description,
      destination: trip.destination,
      start_date: trip.start_date,
      end_date: trip.end_date,
      status: trip.status,
      visibility: trip.visibility,
      cover_image: trip.cover_image,
      metadata: trip.metadata,
      tags: trip.tags,
      budget_amount: trip.budget_amount,
      budget_currency: trip.budget_currency,
      views: trip.views,
      created_at: trip.created_at,
      updated_at: trip.updated_at,
      activities: trip.activities,
      activitiesCount: trip.activities?.length || 0,
      isOwner: access?.isOwner ?? (userId === trip.user_id),
      canEdit: access?.canEdit ?? (userId === trip.user_id),
    };
  }

  /**
   * Map internal activity model to API response
   */
  private mapActivityToResponse(activity: Activity): ActivityResponse {
    return {
      id: activity.id,
      tripId: activity.trip_id,
      title: activity.title,
      description: activity.description,
      type: activity.type,
      position: activity.position,
      start_time: activity.start_time,
      end_time: activity.end_time,
      location: activity.location,
      location_lat: activity.location_lat,
      location_lng: activity.location_lng,
      price: activity.price,
      currency: activity.currency,
      booking_reference: activity.booking_reference,
      booking_url: activity.booking_url,
      affiliate_url: activity.affiliate_url,
      notes: activity.notes,
      metadata: activity.metadata,
      attachments: activity.attachments,
      created_at: activity.created_at,
      updated_at: activity.updated_at,
      canEdit: true, // Determined by trip permissions
      affiliateUrl: activity.affiliate_url || undefined,
    };
  }

  /**
   * Filter trips based on provided filters
   */
  private filterTrips(trips: Trip[], filters: TripFilters): Trip[] {
    let filtered = [...trips];

    if (filters.status) {
      filtered = filtered.filter(trip => trip.status === filters.status);
    }

    if (filters.visibility) {
      filtered = filtered.filter(trip => trip.visibility === filters.visibility);
    }

    if (filters.destination) {
      const search = filters.destination.toLowerCase();
      filtered = filtered.filter(trip => 
        trip.destination?.toLowerCase().includes(search)
      );
    }

    if (filters.startDateFrom) {
      filtered = filtered.filter(trip => 
        trip.start_date && new Date(trip.start_date) >= new Date(filters.startDateFrom!)
      );
    }

    if (filters.startDateTo) {
      filtered = filtered.filter(trip => 
        trip.start_date && new Date(trip.start_date) <= new Date(filters.startDateTo!)
      );
    }

    if (filters.tags && filters.tags.length > 0) {
      filtered = filtered.filter(trip => 
        filters.tags!.some(tag => trip.tags?.includes(tag))
      );
    }

    return filtered;
  }
}