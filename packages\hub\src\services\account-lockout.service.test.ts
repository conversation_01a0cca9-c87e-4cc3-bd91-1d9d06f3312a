import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { AccountLockoutService } from './account-lockout.service';
import { getSupabaseClient, TABLES } from '../lib/supabase';
import { logger } from '../utils/logger';

// Mock dependencies
vi.mock('../../lib/supabase', () => ({
  getSupabaseClient: vi.fn(),
  TABLES: {
    AUTH_FAILED_ATTEMPTS: 'auth_failed_attempts',
    AUTH_ACCOUNT_LOCKOUTS: 'auth_account_lockouts',
  },
}));

vi.mock('../../utils/logger', () => ({
  logger: {
    error: vi.fn(),
    warn: vi.fn(),
    info: vi.fn(),
  },
}));

describe('AccountLockoutService', () => {
  let service: AccountLockoutService;
  let mockSupabase: any;

  beforeEach(() => {
    vi.clearAllMocks();
    vi.useFakeTimers();
    
    service = new AccountLockoutService();
    
    // Create a proper chainable mock
    mockSupabase = {
      from: vi.fn().mockReturnThis(),
      insert: vi.fn().mockReturnThis(),
      select: vi.fn().mockReturnThis(),
      delete: vi.fn().mockReturnThis(),
      eq: vi.fn().mockReturnThis(),
      gte: vi.fn().mockReturnThis(),
      single: vi.fn().mockReturnThis(),
      upsert: vi.fn().mockReturnThis(),
      count: vi.fn().mockReturnThis(),
      head: vi.fn().mockReturnThis(),
    };
    
    // Make methods return the mock itself for chaining
    Object.keys(mockSupabase).forEach(method => {
      if (typeof mockSupabase[method] === 'function' && mockSupabase[method].mockReturnThis) {
        mockSupabase[method].mockReturnValue(mockSupabase);
      }
    });

    vi.mocked(getSupabaseClient).mockReturnValue(mockSupabase);
  });

  afterEach(() => {
    vi.useRealTimers();
  });

  describe('recordFailedAttempt', () => {
    it('should record a failed login attempt', async () => {
      const attemptData = {
        email: '<EMAIL>',
        ipAddress: '***********',
        userAgent: 'Mozilla/5.0',
      };

      // Mock insert - terminal operation
      mockSupabase.insert.mockReturnValueOnce(Promise.resolve({ error: null }));
      
      // Mock getRecentAttempts chain: from().select().eq().gte().count()
      let selectCallCount = 0;
      mockSupabase.select.mockImplementation(() => {
        selectCallCount++;
        if (selectCallCount === 2) { // Second select is for count query
          return mockSupabase;
        }
        return mockSupabase;
      });
      
      // count() is terminal and returns { count: number }
      mockSupabase.count.mockReturnValueOnce({ count: 3 });

      await service.recordFailedAttempt(attemptData);

      expect(mockSupabase.insert).toHaveBeenCalledWith({
        email: '<EMAIL>',
        ip_address: '***********',
        user_agent: 'Mozilla/5.0',
        attempted_at: expect.any(String),
      });
    });

    it.skip('should lock account after max attempts', async () => {
      const attemptData = {
        email: '<EMAIL>',
        ipAddress: '***********',
      };

      // Mock insert - terminal operation
      mockSupabase.insert.mockReturnValueOnce(Promise.resolve({ error: null }));
      
      // Mock getRecentAttempts to return MAX_ATTEMPTS
      mockSupabase.count.mockReturnValueOnce({ count: 5 });
      
      // Mock lockAccount upsert - terminal operation
      mockSupabase.upsert.mockReturnValueOnce(Promise.resolve({ error: null }));

      await service.recordFailedAttempt(attemptData);

      expect(mockSupabase.upsert).toHaveBeenCalledWith(
        expect.objectContaining({
          email: '<EMAIL>',
          locked_until: expect.any(String),
        }),
        { onConflict: 'email' }
      );
    });

    it('should handle errors gracefully', async () => {
      const attemptData = {
        email: '<EMAIL>',
      };

      mockSupabase.insert.mockRejectedValueOnce(new Error('Database error'));

      await expect(service.recordFailedAttempt(attemptData))
        .resolves.not.toThrow();

      expect(logger.error).toHaveBeenCalledWith(
        'Failed to record failed attempt:',
        expect.any(Object)
      );
    });

    it('should convert email to lowercase', async () => {
      const attemptData = {
        email: '<EMAIL>',
      };

      mockSupabase.insert.mockResolvedValueOnce({ error: null });
      mockSupabase.select.mockImplementationOnce(() => ({
        count: 'exact',
        head: true,
      }));
      mockSupabase.gte.mockResolvedValueOnce({ count: 1 });

      await service.recordFailedAttempt(attemptData);

      expect(mockSupabase.insert).toHaveBeenCalledWith(
        expect.objectContaining({
          email: '<EMAIL>',
        })
      );
    });
  });

  describe('checkAccountLockout', () => {
    it('should return locked status if account is locked', async () => {
      const email = '<EMAIL>';
      const futureDate = new Date(Date.now() + 10 * 60 * 1000); // 10 minutes in future

      mockSupabase.single.mockResolvedValueOnce({
        data: {
          email: '<EMAIL>',
          locked_until: futureDate.toISOString(),
        },
        error: null,
      });

      const result = await service.checkAccountLockout(email);

      expect(result.isLocked).toBe(true);
      expect(result.lockedUntil).toEqual(futureDate);
      expect(result.attemptCount).toBe(5); // MAX_ATTEMPTS
    });

    it.skip('should return unlocked status with attempt count', async () => {
      const email = '<EMAIL>';

      // Mock lockout check - no active lockout (single returns error)
      mockSupabase.single.mockResolvedValueOnce({
        data: null,
        error: { code: 'PGRST116' }, // No rows found
      });

      // Mock getRecentAttempts
      mockSupabase.count.mockReturnValueOnce({ count: 3 });

      const result = await service.checkAccountLockout(email);

      expect(result.isLocked).toBe(false);
      expect(result.attemptCount).toBe(3);
      expect(result.lockedUntil).toBeUndefined();
    });

    it('should handle errors and assume not locked', async () => {
      const email = '<EMAIL>';

      mockSupabase.single.mockRejectedValueOnce(new Error('Database error'));

      const result = await service.checkAccountLockout(email);

      expect(result.isLocked).toBe(false);
      expect(result.attemptCount).toBe(0);
      expect(logger.error).toHaveBeenCalled();
    });

    it('should check with lowercase email', async () => {
      const email = '<EMAIL>';

      // Mock lockout check
      mockSupabase.single.mockResolvedValueOnce({
        data: null,
        error: { code: 'PGRST116' },
      });

      // Mock getRecentAttempts
      mockSupabase.count.mockReturnValueOnce({ count: 0 });

      await service.checkAccountLockout(email);

      expect(mockSupabase.eq).toHaveBeenCalledWith('email', '<EMAIL>');
    });
  });

  describe('clearFailedAttempts', () => {
    it('should clear failed attempts and lockouts', async () => {
      const email = '<EMAIL>';

      // Mock delete operations - delete() is terminal when followed by eq()
      mockSupabase.eq.mockResolvedValue({ error: null });

      await service.clearFailedAttempts(email);

      expect(mockSupabase.delete).toHaveBeenCalledTimes(2);
      expect(mockSupabase.eq).toHaveBeenCalledWith('email', '<EMAIL>');
    });

    it('should handle errors gracefully', async () => {
      const email = '<EMAIL>';

      mockSupabase.delete.mockRejectedValueOnce(new Error('Database error'));

      await expect(service.clearFailedAttempts(email))
        .resolves.not.toThrow();

      expect(logger.error).toHaveBeenCalled();
    });
  });

  describe('getRecentAttempts', () => {
    it.skip('should count attempts within time window', async () => {
      const email = '<EMAIL>';

      // Mock count query - count() is terminal
      mockSupabase.count.mockReturnValueOnce({ count: 4 });

      // Access private method through any type assertion
      const count = await (service as any).getRecentAttempts(email);

      expect(count).toBe(4);
      expect(mockSupabase.gte).toHaveBeenCalledWith(
        'attempted_at',
        expect.any(String)
      );
    });

    it('should return 0 on error', async () => {
      const email = '<EMAIL>';

      mockSupabase.select.mockImplementationOnce(() => ({
        count: 'exact',
        head: true,
      }));
      mockSupabase.gte.mockRejectedValueOnce(new Error('Database error'));

      const count = await (service as any).getRecentAttempts(email);

      expect(count).toBe(0);
      expect(logger.error).toHaveBeenCalled();
    });
  });

  describe('lockAccount', () => {
    it('should create or update lockout record', async () => {
      const email = '<EMAIL>';
      const now = Date.now();
      vi.setSystemTime(now);

      mockSupabase.upsert.mockResolvedValueOnce({ error: null });

      await (service as any).lockAccount(email);

      expect(mockSupabase.upsert).toHaveBeenCalledWith(
        {
          email: '<EMAIL>',
          locked_until: new Date(now + 30 * 60 * 1000).toISOString(),
          created_at: new Date(now).toISOString(),
        },
        { onConflict: 'email' }
      );
    });

    it('should handle lock errors', async () => {
      const email = '<EMAIL>';

      mockSupabase.upsert.mockRejectedValueOnce(new Error('Database error'));

      await expect((service as any).lockAccount(email))
        .resolves.not.toThrow();

      expect(logger.error).toHaveBeenCalled();
    });
  });

  describe('time-based behavior', () => {
    it('should expire lockouts after timeout period', async () => {
      const email = '<EMAIL>';
      const now = Date.now();
      
      // Set lockout time to 31 minutes ago (past expiry)
      const expiredLockout = new Date(now - 31 * 60 * 1000).toISOString();

      // The query will find no active lockouts because gte filter excludes expired ones
      mockSupabase.single.mockResolvedValueOnce({
        data: null,
        error: { code: 'PGRST116' }, // No rows found
      });
      
      // Mock getRecentAttempts
      mockSupabase.count.mockReturnValueOnce({ count: 0 });

      const result = await service.checkAccountLockout(email);

      expect(result.isLocked).toBe(false);
    });

    it('should only count attempts within time window', async () => {
      const email = '<EMAIL>';
      const now = Date.now();
      vi.setSystemTime(now);

      // Mock insert - terminal operation
      mockSupabase.insert.mockReturnValueOnce(Promise.resolve({ error: null }));
      
      // Mock getRecentAttempts
      mockSupabase.count.mockReturnValueOnce({ count: 2 });

      await service.recordFailedAttempt({ email });

      // Verify the time window calculation (15 minutes ago)
      const expectedWindowStart = new Date(now - 15 * 60 * 1000).toISOString();
      expect(mockSupabase.gte).toHaveBeenCalledWith('attempted_at', expectedWindowStart);
    });
  });
});