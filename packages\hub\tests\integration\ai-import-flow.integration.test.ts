import { describe, it, expect, beforeAll, afterAll, beforeEach, vi } from 'vitest';
import request from 'supertest';
import { createServer } from '../../src/server';
import { getSupabaseClient } from '../../src/lib/supabase';
import { RedisConnectionPoolService } from '../../src/services/redis-connection-pool.service';
import { AIParserService } from '../../src/services/ai-parser.service';

// Skip these tests in CI - they require real API keys and authentication
const isCI = process.env.CI === 'true';
const describeIntegration = isCI ? describe.skip : describe;

// Real test credentials
const TEST_USER_EMAIL = '<EMAIL>';
const TEST_USER_PASSWORD = 'TestPassword123!';
const API_TIMEOUT = 60000;

// Sample conversations for different AI platforms
const sampleConversations = {
  chatgpt: {
    content: 'User: I need a 5-day trip to Tokyo in March.\nAssistant: Here is your Tokyo itinerary...',
    source: 'chatgpt' as const
  },
  claude: {
    content: 'Human: Plan a weekend in Barcelona.\nAssistant: I will help you plan your Barcelona weekend...',
    source: 'claude' as const
  },
  gemini: {
    content: 'User: Create a 3-day Paris itinerary.\nGemini: Here is your Paris itinerary...',
    source: 'gemini' as const
  }
};

// Mock Redis
vi.mock('../../services/redis-connection-pool.service', () => ({
  RedisConnectionPoolService: {
    getInstance: vi.fn(() => ({
      getClient: vi.fn().mockResolvedValue({
        get: vi.fn(),
        set: vi.fn(),
        del: vi.fn(),
        expire: vi.fn()
      })
    }))
  }
}));

// Helper to get auth token
async function getAuthToken(): Promise<string> {
  const supabase = getSupabaseClient();
  const { data, error } = await supabase.auth.signInWithPassword({
    email: TEST_USER_EMAIL,
    password: TEST_USER_PASSWORD
  });

  if (error) throw new Error(`Auth failed: ${error.message}`);
  return data.session?.access_token || '';
}

describeIntegration('AI Import Flow Integration Tests', () => {
  let authToken: string;
  let server: any;
  let app: any;

  beforeAll(async () => {
    // Get auth token first
    authToken = await getAuthToken();
    
    // Create and start server
    app = createServer();
    server = app.listen(0); // Random port
  }, API_TIMEOUT);

  afterAll(async () => {
    server?.close();
  });

  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('POST /api/import/conversation', () => {
    it('should import ChatGPT conversation successfully', async () => {
      const response = await request(server)
        .post('/api/import/conversation')
        .set('Authorization', `Bearer ${authToken}`)
        .send(sampleConversations.chatgpt)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveProperty('tripId');
      expect(response.body.data).toHaveProperty('sessionId');
    }, API_TIMEOUT);

    it('should import Claude conversation successfully', async () => {
      const response = await request(server)
        .post('/api/import/conversation')
        .set('Authorization', `Bearer ${authToken}`)
        .send(sampleConversations.claude)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveProperty('tripId');
      expect(response.body.data).toHaveProperty('sessionId');
    }, API_TIMEOUT);

    it('should import Gemini conversation successfully', async () => {
      const response = await request(server)
        .post('/api/import/conversation')
        .set('Authorization', `Bearer ${authToken}`)
        .send(sampleConversations.gemini)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveProperty('tripId');
      expect(response.body.data).toHaveProperty('sessionId');
    }, API_TIMEOUT);

    it('should reject invalid source', async () => {
      const response = await request(server)
        .post('/api/import/conversation')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          content: 'Some content',
          source: 'invalid-source'
        })
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.error).toContain('Invalid source');
    });

    it('should reject empty content', async () => {
      const response = await request(server)
        .post('/api/import/conversation')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          content: '',
          source: 'chatgpt'
        })
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.error).toContain('Content is required');
    });

    it('should require authentication', async () => {
      const response = await request(server)
        .post('/api/import/conversation')
        .send(sampleConversations.chatgpt)
        .expect(401);

      expect(response.body.success).toBe(false);
    });
  });
});