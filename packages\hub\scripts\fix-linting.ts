#!/usr/bin/env node

/**
 * <PERSON>ript to fix common linting issues in the hub package
 * Run with: npx tsx scripts/fix-linting.ts
 */

import { readFileSync, writeFileSync, readdirSync, statSync } from 'fs';
import { join } from 'path';

const SRC_DIR = join(__dirname, '..', 'src');

// Function to fix any types with unknown
function fixAnyTypes(content: string): string {
  // Replace catch (error: any) with catch (error: unknown)
  content = content.replace(/catch \(([\w]+): any\)/g, 'catch ($1: unknown)');
  
  // Replace } catch (error: any) { with } catch (error) {
  content = content.replace(/} catch \(([\w]+): any\) {/g, '} catch ($1) {');
  
  // Replace : any) => with : unknown) =>
  // But be careful not to replace legitimate any[] types
  content = content.replace(/: any\)(\s*=>\s*)/g, ': unknown)$1');
  
  return content;
}

// Function to remove unused parameters with underscore prefix
function fixUnusedParams(content: string): string {
  // For middleware functions that don't use req or next
  content = content.replace(/\(req(,\s*res,\s*next)?\)/g, (match, group1) => {
    if (group1) {
      return '(_req, res, next)';
    }
    return '(_req)';
  });
  
  return content;
}

// Function to remove unused imports
function removeUnusedImports(content: string): string {
  // Common unused imports we can safely remove
  const unusedImports = [
    { pattern: /import\s*{\s*JWTUser\s*}\s*from\s*['"][^'"]+['"];?\n/g, file: 'supabase-auth.middleware.ts' },
    { pattern: /import\s*{\s*AxiosError\s*}\s*from\s*['"][^'"]+['"];?\n/g, file: 'qa-auth-flow.test.ts' },
  ];
  
  return content;
}

// Process all TypeScript files
function processFile(filePath: string) {
  if (!filePath.endsWith('.ts') && !filePath.endsWith('.tsx')) {
    return;
  }
  
  try {
    let content = readFileSync(filePath, 'utf8');
    const originalContent = content;
    
    // Apply fixes
    content = fixAnyTypes(content);
    content = fixUnusedParams(content);
    content = removeUnusedImports(content);
    
    // Only write if content changed
    if (content !== originalContent) {
      writeFileSync(filePath, content);
      console.log(`✅ Fixed: ${filePath}`);
    }
  } catch (error) {
    console.error(`❌ Error processing ${filePath}:`, error);
  }
}

// Recursively process directory
function processDirectory(dir: string) {
  const items = readdirSync(dir);
  
  for (const item of items) {
    const fullPath = join(dir, item);
    const stat = statSync(fullPath);
    
    if (stat.isDirectory() && item !== 'node_modules' && item !== '.git') {
      processDirectory(fullPath);
    } else if (stat.isFile()) {
      processFile(fullPath);
    }
  }
}

console.log('🔧 Fixing linting issues...\n');
processDirectory(SRC_DIR);
console.log('\n✨ Done! Run "pnpm lint" to check remaining issues.');