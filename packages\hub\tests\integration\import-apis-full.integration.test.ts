import { describe, it, expect, beforeAll, afterAll, beforeEach, vi, afterEach } from 'vitest';
import request from 'supertest';
import { createServer } from '../../src/server';
import { getSupabaseClient } from '../../src/lib/supabase';
import { RedisConnectionPoolService } from '../../src/services/redis-connection-pool.service';
import { AIParserService } from '../../src/services/ai-parser.service';

// Skip these tests in CI - they require real API keys and authentication
const isCI = process.env.CI === 'true';
const describeIntegration = isCI ? describe.skip : describe;

// Test configuration
const TEST_USER_EMAIL = '<EMAIL>';
const TEST_USER_PASSWORD = 'Flaremmk123!';
const API_TIMEOUT = 30000; // 30 seconds for AI API calls

// Mock Redis
vi.mock('../../services/redis-connection-pool.service', () => ({
  RedisConnectionPoolService: {
    getInstance: vi.fn(() => ({
      getClient: vi.fn().mockResolvedValue({
        get: vi.fn(),
        set: vi.fn(),
        del: vi.fn(),
        expire: vi.fn()
      })
    }))
  },
  redisConnectionPool: {
    execute: vi.fn((fn) => fn({
      get: vi.fn().mockResolvedValue(null),
      set: vi.fn().mockResolvedValue('OK'),
      del: vi.fn(),
      expire: vi.fn()
    }))
  }
}));

// Mock the redis service
vi.mock('../../services/redis.service', () => ({
  redis: {
    publish: vi.fn().mockResolvedValue(1),
    subscribe: vi.fn().mockResolvedValue(undefined),
    unsubscribe: vi.fn().mockResolvedValue(undefined),
    on: vi.fn(),
    get: vi.fn().mockResolvedValue(null),
    set: vi.fn().mockResolvedValue('OK')
  }
}));

// Mock cache service
vi.mock('../../services/cache.service', () => ({
  cacheService: {
    get: vi.fn().mockResolvedValue(null),
    set: vi.fn().mockResolvedValue(true),
    del: vi.fn().mockResolvedValue(true),
    clear: vi.fn().mockResolvedValue(true)
  }
}));

// Helper to get auth token
async function getAuthToken(): Promise<string> {
  const supabase = getSupabaseClient();
  const { data, error } = await supabase.auth.signInWithPassword({
    email: TEST_USER_EMAIL,
    password: TEST_USER_PASSWORD
  });

  if (error) throw new Error(`Auth failed: ${error.message}`);
  return data.session?.access_token || '';
}

describeIntegration('Import APIs Full Integration Tests', () => {
  let authToken: string;
  let server: any;
  let app: any;

  beforeAll(async () => {
    // Get auth token first
    authToken = await getAuthToken();
    
    // Create and start server
    app = createServer();
    server = app.listen(0); // Random port
  }, API_TIMEOUT);

  afterAll(async () => {
    server?.close();
  });

  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('Conversation Import API', () => {
    const testConversations = {
      chatgpt: {
        content: 'User: Plan a 3-day trip to Tokyo.\nAssistant: Here is your comprehensive Tokyo itinerary for 3 days:\n\nDay 1: Traditional Tokyo\n- Morning: Visit Senso-ji Temple in Asakusa\n- Afternoon: Explore Tokyo National Museum\n- Evening: Dinner in Shibuya\n\nDay 2: Modern Tokyo\n- Morning: TeamLab Borderless\n- Afternoon: Shopping in Harajuku\n- Evening: Tokyo Tower\n\nDay 3: Mount Fuji Day Trip\n- Early morning: Travel to Mount Fuji\n- Afternoon: Lake Kawaguchi\n- Evening: Return to Tokyo',
        source: 'chatgpt' as const
      },
      claude: {
        content: 'Human: I need a weekend in Barcelona.\nAssistant: Perfect! Here is your Barcelona weekend plan:\n\nSaturday:\n- Morning: Start at La Sagrada Familia (book tickets in advance)\n- Lunch: Tapas in the Gothic Quarter\n- Afternoon: Stroll down Las Ramblas and visit Boqueria Market\n- Evening: Watch sunset from Park Güell\n\nSunday:\n- Morning: Explore Casa Batlló and Casa Milà\n- Afternoon: Beach time at Barceloneta\n- Evening: Dinner in El Born neighborhood\n\nDont forget to try paella and sangria!',
        source: 'claude' as const
      },
      gemini: {
        content: 'User: Create a Paris itinerary.\nGemini: I will create a wonderful Paris itinerary for you:\n\nDay 1: Classic Paris\n- Morning: Eiffel Tower (arrive early to avoid crowds)\n- Afternoon: Louvre Museum\n- Evening: Seine River cruise\n\nDay 2: Art and Culture\n- Morning: Musée d\'Orsay\n- Afternoon: Montmartre and Sacré-Cœur\n- Evening: Moulin Rouge show\n\nDay 3: Versailles\n- Full day trip to Palace of Versailles\n- Evening: Dinner in the Latin Quarter',
        source: 'gemini' as const
      }
    };

    it('should successfully import ChatGPT conversation', async () => {
      const response = await request(app)
        .post('/api/v1/import/parse-simple')
        .set('Authorization', `Bearer ${authToken}`)
        .send(testConversations.chatgpt);

      // Log the response if it fails
      if (response.status !== 200) {
        console.log('Response status:', response.status);
        console.log('Response body:', response.body);
      }

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveProperty('importId');
      expect(response.body.data).toHaveProperty('message');
    }, API_TIMEOUT);

    it('should successfully import Claude conversation', async () => {
      const response = await request(app)
        .post('/api/v1/import/parse-simple')
        .set('Authorization', `Bearer ${authToken}`)
        .send(testConversations.claude)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveProperty('importId');
      expect(response.body.data).toHaveProperty('message');
    }, API_TIMEOUT);

    it('should successfully import Gemini conversation', async () => {
      const response = await request(app)
        .post('/api/v1/import/parse-simple')
        .set('Authorization', `Bearer ${authToken}`)
        .send(testConversations.gemini)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveProperty('importId');
      expect(response.body.data).toHaveProperty('message');
    }, API_TIMEOUT);

    it('should validate required fields', async () => {
      const response = await request(app)
        .post('/api/v1/import/parse-simple')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          // Missing required fields
        })
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.error).toBeDefined();
    });

    it('should reject unauthorized requests', async () => {
      const response = await request(app)
        .post('/api/v1/import/parse-simple')
        .send(testConversations.chatgpt)
        .expect(401);

      expect(response.body.success).toBe(false);
    });
  });

  describe('PDF Import API', () => {
    it('should handle PDF upload', async () => {
      // Create a more complete PDF with text content
      const pdfContent = Buffer.from(`%PDF-1.4
1 0 obj
<< /Type /Catalog /Pages 2 0 R >>
endobj
2 0 obj
<< /Type /Pages /Kids [3 0 R] /Count 1 >>
endobj
3 0 obj
<< /Type /Page /Parent 2 0 R /Resources << /Font << /F1 << /Type /Font /Subtype /Type1 /BaseFont /Helvetica >> >> >> /MediaBox [0 0 612 792] /Contents 4 0 R >>
endobj
4 0 obj
<< /Length 200 >>
stream
BT
/F1 12 Tf
100 700 Td
(User: Plan a trip to Japan) Tj
0 -20 Td
(Assistant: Here is your comprehensive Japan itinerary) Tj
0 -20 Td
(Day 1: Tokyo - Visit temples and explore the city) Tj
ET
endstream
endobj
xref
0 5
0000000000 65535 f 
0000000009 00000 n 
0000000058 00000 n 
0000000115 00000 n 
0000000274 00000 n 
trailer
<< /Size 5 /Root 1 0 R >>
startxref
492
%%EOF`);
      
      const response = await request(app)
        .post('/api/v1/import/pdf')
        .set('Authorization', `Bearer ${authToken}`)
        .set('Content-Type', 'multipart/form-data')
        .attach('file', pdfContent, {
          filename: 'test-itinerary.pdf',
          contentType: 'application/pdf'
        });

      if (response.status !== 200) {
        console.log('PDF upload response:', response.status, response.body);
      }

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveProperty('sessionId');
    }, API_TIMEOUT);

    it('should reject non-PDF files', async () => {
      const textContent = Buffer.from('This is not a PDF');
      
      const response = await request(app)
        .post('/api/v1/import/pdf')
        .set('Authorization', `Bearer ${authToken}`)
        .attach('file', textContent, 'test.txt')
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.error).toContain('PDF');
    });
  });

  describe('Import Status API', () => {
    it('should retrieve import session status', async () => {
      // First create an import session
      const importResponse = await request(app)
        .post('/api/v1/import/parse-simple')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          content: 'User: Plan a trip.\nAssistant: Here is your comprehensive trip itinerary with detailed activities and locations for each day of your adventure...',
          source: 'chatgpt'
        });

      const importId = importResponse.body.data?.importId;
      
      if (importId) {
        const statusResponse = await request(app)
          .get(`/api/v1/import/parse-simple/${importId}`)
          .set('Authorization', `Bearer ${authToken}`)
          .expect(200);

        expect(statusResponse.body.success).toBe(true);
        expect(statusResponse.body.data).toHaveProperty('status');
      }
    }, API_TIMEOUT);
  });
});