/**
 * File parser utilities for extracting text from various file formats
 */

// Dynamic imports cache to avoid repeated imports
let pdfjsLib: typeof import('pdfjs-dist') | null = null;
let mammoth: typeof import('mammoth') | null = null;

async function loadPDFLib() {
  if (!pdfjsLib && typeof window !== 'undefined') {
    pdfjsLib = await import('pdfjs-dist');
    // Configure PDF.js worker
    pdfjsLib.GlobalWorkerOptions.workerSrc = `//cdnjs.cloudflare.com/ajax/libs/pdf.js/${pdfjsLib.version}/pdf.worker.min.js`;
  }
  return pdfjsLib;
}

async function loadMammoth() {
  if (!mammoth && typeof window !== 'undefined') {
    mammoth = await import('mammoth');
  }
  return mammoth;
}

/**
 * Extract text content from a file
 * @param file - The file to extract text from
 * @returns Promise<string> - The extracted text content
 */
export async function extractTextFromFile(file: File): Promise<string> {
  const fileType = file.type;
  const fileName = file.name.toLowerCase();

  try {
    // Handle text files
    if (fileType === 'text/plain' || fileName.endsWith('.txt')) {
      return await file.text();
    }

    // Handle PDF files
    if (fileType === 'application/pdf' || fileName.endsWith('.pdf')) {
      return await extractTextFromPDF(file);
    }

    // Handle Word documents
    if (
      fileType === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' ||
      fileName.endsWith('.docx')
    ) {
      return await extractTextFromDOCX(file);
    }

    throw new Error(`Unsupported file type: ${fileType || 'unknown'}`);
  } catch (error) {
    // Error extracting text from file
    throw new Error(`Failed to extract text from ${file.name}: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

/**
 * Extract text from PDF using PDF.js
 * @param file - The PDF file
 * @returns Promise<string> - The extracted text
 */
async function extractTextFromPDF(file: File): Promise<string> {
  const pdfjsLib = await loadPDFLib();
  if (!pdfjsLib) {
    throw new Error('PDF.js not available - client-side only');
  }
  
  let pdf: import('pdfjs-dist').PDFDocumentProxy | null = null;
  
  try {
    // Convert file to ArrayBuffer
    const arrayBuffer = await file.arrayBuffer();
    
    // Load the PDF document
    pdf = await pdfjsLib.getDocument({ data: arrayBuffer }).promise;
    
    let fullText = '';
    const numPages = pdf.numPages;
    
    // Extract text from each page
    for (let pageNum = 1; pageNum <= numPages; pageNum++) {
      const page = await pdf.getPage(pageNum);
      try {
        const textContent = await page.getTextContent();
        
        // Combine text items with proper spacing
        const pageText = (textContent.items as Array<{ str: string }>)
          .map((item) => item.str)
          .join(' ')
          .replace(/\s+/g, ' ')
          .trim();
        
        if (pageText) {
          fullText += pageText + '\n\n';
        }
      } finally {
        // Clean up page resources
        if (page.cleanup) {
          page.cleanup();
        }
      }
    }
    
    if (!fullText.trim()) {
      throw new Error('No text content found in PDF');
    }
    
    return fullText.trim();
  } catch (error) {
    // Error extracting text from PDF
    throw new Error(`Failed to extract text from PDF: ${error instanceof Error ? error.message : 'Unknown error'}`);
  } finally {
    // Clean up PDF document resources
    if (pdf && pdf.destroy) {
      pdf.destroy();
    }
  }
}

/**
 * Extract text from DOCX files
 * @param file - The DOCX file
 * @returns Promise<string> - The extracted text
 */
async function extractTextFromDOCX(file: File): Promise<string> {
  const mammoth = await loadMammoth();
  if (!mammoth) {
    throw new Error('Mammoth not available - client-side only');
  }
  
  try {
    // Convert file to ArrayBuffer
    const arrayBuffer = await file.arrayBuffer();
    
    // Extract text using mammoth
    const result = await mammoth.extractRawText({ arrayBuffer });
    
    if (!result.value || !result.value.trim()) {
      throw new Error('No text content found in DOCX file');
    }
    
    // Clean up the text by normalizing whitespace
    const cleanText = result.value
      .replace(/\r\n/g, '\n')
      .replace(/\n{3,}/g, '\n\n')
      .trim();
    
    return cleanText;
  } catch (error) {
    // Error extracting text from DOCX
    throw new Error(`Failed to extract text from DOCX: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}


/**
 * Combine text from multiple files
 * @param files - Array of files to process
 * @returns Promise<string> - Combined text content
 */
export async function extractTextFromFiles(files: Array<{ file: File }>): Promise<string> {
  const textPromises = files.map(({ file }) => extractTextFromFile(file));
  const textArray = await Promise.all(textPromises);
  
  // Combine all text with separators
  return textArray.join('\n\n--- Next Document ---\n\n');
}