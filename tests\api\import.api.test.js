/**
 * Import API Tests
 * 
 * Tests AI import functionality with real content parsing
 * Uses the proven authentication pattern for token management
 */

const ApiTestClient = require('./utils/api-client');
const apiConfig = require('./api.config');

// Test results tracking
const testResults = {
  passed: 0,
  failed: 0,
  tests: []
};

function logTest(name, result, details = {}) {
  const passed = result.success || result === true;
  testResults.tests.push({ name, passed, details });
  
  if (passed) {
    testResults.passed++;
    console.log(`✅ ${name}`);
  } else {
    testResults.failed++;
    console.log(`❌ ${name}`, details.error || result.error || '');
  }
}

async function ensureAuthenticated(client) {
  console.log('\n🔐 Ensuring Authentication...');
  
  try {
    let token = client.getStoredToken();
    
    if (!token) {
      console.log('   No valid token found, logging in...');
      const loginResponse = await client.login(
        apiConfig.testData.user.email,
        apiConfig.testData.user.password
      );
      
      if (!loginResponse.success) {
        throw new Error(`Login failed: ${loginResponse.error || 'Unknown error'}`);
      }
      
      token = client.getStoredToken();
    }
    
    const meResponse = await client.getMe();
    if (!meResponse.success) {
      throw new Error(`Token validation failed: ${meResponse.error || 'Unknown error'}`);
    }
    
    logTest('Authentication ready', true, { 
      hasToken: !!token,
      userEmail: meResponse.data.data.email 
    });
    
    return true;
  } catch (error) {
    logTest('Authentication setup', false, { error: error.message });
    return false;
  }
}

async function testParseSimple(client) {
  console.log('\n📝 Testing Simple Parse...');
  
  try {
    const response = await client.parseSimple(
      apiConfig.testData.importContent,
      'manual-test'
    );
    
    logTest('Parse simple content', response, response);
    
    if (response.success) {
      const data = response.data.data;
      
      // Verify response structure
      logTest('Parse response has data', !!data, { hasData: !!data });
      
      if (data) {
        logTest('Parse response has session ID', !!data.sessionId || !!data.importId, {
          hasSessionId: !!data.sessionId,
          hasImportId: !!data.importId
        });
      }
      
      return true;
    }
    
    return false;
  } catch (error) {
    logTest('Parse Simple Test', false, { error: error.message });
    return false;
  }
}

async function testParseWithRealContent(client) {
  console.log('\n🌍 Testing Parse with Real Travel Content...');
  
  const realTravelContent = `
User: I'm planning a 5-day trip to Tokyo in April 2024. Can you help me create an itinerary?Assi
stant: I'd be happy to help you plan a 5-day Tokyo itinerary for April 2024! Here's a comprehensive plan:

Day 1 (April 15): Arrival & Shibuya
- Morning: Arrive at Narita/Haneda Airport
- Afternoon: Check into hotel in Shibuya area
- Evening: Explore Shibuya Crossing, dinner at Ichiran Ramen ($12)
- Stay: Hotel Gracery Shinjuku ($150/night)

Day 2 (April 16): Traditional Tokyo
- 9:00 AM: Visit Senso-ji Temple in Asakusa
- 11:00 AM: Explore Nakamise Shopping Street
- 2:00 PM: Tokyo Skytree ($20 entrance)
- Evening: Ginza district for dinner ($40)

Day 3 (April 17): Modern Culture
- Morning: TeamLab Borderless Digital Museum ($30)
- Afternoon: Harajuku and Takeshita Street
- Evening: Robot Restaurant show ($80)

Day 4 (April 18): Day Trip to Mt. Fuji
- 7:00 AM: Take train to Kawaguchiko ($30)
- Full day exploring Mt. Fuji area
- Return by 8:00 PM

Day 5 (April 19): Last Day
- Morning: Tsukiji Outer Market for breakfast
- Afternoon: Shopping in Shinjuku
- Evening: Depart for airport

Total Budget: ~$1,200 including hotel
  `;
  
  try {
    const response = await client.parseSimple(realTravelContent, 'chatgpt');
    
    logTest('Parse real travel content', response, response);
    
    if (response.success) {
      const data = response.data.data;
      
      // Verify the content was parsed meaningfully
      logTest('Parse extracted trip data', !!data, { hasData: !!data });
      
      if (data && data.trip) {
        const trip = data.trip;
        logTest('Trip has title', !!trip.title, { title: trip.title });
        logTest('Trip has destination', !!trip.destination, { destination: trip.destination });
        logTest('Trip has activities', trip.activities && trip.activities.length > 0, { 
          activityCount: trip.activities ? trip.activities.length : 0 
        });
      }
      
      return true;
    }
    
    return false;
  } catch (error) {
    logTest('Parse Real Content Test', false, { error: error.message });
    return false;
  }
}

async function testParseWithInvalidContent(client) {
  console.log('\n🚫 Testing Parse with Invalid Content...');
  
  try {
    const response = await client.parseSimple('This is not travel content', 'manual-test');
    
    // Should either succeed with empty results or fail gracefully
    logTest('Parse invalid content handled gracefully', true, response);
    
    if (response.success) {
      const data = response.data.data;
      logTest('Invalid content returns empty or minimal data', true, { 
        hasData: !!data,
        dataType: typeof data
      });
    } else {
      logTest('Invalid content fails with proper error', response.status >= 400, {
        status: response.status,
        error: response.error
      });
    }
    
    return true;
  } catch (error) {
    logTest('Parse Invalid Content Test', false, { error: error.message });
    return false;
  }
}

async function testParseWithEmptyContent(client) {
  console.log('\n📭 Testing Parse with Empty Content...');
  
  try {
    const response = await client.parseSimple('', 'manual-test');
    
    // Should fail with proper validation error
    logTest('Empty content validation', !response.success, response);
    logTest('Empty content returns 400 status', response.status === 400, {
      expectedStatus: 400,
      actualStatus: response.status
    });
    
    return true;
  } catch (error) {
    logTest('Parse Empty Content Test', false, { error: error.message });
    return false;
  }
}

async function testParseWithDifferentSources(client) {
  console.log('\n🤖 Testing Parse with Different AI Sources...');
  
  const sources = ['chatgpt', 'claude', 'gemini', 'manual'];
  let allPassed = true;
  
  for (const source of sources) {
    try {
      const response = await client.parseSimple(
        `Simple travel plan: Visit Paris for 3 days. Day 1: Eiffel Tower. Day 2: Louvre. Day 3: Versailles.`,
        source
      );
      
      logTest(`Parse with source: ${source}`, response, response);
      
      if (!response.success) {
        allPassed = false;
      }
    } catch (error) {
      logTest(`Parse with source: ${source}`, false, { error: error.message });
      allPassed = false;
    }
  }
  
  return allPassed;
}

async function testParseStartEndpoint(client) {
  console.log('\n🚀 Testing Parse Start Endpoint...');
  
  try {
    const response = await client.startParse(
      'Sample travel conversation about visiting Rome for 4 days',
      'chatgpt'
    );
    
    logTest('Start parse endpoint', response, response);
    
    if (response.success) {
      const data = response.data.data;
      logTest('Parse start returns session info', !!data, { hasData: !!data });
      
      if (data) {
        logTest('Parse start has session ID', !!data.sessionId, { 
          sessionId: data.sessionId 
        });
      }
      
      return true;
    }
    
    return false;
  } catch (error) {
    logTest('Parse Start Test', false, { error: error.message });
    return false;
  }
}

async function runTests() {
  console.log('🚀 Starting Import API Tests');
  console.log('============================');
  console.log(`API Base URL: ${apiConfig.baseUrl}`);
  console.log(`Test User: ${apiConfig.testData.user.email}\n`);

  const client = new ApiTestClient();
  
  try {
    // Ensure we're authenticated first
    const authReady = await ensureAuthenticated(client);
    if (!authReady) {
      console.log('\n❌ Authentication failed - cannot run import API tests');
      return testResults;
    }
    
    // Run all import API tests in sequence
    const tests = [
      () => testParseSimple(client),
      () => testParseWithRealContent(client),
      () => testParseWithInvalidContent(client),
      () => testParseWithEmptyContent(client),
      () => testParseWithDifferentSources(client),
      () => testParseStartEndpoint(client),
    ];

    for (const test of tests) {
      await test();
    }
    
  } catch (error) {
    console.error('\n💥 Test execution error:', error.message);
    testResults.failed++;
  }

  // Summary
  console.log('\n============================');
  console.log('📊 Import API Test Results');
  console.log('============================');
  console.log(`✅ Passed: ${testResults.passed}`);
  console.log(`❌ Failed: ${testResults.failed}`);
  console.log(`📋 Total: ${testResults.tests.length}`);
  console.log(`📈 Success Rate: ${Math.round((testResults.passed / testResults.tests.length) * 100)}%`);

  // Detailed failures
  if (testResults.failed > 0) {
    console.log('\n❌ Failed Tests:');
    testResults.tests
      .filter(t => !t.passed)
      .forEach(t => {
        console.log(`\n- ${t.name}`);
        if (t.details.error) {
          console.log('  Error:', t.details.error);
        }
      });
  }

  return testResults;
}

// Export for use by test runner
module.exports = { runTests };

// Run directly if called as script
if (require.main === module) {
  runTests().catch(console.error);
}