import { describe, it, expect, beforeAll, afterAll } from 'vitest';
import request from 'supertest';
import { createServer } from '../../src/server';
import { getSupabaseClient } from '../../src/lib/supabase';

// Test configuration
const TEST_USER_EMAIL = '<EMAIL>';
const TEST_USER_PASSWORD = 'TestPassword123!';
const API_TIMEOUT = 60000;
const POLL_INTERVAL = 1000;
const MAX_POLL_ATTEMPTS = 30;

// Helper to get auth token
async function getAuthToken(): Promise<string> {
  const supabase = getSupabaseClient();
  const { data, error } = await supabase.auth.signInWithPassword({
    email: TEST_USER_EMAIL,
    password: TEST_USER_PASSWORD
  });

  if (error) throw new Error(`Auth failed: ${error.message}`);
  return data.session?.access_token || '';
}

// Helper to poll for parse completion with better error handling
async function waitForParseCompletion(
  server: any,
  importId: string,
  authToken: string,
  maxAttempts: number = MAX_POLL_ATTEMPTS
): Promise<any> {
  for (let attempt = 0; attempt < maxAttempts; attempt++) {
    const response = await request(server)
      .get(`/api/v1/import/parse-simple/${importId}`)
      .set('Authorization', `Bearer ${authToken}`);

    if (response.status !== 200) {
      throw new Error(`Failed to get parse status: ${response.body.error}`);
    }

    const { status, result, error } = response.body.data;

    if (status === 'complete') {
      return result;
    } else if (status === 'error') {
      throw new Error(`Parse failed: ${error || 'Unknown error'}`);
    }

    // Wait before next attempt
    await new Promise(resolve => setTimeout(resolve, POLL_INTERVAL));
  }

  throw new Error(`Parse timeout after ${maxAttempts} attempts`);
}

describe('AI Import API Enhanced Tests', () => {
  let authToken: string;
  let server: any;
  let app: any;

  beforeAll(async () => {
    authToken = await getAuthToken();
    app = createServer();
    server = app.listen(0);
  }, API_TIMEOUT);

  afterAll(async () => {
    server?.close();
  });

  describe('Authentication Tests', () => {
    it('should require valid authentication token', async () => {
      const response = await request(server)
        .post('/api/v1/import/parse-simple')
        .send({ content: 'test', source: 'test' })
        .expect(401);

      expect(response.body).toEqual({
        success: false,
        error: expect.stringContaining('auth')
      });
    });

    it('should reject invalid authentication token', async () => {
      const response = await request(server)
        .post('/api/v1/import/parse-simple')
        .set('Authorization', 'Bearer invalid-token')
        .send({ content: 'test content', source: 'test' })
        .expect(401);

      expect(response.body.success).toBe(false);
    });
  });

  describe('Input Validation Tests', () => {
    it('should reject content that is too short', async () => {
      const response = await request(server)
        .post('/api/v1/import/parse-simple')
        .set('Authorization', `Bearer ${authToken}`)
        .send({ content: 'too short', source: 'test' })
        .expect(400);

      expect(response.body).toMatchObject({
        success: false,
        error: expect.stringContaining('too short')
      });
    });

    it('should reject missing content', async () => {
      const response = await request(server)
        .post('/api/v1/import/parse-simple')
        .set('Authorization', `Bearer ${authToken}`)
        .send({ source: 'test' })
        .expect(400);

      expect(response.body.success).toBe(false);
    });

    it('should reject content exceeding maximum length', async () => {
      const longContent = 'a'.repeat(51 * 1024); // Over 50KB limit
      
      const response = await request(server)
        .post('/api/v1/import/parse-simple')
        .set('Authorization', `Bearer ${authToken}`)
        .send({ content: longContent, source: 'test' })
        .expect(413);

      expect(response.body.success).toBe(false);
    });
  });

  describe('Simple Conversation Parsing', () => {
    it('should parse a well-structured Paris itinerary', async () => {
      const parisConversation = {
        content: `User: I want to visit Paris for 3 days from March 15-17, 2024.
        
AI: Here's your 3-day Paris itinerary for March 15-17, 2024:

Day 1 - March 15:
- 9:00 AM: Visit the Eiffel Tower (€26 for summit access)
- 1:00 PM: Lunch at Café de l'Homme (€30-40)
- 3:00 PM: Explore the Louvre Museum (€17 entry)
- 7:00 PM: Seine River dinner cruise (€99)

Day 2 - March 16:
- 10:00 AM: Notre-Dame Cathedral (free, exterior only)
- 12:00 PM: Walk through Latin Quarter
- 2:00 PM: Visit Musée d'Orsay (€16)
- 6:00 PM: Montmartre and Sacré-Cœur (free)
- 8:00 PM: Dinner at La Maison Rose (€40-50)

Day 3 - March 17:
- 9:00 AM: Day trip to Versailles (€20 palace ticket)
- 12:00 PM: Lunch at La Petite Venise in Versailles (€25-35)
- 4:00 PM: Return to Paris
- 6:00 PM: Arc de Triomphe (€13)
- 8:00 PM: Farewell dinner at Le Jules Verne (€190)`,
        source: 'chatgpt'
      };

      // Start parsing
      const parseResponse = await request(server)
        .post('/api/v1/import/parse-simple')
        .set('Authorization', `Bearer ${authToken}`)
        .send(parisConversation)
        .expect(200);

      expect(parseResponse.body).toMatchObject({
        success: true,
        data: {
          importId: expect.any(String),
          message: 'Parsing started'
        }
      });

      const importId = parseResponse.body.data.importId;

      // Wait for completion
      const result = await waitForParseCompletion(server, importId, authToken);

      // Validate parsed result structure
      expect(result).toMatchObject({
        title: expect.stringContaining('Paris'),
        startDate: '2024-03-15',
        endDate: '2024-03-17',
        destination: 'Paris',
        activities: expect.arrayContaining([
          expect.objectContaining({
            title: expect.stringContaining('Eiffel Tower'),
            type: expect.stringMatching(/activity|other/),
            startTime: expect.stringContaining('2024-03-15'),
            price: 26,
            currency: 'EUR',
            dayNumber: 1
          }),
          expect.objectContaining({
            title: expect.stringContaining('Louvre'),
            type: expect.stringMatching(/activity|other/),
            price: 17,
            currency: 'EUR',
            dayNumber: 1
          }),
          expect.objectContaining({
            title: expect.stringContaining('Versailles'),
            dayNumber: 3
          })
        ]),
        metadata: {
          source: 'chatgpt',
          confidence: expect.any(Number),
          parseDate: expect.any(String),
          version: expect.any(String)
        }
      });

      // Validate activity count and structure
      expect(result.activities.length).toBeGreaterThanOrEqual(10);
      expect(result.activities.length).toBeLessThanOrEqual(15);

      // Validate all activities have required fields
      result.activities.forEach((activity: any) => {
        expect(activity).toMatchObject({
          title: expect.any(String),
          type: expect.stringMatching(/^(flight|accommodation|activity|transport|dining|shopping|other)$/),
          startTime: expect.any(String),
          dayNumber: expect.any(Number),
          position: expect.any(Number)
        });

        // Validate day numbers are within trip range
        expect(activity.dayNumber).toBeGreaterThanOrEqual(1);
        expect(activity.dayNumber).toBeLessThanOrEqual(3);
      });

      // Check metadata confidence
      expect(result.metadata.confidence).toBeGreaterThan(0.6);
    }, API_TIMEOUT);

    it('should handle conversations with ambiguous dates', async () => {
      const ambiguousConversation = {
        content: `User: I'm planning a trip to Rome next month for a long weekend.

AI: Here's a suggested Rome itinerary for a long weekend:

Friday:
- Arrive and check into hotel
- Evening walk to Trevi Fountain
- Dinner in Trastevere

Saturday:
- Morning: Colosseum and Roman Forum
- Afternoon: Vatican Museums and Sistine Chapel
- Evening: Spanish Steps area

Sunday:
- Morning: Borghese Gallery
- Afternoon: Pantheon and Piazza Navona
- Evening: Departure`,
        source: 'claude'
      };

      const parseResponse = await request(server)
        .post('/api/v1/import/parse-simple')
        .set('Authorization', `Bearer ${authToken}`)
        .send(ambiguousConversation)
        .expect(200);

      const importId = parseResponse.body.data.importId;
      const result = await waitForParseCompletion(server, importId, authToken);

      // Should still parse but with lower confidence
      expect(result).toMatchObject({
        title: expect.stringContaining('Rome'),
        destination: 'Rome',
        activities: expect.any(Array)
      });

      // Check for warnings about ambiguous dates
      expect(result.metadata.warnings).toEqual(
        expect.arrayContaining([
          expect.stringContaining('date')
        ])
      );
    }, API_TIMEOUT);

    it('should handle conversations with missing structure', async () => {
      const unstructuredConversation = {
        content: `User: What should I do in Tokyo?

AI: Tokyo is amazing! You should definitely visit the Senso-ji temple, it's beautiful. 
The Robot Restaurant show is wild. Don't miss the early morning tuna auction at Tsukiji. 
Ramen in Shinjuku is a must. Take a day trip to Mount Fuji if weather permits.`,
        source: 'gemini'
      };

      const parseResponse = await request(server)
        .post('/api/v1/import/parse-simple')
        .set('Authorization', `Bearer ${authToken}`)
        .send(unstructuredConversation)
        .expect(200);

      const importId = parseResponse.body.data.importId;
      const result = await waitForParseCompletion(server, importId, authToken);

      // Should extract some activities even without clear structure
      expect(result.activities.length).toBeGreaterThan(0);
      expect(result.metadata.confidence).toBeLessThan(0.6);
      expect(result.metadata.warnings).toBeDefined();
    }, API_TIMEOUT);
  });

  describe('Complex Conversation Parsing', () => {
    it('should parse multi-destination trips', async () => {
      const multiCityConversation = {
        content: `User: I'm doing a 10-day Europe trip visiting Paris, Rome, and Barcelona from May 1-10, 2024.

AI: Here's your 10-day multi-city Europe itinerary:

**Paris (May 1-3)**
Day 1: Arrive in Paris, Eiffel Tower evening visit
Day 2: Louvre, Seine cruise
Day 3: Versailles day trip

**Rome (May 4-6)**  
Day 4: Fly to Rome (€80), Colosseum afternoon
Day 5: Vatican City full day
Day 6: Roman Forum, Trastevere

**Barcelona (May 7-9)**
Day 7: Fly to Barcelona (€75), Las Ramblas
Day 8: Sagrada Familia, Park Güell
Day 9: Beach day, Gothic Quarter

Day 10 (May 10): Fly home from Barcelona`,
        source: 'chatgpt'
      };

      const parseResponse = await request(server)
        .post('/api/v1/import/parse-simple')
        .set('Authorization', `Bearer ${authToken}`)
        .send(multiCityConversation)
        .expect(200);

      const result = await waitForParseCompletion(server, parseResponse.body.data.importId, authToken);

      // Should identify multiple destinations
      expect(result.title).toMatch(/Europe|Paris.*Rome.*Barcelona/i);
      expect(result.activities.some((a: any) => a.type === 'flight')).toBe(true);
      expect(result.activities.filter((a: any) => a.type === 'flight').length).toBeGreaterThanOrEqual(2);
    }, API_TIMEOUT);
  });

  describe('Trip Creation from Parse', () => {
    it('should create a trip from completed parse session', async () => {
      // First, parse a simple itinerary
      const parseResponse = await request(server)
        .post('/api/v1/import/parse-simple')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          content: `User: 2-day London trip.
          
Day 1: Tower of London, British Museum
Day 2: Westminster Abbey, Buckingham Palace`,
          source: 'test'
        })
        .expect(200);

      const importId = parseResponse.body.data.importId;
      await waitForParseCompletion(server, importId, authToken);

      // Create trip with edits
      const createResponse = await request(server)
        .post(`/api/v1/import/parse-simple/${importId}/create-trip`)
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          edits: {
            title: 'My London Adventure',
            description: 'A quick trip to explore London highlights'
          }
        })
        .expect(200);

      expect(createResponse.body).toMatchObject({
        success: true,
        data: {
          tripId: expect.any(String),
          message: 'Trip created successfully'
        }
      });

      // Verify trip was created
      const tripId = createResponse.body.data.tripId;
      const tripResponse = await request(server)
        .get(`/api/v1/trips/${tripId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(tripResponse.body.data).toMatchObject({
        id: tripId,
        title: 'My London Adventure',
        description: 'A quick trip to explore London highlights',
        activities: expect.any(Array)
      });
    }, API_TIMEOUT);

    it('should prevent duplicate trip creation from same import', async () => {
      const parseResponse = await request(server)
        .post('/api/v1/import/parse-simple')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          content: `Day 1: Test activity`,
          source: 'test'
        })
        .expect(200);

      const importId = parseResponse.body.data.importId;
      await waitForParseCompletion(server, importId, authToken);

      // First creation should succeed
      await request(server)
        .post(`/api/v1/import/parse-simple/${importId}/create-trip`)
        .set('Authorization', `Bearer ${authToken}`)
        .send({ edits: {} })
        .expect(200);

      // Second creation should fail
      const duplicateResponse = await request(server)
        .post(`/api/v1/import/parse-simple/${importId}/create-trip`)
        .set('Authorization', `Bearer ${authToken}`)
        .send({ edits: {} })
        .expect(400);

      expect(duplicateResponse.body.error).toContain('already created');
    }, API_TIMEOUT);
  });

  describe('Performance and Error Handling', () => {
    it('should handle concurrent parse requests', async () => {
      const conversations = [
        { content: 'User: 1-day Paris trip\n\nDay 1: Eiffel Tower, Louvre', source: 'test1' },
        { content: 'User: 1-day Rome trip\n\nDay 1: Colosseum, Vatican', source: 'test2' },
        { content: 'User: 1-day London trip\n\nDay 1: Big Ben, Tower Bridge', source: 'test3' }
      ];

      const responses = await Promise.all(
        conversations.map(conv =>
          request(server)
            .post('/api/v1/import/parse-simple')
            .set('Authorization', `Bearer ${authToken}`)
            .send(conv)
        )
      );

      responses.forEach(response => {
        expect(response.status).toBe(200);
        expect(response.body.success).toBe(true);
        expect(response.body.data.importId).toBeDefined();
      });
    });

    it('should handle invalid import IDs gracefully', async () => {
      const response = await request(server)
        .get('/api/v1/import/parse-simple/invalid-id-12345')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(404);

      expect(response.body).toMatchObject({
        success: false,
        error: expect.stringContaining('not found')
      });
    });
  });
});