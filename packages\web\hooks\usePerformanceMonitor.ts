import { useEffect, useCallback, useRef } from 'react';
import { useAnalytics } from './useAnalytics';

interface PerformanceEntry {
  name: string;
  startTime: number;
  duration: number;
  type: string;
}

interface ResourceTiming {
  name: string;
  duration: number;
  transferSize: number;
  decodedBodySize: number;
  initiatorType: string;
}

/**
 * Optimized performance monitoring hook - focuses on Core Web Vitals only
 * Reduces overhead by consolidating observers and limiting metrics
 */
export function usePerformanceMonitor() {
  const { trackPerformance, trackEvent } = useAnalytics();
  const clsValue = useRef(0);
  const reportedCLS = useRef(false);

  // Track Core Web Vitals with consolidated observers
  useEffect(() => {
    if (typeof window === 'undefined') return;

    const observers: PerformanceObserver[] = [];

    try {
      // Combined observer for paint metrics (LCP, FCP)
      const paintObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        for (const entry of entries) {
          if (entry.entryType === 'largest-contentful-paint') {
            trackPerformance('LCP', Math.round(entry.startTime));
          } else if (entry.name === 'first-contentful-paint') {
            trackPerformance('FCP', Math.round(entry.startTime));
          }
        }
      });

      // First Input Delay (FID) observer
      const fidObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        for (const entry of entries) {
          const fid = (entry as any).processingStart - entry.startTime;
          trackPerformance('FID', Math.round(fid));
        }
      });

      // Cumulative Layout Shift (CLS) observer
      const clsObserver = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          if (!(entry as any).hadRecentInput) {
            clsValue.current += (entry as any).value;
          }
        }
      });

      // Register observers
      paintObserver.observe({ entryTypes: ['largest-contentful-paint', 'paint'] });
      fidObserver.observe({ entryTypes: ['first-input'] });
      clsObserver.observe({ entryTypes: ['layout-shift'] });

      observers.push(paintObserver, fidObserver, clsObserver);

      // Optional: TTFB from navigation timing (less critical)
      if ('PerformanceNavigationTiming' in window) {
        const navObserver = new PerformanceObserver((list) => {
          const entries = list.getEntries();
          if (entries.length > 0) {
            const nav = entries[0] as PerformanceNavigationTiming;
            const ttfb = nav.responseStart - nav.requestStart;
            if (ttfb > 0) {
              trackPerformance('TTFB', Math.round(ttfb));
            }
          }
        });
        
        navObserver.observe({ entryTypes: ['navigation'] });
        observers.push(navObserver);
      }

    } catch (error) {
      console.warn('Performance Observer not supported:', error);
    }

    // Report CLS on page visibility change or before unload
    const reportCLS = () => {
      if (!reportedCLS.current && clsValue.current > 0) {
        trackPerformance('CLS', Math.round(clsValue.current * 1000));
        reportedCLS.current = true;
      }
    };

    document.addEventListener('visibilitychange', reportCLS);
    window.addEventListener('beforeunload', reportCLS);

    return () => {
      observers.forEach(observer => observer.disconnect());
      document.removeEventListener('visibilitychange', reportCLS);
      window.removeEventListener('beforeunload', reportCLS);
    };
  }, [trackPerformance]);

  // Optional: Track critical resource loading issues only
  useEffect(() => {
    if (typeof window === 'undefined') return;

    const resourceObserver = new PerformanceObserver((list) => {
      const entries = list.getEntries();
      for (const entry of entries) {
        const resource = entry as PerformanceResourceTiming;
        
        // Only track severely slow resources (>5s) to reduce noise
        if (resource.duration > 5000) {
          trackEvent('critical_slow_resource', {
            name: resource.name.split('/').pop() || 'unknown', // Only filename for privacy
            duration: Math.round(resource.duration),
            type: resource.initiatorType,
          });
        }

        // Track failed critical resources only
        if (resource.transferSize === 0 && resource.decodedBodySize === 0) {
          const fileName = resource.name.split('/').pop() || 'unknown';
          if (fileName.includes('.js') || fileName.includes('.css')) {
            trackEvent('critical_resource_error', {
              name: fileName,
              type: resource.initiatorType,
            });
          }
        }
      }
    });

    try {
      resourceObserver.observe({ entryTypes: ['resource'] });
    } catch (error) {
      console.warn('Resource observer not supported:', error);
    }

    return () => resourceObserver.disconnect();
  }, [trackEvent]);

  return {
    // Manual performance tracking methods
    markStart: useCallback((name: string) => {
      performance.mark(`${name}-start`);
    }, []),

    markEnd: useCallback((name: string) => {
      performance.mark(`${name}-end`);
      try {
        performance.measure(name, `${name}-start`, `${name}-end`);
        const entries = performance.getEntriesByName(name, 'measure');
        if (entries.length > 0) {
          const duration = entries[0].duration;
          trackPerformance(name, Math.round(duration));
          performance.clearMarks(`${name}-start`);
          performance.clearMarks(`${name}-end`);
          performance.clearMeasures(name);
        }
      } catch (error) {
        console.warn('Performance measurement failed:', error);
      }
    }, [trackPerformance]),

    // Track custom timing
    trackTiming: useCallback((name: string, duration: number) => {
      trackPerformance(name, Math.round(duration));
    }, [trackPerformance]),

    // Track component render performance
    trackRender: useCallback((componentName: string, renderTime: number) => {
      trackPerformance(`render_${componentName}`, Math.round(renderTime));
      
      // Alert on slow renders (>16ms for 60fps)
      if (renderTime > 16) {
        trackEvent('slow_render', {
          component: componentName,
          duration: Math.round(renderTime),
        });
      }
    }, [trackPerformance, trackEvent]),
  };
}

/**
 * Hook for tracking component render performance
 */
export function useRenderTracking(componentName: string) {
  const { trackRender } = usePerformanceMonitor();
  const renderStart = useRef<number>();

  const startRender = useCallback(() => {
    renderStart.current = performance.now();
  }, []);

  const endRender = useCallback(() => {
    if (renderStart.current) {
      const duration = performance.now() - renderStart.current;
      trackRender(componentName, duration);
      renderStart.current = undefined;
    }
  }, [componentName, trackRender]);

  return { startRender, endRender };
}

/**
 * Hook for tracking API call performance
 */
export function useApiTracking() {
  const { trackPerformance, trackEvent } = useAnalytics();

  const trackApiCall = useCallback((
    endpoint: string,
    method: string,
    duration: number,
    success: boolean,
    status?: number
  ) => {
    trackPerformance(`api_${endpoint}_${method}`, Math.round(duration));
    
    trackEvent('api_call', {
      endpoint,
      method,
      duration: Math.round(duration),
      success,
      status,
    });

    // Track slow API calls (>5s)
    if (duration > 5000) {
      trackEvent('slow_api_call', {
        endpoint,
        method,
        duration: Math.round(duration),
      });
    }
  }, [trackPerformance, trackEvent]);

  return { trackApiCall };
}

/**
 * Hook for tracking user interaction performance
 */
export function useInteractionTracking() {
  const { trackPerformance, trackEvent } = useAnalytics();

  const trackInteraction = useCallback((
    type: 'click' | 'scroll' | 'input' | 'focus',
    element: string,
    responseTime: number
  ) => {
    trackPerformance(`interaction_${type}_${element}`, Math.round(responseTime));
    
    // Track slow interactions (>100ms)
    if (responseTime > 100) {
      trackEvent('slow_interaction', {
        type,
        element,
        response_time: Math.round(responseTime),
      });
    }
  }, [trackPerformance, trackEvent]);

  return { trackInteraction };
}