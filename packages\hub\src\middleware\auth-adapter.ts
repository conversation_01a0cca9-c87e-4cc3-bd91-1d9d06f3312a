import { Request } from 'express';
import { JWTUser } from '../utils/tokens';
import { SupabaseUser } from '../utils/supabase-jwt';

/**
 * Unified authenticated request interface that works with both auth systems
 */
export interface UnifiedAuthenticatedRequest extends Request {
  user?: JWTUser | SupabaseUser;
}

/**
 * Type guard to check if user is from Supabase auth
 */
export function isSupabaseUser(user: unknown): user is SupabaseUser {
  return user !== null && 
    typeof user === 'object' && 
    'id' in user && 
    'email' in user &&
    typeof (user as SupabaseUser).id === 'string' && 
    typeof (user as SupabaseUser).email === 'string';
}

/**
 * Type guard to check if user is from custom JWT auth
 */
export function isJWTUser(user: unknown): user is J<PERSON>TUser {
  return user !== null && 
    typeof user === 'object' && 
    'id' in user && 
    'email' in user &&
    typeof (user as <PERSON><PERSON><PERSON><PERSON>).id === 'string' && 
    typeof (user as <PERSON><PERSON><PERSON><PERSON>).email === 'string';
}

/**
 * Normalize user object to common interface
 */
export function normalizeUser(user: JWTUser | SupabaseUser): { id: string; email: string; name?: string } {
  return {
    id: user.id,
    email: user.email,
    name: user.name
  };
}