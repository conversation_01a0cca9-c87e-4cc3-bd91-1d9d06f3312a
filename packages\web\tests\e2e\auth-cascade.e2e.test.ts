import { test, expect, Page } from '@playwright/test';
import { AuthHelpers, testUser } from './auth-helpers';

/**
 * E2E Authentication Cascade Prevention Tests
 * 
 * These tests validate the complete authentication flow from end-to-end,
 * ensuring that authentication failures do not cascade through the system
 * and cause infinite retry loops or browser crashes.
 */

test.describe('Authentication Cascade Prevention E2E', () => {
  let page: Page;

  test.beforeEach(async ({ page: testPage }) => {
    page = testPage;
    // Start from a clean state
    await page.goto('http://localhost:3000/');
    await page.context().clearCookies();
    await page.evaluate(() => localStorage.clear());
  });

  test('should handle expired token gracefully without infinite retries', async () => {
    // Step 1: Login successfully
    await AuthHelpers.loginViaUI(page);
    
    // Verify we're on the dashboard
    await expect(page).toHaveURL(/\/(dashboard|import)/);
    
    // Step 2: Simulate token expiration by modifying localStorage
    await page.evaluate(() => {
      const storageKeys = Object.keys(localStorage).filter(key => key.includes('auth-token'));
      storageKeys.forEach(key => {
        const authData = JSON.parse(localStorage.getItem(key) || '{}');
        if (authData.access_token) {
          // Set token to expired
          authData.expires_at = Date.now() - 1000;
          authData.access_token = 'expired-token-for-testing';
          localStorage.setItem(key, JSON.stringify(authData));
        }
      });
    });

    // Step 3: Navigate to a protected route that requires authentication
    await page.goto('http://localhost:3000/dashboard');
    
    // Step 4: Monitor network requests to ensure no infinite loop
    const apiRequests: string[] = [];
    page.on('request', request => {
      if (request.url().includes('/api/') || request.url().includes('supabase')) {
        apiRequests.push(`${request.method()} ${request.url()}`);
      }
    });

    // Wait for any potential cascade to occur
    await page.waitForTimeout(3000);

    // Step 5: Verify no infinite retry loop occurred
    // Count auth-related requests
    const authRequests = apiRequests.filter(req => 
      req.includes('/auth/') || req.includes('/token') || req.includes('/session')
    );
    
    // Should have limited auth attempts (less than 5)
    expect(authRequests.length).toBeLessThan(5);
    
    // Step 6: Verify user is redirected to login or shown appropriate error
    const currentUrl = page.url();
    const isOnLoginPage = currentUrl.includes('/login');
    const isOnErrorPage = currentUrl.includes('/error');
    const hasErrorMessage = await page.locator('text=/error|unauthorized|expired/i').isVisible();
    
    expect(isOnLoginPage || isOnErrorPage || hasErrorMessage).toBeTruthy();
  });

  test('should prevent cascade when refresh token is invalid', async () => {
    // Step 1: Setup initial auth state
    await AuthHelpers.loginViaUI(page);
    
    // Step 2: Corrupt the refresh token
    await page.evaluate(() => {
      const storageKeys = Object.keys(localStorage).filter(key => key.includes('auth-token'));
      storageKeys.forEach(key => {
        const authData = JSON.parse(localStorage.getItem(key) || '{}');
        if (authData.refresh_token) {
          authData.refresh_token = 'invalid-refresh-token';
          authData.expires_at = Date.now() - 1000; // Also expire the access token
          localStorage.setItem(key, JSON.stringify(authData));
        }
      });
    });

    // Step 3: Intercept network requests to monitor behavior
    const refreshAttempts: number[] = [];
    await page.route('**/auth/v1/token**', async route => {
      refreshAttempts.push(Date.now());
      await route.fulfill({
        status: 401,
        contentType: 'application/json',
        body: JSON.stringify({ error: 'Invalid refresh token' })
      });
    });

    // Step 4: Trigger an API call that requires authentication
    await page.goto('http://localhost:3000/dashboard');
    
    // Wait for potential retries
    await page.waitForTimeout(2000);

    // Step 5: Verify limited refresh attempts
    expect(refreshAttempts.length).toBeLessThanOrEqual(2); // Initial + maybe one retry
    
    // Verify attempts are spaced out (not rapid fire)
    if (refreshAttempts.length > 1) {
      const timeBetweenAttempts = refreshAttempts[1] - refreshAttempts[0];
      expect(timeBetweenAttempts).toBeGreaterThan(100); // At least 100ms between attempts
    }
  });

  test('should handle rate limiting gracefully', async () => {
    await AuthHelpers.loginViaUI(page);
    
    // Intercept API calls to simulate rate limiting
    let rateLimitedRequests = 0;
    await page.route('**/api/v1/**', async route => {
      rateLimitedRequests++;
      await route.fulfill({
        status: 429,
        headers: { 'Retry-After': '60' },
        contentType: 'application/json',
        body: JSON.stringify({ error: 'Rate limit exceeded' })
      });
    });

    // Navigate to dashboard which triggers API calls
    await page.goto('http://localhost:3000/dashboard');
    
    // Wait for the page to handle the rate limit
    await page.waitForTimeout(2000);
    
    // Should show appropriate error message
    const errorVisible = await page.locator('text=/rate limit|too many requests|try again/i').isVisible();
    expect(errorVisible).toBeTruthy();
    
    // Should not make excessive requests
    expect(rateLimitedRequests).toBeLessThan(3);
  });

  test('should maintain session stability during concurrent requests', async () => {
    await AuthHelpers.loginViaUI(page);
    
    // Navigate to dashboard
    await page.goto('http://localhost:3000/dashboard');
    
    // Trigger multiple concurrent actions that require auth
    const actions = [
      page.click('[data-testid="refresh-trips"]').catch(() => {}),
      page.click('[data-testid="load-more"]').catch(() => {}),
      page.click('[data-testid="user-menu"]').catch(() => {})
    ];
    
    // Execute concurrent actions
    await Promise.all(actions);
    
    // Wait for any potential issues
    await page.waitForTimeout(1000);
    
    // Verify session is still valid
    const isLoggedIn = await AuthHelpers.isLoggedIn(page);
    expect(isLoggedIn).toBeTruthy();
    
    // Verify no error modals or redirects
    const hasErrorModal = await page.locator('[role="dialog"]:has-text("error")').isVisible();
    expect(hasErrorModal).toBeFalsy();
    expect(page.url()).toContain('/dashboard');
  });

  test('should handle network failures without cascading', async () => {
    await AuthHelpers.loginViaUI(page);
    
    // Simulate network failure for API calls
    await page.route('**/api/v1/**', async route => {
      await route.abort('failed');
    });

    // Try to navigate to dashboard
    await page.goto('http://localhost:3000/dashboard');
    
    // Wait for error handling
    await page.waitForTimeout(2000);
    
    // Should show network error message, not auth error
    const networkErrorVisible = await page.locator('text=/network|connection|offline/i').isVisible();
    const authErrorVisible = await page.locator('text=/unauthorized|login/i').isVisible();
    
    expect(networkErrorVisible || !authErrorVisible).toBeTruthy();
    
    // User should still be considered logged in (network issue, not auth issue)
    const isLoggedIn = await AuthHelpers.isLoggedIn(page);
    expect(isLoggedIn).toBeTruthy();
  });

  test('should recover gracefully from temporary auth failures', async () => {
    await AuthHelpers.loginViaUI(page);
    
    let failureCount = 0;
    
    // Simulate temporary auth failure that recovers
    await page.route('**/api/v1/trips**', async route => {
      failureCount++;
      if (failureCount <= 2) {
        // First two requests fail with 401
        await route.fulfill({
          status: 401,
          contentType: 'application/json',
          body: JSON.stringify({ error: 'Unauthorized' })
        });
      } else {
        // Third request succeeds
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({ 
            success: true, 
            data: { trips: [], total: 0, page: 1, limit: 20 } 
          })
        });
      }
    });

    // Mock successful token refresh
    await page.route('**/auth/v1/token**', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          access_token: 'new-valid-token',
          refresh_token: 'new-refresh-token',
          expires_in: 3600
        })
      });
    });

    // Navigate to dashboard
    await page.goto('http://localhost:3000/dashboard');
    
    // Wait for recovery
    await page.waitForTimeout(3000);
    
    // Should eventually show dashboard content
    await expect(page.locator('[data-testid="dashboard-content"], .dashboard-container')).toBeVisible();
    
    // Should not be on login page
    expect(page.url()).not.toContain('/login');
  });

  test('should handle logout during active requests', async () => {
    await AuthHelpers.loginViaUI(page);
    await page.goto('http://localhost:3000/dashboard');
    
    // Start a slow API request
    let slowRequestCompleted = false;
    page.route('**/api/v1/trips**', async route => {
      await page.waitForTimeout(2000); // Simulate slow request
      slowRequestCompleted = true;
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({ success: true, data: { trips: [] } })
      });
    });

    // Trigger the slow request (don't await)
    page.click('[data-testid="refresh-trips"], button:has-text("Refresh")').catch(() => {});
    
    // Immediately logout
    await page.waitForTimeout(100);
    await AuthHelpers.logout(page);
    
    // Wait for potential issues
    await page.waitForTimeout(3000);
    
    // Should be on login page
    expect(page.url()).toContain('/login');
    
    // No JavaScript errors should occur
    const jsErrors: string[] = [];
    page.on('pageerror', error => jsErrors.push(error.message));
    await page.waitForTimeout(1000);
    expect(jsErrors).toHaveLength(0);
  });

  test('should prevent authentication loops in SPA navigation', async () => {
    await AuthHelpers.loginViaUI(page);
    
    // Simulate token becoming invalid mid-session
    await page.evaluate(() => {
      // Set up a timer to invalidate token after navigation
      setTimeout(() => {
        const storageKeys = Object.keys(localStorage).filter(key => key.includes('auth-token'));
        storageKeys.forEach(key => {
          const authData = JSON.parse(localStorage.getItem(key) || '{}');
          authData.access_token = 'suddenly-invalid-token';
          localStorage.setItem(key, JSON.stringify(authData));
        });
      }, 500);
    });

    // Navigate through the app
    await page.goto('http://localhost:3000/dashboard');
    await page.waitForTimeout(600); // Wait for token to be invalidated
    
    // Try to navigate to another protected route
    await page.click('a[href="/trips"], [data-testid="trips-link"]').catch(() => {});
    
    // Monitor for navigation loops
    const navigationEvents: string[] = [];
    page.on('framenavigated', frame => {
      if (frame === page.mainFrame()) {
        navigationEvents.push(frame.url());
      }
    });
    
    await page.waitForTimeout(3000);
    
    // Check for navigation loops (same URL appearing multiple times)
    const uniqueUrls = new Set(navigationEvents);
    expect(uniqueUrls.size).toBeGreaterThanOrEqual(navigationEvents.length - 2); // Allow max 2 retries
  });

  test('should handle auth state synchronization across tabs', async ({ context }) => {
    // Login in first tab
    const page1 = await context.newPage();
    await page1.goto('http://localhost:3000/');
    await AuthHelpers.loginViaUI(page1);
    
    // Open second tab
    const page2 = await context.newPage();
    await page2.goto('http://localhost:3000/dashboard');
    
    // Both tabs should be authenticated
    expect(await AuthHelpers.isLoggedIn(page1)).toBeTruthy();
    expect(await AuthHelpers.isLoggedIn(page2)).toBeTruthy();
    
    // Logout from first tab
    await AuthHelpers.logout(page1);
    
    // Wait for state sync
    await page2.waitForTimeout(1000);
    
    // Second tab should detect logout (via storage events)
    // Try to interact with the page
    await page2.click('body').catch(() => {});
    await page2.waitForTimeout(1000);
    
    // Should redirect to login or show logged out state
    const isLoggedOut = (await page2.url()).includes('/login') || 
                       !(await AuthHelpers.isLoggedIn(page2));
    expect(isLoggedOut).toBeTruthy();
  });

  test('should maintain stable state during rapid auth status checks', async () => {
    await AuthHelpers.loginViaUI(page);
    await page.goto('http://localhost:3000/dashboard');
    
    // Simulate rapid polling of auth status (like a real-time app might do)
    const authChecks: boolean[] = [];
    
    for (let i = 0; i < 10; i++) {
      const isAuthenticated = await page.evaluate(() => {
        // Check if auth token exists in localStorage
        const storageKeys = Object.keys(localStorage).filter(key => key.includes('auth-token'));
        return storageKeys.some(key => {
          const data = JSON.parse(localStorage.getItem(key) || '{}');
          return data.access_token && data.expires_at > Date.now();
        });
      });
      authChecks.push(isAuthenticated);
      await page.waitForTimeout(100);
    }
    
    // All checks should return the same result (stable state)
    const allSame = authChecks.every(check => check === authChecks[0]);
    expect(allSame).toBeTruthy();
    
    // Should remain logged in
    expect(authChecks[0]).toBeTruthy();
  });
});

test.describe('Circuit Breaker E2E Tests', () => {
  test('should activate circuit breaker after consecutive failures', async ({ page }) => {
    await AuthHelpers.loginViaUI(page);
    
    let requestCount = 0;
    
    // Intercept all API calls to fail
    await page.route('**/api/v1/**', async route => {
      requestCount++;
      await route.fulfill({
        status: 500,
        contentType: 'application/json',
        body: JSON.stringify({ error: 'Server error' })
      });
    });

    // Navigate to dashboard (triggers API calls)
    await page.goto('http://localhost:3000/dashboard');
    
    // Try multiple actions that would trigger API calls
    for (let i = 0; i < 5; i++) {
      await page.click('[data-testid="refresh-trips"], button:has-text("Refresh")').catch(() => {});
      await page.waitForTimeout(500);
    }
    
    // Circuit breaker should limit requests after 3 failures
    expect(requestCount).toBeLessThanOrEqual(4); // Initial load + 3 failures
    
    // Should show service unavailable message
    const errorMessage = await page.locator('text=/service unavailable|temporarily unavailable|try again later/i').isVisible();
    expect(errorMessage).toBeTruthy();
  });

  test('should reset circuit breaker after timeout', async ({ page }) => {
    await AuthHelpers.loginViaUI(page);
    
    let failureCount = 0;
    let successCount = 0;
    
    // Setup route handler that fails initially then succeeds
    await page.route('**/api/v1/trips**', async route => {
      if (Date.now() < startTime + 5000) {
        failureCount++;
        await route.fulfill({
          status: 500,
          contentType: 'application/json',
          body: JSON.stringify({ error: 'Server error' })
        });
      } else {
        successCount++;
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({ 
            success: true, 
            data: { trips: [], total: 0 } 
          })
        });
      }
    });

    const startTime = Date.now();
    
    // Navigate to dashboard (should fail)
    await page.goto('http://localhost:3000/dashboard');
    
    // Try a few more times to open circuit breaker
    for (let i = 0; i < 3; i++) {
      await page.click('[data-testid="refresh-trips"]').catch(() => {});
      await page.waitForTimeout(200);
    }
    
    // Wait for circuit breaker timeout (simulated shorter timeout for testing)
    await page.waitForTimeout(6000);
    
    // Try again - should succeed now
    await page.reload();
    
    // Should eventually show success
    await expect(page.locator('[data-testid="dashboard-content"], .trips-container, text=/no trips/i')).toBeVisible({ timeout: 5000 });
    
    expect(successCount).toBeGreaterThan(0);
  });
});