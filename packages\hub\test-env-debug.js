const { config } = require('dotenv');
const path = require('path');

// Load .env.test exactly as test setup does
config({ path: path.resolve(__dirname, '.env.test') });

console.log('Environment variables after loading .env.test:');
console.log('UPSTASH_REDIS_REST_URL:', process.env.UPSTASH_REDIS_REST_URL);
console.log('UPSTASH_REDIS_REST_TOKEN:', process.env.UPSTASH_REDIS_REST_TOKEN?.substring(0, 20) + '...');

// Try to connect using the exact same code
const { Redis } = require('@upstash/redis');

async function testRedis() {
  const url = process.env.UPSTASH_REDIS_REST_URL;
  const token = process.env.UPSTASH_REDIS_REST_TOKEN;
  
  console.log('\nTrying to connect with:');
  console.log('URL:', url);
  console.log('Token:', token ? 'Set' : 'Not set');
  
  try {
    const redis = new Redis({
      url,
      token,
      retry: {
        retries: 3,
        backoff: (attempt) => Math.min(attempt * 100, 3000)
      }
    });
    
    console.log('\nPinging Redis...');
    const result = await redis.ping();
    console.log('Success! Result:', result);
  } catch (error) {
    console.error('\nFailed to connect:', error.message);
    if (error.response) {
      console.error('Response:', error.response);
    }
  }
}

testRedis();