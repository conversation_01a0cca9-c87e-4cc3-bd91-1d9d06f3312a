import { TripsService } from '../services/trips.service';
import { vi, describe, it, expect, beforeEach, afterEach } from 'vitest';
import { getSupabaseClient, TABLES } from '../lib/supabase';

// Mock the supabase client
vi.mock('../lib/supabase', () => ({
  getSupabaseClient: vi.fn(),
  TABLES: {
    TRIPS: 'trips',
    ACTIVITIES: 'activities'
  },
  handleSupabaseError: vi.fn((error) => ({
    message: error?.message || 'Unknown error',
    statusCode: error?.status || 500,
    code: error?.code
  })),
  validateDatabaseResponse: vi.fn((schema, data) => data),
  TripSchema: {},
  ActivitySchema: {}
}));

describe('TripsService', () => {
  let tripsService: TripsService;
  let mockSupabaseClient: any;

  beforeEach(() => {
    tripsService = new TripsService();
    
    // Create the mock with proper chaining
    const createChainableMock = () => {
      const mock: any = {
        from: vi.fn(),
        select: vi.fn(),
        insert: vi.fn(),
        update: vi.fn(),
        delete: vi.fn(),
        eq: vi.fn(),
        order: vi.fn(),
        single: vi.fn(),
        upsert: vi.fn(),
        rpc: vi.fn().mockResolvedValue({ error: null }),
        limit: vi.fn(),
      };
      
      // Make all methods return the mock itself for chaining
      Object.keys(mock).forEach(key => {
        if (key !== 'single' && key !== 'rpc') {
          mock[key].mockReturnValue(mock);
        }
      });
      
      // single should return a promise
      mock.single.mockResolvedValue({ data: null, error: null });
      
      return mock;
    };
    
    mockSupabaseClient = createChainableMock();
    (getSupabaseClient as any).mockReturnValue(mockSupabaseClient);
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  it('should be defined', () => {
    expect(tripsService).toBeDefined();
  });

  describe('createTrip', () => {
    it('should create a trip successfully', async () => {
      const tripData = {
        userId: 'a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a11',
        title: 'Test Trip',
      };
      const expectedTrip = {
        id: 'a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a12',
        user_id: tripData.userId,
        title: tripData.title,
        description: null,
        destination: null,
        start_date: null,
        end_date: null,
        status: 'draft',
        visibility: 'private',
        cover_image: null,
        metadata: {},
        tags: [],
        budget_amount: null,
        budget_currency: 'USD',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        deleted_at: null,
      };

      mockSupabaseClient.single.mockResolvedValueOnce({ data: expectedTrip, error: null });

      const result = await tripsService.createTrip(tripData);

      expect(result.title).toBe(tripData.title);
      expect(result.user_id).toBe(tripData.userId);
      expect(result.activities).toEqual([]);
      expect(mockSupabaseClient.from).toHaveBeenCalledWith('trips');
      expect(mockSupabaseClient.insert).toHaveBeenCalled();
      expect(mockSupabaseClient.select).toHaveBeenCalled();
      expect(mockSupabaseClient.single).toHaveBeenCalled();
    });
  });

  describe('getUserTrips', () => {
    it('should return an array of trips for a given user', async () => {
      const userId = 'a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a11';
      const expectedTrips = [
        {
          id: 'a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a12',
          user_id: userId,
          title: 'Test Trip 1',
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        },
        {
          id: 'a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a13',
          user_id: userId,
          title: 'Test Trip 2',
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        },
      ];

      mockSupabaseClient.order.mockResolvedValueOnce({ data: expectedTrips, error: null });

      const result = await tripsService.getUserTrips(userId);

      expect(result).toHaveLength(2);
      expect(mockSupabaseClient.from).toHaveBeenCalledWith(TABLES.TRIPS);
      expect(mockSupabaseClient.select).toHaveBeenCalledWith(expect.stringContaining('*'));
      expect(mockSupabaseClient.eq).toHaveBeenCalledWith('user_id', userId);
      expect(mockSupabaseClient.order).toHaveBeenCalledWith('created_at', { ascending: false });
    });
  });

  describe('getTripById', () => {
    it('should return a single trip with a given id', async () => {
      const tripId = 'a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a12';
      const userId = 'a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a11';
      const expectedTrip = {
        id: tripId,
        user_id: userId,
        title: 'Test Trip 1',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      };

      mockSupabaseClient.single.mockResolvedValueOnce({ data: expectedTrip, error: null });

      const result = await tripsService.getTripById(tripId, userId);

      expect(result).toEqual(expect.objectContaining({ id: tripId }));
      expect(mockSupabaseClient.from).toHaveBeenCalledWith(TABLES.TRIPS);
      expect(mockSupabaseClient.select).toHaveBeenCalledWith(expect.stringContaining('*'));
      expect(mockSupabaseClient.eq).toHaveBeenCalledWith('id', tripId);
      expect(mockSupabaseClient.eq).toHaveBeenCalledWith('user_id', userId);
      expect(mockSupabaseClient.single).toHaveBeenCalled();
    });
  });

  describe('updateTrip', () => {
    it('should update a trip successfully', async () => {
      const tripId = 'a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a12';
      const userId = 'a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a11';
      const updateData = { title: 'Updated Test Trip' };
      const expectedTrip = {
        id: tripId,
        user_id: userId,
        title: 'Updated Test Trip',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      };

      mockSupabaseClient.single.mockResolvedValueOnce({ data: expectedTrip, error: null });

      const result = await tripsService.updateTrip(tripId, userId, updateData);

      expect(result).toEqual(expect.objectContaining({ title: 'Updated Test Trip' }));
      expect(mockSupabaseClient.from).toHaveBeenCalledWith(TABLES.TRIPS);
      expect(mockSupabaseClient.update).toHaveBeenCalledWith(expect.objectContaining(updateData));
      expect(mockSupabaseClient.eq).toHaveBeenCalledWith('id', tripId);
      expect(mockSupabaseClient.eq).toHaveBeenCalledWith('user_id', userId);
      expect(mockSupabaseClient.select).toHaveBeenCalled();
      expect(mockSupabaseClient.single).toHaveBeenCalled();
    });
  });

  describe('deleteTrip', () => {
    it('should delete a trip successfully', async () => {
      const tripId = 'a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a12';
      const userId = 'a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a11';

      mockSupabaseClient.delete.mockReturnThis();
      mockSupabaseClient.eq.mockReturnThis();
      mockSupabaseClient.single.mockResolvedValueOnce({ error: null });

      const result = await tripsService.deleteTrip(tripId, userId);

      expect(result).toBe(true);
      expect(mockSupabaseClient.from).toHaveBeenCalledWith(TABLES.TRIPS);
      expect(mockSupabaseClient.delete).toHaveBeenCalled();
      expect(mockSupabaseClient.eq).toHaveBeenCalledWith('id', tripId);
      expect(mockSupabaseClient.eq).toHaveBeenCalledWith('user_id', userId);
    });
  });

  describe('addActivityToTrip', () => {
    it('should add an activity to a trip successfully', async () => {
      const tripId = 'a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a12';
      const userId = 'a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a11';
      const activityData = {
        title: 'Test Activity',
        description: 'A test activity',
      };
      const expectedActivity = {
        id: 'a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a13',
        trip_id: tripId,
        title: activityData.title,
        description: activityData.description,
        position: 0,
        type: 'activity',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      };

      mockSupabaseClient.single.mockResolvedValueOnce({ data: { id: tripId }, error: null }); // For trip ownership check
      mockSupabaseClient.single.mockResolvedValueOnce({ data: expectedActivity, error: null }); // For activity insertion

      const result = await tripsService.addActivityToTrip(tripId, userId, activityData);

      expect(result).toEqual(expect.objectContaining({ title: activityData.title }));
      expect(mockSupabaseClient.from).toHaveBeenCalledWith(TABLES.TRIPS);
      expect(mockSupabaseClient.from).toHaveBeenCalledWith(TABLES.ACTIVITIES);
      expect(mockSupabaseClient.insert).toHaveBeenCalled();
      expect(mockSupabaseClient.select).toHaveBeenCalled();
      expect(mockSupabaseClient.single).toHaveBeenCalledTimes(2);
    });
  });

  describe('updateActivity', () => {
    it('should update an activity successfully', async () => {
      const activityId = 'a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a13';
      const userId = 'a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a11';
      const updateData = { title: 'Updated Activity' };
      const expectedActivity = {
        id: activityId,
        trip_id: 'a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a12',
        title: updateData.title,
        position: 0,
        type: 'activity',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      };

      mockSupabaseClient.single.mockResolvedValueOnce({ data: { id: activityId, trips: { user_id: userId } }, error: null }); // For ownership check
      mockSupabaseClient.single.mockResolvedValueOnce({ data: expectedActivity, error: null }); // For activity update

      const result = await tripsService.updateActivity(activityId, userId, updateData);

      expect(result).toEqual(expect.objectContaining({ title: updateData.title }));
      expect(mockSupabaseClient.from).toHaveBeenCalledWith(TABLES.ACTIVITIES);
      expect(mockSupabaseClient.update).toHaveBeenCalledWith(expect.objectContaining(updateData));
      expect(mockSupabaseClient.eq).toHaveBeenCalledWith('id', activityId);
      expect(mockSupabaseClient.select).toHaveBeenCalledTimes(2);
      expect(mockSupabaseClient.single).toHaveBeenCalledTimes(2);
    });
  });

  describe('deleteActivity', () => {
    it('should delete an activity successfully', async () => {
      const activityId = 'a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a13';
      const userId = 'a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a11';

      // We need to handle two different call chains:
      // 1. from(ACTIVITIES).select(...).eq(id, activityId).single() - for ownership check
      // 2. from(ACTIVITIES).delete().eq(id, activityId) - for delete operation
      
      // Reset mocks to track call order
      let callIndex = 0;
      
      // Track which method follows from()
      mockSupabaseClient.from.mockImplementation(() => {
        callIndex++;
        return mockSupabaseClient;
      });
      
      // For the first chain (select), single should return the ownership data
      // For the second chain (delete), eq should return a promise
      mockSupabaseClient.eq.mockImplementation(() => {
        if (callIndex === 1) {
          // First chain: ownership check - eq returns mock for .single()
          return mockSupabaseClient;
        } else {
          // Second chain: delete operation - eq returns a promise
          return Promise.resolve({ error: null });
        }
      });
      
      // Setup for ownership check
      mockSupabaseClient.single.mockResolvedValueOnce({ 
        data: { 
          id: activityId, 
          trips: { user_id: userId } 
        }, 
        error: null 
      });

      const result = await tripsService.deleteActivity(activityId, userId);

      expect(result).toBe(true);
      expect(mockSupabaseClient.from).toHaveBeenCalledWith(TABLES.ACTIVITIES);
      expect(mockSupabaseClient.delete).toHaveBeenCalled();
      expect(mockSupabaseClient.eq).toHaveBeenCalledWith('id', activityId);
    });
  });

  describe('reorderActivities', () => {
    const tripId = 'a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a12';
    const userId = 'a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a11';

    it('should reorder activities successfully', async () => {
      const activity1Id = 'a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a21';
      const activity2Id = 'a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a22';
      const activity3Id = 'a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a23';
      
      const orderedIds = [activity3Id, activity1Id, activity2Id];
      const activities = [
        { id: activity1Id, trip_id: tripId, position: 0 },
        { id: activity2Id, trip_id: tripId, position: 1 },
        { id: activity3Id, trip_id: tripId, position: 2 },
      ];
      const reorderedActivities = [
        { 
          id: activity3Id, 
          trip_id: tripId, 
          position: 0,
          title: 'Activity 3',
          type: 'activity',
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        },
        { 
          id: activity1Id, 
          trip_id: tripId, 
          position: 1,
          title: 'Activity 1',
          type: 'activity',
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        },
        { 
          id: activity2Id, 
          trip_id: tripId, 
          position: 2,
          title: 'Activity 2',
          type: 'activity',
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        },
      ];

      // Set up the mock chain for trip check
      let callCount = 0;
      mockSupabaseClient.from.mockImplementation(() => {
        callCount++;
        if (callCount === 1) {
          // First call - trip ownership check
          mockSupabaseClient.single.mockResolvedValueOnce({ 
            data: { id: tripId, user_id: userId }, 
            error: null 
          });
        } else if (callCount === 2) {
          // Second call - fetch activities
          mockSupabaseClient.eq.mockReturnValueOnce(Promise.resolve({
            data: activities,
            error: null
          }));
        } else if (callCount === 3) {
          // Third call - batch update
          mockSupabaseClient.upsert.mockResolvedValueOnce({
            error: null
          });
        } else if (callCount === 4) {
          // Fourth call - fetch updated activities
          mockSupabaseClient.order.mockResolvedValueOnce({
            data: reorderedActivities,
            error: null
          });
        }
        return mockSupabaseClient;
      });

      const result = await tripsService.reorderActivities(tripId, userId, orderedIds);

      expect(result).toHaveLength(3);
      expect(result[0].id).toBe(activity3Id);
      expect(result[0].position).toBe(0);
      expect(result[1].id).toBe(activity1Id);
      expect(result[1].position).toBe(1);
      expect(result[2].id).toBe(activity2Id);
      expect(result[2].position).toBe(2);
      expect(mockSupabaseClient.upsert).toHaveBeenCalledWith(
        [
          { id: activity3Id, trip_id: tripId, position: 0 },
          { id: activity1Id, trip_id: tripId, position: 1 },
          { id: activity2Id, trip_id: tripId, position: 2 }
        ],
        { onConflict: 'id' }
      );
    });

    it('should throw error if trip not found', async () => {
      const orderedIds = ['a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a21'];

      // Mock trip not found
      mockSupabaseClient.single.mockResolvedValueOnce({ 
        data: null, 
        error: { code: 'PGRST116' } 
      });

      await expect(
        tripsService.reorderActivities(tripId, userId, orderedIds)
      ).rejects.toThrow('Trip not found');
    });

    it('should throw error if user does not own the trip', async () => {
      const orderedIds = ['a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a21'];

      // Mock trip with different owner
      mockSupabaseClient.single.mockResolvedValueOnce({ 
        data: { id: tripId, user_id: 'different-user' }, 
        error: null 
      });

      await expect(
        tripsService.reorderActivities(tripId, userId, orderedIds)
      ).rejects.toThrow('Unauthorized to modify this trip');
    });

    it('should throw error if orderedIds contains invalid activity IDs', async () => {
      const validActivityId = 'a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a21';
      const invalidActivityId = 'a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a99';
      const orderedIds = [validActivityId, invalidActivityId];
      const activities = [
        { id: validActivityId, trip_id: tripId, position: 0 },
      ];

      let callCount = 0;
      mockSupabaseClient.from.mockImplementation(() => {
        callCount++;
        if (callCount === 1) {
          // First call - trip ownership check
          mockSupabaseClient.single.mockResolvedValueOnce({ 
            data: { id: tripId, user_id: userId }, 
            error: null 
          });
        } else if (callCount === 2) {
          // Second call - fetch activities
          mockSupabaseClient.eq.mockReturnValueOnce(Promise.resolve({
            data: activities,
            error: null
          }));
        }
        return mockSupabaseClient;
      });

      await expect(
        tripsService.reorderActivities(tripId, userId, orderedIds)
      ).rejects.toThrow('Invalid activity IDs provided');
    });

    it('should handle database errors when fetching activities', async () => {
      const orderedIds = ['a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a21'];

      let callCount = 0;
      mockSupabaseClient.from.mockImplementation(() => {
        callCount++;
        if (callCount === 1) {
          // First call - trip ownership check
          mockSupabaseClient.single.mockResolvedValueOnce({ 
            data: { id: tripId, user_id: userId }, 
            error: null 
          });
        } else if (callCount === 2) {
          // Second call - fetch activities with error
          mockSupabaseClient.eq.mockReturnValueOnce(Promise.resolve({
            data: null,
            error: { message: 'Database error' }
          }));
        }
        return mockSupabaseClient;
      });

      await expect(
        tripsService.reorderActivities(tripId, userId, orderedIds)
      ).rejects.toThrow('Failed to fetch activities');
    });

    it('should handle database errors when updating positions', async () => {
      const activityId = 'a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a21';
      const orderedIds = [activityId];
      const activities = [
        { id: activityId, trip_id: tripId, position: 0 },
      ];

      let callCount = 0;
      mockSupabaseClient.from.mockImplementation(() => {
        callCount++;
        if (callCount === 1) {
          // First call - trip ownership check
          mockSupabaseClient.single.mockResolvedValueOnce({ 
            data: { id: tripId, user_id: userId }, 
            error: null 
          });
        } else if (callCount === 2) {
          // Second call - fetch activities
          mockSupabaseClient.eq.mockReturnValueOnce(Promise.resolve({
            data: activities,
            error: null
          }));
        } else if (callCount === 3) {
          // Third call - batch update with error
          mockSupabaseClient.upsert.mockResolvedValueOnce({
            error: { message: 'Update failed' }
          });
        }
        return mockSupabaseClient;
      });

      await expect(
        tripsService.reorderActivities(tripId, userId, orderedIds)
      ).rejects.toThrow('Failed to update activity positions');
    });
  });
});