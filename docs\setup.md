# TravelViz Setup Guide

This guide will help you set up the TravelViz development environment from scratch.

## Prerequisites

### Required Software
- **Node.js** (v18 or higher) - [Download](https://nodejs.org/)
- **pnpm** (v9.0.0 or higher) - [Install Guide](https://pnpm.io/installation)
- **Git** - [Download](https://git-scm.com/)

### Recommended Tools
- **VS Code** with extensions:
  - TypeScript and JavaScript Language Features
  - Prettier - Code formatter
  - ESLint
  - Tailwind CSS IntelliSense
  - GitLens

## Installation Steps

### 1. Clone Repository
```bash
git clone <repository-url>
cd travelviz
```

### 2. Install Dependencies
```bash
# Install all workspace dependencies
pnpm install

# Verify installation
pnpm --version
node --version
```

### 3. Environment Configuration

#### Hub Environment (.env)
```bash
# Copy example file
cp packages/hub/.env.example packages/hub/.env

# Edit with your actual values
# Required for basic functionality:
# - SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY
# - JWT_SECRET (generate with: openssl rand -base64 32)
```

#### Web Environment (.env.local)
```bash
# Copy example file
cp packages/web/.env.example packages/web/.env.local

# Edit with your actual values
# Required for basic functionality:
# - NEXT_PUBLIC_SUPABASE_URL
# - NEXT_PUBLIC_SUPABASE_ANON_KEY
# - NEXT_PUBLIC_API_URL=http://localhost:3001
```

### 4. Database Setup (Supabase)

1. **Create Supabase Project**
   - Go to [supabase.com](https://supabase.com)
   - Create new project
   - Copy URL and anon key to your .env files

2. **Run Migrations** (when available)
   ```bash
   cd packages/hub
   pnpm db:migrate
   ```

### 5. Start Development Servers

```bash
# Start both hub and web in development mode
pnpm dev

# Or start individually:
pnpm --filter @travelviz/hub dev    # Hub on port 3001
pnpm --filter @travelviz/web dev    # Web on port 3000
```

## Verification

### 1. Check Services
- **Web Frontend**: http://localhost:3000
- **Hub API**: http://localhost:3001/health
- **API Test**: http://localhost:3001/api/test

### 2. Test TypeScript Compilation
```bash
# Check all packages
pnpm type-check

# Check individual packages
pnpm --filter @travelviz/hub type-check
pnpm --filter @travelviz/web type-check
```

### 3. Test Shared Package Import
```bash
# This should work without errors
curl http://localhost:3001/api/test
```

### 4. Verify Hot Reload
- Make a change in `packages/web/app/page.tsx`
- Make a change in `packages/hub/src/index.ts`
- Both should reload automatically

## Common Issues

### Port Already in Use
```bash
# Kill processes on ports 3000/3001
npx kill-port 3000 3001

# Or use different ports
PORT=3002 pnpm --filter @travelviz/hub dev
```

### TypeScript Errors
```bash
# Clear TypeScript cache
pnpm --filter @travelviz/hub clean
pnpm --filter @travelviz/web clean

# Rebuild shared package
pnpm --filter @travelviz/shared build
```

### Module Resolution Issues
```bash
# Clear all node_modules and reinstall
pnpm clean:all
pnpm install
```

## Next Steps

1. **Read Architecture Guide**: [architecture.md](./architecture.md)
2. **Set up API Keys**: Configure external services (Mapbox, OpenRouter, etc.)
3. **Run Tests**: `pnpm test`
4. **Start Development**: Check [development.md](./development.md)

## Environment Variables Reference

### Required for Basic Development
```bash
# Hub (.env)
SUPABASE_URL=your_supabase_url
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key
JWT_SECRET=your_32_character_secret

# Web (.env.local)
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
NEXT_PUBLIC_API_URL=http://localhost:3001
```

### Optional for Full Features
- **AI Parsing**: OPENROUTER_API_KEY
- **Maps**: NEXT_PUBLIC_MAPBOX_ACCESS_TOKEN
- **Flight APIs**: DUFFEL_API_KEY, TRAVELPAYOUTS_API_KEY
- **Hotel APIs**: EXPEDIA_API_KEY
- **Caching**: REDIS_URL

---

*Need help? Check [troubleshooting.md](./operations/troubleshooting.md) or create an issue.*
