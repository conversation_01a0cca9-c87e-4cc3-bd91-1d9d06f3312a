-- Migration: Add RPC functions for query optimization
-- These functions are expected by the application code

BEGIN;

-- ============================================
-- 1. DASHBOARD STATISTICS FUNCTION
-- ============================================

CREATE OR REPLACE FUNCTION get_user_dashboard_stats(p_user_id UUID)
RETURNS JSON
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  result JSON;
BEGIN
  SELECT json_build_object(
    'total_trips', COUNT(DISTINCT t.id),
    'upcoming_trips', COUNT(DISTINCT t.id) FILTER (WHERE t.start_date > CURRENT_DATE AND t.status != 'cancelled'),
    'completed_trips', COUNT(DISTINCT t.id) FILTER (WHERE t.end_date < CURRENT_DATE OR t.status = 'completed'),
    'total_activities', COUNT(DISTINCT a.id),
    'total_destinations', COUNT(DISTINCT t.destination),
    'total_budget', COALESCE(SUM(DISTINCT t.budget_amount), 0)
  ) INTO result
  FROM trips t
  LEFT JOIN activities a ON a.trip_id = t.id
  WHERE t.user_id = p_user_id 
    AND t.deleted_at IS NULL;
  
  RETURN result;
END;
$$;

-- ============================================
-- 2. TRIP ACTIVITY STATISTICS
-- ============================================

CREATE OR REPLACE FUNCTION get_trip_activity_stats(p_trip_id UUID)
RETURNS JSON
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  result JSON;
BEGIN
  SELECT json_build_object(
    'total_activities', COUNT(*),
    'by_type', json_object_agg(
      type, 
      type_count
    ) FILTER (WHERE type IS NOT NULL),
    'total_cost', SUM(price),
    'date_range', json_build_object(
      'earliest', MIN(start_time),
      'latest', MAX(end_time)
    )
  ) INTO result
  FROM (
    SELECT 
      type,
      COUNT(*) as type_count,
      price,
      start_time,
      end_time
    FROM activities
    WHERE trip_id = p_trip_id
    GROUP BY type, price, start_time, end_time
  ) a;
  
  RETURN result;
END;
$$;

-- ============================================
-- 3. BATCH ACTIVITY COUNT FUNCTION
-- ============================================

CREATE OR REPLACE FUNCTION get_activity_counts_batch(trip_ids UUID[])
RETURNS TABLE(trip_id UUID, activity_count BIGINT)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  RETURN QUERY
  SELECT 
    a.trip_id,
    COUNT(*)::BIGINT as activity_count
  FROM activities a
  WHERE a.trip_id = ANY(trip_ids)
  GROUP BY a.trip_id;
END;
$$;

-- ============================================
-- 4. OPTIMIZED TRIP PREVIEW FUNCTION
-- ============================================

CREATE OR REPLACE FUNCTION get_trip_preview_optimized(trip_slug TEXT)
RETURNS JSON
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  result JSON;
BEGIN
  SELECT json_build_object(
    'id', t.id,
    'title', t.title,
    'description', t.description,
    'destination', t.destination,
    'start_date', t.start_date,
    'end_date', t.end_date,
    'status', t.status,
    'visibility', t.visibility,
    'cover_image', t.cover_image,
    'activity_count', COUNT(a.id),
    'user', json_build_object(
      'id', p.id,
      'name', p.name,
      'avatar_url', p.avatar_url
    )
  ) INTO result
  FROM trips t
  LEFT JOIN activities a ON a.trip_id = t.id
  LEFT JOIN profiles p ON p.id = t.user_id
  WHERE t.share_slug = trip_slug
    AND t.deleted_at IS NULL
    AND (t.visibility = 'public' OR t.visibility = 'unlisted')
  GROUP BY t.id, p.id, p.name, p.avatar_url;
  
  RETURN result;
END;
$$;

-- ============================================
-- 5. BATCH UPDATE ACTIVITY POSITIONS
-- ============================================

CREATE OR REPLACE FUNCTION batch_update_activity_positions(activity_updates JSONB)
RETURNS VOID
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  update_record JSONB;
BEGIN
  -- Validate input
  IF activity_updates IS NULL OR jsonb_array_length(activity_updates) = 0 THEN
    RETURN;
  END IF;

  -- Update each activity position
  FOR update_record IN SELECT * FROM jsonb_array_elements(activity_updates)
  LOOP
    UPDATE activities
    SET 
      position = (update_record->>'position')::INTEGER,
      updated_at = NOW()
    WHERE id = (update_record->>'id')::UUID;
  END LOOP;
END;
$$;

-- ============================================
-- 6. TRIPS WITH ACTIVITIES BATCH LOADER
-- ============================================

CREATE OR REPLACE FUNCTION get_trips_with_activities_batch(trip_ids UUID[])
RETURNS TABLE(
  trip_data JSON
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  RETURN QUERY
  WITH trip_activities AS (
    SELECT 
      t.id as trip_id,
      json_agg(
        json_build_object(
          'id', a.id,
          'title', a.title,
          'type', a.type,
          'start_time', a.start_time,
          'end_time', a.end_time,
          'location', a.location,
          'position', a.position
        ) ORDER BY a.position, a.start_time
      ) FILTER (WHERE a.id IS NOT NULL) as activities
    FROM trips t
    LEFT JOIN activities a ON a.trip_id = t.id
    WHERE t.id = ANY(trip_ids)
    GROUP BY t.id
  )
  SELECT 
    json_build_object(
      'id', t.id,
      'title', t.title,
      'destination', t.destination,
      'start_date', t.start_date,
      'end_date', t.end_date,
      'status', t.status,
      'visibility', t.visibility,
      'activities', COALESCE(ta.activities, '[]'::json)
    ) as trip_data
  FROM trips t
  LEFT JOIN trip_activities ta ON ta.trip_id = t.id
  WHERE t.id = ANY(trip_ids);
END;
$$;

-- ============================================
-- 7. SEARCH TRIPS RANKED FUNCTION
-- ============================================

CREATE OR REPLACE FUNCTION search_trips_ranked(
  p_search_query TEXT,
  p_user_id UUID DEFAULT NULL,
  p_limit INTEGER DEFAULT 20
)
RETURNS TABLE(
  trip_id UUID,
  title TEXT,
  destination TEXT,
  description TEXT,
  rank REAL,
  start_date DATE,
  end_date DATE,
  visibility TEXT
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  RETURN QUERY
  SELECT 
    t.id as trip_id,
    t.title,
    t.destination,
    t.description,
    ts_rank(t.search_vector, plainto_tsquery('english', p_search_query)) as rank,
    t.start_date,
    t.end_date,
    t.visibility
  FROM trips t
  WHERE 
    t.deleted_at IS NULL
    AND (
      -- If user_id provided, include their private trips
      (p_user_id IS NOT NULL AND t.user_id = p_user_id)
      OR 
      -- Always include public trips
      t.visibility = 'public'
    )
    AND (
      -- Full-text search
      t.search_vector @@ plainto_tsquery('english', p_search_query)
      OR
      -- Fuzzy match on title
      t.title ILIKE '%' || p_search_query || '%'
      OR
      -- Fuzzy match on destination
      t.destination ILIKE '%' || p_search_query || '%'
    )
  ORDER BY rank DESC, t.created_at DESC
  LIMIT p_limit;
END;
$$;

-- ============================================
-- 8. GENERIC BATCH OPERATION EXECUTOR
-- ============================================

CREATE OR REPLACE FUNCTION execute_batch_operation(
  p_table_name TEXT,
  p_operation TEXT,
  p_updates JSONB
)
RETURNS JSONB
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  allowed_tables TEXT[] := ARRAY['activities', 'trips'];
  allowed_operations TEXT[] := ARRAY['update_position', 'update_status'];
  result JSONB := '{"success": true, "affected_rows": 0}'::JSONB;
  affected_count INTEGER := 0;
BEGIN
  -- Security checks
  IF NOT (p_table_name = ANY(allowed_tables)) THEN
    RAISE EXCEPTION 'Invalid table name: %', p_table_name;
  END IF;
  
  IF NOT (p_operation = ANY(allowed_operations)) THEN
    RAISE EXCEPTION 'Invalid operation: %', p_operation;
  END IF;

  -- Execute based on operation
  IF p_operation = 'update_position' AND p_table_name = 'activities' THEN
    PERFORM batch_update_activity_positions(p_updates);
    GET DIAGNOSTICS affected_count = ROW_COUNT;
  END IF;

  result := jsonb_set(result, '{affected_rows}', to_jsonb(affected_count));
  RETURN result;
END;
$$;

-- ============================================
-- 9. PERFORMANCE MONITORING TABLE
-- ============================================

CREATE TABLE IF NOT EXISTS query_stats (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  query_fingerprint TEXT NOT NULL,
  query_text TEXT,
  execution_count INTEGER DEFAULT 1,
  total_time NUMERIC DEFAULT 0,
  mean_time NUMERIC DEFAULT 0,
  max_time NUMERIC DEFAULT 0,
  last_executed TIMESTAMPTZ DEFAULT NOW(),
  created_at TIMESTAMPTZ DEFAULT NOW()
);

CREATE INDEX IF NOT EXISTS idx_query_stats_fingerprint 
ON query_stats(query_fingerprint);

CREATE INDEX IF NOT EXISTS idx_query_stats_performance 
ON query_stats(mean_time DESC, execution_count DESC);

-- ============================================
-- 10. SLOW QUERY LOGGING FUNCTION
-- ============================================

CREATE OR REPLACE FUNCTION log_slow_query(
  p_query_text TEXT,
  p_execution_time NUMERIC,
  p_query_type TEXT DEFAULT 'unknown'
)
RETURNS VOID
LANGUAGE plpgsql
AS $$
DECLARE
  query_hash TEXT;
BEGIN
  -- Generate fingerprint
  query_hash := md5(regexp_replace(p_query_text, '\s+', ' ', 'g'));
  
  -- Insert or update stats
  INSERT INTO query_stats (
    query_fingerprint,
    query_text,
    execution_count,
    total_time,
    mean_time,
    max_time,
    last_executed
  ) VALUES (
    query_hash,
    p_query_text,
    1,
    p_execution_time,
    p_execution_time,
    p_execution_time,
    NOW()
  )
  ON CONFLICT (query_fingerprint) DO UPDATE SET
    execution_count = query_stats.execution_count + 1,
    total_time = query_stats.total_time + p_execution_time,
    mean_time = (query_stats.total_time + p_execution_time) / (query_stats.execution_count + 1),
    max_time = GREATEST(query_stats.max_time, p_execution_time),
    last_executed = NOW();
END;
$$;

-- ============================================
-- 11. GET SLOW QUERIES FUNCTION
-- ============================================

CREATE OR REPLACE FUNCTION get_slow_queries(threshold_ms NUMERIC DEFAULT 100)
RETURNS TABLE(
  query_text TEXT,
  execution_count INTEGER,
  mean_time_ms NUMERIC,
  max_time_ms NUMERIC,
  total_time_ms NUMERIC,
  last_executed TIMESTAMPTZ
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  RETURN QUERY
  SELECT 
    qs.query_text,
    qs.execution_count,
    ROUND(qs.mean_time, 2) as mean_time_ms,
    ROUND(qs.max_time, 2) as max_time_ms,
    ROUND(qs.total_time, 2) as total_time_ms,
    qs.last_executed
  FROM query_stats qs
  WHERE qs.mean_time > threshold_ms
  ORDER BY qs.mean_time DESC
  LIMIT 50;
END;
$$;

-- ============================================
-- 12. GRANTS FOR RPC FUNCTIONS
-- ============================================

-- Grant execute permissions to authenticated users
GRANT EXECUTE ON FUNCTION get_user_dashboard_stats(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION get_trip_activity_stats(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION get_activity_counts_batch(UUID[]) TO authenticated;
GRANT EXECUTE ON FUNCTION get_trip_preview_optimized(TEXT) TO anon, authenticated;
GRANT EXECUTE ON FUNCTION batch_update_activity_positions(JSONB) TO authenticated;
GRANT EXECUTE ON FUNCTION get_trips_with_activities_batch(UUID[]) TO authenticated;
GRANT EXECUTE ON FUNCTION search_trips_ranked(TEXT, UUID, INTEGER) TO anon, authenticated;
GRANT EXECUTE ON FUNCTION execute_batch_operation(TEXT, TEXT, JSONB) TO authenticated;

-- Performance monitoring functions are admin only
GRANT EXECUTE ON FUNCTION log_slow_query(TEXT, NUMERIC, TEXT) TO service_role;
GRANT EXECUTE ON FUNCTION get_slow_queries(NUMERIC) TO service_role;

COMMIT;

-- ============================================
-- VERIFICATION QUERIES
-- ============================================

/*
-- Test dashboard stats
SELECT get_user_dashboard_stats('your-user-id'::uuid);

-- Test activity counts
SELECT * FROM get_activity_counts_batch(ARRAY['trip-id-1'::uuid, 'trip-id-2'::uuid]);

-- Test trip search
SELECT * FROM search_trips_ranked('paris', NULL, 10);

-- Check functions exist
SELECT 
  routine_name,
  routine_type
FROM information_schema.routines
WHERE routine_schema = 'public'
ORDER BY routine_name;
*/