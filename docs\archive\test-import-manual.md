# Manual Test Instructions for AI Import Feature

## Services Started ✅

The services are configured but may need to be run in separate terminals:

**Terminal 1 - Hub Service:**

```bash
cd packages/hub
pnpm dev
```

**Terminal 2 - Web Service:**

```bash
cd packages/web
pnpm dev
```

## Test Steps

### 1. Login to the Application

- Navigate to: http://localhost:3000
- Click "Login"
- Enter credentials:
  - Email: `<EMAIL>`
  - Password: `Flaremmk123!`

### 2. Navigate to Import Page

- After login, go to: http://localhost:3000/import
- Or click "Import" from the navigation menu

### 3. Upload the PDF File

- Click the file upload area
- Select: `Updated 15-Day European Travel Itinerary_ London, Madrid, Lisbon and Porto.pdf`
- File is located at: `/Users/<USER>/TravelViz/Travelviz/`

### 4. Start Import Process

- Click "Parse Conversation" button
- Watch the real-time progress animation

### 5. Monitor Parsing Progress

You should see these steps with progress animations:

- ✓ Uploading conversation
- ✓ Extracting trip details
- ✓ Finding dates and times
- ✓ Identifying locations
- ✓ Organizing activities
- ✓ Creating your itinerary

### 6. Review Parsed Trip

- View the timeline of activities
- Check the map with pinned locations
- Verify trip details match the PDF content

### 7. Create Trip

- Click "Create Trip" button
- You'll be redirected to the trip planning page
- The URL will be: http://localhost:3000/plan/[trip-id]

## Expected Results

From the PDF filename, the system should parse:

- **Title**: 15-Day European Travel Itinerary
- **Duration**: 15 days
- **Destinations**: London, Madrid, Lisbon, Porto
- **Activities**: Multiple activities per city with dates/times

## Troubleshooting

If the import fails:

1. Check browser console for errors (F12)
2. Check hub service logs in terminal
3. Verify OpenRouter API key is working
4. Ensure Supabase connection is active

## API Endpoints Being Used

1. **Start Import**: POST http://localhost:3001/api/v1/import/parse
2. **Progress Updates**: GET http://localhost:3001/api/v1/import/{importId}/progress (SSE)
3. **Create Trip**: POST http://localhost:3001/api/v1/import/{importId}/create-trip

## Success Indicators

- No errors in console
- Progress completes to 100%
- Trip is created in database
- Redirect to trip planning page
- Activities are properly organized by date/time
