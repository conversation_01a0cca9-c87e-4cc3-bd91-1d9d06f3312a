import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import axios from 'axios';
import { travelpayoutsService } from './travelpayouts.service';
import { cacheService } from './cache.service';
import { logger } from '../utils/logger';

// Mock dependencies
vi.mock('axios');
vi.mock('../cache.service', () => ({
  cacheService: {
    getOrSet: vi.fn(),
  },
}));
vi.mock('../../utils/logger', () => ({
  logger: {
    warn: vi.fn(),
    info: vi.fn(),
    error: vi.fn(),
  },
}));

describe.skip('TravelpayoutsService', () => {
  const originalEnv = { ...process.env };

  beforeEach(() => {
    vi.clearAllMocks();
    process.env.TRAVELPAYOUTS_API_KEY = 'test-api-key';
    process.env.TRAVELPAYOUTS_MARKER = 'test-marker';
  });

  afterEach(() => {
    process.env = originalEnv;
  });

  describe('constructor', () => {
    it('should initialize with API credentials', () => {
      // Service is already initialized with test credentials
      expect(logger.warn).not.toHaveBeenCalled();
    });

    it('should warn if API key is missing', () => {
      delete process.env.TRAVELPAYOUTS_API_KEY;
      
      // Need to re-import to trigger constructor
      vi.resetModules();
      require('../travelpayouts.service');
      
      expect(logger.warn).toHaveBeenCalledWith('Travelpayouts API key not configured');
    });
  });

  describe('searchFlights', () => {
    const mockFlightParams = {
      origin: 'NYC',
      destination: 'LAX',
      departDate: '2024-06-01',
      returnDate: '2024-06-08',
      currency: 'USD',
      adults: 2,
    };

    const mockFlightResponse = {
      data: {
        data: [
          {
            price: 600,
            airline: 'AA',
            flight_number: 'AA123',
            departure_at: '2024-06-01T10:00:00',
            arrival_at: '2024-06-01T13:00:00',
            duration: 180,
            stops: 0,
            link: 'https://aviasales.com/flight123',
          },
          {
            price: 450,
            airline: 'UA',
            flight_number: 'UA456',
            departure_at: '2024-06-01T14:00:00',
            arrival_at: '2024-06-01T17:00:00',
            duration: 180,
            stops: 0,
            link: 'https://aviasales.com/flight456',
          },
        ],
      },
    };

    it('should search flights and return filtered results', async () => {
      vi.mocked(cacheService.getOrSet).mockImplementation(async (key, factory) => {
        return factory();
      });

      vi.mocked(axios.get).mockResolvedValue(mockFlightResponse);

      const result = await travelpayoutsService.searchFlights(mockFlightParams);

      expect(result).toHaveLength(2);
      expect(result[0]).toMatchObject({
        price: 600,
        airline: 'AA',
        flightNumber: 'AA123',
        departure: '2024-06-01T10:00:00',
        arrival: '2024-06-01T13:00:00',
        duration: 180,
        stops: 0,
        bookingUrl: 'https://aviasales.com/flight123',
        affiliateUrl: expect.stringContaining('tp.media'),
      });

      expect(axios.get).toHaveBeenCalledWith(
        'https://api.travelpayouts.com/v1/flight_search',
        expect.objectContaining({
          headers: { 'X-Access-Token': 'test-api-key' },
          params: expect.objectContaining({
            origin: 'NYC',
            destination: 'LAX',
            depart_date: '2024-06-01',
            return_date: '2024-06-08',
            currency: 'USD',
            adults: 2,
            marker: 'test-marker',
          }),
        })
      );
    });

    it('should filter out flights below minimum commission', async () => {
      vi.mocked(cacheService.getOrSet).mockImplementation(async (key, factory) => {
        return factory();
      });

      const lowPriceResponse = {
        data: {
          data: [
            {
              price: 100, // $100 * 0.02 = $2 commission (below $10 minimum)
              airline: 'AA',
              flight_number: 'AA789',
              departure_at: '2024-06-01T10:00:00',
              arrival_at: '2024-06-01T13:00:00',
              duration: 180,
              stops: 0,
              link: 'https://aviasales.com/cheap',
            },
            {
              price: 600, // $600 * 0.02 = $12 commission (above minimum)
              airline: 'UA',
              flight_number: 'UA999',
              departure_at: '2024-06-01T14:00:00',
              arrival_at: '2024-06-01T17:00:00',
              duration: 180,
              stops: 0,
              link: 'https://aviasales.com/expensive',
            },
          ],
        },
      };

      vi.mocked(axios.get).mockResolvedValue(lowPriceResponse);

      const result = await travelpayoutsService.searchFlights(mockFlightParams);

      expect(result).toHaveLength(1);
      expect(result[0].price).toBe(600);
    });

    it('should use cache for repeated searches', async () => {
      const cachedData = [
        {
          price: 500,
          airline: 'DL',
          flightNumber: 'DL111',
          departure: '2024-06-01T09:00:00',
          arrival: '2024-06-01T12:00:00',
          duration: 180,
          stops: 0,
          bookingUrl: 'https://aviasales.com/cached',
          affiliateUrl: 'https://tp.media/cached',
        },
      ];

      vi.mocked(cacheService.getOrSet).mockResolvedValue(cachedData);

      const result = await travelpayoutsService.searchFlights(mockFlightParams);

      expect(result).toEqual(cachedData);
      expect(axios.get).not.toHaveBeenCalled();
    });

    it('should handle API errors', async () => {
      delete process.env.TRAVELPAYOUTS_API_KEY;
      const service = new (require('../travelpayouts.service').TravelpayoutsService)();

      await expect(service.searchFlights(mockFlightParams))
        .rejects.toThrow('Travelpayouts API key not configured');
    });

    it('should handle axios errors', async () => {
      vi.mocked(cacheService.getOrSet).mockImplementation(async (key, factory) => {
        return factory();
      });

      const axiosError = {
        isAxiosError: true,
        response: {
          data: { message: 'Invalid API key' },
        },
      };

      vi.mocked(axios.get).mockRejectedValue(axiosError);
      vi.mocked(axios.isAxiosError).mockReturnValue(true);

      await expect(travelpayoutsService.searchFlights(mockFlightParams))
        .rejects.toThrow('Flight search failed: Invalid API key');

      expect(logger.error).toHaveBeenCalled();
    });
  });

  describe('searchHotels', () => {
    const mockHotelParams = {
      location: 'Paris',
      checkIn: '2024-07-01',
      checkOut: '2024-07-05',
      currency: 'EUR',
      adults: 2,
    };

    const mockHotelListResponse = {
      data: {
        results: [
          {
            id: 'hotel1',
            name: 'Hotel Paris',
            stars: 4,
            location: { lat: 48.8566, lon: 2.3522 },
            amenities: ['wifi', 'pool'],
            images: ['image1.jpg'],
          },
          {
            id: 'hotel2',
            name: 'Grand Hotel',
            stars: 5,
            location: { lat: 48.8584, lon: 2.2945 },
            amenities: ['spa', 'restaurant'],
            images: ['image2.jpg'],
          },
        ],
      },
    };

    const mockPricesResponse = {
      data: {
        hotel1: {
          price: 300,
          link: 'https://booking.com/hotel1',
        },
        hotel2: {
          price: 50, // Below minimum commission
          link: 'https://booking.com/hotel2',
        },
      },
    };

    it('should search hotels and filter by minimum commission', async () => {
      vi.mocked(axios.get)
        .mockResolvedValueOnce(mockHotelListResponse)
        .mockResolvedValueOnce(mockPricesResponse);

      const result = await travelpayoutsService.searchHotels(mockHotelParams);

      expect(result).toHaveLength(1);
      expect(result[0]).toMatchObject({
        hotelId: 'hotel1',
        name: 'Hotel Paris',
        stars: 4,
        price: 300,
        currency: 'EUR',
        bookingUrl: 'https://booking.com/hotel1',
        affiliateUrl: expect.stringContaining('tp.media'),
      });

      expect(axios.get).toHaveBeenCalledTimes(2);
    });

    it('should handle hotels without price data', async () => {
      const pricesWithMissing = {
        data: {
          hotel1: {
            price: 400,
            link: 'https://booking.com/hotel1',
          },
          // hotel2 has no price data
        },
      };

      vi.mocked(axios.get)
        .mockResolvedValueOnce(mockHotelListResponse)
        .mockResolvedValueOnce(pricesWithMissing);

      const result = await travelpayoutsService.searchHotels(mockHotelParams);

      expect(result).toHaveLength(1);
      expect(result[0].hotelId).toBe('hotel1');
    });

    it('should handle API errors gracefully', async () => {
      const axiosError = {
        isAxiosError: true,
        response: {
          data: { message: 'Location not found' },
        },
      };

      vi.mocked(axios.get).mockRejectedValue(axiosError);
      vi.mocked(axios.isAxiosError).mockReturnValue(true);

      await expect(travelpayoutsService.searchHotels(mockHotelParams))
        .rejects.toThrow('Hotel search failed: Location not found');
    });
  });

  describe('generateAffiliateUrl', () => {
    it('should generate flight affiliate URL', () => {
      const originalUrl = 'https://aviasales.com/flight123';
      const result = travelpayoutsService.generateAffiliateUrl(originalUrl, 'flight');

      expect(result).toContain('https://tp.media/r');
      expect(result).toContain('marker=test-marker');
      expect(result).toContain('p=13804'); // Flight program ID
      expect(result).toContain(encodeURIComponent(originalUrl));
    });

    it('should generate hotel affiliate URL', () => {
      const originalUrl = 'https://booking.com/hotel456';
      const result = travelpayoutsService.generateAffiliateUrl(originalUrl, 'hotel');

      expect(result).toContain('https://tp.media/r');
      expect(result).toContain('marker=test-marker');
      expect(result).toContain('p=13805'); // Hotel program ID
      expect(result).toContain(encodeURIComponent(originalUrl));
    });

    it('should return original URL if no marker configured', () => {
      delete process.env.TRAVELPAYOUTS_MARKER;
      const service = new (require('../travelpayouts.service').TravelpayoutsService)();
      
      const originalUrl = 'https://example.com';
      const result = service.generateAffiliateUrl(originalUrl, 'flight');

      expect(result).toBe(originalUrl);
    });
  });

  describe('getAirports', () => {
    const mockAirportsResponse = {
      data: {
        JFK: {
          name: 'John F Kennedy International',
          code: 'JFK',
          city: 'New York',
        },
        LGA: {
          name: 'LaGuardia',
          code: 'LGA',
          city: 'New York',
        },
        EWR: {
          name: 'Newark Liberty International',
          code: 'EWR',
          city: 'Newark',
        },
      },
    };

    it('should return filtered airports by query', async () => {
      vi.mocked(axios.get).mockResolvedValue(mockAirportsResponse);

      const result = await travelpayoutsService.getAirports('new york');

      expect(result).toHaveLength(2);
      expect(result[0].code).toBe('JFK');
      expect(result[1].code).toBe('LGA');
    });

    it('should filter by airport code', async () => {
      vi.mocked(axios.get).mockResolvedValue(mockAirportsResponse);

      const result = await travelpayoutsService.getAirports('jfk');

      expect(result).toHaveLength(1);
      expect(result[0].code).toBe('JFK');
    });

    it('should limit results to 10', async () => {
      const manyAirports: any = {};
      for (let i = 0; i < 20; i++) {
        manyAirports[`AIR${i}`] = {
          name: `Airport ${i}`,
          code: `AIR${i}`,
          city: 'Test City',
        };
      }

      vi.mocked(axios.get).mockResolvedValue({ data: manyAirports });

      const result = await travelpayoutsService.getAirports('test');

      expect(result).toHaveLength(10);
    });

    it('should handle errors and return empty array', async () => {
      vi.mocked(axios.get).mockRejectedValue(new Error('Network error'));

      const result = await travelpayoutsService.getAirports('test');

      expect(result).toEqual([]);
      expect(logger.error).toHaveBeenCalled();
    });
  });

  describe('getCityName', () => {
    it('should return city name for IATA code', async () => {
      const mockCitiesResponse = {
        data: {
          NYC: { name: 'New York' },
          LAX: { name: 'Los Angeles' },
        },
      };

      vi.mocked(axios.get).mockResolvedValue(mockCitiesResponse);

      const result = await travelpayoutsService.getCityName('NYC');

      expect(result).toBe('New York');
    });

    it('should return IATA code if city not found', async () => {
      vi.mocked(axios.get).mockResolvedValue({ data: {} });

      const result = await travelpayoutsService.getCityName('XXX');

      expect(result).toBe('XXX');
    });

    it('should handle errors and return IATA code', async () => {
      vi.mocked(axios.get).mockRejectedValue(new Error('API error'));

      const result = await travelpayoutsService.getCityName('NYC');

      expect(result).toBe('NYC');
      expect(logger.error).toHaveBeenCalled();
    });
  });

  describe('commission calculations', () => {
    it('should correctly calculate flight commissions', async () => {
      vi.mocked(cacheService.getOrSet).mockImplementation(async (key, factory) => {
        return factory();
      });

      const response = {
        data: {
          data: [
            { price: 500, airline: 'AA', flight_number: 'AA1', departure_at: '2024-01-01', arrival_at: '2024-01-01', duration: 120, stops: 0, link: 'https://link1.com' }, // $10 commission
            { price: 499, airline: 'BB', flight_number: 'BB1', departure_at: '2024-01-01', arrival_at: '2024-01-01', duration: 120, stops: 0, link: 'https://link2.com' }, // $9.98 commission (rejected)
          ],
        },
      };

      vi.mocked(axios.get).mockResolvedValue(response);

      const result = await travelpayoutsService.searchFlights({
        origin: 'NYC',
        destination: 'LAX',
        departDate: '2024-01-01',
      });

      expect(result).toHaveLength(1);
      expect(result[0].price).toBe(500);
    });

    it('should correctly calculate hotel commissions', async () => {
      const hotelList = {
        data: {
          results: [
            { id: 'h1', name: 'Hotel 1', stars: 4, location: { lat: 0, lon: 0 } },
            { id: 'h2', name: 'Hotel 2', stars: 3, location: { lat: 0, lon: 0 } },
          ],
        },
      };

      const prices = {
        data: {
          h1: { price: 250, link: 'https://hotel1.com' }, // $10 commission
          h2: { price: 249, link: 'https://hotel2.com' }, // $9.96 commission (rejected)
        },
      };

      vi.mocked(axios.get)
        .mockResolvedValueOnce(hotelList)
        .mockResolvedValueOnce(prices);

      const result = await travelpayoutsService.searchHotels({
        location: 'Test',
        checkIn: '2024-01-01',
        checkOut: '2024-01-02',
      });

      expect(result).toHaveLength(1);
      expect(result[0].price).toBe(250);
    });
  });
});