import { Redis } from '@upstash/redis';

// Base Redis interface with common methods
interface RedisBase {
  get: (key: string) => Promise<string | null>;
  set: (key: string, value: string) => Promise<string>;
  del: (key: string) => Promise<number>;
  expire: (key: string, seconds: number) => Promise<number>;
  exists: (key: string) => Promise<number>;
  incr: (key: string) => Promise<number>;
  decr: (key: string) => Promise<number>;
  ttl: (key: string) => Promise<number>;
  mget: (keys: string[]) => Promise<(string | null)[]>;
  mset: (data: Record<string, string>) => Promise<string>;
  keys: (pattern: string) => Promise<string[]>;
  flushdb: () => Promise<string>;
  publish: (channel: string, message: string) => Promise<number>;
}

// Extended interface for pub/sub support (mock only)
interface RedisPubSub extends RedisBase {
  subscribe: (channel: string) => Promise<void>;
  on: (event: string, callback: (channel: string, message: string) => void) => void;
  unsubscribe: (channel: string) => Promise<void>;
}

// Type guard to check if Redis instance supports pub/sub
export function supportsPubSub(redis: RedisBase | RedisPubSub): redis is RedisPubSub {
  return 'subscribe' in redis && 'on' in redis && 'unsubscribe' in redis;
}


// Mock Redis client for testing (with pub/sub support)
const mockRedisClient: RedisPubSub = {
  get: async () => null,
  set: async () => 'OK',
  del: async () => 1,
  expire: async () => 1,
  exists: async () => 0,
  incr: async () => 1,
  decr: async () => 0,
  ttl: async () => -1,
  mget: async () => [],
  mset: async () => 'OK',
  keys: async () => [],
  flushdb: async () => 'OK',
  publish: async () => 1,
  subscribe: async () => {},
  on: () => {},
  unsubscribe: async () => {}
};

// Create Redis client
let redisInstance: RedisBase | RedisPubSub;

// Support both naming conventions for backward compatibility
const redisUrl = process.env.UPSTASH_REDIS_URL || process.env.UPSTASH_REDIS_REST_URL;
const redisToken = process.env.UPSTASH_REDIS_TOKEN || process.env.UPSTASH_REDIS_REST_TOKEN;

if (redisUrl && redisToken) {
  // Upstash Redis doesn't support pub/sub, only basic operations
  const upstashClient = new Redis({
    url: redisUrl,
    token: redisToken
  });
  
  // Create a wrapper that implements RedisBase interface
  redisInstance = {
    get: async (key: string) => upstashClient.get(key),
    set: async (key: string, value: string) => {
      const result = await upstashClient.set(key, value);
      return result || 'OK';
    },
    del: async (key: string) => upstashClient.del(key),
    expire: async (key: string, seconds: number) => upstashClient.expire(key, seconds),
    exists: async (key: string) => upstashClient.exists(key),
    incr: async (key: string) => upstashClient.incr(key),
    decr: async (key: string) => upstashClient.decr(key),
    ttl: async (key: string) => upstashClient.ttl(key),
    mget: async (keys: string[]) => upstashClient.mget(...keys),
    mset: async (data: Record<string, string>) => {
      const pairs: [string, string][] = Object.entries(data);
      for (const [key, value] of pairs) {
        await upstashClient.set(key, value);
      }
      return 'OK';
    },
    keys: async (pattern: string) => upstashClient.keys(pattern),
    flushdb: async () => {
      await upstashClient.flushdb();
      return 'OK';
    },
    publish: async (channel: string, message: string) => upstashClient.publish(channel, message)
  };
} else {
  redisInstance = mockRedisClient;
}

// Export redis instance
export const redis = redisInstance;

// Export a function to get Redis client (for backward compatibility)
export function getRedisClient() {
  return Promise.resolve(redis);
}