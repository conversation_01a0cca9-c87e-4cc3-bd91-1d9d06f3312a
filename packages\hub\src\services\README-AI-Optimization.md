# AI Model Optimization Implementation

This document describes the implementation of the AI Model Optimization system for TravelViz, which provides intelligent model selection, comprehensive usage tracking, and cost optimization.

## Overview

The AI Model Optimization system implements:
- **Intelligent Model Selection**: Prioritizes free tier models and selects optimal models based on content complexity
- **Usage Tracking**: Tracks daily API usage with Pacific Time reset
- **Cost Optimization**: Minimizes operational costs while maintaining service quality
- **Rate Limit Handling**: Graceful fallback mechanisms with exponential backoff
- **Real-time Monitoring**: Usage statistics and threshold alerts

## Architecture

### Model Priority Chain
Per design.md requirements, the system follows this priority order:

1. **Moonshot AI (kimi-k2:free)** - Primary free tier model (1000 requests/day)
2. **Google Gemini Models** - Based on RPM/TPM/RPD limits:
   - Gemini 2.0 Flash (RPM: 15, TPM: 1M, RPD: 200)
   - Gemini 2.5 Flash (RPM: 10, TPM: 250k, RPD: 250)
   - Gemini 2.5 Pro (RPM: 5, TPM: 250k, RPD: 100)
3. **OpenAI GPT-4.1 Nano** - Last resort fallback (paid)

### Components

#### 1. Usage Tracking Service (`usage-tracking.service.ts`)
- Tracks daily API usage across all models
- Implements Pacific Time reset at midnight
- Provides usage statistics and availability checks
- Redis caching for performance

**Key Methods:**
- `trackRequest(modelId, inputTokens, outputTokens)` - Record API usage
- `getCurrentUsage(modelId)` - Get current usage for a model
- `getAllUsage()` - Get usage statistics for all models
- `isModelAvailable(modelId)` - Check if model is available (not rate limited)

#### 2. Model Selector Service (`model-selector.service.ts`)
- Intelligent model selection based on content complexity and usage
- Token estimation for optimal model routing
- Fallback chain generation

**Key Methods:**
- `selectModel(content, requirements?)` - Select optimal model
- `estimateTokens(content)` - Estimate token requirements
- `getFallbackChain(primaryModel, complexity)` - Get fallback models

#### 3. Prompt Manager (`prompt-manager.service.ts`)
- Model-specific optimized system prompts
- Format instructions for each model
- Prompt validation and A/B testing support

**Key Methods:**
- `getSystemPrompt(modelId)` - Get optimized prompt for model
- `getFormatInstructions(modelId)` - Get formatting instructions
- `updatePrompt(modelId, prompt)` - Update prompt with validation

#### 4. Enhanced AI Router Service (`enhanced-ai-router.service.ts`)
- Orchestrates model selection, usage tracking, and request execution
- Handles rate limits and fallbacks
- Provides usage statistics

**Key Methods:**
- `parseContent(content, source, userId)` - Main parsing with intelligent routing
- `executeWithModel(modelId, prompt, content)` - Execute with specific model
- `handleRateLimit(modelId, error)` - Handle rate limits and fallbacks

## Database Schema

### New Tables

#### `ai_model_usage`
Tracks daily usage per model:
```sql
CREATE TABLE ai_model_usage (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    model_id TEXT NOT NULL,
    date DATE NOT NULL DEFAULT CURRENT_DATE,
    request_count INTEGER DEFAULT 0,
    input_tokens BIGINT DEFAULT 0,
    output_tokens BIGINT DEFAULT 0,
    total_cost DECIMAL(10,6) DEFAULT 0,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    UNIQUE(model_id, date)
);
```

#### `ai_request_logs`
Detailed request tracking:
```sql
CREATE TABLE ai_request_logs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES auth.users(id),
    session_id UUID,
    model_id TEXT NOT NULL,
    input_tokens INTEGER,
    output_tokens INTEGER,
    cost DECIMAL(8,6),
    duration_ms INTEGER,
    success BOOLEAN DEFAULT true,
    error_message TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW()
);
```

#### `ai_model_configs`
Model configuration and limits:
```sql
CREATE TABLE ai_model_configs (
    id TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    provider TEXT NOT NULL,
    daily_request_limit INTEGER,
    rpm_limit INTEGER,
    tpm_limit BIGINT,
    rpd_limit INTEGER,
    cost_per_1k_tokens DECIMAL(8,6),
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);
```

### Enhanced Existing Tables

#### `ai_import_logs` (Enhanced)
Added usage tracking columns:
- `model_used TEXT` - Model used for parsing
- `input_tokens INTEGER` - Input tokens consumed
- `output_tokens INTEGER` - Output tokens generated
- `processing_cost DECIMAL(8,6)` - Cost of processing
- `fallback_attempts INTEGER` - Number of fallback attempts

### Database Functions

#### `reset_daily_ai_usage()`
Resets daily usage counters at midnight Pacific Time:
```sql
CREATE OR REPLACE FUNCTION reset_daily_ai_usage()
RETURNS void AS $$
BEGIN
    UPDATE ai_model_usage 
    SET request_count = 0, 
        input_tokens = 0, 
        output_tokens = 0,
        total_cost = 0,
        updated_at = NOW()
    WHERE date = CURRENT_DATE;
    
    PERFORM pg_notify('ai_usage_reset', 'daily_reset_complete');
END;
$$ LANGUAGE plpgsql;
```

#### `get_model_usage(model_id_param TEXT)`
Returns current usage for a model.

#### `is_model_available(model_id_param TEXT)`
Checks if a model is available (not rate limited).

### pg_cron Setup
Daily reset scheduled at midnight Pacific Time:
```sql
SELECT cron.schedule(
    'daily-ai-usage-reset',
    '0 8 * * *', -- 8:00 UTC = midnight PST
    'SELECT reset_daily_ai_usage();'
);
```

## Model-Specific Optimizations

### Moonshot AI (kimi-k2:free)
- **System Prompt**: Optimized for structured JSON output
- **Temperature**: 0.2 for consistency
- **Max Tokens**: 66,000 (high capacity)
- **Priority**: First choice if under 1000 daily requests

### Google Gemini Models
- **2.5 Pro**: Complex itineraries, highest accuracy
- **2.5 Flash**: Balanced performance for medium complexity
- **2.0 Flash**: High-volume processing, simple to medium complexity
- **Rate Limits**: Tracked separately (RPM, TPM, RPD)

### OpenAI GPT-4.1 Nano
- **Usage**: Last resort fallback only
- **System Prompt**: Minimal token usage optimization
- **Cost**: $0.00015 per 1K tokens

## Usage Examples

### Basic Model Selection
```typescript
import { modelSelectorService } from './services/model-selector.service';

const content = "Plan a 5-day trip to Paris...";
const selection = await modelSelectorService.selectModel(content);
console.log(`Selected: ${selection.modelId} (${selection.reason})`);
```

### Usage Tracking
```typescript
import { usageTrackingService } from './services/usage-tracking.service';

// Track a request
await usageTrackingService.trackRequest('moonshotai/kimi-k2:free', 150, 300);

// Check current usage
const usage = await usageTrackingService.getCurrentUsage('moonshotai/kimi-k2:free');
console.log(`Used: ${usage.requestCount}/1000 requests`);
```

### Enhanced AI Parsing
```typescript
import { enhancedAIRouterService } from './services/enhanced-ai-router.service';

const parsedTrip = await enhancedAIRouterService.parseContent(
  content, 
  'chatgpt', 
  userId
);
```

## Installation and Setup

### 1. Apply Database Migration
```bash
cd packages/hub
npm run script:apply-ai-migration
```

### 2. Run Tests
```bash
npm run script:test-ai-optimization
```

### 3. Environment Variables
Ensure these are configured:
```env
GOOGLE_GEMINI_API_KEY=your_gemini_key
OPENROUTER_API_KEY=your_openrouter_key
REDIS_URL=your_redis_url
```

## Monitoring and Alerts

### Usage Thresholds
The system logs warnings at:
- 80% of daily limits
- 90% of daily limits

### Model Health Monitoring
- Success rates tracked per model
- Response times monitored
- Circuit breaker pattern for failing models

### Cost Tracking
- Daily cost reports
- Budget alerts
- Cost optimization recommendations

## Performance Considerations

### Caching Strategy
- **Usage Counters**: Redis with 5-minute TTL
- **Model Availability**: Cached for rate limit status
- **Token Estimates**: Cached for similar content patterns

### Database Optimization
- Indexes on model_id, date, user_id
- Daily reset via pg_cron
- Row Level Security (RLS) policies

## Error Handling

### Rate Limit Management
1. **Detection**: Monitor HTTP 429 responses
2. **Fallback**: Automatic switch to next available model
3. **Backoff**: Exponential backoff with jitter
4. **Communication**: Clear error messages with wait times

### Model Failure Handling
- Circuit breaker pattern
- Automatic fallback chains
- Comprehensive error logging
- User-friendly error messages

## Testing

The implementation includes comprehensive tests:
- Database schema validation
- Service functionality tests
- Model selection logic tests
- Integration tests
- Load tests

Run tests with:
```bash
npm run script:test-ai-optimization
```

## Requirements Compliance

This implementation satisfies all requirements from requirements.md:

- **Requirement 1**: ✅ Daily usage tracking with Pacific Time reset
- **Requirement 2**: ✅ Free tier model prioritization
- **Requirement 3**: ✅ Token estimation and model routing
- **Requirement 4**: ✅ Model-specific optimized prompts
- **Requirement 5**: ✅ Rate limit handling with fallbacks
- **Requirement 6**: ✅ Real-time usage visibility
- **Requirement 7**: ✅ Response quality consistency

## Future Enhancements

- A/B testing for prompts
- Machine learning for usage prediction
- Advanced cost optimization algorithms
- Real-time dashboard for monitoring
- Automated model performance tuning