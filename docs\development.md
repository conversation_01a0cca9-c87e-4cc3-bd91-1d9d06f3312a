# Development Guide

This guide covers the essential development workflow, patterns, and best practices for contributing to TravelViz. For initial setup instructions, see [setup.md](./setup.md).

## Table of Contents

1. [Development Workflow](#development-workflow)
2. [Code Organization](#code-organization)
3. [Development Best Practices](#development-best-practices)
4. [Common Development Tasks](#common-development-tasks)
5. [Debugging Tips](#debugging-tips)
6. [Git Workflow](#git-workflow)
7. [Testing During Development](#testing-during-development)

## Development Workflow

### Starting Development

1. **Ensure your environment is set up** (see [setup.md](./setup.md) for details)

2. **Start the development servers:**

   ```bash
   # From the root directory
   pnpm dev
   ```

   This starts:
   - Hub API: http://localhost:3001
   - Web frontend: http://localhost:3000

3. **Verify everything is running:**
   - Open http://localhost:3000 in your browser
   - Check the console for any errors
   - The API health check: http://localhost:3001/health

### Hot Reloading

Both the web frontend and API hub support hot reloading:

- **Frontend (Next.js)**: Automatically refreshes on file changes
- **Backend (Express)**: Uses nodemon to restart on changes
- **Shared package**: Changes trigger rebuilds in dependent packages

### Development Tools

#### Recommended VS Code Extensions

- **ESLint**: For linting
- **Prettier**: For formatting
- **TypeScript Vue Plugin**: For better TS support
- **Tailwind CSS IntelliSense**: For Tailwind autocomplete
- **Thunder Client**: For API testing

#### Browser Extensions

- **React Developer Tools**: For component inspection
- **Redux DevTools**: Works with Zustand for state debugging

## Code Organization

### Monorepo Structure

TravelViz uses a pnpm workspace monorepo with three main packages:

```
packages/
├── hub/          # Express.js API (port 3001)
├── web/          # Next.js frontend (port 3000)
└── shared/       # Shared types, utils, schemas
```

### Package Responsibilities

#### @travelviz/hub

The central API that handles ALL business logic:

- Database operations (via Supabase)
- External API integrations (AI, maps, etc.)
- Authentication and authorization
- Data validation and transformation

```
packages/hub/
├── src/
│   ├── routes/       # API endpoints
│   ├── services/     # Business logic
│   ├── middleware/   # Express middleware
│   ├── utils/        # Helper functions
│   └── types/        # Hub-specific types
```

#### @travelviz/web

The Next.js 14 frontend with App Router:

- Server and client components
- UI/UX implementation
- State management (Zustand)
- Form handling

```
packages/web/
├── src/
│   ├── app/          # App Router pages
│   ├── components/   # React components
│   ├── lib/          # Utilities and helpers
│   ├── store/        # Zustand stores
│   └── styles/       # Global styles
```

#### @travelviz/shared

Shared code between packages:

- TypeScript types and interfaces
- Zod validation schemas
- Common utilities
- API response helpers

```
packages/shared/
├── src/
│   ├── types/        # Shared TypeScript types
│   ├── schemas/      # Zod validation schemas
│   └── utils/        # Common utilities
```

### Where to Add New Features

1. **New API endpoint**: `packages/hub/src/routes/`
2. **New UI page**: `packages/web/src/app/`
3. **New component**: `packages/web/src/components/`
4. **New shared type**: `packages/shared/src/types/`
5. **New validation**: `packages/shared/src/schemas/`

## Development Best Practices

### TypeScript Conventions

1. **Always use strict typing:**

   ```typescript
   // ❌ Avoid
   const processData = (data: any) => { ... }

   // ✅ Preferred
   const processData = (data: TripData) => { ... }
   ```

2. **Import types from shared:**

   ```typescript
   import { Trip, Activity } from '@travelviz/shared/types';
   ```

3. **Use type inference where possible:**
   ```typescript
   // Let TypeScript infer the return type
   const calculateTotal = (prices: number[]) => prices.reduce((sum, price) => sum + price, 0);
   ```

### Component Structure (Atomic Design)

Follow the atomic design pattern in the web package:

```
components/
├── atoms/        # Basic building blocks (Button, Input)
├── molecules/    # Simple combinations (SearchBar, Card)
├── organisms/    # Complex components (Header, TripList)
├── templates/    # Page layouts
└── pages/        # Full page components
```

Example component structure:

```typescript
// components/molecules/TripCard.tsx
import { FC } from 'react';
import { Trip } from '@travelviz/shared/types';
import { Card, CardHeader, CardContent } from '@/components/ui/card';

interface TripCardProps {
  trip: Trip;
  onSelect?: (trip: Trip) => void;
}

export const TripCard: FC<TripCardProps> = ({ trip, onSelect }) => {
  return (
    <Card onClick={() => onSelect?.(trip)}>
      <CardHeader>{trip.title}</CardHeader>
      <CardContent>{trip.description}</CardContent>
    </Card>
  );
};
```

### State Management (Zustand)

Create focused stores for different features:

```typescript
// store/tripStore.ts
import { create } from 'zustand';
import { Trip } from '@travelviz/shared/types';

interface TripStore {
  trips: Trip[];
  selectedTrip: Trip | null;
  setTrips: (trips: Trip[]) => void;
  selectTrip: (trip: Trip | null) => void;
}

export const useTripStore = create<TripStore>(set => ({
  trips: [],
  selectedTrip: null,
  setTrips: trips => set({ trips }),
  selectTrip: trip => set({ selectedTrip: trip }),
}));
```

### API Communication Patterns

Always communicate through the hub API:

```typescript
// lib/api/trips.ts
import { Trip } from '@travelviz/shared/types';

const API_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001';

export const tripApi = {
  async getAll(): Promise<Trip[]> {
    const response = await fetch(`${API_URL}/api/trips`);
    if (!response.ok) throw new Error('Failed to fetch trips');
    return response.json();
  },

  async create(trip: Partial<Trip>): Promise<Trip> {
    const response = await fetch(`${API_URL}/api/trips`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(trip),
    });
    if (!response.ok) throw new Error('Failed to create trip');
    return response.json();
  },
};
```

## Common Development Tasks

### Adding a New API Endpoint

1. **Define the route in hub:**

   ```typescript
   // packages/hub/src/routes/activities.ts
   import { Router } from 'express';
   import { activitySchema } from '@travelviz/shared/schemas';
   import { createSuccessResponse, createErrorResponse } from '@travelviz/shared/utils';

   const router = Router();

   router.post('/', async (req, res) => {
     try {
       const validated = activitySchema.parse(req.body);
       const activity = await activityService.create(validated);
       res.json(createSuccessResponse(activity));
     } catch (error) {
       res.status(400).json(createErrorResponse(error));
     }
   });

   export default router;
   ```

2. **Add to main router:**

   ```typescript
   // packages/hub/src/routes/index.ts
   import activityRoutes from './activities';

   router.use('/activities', activityRoutes);
   ```

### Creating a New UI Component

1. **Create the component:**

   ```typescript
   // packages/web/src/components/molecules/ActivityCard.tsx
   import { FC } from 'react';
   import { Activity } from '@travelviz/shared/types';

   export const ActivityCard: FC<{ activity: Activity }> = ({ activity }) => {
     return (
       <div className="p-4 border rounded-lg">
         <h3 className="font-bold">{activity.name}</h3>
         <p className="text-gray-600">{activity.duration} minutes</p>
       </div>
     );
   };
   ```

2. **Use in a page:**

   ```typescript
   // packages/web/src/app/activities/page.tsx
   import { ActivityCard } from '@/components/molecules/ActivityCard';

   export default async function ActivitiesPage() {
     const activities = await getActivities();

     return (
       <div className="grid gap-4">
         {activities.map((activity) => (
           <ActivityCard key={activity.id} activity={activity} />
         ))}
       </div>
     );
   }
   ```

### Adding Shared Types

1. **Define the type:**

   ```typescript
   // packages/shared/src/types/activity.ts
   export interface Activity {
     id: string;
     tripId: string;
     name: string;
     duration: number;
     location: {
       lat: number;
       lng: number;
     };
   }
   ```

2. **Export from index:**

   ```typescript
   // packages/shared/src/types/index.ts
   export * from './activity';
   ```

3. **Build shared package:**
   ```bash
   cd packages/shared
   pnpm build
   ```

### Writing Tests

1. **Create a test file next to the component:**

   ```typescript
   // components/molecules/TripCard.test.tsx
   import { render, screen } from '@testing-library/react';
   import { TripCard } from './TripCard';

   describe('TripCard', () => {
     it('renders trip title', () => {
       const trip = { id: '1', title: 'Test Trip' };
       render(<TripCard trip={trip} />);
       expect(screen.getByText('Test Trip')).toBeInTheDocument();
     });
   });
   ```

2. **Run tests:**
   ```bash
   pnpm test
   # or watch mode
   pnpm test:watch
   ```

## Debugging Tips

### Chrome DevTools with Next.js

1. **Enable source maps in development** (already configured)
2. **Use the React DevTools extension** to inspect component state
3. **Network tab** to debug API calls
4. **Console for server-side logs** in the terminal

### Debugging the Express API

1. **Use the VS Code debugger:**

   ```json
   // .vscode/launch.json
   {
     "type": "node",
     "request": "attach",
     "name": "Attach to Hub",
     "port": 9229,
     "restart": true
   }
   ```

2. **Add debug script to package.json:**

   ```json
   "scripts": {
     "dev:debug": "NODE_OPTIONS='--inspect' pnpm dev"
   }
   ```

3. **Use console.log or debugger statements:**
   ```typescript
   router.post('/', async (req, res) => {
     console.log('Request body:', req.body);
     debugger; // Breakpoint here
     // ...
   });
   ```

### Common Issues and Solutions

1. **Port already in use:**

   ```bash
   # Kill process on port 3000 or 3001
   lsof -ti:3000 | xargs kill -9
   ```

2. **Type errors after adding to shared:**

   ```bash
   # Rebuild shared package
   cd packages/shared && pnpm build
   # Restart dev servers
   pnpm dev
   ```

3. **Module not found errors:**
   ```bash
   # Clear caches and reinstall
   pnpm clean
   pnpm install
   ```

## Git Workflow

See [git-best-practices.md](./git-best-practices.md) for detailed Git guidelines.

### Quick Reference

1. **Branch naming:**
   - `feature/add-trip-sharing`
   - `fix/map-loading-error`
   - `chore/update-dependencies`

2. **Commit messages (Conventional Commits):**

   ```bash
   git commit -m "feat: add trip sharing functionality"
   git commit -m "fix: resolve map loading error on mobile"
   git commit -m "chore: update dependencies to latest versions"
   ```

3. **Before committing:**

   ```bash
   # Run all checks
   pnpm lint
   pnpm type-check
   pnpm test
   ```

4. **Creating a PR:**
   - Create from feature branch to `dev`
   - Fill out the PR template
   - Ensure all CI checks pass

## Testing During Development

### Running Tests Locally

```bash
# Run all tests
pnpm test

# Run tests in watch mode
pnpm test:watch

# Run tests for a specific package
pnpm --filter @travelviz/web test

# Run tests with coverage
pnpm test:coverage
```

### Writing New Tests

1. **Use the test creation script:**

   ```bash
   pnpm test:create src/components/TripCard.tsx
   ```

2. **Follow the AAA pattern:**
   ```typescript
   it('should handle click events', () => {
     // Arrange
     const handleClick = jest.fn();
     const trip = { id: '1', title: 'Test' };

     // Act
     render(<TripCard trip={trip} onClick={handleClick} />);
     fireEvent.click(screen.getByText('Test'));

     // Assert
     expect(handleClick).toHaveBeenCalledWith(trip);
   });
   ```

### E2E Testing with Playwright

1. **Run E2E tests:**

   ```bash
   pnpm test:e2e
   ```

2. **Write new E2E tests:**

   ```typescript
   // e2e/trip-creation.spec.ts
   import { test, expect } from '@playwright/test';

   test('create new trip', async ({ page }) => {
     await page.goto('/');
     await page.click('text=New Trip');
     await page.fill('[name="title"]', 'Paris Adventure');
     await page.click('text=Create');
     await expect(page).toHaveURL(/\/trips\/\w+/);
   });
   ```

3. **Debug E2E tests:**

   ```bash
   # Run with UI mode
   pnpm test:e2e --ui

   # Run with headed browser
   pnpm test:e2e --headed
   ```

---

## Additional Resources

- [Setup Guide](./setup.md) - Initial environment setup
- [Architecture Overview](./architecture.md) - System design details
- [API Documentation](./api.md) - Endpoint reference
- [Testing Guide](./testing.md) - Comprehensive testing strategies
- [Deployment Guide](./deployment.md) - Production deployment

## Need Help?

- Check existing issues on GitHub
- Review the [troubleshooting guide](./troubleshooting.md)
- Ask in the project discussions
