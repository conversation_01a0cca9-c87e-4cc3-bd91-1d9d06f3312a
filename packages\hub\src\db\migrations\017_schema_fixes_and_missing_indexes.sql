-- Migration: Fix schema mismatches and add missing indexes
-- Generated from comprehensive database analysis

BEGIN;

-- ============================================
-- 1. SCHEMA FIXES - Add missing columns
-- ============================================

-- Add missing affiliate_url to activities table
ALTER TABLE activities 
ADD COLUMN IF NOT EXISTS affiliate_url TEXT;

COMMENT ON COLUMN activities.affiliate_url IS 'Affiliate URL for monetization tracking';

-- Add missing affiliate_id to affiliate_clicks table
ALTER TABLE affiliate_clicks 
ADD COLUMN IF NOT EXISTS affiliate_id TEXT;

-- Add missing timestamp fields to auth tables
ALTER TABLE auth_failed_attempts 
ADD COLUMN IF NOT EXISTS attempt_time TIMESTAMPTZ DEFAULT NOW();

ALTER TABLE auth_account_lockouts 
ADD COLUMN IF NOT EXISTS locked_at TIMESTAMPTZ DEFAULT NOW(),
ADD COLUMN IF NOT EXISTS unlocked_at TIMESTAMPTZ;

-- ============================================
-- 2. CRITICAL PERFORMANCE INDEXES
-- ============================================

-- User query optimization indexes
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_trips_user_created 
ON trips(user_id, created_at DESC) 
INCLUDE (title, destination, start_date, end_date, visibility)
WHERE deleted_at IS NULL;

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_trips_user_updated 
ON trips(user_id, updated_at DESC) 
INCLUDE (title, destination, start_date, end_date)
WHERE deleted_at IS NULL;

-- Public trip visibility index
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_trips_public_visibility 
ON trips(visibility, created_at DESC) 
INCLUDE (title, destination, user_id)
WHERE visibility = 'public' AND deleted_at IS NULL;

-- Share slug unique index
CREATE UNIQUE INDEX CONCURRENTLY IF NOT EXISTS idx_trips_share_slug 
ON trips(share_slug) 
WHERE share_slug IS NOT NULL;

-- Activity ordering indexes
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_activities_trip_position 
ON activities(trip_id, position) 
INCLUDE (title, start_time, location);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_activities_trip_time 
ON activities(trip_id, start_time) 
INCLUDE (title, end_time, type, position)
WHERE start_time IS NOT NULL;

-- Date-based indexes
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_trips_start_date 
ON trips(start_date) 
WHERE deleted_at IS NULL;

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_trips_end_date 
ON trips(end_date) 
WHERE deleted_at IS NULL;

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_activities_start_time 
ON activities(start_time)
WHERE start_time IS NOT NULL;

-- Status index for filtering
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_trips_status 
ON trips(status) 
WHERE deleted_at IS NULL;

-- Tags array index
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_trips_tags 
ON trips USING GIN(tags)
WHERE tags IS NOT NULL AND array_length(tags, 1) > 0;

-- Clone tracking indexes
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_trips_cloned_from 
ON trips(cloned_from_trip_id, cloned_from_user_id)
WHERE cloned_from_trip_id IS NOT NULL;

-- ============================================
-- 3. FULL-TEXT SEARCH INDEXES
-- ============================================

-- Enable pg_trgm extension for fuzzy search
CREATE EXTENSION IF NOT EXISTS pg_trgm;

-- Full-text search configuration
ALTER TABLE trips ADD COLUMN IF NOT EXISTS search_vector tsvector
GENERATED ALWAYS AS (
  setweight(to_tsvector('english', COALESCE(title, '')), 'A') ||
  setweight(to_tsvector('english', COALESCE(destination, '')), 'B') ||
  setweight(to_tsvector('english', COALESCE(description, '')), 'C')
) STORED;

-- Full-text search index
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_trips_search 
ON trips USING GIN(search_vector);

-- Trigram indexes for fuzzy search
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_trips_title_trgm 
ON trips USING GIN(title gin_trgm_ops);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_trips_destination_trgm 
ON trips USING GIN(destination gin_trgm_ops);

-- ============================================
-- 4. FOREIGN KEY INDEXES
-- ============================================

-- Ensure foreign key columns have indexes
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_trips_user_id 
ON trips(user_id);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_activities_trip_id 
ON activities(trip_id);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_trip_shares_trip_id 
ON trip_shares(trip_id);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_trip_shares_shared_by 
ON trip_shares(shared_by);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_affiliate_clicks_activity_id 
ON affiliate_clicks(activity_id);

-- ============================================
-- 5. MONITORING & ANALYTICS INDEXES
-- ============================================

-- Auth monitoring indexes
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_auth_failed_attempts_email 
ON auth_failed_attempts(email, attempt_time DESC);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_auth_failed_attempts_ip 
ON auth_failed_attempts(ip_address, attempt_time DESC);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_auth_lockouts_email 
ON auth_account_lockouts(email, locked_at DESC);

-- View tracking for analytics
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_trips_views 
ON trips(views DESC)
WHERE deleted_at IS NULL;

-- ============================================
-- 6. COMPOSITE INDEXES FOR COMMON QUERIES
-- ============================================

-- User's upcoming trips
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_trips_user_upcoming 
ON trips(user_id, start_date ASC)
WHERE deleted_at IS NULL AND status != 'cancelled';

-- User's past trips
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_trips_user_past 
ON trips(user_id, end_date DESC)
WHERE deleted_at IS NULL;

-- ============================================
-- 7. DATA INTEGRITY CONSTRAINTS
-- ============================================

-- Ensure affiliate_id is required when set
ALTER TABLE affiliate_clicks
ADD CONSTRAINT affiliate_clicks_affiliate_id_not_empty 
CHECK (affiliate_id IS NULL OR length(trim(affiliate_id)) > 0);

-- Ensure lockout dates are logical
ALTER TABLE auth_account_lockouts
ADD CONSTRAINT auth_lockouts_dates_check 
CHECK (unlocked_at IS NULL OR unlocked_at > locked_at);

-- ============================================
-- 8. UPDATE STATISTICS
-- ============================================

-- Update table statistics for query planner
ANALYZE trips;
ANALYZE activities;
ANALYZE profiles;
ANALYZE trip_shares;
ANALYZE affiliate_clicks;
ANALYZE auth_failed_attempts;
ANALYZE auth_account_lockouts;

-- ============================================
-- 9. ADD COMMENTS FOR DOCUMENTATION
-- ============================================

COMMENT ON INDEX idx_trips_user_created IS 'Optimizes user trip queries ordered by creation date';
COMMENT ON INDEX idx_trips_public_visibility IS 'Optimizes public trip discovery queries';
COMMENT ON INDEX idx_activities_trip_position IS 'Optimizes ordered activity fetching within trips';
COMMENT ON INDEX idx_trips_search IS 'Full-text search on trip title, destination, and description';

COMMIT;

-- ============================================
-- POST-MIGRATION VERIFICATION QUERIES
-- ============================================

-- Run these after migration to verify:
/*
-- Check indexes were created
SELECT indexname, indexdef 
FROM pg_indexes 
WHERE schemaname = 'public' 
ORDER BY tablename, indexname;

-- Check new columns exist
SELECT column_name, data_type, is_nullable
FROM information_schema.columns
WHERE table_schema = 'public' 
  AND table_name = 'activities'
  AND column_name = 'affiliate_url';

-- Check constraints
SELECT conname, contype, condeferrable, condeferred
FROM pg_constraint
WHERE connamespace = 'public'::regnamespace;

-- Check index usage (after some queries run)
SELECT 
  schemaname,
  tablename,
  indexname,
  idx_scan,
  idx_tup_read,
  idx_tup_fetch
FROM pg_stat_user_indexes
WHERE schemaname = 'public'
ORDER BY idx_scan DESC;
*/