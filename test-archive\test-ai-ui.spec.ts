import { test, expect, chromium, Browser, <PERSON> } from '@playwright/test';
import path from 'path';
import fs from 'fs';

const TEST_USER = {
  email: '<EMAIL>',
  password: 'Flaremmk123!'
};

const PDF_FILE = path.join(__dirname, 'Updated 15-Day European Travel Itinerary_ London, Madrid, Lisbon and Porto.pdf');

const AI_MODELS = [
  'openrouter/cypher-alpha:free',
  'deepseek/deepseek-chat-v3-0324:free', 
  'deepseek/deepseek-r1-0528:free',
  'google/gemini-2.0-flash-001',
  'google/gemini-2.5-flash-preview-05-20'
];

interface TestResult {
  model: string;
  success: boolean;
  responseTime: number;
  activitiesCount: number;
  hasTitle: boolean;
  hasDestination: boolean;
  error?: string;
}

async function runAIImportTest() {
  const browser = await chromium.launch({ headless: false });
  const context = await browser.newContext();
  const page = await context.newPage();
  
  const results: TestResult[] = [];
  
  try {
    // Navigate to login
    console.log('🔐 Logging in...');
    await page.goto('http://localhost:3001/login');
    
    // Login
    await page.fill('input[type="email"]', TEST_USER.email);
    await page.fill('input[type="password"]', TEST_USER.password);
    await page.click('button[type="submit"]');
    
    // Wait for navigation
    await page.waitForURL('**/dashboard', { timeout: 10000 });
    console.log('✅ Login successful\n');
    
    // Test each model
    for (const model of AI_MODELS) {
      console.log(`🤖 Testing model: ${model}`);
      const startTime = Date.now();
      const result: TestResult = {
        model,
        success: false,
        responseTime: 0,
        activitiesCount: 0,
        hasTitle: false,
        hasDestination: false
      };
      
      try {
        // Navigate to import page
        await page.goto('http://localhost:3001/import');
        await page.waitForLoadState('networkidle');
        
        // Upload PDF
        const fileInput = await page.locator('input[type="file"]');
        await fileInput.setInputFiles(PDF_FILE);
        
        // Select model if dropdown exists
        const modelSelect = page.locator('select[name="model"], select[id*="model"]');
        if (await modelSelect.count() > 0) {
          await modelSelect.selectOption(model);
        }
        
        // Find and click import button
        const importButton = page.locator('button:has-text("Import"), button:has-text("Process"), button:has-text("Parse")');
        await importButton.click();
        
        // Wait for processing
        await page.waitForLoadState('networkidle', { timeout: 60000 });
        
        // Check for success
        const successIndicator = page.locator('.success, [data-testid="success"], .toast-success');
        const errorIndicator = page.locator('.error, [data-testid="error"], .toast-error');
        
        if (await successIndicator.count() > 0) {
          result.success = true;
          
          // Count activities
          const activities = await page.locator('.activity, [data-testid="activity"], .trip-activity').count();
          result.activitiesCount = activities;
          
          // Check for title
          const title = await page.locator('h1, h2, .trip-title').first().textContent();
          result.hasTitle = !!title && title.length > 0;
          
          // Check for destination
          const destination = await page.locator('.destination, [data-testid="destination"]').count();
          result.hasDestination = destination > 0;
          
          console.log(`   ✅ Success: ${activities} activities found`);
        } else if (await errorIndicator.count() > 0) {
          const errorText = await errorIndicator.textContent();
          result.error = errorText || 'Unknown error';
          console.log(`   ❌ Error: ${result.error}`);
        }
        
        result.responseTime = Date.now() - startTime;
        
        // Take screenshot
        await page.screenshot({ 
          path: `test-results/${model.replace(/[/:]/g, '-')}.png`,
          fullPage: true 
        });
        
      } catch (error: any) {
        result.error = error.message;
        result.responseTime = Date.now() - startTime;
        console.log(`   ❌ Error: ${error.message}`);
      }
      
      results.push(result);
      console.log(`   Response time: ${result.responseTime}ms\n`);
    }
    
    // Analyze results
    console.log('\n📊 RESULTS SUMMARY\n');
    console.log('Model Performance:');
    
    const sortedResults = [...results].sort((a, b) => {
      if (a.success !== b.success) return b.success ? 1 : -1;
      return b.activitiesCount - a.activitiesCount;
    });
    
    sortedResults.forEach((result, index) => {
      console.log(`${index + 1}. ${result.model}`);
      console.log(`   Success: ${result.success ? '✅' : '❌'}`);
      console.log(`   Activities: ${result.activitiesCount}`);
      console.log(`   Response Time: ${result.responseTime}ms`);
      if (result.error) {
        console.log(`   Error: ${result.error}`);
      }
    });
    
    // Save results
    fs.mkdirSync('test-results', { recursive: true });
    fs.writeFileSync(
      'test-results/ai-model-ui-results.json',
      JSON.stringify(results, null, 2)
    );
    
    console.log('\n💾 Results saved to test-results/ai-model-ui-results.json');
    
  } finally {
    await browser.close();
  }
}

// Run the test
runAIImportTest().catch(console.error);