import { describe, it, expect, vi, beforeEach } from 'vitest';
import { publicTripService } from './publicTrip.service';
import { getSupabaseClient, handleSupabaseError, TABLES } from '../lib/supabase';
import { logger } from '../utils/logger';
import { generateUniqueSlug } from '../utils/slug-generator';

// Mock dependencies
vi.mock('../../lib/supabase', () => ({
  getSupabaseClient: vi.fn(),
  handleSupabaseError: vi.fn(),
  TABLES: {
    TRIPS: 'trips',
    ACTIVITIES: 'activities',
    TRIP_SHARES: 'trip_shares',
    USERS: 'users',
  },
}));

vi.mock('../../utils/logger', () => ({
  logger: {
    info: vi.fn(),
    error: vi.fn(),
  },
}));

vi.mock('../../utils/slug-generator', () => ({
  generateUniqueSlug: vi.fn(),
}));

describe('PublicTripService', () => {
  let mockSupabase: any;

  beforeEach(() => {
    vi.clearAllMocks();
    
    mockSupabase = {
      from: vi.fn(() => mockSupabase),
      select: vi.fn(() => mockSupabase),
      eq: vi.fn(() => mockSupabase),
      gte: vi.fn(() => mockSupabase),
      single: vi.fn(() => mockSupabase),
      update: vi.fn(() => mockSupabase),
      insert: vi.fn(() => mockSupabase),
      order: vi.fn(() => mockSupabase),
      rpc: vi.fn(() => mockSupabase),
    };

    vi.mocked(getSupabaseClient).mockReturnValue(mockSupabase);
    vi.mocked(handleSupabaseError).mockImplementation((error) => ({
      message: error?.message || 'Unknown error',
      code: error?.code,
    }));
  });

  describe('makePublic', () => {
    it('should make a trip public and return the slug', async () => {
      const tripId = 'trip123';
      const userId = 'user123';
      const mockSlug = 'unique-slug-123';

      mockSupabase.single.mockResolvedValueOnce({
        data: { share_slug: null, visibility: 'private' },
        error: null,
      });

      vi.mocked(generateUniqueSlug).mockResolvedValue(mockSlug);

      mockSupabase.update.mockReturnValue({
        eq: vi.fn(() => ({
          eq: vi.fn(() => Promise.resolve({ error: null })),
        })),
      });

      const result = await publicTripService.makePublic(tripId, userId);

      expect(result).toBe(mockSlug);
      expect(mockSupabase.update).toHaveBeenCalledWith({
        share_slug: mockSlug,
        visibility: 'public',
      });
      expect(logger.info).toHaveBeenCalledWith(`Made trip ${tripId} public with slug: ${mockSlug}`);
    });

    it('should return existing slug if trip is already public', async () => {
      const tripId = 'trip123';
      const userId = 'user123';
      const existingSlug = 'existing-slug';

      mockSupabase.single.mockResolvedValueOnce({
        data: { share_slug: existingSlug, visibility: 'public' },
        error: null,
      });

      const result = await publicTripService.makePublic(tripId, userId);

      expect(result).toBe(existingSlug);
      expect(generateUniqueSlug).not.toHaveBeenCalled();
      expect(mockSupabase.update).not.toHaveBeenCalled();
    });

    it('should throw error if trip not found', async () => {
      const tripId = 'invalid';
      const userId = 'user123';

      mockSupabase.single.mockResolvedValueOnce({
        data: null,
        error: { message: 'Not found' },
      });

      await expect(publicTripService.makePublic(tripId, userId))
        .rejects.toThrow('Trip not found or unauthorized');
    });

    it('should handle update errors', async () => {
      const tripId = 'trip123';
      const userId = 'user123';

      mockSupabase.single.mockResolvedValueOnce({
        data: { share_slug: null, visibility: 'private' },
        error: null,
      });

      vi.mocked(generateUniqueSlug).mockResolvedValue('new-slug');

      mockSupabase.update.mockReturnValue({
        eq: vi.fn(() => ({
          eq: vi.fn(() => Promise.resolve({ error: { message: 'Update failed' } })),
        })),
      });

      await expect(publicTripService.makePublic(tripId, userId))
        .rejects.toThrow('Failed to make trip public: Update failed');
    });
  });

  describe('getPublicTrip', () => {
    it('should return public trip data with activities', async () => {
      const slug = 'test-slug';
      const mockActivities = [
        { id: 'act1', name: 'Activity 1', position: 0 },
        { id: 'act2', name: 'Activity 2', position: 1 },
      ];

      const mockTrip = {
        id: 'trip123',
        title: 'Test Trip',
        description: 'Test Description',
        destination: 'Paris',
        start_date: '2024-01-01',
        end_date: '2024-01-07',
        cover_image: 'image.jpg',
        tags: ['travel'],
        budget_amount: 1000,
        budget_currency: 'USD',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        users: { name: 'John Doe' },
        activities: mockActivities,  // Activities now included in JOIN
      };

      // Mock trip query with joined activities
      mockSupabase.single.mockResolvedValueOnce({
        data: mockTrip,
        error: null,
      });

      // Mock view count query - use a separate mock for the trip_shares query
      const mockViewCountQuery = {
        select: vi.fn(() => mockViewCountQuery),
        eq: vi.fn(() => Promise.resolve({ count: 5 })),
      };
      
      // Override the from call for trip_shares table
      mockSupabase.from.mockImplementation((table: string) => {
        if (table === 'trip_shares') {
          return mockViewCountQuery;
        }
        return mockSupabase;
      });

      const result = await publicTripService.getPublicTrip(slug);

      expect(result).toBeTruthy();
      expect(result?.trip.title).toBe('Test Trip');
      expect(result?.trip.authorName).toBe('John Doe');
      expect(result?.activities).toHaveLength(2);
      expect(result?.viewCount).toBe(5);
    });

    it('should return null if trip not found', async () => {
      const slug = 'invalid-slug';

      mockSupabase.single.mockResolvedValueOnce({
        data: null,
        error: { message: 'Not found' },
      });

      const result = await publicTripService.getPublicTrip(slug);

      expect(result).toBeNull();
      expect(logger.error).not.toHaveBeenCalled();
    });

    it('should handle errors gracefully', async () => {
      const slug = 'test-slug';

      mockSupabase.single.mockRejectedValueOnce(new Error('Database error'));

      const result = await publicTripService.getPublicTrip(slug);

      expect(result).toBeNull();
      expect(logger.error).toHaveBeenCalledWith(
        'Failed to fetch public trip:',
        expect.any(Object)
      );
    });
  });

  describe('getTripPreview', () => {
    it('should return trip preview data', async () => {
      const slug = 'test-slug';
      const mockPreviewData = [{
        id: 'trip123',
        title: 'Preview Trip',
        description: 'Preview Description',
        destination: 'London',
        start_date: '2024-02-01',
        end_date: '2024-02-05',
        cover_image: 'preview.jpg',
        author_name: 'Jane Smith',
        activity_count: 10,
      }];

      // Mock the optimized RPC call first
      mockSupabase.rpc.mockResolvedValueOnce({
        data: mockPreviewData,
        error: null,
      });

      const result = await publicTripService.getTripPreview(slug);

      expect(result).toBeTruthy();
      expect(result?.title).toBe('Preview Trip');
      expect(result?.authorName).toBe('Jane Smith');
      expect(result?.activityCount).toBe(10);
    });

    it('should return null if preview not found', async () => {
      const slug = 'invalid-slug';

      mockSupabase.single.mockResolvedValueOnce({
        data: null,
        error: { message: 'Not found' },
      });

      const result = await publicTripService.getTripPreview(slug);

      expect(result).toBeNull();
    });
  });

  describe('trackView', () => {
    it('should track a new view', async () => {
      const slug = 'test-slug';
      const ipAddress = '***********';

      // Mock trip lookup
      mockSupabase.single.mockResolvedValueOnce({
        data: { id: 'trip123' },
        error: null,
      });

      // Mock recent view check
      mockSupabase.single.mockResolvedValueOnce({
        data: null,
        error: { code: 'PGRST116' }, // No rows found
      });

      // Mock insert
      mockSupabase.insert.mockResolvedValueOnce({
        error: null,
      });

      await publicTripService.trackView(slug, ipAddress);

      expect(mockSupabase.insert).toHaveBeenCalledWith({
        trip_id: 'trip123',
        ip_address: ipAddress,
        referrer: null,
      });
    });

    it('should not track view if already viewed recently', async () => {
      const slug = 'test-slug';
      const ipAddress = '***********';

      // Mock trip lookup
      mockSupabase.single.mockResolvedValueOnce({
        data: { id: 'trip123' },
        error: null,
      });

      // Mock recent view exists
      mockSupabase.single.mockResolvedValueOnce({
        data: { id: 'view123' },
        error: null,
      });

      await publicTripService.trackView(slug, ipAddress);

      expect(mockSupabase.insert).not.toHaveBeenCalled();
    });

    it('should handle errors silently', async () => {
      const slug = 'test-slug';
      const ipAddress = '***********';

      mockSupabase.single.mockRejectedValueOnce(new Error('Database error'));

      await expect(publicTripService.trackView(slug, ipAddress))
        .resolves.not.toThrow();

      expect(logger.error).toHaveBeenCalledWith(
        'Failed to track view:',
        expect.any(Object)
      );
    });
  });

  describe('isSlugAvailable', () => {
    it('should return true if slug is available', async () => {
      const slug = 'new-slug';

      mockSupabase.single.mockResolvedValueOnce({
        data: null,
        error: { code: 'PGRST116' }, // No rows found
      });

      const result = await publicTripService.isSlugAvailable(slug);

      expect(result).toBe(true);
    });

    it('should return false if slug is taken', async () => {
      const slug = 'existing-slug';

      mockSupabase.single.mockResolvedValueOnce({
        data: { id: 'trip123' },
        error: null,
      });

      const result = await publicTripService.isSlugAvailable(slug);

      expect(result).toBe(false);
    });

    it('should return false on error', async () => {
      const slug = 'test-slug';

      mockSupabase.single.mockRejectedValueOnce(new Error('Database error'));

      const result = await publicTripService.isSlugAvailable(slug);

      expect(result).toBe(false);
      expect(logger.error).toHaveBeenCalled();
    });
  });

  describe('makePrivate', () => {
    it('should make a trip private', async () => {
      const tripId = 'trip123';
      const userId = 'user123';

      mockSupabase.update.mockReturnValue({
        eq: vi.fn(() => ({
          eq: vi.fn(() => Promise.resolve({ error: null })),
        })),
      });

      await publicTripService.makePrivate(tripId, userId);

      expect(mockSupabase.update).toHaveBeenCalledWith({
        visibility: 'private',
      });
      expect(logger.info).toHaveBeenCalledWith(`Made trip ${tripId} private`);
    });

    it('should handle update errors', async () => {
      const tripId = 'trip123';
      const userId = 'user123';

      mockSupabase.update.mockReturnValue({
        eq: vi.fn(() => ({
          eq: vi.fn(() => Promise.resolve({ error: { message: 'Update failed' } })),
        })),
      });

      await expect(publicTripService.makePrivate(tripId, userId))
        .rejects.toThrow('Failed to make trip private: Update failed');
    });
  });
});