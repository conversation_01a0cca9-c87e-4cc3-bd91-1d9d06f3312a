#!/usr/bin/env node

/**
 * Authentication Cascade Test Runner
 * 
 * Runs all integration tests that prevent the auth cascade failure
 * Usage: pnpm test:auth-cascade
 */

const { execSync } = require('child_process');

// Simple console colors without external dependencies
const colors = {
  reset: '\x1b[0m',
  bold: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  cyan: '\x1b[36m',
};

const chalk = {
  bold: {
    cyan: (text) => `${colors.bold}${colors.cyan}${text}${colors.reset}`,
    green: (text) => `${colors.bold}${colors.green}${text}${colors.reset}`,
    red: (text) => `${colors.bold}${colors.red}${text}${colors.reset}`,
    yellow: (text) => `${colors.bold}${colors.yellow}${text}${colors.reset}`,
  },
  cyan: (text) => `${colors.cyan}${text}${colors.reset}`,
  green: (text) => `${colors.green}${text}${colors.reset}`,
  red: (text) => `${colors.red}${text}${colors.reset}`,
  yellow: (text) => `${colors.yellow}${text}${colors.reset}`,
  gray: (text) => `\x1b[90m${text}${colors.reset}`,
};

console.log(chalk.bold.cyan('\n🔐 Running Authentication Cascade Prevention Tests\n'));

const tests = [
  {
    name: 'API Client Circuit Breaker',
    path: 'packages/web/lib/api-client.integration.test.ts',
    critical: true,
  },
  {
    name: 'Auth Middleware',
    path: 'packages/hub/src/middleware/supabase-auth.middleware.test.ts',
    critical: true,
  },
  {
    name: 'JWT Verification',
    path: 'packages/hub/src/utils/supabase-jwt.integration.test.ts',
    critical: true,
  },
  {
    name: 'Dashboard Integration',
    path: 'packages/web/components/dashboard/BentoDashboard.integration.test.tsx',
    critical: false,
  },
  {
    name: 'E2E Authentication Flow',
    path: 'packages/hub/src/routes/auth-cascade.e2e.test.ts',
    critical: true,
  },
];

let passed = 0;
let failed = 0;
const results = [];

for (const test of tests) {
  console.log(chalk.yellow(`\nRunning ${test.name}...`));
  
  try {
    execSync(`pnpm test ${test.path}`, { 
      stdio: 'inherit',
      cwd: process.cwd(),
    });
    
    console.log(chalk.green(`✅ ${test.name} passed`));
    passed++;
    results.push({ ...test, status: 'passed' });
  } catch (error) {
    console.log(chalk.red(`❌ ${test.name} failed`));
    failed++;
    results.push({ ...test, status: 'failed' });
    
    if (test.critical) {
      console.log(chalk.red.bold('\n⚠️  Critical test failed! Auth cascade prevention is broken.\n'));
    }
  }
}

// Summary
console.log(chalk.bold.cyan('\n📊 Test Summary\n'));
console.log(chalk.green(`Passed: ${passed}`));
console.log(chalk.red(`Failed: ${failed}`));
console.log(chalk.gray(`Total: ${tests.length}`));

// Detailed results
console.log(chalk.bold('\n📋 Detailed Results:\n'));
results.forEach(result => {
  const icon = result.status === 'passed' ? '✅' : '❌';
  const color = result.status === 'passed' ? chalk.green : chalk.red;
  const critical = result.critical ? chalk.yellow(' [CRITICAL]') : '';
  console.log(`${icon} ${color(result.name)}${critical}`);
});

// Final verdict
if (failed === 0) {
  console.log(chalk.bold.green('\n✨ All auth cascade tests passed! Safe to deploy.\n'));
  process.exit(0);
} else {
  const criticalFailures = results.filter(r => r.critical && r.status === 'failed').length;
  if (criticalFailures > 0) {
    console.log(chalk.bold.red(`\n🚨 ${criticalFailures} critical test(s) failed!`));
    console.log(chalk.red('The authentication cascade prevention is broken.'));
    console.log(chalk.red('DO NOT DEPLOY until these are fixed.\n'));
  } else {
    console.log(chalk.bold.yellow('\n⚠️  Some tests failed, but no critical failures.\n'));
  }
  process.exit(1);
}