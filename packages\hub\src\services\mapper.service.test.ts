import { describe, it, expect } from 'vitest';
import { MapperService } from './mapper.service';
import { Trip, Activity, Profile, ActivityType } from '@travelviz/shared';

describe('MapperService', () => {
  describe('mapTripToResponse', () => {
    const mockTrip: Trip = {
      id: 'trip123',
      user_id: 'user123',
      title: 'Summer Vacation',
      description: 'A relaxing trip to the beach',
      destination: 'Hawaii',
      start_date: '2024-07-01',
      end_date: '2024-07-15',
      status: 'planning',
      visibility: 'private',
      cover_image: 'https://example.com/image.jpg',
      metadata: { theme: 'beach' },
      tags: ['summer', 'beach'],
      budget_amount: 5000,
      budget_currency: 'USD',
      views: 10,
      created_at: '2024-01-01T00:00:00Z',
      updated_at: '2024-01-02T00:00:00Z',
      activities: [],
    };

    it('should map trip with owner access', () => {
      const result = MapperService.mapTripToResponse(mockTrip, 'user123');

      expect(result).toEqual({
        id: 'trip123',
        userId: 'user123',
        title: 'Summer Vacation',
        description: 'A relaxing trip to the beach',
        destination: 'Hawaii',
        start_date: '2024-07-01',
        end_date: '2024-07-15',
        status: 'planning',
        visibility: 'private',
        cover_image: 'https://example.com/image.jpg',
        metadata: { theme: 'beach' },
        tags: ['summer', 'beach'],
        budget_amount: 5000,
        budget_currency: 'USD',
        views: 10,
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-02T00:00:00Z',
        activities: [],
        activitiesCount: 0,
        isOwner: true,
        canEdit: true,
      });
    });

    it('should map trip with custom access', () => {
      const access = {
        canView: true,
        canEdit: false,
        canDelete: false,
        isOwner: false,
      };

      const result = MapperService.mapTripToResponse(mockTrip, 'other-user', access);

      expect(result.isOwner).toBe(false);
      expect(result.canEdit).toBe(false);
    });

    it('should handle trip with activities', () => {
      const tripWithActivities = {
        ...mockTrip,
        activities: [
          { id: 'act1', title: 'Beach Day' },
          { id: 'act2', title: 'Snorkeling' },
        ] as Activity[],
      };

      const result = MapperService.mapTripToResponse(tripWithActivities, 'user123');

      expect(result.activitiesCount).toBe(2);
      expect(result.activities).toHaveLength(2);
    });

    it('should determine ownership based on userId', () => {
      const result1 = MapperService.mapTripToResponse(mockTrip, 'user123');
      expect(result1.isOwner).toBe(true);
      expect(result1.canEdit).toBe(true);

      const result2 = MapperService.mapTripToResponse(mockTrip, 'other-user');
      expect(result2.isOwner).toBe(false);
      expect(result2.canEdit).toBe(false);
    });
  });

  describe('mapActivityToResponse', () => {
    const mockActivity: Activity = {
      id: 'activity123',
      trip_id: 'trip123',
      title: 'Beach Day',
      description: 'Relaxing at the beach',
      location: 'Waikiki Beach',
      start_time: '2024-07-02T10:00:00Z',
      end_time: '2024-07-02T16:00:00Z',
      type: ActivityType.activity,
      position: 0,
      price: 50,
      currency: 'USD',
      booking_url: 'https://example.com/beach',
      notes: 'Bring sunscreen',
      location_lat: 21.2765,
      location_lng: -157.8281,
      created_at: '2024-01-01T00:00:00Z',
      updated_at: '2024-01-02T00:00:00Z',
      metadata: { weather: 'sunny' },
      attachments: [],
    };

    it('should map activity with edit permission', () => {
      const result = MapperService.mapActivityToResponse(mockActivity);

      expect(result).toEqual({
        id: 'activity123',
        tripId: 'trip123',
        title: 'Beach Day',
        description: 'Relaxing at the beach',
        location: 'Waikiki Beach',
        start_time: '2024-07-02T10:00:00Z',
        end_time: '2024-07-02T16:00:00Z',
        type: ActivityType.activity,
        position: 0,
        price: 50,
        currency: 'USD',
        booking_reference: undefined,
        booking_url: 'https://example.com/beach',
        notes: 'Bring sunscreen',
        location_lat: 21.2765,
        location_lng: -157.8281,
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-02T00:00:00Z',
        metadata: { weather: 'sunny' },
        attachments: [],
        canEdit: true,
        affiliateUrl: undefined,
      });
    });

    it('should map activity without edit permission', () => {
      const result = MapperService.mapActivityToResponse(mockActivity, false);

      expect(result.canEdit).toBe(false);
    });
  });

  describe('mapProfileToResponse', () => {
    const mockProfile: Profile = {
      id: 'user123',
      email: '<EMAIL>',
      name: 'John Doe',
      avatar_url: 'https://example.com/avatar.jpg',
      bio: 'Travel enthusiast',
      preferences: { theme: 'dark' },
      created_at: '2024-01-01T00:00:00Z',
      updated_at: '2024-01-02T00:00:00Z',
    };

    it('should map profile to response', () => {
      const result = MapperService.mapProfileToResponse(mockProfile);

      expect(result).toEqual({
        id: 'user123',
        email: '<EMAIL>',
        name: 'John Doe',
        avatarUrl: 'https://example.com/avatar.jpg',
        bio: 'Travel enthusiast',
        preferences: { theme: 'dark' },
        createdAt: '2024-01-01T00:00:00Z',
        updatedAt: '2024-01-02T00:00:00Z',
      });
    });
  });

  describe('mapCreateTripRequest', () => {
    it('should map create trip request', () => {
      const request = {
        title: 'New Trip',
        description: 'Trip description',
        destination: 'Paris',
        startDate: '2024-08-01',
        endDate: '2024-08-15',
        visibility: 'public' as const,
      };

      const result = MapperService.mapCreateTripRequest(request, 'user123');

      expect(result).toEqual({
        user_id: 'user123',
        title: 'New Trip',
        description: 'Trip description',
        destination: 'Paris',
        start_date: '2024-08-01',
        end_date: '2024-08-15',
        status: 'draft',
        visibility: 'public',
        cover_image: undefined,
        tags: [],
        budget_amount: undefined,
        budget_currency: 'USD',
        metadata: {},
      });
    });

    it('should use default visibility and status', () => {
      const request = {
        title: 'New Trip',
      };

      const result = MapperService.mapCreateTripRequest(request, 'user123');

      expect(result.visibility).toBe('private');
      expect(result.status).toBe('draft');
    });
  });

  describe('mapCreateActivityRequest', () => {
    it('should map create activity request', () => {
      const request = {
        title: 'Museum Visit',
        description: 'Visit the Louvre',
        location: 'Louvre Museum',
        startTime: '2024-08-05T10:00:00Z',
        endTime: '2024-08-05T13:00:00Z',
        type: 'sightseeing' as const,
        price: 20,
        currency: 'EUR',
      };

      const result = MapperService.mapCreateActivityRequest(request, 'trip123');

      expect(result).toEqual({
        trip_id: 'trip123',
        title: 'Museum Visit',
        description: 'Visit the Louvre',
        type: 'sightseeing',
        start_time: '2024-08-05T10:00:00Z',
        end_time: '2024-08-05T13:00:00Z',
        location: 'Louvre Museum',
        location_lat: undefined,
        location_lng: undefined,
        price: 20,
        currency: 'EUR',
        booking_reference: undefined,
        booking_url: undefined,
        notes: undefined,
        attachments: [],
        metadata: {},
      });
    });

    it('should use default type if not provided', () => {
      const request = {
        title: 'Activity',
      };

      const result = MapperService.mapCreateActivityRequest(request, 'trip123');

      expect(result.type).toBe(ActivityType.activity);
    });
  });

  describe('mapUpdateRequest', () => {
    it('should filter out undefined values', () => {
      const request = {
        title: 'Updated Title',
        description: undefined,
        location: null,
        price: 100,
      };

      const result = MapperService.mapUpdateRequest(request);

      expect(result).toEqual({
        title: 'Updated Title',
        location: null,
        price: 100,
      });
      expect('description' in result).toBe(false);
    });

    it('should convert camelCase to snake_case', () => {
      const request = {
        startDate: '2024-08-01',
        endDate: '2024-08-15',
        coverImage: 'https://example.com/image.jpg',
      };

      const result = MapperService.mapUpdateRequest(request);

      expect(result).toEqual({
        start_date: '2024-08-01',
        end_date: '2024-08-15',
        cover_image: 'https://example.com/image.jpg',
      });
    });

    it('should handle nested objects', () => {
      const request = {
        title: 'Updated',
        metadata: { theme: 'adventure' },
        tags: ['hiking', 'nature'],
      };

      const result = MapperService.mapUpdateRequest(request);

      expect(result).toEqual({
        title: 'Updated',
        metadata: { theme: 'adventure' },
        tags: ['hiking', 'nature'],
      });
    });
  });
});
