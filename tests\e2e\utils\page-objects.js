/**
 * Page Object Models for E2E Tests
 * 
 * Provides clean interfaces for interacting with different pages
 * in the TravelViz application during E2E testing.
 */

const testConfig = require('../../test.config');

class HomePage {
  constructor(page) {
    this.page = page;
    this.baseUrl = testConfig.e2e.baseUrl;
  }

  async navigate() {
    await this.page.goto(this.baseUrl);
    await this.page.waitForLoadState('networkidle');
  }

  async isLoaded() {
    try {
      // Wait for key elements that indicate the homepage is loaded
      await this.page.waitForSelector('h1, [data-testid="hero-title"], .hero-title', { timeout: 10000 });
      return true;
    } catch (error) {
      return false;
    }
  }

  async clickLogin() {
    // Try multiple selectors for login button/link
    const loginSelectors = [
      'a[href="/login"]',
      'button:has-text("Login")',
      'a:has-text("Login")',
      '[data-testid="login-button"]',
      '.login-button'
    ];

    for (const selector of loginSelectors) {
      try {
        const element = await this.page.locator(selector).first();
        if (await element.isVisible()) {
          await element.click();
          return;
        }
      } catch (error) {
        // Continue to next selector
      }
    }

    // If no login button found, navigate directly to login page
    await this.page.goto(`${this.baseUrl}/login`);
  }

  async clickSignup() {
    const signupSelectors = [
      'a[href="/signup"]',
      'button:has-text("Sign up")',
      'a:has-text("Sign up")',
      '[data-testid="signup-button"]',
      '.signup-button'
    ];

    for (const selector of signupSelectors) {
      try {
        const element = await this.page.locator(selector).first();
        if (await element.isVisible()) {
          await element.click();
          return;
        }
      } catch (error) {
        // Continue to next selector
      }
    }

    // If no signup button found, navigate directly to signup page
    await this.page.goto(`${this.baseUrl}/signup`);
  }
}

class LoginPage {
  constructor(page) {
    this.page = page;
    this.baseUrl = testConfig.e2e.baseUrl;
  }

  async navigate() {
    await this.page.goto(`${this.baseUrl}/login`);
    await this.page.waitForLoadState('networkidle');
  }

  async isLoaded() {
    try {
      // Wait for login form elements
      await this.page.waitForSelector('input[type="email"], input[name="email"]', { timeout: 10000 });
      await this.page.waitForSelector('input[type="password"], input[name="password"]', { timeout: 5000 });
      return true;
    } catch (error) {
      return false;
    }
  }

  async login(email, password) {
    // Fill email field
    const emailSelectors = [
      'input[type="email"]',
      'input[name="email"]',
      '[data-testid="email-input"]',
      '#email'
    ];

    for (const selector of emailSelectors) {
      try {
        const element = await this.page.locator(selector).first();
        if (await element.isVisible()) {
          await element.fill(email);
          break;
        }
      } catch (error) {
        // Continue to next selector
      }
    }

    // Fill password field
    const passwordSelectors = [
      'input[type="password"]',
      'input[name="password"]',
      '[data-testid="password-input"]',
      '#password'
    ];

    for (const selector of passwordSelectors) {
      try {
        const element = await this.page.locator(selector).first();
        if (await element.isVisible()) {
          await element.fill(password);
          break;
        }
      } catch (error) {
        // Continue to next selector
      }
    }

    // Submit form
    const submitSelectors = [
      'button[type="submit"]',
      'button:has-text("Login")',
      'button:has-text("Sign in")',
      '[data-testid="login-submit"]',
      '.login-submit'
    ];

    for (const selector of submitSelectors) {
      try {
        const element = await this.page.locator(selector).first();
        if (await element.isVisible()) {
          await element.click();
          break;
        }
      } catch (error) {
        // Continue to next selector
      }
    }

    // Wait for navigation or response
    await this.page.waitForLoadState('networkidle');
  }

  async expectLoginSuccess() {
    try {
      // Wait for redirect to dashboard or home page
      await this.page.waitForURL('**/dashboard', { timeout: 15000 });
      return true;
    } catch (error) {
      // Try alternative success indicators
      try {
        await this.page.waitForURL('**/', { timeout: 5000 });
        // Check if we're no longer on login page
        const currentUrl = this.page.url();
        return !currentUrl.includes('/login');
      } catch (error2) {
        return false;
      }
    }
  }

  async expectLoginError(expectedMessage) {
    try {
      // Look for error messages
      const errorSelectors = [
        '.error',
        '[data-testid="error"]',
        '.toast-error',
        '.alert-error',
        '[role="alert"]'
      ];

      for (const selector of errorSelectors) {
        try {
          const element = await this.page.locator(selector).first();
          if (await element.isVisible()) {
            const errorText = await element.textContent();
            return errorText.includes(expectedMessage);
          }
        } catch (error) {
          // Continue to next selector
        }
      }

      return false;
    } catch (error) {
      return false;
    }
  }
}

class DashboardPage {
  constructor(page) {
    this.page = page;
    this.baseUrl = testConfig.e2e.baseUrl;
  }

  async navigate() {
    await this.page.goto(`${this.baseUrl}/dashboard`);
    await this.page.waitForLoadState('networkidle');
  }

  async isLoaded() {
    try {
      // Wait for dashboard-specific elements
      const dashboardSelectors = [
        '[data-testid="dashboard"]',
        '.dashboard',
        'h1:has-text("Dashboard")',
        '.trips-list',
        '[data-testid="trips-list"]'
      ];

      for (const selector of dashboardSelectors) {
        try {
          await this.page.waitForSelector(selector, { timeout: 5000 });
          return true;
        } catch (error) {
          // Continue to next selector
        }
      }

      // Fallback: check URL contains dashboard
      const currentUrl = this.page.url();
      return currentUrl.includes('/dashboard');
    } catch (error) {
      return false;
    }
  }

  async expectWelcomeMessage() {
    try {
      const welcomeSelectors = [
        ':has-text("Welcome")',
        '[data-testid="welcome"]',
        '.welcome-message'
      ];

      for (const selector of welcomeSelectors) {
        try {
          const element = await this.page.locator(selector).first();
          if (await element.isVisible()) {
            return true;
          }
        } catch (error) {
          // Continue to next selector
        }
      }

      return false;
    } catch (error) {
      return false;
    }
  }

  async getTripsCount() {
    try {
      const tripSelectors = [
        '.trip-card',
        '[data-testid="trip-card"]',
        '.trip-item',
        '[data-testid="trip-item"]'
      ];

      for (const selector of tripSelectors) {
        try {
          const elements = await this.page.locator(selector).all();
          if (elements.length >= 0) {
            return elements.length;
          }
        } catch (error) {
          // Continue to next selector
        }
      }

      return 0;
    } catch (error) {
      return 0;
    }
  }
}

class ImportPage {
  constructor(page) {
    this.page = page;
    this.baseUrl = testConfig.e2e.baseUrl;
  }

  async navigate() {
    await this.page.goto(`${this.baseUrl}/import`);
    await this.page.waitForLoadState('networkidle');
  }

  async isLoaded() {
    try {
      // Wait for import page elements
      const importSelectors = [
        'textarea',
        'input[type="file"]',
        '[data-testid="import-content"]',
        '.import-form'
      ];

      for (const selector of importSelectors) {
        try {
          await this.page.waitForSelector(selector, { timeout: 5000 });
          return true;
        } catch (error) {
          // Continue to next selector
        }
      }

      return false;
    } catch (error) {
      return false;
    }
  }

  async pasteContent(content) {
    const textareaSelectors = [
      'textarea',
      '[data-testid="import-content"]',
      'textarea[name="content"]',
      '#import-content'
    ];

    for (const selector of textareaSelectors) {
      try {
        const element = await this.page.locator(selector).first();
        if (await element.isVisible()) {
          await element.fill(content);
          return;
        }
      } catch (error) {
        // Continue to next selector
      }
    }

    throw new Error('Could not find textarea to paste content');
  }

  async selectSource(source) {
    const sourceSelectors = [
      'select[name="source"]',
      '[data-testid="source-select"]',
      '#source-select'
    ];

    for (const selector of sourceSelectors) {
      try {
        const element = await this.page.locator(selector).first();
        if (await element.isVisible()) {
          await element.selectOption(source);
          return;
        }
      } catch (error) {
        // Continue to next selector
      }
    }

    // Source selection might be optional
    console.log('Source selection not found - continuing without it');
  }

  async startImport() {
    const importButtonSelectors = [
      'button:has-text("Import")',
      'button:has-text("Parse")',
      'button:has-text("Start Import")',
      '[data-testid="import-button"]',
      '.import-button'
    ];

    for (const selector of importButtonSelectors) {
      try {
        const element = await this.page.locator(selector).first();
        if (await element.isVisible()) {
          await element.click();
          return;
        }
      } catch (error) {
        // Continue to next selector
      }
    }

    throw new Error('Could not find import button');
  }

  async waitForImportComplete() {
    try {
      // Wait for import completion indicators
      await this.page.waitForSelector('.import-complete, [data-testid="import-complete"], :has-text("Import complete")', { timeout: 60000 });
      return true;
    } catch (error) {
      return false;
    }
  }

  async expectImportSuccess() {
    try {
      const successSelectors = [
        '.success',
        '[data-testid="success"]',
        '.toast-success',
        ':has-text("Success")',
        ':has-text("Complete")'
      ];

      for (const selector of successSelectors) {
        try {
          const element = await this.page.locator(selector).first();
          if (await element.isVisible()) {
            return true;
          }
        } catch (error) {
          // Continue to next selector
        }
      }

      return false;
    } catch (error) {
      return false;
    }
  }
}

module.exports = {
  HomePage,
  LoginPage,
  DashboardPage,
  ImportPage
};