{"root": true, "extends": ["eslint:recommended", "@typescript-eslint/recommended"], "parser": "@typescript-eslint/parser", "parserOptions": {"ecmaVersion": 2020, "sourceType": "module"}, "env": {"node": true, "es6": true}, "rules": {"@typescript-eslint/no-unused-vars": "warn", "@typescript-eslint/no-explicit-any": "warn", "prefer-const": "error", "no-var": "error"}, "ignorePatterns": ["node_modules/", "dist/", ".next/", "**/*.js", "**/*.jsx"]}