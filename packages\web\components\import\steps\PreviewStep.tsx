'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useImport } from '@/contexts/ImportContext';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { 
  CheckCircle, 
  AlertCircle, 
  Edit, 
  MapPin, 
  Calendar,
  DollarSign,
  Clock,
  ArrowRight,
  Sparkles 
} from 'lucide-react';
import { format } from 'date-fns';
import { MiniTimeline } from '@/components/timeline/MiniTimeline';
import { MiniMap } from '@/components/map/MiniMap';
import { TripGlobe } from '@/components/trip/TripGlobe';
import { importApi } from '@/lib/api/import';
import { useImportAnalytics } from '@/hooks/useImportAnalytics';
import { ShimmerButton } from '@/components/magic-ui/shimmer-button';
import { DynamicNumberTicker } from '@/components/magic-ui/number-ticker-dynamic';
import { MorphingText } from '@/components/magic-ui/morphing-text';
import { useLoadingMessages, loadingMessageSets } from '@/hooks/useLoadingMessages';

export function PreviewStep() {
  const router = useRouter();
  const { parsedTrip, importId, setStep, setError } = useImport();
  const [activeTab, setActiveTab] = useState<'timeline' | 'map' | 'globe'>('timeline');
  const [isCreating, setIsCreating] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const { trackTripCreated, trackPreviewEdit, trackImportSuccess } = useImportAnalytics();
  const { currentMessage, reset: resetLoadingMessages } = useLoadingMessages(loadingMessageSets.create, false);

  if (!parsedTrip) {
    setError('No parsed trip data available');
    setStep('input');
    return null;
  }

  const handleCreateTrip = async () => {
    if (!importId || !parsedTrip) return;

    setIsCreating(true);
    setError(null);
    resetLoadingMessages();

    try {
      setStep('creating');
      const { tripId } = await importApi.createTripFromImport(importId);
      
      // Track successful trip creation
      trackTripCreated(importId, tripId);
      trackImportSuccess(
        importId,
        parsedTrip.activities.length,
        stats.days,
        parsedTrip.metadata?.source || 'unknown'
      );
      
      // Navigate to the new trip in dashboard
      router.push(`/plan/${tripId}`);
    } catch (error) {
      setError('Failed to create trip. Please try again.');
      setStep('preview');
    } finally {
      setIsCreating(false);
    }
  };

  const handleViewParsed = () => {
    if (!importId) return;
    // Navigate to the import trip view page we created
    router.push(`/import/trip/${importId}`);
  };

  const getConfidenceColor = (confidence: number) => {
    if (confidence >= 0.8) return 'text-green-600 bg-green-50';
    if (confidence >= 0.6) return 'text-yellow-600 bg-yellow-50';
    return 'text-red-600 bg-red-50';
  };

  const stats = {
    days: Math.ceil((new Date(parsedTrip.endDate).getTime() - new Date(parsedTrip.startDate).getTime()) / (1000 * 60 * 60 * 24)),
    activities: parsedTrip.activities.length,
    locations: new Set(parsedTrip.activities.map((a: any) => a.location?.address).filter(Boolean)).size,
    estimatedBudget: parsedTrip.activities.reduce((sum: number, a: any) => sum + (a.price || 0), 0)
  };

  return (
    <div className="space-y-6" role="region" aria-label="Trip preview">
      {/* Success Alert */}
      <Alert className="bg-green-50 border-green-200" role="alert" aria-live="polite">
        <CheckCircle className="h-5 w-5 text-green-600" aria-hidden="true" />
        <AlertDescription className="text-green-800">
          <strong>Success!</strong> We extracted{' '}
          <DynamicNumberTicker 
            value={stats.activities} 
            springConfig="bounce"
            className="font-semibold"
          />{' '}
          activities across{' '}
          <DynamicNumberTicker 
            value={stats.days} 
            springConfig="bounce"
            className="font-semibold"
            delay={200}
          />{' '}
          days from your conversation.
        </AlertDescription>
      </Alert>

      {/* Trip Overview Card */}
      <Card>
        <CardHeader role="heading" aria-level={2}>
          <div className="flex items-start justify-between">
            <div className="space-y-2">
              <h2 className="text-2xl font-bold">{parsedTrip.title}</h2>
              <p className="text-gray-600">
                {format(new Date(parsedTrip.startDate), 'MMM d')} - {format(new Date(parsedTrip.endDate), 'MMM d, yyyy')}
              </p>
              {parsedTrip.destination && (
                <div className="flex items-center text-gray-600">
                  <MapPin className="w-4 h-4 mr-1" />
                  {parsedTrip.destination}
                </div>
              )}
            </div>
            <Button 
              variant="outline" 
              size="sm"
              onClick={() => {
                setShowEditModal(true);
                trackPreviewEdit('title');
              }}
              aria-label="Edit trip details"
            >
              <Edit className="w-4 h-4 mr-2" aria-hidden="true" />
              Edit Details
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          {/* Quick Stats with animated numbers */}
          <div className="grid grid-cols-4 gap-4 mb-6" role="list" aria-label="Trip statistics">
            <div className="text-center" role="listitem">
              <Calendar className="w-6 h-6 text-gray-400 mx-auto mb-1" aria-hidden="true" />
              <p className="text-2xl font-bold">
                <DynamicNumberTicker 
                  value={stats.days} 
                  springConfig="quick"
                  delay={0}
                /> days
              </p>
              <p className="text-xs text-gray-600" aria-hidden="true">Days</p>
            </div>
            <div className="text-center" role="listitem">
              <MapPin className="w-6 h-6 text-gray-400 mx-auto mb-1" aria-hidden="true" />
              <p className="text-2xl font-bold">
                <DynamicNumberTicker 
                  value={stats.activities} 
                  springConfig="quick"
                  delay={100}
                /> {stats.activities === 1 ? "activity" : "activities"}
              </p>
              <p className="text-xs text-gray-600" aria-hidden="true">Activities</p>
            </div>
            <div className="text-center" role="listitem">
              <Clock className="w-6 h-6 text-gray-400 mx-auto mb-1" aria-hidden="true" />
              <p className="text-2xl font-bold">
                <DynamicNumberTicker 
                  value={stats.locations} 
                  springConfig="quick"
                  delay={200}
                /> {stats.locations === 1 ? "location" : "locations"}
              </p>
              <p className="text-xs text-gray-600" aria-hidden="true">Locations</p>
            </div>
            <div className="text-center" role="listitem">
              <DollarSign className="w-6 h-6 text-gray-400 mx-auto mb-1" aria-hidden="true" />
              <p className="text-2xl font-bold">
                $<DynamicNumberTicker 
                  value={stats.estimatedBudget} 
                  springConfig="quick"
                  delay={300}
                />
              </p>
              <p className="text-xs text-gray-600" aria-hidden="true">Est. Budget</p>
            </div>
          </div>

          {/* Parse Quality */}
          <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
            <span className="text-sm font-medium">Import Quality</span>
            <div className="flex items-center gap-2">
              <Badge className="text-green-600 bg-green-50">
                AI Parsed
              </Badge>
              <Badge variant="outline">
                {parsedTrip.metadata?.source || 'unknown'} detected
              </Badge>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Preview Tabs */}
      <Tabs value={activeTab} onValueChange={(v) => setActiveTab(v as any)} aria-label="Trip preview options">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="timeline" aria-label="View trip timeline">Timeline View</TabsTrigger>
          <TabsTrigger value="map" aria-label="View trip on map">Map View</TabsTrigger>
          <TabsTrigger value="globe" aria-label="View trip on 3D globe">Globe View</TabsTrigger>
        </TabsList>

        <TabsContent value="timeline" className="mt-6">
          <Card>
            <CardContent className="p-6" role="tabpanel" aria-label="Timeline view">
              <MiniTimeline activities={parsedTrip.activities} />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="map" className="mt-6">
          <Card>
            <CardContent className="p-6" role="tabpanel" aria-label="Map view">
              <div className="h-[400px]" role="application" aria-label="Interactive trip map">
                <MiniMap activities={parsedTrip.activities.map((a: any, i: number) => ({ ...a, id: `activity-${i}` }))} />
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="globe" className="mt-6">
          <Card>
            <CardContent className="p-6" role="tabpanel" aria-label="3D globe view">
              <div className="h-[400px]" role="application" aria-label="Interactive 3D globe showing trip route">
                <TripGlobe 
                  activities={parsedTrip.activities}
                  onCityClick={(cityName) => {
                    console.log('City clicked:', cityName);
                    // Could implement scrolling to that city in timeline
                  }}
                />
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* AI Parsing Info */}
      {parsedTrip.metadata?.warnings && parsedTrip.metadata.warnings.length > 0 && (
        <Card className="border-amber-200 bg-amber-50" role="alert" aria-live="polite">
          <CardHeader className="pb-3">
            <div className="flex items-center gap-2">
              <AlertCircle className="w-5 h-5 text-amber-600" aria-hidden="true" />
              <h3 className="font-medium text-amber-900" id="parsing-warnings-title">
                AI Parsing Warnings
              </h3>
            </div>
          </CardHeader>
          <CardContent>
            <ul className="space-y-2" role="list" aria-labelledby="parsing-warnings-title">
              {parsedTrip.metadata.warnings.map((warning: any, i: number) => (
                <li key={i} className="text-sm text-amber-800">
                  {warning}
                </li>
              ))}
            </ul>
          </CardContent>
        </Card>
      )}

      {/* Action Buttons */}
      <div className="flex items-center justify-between">
        <Button
          variant="outline"
          onClick={() => setStep('input')}
          aria-label="Start over with a new import"
        >
          Start Over
        </Button>

        <div className="flex gap-3">
          <Button
            variant="outline"
            onClick={handleViewParsed}
            className="min-w-[150px]"
            aria-label="View the parsed trip details"
          >
            View Parsed Trip
            <ArrowRight className="ml-2 h-4 w-4" aria-hidden="true" />
          </Button>
          
          <ShimmerButton
            onClick={handleCreateTrip}
            disabled={isCreating}
            className="min-w-[200px]"
            aria-label={isCreating ? "Creating trip, please wait" : "Save trip to your account"}
            aria-busy={isCreating}
          >
            {isCreating ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" aria-hidden="true" />
                <MorphingText 
                  text={currentMessage}
                  speed="normal"
                  variant="default"
                  as="span"
                  className="text-white"
                />
              </>
            ) : (
              <>
                <Sparkles className="w-4 h-4 mr-2" aria-hidden="true" />
                Save to My Trips
                <ArrowRight className="ml-2 h-4 w-4" aria-hidden="true" />
              </>
            )}
          </ShimmerButton>
        </div>
      </div>
    </div>
  );
}