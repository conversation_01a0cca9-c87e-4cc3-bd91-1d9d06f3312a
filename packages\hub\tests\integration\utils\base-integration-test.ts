import { beforeAll, afterAll, beforeEach, afterEach } from 'vitest';
import { testServer } from './test-server';
import { testCleanup } from './test-cleanup';
import { TestFixtures, cleanTestData } from './test-fixtures';

/**
 * Base class for integration tests
 * Provides common setup, teardown, and utilities
 */
export abstract class BaseIntegrationTest {
  protected server = testServer;
  protected fixtures = TestFixtures;
  protected cleanData = cleanTestData;
  protected cleanup = testCleanup;

  /**
   * Set up test environment
   * Call this in beforeAll
   */
  async setupTest(): Promise<void> {
    await this.server.setup();
  }

  /**
   * Tear down test environment  
   * Call this in afterAll
   */
  async teardownTest(): Promise<void> {
    await this.cleanup.cleanupAll();
    await this.server.teardown();
  }

  /**
   * Clean up between tests
   * Call this in afterEach
   */
  async cleanupBetweenTests(): Promise<void> {
    await this.cleanup.cleanupAll();
    this.cleanup.reset();
  }

  /**
   * Helper methods for common operations
   */
  
  /**
   * Create a test trip and track for cleanup
   */
  async createTestTrip(tripData = this.fixtures.trips.basic) {
    const response = await this.server.post('/api/v1/trips', tripData);
    if (response.body?.data?.trip?.id) {
      this.cleanup.trackTestData('trips', response.body.data.trip.id);
    }
    return response;
  }

  /**
   * Import conversation and track for cleanup
   */
  async importConversation(conversation: string, source = 'chatgpt') {
    const response = await this.server.post('/api/v1/import/parse-simple', {
      content: conversation,
      source
    });
    if (response.body?.data?.importId) {
      this.cleanup.trackTestData('import_requests', response.body.data.importId);
    }
    return response;
  }

  /**
   * Add activity to trip and track for cleanup
   */
  async addActivityToTrip(tripId: string, activityData = this.fixtures.activities.museum) {
    const response = await this.server.post(`/api/v1/trips/${tripId}/activities`, activityData);
    if (response.body?.data?.activity?.id) {
      this.cleanup.trackTestData('activities', response.body.data.activity.id);
    }
    return response;
  }

  /**
   * Wait for async operations (like AI parsing)
   */
  async waitForProcessing(ms = 2000): Promise<void> {
    await new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Check if response matches expected pattern
   */
  expectResponsePattern(response: any, pattern: any) {
    expect(response.status).toBe(200);
    expect(response.body.success).toBe(pattern.success);
    
    // Check if data structure matches
    if (pattern.data) {
      expect(response.body.data).toBeDefined();
      
      // Check for required properties
      Object.keys(pattern.data).forEach(key => {
        if (pattern.data[key] === true) {
          expect(response.body.data[key]).toBeDefined();
        } else if (typeof pattern.data[key] === 'object') {
          expect(response.body.data[key]).toBeDefined();
          Object.keys(pattern.data[key]).forEach(subKey => {
            if (pattern.data[key][subKey] === true) {
              expect(response.body.data[key][subKey]).toBeDefined();
            }
          });
        }
      });
    }
  }

  /**
   * Test authentication requirement
   */
  async testAuthRequired(endpoint: string, method = 'POST', data?: any) {
    let response;
    switch (method.toUpperCase()) {
      case 'POST':
        response = await this.server.request().post(endpoint).send(data || {});
        break;
      case 'GET':
        response = await this.server.request().get(endpoint);
        break;
      case 'PUT':
        response = await this.server.request().put(endpoint).send(data || {});
        break;
      case 'DELETE':
        response = await this.server.request().delete(endpoint);
        break;
      default:
        throw new Error(`Unsupported method: ${method}`);
    }
    
    expect(response.status).toBe(401);
    return response;
  }
}

/**
 * Integration test setup helpers
 */
export const setupIntegrationTest = () => {
  const baseTest = new (class extends BaseIntegrationTest {})();
  
  beforeAll(async () => {
    await baseTest.setupTest();
  });

  afterAll(async () => {
    await baseTest.teardownTest();
  });

  afterEach(async () => {
    await baseTest.cleanupBetweenTests();
  });

  return baseTest;
};