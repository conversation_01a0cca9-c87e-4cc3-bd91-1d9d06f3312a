# MVP Daily Task Checklist

**Purpose**: Clear definition of "done" for each day  
**For**: Development team execution  
**Timeline**: 15 days to testable MVP

---

## Day 1: Unblock Deployment ✓

### Morning (4 hours)

- [ ] Fix shared package exports
  - [ ] Export `ActivityType` enum from shared
  - [ ] Export `Activity` interface with affiliate fields
  - [ ] Run `pnpm type-check` - must show 0 errors in shared

- [ ] Fix server.ts issues
  - [ ] Remove duplicate cors import
  - [ ] Fix errorHandler import name
  - [ ] Verify hub starts without errors

### Afternoon (4 hours)

- [ ] Fix remaining TypeScript errors
  - [ ] Run `pnpm type-check` in root
  - [ ] All 37 errors must be resolved
  - [ ] Build must complete successfully

### Definition of Done

```bash
pnpm build  # Completes without errors
pnpm dev    # Both hub and web start successfully
```

---

## Day 2: Critical Security ✓

### Morning (4 hours)

- [ ] JWT Security
  - [ ] Remove hardcoded fallback in tokens.ts
  - [ ] Test: App fails to start without JWT_SECRET env var

- [ ] Foreign Key Fixes
  - [ ] Create migration 010_fix_foreign_keys.sql
  - [ ] Run migration on local database
  - [ ] Verify: Can insert into search_history and trip_clones

### Afternoon (4 hours)

- [ ] Re-enable RLS
  - [ ] Create migration 011_enable_rls_with_policies.sql
  - [ ] Add policies for trips and activities
  - [ ] Test: User A cannot access User B's trips

- [ ] Ownership Middleware
  - [ ] Create ownership.middleware.ts
  - [ ] Apply to all trip/activity routes
  - [ ] Test: 403 error when accessing other user's data

### Definition of Done

```bash
# Security test script passes
curl -X GET localhost:3001/api/v1/trips/[other-user-trip-id]
# Returns 403 Forbidden
```

---

## Day 3: Performance Crisis Prevention ✓

### Morning (3 hours)

- [ ] Database Indexes
  - [ ] Create migration 012_performance_indexes.sql
  - [ ] Add indexes for: user+created_at, position, created_at
  - [ ] Run EXPLAIN on getUserTrips query - must use indexes

### Afternoon (3 hours)

- [ ] Pagination Implementation
  - [ ] Update getUserTrips to accept page/limit params
  - [ ] Return total count with results
  - [ ] Update frontend to handle paginated response

### Definition of Done

```bash
# Load test passes
npm run test:load  # 100 concurrent users, <500ms response time
```

---

## Days 4-5: AI Import UI (Most Critical) ✓

### Day 4 Morning (4 hours)

- [ ] Create Import Page Structure
  - [ ] /app/import/page.tsx created
  - [ ] Basic layout with tabs (Paste/Upload)
  - [ ] Textarea for paste functionality
  - [ ] Route accessible from dashboard

### Day 4 Afternoon (4 hours)

- [ ] Import Wizard Component
  - [ ] Step indicator (Input → Processing → Preview)
  - [ ] Paste text functionality working
  - [ ] Character count display
  - [ ] Submit button triggers API call

### Day 5 Morning (4 hours)

- [ ] Parsing Animation
  - [ ] Progress bar component
  - [ ] Step-by-step status updates
  - [ ] Animated icons for each parsing phase
  - [ ] Loading state prevents navigation

### Day 5 Afternoon (4 hours)

- [ ] Error Handling
  - [ ] Show specific error messages
  - [ ] Allow retry on failure
  - [ ] Graceful handling of partial data
  - [ ] Success redirects to new trip

### Definition of Done

```
1. Navigate to /import
2. Paste ChatGPT conversation
3. See parsing animation
4. Successfully create trip
5. Redirect to trip page with parsed data
```

---

## Days 6-7: Visual Display Integration ✓

### Day 6 (8 hours)

- [ ] Connect Import to Backend
  - [ ] API endpoint accepts text input
  - [ ] Returns parsed trip data
  - [ ] Frontend displays preview
  - [ ] Create trip from parsed data

### Day 7 (8 hours)

- [ ] Polish Import Flow
  - [ ] Preview shows timeline + map
  - [ ] Quick edit for activities
  - [ ] Confidence indicators
  - [ ] Share immediately after import

### Definition of Done

```
Import 5 different ChatGPT conversations
All 5 create valid trips with timeline + map
```

---

## Day 8: Testing & Polish ✓

### All Day (8 hours)

- [ ] Edge Case Testing
  - [ ] Import with partial dates
  - [ ] Import with vague locations
  - [ ] Import with no activities
  - [ ] Import non-English conversation

- [ ] UI Polish
  - [ ] Mobile responsive
  - [ ] Loading states smooth
  - [ ] Error messages helpful
  - [ ] Success feels magical

### Definition of Done

```
85% success rate on 20 test conversations
Mobile UI works perfectly
Zero crashes or hangs
```

---

## Day 9: Viral Sharing ✓

### All Day (8 hours)

- [ ] Public Trip Enhancement
  - [ ] Beautiful public trip page
  - [ ] "Copy to My Account" CTA prominent
  - [ ] OG meta tags for social sharing
  - [ ] Track views and copies

### Definition of Done

```
Share link on Twitter
Preview card looks professional
One-click copy works
Analytics tracks shares
```

---

## Days 10-11: Basic Monetization ✓

### Day 10 (8 hours)

- [ ] Stripe Payment Links
  - [ ] Create Payment Links in Stripe Dashboard
  - [ ] Add upgrade CTA to UI
  - [ ] Track who has paid in database
  - [ ] Show Pro badge for paid users

### Day 11 (8 hours)

- [ ] Affiliate Polish
  - [ ] Ensure Travelpayouts links work
  - [ ] Add affiliate disclosure
  - [ ] Track clicks properly
  - [ ] Test booking flow

### Definition of Done

```
Can upgrade to Pro via Stripe
Affiliate links track clicks
At least one test booking works
```

---

## Day 12: User Testing Prep ✓

### All Day (8 hours)

- [ ] Demo Content
  - [ ] Create 5 impressive demo trips
  - [ ] Record 2-minute demo video
  - [ ] Write FAQ document
  - [ ] Set up Canny for feedback

### Definition of Done

```
5 demo trips live and shareable
Demo video uploaded
FAQ answers common questions
Feedback system ready
```

---

## Days 13-14: Scale Preparation ✓

### Day 13 (8 hours)

- [ ] Basic Monitoring
  - [ ] Sentry error tracking configured
  - [ ] Health check endpoint working
  - [ ] Basic analytics (PostHog lite)
  - [ ] Uptime monitoring active

### Day 14 (8 hours)

- [ ] Performance Testing
  - [ ] Load test with 100 users
  - [ ] Fix any bottlenecks found
  - [ ] Cache public trips
  - [ ] Optimize import processing

### Definition of Done

```
100 concurrent users supported
All errors tracked in Sentry
<3 second page loads
Zero crashes under load
```

---

## Day 15: LAUNCH! 🚀

### Morning (4 hours)

- [ ] Final Checks
  - [ ] All tests passing
  - [ ] Database backed up
  - [ ] Environment variables verified
  - [ ] Domain configured

### Afternoon (4 hours)

- [ ] Go Live
  - [ ] Deploy to production
  - [ ] Send to first 10 beta users
  - [ ] Monitor everything
  - [ ] Celebrate! 🎉

### Definition of Done

```
Live URL working
First user successfully imports trip
No critical errors in first hour
Team beer opened
```

---

## Daily Standup Questions

1. **What did I complete yesterday?**
2. **What will I complete today?**
3. **What's blocking me?**
4. **Will we hit Day 15 launch?**

## If Behind Schedule

**Cut these first:**

1. Polish animations
2. Multiple demo trips
3. Advanced error handling
4. Mobile optimizations

**Never cut:**

1. Security fixes
2. AI import functionality
3. Basic sharing
4. Performance minimums

---

_Remember: Done is better than perfect. Ship it._
