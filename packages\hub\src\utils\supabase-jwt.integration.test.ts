import { describe, it, expect, vi, beforeEach } from 'vitest';
import { verifySupabaseJWT, isTokenExpired, SupabaseJWTError } from './supabase-jwt';
import { getSupabaseClient } from '../lib/supabase';
import jwt from 'jsonwebtoken';

// Mock Supabase client
vi.mock('../lib/supabase');
vi.mock('../utils/logger');

describe('Supabase JWT Integration - Cascade Prevention', () => {
  const mockSupabaseClient = {
    auth: {
      getUser: vi.fn(),
    },
  };

  beforeEach(() => {
    vi.clearAllMocks();
    vi.mocked(getSupabaseClient).mockReturnValue(mockSupabaseClient as any);
  });

  describe('verifySupabaseJWT - Critical Failure Points', () => {
    it('should fail fast when Supabase auth.getUser returns error', async () => {
      // This is the ROOT CAUSE - Supabase auth.getUser fails
      const token = jwt.sign({ sub: 'user-123', email: '<EMAIL>' }, 'secret', {
        expiresIn: '1h',
      });

      mockSupabaseClient.auth.getUser.mockResolvedValueOnce({
        data: { user: null },
        error: { message: 'Invalid token', status: 401 },
      });

      await expect(verifySupabaseJWT(token)).rejects.toThrow(SupabaseJWTError);
      await expect(verifySupabaseJWT(token)).rejects.toThrow('Invalid token');
    });

    it('should handle network failures gracefully', async () => {
      const token = jwt.sign({ sub: 'user-123', email: '<EMAIL>' }, 'secret');
      
      mockSupabaseClient.auth.getUser.mockRejectedValueOnce(
        new Error('Network error: ECONNREFUSED')
      );

      await expect(verifySupabaseJWT(token)).rejects.toThrow(SupabaseJWTError);
    });

    it('should reject expired tokens before calling Supabase', async () => {
      // Token expired 1 hour ago
      const token = jwt.sign(
        { sub: 'user-123', email: '<EMAIL>' },
        'secret',
        { expiresIn: '-1h' }
      );

      await expect(verifySupabaseJWT(token)).rejects.toThrow('Token expired');
      
      // Should NOT call Supabase for expired token
      expect(mockSupabaseClient.auth.getUser).not.toHaveBeenCalled();
    });

    it('should reject malformed tokens immediately', async () => {
      const malformedToken = 'not.a.valid.jwt';

      await expect(verifySupabaseJWT(malformedToken)).rejects.toThrow(
        'Invalid token structure'
      );
      
      // Should NOT call Supabase for malformed token
      expect(mockSupabaseClient.auth.getUser).not.toHaveBeenCalled();
    });

    it('should transform Supabase user to expected format', async () => {
      const token = jwt.sign({ sub: 'user-123', email: '<EMAIL>' }, 'secret');
      
      const supabaseUser = {
        id: 'user-123',
        email: '<EMAIL>',
        user_metadata: { name: 'Test User' },
        app_metadata: { role: 'user' },
        role: 'authenticated',
      };

      mockSupabaseClient.auth.getUser.mockResolvedValueOnce({
        data: { user: supabaseUser },
        error: null,
      });

      const result = await verifySupabaseJWT(token);

      expect(result).toEqual({
        id: 'user-123',
        email: '<EMAIL>',
        name: 'Test User',
        role: 'authenticated',
        app_metadata: { role: 'user' },
        user_metadata: { name: 'Test User' },
      });
    });
  });

  describe('Token Validation Helpers', () => {
    it('should correctly identify expired tokens', () => {
      const expiredToken = jwt.sign(
        { sub: 'user-123', exp: Math.floor(Date.now() / 1000) - 3600 },
        'secret'
      );
      
      expect(isTokenExpired(expiredToken)).toBe(true);
    });

    it('should handle tokens with buffer time', () => {
      // Token expires in 3 seconds (less than 5 second buffer)
      const almostExpiredToken = jwt.sign(
        { sub: 'user-123', exp: Math.floor(Date.now() / 1000) + 3 },
        'secret'
      );
      
      expect(isTokenExpired(almostExpiredToken)).toBe(true);
    });

    it('should handle invalid token gracefully', () => {
      expect(isTokenExpired('invalid.token')).toBe(true);
      expect(isTokenExpired('')).toBe(true);
      expect(isTokenExpired(null as any)).toBe(true);
    });
  });

  describe('Error Handling', () => {
    it('should preserve error type for debugging', async () => {
      const token = jwt.sign({ sub: 'user-123' }, 'secret');
      
      mockSupabaseClient.auth.getUser.mockResolvedValueOnce({
        data: { user: null },
        error: { message: 'Custom error', code: 'CUSTOM_CODE' },
      });

      try {
        await verifySupabaseJWT(token);
        expect.fail('Should have thrown');
      } catch (error) {
        expect(error).toBeInstanceOf(SupabaseJWTError);
        expect(error.message).toBe('Invalid token');
      }
    });
  });
});