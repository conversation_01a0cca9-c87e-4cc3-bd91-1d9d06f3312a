import { describe, it, expect, beforeEach, vi } from 'vitest';
import { TestDataBuilder, TripBuilder, ActivityBuilder, ConversationBuilder } from './TestDataBuilder';
import { faker } from '@faker-js/faker';

// Mock faker to have predictable values in tests
vi.mock('@faker-js/faker', () => ({
  faker: {
    string: {
      uuid: vi.fn(() => 'test-uuid'),
    },
    location: {
      city: vi.fn(() => 'TestCity'),
      streetAddress: vi.fn(() => '123 Test St'),
    },
    date: {
      future: vi.fn(() => new Date('2024-12-01')),
    },
    lorem: {
      words: vi.fn((count) => 'word '.repeat(count).trim()),
      paragraphs: vi.fn((count) => 'paragraph '.repeat(count).trim()),
      paragraph: vi.fn(() => 'test paragraph content'),
    },
    company: {
      name: vi.fn(() => 'TestCompany'),
    },
  },
}));

describe('TripBuilder', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('should create a valid default trip', () => {
    const trip = new TripBuilder().build();

    expect(trip).toMatchObject({
      id: 'test-uuid',
      user_id: 'test-uuid',
      title: 'TestCity Adventure',
      status: 'draft',
      visibility: 'private',
      metadata: {},
      tags: [],
      budget_currency: 'USD',
    });
    expect(trip.created_at).toBeDefined();
    expect(trip.updated_at).toBeDefined();
  });

  it('should allow custom overrides', () => {
    const trip = new TripBuilder()
      .with({
        title: 'Custom Trip',
        status: 'published',
        budget_currency: 'EUR',
      })
      .build();

    expect(trip.title).toBe('Custom Trip');
    expect(trip.status).toBe('published');
    expect(trip.budget_currency).toBe('EUR');
  });

  it('should handle partial dates', () => {
    const trip = new TripBuilder()
      .withPartialDates()
      .build();

    expect(trip.start_date).toBeUndefined();
    expect(trip.end_date).toBe('2024-12-01T00:00:00.000Z');
  });

  it('should create mixed language content', () => {
    const trip = new TripBuilder()
      .withMixedLanguages()
      .build();

    expect(trip.title).toBe('TestCity 旅行 - Voyage à TestCity');
    expect(trip.description).toBe('Un voyage incroyable 素晴らしい旅 amazing trip');
  });

  it('should create long content', () => {
    const trip = new TripBuilder()
      .withLongContent()
      .build();

    expect(trip.title).toContain('word');
    expect(trip.description).toContain('paragraph');
  });

  it('should create corrupted data with security warning', () => {
    const trip = new TripBuilder()
      .withCorruptedData()
      .build();

    expect(trip.title).toBe('���� Corrupted ����');
    expect(trip.metadata).toEqual({ 
      corrupt: '\u0000\u0001\u0002',
      warning: 'TEST_DATA_ONLY_DO_NOT_USE_IN_PRODUCTION'
    });
    
    // Security test: ensure null bytes are contained in test environment
    expect(trip.metadata?.corrupt).toMatch(/\u0000/);
  });
});

describe('ActivityBuilder', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('should create a valid default activity', () => {
    const activity = new ActivityBuilder().build();

    expect(activity).toMatchObject({
      id: 'test-uuid',
      trip_id: 'test-uuid',
      title: 'TestCompany Visit',
      type: 'activity',
      position: 0,
      metadata: {},
      attachments: [],
      currency: 'USD',
    });
    expect(activity.created_at).toBeDefined();
    expect(activity.updated_at).toBeDefined();
  });

  it('should handle partial location data', () => {
    const activity = new ActivityBuilder()
      .withPartialLocation()
      .build();

    expect(activity.location).toEqual({
      address: '123 Test St',
      // Missing lat/lng intentionally
    });
  });

  it('should handle vague time formats', () => {
    const activity = new ActivityBuilder()
      .withVagueTime()
      .build();

    expect(activity.start_time).toBe('sometime in the morning');
    expect(activity.end_time).toBe('late afternoon');
  });

  it('should handle multiple price structures', () => {
    const activity = new ActivityBuilder()
      .withMultiplePrices()
      .build();

    expect(activity.price).toBe(100);
    expect(activity.metadata).toEqual({
      prices: {
        adult: 100,
        child: 50,
        senior: 75,
      },
      currencies: ['USD', 'EUR', 'JPY'],
    });
  });

  it('should handle emoji content', () => {
    const activity = new ActivityBuilder()
      .withEmoji()
      .build();

    expect(activity.title).toBe('🌟 Amazing Place 🎉');
    expect(activity.notes).toBe('Must see! 👀 Don\'t forget camera 📸');
  });
});

describe('ConversationBuilder', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('should build empty conversation', () => {
    const conversation = new ConversationBuilder().build();
    expect(conversation).toBe('');
  });

  it('should add user and assistant messages', () => {
    const conversation = new ConversationBuilder()
      .addUserMessage('Hello')
      .addAssistantMessage('Hi there!')
      .build();

    expect(conversation).toBe('User: Hello\n\nAssistant: Hi there!');
  });

  it('should create simple itinerary conversation', () => {
    const conversation = new ConversationBuilder()
      .withSimpleItinerary()
      .build();

    expect(conversation).toContain('Plan a 3-day trip to Paris');
    expect(conversation).toContain('Day 1:');
    expect(conversation).toContain('Eiffel Tower');
    expect(conversation).toContain('Louvre Museum');
  });

  it('should create complex multi-city conversation', () => {
    const conversation = new ConversationBuilder()
      .withComplexMultiCity()
      .build();

    expect(conversation).toContain('London, Paris, and Rome');
    expect(conversation).toContain('paragraph');
  });

  it('should create partial dates conversation', () => {
    const conversation = new ConversationBuilder()
      .withPartialDates()
      .build();

    expect(conversation).toContain('Sometime in March');
    expect(conversation).toContain('A few days later');
    expect(conversation).toContain('End of the month');
  });

  it('should create mixed languages conversation', () => {
    const conversation = new ConversationBuilder()
      .withMixedLanguages()
      .build();

    expect(conversation).toContain('Jour 1 - パリ');
    expect(conversation).toContain('Tour Eiffel 参观');
    expect(conversation).toContain('Musée du Louvre');
  });

  it('should create corrupted text with replacement characters', () => {
    const conversation = new ConversationBuilder()
      .withCorruptedText()
      .build();

    expect(conversation).toContain('�');
    expect(conversation).toContain('Assistant:');
  });
});

describe('TestDataBuilder static methods', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('should create trip builder', () => {
    const builder = TestDataBuilder.trip();
    expect(builder).toBeInstanceOf(TripBuilder);
  });

  it('should create activity builder', () => {
    const builder = TestDataBuilder.activity();
    expect(builder).toBeInstanceOf(ActivityBuilder);
  });

  it('should create conversation builder', () => {
    const builder = TestDataBuilder.conversation();
    expect(builder).toBeInstanceOf(ConversationBuilder);
  });

  it('should generate multiple trips', () => {
    const trips = TestDataBuilder.trips(3);
    
    expect(trips).toHaveLength(3);
    trips.forEach(trip => {
      expect(trip).toHaveProperty('id');
      expect(trip).toHaveProperty('title');
    });
  });

  it('should generate trips with modifier function', () => {
    const trips = TestDataBuilder.trips(2, (builder, index) => {
      builder.with({ title: `Trip ${index}` });
    });
    
    expect(trips[0].title).toBe('Trip 0');
    expect(trips[1].title).toBe('Trip 1');
  });

  it('should generate multiple activities for a trip', () => {
    const tripId = 'test-trip-id';
    const activities = TestDataBuilder.activities(3, tripId);
    
    expect(activities).toHaveLength(3);
    activities.forEach((activity, index) => {
      expect(activity.trip_id).toBe(tripId);
      expect(activity.position).toBe(index);
    });
  });

  it('should generate activities with modifier function', () => {
    const activities = TestDataBuilder.activities(2, 'trip-id', (builder, index) => {
      builder.with({ title: `Activity ${index}` });
    });
    
    expect(activities[0].title).toBe('Activity 0');
    expect(activities[1].title).toBe('Activity 1');
  });
});

describe('Security considerations', () => {
  it('should handle corrupted data safely in controlled test environment', () => {
    const trip = new TripBuilder().withCorruptedData().build();
    
    // Ensure corrupted data is isolated to test scenarios
    expect(typeof trip.metadata?.corrupt).toBe('string');
    
    // Test that JSON stringification doesn't crash
    expect(() => JSON.stringify(trip)).not.toThrow();
  });

  it('should prevent null byte injection in production usage', () => {
    const trip = new TripBuilder().withCorruptedData().build();
    
    // In real applications, this data should be sanitized before use
    const sanitized = JSON.parse(JSON.stringify(trip));
    expect(sanitized.metadata.corrupt).toBeDefined();
    
    // Warning: This test data should never be used in production
    console.warn('⚠️ TestDataBuilder.withCorruptedData() is for testing only - never use in production');
  });
});