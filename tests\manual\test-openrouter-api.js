// Direct test of OpenRouter API to see if it's hanging
const axios = require('axios');

const OPENROUTER_API_KEY = '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';

async function testOpenRouterAPI() {
  console.log('🔍 TESTING OPENROUTER API DIRECTLY');
  console.log('Time:', new Date().toISOString());
  console.log('');

  const testContent = `Your Complete 15-Day European Adventure: London, Madrid, Lisbon & Porto

LONDON: July 23-25 (2 Nights)

Day 1 - Thursday, July 23: Arrival & Sky Garden Sunset
Why Sky Garden Over London Eye: While tourists pay £30 for the slow-moving London Eye, locals know Sky Garden offers superior 360° views absolutely FREE.

Day 2 - Friday, July 24: Markets & Culture
Morning: Borough Market Masters Class
Strategic Eating Route:
1. Start: Monmouth Coffee - flat white £3.50
2. Breakfast: Maria's Market Cafe - full English £12

MADRID: July 25-29 (4 Nights)
Day 3 - Saturday, July 25: Travel & Arrival
Day 4 - Sunday, July 26: Art Triangle - Prado Museum (€15), Reina Sofia Museum (€12)

LISBON: July 29-Aug 1 (3 Nights)
Day 7 - Tuesday, July 29: Travel & Arrival
Day 8 - Wednesday, July 30: Historic Lisbon - Jerónimos Monastery (€10)

PORTO: Aug 1-6 (5 Nights)
Day 10 - Friday, Aug 1: Travel & Arrival
Day 11 - Saturday, Aug 2: Porto Highlights

Budget Summary: €2,750 total`;

  const systemPrompt = `You are a travel itinerary parser. Parse the provided travel conversation into a structured JSON format.

Return ONLY valid JSON in this exact format:
{
  "title": "Trip title",
  "description": "Brief description",
  "destination": "Main destination",
  "startDate": "YYYY-MM-DD",
  "endDate": "YYYY-MM-DD",
  "activities": [
    {
      "title": "Activity name",
      "description": "Activity description",
      "location": "Location name",
      "date": "YYYY-MM-DD",
      "time": "HH:MM",
      "duration": 120,
      "price": 25.50,
      "currency": "EUR",
      "type": "activity",
      "day": 1
    }
  ]
}`;

  // Test different models to see which ones work
  const modelsToTest = [
    'deepseek/deepseek-chat-v3-0324:free',
    'google/gemini-flash-1.5',
    'anthropic/claude-3-haiku',
    'openai/gpt-3.5-turbo'
  ];

  for (const model of modelsToTest) {
    console.log(`\n🧪 Testing model: ${model}`);
    const startTime = Date.now();
    
    try {
      console.log('  Making API call...');
      
      const response = await Promise.race([
        axios.post(
          'https://openrouter.ai/api/v1/chat/completions',
          {
            model: model,
            messages: [
              {
                role: 'system',
                content: systemPrompt
              },
              {
                role: 'user',
                content: testContent
              }
            ],
            temperature: 0.3,
            max_tokens: 4000,
            response_format: { type: 'json_object' }
          },
          {
            headers: {
              'Authorization': `Bearer ${OPENROUTER_API_KEY}`,
              'Content-Type': 'application/json',
              'HTTP-Referer': 'https://travelviz.app',
              'X-Title': 'TravelViz'
            },
            timeout: 45000 // 45 second timeout
          }
        ),
        new Promise((_, reject) => {
          setTimeout(() => {
            reject(new Error(`Manual timeout after 45 seconds for model ${model}`));
          }, 45000);
        })
      ]);

      const duration = Date.now() - startTime;
      const aiResponse = response.data.choices[0]?.message?.content;
      
      if (!aiResponse) {
        console.log(`  ❌ No response content from ${model} (${duration}ms)`);
        continue;
      }

      // Try to parse JSON
      try {
        const parsed = JSON.parse(aiResponse);
        console.log(`  ✅ ${model} SUCCESS (${duration}ms)`);
        console.log(`     Activities parsed: ${parsed.activities?.length || 0}`);
        console.log(`     Response length: ${aiResponse.length} chars`);
      } catch (parseError) {
        console.log(`  ⚠️ ${model} returned invalid JSON (${duration}ms)`);
        console.log(`     Response preview: ${aiResponse.substring(0, 100)}...`);
      }

    } catch (error) {
      const duration = Date.now() - startTime;
      console.log(`  ❌ ${model} FAILED (${duration}ms)`);
      
      if (error.code === 'ECONNABORTED' || error.message.includes('timeout')) {
        console.log(`     Error: TIMEOUT - API call hung for 45+ seconds`);
      } else if (error.response) {
        console.log(`     Error: HTTP ${error.response.status} - ${error.response.data?.error?.message || error.response.statusText}`);
      } else {
        console.log(`     Error: ${error.message}`);
      }
    }
  }

  console.log('\n🎯 OPENROUTER API TEST SUMMARY:');
  console.log('If any models timed out, this explains why AI processing gets stuck.');
  console.log('If all models work, the issue is in the application code.');
}

// Run the test
testOpenRouterAPI().catch(console.error);
