'use client';

import React, { createContext, useContext, ReactNode } from 'react';
import type { AnalyticsProvider, AnalyticsConfig } from '@travelviz/shared';
import { PostHogAnalyticsProvider } from '../lib/analytics/posthog-adapter';

interface AnalyticsContextValue {
  analytics: AnalyticsProvider;
}

const AnalyticsContext = createContext<AnalyticsContextValue | undefined>(undefined);

interface AnalyticsProviderProps {
  children: ReactNode;
  provider?: AnalyticsProvider;
  config?: AnalyticsConfig;
}

/**
 * Analytics Provider component that provides analytics functionality to child components
 */
export function AnalyticsProvider({ 
  children, 
  provider,
  config = {
    provider: 'posthog',
    apiKey: process.env.NEXT_PUBLIC_POSTHOG_KEY,
    apiHost: process.env.NEXT_PUBLIC_POSTHOG_HOST || 'https://app.posthog.com',
    debug: process.env.NODE_ENV === 'development',
    disabled: process.env.NODE_ENV === 'test',
  }
}: AnalyticsProviderProps) {
  // Use provided provider or create default PostHog provider
  const analytics = provider || new PostHogAnalyticsProvider(config);

  return (
    <AnalyticsContext.Provider value={{ analytics }}>
      {children}
    </AnalyticsContext.Provider>
  );
}

/**
 * Hook to access analytics provider
 */
export function useAnalytics(): AnalyticsProvider {
  const context = useContext(AnalyticsContext);
  
  if (!context) {
    throw new Error('useAnalytics must be used within an AnalyticsProvider');
  }
  
  return context.analytics;
}

/**
 * Hook to check if analytics is available
 */
export function useAnalyticsAvailable(): boolean {
  const context = useContext(AnalyticsContext);
  return context !== undefined;
}