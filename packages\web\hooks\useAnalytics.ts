import { useCallback, useEffect, useRef } from 'react';
import { track } from '@vercel/analytics';
// TODO: Fix @vercel/speed-insights import path
// import { metric } from '@vercel/speed-insights';

export interface AnalyticsContext {
  trackEvent: (name: string, properties?: Record<string, any>) => void;
  trackError: (error: Error, context?: Record<string, any>) => void;
  trackPerformance: (name: string, value: number, unit?: 'ms' | 'bytes' | 'count') => void;
  trackConversion: (type: 'import_completed' | 'trip_created' | 'trip_shared', properties?: Record<string, any>) => void;
  trackImportEvent: (step: 'input' | 'parsing' | 'preview' | 'creating' | 'completed', properties?: Record<string, any>) => void;
  trackClick: (element: string, properties?: Record<string, any>) => void;
  trackFormSubmit: (formName: string, success: boolean, properties?: Record<string, any>) => void;
  trackSearch: (query: string, results: number, properties?: Record<string, any>) => void;
  trackFeatureUsage: (feature: string, variant?: string, properties?: Record<string, any>) => void;
  trackExperiment: (experiment: string, variant: string, properties?: Record<string, any>) => void;
  startTimer: () => number;
  endTimer: (startTime: number, label?: string) => number;
  trackApiCall: (endpoint: string, method: string, duration: number, success: boolean, status?: number) => void;
  trackInteraction: (type: 'click' | 'scroll' | 'input' | 'focus', element: string, responseTime: number) => void;
}

/**
 * Unified analytics hook for tracking all user interactions and performance
 * Consolidates 4 separate hooks into one comprehensive interface
 */
export function useAnalytics(): AnalyticsContext {
  // Track page views automatically
  useEffect(() => {
    if (typeof window !== 'undefined') {
      track('page_view', {
        url: window.location.pathname,
        timestamp: new Date().toISOString(),
      });
    }
  }, []);

  const trackEvent = useCallback((
    name: string, 
    properties?: Record<string, any>
  ) => {
    track(name, {
      ...properties,
      timestamp: new Date().toISOString(),
    });
  }, []);

  const trackError = useCallback((
    error: Error,
    context?: Record<string, any>
  ) => {
    trackEvent('error_occurred', {
      error: error.message,
      stack: process.env.NODE_ENV === 'production' ? 'REDACTED' : error.stack,
      context,
    });
  }, [trackEvent]);

  const trackPerformance = useCallback((
    name: string,
    value: number,
    unit: 'ms' | 'bytes' | 'count' = 'ms'
  ) => {
    // Note: metric function temporarily disabled due to import issues
    // TODO: Fix @vercel/speed-insights import or find alternative
    // metric(name, value, unit);
    
    // Track slow operations
    if (unit === 'ms' && value > 1000) {
      trackEvent('slow_operation', {
        operation: name,
        duration: Math.round(value),
      });
    }
  }, [trackEvent]);

  const trackConversion = useCallback((
    type: 'import_completed' | 'trip_created' | 'trip_shared',
    properties?: Record<string, any>
  ) => {
    trackEvent('conversion', {
      type,
      ...properties,
    });
  }, [trackEvent]);

  // Import-specific tracking
  const trackImportEvent = useCallback((
    step: 'input' | 'parsing' | 'preview' | 'creating' | 'completed',
    properties?: Record<string, any>
  ) => {
    trackEvent(`import_${step}`, {
      step,
      ...properties,
    });
  }, [trackEvent]);

  // Interaction tracking
  const trackClick = useCallback((
    element: string,
    properties?: Record<string, any>
  ) => {
    trackEvent('click', {
      element,
      ...properties,
    });
  }, [trackEvent]);

  const trackFormSubmit = useCallback((
    formName: string,
    success: boolean,
    properties?: Record<string, any>
  ) => {
    trackEvent('form_submit', {
      form: formName,
      success,
      ...properties,
    });
  }, [trackEvent]);

  const trackSearch = useCallback((
    query: string,
    results: number,
    properties?: Record<string, any>
  ) => {
    trackEvent('search', {
      query: query.length > 100 ? query.substring(0, 100) + '...' : query,
      results,
      ...properties,
    });
  }, [trackEvent]);

  // Feature tracking
  const trackFeatureUsage = useCallback((
    feature: string,
    variant?: string,
    properties?: Record<string, any>
  ) => {
    trackEvent('feature_usage', {
      feature,
      variant,
      ...properties,
    });
  }, [trackEvent]);

  const trackExperiment = useCallback((
    experiment: string,
    variant: string,
    properties?: Record<string, any>
  ) => {
    trackEvent('experiment_exposure', {
      experiment,
      variant,
      ...properties,
    });
  }, [trackEvent]);

  // Performance timing
  const startTimer = useCallback(() => {
    return performance.now();
  }, []);

  const endTimer = useCallback((startTime: number, label?: string) => {
    const duration = performance.now() - startTime;
    const metricName = label || 'operation';
    trackPerformance(metricName, Math.round(duration));
    return duration;
  }, [trackPerformance]);

  // API call tracking
  const trackApiCall = useCallback((
    endpoint: string,
    method: string,
    duration: number,
    success: boolean,
    status?: number
  ) => {
    trackPerformance(`api_${endpoint}_${method}`, Math.round(duration));
    
    trackEvent('api_call', {
      endpoint,
      method,
      duration: Math.round(duration),
      success,
      status,
    });

    // Track slow API calls
    if (duration > 5000) {
      trackEvent('slow_api_call', {
        endpoint,
        method,
        duration: Math.round(duration),
      });
    }
  }, [trackPerformance, trackEvent]);

  // Interaction performance tracking
  const trackInteraction = useCallback((
    type: 'click' | 'scroll' | 'input' | 'focus',
    element: string,
    responseTime: number
  ) => {
    trackPerformance(`interaction_${type}_${element}`, Math.round(responseTime));
    
    // Track slow interactions
    if (responseTime > 100) {
      trackEvent('slow_interaction', {
        type,
        element,
        response_time: Math.round(responseTime),
      });
    }
  }, [trackPerformance, trackEvent]);

  return {
    trackEvent,
    trackError,
    trackPerformance,
    trackConversion,
    trackImportEvent,
    trackClick,
    trackFormSubmit,
    trackSearch,
    trackFeatureUsage,
    trackExperiment,
    startTimer,
    endTimer,
    trackApiCall,
    trackInteraction,
  };
}

/**
 * Component-specific performance timing hook
 * @deprecated Use useAnalytics().startTimer() and endTimer() instead
 */
export function usePerformanceTimer(componentName: string) {
  const { startTimer, endTimer } = useAnalytics();

  const startComponentTimer = useCallback(() => {
    return startTimer();
  }, [startTimer]);

  const endComponentTimer = useCallback((startTime: number, label?: string) => {
    return endTimer(startTime, `${componentName}_${label || 'render'}`);
  }, [componentName, endTimer]);

  return { 
    startTimer: startComponentTimer, 
    endTimer: endComponentTimer 
  };
}

/**
 * @deprecated Use useAnalytics() directly for interaction tracking
 */
export function useInteractionTracking() {
  const { trackClick, trackFormSubmit, trackSearch } = useAnalytics();
  return { trackClick, trackFormSubmit, trackSearch };
}

/**
 * @deprecated Use useAnalytics() directly for feature tracking
 */
export function useFeatureTracking() {
  const { trackFeatureUsage, trackExperiment } = useAnalytics();
  return { trackFeatureUsage, trackExperiment };
}