#!/usr/bin/env node

/**
 * Scheduled monitoring job to check usage and send alerts
 * Run this with cron or a scheduler in production
 * Example cron: */15 * * * * /usr/bin/node /path/to/monitoring-cron.js
 */

const path = require('path');
require('dotenv').config({ path: path.join(__dirname, '../.env.local') });

// Import the monitoring service
const { MonitoringService } = require('../dist/services/monitoring.service');

async function runMonitoringCheck() {
  console.log(`[${new Date().toISOString()}] Running monitoring check...`);
  
  const monitoringService = MonitoringService.getInstance();
  
  try {
    // Check for usage alerts
    const alerts = await monitoringService.checkUsageAlerts();
    
    if (alerts.length > 0) {
      console.log(`[ALERT] Found ${alerts.length} alerts:`);
      alerts.forEach(alert => {
        console.log(`- ${alert.level.toUpperCase()}: ${alert.message}`);
      });
    } else {
      console.log('✅ No alerts - all services within normal usage');
    }
    
    // Get and log current metrics
    const metrics = await monitoringService.getUsageMetrics();
    console.log('\n📊 Current Usage:');
    
    metrics.forEach(metric => {
      if (metric.quotaRemaining >= 0) {
        console.log(`- ${metric.service}: ${metric.daily}/${metric.daily + metric.quotaRemaining} daily (${metric.quotaPercentage.toFixed(1)}%)`);
      } else {
        console.log(`- ${metric.service}: ${metric.daily} requests today`);
      }
      
      if (metric.costEstimate > 0) {
        console.log(`  Cost: $${metric.costEstimate.toFixed(4)}`);
      }
    });
    
    // Get performance metrics
    const performance = await monitoringService.getPerformanceMetrics();
    console.log('\n⚡ Performance Metrics:');
    console.log(`- Avg Response Time: ${performance.avgResponseTime}ms`);
    console.log(`- Cache Hit Rate: ${performance.cacheHitRate}%`);
    console.log(`- Success Rate: ${performance.successRate}%`);
    
  } catch (error) {
    console.error('[ERROR] Monitoring check failed:', error.message);
  }
}

// First compile TypeScript
console.log('📦 Compiling TypeScript...');
const { execSync } = require('child_process');

try {
  execSync('npm run build', { 
    cwd: path.join(__dirname, '..'),
    stdio: 'inherit'
  });
  
  console.log('\n');
  
  // Then run monitoring check
  runMonitoringCheck().catch(console.error);
  
} catch (error) {
  console.error('Failed to compile:', error.message);
}