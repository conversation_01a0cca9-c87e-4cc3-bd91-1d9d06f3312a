#!/usr/bin/env node

/**
 * Real AI API Test
 * Tests actual AI API calls with different models and measures real performance
 */

// Load environment variables first
import { loadEnvironment } from '../utils/env-loader';
loadEnvironment();

import { enhancedAIRouterService } from '../services/enhanced-ai-router.service';
import { usageTrackingService } from '../services/usage-tracking.service';
import { modelSelectorService } from '../services/model-selector.service';
import { promptManager } from '../services/prompt-manager.service';
import { getSupabaseClient } from '../lib/supabase';
import { logger } from '../utils/logger';
import axios from 'axios';

// Test content samples with known expected results
const TEST_SAMPLES = [
  {
    name: 'Simple Paris Trip',
    content: `
      Day 1: Paris
      - 10:00 AM: Visit Eiffel Tower
      - 1:00 PM: Lunch at Café de Flore  
      - 3:00 PM: Louvre Museum
      - 7:00 PM: Seine River cruise
    `,
    expectedActivities: 4,
    expectedLocations: ['Eiffel Tower', 'Café de Flore', 'Louvre Museum', 'Seine River'],
    complexity: 'simple'
  },
  {
    name: 'Multi-City Europe',
    content: `
      March 15-20: Paris, France
      - Eiffel Tower visit on March 16
      - Louvre Museum on March 17
      - Champs-Élysées shopping on March 18
      
      March 21-25: Rome, Italy  
      - Colosseum tour on March 22
      - Vatican Museums on March 23
      - Trevi Fountain visit on March 24
    `,
    expectedActivities: 6,
    expectedLocations: ['Eiffel Tower', 'Louvre Museum', 'Champs-Élysées', 'Colosseum', 'Vatican Museums', 'Trevi Fountain'],
    complexity: 'medium'
  }
];

interface TestResult {
  sampleName: string;
  modelId: string;
  modelName: string;
  provider: string;
  success: boolean;
  duration: number;
  activitiesFound: number;
  expectedActivities: number;
  locationsFound: number;
  expectedLocations: number;
  accuracy: number;
  locationAccuracy: number;
  cost: number;
  inputTokens: number;
  outputTokens: number;
  rawResponse?: string;
  parsedData?: any;
  error?: string;
}

async function testRealAIAPIs() {
  console.log('🤖 Starting Real AI API Test...\n');
  console.log('⚠️  This test makes actual API calls and may incur costs!\n');

  const results: TestResult[] = [];

  try {
    // Get active models - limit to a few for real testing
    const { data: modelConfigs } = await getSupabaseClient()
      .from('ai_model_configs')
      .select('*')
      .eq('is_active', true)
      .limit(3); // Limit to avoid excessive API costs

    if (!modelConfigs || modelConfigs.length === 0) {
      throw new Error('No active model configurations found');
    }

    console.log(`Testing ${modelConfigs.length} models with ${TEST_SAMPLES.length} content samples...\n`);
    console.log('Models to test:', modelConfigs.map(m => m.id).join(', '));

    // Test each sample with each model
    for (const sample of TEST_SAMPLES) {
      console.log(`\n📝 Testing Sample: ${sample.name}`);
      console.log(`Content length: ${sample.content.length} characters`);
      console.log(`Expected: ${sample.expectedActivities} activities, ${sample.expectedLocations.length} locations`);

      for (const model of modelConfigs) {
        console.log(`\n  🤖 Testing with ${model.id}...`);
        
        try {
          const startTime = Date.now();
          
          // Get current usage before test
          const usageBefore = await usageTrackingService.getCurrentUsage(model.id);
          
          // Make actual AI API call
          const testResult = await makeRealAICall(model.id, sample.content, sample.name);
          
          const endTime = Date.now();
          const duration = endTime - startTime;
          
          // Get usage after test
          const usageAfter = await usageTrackingService.getCurrentUsage(model.id);
          
          // Calculate accuracy metrics
          const accuracy = testResult.activitiesFound / sample.expectedActivities;
          const locationAccuracy = testResult.locationsFound / sample.expectedLocations.length;
          
          console.log(`  ✅ Success! Duration: ${duration}ms`);
          console.log(`  📊 Activities: ${testResult.activitiesFound}/${sample.expectedActivities} (${(accuracy * 100).toFixed(1)}%)`);
          console.log(`  📍 Locations: ${testResult.locationsFound}/${sample.expectedLocations.length} (${(locationAccuracy * 100).toFixed(1)}%)`);
          console.log(`  💰 Cost: $${testResult.cost.toFixed(6)}`);
          console.log(`  🔢 Tokens: ${testResult.inputTokens} in, ${testResult.outputTokens} out`);
          console.log(`  📈 Usage: ${usageBefore.requestCount} → ${usageAfter.requestCount} requests`);
          
          results.push({
            sampleName: sample.name,
            modelId: model.id,
            modelName: model.name,
            provider: model.provider,
            success: true,
            duration,
            activitiesFound: testResult.activitiesFound,
            expectedActivities: sample.expectedActivities,
            locationsFound: testResult.locationsFound,
            expectedLocations: sample.expectedLocations.length,
            accuracy,
            locationAccuracy,
            cost: testResult.cost,
            inputTokens: testResult.inputTokens,
            outputTokens: testResult.outputTokens,
            rawResponse: testResult.rawResponse,
            parsedData: testResult.parsedData
          });
          
        } catch (error) {
          console.log(`  ❌ Failed: ${error instanceof Error ? error.message : String(error)}`);
          
          results.push({
            sampleName: sample.name,
            modelId: model.id,
            modelName: model.name,
            provider: model.provider,
            success: false,
            duration: 0,
            activitiesFound: 0,
            expectedActivities: sample.expectedActivities,
            locationsFound: 0,
            expectedLocations: sample.expectedLocations.length,
            accuracy: 0,
            locationAccuracy: 0,
            cost: 0,
            inputTokens: 0,
            outputTokens: 0,
            error: error instanceof Error ? error.message : String(error)
          });
        }
        
        // Delay between tests to respect rate limits
        console.log('  ⏳ Waiting 3 seconds to respect rate limits...');
        await new Promise(resolve => setTimeout(resolve, 3000));
      }
    }

    // Generate comprehensive performance analysis
    console.log('\n\n📊 REAL AI PERFORMANCE ANALYSIS\n');
    generateRealPerformanceAnalysis(results);

    return true;

  } catch (error) {
    console.error('❌ Real AI API test failed:', error);
    return false;
  }
}

async function makeRealAICall(modelId: string, content: string, sampleName: string) {
  // Get model-specific prompt and format instructions
  const systemPrompt = promptManager.getSystemPrompt(modelId);
  const formatInstructions = promptManager.getFormatInstructions(modelId);
  
  // Estimate tokens for cost calculation
  const tokenEstimate = modelSelectorService.estimateTokens(content);
  
  console.log(`    📝 Using prompt: ${systemPrompt.length} chars`);
  console.log(`    🔢 Estimated tokens: ${tokenEstimate.inputTokens} in, ${tokenEstimate.outputTokens} out`);
  
  // Build the full prompt
  const fullPrompt = `${systemPrompt}\n\n${formatInstructions}\n\nContent to parse:\n${content}`;
  
  let rawResponse = '';
  let parsedData: any = null;
  let activitiesFound = 0;
  let locationsFound = 0;
  
  try {
    // Make actual API call based on provider
    if (modelId.startsWith('google/')) {
      // Use Google Gemini API
      rawResponse = await callGoogleGeminiAPI(modelId, systemPrompt, content);
    } else if (modelId.startsWith('moonshotai/')) {
      // Use OpenRouter for Moonshot AI
      rawResponse = await callOpenRouterAPI(modelId, fullPrompt);
    } else {
      // Use OpenRouter for other models
      rawResponse = await callOpenRouterAPI(modelId, fullPrompt);
    }
    
    // Try to parse the response as JSON
    try {
      parsedData = JSON.parse(rawResponse);
      
      // Count activities and locations from parsed data
      if (parsedData.activities && Array.isArray(parsedData.activities)) {
        activitiesFound = parsedData.activities.length;
        
        // Count unique locations
        const locations = new Set();
        parsedData.activities.forEach((activity: any) => {
          if (activity.location && activity.location.address) {
            locations.add(activity.location.address);
          }
          if (activity.name) {
            // Extract location names from activity names
            const locationKeywords = ['Tower', 'Museum', 'Café', 'River', 'Champs-Élysées', 'Colosseum', 'Vatican', 'Fountain'];
            locationKeywords.forEach(keyword => {
              if (activity.name.includes(keyword)) {
                locations.add(keyword);
              }
            });
          }
        });
        locationsFound = locations.size;
      }
    } catch (parseError) {
      console.log(`    ⚠️  Failed to parse JSON response: ${parseError}`);
      // Fallback: count activities and locations from raw text
      activitiesFound = (rawResponse.match(/activity|visit|lunch|tour|museum/gi) || []).length;
      locationsFound = (rawResponse.match(/tower|museum|café|river|colosseum|vatican|fountain/gi) || []).length;
    }
    
  } catch (apiError) {
    throw new Error(`API call failed: ${apiError instanceof Error ? apiError.message : String(apiError)}`);
  }
  
  // Calculate actual cost based on real token usage
  const actualInputTokens = Math.ceil(fullPrompt.length / 4); // Rough estimate
  const actualOutputTokens = Math.ceil(rawResponse.length / 4); // Rough estimate
  const cost = calculateRealModelCost(modelId, actualInputTokens, actualOutputTokens);
  
  return {
    activitiesFound,
    locationsFound,
    cost,
    inputTokens: actualInputTokens,
    outputTokens: actualOutputTokens,
    rawResponse: rawResponse.substring(0, 500) + '...', // Truncate for logging
    parsedData
  };
}

async function callOpenRouterAPI(modelId: string, prompt: string): Promise<string> {
  const apiKey = process.env.OPENROUTER_API_KEY;
  if (!apiKey) {
    throw new Error('OpenRouter API key not configured');
  }
  
  const response = await axios.post('https://openrouter.ai/api/v1/chat/completions', {
    model: modelId,
    messages: [
      { role: 'user', content: prompt }
    ],
    temperature: 0.2,
    max_tokens: 2000
  }, {
    headers: {
      'Authorization': `Bearer ${apiKey}`,
      'Content-Type': 'application/json',
      'HTTP-Referer': 'https://travelviz.ai',
      'X-Title': 'TravelViz AI Parser Test'
    },
    timeout: 30000
  });
  
  return response.data.choices[0].message.content;
}

async function callGoogleGeminiAPI(modelId: string, systemPrompt: string, content: string): Promise<string> {
  const apiKey = process.env.GOOGLE_GEMINI_API_KEY;
  if (!apiKey) {
    throw new Error('Google Gemini API key not configured');
  }
  
  // Extract model name from modelId (e.g., 'google/gemini-2.0-flash' -> 'gemini-2.0-flash')
  const modelName = modelId.replace('google/', '');
  
  const response = await axios.post(`https://generativelanguage.googleapis.com/v1beta/models/${modelName}:generateContent?key=${apiKey}`, {
    contents: [{
      parts: [{
        text: `${systemPrompt}\n\nContent to parse:\n${content}`
      }]
    }],
    generationConfig: {
      temperature: 0.2,
      maxOutputTokens: 2000
    }
  }, {
    headers: {
      'Content-Type': 'application/json'
    },
    timeout: 30000
  });
  
  return response.data.candidates[0].content.parts[0].text;
}

function calculateRealModelCost(modelId: string, inputTokens: number, outputTokens: number): number {
  // Real cost rates (approximate)
  const costPer1kTokens: Record<string, { input: number; output: number }> = {
    'moonshotai/kimi-k2:free': { input: 0, output: 0 },
    'google/gemini-2.5-pro': { input: 0, output: 0 }, // Free tier
    'google/gemini-2.5-flash': { input: 0, output: 0 }, // Free tier
    'google/gemini-2.0-flash': { input: 0, output: 0 }, // Free tier
    'openai/gpt-4.1-nano': { input: 0.0015, output: 0.006 }
  };
  
  const rates = costPer1kTokens[modelId] || { input: 0.001, output: 0.002 };
  return (inputTokens / 1000) * rates.input + (outputTokens / 1000) * rates.output;
}

function generateRealPerformanceAnalysis(results: TestResult[]) {
  const successfulResults = results.filter(r => r.success);
  const failedResults = results.filter(r => !r.success);
  
  console.log(`📈 Overall Results:`);
  console.log(`   Total tests: ${results.length}`);
  console.log(`   Successful: ${successfulResults.length}`);
  console.log(`   Failed: ${failedResults.length}`);
  console.log(`   Success rate: ${((successfulResults.length / results.length) * 100).toFixed(1)}%`);
  
  if (successfulResults.length > 0) {
    // Group by model
    const groupedResults: Record<string, TestResult[]> = successfulResults.reduce((acc, result) => {
      if (!acc[result.modelId]) acc[result.modelId] = [];
      acc[result.modelId].push(result);
      return acc;
    }, {} as Record<string, TestResult[]>);
    
    console.log(`\n📊 Model Performance Comparison:`);
    for (const [modelId, modelResults] of Object.entries(groupedResults)) {
      const avgDuration = modelResults.reduce((sum, r) => sum + r.duration, 0) / modelResults.length;
      const avgAccuracy = modelResults.reduce((sum, r) => sum + r.accuracy, 0) / modelResults.length;
      const avgLocationAccuracy = modelResults.reduce((sum, r) => sum + r.locationAccuracy, 0) / modelResults.length;
      const totalCost = modelResults.reduce((sum, r) => sum + r.cost, 0);
      
      console.log(`\n   🤖 ${modelId}:`);
      console.log(`      Tests: ${modelResults.length}`);
      console.log(`      Avg Duration: ${avgDuration.toFixed(0)}ms`);
      console.log(`      Activity Accuracy: ${(avgAccuracy * 100).toFixed(1)}%`);
      console.log(`      Location Accuracy: ${(avgLocationAccuracy * 100).toFixed(1)}%`);
      console.log(`      Total Cost: $${totalCost.toFixed(6)}`);
      console.log(`      Provider: ${modelResults[0].provider}`);
      
      // Show detailed results for each test
      modelResults.forEach(result => {
        console.log(`        ${result.sampleName}: ${result.activitiesFound}/${result.expectedActivities} activities, ${result.locationsFound}/${result.expectedLocations} locations`);
      });
    }
  }
  
  if (failedResults.length > 0) {
    console.log(`\n❌ Failed Tests:`);
    failedResults.forEach(result => {
      console.log(`   ${result.modelId} on ${result.sampleName}: ${result.error}`);
    });
  }
  
  console.log(`\n💡 Key Insights:`);
  if (successfulResults.length > 0) {
    const bestAccuracy = Math.max(...successfulResults.map(r => r.accuracy));
    const bestModel = successfulResults.find(r => r.accuracy === bestAccuracy);
    console.log(`   🏆 Best Activity Accuracy: ${bestModel?.modelId} (${(bestAccuracy * 100).toFixed(1)}%)`);

    // Group by model for fastest calculation
    const modelGroups: Record<string, TestResult[]> = successfulResults.reduce((acc, result) => {
      if (!acc[result.modelId]) acc[result.modelId] = [];
      acc[result.modelId].push(result);
      return acc;
    }, {} as Record<string, TestResult[]>);

    const fastestAvg = Math.min(...Object.values(modelGroups).map((results: TestResult[]) =>
      results.reduce((sum: number, r: TestResult) => sum + r.duration, 0) / results.length
    ));
    const fastestModelResults = Object.entries(modelGroups).find(([_, results]) =>
      (results.reduce((sum: number, r: TestResult) => sum + r.duration, 0) / results.length) === fastestAvg
    );
    console.log(`   ⚡ Fastest Model: ${fastestModelResults?.[0]} (${fastestAvg.toFixed(0)}ms avg)`);

    const totalCostAll = successfulResults.reduce((sum, r) => sum + r.cost, 0);
    console.log(`   💰 Total Test Cost: $${totalCostAll.toFixed(6)}`);
  }
}

// Run the test if this script is executed directly
if (require.main === module) {
  testRealAIAPIs()
    .then((success) => {
      if (success) {
        console.log('\n🎉 Real AI API test completed!');
        console.log('\n⚠️  Remember: This test used real API calls and may have incurred costs.');
      }
      process.exit(success ? 0 : 1);
    })
    .catch((error) => {
      console.error('Test execution failed:', error);
      process.exit(1);
    });
}

export { testRealAIAPIs };
