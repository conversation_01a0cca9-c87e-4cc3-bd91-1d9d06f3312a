// Debug script to check authentication state
// Run this in browser console on the import page

console.log('🔍 TravelViz Authentication Debug');
console.log('================================');

// Check localStorage for auth data
const authStorage = localStorage.getItem('auth-storage');
console.log('📦 Auth Storage:', authStorage ? JSON.parse(authStorage) : 'Not found');

// Check cookies
const cookies = document.cookie.split(';').reduce((acc, cookie) => {
  const [key, value] = cookie.trim().split('=');
  acc[key] = value;
  return acc;
}, {});
console.log('🍪 Cookies:', cookies);

// Check if we're on the right domain
console.log('🌐 Current URL:', window.location.href);
console.log('🌐 API Base URL:', process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001');

// Test API connectivity
fetch('/api/v1/auth/me', {
  headers: {
    'Authorization': `Bearer ${authStorage ? JSON.parse(authStorage).accessToken : 'no-token'}`
  }
})
.then(response => {
  console.log('🔐 Auth Test Response:', response.status, response.statusText);
  return response.json();
})
.then(data => {
  console.log('🔐 Auth Test Data:', data);
})
.catch(error => {
  console.log('❌ Auth Test Error:', error);
});

// Instructions
console.log('\n📋 Next Steps:');
console.log('1. If no auth storage found, go to /login');
console.log('2. Use test credentials: <EMAIL> / Flaremmk123!');
console.log('3. After login, return to /import and try PDF upload');
console.log('4. If still having issues, check network tab for 401 errors');
