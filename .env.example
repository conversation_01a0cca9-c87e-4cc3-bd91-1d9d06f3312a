# TravelViz Environment Configuration Example
# DO NOT commit .env.local files to version control

# ==================================
# QUICK START
# ==================================
# 1. Copy this file to packages/web/.env.local
# 2. Copy this file to packages/hub/.env.local (optional)
# 3. Fill in your actual values
# 4. See docs/environment-setup.md for detailed setup instructions

# ==================================
# WEB PACKAGE ENVIRONMENT VARIABLES
# Location: packages/web/.env.local
# ==================================

# Supabase Configuration (REQUIRED)
# Get these from: https://app.supabase.com/project/_/settings/api
NEXT_PUBLIC_SUPABASE_URL=https://your-project-id.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-anon-key-here
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key-here

# Mapbox Configuration (REQUIRED for map features)
# Get from: https://account.mapbox.com/access-tokens/
NEXT_PUBLIC_MAPBOX_TOKEN=pk.eyXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX

# OpenRouter Configuration (REQUIRED for AI features)
# Get from: https://openrouter.ai/keys
OPENROUTER_API_KEY=sk-or-v1-XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX

# Application Configuration
NEXT_PUBLIC_APP_URL=http://localhost:3000
NEXT_PUBLIC_HUB_URL=http://localhost:3001

# Optional: Email Service (Resend)
# Get from: https://resend.com/api-keys
RESEND_API_KEY=re_XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX

# Optional: Analytics
# Get from: https://analytics.google.com
NEXT_PUBLIC_GA_ID=G-XXXXXXXXXX

# Optional: Error Tracking (Sentry)
# Get from: https://sentry.io/settings/projects/your-project/keys/
NEXT_PUBLIC_SENTRY_DSN=https://<EMAIL>/project-id

# ==================================
# HUB PACKAGE ENVIRONMENT VARIABLES
# Location: packages/hub/.env.local
# ==================================

# Server Configuration
PORT=3001

# Database Access (if hub needs direct access)
SUPABASE_URL=https://your-project-id.supabase.co
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key-here

# AI Services (hub handles AI requests)
OPENROUTER_API_KEY=sk-or-v1-XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX

# Mapbox (for geocoding and distance calculations)
MAPBOX_ACCESS_TOKEN=sk.eyXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX

# Security
JWT_SECRET=your-random-jwt-secret-minimum-32-characters

# ==================================
# PRODUCTION DEPLOYMENT
# ==================================

# RENDER (Hub API)
# Add these in Render Dashboard:
# - All hub environment variables above
# - NODE_ENV=production

# VERCEL (Web App)
# Add these in Vercel Dashboard:
# - All NEXT_PUBLIC_* variables from above
# - NEXT_PUBLIC_HUB_URL=https://your-hub.onrender.com (your Render URL)

# ==================================
# OPTIONAL SERVICES
# ==================================

# Flight Search APIs
# DUFFEL_API_KEY=duffel_test_XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX
# TRAVELPAYOUTS_API_KEY=XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX

# Alternative AI Providers (if not using OpenRouter)
# OPENAI_API_KEY=sk-proj-XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX
# ANTHROPIC_API_KEY=sk-ant-XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX

# Caching (for production)
# REDIS_URL=redis://your-redis-instance

# Other Map Providers
# GOOGLE_MAPS_API_KEY=your_google_maps_key
