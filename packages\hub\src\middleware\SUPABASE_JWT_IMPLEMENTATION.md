# Supabase JWT Authentication Implementation

This implementation provides proper Supabase JWT verification for the TravelViz hub API.

## Overview

The implementation includes:
- **supabase-jwt.ts**: Utilities for verifying Supabase JWT tokens
- **supabase-auth.middleware.ts**: Express middleware for authenticating requests
- **supabase-auth.service.ts**: Service layer using Supabase Auth
- **supabase-auth.controller.ts**: Controller handling auth endpoints
- **supabase-auth.routes.ts**: Route definitions

## Key Features

1. **Native Supabase JWT Verification**: Uses Supabase's built-in auth.getUser() method to verify tokens
2. **Proper Session Management**: Returns Supabase session tokens instead of custom JWTs
3. **Email Confirmation Support**: Handles email confirmation flow
4. **Password Reset Flow**: Includes password reset functionality
5. **Type Safety**: Full TypeScript support with proper types

## Integration Steps

### 1. Update Environment Variables

Add to your `.env.local`:
```env
SUPABASE_JWT_SECRET=your-jwt-secret-from-supabase-dashboard  # Optional
```

### 2. Update Main API Router

In `src/index.ts`, you can choose between the custom JWT implementation or Supabase JWT:

```typescript
import supabaseAuthRoutes from './routes/supabase-auth.routes';

// Option 1: Use Supabase JWT authentication (recommended)
app.use('/api/v1/auth', supabaseAuthRoutes);

// Option 2: Keep existing custom JWT implementation
// app.use('/api/v1/auth', authRoutes);
```

### 3. Update Protected Routes

For routes that need authentication, use the Supabase middleware:

```typescript
import { authenticateSupabaseUser } from '../middleware/supabase-auth.middleware';

// Protect routes with Supabase JWT
router.use(authenticateSupabaseUser);
```

### 4. Update Trip Routes

If you want to use Supabase auth for trips:

```typescript
import { Router } from 'express';
import { TripsController } from '../controllers/trips.controller';
import { authenticateSupabaseUser } from '../middleware/supabase-auth.middleware';

const router = Router();
const tripsController = new TripsController();

// All routes require authentication
router.use(authenticateSupabaseUser);

router.post('/', tripsController.createTrip);
router.get('/', tripsController.getTrips);
// ... other routes
```

## API Response Format

### Login/Signup Response
```json
{
  "success": true,
  "data": {
    "user": {
      "id": "uuid",
      "email": "<EMAIL>",
      "name": "John Doe"
    },
    "access_token": "eyJ...",
    "refresh_token": "eyJ...",
    "expires_in": 3600,
    "expires_at": 1234567890,
    "token_type": "bearer"
  },
  "message": "Login successful"
}
```

### Using the Token

Include the access token in the Authorization header:
```
Authorization: Bearer eyJ...
```

## Migration from Custom JWT

To migrate from the custom JWT implementation:

1. **Frontend Changes**:
   - Store the `access_token` and `refresh_token` from Supabase
   - Use `access_token` in Authorization headers
   - Implement token refresh using the `/auth/refresh` endpoint

2. **Backend Changes**:
   - Replace custom auth middleware with Supabase middleware
   - Update user type from `JWTUser` to `SupabaseUser`
   - Remove custom token generation/verification

3. **Database**:
   - No changes needed - Supabase Auth handles user storage
   - Profiles table continues to work with Supabase user IDs

## Testing

Test the implementation:

```bash
# Signup
curl -X POST http://localhost:3001/api/v1/auth/signup \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"password123","name":"Test User"}'

# Login
curl -X POST http://localhost:3001/api/v1/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"password123"}'

# Get current user (with token)
curl -X GET http://localhost:3001/api/v1/auth/me \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

## Benefits

1. **Security**: Leverages Supabase's battle-tested auth system
2. **Scalability**: No need to manage JWT secrets or token blacklists
3. **Features**: Built-in email confirmation, password reset, OAuth support
4. **Consistency**: Same auth tokens work across all Supabase services