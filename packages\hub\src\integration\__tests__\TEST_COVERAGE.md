# AI Import API Test Coverage

This document outlines the comprehensive test suite for AI import, PDF import, and chat itinerary features.

## Test Files Overview

### 1. `ai-import-enhanced.test.ts` ✅
**Purpose**: Comprehensive testing of AI conversation parsing with robust assertions

**Coverage**:
- ✅ Authentication validation (401 errors)
- ✅ Input validation (content length, missing fields)
- ✅ Well-structured conversation parsing (Paris example)
- ✅ Activity extraction with detailed validation
- ✅ Date and time parsing accuracy
- ✅ Price and currency extraction
- ✅ Ambiguous date handling with warnings
- ✅ Unstructured conversation parsing
- ✅ Multi-destination trip parsing
- ✅ Trip creation from parsed data
- ✅ Duplicate prevention
- ✅ Concurrent request handling
- ✅ Performance benchmarks

**Key Improvements**:
- Validates actual parsed content, not just structure
- Checks specific fields like dates, prices, activity types
- Handles edge cases and error scenarios
- Uses proper async/await patterns with timeout handling

### 2. `pdf-import.test.ts` ✅
**Purpose**: Testing PDF upload and processing functionality

**Coverage**:
- ✅ Authentication requirements
- ✅ File type validation (reject non-PDFs)
- ✅ File extension validation
- ✅ File size limits (10MB max)
- ✅ Missing file handling
- ✅ Valid PDF acceptance
- ✅ Multi-page PDF support
- ✅ Corrupted PDF handling
- ✅ Password-protected PDF scenarios
- ✅ PDF to trip creation flow

**Key Features**:
- Creates minimal valid PDF buffers for testing
- Tests all validation middleware
- Handles various PDF edge cases

### 3. `ai-chat-itinerary.test.ts` ✅
**Purpose**: Testing chat-based itinerary imports from various sources

**Coverage**:
- ✅ Incremental chat message parsing
- ✅ Multi-turn conversation handling
- ✅ Context understanding across messages
- ✅ Multi-language support (Spanish, mixed languages)
- ✅ WhatsApp export format
- ✅ Discord chat format
- ✅ Clarification request handling
- ✅ Chats without itinerary content
- ✅ Interrupted conversations

**Key Features**:
- Tests realistic chat scenarios
- Handles various chat export formats
- Validates context preservation

### 4. `ai-import-simple.test.ts` ✅
**Purpose**: Basic smoke tests for AI import functionality

**Coverage**:
- ✅ Health check endpoint
- ✅ Basic authentication
- ✅ Simple conversation parsing
- ✅ Polling mechanism
- ✅ Content validation

## Running the Tests

### All Tests
```bash
pnpm test:ai-import
```

### Individual Test Files
```bash
# Enhanced AI tests only
pnpm vitest run src/integration/__tests__/ai-import-enhanced.test.ts

# PDF tests only
pnpm vitest run src/integration/__tests__/pdf-import.test.ts

# Chat tests only
pnpm vitest run src/integration/__tests__/ai-chat-itinerary.test.ts
```

### Watch Mode
```bash
pnpm test:ai-import:watch
```

### With Coverage
```bash
pnpm test:ai-import:coverage
```

## Test Data Examples

### Well-Structured Itinerary
- Paris 3-day trip with specific dates, times, prices
- Expected: High confidence parsing with all details extracted

### Ambiguous Conversations
- "Next month" or "long weekend" without specific dates
- Expected: Parse with warnings and lower confidence

### Multi-Format Support
- ChatGPT, Claude, Gemini conversation styles
- WhatsApp and Discord chat exports
- PDF uploads with various content types

## CI/CD Integration

**Current Status**: Tests run in CI without skipping (fixed)

**Requirements**:
1. Hub server must be running
2. Environment variables properly configured
3. Test database accessible
4. Redis connection available

## Performance Benchmarks

- Simple parse: < 5 seconds
- Complex itinerary: < 15 seconds
- PDF processing: < 10 seconds
- Concurrent requests: 10+ supported

## Error Handling Coverage

- ✅ Authentication failures (401)
- ✅ Validation errors (400)
- ✅ Size limits (413)
- ✅ Not found errors (404)
- ✅ Server errors (500)
- ✅ Timeout handling
- ✅ Malformed data handling

## Next Steps

1. **Add E2E Visual Tests**: Test the complete flow including UI
2. **Add Load Testing**: Test with 100+ concurrent users
3. **Add Integration with External Services**: Test OpenRouter API integration
4. **Add Webhook Tests**: Test real-time updates via webhooks
5. **Add Export Tests**: Test trip export functionality

## Maintenance Notes

- Tests use real API calls (not mocked)
- Requires test account credentials
- Uses test database that should be cleaned periodically
- Monitor API rate limits during test runs