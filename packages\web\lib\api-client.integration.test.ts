import { describe, it, expect, vi, beforeEach, afterEach, afterAll, beforeAll } from 'vitest';
import { setupServer } from 'msw/node';
import { http, HttpResponse, delay } from 'msw';
import { api, ApiClient } from './api-client';
import { useAuthStore } from '../stores/auth.store';
import type { ApiError } from './api-client';
import type { User } from '@travelviz/shared';

// Mock the cookie storage used by the zustand persist middleware
vi.mock('./cookie-storage', () => {
  let store: Record<string, string> = {};
  return {
    cookieStorage: {
      getItem: (name: string) => store[name] || null,
      setItem: (name: string, value: string) => {
        store[name] = value;
      },
      removeItem: (name: string) => {
        delete store[name];
      },
    },
  };
});

// MSW server setup to intercept fetch requests
const server = setupServer();

beforeAll(() => server.listen({ onUnhandledRequest: 'error' }));
afterEach(() => server.resetHandlers());
afterAll(() => server.close());

describe('ApiClient Integration Tests - Authentication Cascade Prevention', () => {
  beforeEach(() => {
    // Reset auth store to clean state
    useAuthStore.getState().clearAuth();
    useAuthStore.persist.rehydrate();
  });

  describe('Infinite Loop Prevention', () => {
    it('should NOT enter infinite loop when token refresh fails with 401', async () => {
      // CRITICAL TEST: This directly prevents the production incident's root cause
      // If refresh token is invalid and returns 401, it should NOT retry infinitely
      
      // Arrange: Mock endpoints to always return 401
      server.use(
        http.get('http://localhost:3001/api/v1/trips', () => {
          return new HttpResponse(null, { status: 401 });
        }),
        http.post('http://localhost:3001/api/v1/auth/refresh', () => {
          return new HttpResponse(
            JSON.stringify({ error: 'Invalid refresh token' }),
            { status: 401 }
          );
        })
      );

      // Set initial state with expired tokens
      useAuthStore.setState({
        accessToken: 'expired-access-token',
        refreshToken: 'invalid-refresh-token',
        isAuthenticated: true,
        user: { id: 'user-1', email: '<EMAIL>' },
      });

      // Act & Assert: Should fail gracefully without infinite loop
      const startTime = Date.now();
      await expect(api.trips.list()).rejects.toThrow();
      const endTime = Date.now();

      // Should complete quickly (not timeout from infinite loop)
      expect(endTime - startTime).toBeLessThan(5000);

      // Auth should be cleared after failed refresh
      const state = useAuthStore.getState();
      expect(state.isAuthenticated).toBe(false);
      expect(state.accessToken).toBeNull();
      expect(state.refreshToken).toBeNull();
    });

    it('should limit retry attempts and implement circuit breaker', async () => {
      let retryCount = 0;
      
      // Track API calls to verify retry limit
      server.use(
        http.get('http://localhost:3001/api/v1/trips', () => {
          retryCount++;
          return new HttpResponse(null, { status: 401 });
        }),
        http.post('http://localhost:3001/api/v1/auth/refresh', () => {
          return new HttpResponse(null, { status: 401 });
        })
      );

      useAuthStore.setState({
        accessToken: 'expired-token',
        refreshToken: 'invalid-refresh',
        isAuthenticated: true,
      });

      await expect(api.trips.list()).rejects.toThrow();

      // Should only retry once (initial call + one retry after refresh attempt)
      expect(retryCount).toBeLessThanOrEqual(2);
    });
  });

  describe('Successful Token Refresh Flow', () => {
    it('should refresh token and retry original request successfully', async () => {
      const newTokens = {
        access_token: 'new-valid-access-token',
        refresh_token: 'new-valid-refresh-token',
        user: { id: 'user-1', email: '<EMAIL>' } as User,
      };

      const successfulResponse = { trips: [{ id: 'trip-1', title: 'My Trip' }] };
      let tripsCallCount = 0;

      server.use(
        // First call fails, retry succeeds
        http.get('http://localhost:3001/api/v1/trips', ({ request }) => {
          tripsCallCount++;
          const authHeader = request.headers.get('Authorization');
          
          if (tripsCallCount === 1 || authHeader?.includes('expired')) {
            return new HttpResponse(null, { status: 401 });
          }
          
          if (authHeader?.includes('new-valid-access-token')) {
            return HttpResponse.json({ success: true, data: successfulResponse });
          }
          
          return new HttpResponse('Invalid setup', { status: 500 });
        }),
        
        // Refresh succeeds
        http.post('http://localhost:3001/api/v1/auth/refresh', () => {
          return HttpResponse.json({ success: true, data: newTokens });
        })
      );

      useAuthStore.setState({
        accessToken: 'expired-access-token',
        refreshToken: 'valid-refresh-token',
        isAuthenticated: true,
        user: { id: 'user-1', email: '<EMAIL>' },
      });

      // Act
      const result = await api.trips.list();

      // Assert
      expect(result).toEqual(successfulResponse);
      expect(tripsCallCount).toBe(2); // Initial fail + retry
      
      // Auth store should have new tokens
      const state = useAuthStore.getState();
      expect(state.accessToken).toBe(newTokens.access_token);
      expect(state.refreshToken).toBe(newTokens.refresh_token);
    });
  });

  describe('Error Handling Scenarios', () => {
    it('should handle 500 error during token refresh gracefully', async () => {
      server.use(
        http.get('http://localhost:3001/api/v1/trips', () => 
          new HttpResponse(null, { status: 401 })
        ),
        http.post('http://localhost:3001/api/v1/auth/refresh', () => 
          new HttpResponse('Internal Server Error', { status: 500 })
        )
      );

      useAuthStore.setState({
        accessToken: 'expired-token',
        refreshToken: 'valid-refresh',
        isAuthenticated: true,
      });

      const error = await api.trips.list().catch(e => e) as ApiError;

      expect(error).toBeInstanceOf(Error);
      expect(error.status).toBe(500);

      // Auth should be cleared
      const state = useAuthStore.getState();
      expect(state.isAuthenticated).toBe(false);
    });

    it('should handle network timeouts without cascading failures', async () => {
      server.use(
        http.get('http://localhost:3001/api/v1/trips', async () => {
          await delay(35000); // Longer than default timeout
          return HttpResponse.json({ data: [] });
        })
      );

      useAuthStore.setState({
        accessToken: 'valid-token',
        isAuthenticated: true,
      });

      const startTime = Date.now();
      await expect(api.trips.list()).rejects.toThrow('Request timeout');
      const endTime = Date.now();

      // Should timeout within reasonable time (30s + buffer)
      expect(endTime - startTime).toBeLessThan(35000);
    });

    it('should handle concurrent authentication requests without race conditions', async () => {
      let refreshCallCount = 0;
      
      server.use(
        http.get('http://localhost:3001/api/v1/trips', () => 
          new HttpResponse(null, { status: 401 })
        ),
        http.post('http://localhost:3001/api/v1/auth/refresh', async () => {
          refreshCallCount++;
          await delay(100); // Simulate network delay
          return HttpResponse.json({
            success: true,
            data: {
              access_token: 'new-token',
              refresh_token: 'new-refresh',
              user: { id: 'user-1', email: '<EMAIL>' },
            },
          });
        })
      );

      useAuthStore.setState({
        accessToken: 'expired-token',
        refreshToken: 'valid-refresh',
        isAuthenticated: true,
      });

      // Make multiple concurrent requests
      const promises = Array(3).fill(null).map(() => 
        api.trips.list().catch(() => null)
      );

      await Promise.all(promises);

      // Should only call refresh once despite concurrent requests
      expect(refreshCallCount).toBe(1);
    });
  });

  describe('Rate Limiting Integration', () => {
    it('should respect rate limits and not retry excessively', async () => {
      let requestCount = 0;
      
      server.use(
        http.get('http://localhost:3001/api/v1/trips', () => {
          requestCount++;
          return new HttpResponse(
            JSON.stringify({ error: 'Rate limit exceeded' }),
            { status: 429, headers: { 'Retry-After': '60' } }
          );
        })
      );

      useAuthStore.setState({
        accessToken: 'valid-token',
        isAuthenticated: true,
      });

      await expect(api.trips.list()).rejects.toThrow();

      // Should not retry on 429 status
      expect(requestCount).toBe(1);
    });
  });

  describe('Custom API Client Configuration', () => {
    it('should allow custom error handling without affecting auth flow', async () => {
      const customErrorHandler = vi.fn();
      const customClient = new ApiClient({
        onError: customErrorHandler,
        timeout: 5000,
      });

      server.use(
        http.get('http://localhost:3001/api/v1/trips', () => 
          new HttpResponse('Not Found', { status: 404 })
        )
      );

      await expect(customClient.get('/api/v1/trips')).rejects.toThrow();
      
      expect(customErrorHandler).toHaveBeenCalledWith(
        expect.objectContaining({ status: 404 })
      );
    });

    it('should handle unauthorized callback without interfering with token refresh', async () => {
      const unauthorizedHandler = vi.fn();
      const customClient = new ApiClient({
        onUnauthorized: unauthorizedHandler,
      });

      server.use(
        http.get('http://localhost:3001/api/v1/trips', () => 
          new HttpResponse(null, { status: 401 })
        ),
        http.post('http://localhost:3001/api/v1/auth/refresh', () => 
          new HttpResponse(null, { status: 401 })
        )
      );

      useAuthStore.setState({
        accessToken: 'expired-token',
        refreshToken: 'invalid-refresh',
        isAuthenticated: true,
      });

      await expect(customClient.get('/api/v1/trips')).rejects.toThrow();
      
      // Should call unauthorized handler after failed refresh
      expect(unauthorizedHandler).toHaveBeenCalled();
    });
  });

  describe('Circuit Breaker Integration', () => {
    it('should open circuit after 3 consecutive failures', async () => {
      const customClient = new ApiClient({ timeout: 5000 });
      const fetchSpy = vi.spyOn(global, 'fetch');
      
      server.use(
        http.get('http://localhost:3001/api/v1/test', () => 
          new HttpResponse(null, { status: 500 })
        )
      );

      // Trigger 3 failures
      await expect(customClient.get('/api/v1/test')).rejects.toThrow();
      await expect(customClient.get('/api/v1/test')).rejects.toThrow();
      await expect(customClient.get('/api/v1/test')).rejects.toThrow();

      // Circuit should be open
      const status = customClient.getCircuitBreakerStatus();
      expect(status.isOpen).toBe(true);
      expect(status.failures).toBe(3);
      expect(fetchSpy).toHaveBeenCalledTimes(3);

      // 4th request should be blocked
      await expect(customClient.get('/api/v1/test')).rejects.toThrow('Service temporarily unavailable');
      expect(fetchSpy).toHaveBeenCalledTimes(3); // No additional fetch
    });

    it('should reset circuit after successful request', async () => {
      const customClient = new ApiClient({ timeout: 5000 });
      
      server.use(
        http.get('http://localhost:3001/api/v1/fail', () => 
          new HttpResponse(null, { status: 500 })
        ),
        http.get('http://localhost:3001/api/v1/success', () => 
          HttpResponse.json({ success: true, data: 'ok' })
        )
      );

      // Two failures
      await expect(customClient.get('/api/v1/fail')).rejects.toThrow();
      await expect(customClient.get('/api/v1/fail')).rejects.toThrow();
      expect(customClient.getCircuitBreakerStatus().failures).toBe(2);

      // One success should reset
      const result = await customClient.get('/api/v1/success');
      expect(result).toBe('ok');

      const status = customClient.getCircuitBreakerStatus();
      expect(status.isOpen).toBe(false);
      expect(status.failures).toBe(0);
    });

    it('should auto-reset circuit after 60 seconds', async () => {
      vi.useFakeTimers();
      const customClient = new ApiClient({ timeout: 5000 });
      
      server.use(
        http.get('http://localhost:3001/api/v1/test', () => 
          new HttpResponse(null, { status: 500 })
        )
      );

      // Open the circuit
      await expect(customClient.get('/api/v1/test')).rejects.toThrow();
      await expect(customClient.get('/api/v1/test')).rejects.toThrow();
      await expect(customClient.get('/api/v1/test')).rejects.toThrow();
      expect(customClient.getCircuitBreakerStatus().isOpen).toBe(true);

      // Configure success for after reset
      server.use(
        http.get('http://localhost:3001/api/v1/test', () => 
          HttpResponse.json({ success: true, data: 'ok' })
        )
      );

      // Advance time past reset window
      await vi.advanceTimersByTimeAsync(61 * 1000);
      
      const result = await customClient.get('/api/v1/test');
      expect(result).toBe('ok');
      
      const status = customClient.getCircuitBreakerStatus();
      expect(status.isOpen).toBe(false);
      expect(status.failures).toBe(0);
      
      vi.useRealTimers();
    });

    it('should prevent infinite auth retries with circuit breaker', async () => {
      const customClient = new ApiClient({ timeout: 5000 });
      
      // Always return 401
      server.use(
        http.get('http://localhost:3001/api/v1/auth-test', () => 
          new HttpResponse(null, { status: 401 })
        ),
        http.post('http://localhost:3001/api/v1/auth/refresh', () => 
          new HttpResponse(null, { status: 401 })
        )
      );

      useAuthStore.setState({
        accessToken: 'expired-token',
        refreshToken: 'invalid-refresh',
        isAuthenticated: true,
      });

      // First 3 failures will attempt refresh and retry
      await expect(customClient.get('/api/v1/auth-test')).rejects.toThrow();
      await expect(customClient.get('/api/v1/auth-test')).rejects.toThrow();
      await expect(customClient.get('/api/v1/auth-test')).rejects.toThrow();

      // Circuit should be open now
      expect(customClient.getCircuitBreakerStatus().isOpen).toBe(true);

      // Next request should fail immediately without auth attempt
      const fetchSpy = vi.spyOn(global, 'fetch');
      await expect(customClient.get('/api/v1/auth-test')).rejects.toThrow('Service temporarily unavailable');
      expect(fetchSpy).not.toHaveBeenCalled();
    });

    it('should handle concurrent requests with circuit breaker', async () => {
      const customClient = new ApiClient({ timeout: 5000 });
      let requestCount = 0;
      
      server.use(
        http.get('http://localhost:3001/api/v1/concurrent', () => {
          requestCount++;
          if (requestCount <= 3) {
            return new HttpResponse(null, { status: 500 });
          }
          return HttpResponse.json({ success: true, data: 'ok' });
        })
      );

      // Make 5 concurrent requests
      const promises = Array(5).fill(null).map(() => 
        customClient.get('/api/v1/concurrent').catch(e => e)
      );

      const results = await Promise.all(promises);

      // First 3 should fail and open circuit
      const errors = results.filter(r => r instanceof Error);
      expect(errors.length).toBeGreaterThanOrEqual(3);

      // Circuit should be open
      expect(customClient.getCircuitBreakerStatus().isOpen).toBe(true);
    });

    it('should update circuit breaker on rate limit errors', async () => {
      const customClient = new ApiClient({ timeout: 5000 });
      
      server.use(
        http.get('http://localhost:3001/api/v1/rate-limit', () => 
          new HttpResponse(
            JSON.stringify({ error: 'Rate limit exceeded' }),
            { status: 429, headers: { 'Retry-After': '120' } }
          )
        )
      );

      await expect(customClient.get('/api/v1/rate-limit')).rejects.toThrow('Rate limit exceeded');
      
      // Rate limit should count as a failure
      expect(customClient.getCircuitBreakerStatus().failures).toBe(1);
    });

    it('should respect max retries with exponential backoff', async () => {
      vi.useFakeTimers();
      const customClient = new ApiClient({ timeout: 5000, retryDelay: 100 });
      let requestCount = 0;
      const requestTimes: number[] = [];
      
      server.use(
        http.get('http://localhost:3001/api/v1/backoff-test', () => {
          requestCount++;
          requestTimes.push(Date.now());
          return new HttpResponse(null, { status: 401 });
        }),
        http.post('http://localhost:3001/api/v1/auth/refresh', () => {
          return HttpResponse.json({
            success: true,
            data: {
              access_token: 'new-token',
              refresh_token: 'new-refresh',
              user: { id: 'user-1', email: '<EMAIL>' },
            },
          });
        })
      );

      useAuthStore.setState({
        accessToken: 'expired-token',
        refreshToken: 'valid-refresh',
        isAuthenticated: true,
      });

      // Mock refresh to succeed but token still invalid
      vi.mocked(useAuthStore).getState = vi.fn()
        .mockReturnValueOnce({ 
          accessToken: 'expired-token', 
          refreshToken: 'valid-refresh',
          refreshSession: vi.fn().mockResolvedValueOnce(undefined),
          clearAuth: vi.fn()
        })
        .mockReturnValue({ 
          accessToken: 'new-token', 
          refreshToken: 'new-refresh',
          refreshSession: vi.fn(),
          clearAuth: vi.fn()
        });

      const promise = customClient.get('/api/v1/backoff-test');
      
      // Advance timers to handle the delay
      await vi.advanceTimersByTimeAsync(200);
      
      await expect(promise).rejects.toThrow();
      
      // Should have initial request + 1 retry
      expect(requestCount).toBe(2);
      
      vi.useRealTimers();
    });
  });
});