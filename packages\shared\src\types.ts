// Note: z import removed as it's not used in this file

// Re-export Activity types
export { ActivityType, normalizeActivityType } from './types/activity';
export type { Activity } from './types/models';

// Re-export Analytics types
export { AnalyticsEvent } from './types/analytics';
export type { AnalyticsProvider, AnalyticsProperties, AnalyticsConfig } from './types/analytics';

// Re-export AI Parser types
export type {
  ParsedLocation,
  ParsedActivity,
  ParseMetadata,
  ParsedTrip,
  ParseSession,
} from './types/ai-parser';
export {
  ParsedLocationSchema,
  ParsedActivitySchema,
  ParseMetadataSchema,
  ParsedTripSchema,
  ParseSessionSchema,
  parsedTripToDbFormat,
  parsedActivityToDbFormat,
} from './types/ai-parser';

// API Response types
export interface ApiResponse<T = unknown> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export interface PaginatedResponse<T = unknown> extends ApiResponse<T[]> {
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

// Common error types
export interface ErrorResponse {
  success: false;
  error: string;
  message?: string;
  code?: string;
} 