import { Response } from 'express';
import { TripsService } from '../services/trips.service';
import { GeocodingService } from '../services/geocoding.service';
import { ImportLockService } from '../services/import-lock.service';
import { PDFParserService } from '../services/pdf-parser.service';
import { enhancedAIRouterService } from '../services/enhanced-ai-router.service';
import { createSuccessResponse, createErrorResponse, ActivityType } from '@travelviz/shared';
import { logger } from '../utils/logger';
import { SupabaseAuthenticatedRequest as AuthenticatedRequest } from '../middleware/supabase-auth.middleware';

// Define ValidationError locally since we're removing the old parser service
export class ValidationError extends Error {
  constructor(message: string) {
    super(message);
    this.name = 'ValidationError';
  }
}

export class ImportController {
  private tripsService: TripsService | undefined;
  private geocodingService: GeocodingService | undefined;
  private pdfParserService: PDFParserService | undefined;
  private importLockService: ImportLockService;

  constructor() {
    // Delay service instantiation until first use to ensure env vars are loaded
    this.importLockService = ImportLockService.getInstance();
  }

  private getServices() {
    if (!this.tripsService) {
      this.tripsService = new TripsService();
    }
    if (!this.geocodingService) {
      this.geocodingService = new GeocodingService();
    }
    if (!this.pdfParserService) {
      this.pdfParserService = new PDFParserService();
    }
    return {
      tripsService: this.tripsService,
      geocodingService: this.geocodingService,
      pdfParserService: this.pdfParserService
    };
  }

  /**
   * Parse and create a trip from AI conversation text
   */
  async parseAndCreateTrip(req: AuthenticatedRequest, res: Response): Promise<void> {
    let lockAcquired = false;
    let userId: string | undefined;
    
    try {
      const { text, source } = req.body;
      userId = req.user?.id;

      if (!userId) {
        res.status(401).json(createErrorResponse('Unauthorized'));
        return;
      }

      // Try to acquire import lock for this user
      lockAcquired = this.importLockService.acquireLock(userId);
      if (!lockAcquired) {
        res.status(429).json(createErrorResponse(
          'An import is already in progress. Please wait for it to complete before starting another import.'
        ));
        return;
      }

      logger.info('Starting AI import with Enhanced AI Router', {
        userId,
        source,
        textLength: text.length
      });

      // Get services (lazy initialization)
      const { tripsService, geocodingService } = this.getServices();

      // Parse the text using Enhanced AI Router Service with intelligent model selection
      const parsedTrip = await enhancedAIRouterService.parseContent(text, source, userId);

      // Create the trip in database
      const createdTrip = await tripsService.createTrip({
        userId,
        title: parsedTrip.title,
        description: parsedTrip.description || `Imported from ${source}`,
        destination: parsedTrip.destination,
        startDate: parsedTrip.startDate,
        endDate: parsedTrip.endDate,
        status: 'draft',
        visibility: 'private',
        tags: [`imported-${source}`, 'ai-generated']
      });

      // Collect unique locations for batch geocoding
      const locationsToGeocode = new Set<string>();
      parsedTrip.activities.forEach(activity => {
        if (activity.location) {
          locationsToGeocode.add(activity.location);
        }
      });

      // Batch geocode all locations if geocoding is available
      let geocodingResults = new Map<string, { lat: number; lng: number } | null>();
      if (geocodingService.isAvailable() && locationsToGeocode.size > 0) {
        logger.info('Batch geocoding locations', { count: locationsToGeocode.size });
        try {
          geocodingResults = await geocodingService.geocodeBatch([...locationsToGeocode]);
          logger.info('Batch geocoding completed', { 
            total: locationsToGeocode.size,
            successful: [...geocodingResults.values()].filter(r => r !== null).length
          });
        } catch (error) {
          logger.error('Batch geocoding failed', { error });
          // Continue without geocoding results
        }
      }

      // Add activities to the trip with deduplication
      const activities = [];
      const failedActivities: Array<{
        activity: typeof parsedTrip.activities[0];
        error: string;
        reason?: string;
      }> = [];
      
      // Track processed activities to detect duplicates
      const processedActivities = new Set<string>();
      
      
      for (const parsedActivity of parsedTrip.activities) {
        // Create a unique key for deduplication
        const activityKey = `${parsedActivity.title.toLowerCase().trim()}_${parsedActivity.day || 'no-day'}_${parsedActivity.startTime || 'no-time'}`;
        
        // Skip if we've already processed this activity
        if (processedActivities.has(activityKey)) {
          logger.info('Skipping duplicate activity', { 
            title: parsedActivity.title,
            day: parsedActivity.day,
            startTime: parsedActivity.startTime
          });
          continue;
        }
        
        processedActivities.add(activityKey);
        try {
          // Calculate start time based on day number if provided
          let startTime: string | undefined;
          if (parsedActivity.day && parsedTrip.startDate) {
            const tripStartDate = new Date(parsedTrip.startDate);
            const activityDate = new Date(tripStartDate);
            activityDate.setDate(tripStartDate.getDate() + parsedActivity.day - 1);
            
            if (parsedActivity.startTime) {
              // Parse time (e.g., "9:00 AM", "14:30")
              const timeMatch = parsedActivity.startTime.match(/(\d{1,2}):?(\d{2})?\s*(AM|PM)?/i);
              if (timeMatch) {
                let hours = parseInt(timeMatch[1], 10);
                const minutes = parseInt(timeMatch[2] || '0', 10);
                const period = timeMatch[3]?.toUpperCase();
                
                // Handle 12-hour to 24-hour conversion correctly
                if (period === 'PM') {
                  if (hours !== 12) {
                    hours += 12;
                  }
                  // 12 PM stays as 12
                } else if (period === 'AM') {
                  if (hours === 12) {
                    hours = 0;
                  }
                  // 12 AM becomes 0, others stay the same
                }
                
                activityDate.setHours(hours, minutes, 0, 0);
                startTime = activityDate.toISOString();
              }
            }
          }

          // Use pre-geocoded location if available
          let locationLat: number | undefined;
          let locationLng: number | undefined;
          
          if (parsedActivity.location) {
            const geocodeResult = geocodingResults.get(parsedActivity.location);
            if (geocodeResult) {
              locationLat = geocodeResult.lat;
              locationLng = geocodeResult.lng;
              logger.debug('Using cached geocoding result', { 
                location: parsedActivity.location, 
                lat: locationLat, 
                lng: locationLng 
              });
            }
          }

          const activity = await tripsService.addActivityToTrip(
            createdTrip.id,
            userId,
            {
              title: parsedActivity.title,
              description: parsedActivity.description,
              type: parsedActivity.type || ActivityType.activity,
              startTime,
              endTime: parsedActivity.endTime,
              location: parsedActivity.location,
              locationLat,
              locationLng,
              price: parsedActivity.price,
              currency: parsedActivity.currency
            }
          );
          activities.push(activity);
          // Increment position for next activity (position tracking disabled for now)
          // currentPosition++;
        } catch (error) {
          logger.error('Failed to create activity', { 
            error: error instanceof Error ? error.message : String(error),
            errorStack: error instanceof Error ? error.stack : undefined,
            activity: parsedActivity,
            tripId: createdTrip.id 
          });
          
          // Store failed activity information
          failedActivities.push({
            activity: parsedActivity,
            error: error instanceof Error ? error.message : 'Unknown error'
          });
          
          // Continue with other activities even if one fails
        }
      }

      // Return the created trip with activities and import summary
      const tripWithActivities = {
        ...createdTrip,
        activities
      };

      // Prepare import summary
      const importSummary = {
        trip: tripWithActivities,
        importStats: {
          totalActivitiesParsed: parsedTrip.activities.length,
          successfullyCreated: activities.length,
          failed: failedActivities.length,
          failedActivities: failedActivities.length > 0 ? failedActivities : undefined
        }
      };

      logger.info('Successfully imported trip', { 
        tripId: createdTrip.id, 
        activitiesCount: activities.length,
        failedCount: failedActivities.length
      });

      // Include warning message if some activities failed
      const message = failedActivities.length > 0
        ? `Trip imported with ${activities.length} activities. ${failedActivities.length} activities could not be created.`
        : 'Trip imported successfully';

      res.status(201).json(createSuccessResponse(
        importSummary,
        message
      ));
    } catch (error) {
      logger.error('Failed to import trip', { error });
      
      // Handle validation errors with 400 status
      if (error instanceof ValidationError) {
        res.status(400).json(createErrorResponse(error.message));
        return;
      }
      
      res.status(500).json(createErrorResponse(
        error instanceof Error ? error.message : 'Failed to import trip'
      ));
    } finally {
      // Always release the lock if it was acquired
      if (lockAcquired && userId) {
        this.importLockService.releaseLock(userId);
      }
    }
  }

  /**
   * Parse and create a trip from uploaded file (PDF)
   */
  async parseAndCreateTripFromFile(req: AuthenticatedRequest, res: Response): Promise<void> {
    let lockAcquired = false;
    let userId: string | undefined;
    
    try {
      const { source } = req.body;
      userId = req.user?.id;

      if (!userId) {
        res.status(401).json(createErrorResponse('Unauthorized'));
        return;
      }

      if (!req.file) {
        res.status(400).json(createErrorResponse('No file uploaded'));
        return;
      }

      // Try to acquire import lock for this user
      lockAcquired = this.importLockService.acquireLock(userId);
      if (!lockAcquired) {
        res.status(429).json(createErrorResponse(
          'An import is already in progress. Please wait for it to complete before starting another import.'
        ));
        return;
      }

      logger.info('Starting PDF import', { 
        userId, 
        source, 
        filename: req.file.originalname,
        size: req.file.size 
      });

      // Get services (lazy initialization)
      const { pdfParserService, tripsService, geocodingService } = this.getServices();

      // Validate and extract text from PDF
      if (!pdfParserService.validatePDF(req.file.buffer)) {
        res.status(400).json(createErrorResponse('Invalid PDF file'));
        return;
      }

      const extractedText = await pdfParserService.extractText(req.file.buffer);
      
      if (!extractedText || extractedText.trim().length === 0) {
        res.status(400).json(createErrorResponse('No text content found in PDF'));
        return;
      }

      logger.info('PDF text extracted', { 
        textLength: extractedText.length,
        preview: extractedText.substring(0, 200) 
      });

      // Parse the extracted text using Enhanced AI Router Service with intelligent model selection
      const parsedTrip = await enhancedAIRouterService.parseContent(extractedText, source, userId);

      // Create the trip in database
      const createdTrip = await tripsService.createTrip({
        userId,
        title: parsedTrip.title,
        description: parsedTrip.description || `Imported from ${req.file.originalname}`,
        destination: parsedTrip.destination,
        startDate: parsedTrip.startDate,
        endDate: parsedTrip.endDate,
        status: 'draft',
        visibility: 'private',
        tags: [`imported-${source}`, 'ai-generated', 'pdf-import']
      });

      // Collect unique locations for batch geocoding
      const locationsToGeocode = new Set<string>();
      parsedTrip.activities.forEach(activity => {
        if (activity.location) {
          locationsToGeocode.add(activity.location);
        }
      });

      // Batch geocode all locations if geocoding is available
      let geocodingResults = new Map<string, { lat: number; lng: number } | null>();
      if (geocodingService.isAvailable() && locationsToGeocode.size > 0) {
        logger.info('Batch geocoding locations', { count: locationsToGeocode.size });
        try {
          geocodingResults = await geocodingService.geocodeBatch([...locationsToGeocode]);
          logger.info('Batch geocoding completed', { 
            total: locationsToGeocode.size,
            successful: [...geocodingResults.values()].filter(r => r !== null).length
          });
        } catch (error) {
          logger.error('Batch geocoding failed', { error });
          // Continue without geocoding results
        }
      }

      // Add activities to the trip
      const activities = [];
      const failedActivities: Array<{
        activity: typeof parsedTrip.activities[0];
        error: string;
      }> = [];
      
      // Track processed activities to detect duplicates
      const processedActivities = new Set<string>();
      
      
      for (const parsedActivity of parsedTrip.activities) {
        // Create a unique key for deduplication
        const activityKey = `${parsedActivity.title.toLowerCase().trim()}_${parsedActivity.day || 'no-day'}_${parsedActivity.startTime || 'no-time'}`;
        
        // Skip if we've already processed this activity
        if (processedActivities.has(activityKey)) {
          logger.info('Skipping duplicate activity', { 
            title: parsedActivity.title,
            day: parsedActivity.day,
            startTime: parsedActivity.startTime
          });
          continue;
        }
        
        processedActivities.add(activityKey);
        try {
          // Calculate start time based on day number if provided
          let startTime: string | undefined;
          if (parsedActivity.day && parsedTrip.startDate) {
            const tripStartDate = new Date(parsedTrip.startDate);
            const activityDate = new Date(tripStartDate);
            activityDate.setDate(tripStartDate.getDate() + parsedActivity.day - 1);
            
            if (parsedActivity.startTime) {
              // Parse time (e.g., "9:00 AM", "14:30")
              const timeMatch = parsedActivity.startTime.match(/(\d{1,2}):?(\d{2})?\s*(AM|PM)?/i);
              if (timeMatch) {
                let hours = parseInt(timeMatch[1], 10);
                const minutes = parseInt(timeMatch[2] || '0', 10);
                const period = timeMatch[3]?.toUpperCase();
                
                // Handle 12-hour to 24-hour conversion correctly
                if (period === 'PM') {
                  if (hours !== 12) {
                    hours += 12;
                  }
                  // 12 PM stays as 12
                } else if (period === 'AM') {
                  if (hours === 12) {
                    hours = 0;
                  }
                  // 12 AM becomes 0, others stay the same
                }
                
                activityDate.setHours(hours, minutes, 0, 0);
                startTime = activityDate.toISOString();
              }
            }
          }

          // Use pre-geocoded location if available
          let locationLat: number | undefined;
          let locationLng: number | undefined;
          
          if (parsedActivity.location) {
            const geocodeResult = geocodingResults.get(parsedActivity.location);
            if (geocodeResult) {
              locationLat = geocodeResult.lat;
              locationLng = geocodeResult.lng;
              logger.debug('Using cached geocoding result', { 
                location: parsedActivity.location, 
                lat: locationLat, 
                lng: locationLng 
              });
            }
          }

          const activity = await tripsService.addActivityToTrip(
            createdTrip.id,
            userId,
            {
              title: parsedActivity.title,
              description: parsedActivity.description,
              type: parsedActivity.type || ActivityType.activity,
              startTime,
              endTime: parsedActivity.endTime,
              location: parsedActivity.location,
              locationLat,
              locationLng,
              price: parsedActivity.price,
              currency: parsedActivity.currency
            }
          );
          activities.push(activity);
          // Increment position for next activity (position tracking disabled for now)
          // currentPosition++;
        } catch (error) {
          logger.error('Failed to create activity', { 
            error: error instanceof Error ? error.message : String(error),
            errorStack: error instanceof Error ? error.stack : undefined,
            activity: parsedActivity,
            tripId: createdTrip.id 
          });
          
          // Store failed activity information
          failedActivities.push({
            activity: parsedActivity,
            error: error instanceof Error ? error.message : 'Unknown error'
          });
          
          // Continue with other activities even if one fails
        }
      }

      // Return the created trip with activities and import summary
      const tripWithActivities = {
        ...createdTrip,
        activities
      };

      // Prepare import summary
      const importSummary = {
        trip: tripWithActivities,
        importStats: {
          totalActivitiesParsed: parsedTrip.activities.length,
          successfullyCreated: activities.length,
          failed: failedActivities.length,
          failedActivities: failedActivities.length > 0 ? failedActivities : undefined,
          sourceFile: req.file.originalname
        }
      };

      logger.info('Successfully imported trip from PDF', { 
        tripId: createdTrip.id, 
        activitiesCount: activities.length,
        failedCount: failedActivities.length,
        filename: req.file.originalname
      });

      // Include warning message if some activities failed
      const message = failedActivities.length > 0
        ? `Trip imported with ${activities.length} activities. ${failedActivities.length} activities could not be created.`
        : 'Trip imported successfully from PDF';

      res.status(201).json(createSuccessResponse(
        importSummary,
        message
      ));
    } catch (error) {
      logger.error('Failed to import trip from PDF', { error });
      
      // Handle validation errors with 400 status
      if (error instanceof ValidationError) {
        res.status(400).json(createErrorResponse(error.message));
        return;
      }
      
      res.status(500).json(createErrorResponse(
        error instanceof Error ? error.message : 'Failed to import trip from PDF'
      ));
    } finally {
      // Always release the lock if it was acquired
      if (lockAcquired && userId) {
        this.importLockService.releaseLock(userId);
      }
    }
  }
}