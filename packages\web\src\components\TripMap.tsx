'use client';

import React from 'react';
import { cn } from '@/lib/utils';
import type { Activity } from '@/stores/trip.store';

interface TripMapProps {
  activities: Activity[];
  className?: string;
}

export default function TripMap({ activities, className }: TripMapProps) {
  return (
    <div 
      data-testid="trip-map" 
      className={cn('w-full h-full min-h-[400px] bg-gray-100 rounded-lg', className)}
    >
      {/* Placeholder for map implementation */}
      <div className="flex items-center justify-center h-full text-gray-500">
        Map View ({activities.length} activities)
      </div>
    </div>
  );
}