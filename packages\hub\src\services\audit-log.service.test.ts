import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { auditLog, AuditEventType } from './audit-log.service';
import { logger } from '../utils/logger';

// Mock dependencies
vi.mock('../../utils/logger', () => ({
  logger: {
    info: vi.fn(),
  },
}));

describe('AuditLogService', () => {
  const originalEnv = process.env.NODE_ENV;

  beforeEach(() => {
    vi.clearAllMocks();
    auditLog.clearLogs();
    process.env.NODE_ENV = 'development';
  });

  afterEach(() => {
    process.env.NODE_ENV = originalEnv;
  });

  describe('logAuthSuccess', () => {
    it('should log successful authentication events', async () => {
      const userId = 'user123';
      const email = '<EMAIL>';
      const ipAddress = '***********';
      const userAgent = 'Mozilla/5.0';

      await auditLog.logAuthSuccess(
        AuditEventType.LOGIN_SUCCESS,
        userId,
        email,
        ipAddress,
        userAgent
      );

      const logs = auditLog.getLogs();
      expect(logs).toHaveLength(1);
      expect(logs[0]).toMatchObject({
        eventType: AuditEventType.LOGIN_SUCCESS,
        userId,
        email,
        ipAddress,
        userAgent,
        timestamp: expect.any(Date),
      });

      expect(logger.info).toHaveBeenCalledWith('[AUDIT]', expect.any(Object));
    });

    it('should handle signup success events', async () => {
      const userId = 'user456';
      const email = '<EMAIL>';

      await auditLog.logAuthSuccess(
        AuditEventType.SIGNUP_SUCCESS,
        userId,
        email
      );

      const logs = auditLog.getLogs();
      expect(logs).toHaveLength(1);
      expect(logs[0].eventType).toBe(AuditEventType.SIGNUP_SUCCESS);
      expect(logs[0].ipAddress).toBeUndefined();
      expect(logs[0].userAgent).toBeUndefined();
    });

    it('should log token refresh events', async () => {
      const userId = 'user789';
      const email = '<EMAIL>';

      await auditLog.logAuthSuccess(
        AuditEventType.TOKEN_REFRESH,
        userId,
        email
      );

      const logs = auditLog.getLogs();
      expect(logs[0].eventType).toBe(AuditEventType.TOKEN_REFRESH);
    });
  });

  describe('logAuthFailure', () => {
    it('should log failed authentication events', async () => {
      const email = '<EMAIL>';
      const reason = 'Invalid credentials';
      const ipAddress = '***********';
      const userAgent = 'Chrome/91.0';

      await auditLog.logAuthFailure(
        AuditEventType.LOGIN_FAILURE,
        email,
        reason,
        ipAddress,
        userAgent
      );

      const logs = auditLog.getLogs();
      expect(logs).toHaveLength(1);
      expect(logs[0]).toMatchObject({
        eventType: AuditEventType.LOGIN_FAILURE,
        email,
        ipAddress,
        userAgent,
        metadata: { reason },
        timestamp: expect.any(Date),
      });
      expect(logs[0].userId).toBeUndefined();
    });

    it('should log signup failures', async () => {
      const email = '<EMAIL>';
      const reason = 'Email already exists';

      await auditLog.logAuthFailure(
        AuditEventType.SIGNUP_FAILURE,
        email,
        reason
      );

      const logs = auditLog.getLogs();
      expect(logs[0].eventType).toBe(AuditEventType.SIGNUP_FAILURE);
      expect(logs[0].metadata?.reason).toBe(reason);
    });

    it('should handle missing optional parameters', async () => {
      const email = '<EMAIL>';
      const reason = 'Some error';

      await auditLog.logAuthFailure(
        AuditEventType.LOGIN_FAILURE,
        email,
        reason
      );

      const logs = auditLog.getLogs();
      expect(logs[0].ipAddress).toBeUndefined();
      expect(logs[0].userAgent).toBeUndefined();
    });
  });

  describe('logSecurityEvent', () => {
    it('should log account locked events', async () => {
      const email = '<EMAIL>';
      const ipAddress = '***********';
      const attemptCount = 5;

      await auditLog.logSecurityEvent(AuditEventType.ACCOUNT_LOCKED, {
        email,
        ipAddress,
        metadata: { attemptCount },
      });

      const logs = auditLog.getLogs();
      expect(logs).toHaveLength(1);
      expect(logs[0]).toMatchObject({
        eventType: AuditEventType.ACCOUNT_LOCKED,
        email,
        ipAddress,
        metadata: { attemptCount },
      });
    });

    it('should log unauthorized access events', async () => {
      const userId = 'user123';
      const resource = '/api/admin';
      const ipAddress = '***********';

      await auditLog.logSecurityEvent(AuditEventType.UNAUTHORIZED_ACCESS, {
        userId,
        ipAddress,
        metadata: { resource },
      });

      const logs = auditLog.getLogs();
      expect(logs[0].eventType).toBe(AuditEventType.UNAUTHORIZED_ACCESS);
      expect(logs[0].metadata?.resource).toBe(resource);
    });

    it('should log password reset requests', async () => {
      const email = '<EMAIL>';
      const ipAddress = '***********';

      await auditLog.logSecurityEvent(AuditEventType.PASSWORD_RESET_REQUEST, {
        email,
        ipAddress,
      });

      const logs = auditLog.getLogs();
      expect(logs[0].eventType).toBe(AuditEventType.PASSWORD_RESET_REQUEST);
    });

    it('should handle all security event types', async () => {
      const testCases = [
        { type: AuditEventType.ACCOUNT_UNLOCKED, data: { email: '<EMAIL>' } },
        { type: AuditEventType.PASSWORD_RESET_SUCCESS, data: { userId: 'user123' } },
      ];

      for (const testCase of testCases) {
        auditLog.clearLogs();
        await auditLog.logSecurityEvent(testCase.type, testCase.data);
        const logs = auditLog.getLogs();
        expect(logs[0].eventType).toBe(testCase.type);
      }
    });
  });

  describe('getLogs', () => {
    it('should return a copy of logs array', async () => {
      await auditLog.logAuthSuccess(
        AuditEventType.LOGIN_SUCCESS,
        'user1',
        '<EMAIL>'
      );
      
      await auditLog.logAuthSuccess(
        AuditEventType.LOGIN_SUCCESS,
        'user2',
        '<EMAIL>'
      );

      const logs = auditLog.getLogs();
      expect(logs).toHaveLength(2);

      // Verify it's a copy, not the original array
      logs.push({
        eventType: AuditEventType.LOGIN_SUCCESS,
        userId: 'user3',
        email: '<EMAIL>',
        timestamp: new Date(),
      });

      const logsAgain = auditLog.getLogs();
      expect(logsAgain).toHaveLength(2); // Original unchanged
    });

    it('should preserve log order', async () => {
      const events = [
        { type: AuditEventType.LOGIN_SUCCESS, userId: 'user1', email: '<EMAIL>' },
        { type: AuditEventType.LOGIN_FAILURE, email: '<EMAIL>', reason: 'Invalid' },
        { type: AuditEventType.SIGNUP_SUCCESS, userId: 'user3', email: '<EMAIL>' },
      ];

      for (const event of events) {
        if (event.userId) {
          await auditLog.logAuthSuccess(event.type as AuditEventType, event.userId, event.email);
        } else {
          await auditLog.logAuthFailure(
            event.type as AuditEventType,
            event.email,
            (event as any).reason || 'Error'
          );
        }
      }

      const logs = auditLog.getLogs();
      expect(logs[0].eventType).toBe(AuditEventType.LOGIN_SUCCESS);
      expect(logs[1].eventType).toBe(AuditEventType.LOGIN_FAILURE);
      expect(logs[2].eventType).toBe(AuditEventType.SIGNUP_SUCCESS);
    });
  });

  describe('clearLogs', () => {
    it('should clear all logs', async () => {
      await auditLog.logAuthSuccess(
        AuditEventType.LOGIN_SUCCESS,
        'user1',
        '<EMAIL>'
      );
      
      await auditLog.logAuthFailure(
        AuditEventType.LOGIN_FAILURE,
        '<EMAIL>',
        'Invalid'
      );

      expect(auditLog.getLogs()).toHaveLength(2);

      auditLog.clearLogs();

      expect(auditLog.getLogs()).toHaveLength(0);
    });

    it('should allow new logs after clearing', async () => {
      await auditLog.logAuthSuccess(
        AuditEventType.LOGIN_SUCCESS,
        'user1',
        '<EMAIL>'
      );

      auditLog.clearLogs();

      await auditLog.logAuthSuccess(
        AuditEventType.LOGIN_SUCCESS,
        'user2',
        '<EMAIL>'
      );

      const logs = auditLog.getLogs();
      expect(logs).toHaveLength(1);
      expect(logs[0].email).toBe('<EMAIL>');
    });
  });

  describe('environment-based logging', () => {
    it('should log to console in development', async () => {
      process.env.NODE_ENV = 'development';

      await auditLog.logAuthSuccess(
        AuditEventType.LOGIN_SUCCESS,
        'user123',
        '<EMAIL>'
      );

      expect(logger.info).toHaveBeenCalledWith('[AUDIT]', expect.any(Object));
    });

    it('should not log to console in production', async () => {
      process.env.NODE_ENV = 'production';

      await auditLog.logAuthSuccess(
        AuditEventType.LOGIN_SUCCESS,
        'user123',
        '<EMAIL>'
      );

      expect(logger.info).not.toHaveBeenCalled();

      // But should still store in memory
      expect(auditLog.getLogs()).toHaveLength(1);
    });

    it('should not log to console in test environment', async () => {
      process.env.NODE_ENV = 'test';

      await auditLog.logAuthSuccess(
        AuditEventType.LOGIN_SUCCESS,
        'user123',
        '<EMAIL>'
      );

      expect(logger.info).not.toHaveBeenCalled();
    });
  });

  describe('timestamp handling', () => {
    it('should add timestamp to all log entries', async () => {
      const before = new Date();

      await auditLog.logAuthSuccess(
        AuditEventType.LOGIN_SUCCESS,
        'user123',
        '<EMAIL>'
      );

      const after = new Date();
      const logs = auditLog.getLogs();
      const timestamp = logs[0].timestamp;

      expect(timestamp).toBeInstanceOf(Date);
      expect(timestamp.getTime()).toBeGreaterThanOrEqual(before.getTime());
      expect(timestamp.getTime()).toBeLessThanOrEqual(after.getTime());
    });
  });
});