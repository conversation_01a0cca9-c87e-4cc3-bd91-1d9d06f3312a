/**
 * API Test Configuration
 * 
 * Configuration specific to API integration tests
 */

const testConfig = require('../test.config');

const apiConfig = {
  ...testConfig.api,
  
  // API-specific settings
  endpoints: {
    auth: {
      login: '/api/v1/auth/login',
      signup: '/api/v1/auth/signup',
      me: '/api/v1/auth/me',
      logout: '/api/v1/auth/logout',
      refresh: '/api/v1/auth/refresh',
    },
    trips: {
      base: '/api/v1/trips',
      create: '/api/v1/trips',
      list: '/api/v1/trips',
      get: (id) => `/api/v1/trips/${id}`,
      update: (id) => `/api/v1/trips/${id}`,
      delete: (id) => `/api/v1/trips/${id}`,
    },
    activities: {
      create: (tripId) => `/api/v1/trips/${tripId}/activities`,
      update: (id) => `/api/v1/activities/${id}`,
      delete: (id) => `/api/v1/activities/${id}`,
    },
    import: {
      parseSimple: '/api/v1/import/parse-simple',
      parse: '/api/v1/import/parse',
      status: (sessionId) => `/api/v1/import/status/${sessionId}`,
    },
    places: {
      autocomplete: '/api/v1/places/autocomplete',
      geocode: '/api/v1/places/geocode',
    },
    health: '/api/v1/health',
  },
  
  // Test data
  testData: {
    user: {
      email: testConfig.auth.testUserEmail,
      password: testConfig.auth.testUserPassword,
    },
    trip: {
      title: 'API Test Trip',
      description: 'Test trip created by API tests',
      destination: 'Paris, France',
      startDate: '2024-06-01',
      endDate: '2024-06-07',
      status: 'planning',
    },
    activity: {
      title: 'Visit Eiffel Tower',
      description: 'Morning visit to the iconic tower',
      type: 'activity',
      startTime: '2024-06-02T09:00:00',
      endTime: '2024-06-02T12:00:00',
      location: 'Eiffel Tower, Paris',
      price: 25,
      currency: 'EUR',
    },
    importContent: `Day 1: Arrive in Tokyo, check into hotel in Shinjuku
Day 2: Visit Senso-ji Temple in the morning, explore Akihabara in afternoon  
Day 3: Day trip to Mount Fuji`,
  },
};

module.exports = apiConfig;