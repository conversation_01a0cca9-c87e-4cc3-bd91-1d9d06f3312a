#!/usr/bin/env node

/**
 * Manual API Test Script for TravelViz
 * 
 * Quick manual tests to verify API endpoints are working
 * Run this while the dev server is running (pnpm dev)
 */

const axios = require('axios');

const BASE_URL = process.env.API_BASE_URL || 'http://localhost:3001';
const FRONTEND_URL = process.env.FRONTEND_URL || 'http://localhost:3000';

// Test user credentials (you'll need to create this user first)
const TEST_USER = {
  email: '<EMAIL>',
  password: 'TestPassword123!'
};

let authToken = '';
let testTripId = '';

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function logTest(name) {
  log(`\n🧪 Testing: ${name}`, 'blue');
}

function logSuccess(message) {
  log(`✅ ${message}`, 'green');
}

function logError(message) {
  log(`❌ ${message}`, 'red');
}

function logWarning(message) {
  log(`⚠️  ${message}`, 'yellow');
}

async function testHealthCheck() {
  logTest('Health Check');
  try {
    const response = await axios.get(`${BASE_URL}/health`);
    if (response.data.status === 'healthy' || response.data.status === 'ok') {
      logSuccess('Health check passed');
      return true;
    } else {
      logError('Health check returned unexpected status');
      return false;
    }
  } catch (error) {
    logError(`Health check failed: ${error.message}`);
    return false;
  }
}

async function testAuth() {
  logTest('Authentication');
  
  try {
    // Test login
    const loginResponse = await axios.post(`${BASE_URL}/api/v1/auth/login`, {
      email: TEST_USER.email,
      password: TEST_USER.password
    });

    if (loginResponse.data.success && loginResponse.data.data.accessToken) {
      authToken = loginResponse.data.data.accessToken;
      logSuccess('Login successful');
      
      // Test /me endpoint
      const meResponse = await axios.get(`${BASE_URL}/api/v1/auth/me`, {
        headers: { Authorization: `Bearer ${authToken}` }
      });
      
      if (meResponse.data.success) {
        logSuccess(`User info retrieved: ${meResponse.data.data.email}`);
        return true;
      } else {
        logError('Failed to get user info');
        return false;
      }
    } else {
      logError('Login failed - check credentials');
      return false;
    }
  } catch (error) {
    if (error.response?.status === 400) {
      logWarning('Login failed - user may not exist. Try creating account first.');
    } else {
      logError(`Auth test failed: ${error.message}`);
    }
    return false;
  }
}

async function testTripsAPI() {
  logTest('Trips API');
  
  if (!authToken) {
    logError('No auth token - skipping trips test');
    return false;
  }

  try {
    // Create a trip
    const createResponse = await axios.post(`${BASE_URL}/api/v1/trips`, {
      title: 'Test Trip - Manual API Test',
      destination: 'Paris, France',
      startDate: '2024-06-01',
      endDate: '2024-06-05',
      description: 'Automated test trip'
    }, {
      headers: { Authorization: `Bearer ${authToken}` }
    });

    if (createResponse.data.success) {
      testTripId = createResponse.data.data.id;
      logSuccess(`Trip created: ${createResponse.data.data.title}`);
      
      // Get trips list
      const listResponse = await axios.get(`${BASE_URL}/api/v1/trips`, {
        headers: { Authorization: `Bearer ${authToken}` }
      });
      
      if (listResponse.data.success) {
        logSuccess(`Retrieved ${listResponse.data.data.length} trips`);
        
        // Get specific trip
        const tripResponse = await axios.get(`${BASE_URL}/api/v1/trips/${testTripId}`, {
          headers: { Authorization: `Bearer ${authToken}` }
        });
        
        if (tripResponse.data.success) {
          logSuccess('Trip details retrieved');
          return true;
        }
      }
    }
    
    logError('Trips API test failed');
    return false;
  } catch (error) {
    logError(`Trips API test failed: ${error.response?.data?.error || error.message}`);
    return false;
  }
}

async function testActivitiesAPI() {
  logTest('Activities API');
  
  if (!authToken || !testTripId) {
    logError('No auth token or trip ID - skipping activities test');
    return false;
  }

  try {
    const activityResponse = await axios.post(`${BASE_URL}/api/v1/trips/${testTripId}/activities`, {
      title: 'Visit Eiffel Tower',
      type: 'sightseeing',
      startTime: '10:00',
      endTime: '12:00',
      date: '2024-06-02',
      location: 'Eiffel Tower, Paris',
      description: 'Test activity'
    }, {
      headers: { Authorization: `Bearer ${authToken}` }
    });

    if (activityResponse.data.success) {
      logSuccess(`Activity created: ${activityResponse.data.data.title}`);
      return true;
    } else {
      logError('Activity creation failed');
      return false;
    }
  } catch (error) {
    logError(`Activities API test failed: ${error.response?.data?.error || error.message}`);
    return false;
  }
}

async function testAIImport() {
  logTest('AI Import API');
  
  if (!authToken) {
    logError('No auth token - skipping AI import test');
    return false;
  }

  const sampleContent = `
User: I'm planning a 3-day trip to Tokyo. Can you help me create an itinerary?A
ssistant: I'd love to help you plan your Tokyo trip! Here's a suggested 3-day itinerary:

Day 1: Traditional Tokyo
- Morning: Visit Senso-ji Temple in Asakusa
- Afternoon: Explore Tokyo National Museum
- Evening: Walk through Ueno Park

Day 2: Modern Tokyo  
- Morning: Tokyo Skytree observation deck
- Afternoon: Shopping in Shibuya and Harajuku
- Evening: Dinner in Shinjuku

Day 3: Culture and Relaxation
- Morning: Meiji Shrine visit
- Afternoon: Imperial Palace East Gardens
- Evening: Traditional dinner in Ginza

This itinerary covers both traditional and modern aspects of Tokyo!
  `;

  try {
    const importResponse = await axios.post(`${BASE_URL}/api/v1/import/parse-simple`, {
      content: sampleContent,
      source: 'manual-test'
    }, {
      headers: { Authorization: `Bearer ${authToken}` }
    });

    if (importResponse.data.success) {
      const importId = importResponse.data.data.importId;
      logSuccess(`AI import started: ${importId}`);
      
      // Wait a moment then check status
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      const statusResponse = await axios.get(`${BASE_URL}/api/v1/import/parse-simple/${importId}`, {
        headers: { Authorization: `Bearer ${authToken}` }
      });
      
      if (statusResponse.data.success) {
        logSuccess(`Import status: ${statusResponse.data.data.status}`);
        return true;
      }
    }
    
    logError('AI import test failed');
    return false;
  } catch (error) {
    logError(`AI import test failed: ${error.response?.data?.error || error.message}`);
    return false;
  }
}

async function testPlacesAPI() {
  logTest('Places API (Google Places)');
  
  if (!authToken) {
    logError('No auth token - skipping places test');
    return false;
  }

  try {
    const placesResponse = await axios.get(`${BASE_URL}/api/v1/places/autocomplete?query=Paris`, {
      headers: { Authorization: `Bearer ${authToken}` }
    });

    if (placesResponse.data.success) {
      logSuccess(`Found ${placesResponse.data.data.length} places for "Paris"`);
      return true;
    } else {
      logError('Places API test failed');
      return false;
    }
  } catch (error) {
    if (error.response?.status === 500) {
      logWarning('Places API failed - check Google Places API key configuration');
    } else {
      logError(`Places API test failed: ${error.response?.data?.error || error.message}`);
    }
    return false;
  }
}

async function testRateLimiting() {
  logTest('Rate Limiting');
  
  try {
    // Make multiple rapid requests to test rate limiting
    const requests = Array(6).fill(null).map(() =>
      axios.post(`${BASE_URL}/api/v1/auth/login`, {
        email: '<EMAIL>',
        password: 'wrongpassword'
      }).catch(error => error.response)
    );

    const responses = await Promise.all(requests);
    const rateLimitedResponses = responses.filter(r => r?.status === 429);
    
    if (rateLimitedResponses.length > 0) {
      logSuccess('Rate limiting is working');
      return true;
    } else {
      logWarning('Rate limiting may not be configured properly');
      return true; // Don't fail the test for this
    }
  } catch (error) {
    logError(`Rate limiting test failed: ${error.message}`);
    return false;
  }
}

async function testCORS() {
  logTest('CORS Configuration');
  
  try {
    const corsResponse = await axios.options(`${BASE_URL}/api/v1/trips`, {
      headers: {
        'Origin': FRONTEND_URL,
        'Access-Control-Request-Method': 'POST',
        'Access-Control-Request-Headers': 'Content-Type,Authorization'
      }
    });

    if (corsResponse.status === 200 || corsResponse.status === 204) {
      logSuccess('CORS is configured correctly');
      return true;
    } else {
      logError('CORS configuration issue');
      return false;
    }
  } catch (error) {
    logError(`CORS test failed: ${error.message}`);
    return false;
  }
}

async function cleanup() {
  logTest('Cleanup');
  
  if (!authToken || !testTripId) {
    logWarning('No cleanup needed');
    return true;
  }

  try {
    await axios.delete(`${BASE_URL}/api/v1/trips/${testTripId}`, {
      headers: { Authorization: `Bearer ${authToken}` }
    });
    logSuccess('Test trip cleaned up');
    return true;
  } catch (error) {
    logWarning(`Cleanup failed: ${error.message}`);
    return true; // Don't fail for cleanup issues
  }
}

async function main() {
  log('\n🚀 TravelViz API Manual Test Suite', 'cyan');
  log('=====================================', 'cyan');
  log(`Testing API at: ${BASE_URL}`, 'blue');
  log(`Frontend URL: ${FRONTEND_URL}`, 'blue');
  
  const tests = [
    testHealthCheck,
    testAuth,
    testTripsAPI,
    testActivitiesAPI,
    testAIImport,
    testPlacesAPI,
    testRateLimiting,
    testCORS,
    cleanup
  ];

  let passed = 0;
  let failed = 0;

  for (const test of tests) {
    try {
      const result = await test();
      if (result) {
        passed++;
      } else {
        failed++;
      }
    } catch (error) {
      logError(`Test error: ${error.message}`);
      failed++;
    }
  }

  log('\n📊 Test Results', 'cyan');
  log('================', 'cyan');
  log(`✅ Passed: ${passed}`, 'green');
  log(`❌ Failed: ${failed}`, 'red');
  log(`📈 Success Rate: ${Math.round((passed / (passed + failed)) * 100)}%`, 'blue');

  if (failed === 0) {
    log('\n🎉 All tests passed! Your API is working correctly.', 'green');
  } else {
    log('\n⚠️  Some tests failed. Check the output above for details.', 'yellow');
    log('\nCommon issues:', 'yellow');
    log('• Make sure the dev server is running: pnpm dev');
    log('• Check environment variables in .env file');
    log('• Verify Supabase configuration');
    log('• Ensure Google Places API key is set');
    log('• Create test user account if auth fails');
  }

  log('\n📝 Next Steps:', 'blue');
  log('• Run full integration tests: node test-api-integration.js');
  log('• Test frontend integration');
  log('• Deploy to staging environment');
}

// Handle errors gracefully
process.on('unhandledRejection', (reason, promise) => {
  logError(`Unhandled Rejection: ${reason}`);
  process.exit(1);
});

// Run the tests
main().catch(error => {
  logError(`Fatal error: ${error.message}`);
  process.exit(1);
});