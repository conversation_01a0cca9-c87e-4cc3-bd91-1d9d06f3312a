-- Fix RLS infinite recursion by using SECURITY DEFINER functions
-- This approach breaks the circular dependency between trips and activities policies

-- Create schema for auth functions if it doesn't exist
CREATE SCHEMA IF NOT EXISTS auth;

-- Create SECURITY DEFINER function to check trip ownership
-- This function runs with elevated privileges, avoiding recursive policy checks
CREATE OR REPLACE FUNCTION auth.user_owns_trip(trip_id uuid)
RETURNS boolean AS $$
BEGIN
  -- Return true if the current user owns the trip
  RETURN EXISTS (
    SELECT 1 
    FROM public.trips 
    WHERE id = trip_id 
    AND user_id = auth.uid()
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER STABLE;

-- Create function to get user's trip IDs (for performance optimization)
CREATE OR REPLACE FUNCTION auth.user_trip_ids()
RETURNS SETOF uuid AS $$
BEGIN
  RETURN QUERY
  SELECT id 
  FROM public.trips 
  WHERE user_id = auth.uid();
END;
$$ LANGUAGE plpgsql SECURITY DEFINER STABLE;

-- <PERSON> execute permissions to authenticated users
GRANT EXECUTE ON FUNCTION auth.user_owns_trip(uuid) TO authenticated;
GRANT EXECUTE ON FUNCTION auth.user_trip_ids() TO authenticated;

-- Drop existing problematic policies on activities
DROP POLICY IF EXISTS "Users can view activities for their trips" ON activities;
DROP POLICY IF EXISTS "Users can insert activities for their trips" ON activities;
DROP POLICY IF EXISTS "Users can update activities for their trips" ON activities;
DROP POLICY IF EXISTS "Users can delete activities for their trips" ON activities;

-- Create new policies using the SECURITY DEFINER functions
-- These avoid recursion by not directly querying trips table with its own policies

-- View activities: use the ownership check function
CREATE POLICY "Users can view activities for their trips" ON activities
    FOR SELECT 
    USING (auth.user_owns_trip(trip_id));

-- Insert activities: check trip ownership
CREATE POLICY "Users can insert activities for their trips" ON activities
    FOR INSERT 
    WITH CHECK (auth.user_owns_trip(trip_id));

-- Update activities: check trip ownership
CREATE POLICY "Users can update activities for their trips" ON activities
    FOR UPDATE 
    USING (auth.user_owns_trip(trip_id));

-- Delete activities: check trip ownership
CREATE POLICY "Users can delete activities for their trips" ON activities
    FOR DELETE 
    USING (auth.user_owns_trip(trip_id));

-- Create optimized policy for better performance on large datasets
-- This uses a materialized check rather than repeated function calls
CREATE POLICY "Service role bypass for activities" ON activities
    FOR ALL
    USING (
        -- Allow service role to bypass all checks
        auth.jwt() ->> 'role' = 'service_role'
        OR 
        -- Otherwise check ownership
        auth.user_owns_trip(trip_id)
    );

-- Add comment explaining the approach
COMMENT ON FUNCTION auth.user_owns_trip(uuid) IS 
'SECURITY DEFINER function to check trip ownership without triggering RLS recursion. 
This function executes with elevated privileges and directly queries the trips table,
avoiding the infinite recursion that occurs when activities policies reference trips
policies that might query activities.';

-- Ensure indexes exist for performance
CREATE INDEX IF NOT EXISTS idx_trips_user_id ON trips(user_id);
CREATE INDEX IF NOT EXISTS idx_activities_trip_id ON activities(trip_id);