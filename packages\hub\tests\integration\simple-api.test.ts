import { describe, it, expect, beforeAll, afterAll } from 'vitest';
import request from 'supertest';
import { getSupabaseClient } from '../../src/lib/supabase';
import { createServer } from '../../src/server';
import { Express } from 'express';
import path from 'path';

// Load environment variables from .env.local
import dotenv from 'dotenv';
dotenv.config({ path: path.join(__dirname, '../../../.env.local') });

// Test configuration
const TEST_USER_EMAIL = '<EMAIL>';
const TEST_USER_PASSWORD = 'Flaremmk123!';

describe('Simple API Test', () => {
  let app: Express;
  let server: any;
  let authToken: string;

  beforeAll(async () => {
    console.log('🚀 Starting simple API test...');
    app = createServer();
    server = app.listen(0);

    // Get auth token directly from Supabase
    const supabase = getSupabaseClient();
    const { data, error } = await supabase.auth.signInWithPassword({
      email: TEST_USER_EMAIL,
      password: TEST_USER_PASSWORD
    });

    if (error) {
      console.error('Auth error:', error);
      throw new Error(`Failed to authenticate: ${error.message}`);
    }

    authToken = data.session?.access_token || '';
    console.log('✅ Got auth token');
  });

  afterAll(async () => {
    server?.close();
  });

  it('should test AI import endpoint', async () => {
    const conversation = `User: Plan a 2-day trip to Paris.
Assistant: Here's your Paris itinerary:

Day 1:
- Morning: Eiffel Tower
- Afternoon: Louvre Museum
- Evening: Seine River cruise

Day 2:
- Morning: Versailles Palace
- Afternoon: Montmartre
- Evening: Latin Quarter dinner`;

    console.log('📤 Testing AI import...');
    
    const response = await request(app)
      .post('/api/v1/import/parse-simple')
      .set('Authorization', `Bearer ${authToken}`)
      .send({
        content: conversation,
        source: 'chatgpt'
      });

    console.log('Response status:', response.status);
    console.log('Response body:', JSON.stringify(response.body, null, 2));

    expect(response.status).toBe(200);
    expect(response.body.success).toBe(true);
    expect(response.body.data).toHaveProperty('importId');
    expect(response.body.data).toHaveProperty('message');
  });

  it('should check import status', async () => {
    // First start an import
    const conversation = `User: Plan a 2-day trip to Rome.
Assistant: Here's your Rome itinerary:

Day 1:
- Morning: Colosseum and Roman Forum
- Afternoon: Pantheon and Trevi Fountain
- Evening: Dinner in Trastevere

Day 2:
- Morning: Vatican Museums and Sistine Chapel
- Afternoon: St. Peter's Basilica
- Evening: Spanish Steps and Villa Borghese`;

    const importResponse = await request(app)
      .post('/api/v1/import/parse-simple')
      .set('Authorization', `Bearer ${authToken}`)
      .send({
        content: conversation,
        source: 'chatgpt'
      });

    console.log('Import response:', JSON.stringify(importResponse.body, null, 2));
    expect(importResponse.status).toBe(200);
    expect(importResponse.body.success).toBe(true);
    
    const importId = importResponse.body.data.importId;
    console.log('Import ID:', importId);

    // Wait a bit for processing
    await new Promise(resolve => setTimeout(resolve, 2000));

    // Check status
    const statusResponse = await request(app)
      .get(`/api/v1/import/parse-simple/${importId}`)
      .set('Authorization', `Bearer ${authToken}`);

    console.log('Status response:', JSON.stringify(statusResponse.body, null, 2));
    expect(statusResponse.status).toBe(200);
  });

  it('should test auth is required', async () => {
    const response = await request(app)
      .post('/api/v1/import/parse-simple')
      .send({
        content: 'test',
        source: 'chatgpt'
      });

    expect(response.status).toBe(401);
  });
});