services:
  - type: web
    name: travelviz-hub
    runtime: node
    region: oregon # Change to your preferred region
    plan: free
    rootDir: packages/hub
    buildCommand: cd ../.. && npm install -g pnpm && pnpm install --frozen-lockfile && pnpm --filter @travelviz/hub build
    startCommand: pnpm start
    envVars:
      - key: NODE_ENV
        value: production
      - key: PORT
        value: 3001
      # Add these in Render dashboard for security:
      # - SUPABASE_URL
      # - SUPABASE_SERVICE_ROLE_KEY
      # - OPENROUTER_API_KEY
      # - MAPBOX_ACCESS_TOKEN
      # - JWT_SECRET