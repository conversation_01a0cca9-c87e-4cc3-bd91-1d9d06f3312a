/**
 * Graduated success criteria for different test scenarios
 */
export interface TestCriteria {
  category: 'simple' | 'complex' | 'edge';
  successRate: number;
  timeout: number;
  retries: number;
}

export const TEST_CRITERIA: Record<string, TestCriteria> = {
  simple: {
    category: 'simple',
    successRate: 0.95, // 95% success rate for simple conversations
    timeout: 5000,     // 5 seconds
    retries: 1,
  },
  complex: {
    category: 'complex',
    successRate: 0.85, // 85% success rate for complex multi-location
    timeout: 10000,    // 10 seconds
    retries: 2,
  },
  edge: {
    category: 'edge',
    successRate: 0.70, // 70% success rate for edge cases
    timeout: 15000,    // 15 seconds
    retries: 3,
  },
};

/**
 * Determine test category based on content characteristics
 */
export function categorizeTest(content: string): TestCriteria['category'] {
  const characteristics = {
    hasMultipleLanguages: /[\u4e00-\u9fa5]|[\u3040-\u309f]|[\u30a0-\u30ff]/.test(content),
    hasCorruptedText: /�|[\u0000-\u001f]/.test(content),
    hasVagueDates: /sometime|roughly|about|few days|maybe/.test(content),
    isLongContent: content.length > 10000,
    hasMultipleCities: (content.match(/day \d/gi) || []).length > 5,
  };

  const edgeCharacteristics = Object.values(characteristics).filter(Boolean).length;
  
  if (edgeCharacteristics >= 2) return 'edge';
  if (edgeCharacteristics === 1 || characteristics.hasMultipleCities) return 'complex';
  return 'simple';
}

/**
 * Test result evaluator with graduated criteria
 */
export class TestResultEvaluator {
  private results: Map<string, { success: number; total: number }> = new Map();

  recordResult(category: TestCriteria['category'], success: boolean): void {
    const current = this.results.get(category) || { success: 0, total: 0 };
    current.total++;
    if (success) current.success++;
    this.results.set(category, current);
  }

  evaluate(): { passed: boolean; report: string } {
    let allPassed = true;
    const reports: string[] = [];

    for (const [category, criteria] of Object.entries(TEST_CRITERIA)) {
      const result = this.results.get(category as TestCriteria['category']);
      if (!result || result.total === 0) continue;

      const successRate = result.success / result.total;
      const passed = successRate >= criteria.successRate;
      allPassed = allPassed && passed;

      reports.push(
        `${category}: ${result.success}/${result.total} (${(successRate * 100).toFixed(1)}%) - ` +
        `${passed ? '✅ PASS' : '❌ FAIL'} (required: ${(criteria.successRate * 100).toFixed(0)}%)`
      );
    }

    return {
      passed: allPassed,
      report: reports.join('\n'),
    };
  }
}

/**
 * Progressive test runner that respects graduated criteria
 */
export async function runProgressiveTest<T>(
  testFn: () => Promise<T>,
  category: TestCriteria['category']
): Promise<{ result?: T; success: boolean; error?: Error }> {
  const criteria = TEST_CRITERIA[category];
  let lastError: Error | undefined;

  for (let attempt = 0; attempt <= criteria.retries; attempt++) {
    try {
      const result = await Promise.race([
        testFn(),
        new Promise<never>((_, reject) =>
          setTimeout(() => reject(new Error('Test timeout')), criteria.timeout)
        ),
      ]);
      return { result, success: true };
    } catch (error) {
      lastError = error as Error;
      if (attempt < criteria.retries) {
        // Exponential backoff between retries
        await new Promise(resolve => setTimeout(resolve, Math.pow(2, attempt) * 1000));
      }
    }
  }

  return { success: false, error: lastError };
}