#!/usr/bin/env node

/**
 * Generate a summary of AI import test results
 * 
 * Usage:
 *   npm run test:summary
 */

const fs = require('fs');
const path = require('path');

const logsDir = path.join(__dirname, '..', 'logs');

if (!fs.existsSync(logsDir)) {
  console.log('No logs directory found. Run tests first with: pnpm test:ai-import');
  process.exit(1);
}

// Find the most recent log file
const files = fs.readdirSync(logsDir)
  .filter(f => f.startsWith('test-run-'))
  .sort()
  .reverse();

if (files.length === 0) {
  console.log('No log files found. Run tests first with: pnpm test:ai-import');
  process.exit(1);
}

const latestLog = path.join(logsDir, files[0]);
const errorLog = latestLog.replace('test-run-', 'test-errors-');

console.log('\n=== AI Import Test Summary ===\n');
console.log(`Log file: ${files[0]}`);
console.log(`Time: ${new Date(files[0].match(/\d{4}-\d{2}-\d{2}T\d{2}-\d{2}-\d{2}/)[0].replace(/T/, ' ').replace(/-/g, ':')).toLocaleString()}\n`);

// Read logs
const content = fs.readFileSync(latestLog, 'utf8');
const lines = content.split('\n');
const errors = fs.existsSync(errorLog) ? fs.readFileSync(errorLog, 'utf8').split('\n') : [];

// Extract test results
const testResults = {
  total: 0,
  passed: 0,
  failed: 0,
  duration: {}
};

// Group tests by suite
const suites = {};
let currentSuite = '';

lines.forEach(line => {
  // Detect test suite
  if (line.includes('[Test Suite Setup]')) {
    currentSuite = 'Setup';
  } else if (line.includes('[health-check]')) {
    currentSuite = 'Health Check';
  } else if (line.includes('[auth-')) {
    currentSuite = 'Authentication';
  } else if (line.includes('[validation-')) {
    currentSuite = 'Input Validation';
  } else if (line.includes('[simple-parse]')) {
    currentSuite = 'Simple Parsing';
  } else if (line.includes('[invalid-')) {
    currentSuite = 'Error Handling';
  }

  // Track test results
  if (line.includes('Test started') && currentSuite) {
    if (!suites[currentSuite]) {
      suites[currentSuite] = { total: 0, passed: 0, failed: 0, tests: [] };
    }
    suites[currentSuite].total++;
    testResults.total++;
  }

  // Track test completion
  if (line.includes('Test completed in') && currentSuite) {
    const match = line.match(/Test completed in (\d+)ms/);
    if (match) {
      const duration = parseInt(match[1]);
      const testName = line.match(/\[([^\]]+)\]/)[1];
      suites[currentSuite].tests.push({ name: testName, duration, passed: true });
      suites[currentSuite].passed++;
      testResults.passed++;
    }
  }

  // Track errors
  if (line.includes('ERROR:') && currentSuite) {
    if (suites[currentSuite] && suites[currentSuite].tests.length > 0) {
      const lastTest = suites[currentSuite].tests[suites[currentSuite].tests.length - 1];
      lastTest.passed = false;
      suites[currentSuite].passed--;
      suites[currentSuite].failed++;
      testResults.passed--;
      testResults.failed++;
    }
  }
});

// Display results by suite
console.log('Test Results by Suite:');
console.log('─'.repeat(60));

Object.entries(suites).forEach(([suiteName, suite]) => {
  const icon = suite.failed === 0 ? '✅' : '❌';
  console.log(`\n${icon} ${suiteName}`);
  console.log(`   Total: ${suite.total} | Passed: ${suite.passed} | Failed: ${suite.failed}`);
  
  if (suite.tests.length > 0) {
    console.log('   Tests:');
    suite.tests.forEach(test => {
      const testIcon = test.passed ? '✓' : '✗';
      const duration = test.duration ? `(${test.duration}ms)` : '';
      console.log(`     ${testIcon} ${test.name} ${duration}`);
    });
  }
});

// Display overall summary
console.log('\n' + '─'.repeat(60));
console.log('\nOverall Summary:');
console.log(`Total Tests: ${testResults.total}`);
console.log(`Passed: ${testResults.passed} (${testResults.total > 0 ? Math.round(testResults.passed / testResults.total * 100) : 0}%)`);
console.log(`Failed: ${testResults.failed}`);

// Check for specific issues
const rateLimit429 = lines.filter(l => l.includes('429')).length;
const timeouts = lines.filter(l => l.includes('timeout')).length;
const authFailures = lines.filter(l => l.includes('Auth failed')).length;

if (rateLimit429 > 0 || timeouts > 0 || authFailures > 0) {
  console.log('\nCommon Issues Detected:');
  if (rateLimit429 > 0) console.log(`- Rate limiting (429): ${rateLimit429} occurrences`);
  if (timeouts > 0) console.log(`- Timeouts: ${timeouts} occurrences`);
  if (authFailures > 0) console.log(`- Auth failures: ${authFailures} occurrences`);
}

// Show error summary
if (errors.length > 0) {
  const uniqueErrors = [...new Set(errors.filter(e => e.includes('ERROR:')))];
  console.log(`\nUnique Errors: ${uniqueErrors.length}`);
  console.log('(Run "pnpm test:logs:errors" to view full error log)');
}

console.log('\n' + '='.repeat(60) + '\n');