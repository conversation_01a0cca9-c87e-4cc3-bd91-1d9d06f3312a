import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { AIRouterService } from './aiRouter.service';

// Mock fetch
global.fetch = vi.fn();

describe('AIRouterService', () => {
  let aiRouterService: AIRouterService;

  beforeEach(() => {
    vi.clearAllMocks();
    // Get singleton instance
    aiRouterService = AIRouterService.getInstance();
    // Clear health cache between tests
    aiRouterService.clearHealthCache();
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('getInstance', () => {
    it('should return singleton instance', () => {
      const instance1 = AIRouterService.getInstance();
      const instance2 = AIRouterService.getInstance();

      expect(instance1).toBe(instance2);
    });
  });

  describe('selectModel', () => {
    it('should select appropriate model for itinerary task', () => {
      const selection = aiRouterService.selectModel('itinerary', {
        preferQuality: true,
      });

      expect(selection).toHaveProperty('modelId');
      expect(selection).toHaveProperty('config');
      expect(selection).toHaveProperty('reasoning');
      expect(selection.config).toHaveProperty('id');
      expect(selection.config).toHaveProperty('name');
      expect(selection.config).toHaveProperty('provider');
    });

    it('should prefer free models when preferFree is true', () => {
      const selection = aiRouterService.selectModel('parse', {
        preferFree: true,
      });

      // Should return a valid selection with reasoning
      expect(selection.reasoning).toBeTruthy();
      // Should select the free model when preferFree is true
      expect(selection.modelId).toBe('deepseek/deepseek-chat-v3-0324:free');
    });

    it('should prefer fast models when preferSpeed is true', () => {
      const selection = aiRouterService.selectModel('suggest', {
        preferSpeed: true,
      });

      expect(selection.reasoning.toLowerCase()).toMatch(/fast|speed|latency/);
    });

    it('should handle unknown task types', () => {
      const selection = aiRouterService.selectModel('unknown' as any);

      // Should fallback to default model
      expect(selection.modelId).toBeTruthy();
      expect(selection.config).toBeTruthy();
    });

    it('should respect maximum latency constraint', () => {
      const selection = aiRouterService.selectModel('places', {
        maxLatency: 5000,
      });

      // If model has latency info, it should be under the limit
      if (selection.config.averageLatency !== undefined) {
        expect(selection.config.averageLatency).toBeLessThanOrEqual(5000);
      } else {
        // Otherwise just check that a model was selected
        expect(selection.modelId).toBeTruthy();
      }
    });
  });

  describe('getModelConfig', () => {
    it('should return model configuration by ID', () => {
      const config = aiRouterService.getModelConfig('anthropic/claude-3-haiku');

      expect(config).toBeTruthy();
      expect(config?.id).toBe('anthropic/claude-3-haiku');
      expect(config?.name).toContain('Claude');
    });

    it('should return null for unknown model ID', () => {
      const config = aiRouterService.getModelConfig('unknown-model');

      expect(config).toBeNull();
    });
  });

  describe('getAllModels', () => {
    it('should return array of all available models', () => {
      const models = aiRouterService.getAllModels();

      expect(Array.isArray(models)).toBe(true);
      expect(models.length).toBeGreaterThan(0);
      expect(models[0]).toHaveProperty('id');
      expect(models[0]).toHaveProperty('name');
      expect(models[0]).toHaveProperty('provider');
    });
  });

  describe('checkModelHealth', () => {
    it('should check model health and cache result', async () => {
      const mockResponse = {
        ok: true,
        status: 200,
      };

      vi.mocked(global.fetch).mockResolvedValueOnce(mockResponse as any);

      const isHealthy = await aiRouterService.checkModelHealth('anthropic/claude-3-haiku');

      expect(isHealthy).toBe(true);
      expect(global.fetch).toHaveBeenCalledWith(
        expect.stringContaining('openrouter.ai'),
        expect.objectContaining({
          method: 'HEAD',
          signal: expect.any(AbortSignal),
        })
      );
    });

    it('should return cached health check result', async () => {
      // First call
      vi.mocked(global.fetch).mockResolvedValueOnce({ ok: true } as any);
      await aiRouterService.checkModelHealth('anthropic/claude-3-haiku');

      // Clear mock calls
      vi.clearAllMocks();

      // Second call should use cache
      const isHealthy = await aiRouterService.checkModelHealth('anthropic/claude-3-haiku');

      expect(isHealthy).toBe(true);
      expect(global.fetch).not.toHaveBeenCalled();
    });

    it('should return false for unknown models', async () => {
      const isHealthy = await aiRouterService.checkModelHealth('unknown-model');

      expect(isHealthy).toBe(false);
      expect(global.fetch).not.toHaveBeenCalled();
    });

    it('should handle health check failures', async () => {
      vi.mocked(global.fetch).mockRejectedValueOnce(new Error('Network error'));

      const isHealthy = await aiRouterService.checkModelHealth('anthropic/claude-3-haiku');

      expect(isHealthy).toBe(false);
      expect(global.fetch).toHaveBeenCalled();
    });
  });

  describe('model selection reasoning', () => {
    it('should provide clear reasoning for model selection', () => {
      const tasks = ['itinerary', 'parse', 'suggest', 'places'] as const;

      tasks.forEach(task => {
        const selection = aiRouterService.selectModel(task);

        expect(selection.reasoning).toBeTruthy();
        // Check that reasoning mentions the task context
        const taskKeywords = {
          itinerary: 'itinerary',
          parse: 'parse',
          suggest: 'suggest',
          places: 'place',
        };
        expect(selection.reasoning.toLowerCase()).toContain(taskKeywords[task] || task);
        expect(selection.reasoning.length).toBeGreaterThan(10);
      });
    });

    it('should adjust weights based on preferences', () => {
      // Quality preference
      const qualitySelection = aiRouterService.selectModel('itinerary', {
        preferQuality: true,
      });
      expect(qualitySelection.reasoning.toLowerCase()).toMatch(
        /high reliability|quality|optimized/i
      );

      // Speed preference
      const speedSelection = aiRouterService.selectModel('suggest', {
        preferSpeed: true,
      });
      expect(speedSelection.reasoning.toLowerCase()).toMatch(/fast|speed|latency|optimized/i);

      // Cost preference
      const costSelection = aiRouterService.selectModel('parse', {
        preferFree: true,
      });
      // Should return a valid selection
      expect(costSelection.reasoning).toBeTruthy();
      expect(costSelection.modelId).toBeTruthy();
    });
  });
});
