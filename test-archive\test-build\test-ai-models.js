"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
const playwright_1 = require("playwright");
const fs = __importStar(require("fs"));
const path = __importStar(require("path"));
const TEST_MODELS = [
    'openrouter/cypher-alpha:free',
    'deepseek/deepseek-chat-v3-0324:free',
    'deepseek/deepseek-r1-0528:free',
    'google/gemini-2.0-flash-001',
    'google/gemini-2.5-flash-preview-05-20'
];
const TEST_USER = {
    email: '<EMAIL>',
    password: 'Flaremmk123!'
};
const PDF_PATH = path.join(__dirname, 'Updated 15-Day European Travel Itinerary_ London, Madrid, Lisbon and Porto.pdf');
class AIModelTester {
    constructor() {
        this.browser = null;
        this.context = null;
        this.page = null;
        this.results = [];
    }
    async initialize() {
        console.log('🚀 Initializing Playwright browser...');
        this.browser = await playwright_1.chromium.launch({
            headless: false,
            slowMo: 100
        });
        this.context = await this.browser.newContext({
            viewport: { width: 1280, height: 720 }
        });
        this.page = await this.context.newPage();
    }
    async login() {
        console.log('🔐 Logging in with test user...');
        await this.page.goto('http://localhost:3000/login');
        // Fill login form
        await this.page.fill('input[name="email"]', TEST_USER.email);
        await this.page.fill('input[name="password"]', TEST_USER.password);
        await this.page.click('button[type="submit"]');
        // Wait for successful login
        await this.page.waitForURL('**/dashboard', { timeout: 10000 });
        console.log('✅ Login successful');
    }
    async testModel(model) {
        console.log(`\n🧪 Testing model: ${model}`);
        const startTime = Date.now();
        try {
            // Navigate to upload page
            await this.page.goto('http://localhost:3000/upload');
            await this.page.waitForLoadState('networkidle');
            // Check if there's a tab to switch to text mode
            const textTabButton = await this.page.$('button:has-text("Paste Text")');
            if (textTabButton) {
                await textTabButton.click();
                await this.page.waitForTimeout(500);
            }
            // Read PDF content (we'll simulate by reading a text file for now)
            // In real scenario, we'd need to extract text from PDF first
            const pdfText = await this.extractPDFText();
            // Find and fill the text area
            const textarea = await this.page.$('textarea');
            if (!textarea) {
                throw new Error('Could not find textarea for text input');
            }
            await textarea.fill(pdfText);
            // Select source (we'll use 'chatgpt' as default)
            const sourceSelect = await this.page.$('select[name="source"]');
            if (sourceSelect) {
                await sourceSelect.selectOption('chatgpt');
            }
            // If model selection is available in UI, select it
            // For now, we'll need to modify the request interceptor
            await this.setupRequestInterceptor(model);
            // Submit the form
            const submitButton = await this.page.$('button:has-text("Import")');
            if (!submitButton) {
                throw new Error('Could not find submit button');
            }
            await submitButton.click();
            // Wait for navigation to trip detail page
            await this.page.waitForURL('**/plan/*', { timeout: 60000 });
            // Extract trip data
            await this.page.waitForTimeout(2000); // Let the page fully load
            // Get activities count
            const activities = await this.page.$$('[data-testid="activity-item"], .activity-card, [class*="activity"]');
            const activitiesCount = activities.length;
            // Take screenshot
            const screenshotPath = `test-results/model-${model.replace(/[/:]/g, '-')}.png`;
            await this.page.screenshot({
                path: screenshotPath,
                fullPage: true
            });
            // Extract trip data for analysis
            const tripData = await this.extractTripData();
            const parseTime = Date.now() - startTime;
            console.log(`✅ Model ${model} completed: ${activitiesCount} activities in ${parseTime}ms`);
            return {
                model,
                success: true,
                activitiesCount,
                parseTime,
                tripData,
                screenshot: screenshotPath
            };
        }
        catch (error) {
            const parseTime = Date.now() - startTime;
            console.error(`❌ Model ${model} failed:`, error);
            // Take error screenshot
            const screenshotPath = `test-results/error-${model.replace(/[/:]/g, '-')}.png`;
            await this.page.screenshot({ path: screenshotPath });
            return {
                model,
                success: false,
                activitiesCount: 0,
                parseTime,
                error: error instanceof Error ? error.message : 'Unknown error',
                screenshot: screenshotPath
            };
        }
    }
    async setupRequestInterceptor(model) {
        // Intercept the API request to inject the model parameter
        await this.page.route('**/api/v1/import/parse', async (route) => {
            const request = route.request();
            const postData = request.postData();
            if (postData) {
                const body = JSON.parse(postData);
                body.model = model;
                await route.continue({
                    postData: JSON.stringify(body),
                    headers: {
                        ...request.headers(),
                        'content-type': 'application/json'
                    }
                });
            }
            else {
                await route.continue();
            }
        });
    }
    async extractPDFText() {
        // For testing, we'll use a sample itinerary text
        // In production, you'd use a PDF parsing library
        return `
15-Day European Travel Itinerary: London, Madrid, Lisbon and Porto

Day 1: Arrival in London
- 10:00 AM: Arrive at Heathrow Airport
- 12:00 PM: Check-in at The Zetter Townhouse (Marylebone)
- 2:00 PM: Lunch at Dishoom (Indian cuisine)
- 4:00 PM: Visit British Museum
- 7:00 PM: Dinner at Hawksmoor (Steakhouse)

Day 2: London Exploration
- 9:00 AM: Breakfast at The Breakfast Club
- 10:00 AM: Tour Tower of London
- 1:00 PM: Lunch at Borough Market
- 3:00 PM: Walk along South Bank
- 5:00 PM: Visit Tate Modern
- 8:00 PM: West End Theater show

Day 3: London to Madrid
- 9:00 AM: Check out from hotel
- 11:00 AM: Flight from London to Madrid (BA458)
- 3:00 PM: Arrive in Madrid
- 4:00 PM: Check-in at Hotel Urban
- 6:00 PM: Walk around Gran Via
- 8:30 PM: Tapas dinner at Mercado de San Miguel

Day 4: Madrid Museums
- 9:00 AM: Breakfast at Café de Oriente
- 10:00 AM: Visit Prado Museum
- 1:00 PM: Lunch at Sobrino de Botín
- 3:00 PM: Explore Retiro Park
- 5:00 PM: Visit Reina Sofia Museum
- 9:00 PM: Dinner at Casa Lucio

Day 5: Madrid Day Trip
- 8:00 AM: Day trip to Toledo
- 10:00 AM: Toledo Cathedral
- 1:00 PM: Lunch in Toledo
- 4:00 PM: Return to Madrid
- 8:00 PM: Flamenco show and dinner

Day 6: Madrid to Lisbon
- 10:00 AM: Check out from hotel
- 12:00 PM: Flight to Lisbon (IB3118)
- 1:30 PM: Arrive in Lisbon
- 3:00 PM: Check-in at Memmo Alfama Hotel
- 5:00 PM: Explore Alfama district
- 8:00 PM: Fado dinner at Clube de Fado

Day 7: Lisbon Highlights
- 9:00 AM: Pastéis de Belém breakfast
- 10:00 AM: Visit Jerónimos Monastery
- 12:00 PM: Explore Belém Tower
- 2:00 PM: Lunch at Time Out Market
- 4:00 PM: Ride Tram 28
- 7:00 PM: Sunset at Miradouro da Senhora do Monte
- 9:00 PM: Dinner at Ramiro (seafood)

Day 8: Sintra Day Trip
- 8:00 AM: Train to Sintra
- 10:00 AM: Visit Pena Palace
- 1:00 PM: Lunch in Sintra
- 3:00 PM: Explore Quinta da Regaleira
- 6:00 PM: Return to Lisbon
- 8:00 PM: Dinner at Cervejaria Trindade

Day 9: Lisbon to Porto
- 10:00 AM: Check out from hotel
- 11:00 AM: Train to Porto (3 hours)
- 2:30 PM: Arrive in Porto
- 3:30 PM: Check-in at The Yeatman Hotel
- 5:00 PM: Port wine tasting in Vila Nova de Gaia
- 8:00 PM: Dinner at DOP Restaurant

Day 10: Porto Old Town
- 9:00 AM: Breakfast at Majestic Café
- 10:00 AM: Visit São Bento Station
- 11:00 AM: Tour Porto Cathedral
- 1:00 PM: Lunch at Cantinho do Avillez
- 3:00 PM: Explore Ribeira district
- 5:00 PM: Visit Livraria Lello bookstore
- 8:00 PM: Dinner at O Paparico

Day 11: Douro Valley
- 8:00 AM: Day trip to Douro Valley
- 10:00 AM: Vineyard tour and tasting
- 1:00 PM: Lunch with valley views
- 4:00 PM: River cruise
- 7:00 PM: Return to Porto
- 9:00 PM: Light dinner at local tasca

Day 12: Porto to Lisbon
- 10:00 AM: Check out from hotel
- 11:00 AM: Train back to Lisbon
- 2:30 PM: Arrive in Lisbon
- 3:30 PM: Check-in at Avenida Palace
- 5:00 PM: Shopping in Chiado
- 8:00 PM: Dinner at Belcanto (Michelin star)

Day 13: Lisbon Coast
- 9:00 AM: Day trip to Cascais
- 11:00 AM: Beach time at Praia do Guincho
- 1:00 PM: Seafood lunch in Cascais
- 4:00 PM: Return to Lisbon
- 6:00 PM: Explore Bairro Alto
- 9:00 PM: Dinner and drinks in Bairro Alto

Day 14: Final Day in Lisbon
- 10:00 AM: Visit Gulbenkian Museum
- 1:00 PM: Lunch at A Cevicheria
- 3:00 PM: Last-minute shopping
- 5:00 PM: Relax at hotel spa
- 8:00 PM: Farewell dinner at Alma

Day 15: Departure
- 9:00 AM: Check out from hotel
- 11:00 AM: Depart for airport
- 2:00 PM: Flight home

Budget Estimate:
- Flights: €600
- Hotels: €2,100 (€150/night average)
- Meals: €1,050 (€70/day)
- Activities: €400
- Transport: €300
- Total: €4,450 per person
    `;
    }
    async extractTripData() {
        // Extract relevant trip data from the page
        try {
            const tripData = await this.page.evaluate(() => {
                const data = {
                    title: document.querySelector('h1')?.textContent || '',
                    activities: []
                };
                // Try different selectors for activities
                const activityElements = document.querySelectorAll('[data-testid="activity-item"], .activity-card, [class*="activity"], [class*="timeline-item"]');
                activityElements.forEach((element) => {
                    const activity = {
                        title: element.querySelector('[class*="title"], h3, h4')?.textContent || '',
                        time: element.querySelector('[class*="time"]')?.textContent || '',
                        location: element.querySelector('[class*="location"]')?.textContent || '',
                        type: element.querySelector('[class*="type"]')?.textContent || ''
                    };
                    if (activity.title) {
                        data.activities.push(activity);
                    }
                });
                return data;
            });
            return tripData;
        }
        catch (error) {
            console.error('Failed to extract trip data:', error);
            return null;
        }
    }
    async runAllTests() {
        console.log('🏁 Starting AI Model Testing Suite');
        console.log(`📋 Testing ${TEST_MODELS.length} models`);
        await this.initialize();
        await this.login();
        // Create results directory
        if (!fs.existsSync('test-results')) {
            fs.mkdirSync('test-results');
        }
        // Test each model
        for (const model of TEST_MODELS) {
            const result = await this.testModel(model);
            this.results.push(result);
            // Wait between tests to avoid rate limiting
            await this.page.waitForTimeout(5000);
        }
        // Generate report
        await this.generateReport();
        // Clean up
        await this.cleanup();
    }
    async generateReport() {
        console.log('\n📊 Generating Test Report...');
        // Sort results by activities count (descending)
        const sortedResults = [...this.results].sort((a, b) => b.activitiesCount - a.activitiesCount);
        let report = `# AI Model Testing Report
Generated: ${new Date().toISOString()}

## Summary
- Models Tested: ${TEST_MODELS.length}
- Successful: ${this.results.filter(r => r.success).length}
- Failed: ${this.results.filter(r => !r.success).length}

## Results by Performance

| Rank | Model | Success | Activities | Parse Time | Notes |
|------|-------|---------|------------|------------|-------|
`;
        sortedResults.forEach((result, index) => {
            report += `| ${index + 1} | ${result.model} | ${result.success ? '✅' : '❌'} | ${result.activitiesCount} | ${result.parseTime}ms | ${result.error || 'OK'} |\n`;
        });
        report += `\n## Detailed Analysis\n\n`;
        // Analyze each model
        sortedResults.forEach((result) => {
            report += `### ${result.model}\n`;
            report += `- **Status**: ${result.success ? 'Success' : 'Failed'}\n`;
            report += `- **Activities Detected**: ${result.activitiesCount}\n`;
            report += `- **Parse Time**: ${result.parseTime}ms\n`;
            if (result.error) {
                report += `- **Error**: ${result.error}\n`;
            }
            if (result.screenshot) {
                report += `- **Screenshot**: [View](${result.screenshot})\n`;
            }
            report += `\n`;
        });
        // Recommendations
        report += `## Recommendations\n\n`;
        const bestModel = sortedResults.find(r => r.success);
        if (bestModel) {
            report += `**Best Performing Model**: ${bestModel.model}\n`;
            report += `- Detected ${bestModel.activitiesCount} activities\n`;
            report += `- Completed in ${bestModel.parseTime}ms\n\n`;
            // Quality analysis
            const avgActivities = sortedResults
                .filter(r => r.success)
                .reduce((sum, r) => sum + r.activitiesCount, 0) / sortedResults.filter(r => r.success).length;
            report += `**Quality Metrics**:\n`;
            report += `- Average activities detected: ${avgActivities.toFixed(1)}\n`;
            report += `- Expected activities from itinerary: ~45-50\n`;
            report += `- Best detection rate: ${((bestModel.activitiesCount / 45) * 100).toFixed(1)}%\n`;
        }
        // Save report
        fs.writeFileSync('test-results/ai-model-test-report.md', report);
        console.log('✅ Report saved to test-results/ai-model-test-report.md');
        // Save raw results as JSON
        fs.writeFileSync('test-results/ai-model-test-results.json', JSON.stringify(this.results, null, 2));
    }
    async cleanup() {
        console.log('\n🧹 Cleaning up...');
        if (this.page)
            await this.page.close();
        if (this.context)
            await this.context.close();
        if (this.browser)
            await this.browser.close();
    }
}
// Run the tests
async function main() {
    const tester = new AIModelTester();
    try {
        await tester.runAllTests();
        console.log('\n✅ All tests completed!');
    }
    catch (error) {
        console.error('\n❌ Test suite failed:', error);
        process.exit(1);
    }
}
// Check if dev server is running
async function checkDevServer() {
    try {
        const response = await fetch('http://localhost:3000');
        return response.ok;
    }
    catch {
        return false;
    }
}
// Entry point
(async () => {
    console.log('🔍 Checking if dev server is running...');
    const isRunning = await checkDevServer();
    if (!isRunning) {
        console.error('❌ Dev server is not running. Please run "pnpm dev" first.');
        process.exit(1);
    }
    console.log('✅ Dev server is running');
    await main();
})();
