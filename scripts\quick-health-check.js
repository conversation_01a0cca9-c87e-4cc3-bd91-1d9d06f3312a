#!/usr/bin/env node

const http = require('http');

const COLORS = {
  RED: '\x1b[31m',
  GREEN: '\x1b[32m',
  YELLOW: '\x1b[33m',
  BLUE: '\x1b[34m',
  RESET: '\x1b[0m'
};

const HUB_PORT = 3001;
const WEB_PORTS = [3000, 3001, 3002, 3003]; // Common ports Next.js might use

function log(message, color = COLORS.RESET) {
  console.log(`${color}${message}${COLORS.RESET}`);
}

function checkHealth(port, serviceName) {
  return new Promise((resolve) => {
    const options = {
      hostname: 'localhost',
      port: port,
      path: '/health',
      method: 'GET',
      timeout: 1000
    };

    const req = http.request(options, (res) => {
      if (res.statusCode === 200) {
        resolve({ healthy: true, port });
      } else {
        resolve({ healthy: false, port });
      }
    });

    req.on('error', () => {
      resolve({ healthy: false, port });
    });

    req.on('timeout', () => {
      req.destroy();
      resolve({ healthy: false, port });
    });

    req.end();
  });
}

async function findWebService() {
  for (const port of WEB_PORTS) {
    const result = await checkHealth(port, 'Web');
    if (result.healthy) {
      return result;
    }
  }
  return { healthy: false, port: null };
}

async function runQuickHealthCheck() {
  log('🏥 Running quick health check...', COLORS.BLUE);
  
  // Check hub
  log('🔍 Checking hub health...', COLORS.BLUE);
  const hubResult = await checkHealth(HUB_PORT, 'Hub');
  
  // Check web
  log('🔍 Checking web health...', COLORS.BLUE);
  const webResult = await findWebService();
  
  let exitCode = 0;
  
  if (hubResult.healthy) {
    log(`✅ Hub is healthy on port ${HUB_PORT}`, COLORS.GREEN);
  } else {
    log(`❌ Hub is not running or unhealthy`, COLORS.RED);
    exitCode = 1;
  }
  
  if (webResult.healthy) {
    log(`✅ Web is healthy on port ${webResult.port}`, COLORS.GREEN);
  } else {
    log(`❌ Web is not running or unhealthy`, COLORS.RED);
    exitCode = 1;
  }
  
  if (exitCode === 0) {
    log('✅ All services are healthy!', COLORS.GREEN);
  } else {
    log('❌ Some services are not healthy. Please run "pnpm dev" and ensure all services start correctly.', COLORS.RED);
  }
  
  process.exit(exitCode);
}

// Run if called directly
if (require.main === module) {
  runQuickHealthCheck().catch(err => {
    log(`❌ Health check error: ${err.message}`, COLORS.RED);
    process.exit(1);
  });
}

module.exports = { runQuickHealthCheck };