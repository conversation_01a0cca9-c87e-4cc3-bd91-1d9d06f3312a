#!/usr/bin/env node

const { execSync } = require('child_process');
const { readFileSync, existsSync } = require('fs');
const { resolve, relative } = require('path');
const { readdirSync, statSync } = require('fs');
const { join } = require('path');

const COLORS = {
  RED: '\x1b[31m',
  GREEN: '\x1b[32m',
  YELLOW: '\x1b[33m',
  BLUE: '\x1b[34m',
  RESET: '\x1b[0m'
};

function log(message, color = COLORS.RESET) {
  console.log(`${color}${message}${COLORS.RESET}`);
}

/**
 * Validates that all imports follow proper module resolution rules
 * that will work in both test and production environments
 */
function validateImports() {
  log('🔍 Validating module imports...', COLORS.BLUE);
  
  const errors = [];
  const warnings = [];
  
  // Find all TypeScript files
  function findFiles(dir, pattern = /\.(ts|tsx)$/, ignore = /\.(test|spec)\.(ts|tsx)$/) {
    const files = [];
    
    function walk(currentDir) {
      try {
        const entries = readdirSync(currentDir);
        
        for (const entry of entries) {
          const fullPath = join(currentDir, entry);
          const stat = statSync(fullPath);
          
          if (stat.isDirectory()) {
            if (!entry.includes('node_modules') && !entry.startsWith('.')) {
              walk(fullPath);
            }
          } else if (stat.isFile() && pattern.test(entry) && !ignore.test(entry)) {
            files.push(fullPath);
          }
        }
      } catch (e) {
        // Skip directories we can't read
      }
    }
    
    walk(dir);
    return files;
  }
  
  const files = [];
  const packagesDir = resolve(__dirname, '../packages');
  
  if (existsSync(packagesDir)) {
    const packages = readdirSync(packagesDir);
    for (const pkg of packages) {
      const srcDir = join(packagesDir, pkg, 'src');
      if (existsSync(srcDir)) {
        files.push(...findFiles(srcDir));
      }
    }
  }
  
  files.forEach(file => {
    const content = readFileSync(file, 'utf8');
    const lines = content.split('\n');
    
    lines.forEach((line, index) => {
      // Check for @travelviz/shared subpath imports
      const sharedSubpathMatch = line.match(/from\s+['"]@travelviz\/shared\/([^'"]+)['"]/);
      if (sharedSubpathMatch) {
        errors.push({
          file,
          line: index + 1,
          message: `Invalid subpath import: @travelviz/shared/${sharedSubpathMatch[1]}`,
          fix: 'Use: import { ... } from "@travelviz/shared"'
        });
      }
      
      // Check for relative imports that cross package boundaries
      const relativeMatch = line.match(/from\s+['"](\.\.[^'"]+)['"]/);
      if (relativeMatch && relativeMatch[1].includes('../../../')) {
        warnings.push({
          file,
          line: index + 1,
          message: `Deep relative import: ${relativeMatch[1]}`,
          fix: 'Consider using package imports instead'
        });
      }
    });
  });
  
  // Check if shared package is built
  const sharedDistPath = resolve(__dirname, '../packages/shared/dist');
  if (!existsSync(sharedDistPath)) {
    errors.push({
      file: 'packages/shared',
      line: 0,
      message: 'Shared package not built',
      fix: 'Run: pnpm --filter @travelviz/shared build'
    });
  }
  
  // Report results
  if (errors.length > 0) {
    log('\n❌ Import validation failed!', COLORS.RED);
    log(`\nFound ${errors.length} error(s):\n`, COLORS.RED);
    
    errors.forEach(error => {
      log(`${error.file}:${error.line}`, COLORS.YELLOW);
      log(`  ${error.message}`, COLORS.RED);
      log(`  Fix: ${error.fix}`, COLORS.GREEN);
    });
  }
  
  if (warnings.length > 0) {
    log(`\n⚠️  Found ${warnings.length} warning(s):`, COLORS.YELLOW);
    
    warnings.forEach(warning => {
      log(`${warning.file}:${warning.line}`, COLORS.YELLOW);
      log(`  ${warning.message}`);
      log(`  Suggestion: ${warning.fix}`, COLORS.BLUE);
    });
  }
  
  if (errors.length === 0 && warnings.length === 0) {
    log('\n✅ All imports are valid!', COLORS.GREEN);
  }
  
  // Exit with error code if there are errors
  if (errors.length > 0) {
    process.exit(1);
  }
}

// Run validation
validateImports();