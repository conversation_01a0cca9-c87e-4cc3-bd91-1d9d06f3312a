/**
 * AI Router Service
 * Intelligent model selection and routing for different AI tasks
 */

export type AITask = 'itinerary' | 'parse' | 'suggest' | 'places';

export interface ModelConfig {
  id: string;
  name: string;
  provider: 'openrouter';
  endpoint: string;
  maxTokens: number;
  temperature: number;
  costPerMToken?: number; // Cost per million tokens
  averageLatency?: number; // Average response time in ms
  reliability?: number; // Success rate 0-1
}

export interface ModelSelection {
  modelId: string;
  config: ModelConfig;
  reasoning: string;
}

/**
 * AI Router Service for intelligent model selection
 */
export class AIRouterService {
  private static instance: AIRouterService;
  private models: Map<string, ModelConfig> = new Map();
  private healthCheckCache: Map<string, { healthy: boolean; checkedAt: Date }> = new Map();
  private healthCheckCacheTTL = 5 * 60 * 1000; // 5 minutes
  private modelUsage: Map<string, { count: number; lastReset: Date }> = new Map();
  private rotationIndex: Map<string, number> = new Map();

  constructor() {
    this.initializeModels();
    // Perform initial health checks asynchronously
    this.performStartupHealthChecks();
  }

  static getInstance(): AIRouterService {
    if (!AIRouterService.instance) {
      AIRouterService.instance = new AIRouterService();
    }
    return AIRouterService.instance;
  }

  /**
   * Perform health checks on startup
   */
  private async performStartupHealthChecks(): Promise<void> {
    // Silent health checks in production
    for (const [modelId] of this.models.entries()) {
      await this.checkModelHealth(modelId);
    }
  }

  /**
   * Initialize available AI models with their configurations
   */
  private initializeModels(): void {
    const models: ModelConfig[] = [
      {
        id: 'gemini-2.0-flash-exp',
        name: 'Gemini Flash 2.0',
        provider: 'openrouter', // Actually uses Google API directly
        endpoint: 'https://generativelanguage.googleapis.com',
        maxTokens: 4096,
        temperature: 0.2,
        costPerMToken: 0, // Free via Google API
        averageLatency: 2000, // 2 seconds
        reliability: 0.95
      },
      {
        id: 'deepseek/deepseek-chat-v3-0324:free',
        name: 'DeepSeek Chat v3 (Free)',
        provider: 'openrouter',
        endpoint: 'https://openrouter.ai/api/v1/chat/completions',
        maxTokens: 4096,
        temperature: 0.1,
        costPerMToken: 0, // Free tier
        averageLatency: 4000, // 4 seconds (faster than before)
        reliability: 0.90
      },
      {
        id: 'google/gemini-2.0-flash-001',
        name: 'Gemini Flash 2.0 (OpenRouter)',
        provider: 'openrouter',
        endpoint: 'https://openrouter.ai/api/v1/chat/completions',
        maxTokens: 4096,
        temperature: 0.1,
        costPerMToken: 0.05, // $0.05 per million tokens
        averageLatency: 3000, // 3 seconds
        reliability: 0.93
      },
      {
        id: 'anthropic/claude-3-haiku',
        name: 'Claude 3 Haiku',
        provider: 'openrouter',
        endpoint: 'https://openrouter.ai/api/v1/chat/completions',
        maxTokens: 4096,
        temperature: 0.1,
        costPerMToken: 0.25, // $0.25 per million tokens
        averageLatency: 3000, // 3 seconds
        reliability: 0.98
      }
    ];

    models.forEach(model => {
      this.models.set(model.id, model);
    });
  }

  /**
   * Select the optimal model for a given task
   */
  selectModel(task: AITask, options?: {
    preferSpeed?: boolean;
    preferCost?: boolean;
    preferFree?: boolean;
    preferQuality?: boolean;
    maxLatency?: number;
  }): ModelSelection {
    const { preferSpeed, preferCost, preferFree, preferQuality, maxLatency } = options || {};

    // Filter out unhealthy models based on cached health checks
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const availableModels = Array.from(this.models.entries()).filter(([modelId]) => {
      const healthCheck = this.healthCheckCache.get(modelId);
      if (!healthCheck) return true; // If no health check, assume healthy
      return healthCheck.healthy;
    });

    // Default preferences by task
    const taskPreferences = {
      itinerary: { quality: 0.6, speed: 0.3, cost: 0.1 },
      parse: { quality: 0.4, speed: 0.4, cost: 0.2 },
      suggest: { quality: 0.5, speed: 0.4, cost: 0.1 },
      places: { quality: 0.3, speed: 0.6, cost: 0.1 }
    };

    // Handle unknown tasks with default preferences
    const defaultPreferences = { quality: 0.5, speed: 0.3, cost: 0.2 };
    const taskPrefs = taskPreferences[task] || defaultPreferences;

    // Override with user preferences
    let weights = {
      quality: preferQuality ? 0.9 : taskPrefs.quality,
      speed: preferSpeed ? 0.9 : taskPrefs.speed,
      cost: (preferCost || preferFree) ? 0.9 : taskPrefs.cost
    };

    // Normalize weights
    const totalWeight = weights.quality + weights.speed + weights.cost;
    weights = {
      quality: weights.quality / totalWeight,
      speed: weights.speed / totalWeight,
      cost: weights.cost / totalWeight
    };

    let bestModel: ModelConfig | null = null;
    let bestScore = -1;
    let reasoning = '';

    // Special handling for free models with rotation
    if (preferFree || preferCost) {
      const freeModels = Array.from(this.models.values()).filter(m => m.costPerMToken === 0);
      if (freeModels.length > 0) {
        // Use rotation to distribute load among free models
        const rotationKey = `${task}-free`;
        const currentIndex = this.rotationIndex.get(rotationKey) || 0;
        
        bestModel = freeModels[currentIndex % freeModels.length];
        this.rotationIndex.set(rotationKey, (currentIndex + 1) % freeModels.length);
        
        this.trackModelUsage(bestModel.id);
        reasoning = `Rotating free models for quota management (using ${bestModel.name})`;
        
        return {
          modelId: bestModel.id,
          config: bestModel,
          reasoning
        };
      }
    }

    // Standard selection logic
    for (const model of this.models.values()) {
      // Skip models that exceed latency requirement
      if (maxLatency && model.averageLatency && model.averageLatency > maxLatency) {
        continue;
      }

      // Calculate scores (normalize to 0-1)
      const qualityScore = model.reliability || 0.5;
      const speedScore = model.averageLatency ? Math.max(0, 1 - (model.averageLatency / 30000)) : 0.5;
      // Give strong advantage to free models when cost is a factor
      const costScore = model.costPerMToken === 0 ? 1 : Math.max(0.1, 0.5 - (model.costPerMToken || 1) / 6);

      // Weighted total score
      const totalScore = 
        qualityScore * weights.quality +
        speedScore * weights.speed +
        costScore * weights.cost;

      if (totalScore > bestScore) {
        bestScore = totalScore;
        bestModel = model;
        reasoning = this.generateReasoning(model, task, weights, {
          qualityScore,
          speedScore,
          costScore,
          totalScore
        });
      }
    }

    // Fallback to Claude 3 Haiku if no model selected
    if (!bestModel) {
      bestModel = this.models.get('anthropic/claude-3-haiku')!;
      reasoning = 'Fallback to reliable default model';
    }

    return {
      modelId: bestModel.id,
      config: bestModel,
      reasoning
    };
  }

  /**
   * Track model usage for monitoring and quota management
   */
  private trackModelUsage(modelId: string): void {
    const usage = this.modelUsage.get(modelId) || { count: 0, lastReset: new Date() };
    const now = new Date();
    
    // Reset daily counters
    if (now.getDate() !== usage.lastReset.getDate()) {
      usage.count = 0;
      usage.lastReset = now;
    }
    
    usage.count++;
    this.modelUsage.set(modelId, usage);
  }

  /**
   * Get model usage statistics
   */
  getModelUsageStats(): Map<string, { count: number; lastReset: Date }> {
    return new Map(this.modelUsage);
  }

  /**
   * Generate human-readable reasoning for model selection
   */
  private generateReasoning(
    model: ModelConfig, 
    task: AITask, 
    weights: { quality: number; speed: number; cost: number },
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    scores: { qualityScore: number; speedScore: number; costScore: number; totalScore: number }
  ): string {
    const reasons: string[] = [];

    if (weights.speed > 0.5) {
      reasons.push(`fast response time (${model.averageLatency || 'unknown'}ms)`);
    }
    if (weights.cost > 0.5 || model.costPerMToken === 0) {
      reasons.push(model.costPerMToken === 0 ? 'free tier' : 'cost-effective');
    }
    if (weights.quality > 0.5) {
      reasons.push(`high reliability (${Math.round((model.reliability || 0.5) * 100)}%)`);
    }

    const taskContext = {
      itinerary: 'itinerary planning',
      parse: 'parse operations',
      suggest: 'suggestion tasks',
      places: 'place queries'
    };
    
    const context = taskContext[task] || 'general AI tasks';

    const reasonText = reasons.length > 0 ? ` - optimized for ${reasons.join(', ')}` : '';
    return `Selected ${model.name} for ${context}${reasonText}`;
  }

  /**
   * Get model configuration by ID
   */
  getModelConfig(modelId: string): ModelConfig | null {
    return this.models.get(modelId) || null;
  }

  /**
   * Get all available models
   */
  getAllModels(): ModelConfig[] {
    return Array.from(this.models.values());
  }

  /**
   * Health check for model availability
   */
  async checkModelHealth(modelId: string): Promise<boolean> {
    const model = this.models.get(modelId);
    if (!model) return false;

    // Check cache first
    const cached = this.healthCheckCache.get(modelId);
    if (cached && (Date.now() - cached.checkedAt.getTime()) < this.healthCheckCacheTTL) {
      return cached.healthy;
    }

    try {
      // Simple ping to the provider with timeout
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 5000);
      
      const response = await fetch(model.endpoint, {
        method: 'HEAD',
        signal: controller.signal
      });
      
      clearTimeout(timeoutId);
      const isHealthy = response.ok;
      
      // Cache the result
      this.healthCheckCache.set(modelId, {
        healthy: isHealthy,
        checkedAt: new Date()
      });
      
      return isHealthy;
    } catch {
      // Cache the failure
      this.healthCheckCache.set(modelId, {
        healthy: false,
        checkedAt: new Date()
      });
      return false;
    }
  }

  /**
   * Clear health check cache (for testing)
   */
  clearHealthCache(): void {
    this.healthCheckCache.clear();
  }

  /**
   * Get optimal model for specific use cases
   */
  getRecommendedModel(scenario: 'fastest' | 'cheapest' | 'best-quality' | 'balanced'): ModelSelection {
    switch (scenario) {
      case 'fastest':
        return this.selectModel('places', { preferSpeed: true, maxLatency: 5000 });
      case 'cheapest':
        return this.selectModel('parse', { preferCost: true });
      case 'best-quality':
        return this.selectModel('itinerary', { preferQuality: true });
      case 'balanced':
      default:
        return this.selectModel('parse', { /* balanced weights */ });
    }
  }
}

// Export singleton instance
export const aiRouter = AIRouterService.getInstance();