'use client';

import { useEffect, useState } from 'react';
import { useAuthStore } from '@/stores/auth.store';
import { usePathname, useRouter } from 'next/navigation';
import { validateAuthState, setupTokenMonitoring } from '@/lib/auth-validator';

// Public routes that don't require authentication
const publicRoutes = [
  '/',
  '/login',
  '/signup',
  '/forgot-password',
  '/about',
  '/contact',
  '/help',
  '/privacy',
  '/terms',
  '/examples',
  '/templates',
  '/p/', // Public trip pages
];

// Routes that should redirect to dashboard if authenticated
const authRoutes = ['/login', '/signup'];

interface AuthProviderProps {
  children: React.ReactNode;
}

export function AuthProvider({ children }: AuthProviderProps) {
  const pathname = usePathname();
  const router = useRouter();
  const { isAuthenticated, isLoading } = useAuthStore();
  const [isValidating, setIsValidating] = useState(true);

  useEffect(() => {
    // Validate authentication state on mount
    const validateAuth = async () => {
      try {
        await validateAuthState();
      } catch (error) {
        console.error('Auth validation failed:', error);
      } finally {
        setIsValidating(false);
      }
    };

    validateAuth();

    // Set up token monitoring
    const cleanup = setupTokenMonitoring();

    // Cleanup on unmount
    return cleanup;
  }, []);

  useEffect(() => {
    // Handle route protection
    const isPublicRoute = publicRoutes.some(route =>
      pathname === route || pathname.startsWith(route)
    );
    const isAuthRoute = authRoutes.includes(pathname);

    if (!isLoading && !isValidating) {
      if (!isAuthenticated && !isPublicRoute) {
        // Redirect to login if trying to access protected route
        router.push(`/login?redirect=${encodeURIComponent(pathname)}`);
      } else if (isAuthenticated && isAuthRoute) {
        // Redirect to dashboard if already authenticated
        router.push('/dashboard');
      }
    }
  }, [isAuthenticated, isLoading, isValidating, pathname, router]);

  // Show loading state while checking auth or validating tokens
  if ((isLoading || isValidating) && !publicRoutes.some(route => pathname === route || pathname.startsWith(route))) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  return <>{children}</>;
}