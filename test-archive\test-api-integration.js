#!/usr/bin/env node

/**
 * TravelViz API Integration Test Runner
 * 
 * This script runs comprehensive integration tests for the TravelViz API
 * to ensure all endpoints are working correctly before deployment.
 */

const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function logHeader(message) {
  log('\n' + '='.repeat(60), 'cyan');
  log(`  ${message}`, 'bright');
  log('='.repeat(60), 'cyan');
}

function logStep(step, message) {
  log(`\n[${step}] ${message}`, 'blue');
}

function logSuccess(message) {
  log(`✅ ${message}`, 'green');
}

function logError(message) {
  log(`❌ ${message}`, 'red');
}

function logWarning(message) {
  log(`⚠️  ${message}`, 'yellow');
}

async function runCommand(command, args, options = {}) {
  return new Promise((resolve, reject) => {
    const child = spawn(command, args, {
      stdio: 'pipe',
      shell: true,
      ...options
    });

    let stdout = '';
    let stderr = '';

    child.stdout?.on('data', (data) => {
      stdout += data.toString();
    });

    child.stderr?.on('data', (data) => {
      stderr += data.toString();
    });

    child.on('close', (code) => {
      if (code === 0) {
        resolve({ stdout, stderr, code });
      } else {
        reject({ stdout, stderr, code });
      }
    });

    child.on('error', (error) => {
      reject({ error, stdout, stderr });
    });
  });
}

async function checkEnvironment() {
  logStep(1, 'Checking Environment Setup');

  // Check if .env file exists
  const envPath = path.join(process.cwd(), '.env');
  if (!fs.existsSync(envPath)) {
    logWarning('.env file not found - using environment variables');
  } else {
    logSuccess('.env file found');
  }

  // Check required environment variables
  const requiredEnvVars = [
    'SUPABASE_URL',
    'SUPABASE_ANON_KEY',
    'JWT_SECRET'
  ];

  const missingVars = requiredEnvVars.filter(varName => !process.env[varName]);
  
  if (missingVars.length > 0) {
    logError(`Missing required environment variables: ${missingVars.join(', ')}`);
    logError('Please set these variables in your .env file or environment');
    return false;
  }

  logSuccess('All required environment variables are set');
  return true;
}

async function checkDependencies() {
  logStep(2, 'Checking Dependencies');

  try {
    // Check if node_modules exists
    if (!fs.existsSync(path.join(process.cwd(), 'node_modules'))) {
      logError('node_modules not found. Please run: pnpm install');
      return false;
    }

    // Check if shared package is built
    const sharedDistPath = path.join(process.cwd(), 'packages/shared/dist');
    if (!fs.existsSync(sharedDistPath)) {
      logWarning('Shared package not built. Building now...');
      await runCommand('pnpm', ['--filter', '@travelviz/shared', 'build']);
      logSuccess('Shared package built successfully');
    } else {
      logSuccess('Shared package is built');
    }

    return true;
  } catch (error) {
    logError(`Dependency check failed: ${error.message || error}`);
    return false;
  }
}

async function runTypeCheck() {
  logStep(3, 'Running Type Check');

  try {
    await runCommand('pnpm', ['--filter', '@travelviz/hub', 'type-check']);
    logSuccess('Type check passed');
    return true;
  } catch (error) {
    logError('Type check failed');
    console.log(error.stdout);
    console.log(error.stderr);
    return false;
  }
}

async function runLinting() {
  logStep(4, 'Running Linting');

  try {
    await runCommand('pnpm', ['--filter', '@travelviz/hub', 'lint']);
    logSuccess('Linting passed');
    return true;
  } catch (error) {
    logError('Linting failed');
    console.log(error.stdout);
    console.log(error.stderr);
    return false;
  }
}

async function runIntegrationTests() {
  logStep(5, 'Running Integration Tests');

  try {
    const result = await runCommand('pnpm', ['--filter', '@travelviz/hub', 'test', 'src/__tests__/api-integration.test.ts']);
    logSuccess('Integration tests passed');
    console.log(result.stdout);
    return true;
  } catch (error) {
    logError('Integration tests failed');
    console.log(error.stdout);
    console.log(error.stderr);
    return false;
  }
}

async function testSpecificEndpoints() {
  logStep(6, 'Testing Specific API Endpoints');

  const baseUrl = process.env.API_BASE_URL || 'http://localhost:3001';
  
  try {
    // Test health endpoint
    const healthResponse = await fetch(`${baseUrl}/health`);
    if (healthResponse.ok) {
      logSuccess('Health endpoint responding');
    } else {
      logError(`Health endpoint failed: ${healthResponse.status}`);
      return false;
    }

    // Test CORS
    const corsResponse = await fetch(`${baseUrl}/api/v1/trips`, {
      method: 'OPTIONS',
      headers: {
        'Origin': 'http://localhost:3000',
        'Access-Control-Request-Method': 'POST',
        'Access-Control-Request-Headers': 'Content-Type,Authorization'
      }
    });

    if (corsResponse.ok) {
      logSuccess('CORS configuration working');
    } else {
      logWarning('CORS test failed - may need server running');
    }

    return true;
  } catch (error) {
    logWarning(`Endpoint tests skipped - server may not be running: ${error.message}`);
    return true; // Don't fail the entire test suite for this
  }
}

async function generateTestReport() {
  logStep(7, 'Generating Test Report');

  const reportData = {
    timestamp: new Date().toISOString(),
    environment: process.env.NODE_ENV || 'development',
    nodeVersion: process.version,
    testResults: {
      environment: true,
      dependencies: true,
      typeCheck: true,
      linting: true,
      integration: true,
      endpoints: true
    }
  };

  const reportPath = path.join(process.cwd(), 'test-results.json');
  fs.writeFileSync(reportPath, JSON.stringify(reportData, null, 2));
  
  logSuccess(`Test report generated: ${reportPath}`);
  return true;
}

async function main() {
  logHeader('TravelViz API Integration Test Suite');
  
  log('This script will test all major API functionality:', 'bright');
  log('• Authentication & Authorization');
  log('• Trip Management (CRUD)');
  log('• Activity Management');
  log('• AI Import (Text & PDF)');
  log('• Places API (Google Places integration)');
  log('• Rate Limiting & Security');
  log('• Error Handling & CORS');

  const steps = [
    checkEnvironment,
    checkDependencies,
    runTypeCheck,
    runLinting,
    runIntegrationTests,
    testSpecificEndpoints,
    generateTestReport
  ];

  let allPassed = true;

  for (const step of steps) {
    try {
      const result = await step();
      if (!result) {
        allPassed = false;
        break;
      }
    } catch (error) {
      logError(`Step failed: ${error.message || error}`);
      allPassed = false;
      break;
    }
  }

  logHeader('Test Results');

  if (allPassed) {
    logSuccess('🎉 All tests passed! Your API is ready for deployment.');
    log('\nNext steps:', 'bright');
    log('• Deploy to staging environment');
    log('• Run E2E tests with frontend');
    log('• Monitor API performance');
    process.exit(0);
  } else {
    logError('❌ Some tests failed. Please fix the issues before deployment.');
    log('\nTroubleshooting:', 'bright');
    log('• Check environment variables in .env file');
    log('• Ensure all dependencies are installed: pnpm install');
    log('• Make sure Supabase is configured correctly');
    log('• Verify Google Places API key is valid');
    process.exit(1);
  }
}

// Handle process termination
process.on('SIGINT', () => {
  log('\n\nTest interrupted by user', 'yellow');
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  logError(`Unhandled Rejection at: ${promise}, reason: ${reason}`);
  process.exit(1);
});

// Run the main function
main().catch((error) => {
  logError(`Fatal error: ${error.message || error}`);
  process.exit(1);
});