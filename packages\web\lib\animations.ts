import { Variants, AnimationProps } from 'framer-motion';

/**
 * Common animation durations
 */
export const DURATIONS = {
  instant: 0,
  fast: 0.1,
  normal: 0.3,
  slow: 0.5,
  slower: 0.8,
  slowest: 1.2,
} as const;

/**
 * Common easing functions
 */
export const EASINGS = {
  easeIn: [0.4, 0, 1, 1],
  easeOut: [0, 0, 0.2, 1],
  easeInOut: [0.4, 0, 0.2, 1],
  bounce: [0.68, -0.55, 0.265, 1.55],
  spring: { type: 'spring', damping: 15, stiffness: 300 },
} as const;

/**
 * Fade animations
 */
export const fadeIn: Variants = {
  hidden: { opacity: 0 },
  visible: { 
    opacity: 1,
    transition: { duration: DURATIONS.normal, ease: EASINGS.easeOut }
  },
  exit: { 
    opacity: 0,
    transition: { duration: DURATIONS.fast, ease: EASINGS.easeIn }
  },
};

/**
 * Slide animations
 */
export const slideUp: Variants = {
  hidden: { y: 20, opacity: 0 },
  visible: { 
    y: 0, 
    opacity: 1,
    transition: { duration: DURATIONS.normal, ease: EASINGS.easeOut }
  },
  exit: { 
    y: -20, 
    opacity: 0,
    transition: { duration: DURATIONS.fast, ease: EASINGS.easeIn }
  },
};

export const slideDown: Variants = {
  hidden: { y: -20, opacity: 0 },
  visible: { 
    y: 0, 
    opacity: 1,
    transition: { duration: DURATIONS.normal, ease: EASINGS.easeOut }
  },
  exit: { 
    y: 20, 
    opacity: 0,
    transition: { duration: DURATIONS.fast, ease: EASINGS.easeIn }
  },
};

export const slideLeft: Variants = {
  hidden: { x: 20, opacity: 0 },
  visible: { 
    x: 0, 
    opacity: 1,
    transition: { duration: DURATIONS.normal, ease: EASINGS.easeOut }
  },
  exit: { 
    x: -20, 
    opacity: 0,
    transition: { duration: DURATIONS.fast, ease: EASINGS.easeIn }
  },
};

export const slideRight: Variants = {
  hidden: { x: -20, opacity: 0 },
  visible: { 
    x: 0, 
    opacity: 1,
    transition: { duration: DURATIONS.normal, ease: EASINGS.easeOut }
  },
  exit: { 
    x: 20, 
    opacity: 0,
    transition: { duration: DURATIONS.fast, ease: EASINGS.easeIn }
  },
};

/**
 * Scale animations
 */
export const scaleIn: Variants = {
  hidden: { scale: 0.8, opacity: 0 },
  visible: { 
    scale: 1, 
    opacity: 1,
    transition: { duration: DURATIONS.normal, ease: EASINGS.bounce }
  },
  exit: { 
    scale: 0.8, 
    opacity: 0,
    transition: { duration: DURATIONS.fast, ease: EASINGS.easeIn }
  },
};

/**
 * Stagger children animations
 */
export const staggerContainer: Variants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
      delayChildren: 0.2,
    },
  },
};

export const staggerItem: Variants = {
  hidden: { y: 20, opacity: 0 },
  visible: {
    y: 0,
    opacity: 1,
    transition: { duration: DURATIONS.normal, ease: EASINGS.easeOut },
  },
};

/**
 * Shimmer effect for loading states
 */
export const shimmer: AnimationProps['animate'] = {
  backgroundPosition: ['200% 0', '-200% 0'],
  transition: {
    duration: 1.5,
    ease: 'linear',
    repeat: Infinity,
  },
};

/**
 * Sparkle animation for success states
 */
export const sparkle: Variants = {
  initial: { scale: 0, rotate: 0 },
  animate: {
    scale: [0, 1, 0],
    rotate: [0, 180, 360],
    transition: {
      duration: DURATIONS.slow,
      ease: EASINGS.easeOut,
      times: [0, 0.5, 1],
    },
  },
};

/**
 * Confetti particle animation
 */
export const confettiParticle: Variants = {
  initial: { 
    y: 0, 
    x: 0, 
    opacity: 1,
    scale: 0,
    rotate: 0,
  },
  animate: (custom: { delay: number; velocity: { x: number; y: number } }) => ({
    y: custom.velocity.y,
    x: custom.velocity.x,
    opacity: [1, 1, 0],
    scale: [0, 1, 1],
    rotate: 720,
    transition: {
      duration: DURATIONS.slowest,
      delay: custom.delay,
      ease: EASINGS.easeOut,
      opacity: { times: [0, 0.8, 1] },
    },
  }),
};

/**
 * Pulse animation for attention
 */
export const pulse: AnimationProps['animate'] = {
  scale: [1, 1.05, 1],
  transition: {
    duration: DURATIONS.slow,
    repeat: Infinity,
    ease: EASINGS.easeInOut,
  },
};

/**
 * Create custom stagger animation
 */
export function createStagger(
  staggerDelay: number = 0.1,
  childDelay: number = 0
): Variants {
  return {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: staggerDelay,
        delayChildren: childDelay,
      },
    },
  };
}

/**
 * Create custom slide animation
 */
export function createSlide(
  direction: 'up' | 'down' | 'left' | 'right',
  distance: number = 20
): Variants {
  const axis = direction === 'up' || direction === 'down' ? 'y' : 'x';
  const sign = direction === 'up' || direction === 'left' ? 1 : -1;

  return {
    hidden: { 
      [axis]: sign * distance, 
      opacity: 0 
    },
    visible: { 
      [axis]: 0, 
      opacity: 1,
      transition: { duration: DURATIONS.normal, ease: EASINGS.easeOut }
    },
    exit: { 
      [axis]: -sign * distance, 
      opacity: 0,
      transition: { duration: DURATIONS.fast, ease: EASINGS.easeIn }
    },
  };
}

/**
 * Animation presets for common use cases
 */
export const animations = {
  // Page transitions
  pageEnter: slideUp,
  pageExit: fadeIn,
  
  // Modal/dialog
  modalOverlay: fadeIn,
  modalContent: scaleIn,
  
  // Lists
  listContainer: staggerContainer,
  listItem: staggerItem,
  
  // Buttons/interactions
  tap: { scale: 0.95 },
  hover: { scale: 1.05 },
  
  // Loading states
  shimmer,
  pulse,
  
  // Success states
  sparkle,
  confetti: confettiParticle,
  
  // Direct variant exports for component imports
  slideUp,
  slideDown,
  slideLeft,
  slideRight,
  fadeIn,
  scaleIn,
} as const;