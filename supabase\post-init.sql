-- Post-initialization script for Supabase-specific configurations

-- Configure auth settings (run these in Supabase Dashboard SQL Editor)
-- These need to be run separately as they modify auth schema

-- 1. Update auth configuration to auto-confirm emails (for development)
-- In production, remove this and use proper email confirmation
UPDATE auth.config 
SET 
    mailer_autoconfirm = true,
    sms_autoconfirm = true
WHERE id = 1;

-- 2. Set JWT expiry times (optional)
UPDATE auth.config 
SET 
    jwt_exp = 3600, -- 1 hour for access tokens
    refresh_token_exp = 604800 -- 7 days for refresh tokens
WHERE id = 1;

-- 3. Create storage buckets for trip images and attachments
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES 
    ('trip-covers', 'trip-covers', true, 5242880, ARRAY['image/jpeg', 'image/png', 'image/webp']),
    ('activity-attachments', 'activity-attachments', false, 10485760, ARRAY['image/jpeg', 'image/png', 'image/pdf'])
ON CONFLICT (id) DO NOTHING;

-- 4. Storage policies
CREATE POLICY "Users can upload their own trip covers"
ON storage.objects FOR INSERT
WITH CHECK (
    bucket_id = 'trip-covers' 
    AND auth.uid()::text = (storage.foldername(name))[1]
);

CREATE POLICY "Trip covers are publicly viewable"
ON storage.objects FOR SELECT
USING (bucket_id = 'trip-covers');

CREATE POLICY "Users can update their own trip covers"
ON storage.objects FOR UPDATE
USING (
    bucket_id = 'trip-covers' 
    AND auth.uid()::text = (storage.foldername(name))[1]
);

CREATE POLICY "Users can delete their own trip covers"
ON storage.objects FOR DELETE
USING (
    bucket_id = 'trip-covers' 
    AND auth.uid()::text = (storage.foldername(name))[1]
);

-- 5. Create useful database functions
CREATE OR REPLACE FUNCTION search_trips(
    p_user_id UUID,
    p_search_term TEXT DEFAULT NULL
)
RETURNS SETOF trips AS $$
BEGIN
    RETURN QUERY
    SELECT t.*
    FROM trips t
    WHERE t.user_id = p_user_id
    AND t.deleted_at IS NULL
    AND (
        p_search_term IS NULL 
        OR t.title ILIKE '%' || p_search_term || '%'
        OR t.description ILIKE '%' || p_search_term || '%'
        OR t.destination ILIKE '%' || p_search_term || '%'
    )
    ORDER BY t.start_date DESC NULLS LAST, t.created_at DESC;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 6. Create function to get trip with all activities
CREATE OR REPLACE FUNCTION get_trip_with_activities(p_trip_id UUID)
RETURNS TABLE (
    trip_data JSONB,
    activities_data JSONB
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        to_jsonb(t.*) as trip_data,
        COALESCE(
            jsonb_agg(
                to_jsonb(a.*) ORDER BY a.start_time, a.created_at
            ) FILTER (WHERE a.id IS NOT NULL),
            '[]'::jsonb
        ) as activities_data
    FROM trips t
    LEFT JOIN activities a ON t.id = a.trip_id
    WHERE t.id = p_trip_id
    AND t.deleted_at IS NULL
    GROUP BY t.id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 7. Add sample data for testing (optional - remove in production)
-- Uncomment the following if you want to add test data

/*
-- Create a test user (password: TestUser123!)
INSERT INTO auth.users (id, email, encrypted_password, email_confirmed_at, created_at, updated_at)
VALUES (
    'f0e2c4a6-70c8-4b99-9c3a-4f8e5d6c2a1b',
    '<EMAIL>',
    crypt('TestUser123!', gen_salt('bf')),
    NOW(),
    NOW(),
    NOW()
);

-- Create test profile
INSERT INTO profiles (id, email, name)
VALUES (
    'f0e2c4a6-70c8-4b99-9c3a-4f8e5d6c2a1b',
    '<EMAIL>',
    'Test User'
);

-- Create a test trip
INSERT INTO trips (id, user_id, title, description, destination, start_date, end_date, status)
VALUES (
    'd3b07384-d9a0-4c3a-8f0e-3d8d4f8e5a1c',
    'f0e2c4a6-70c8-4b99-9c3a-4f8e5d6c2a1b',
    'Tokyo Adventure',
    'A week exploring the vibrant city of Tokyo',
    'Tokyo, Japan',
    CURRENT_DATE + INTERVAL '30 days',
    CURRENT_DATE + INTERVAL '37 days',
    'planning'
);

-- Add some activities
INSERT INTO activities (trip_id, title, description, type, start_time, location, price, currency)
VALUES 
    (
        'd3b07384-d9a0-4c3a-8f0e-3d8d4f8e5a1c',
        'Flight to Tokyo',
        'Direct flight from SFO to NRT',
        'flight',
        CURRENT_DATE + INTERVAL '30 days' + TIME '10:00:00',
        'San Francisco International Airport',
        850.00,
        'USD'
    ),
    (
        'd3b07384-d9a0-4c3a-8f0e-3d8d4f8e5a1c',
        'Hotel in Shibuya',
        'Modern hotel in the heart of Shibuya',
        'accommodation',
        CURRENT_DATE + INTERVAL '30 days' + TIME '15:00:00',
        'Shibuya, Tokyo',
        150.00,
        'USD'
    ),
    (
        'd3b07384-d9a0-4c3a-8f0e-3d8d4f8e5a1c',
        'Visit Senso-ji Temple',
        'Explore Tokyo''s oldest temple',
        'activity',
        CURRENT_DATE + INTERVAL '31 days' + TIME '09:00:00',
        'Asakusa, Tokyo',
        0.00,
        'USD'
    );
*/

COMMIT;