'use client';

import { useMemo, useCallback, forwardRef, memo } from 'react';
import { VariableSizeList as List } from 'react-window';
import InfiniteLoader from 'react-window-infinite-loader';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { getSafeUrl } from '@travelviz/shared';
import { 
  Calendar, 
  Clock, 
  MapPin, 
  DollarSign, 
  Plane, 
  Hotel, 
  Car, 
  Utensils, 
  Activity as ActivityIcon,
  ShoppingBag,
  MoreHorizontal,
  Link,
  FileText,
  Map,
  Eye,
  Theater
} from 'lucide-react';
import { format, parseISO } from 'date-fns';
import type { Activity } from '@/stores/trip.store';
import { MagicCard, activityGlowColors } from '@/components/magic-ui/magic-card';

interface VirtualizedTimelineProps {
  activities: Activity[];
  height?: number;
  width?: number | string;
}

const activityIcons: Record<string, any> = {
  flight: Plane,
  accommodation: Hotel,
  transport: Car,
  dining: Utensils,
  activity: ActivityIcon,
  shopping: ShoppingBag,
  car_rental: Car,
  tour: Map,
  sightseeing: Eye,
  entertainment: Theater,
  other: MoreHorizontal
};

const activityColors: Record<string, string> = {
  flight: 'bg-blue-500',
  accommodation: 'bg-green-500',
  transport: 'bg-purple-500',
  dining: 'bg-yellow-500',
  activity: 'bg-red-500',
  shopping: 'bg-pink-500',
  car_rental: 'bg-indigo-500',
  tour: 'bg-teal-500',
  sightseeing: 'bg-orange-500',
  entertainment: 'bg-violet-500',
  other: 'bg-gray-500'
};

// Estimated heights for different activity types
const ITEM_SIZE_MAP = {
  basic: 120,
  withDescription: 160,
  withNotes: 200,
  dayHeader: 80,
};

type TimelineItem = 
  | { type: 'header'; data: { dayNumber: number; date: string }; index: number }
  | { type: 'activity'; data: Activity; index: number };

interface ListItemData {
  activities: Activity[];
  items: TimelineItem[];
  formatTime: (datetime: string | undefined) => string | undefined;
  formatDuration: (start: string | undefined, end: string | undefined) => string | undefined;
}

// Memoized activity card component
const ActivityCard = memo(({ activity, formatTime, formatDuration }: {
  activity: Activity;
  formatTime: (datetime: string | undefined) => string | undefined;
  formatDuration: (start: string | undefined, end: string | undefined) => string | undefined;
}) => {
  const Icon = activityIcons[activity.type];
  const colorClass = activityColors[activity.type];
  const time = formatTime(activity.start_time || undefined);
  const duration = formatDuration(activity.start_time || undefined, activity.end_time || undefined);

  return (
    <div className="relative ml-6 border-l-2 border-gray-200 pl-10 pb-6">
      {/* Timeline dot */}
      <div className={`absolute -left-11 w-5 h-5 ${colorClass} rounded-full border-4 border-white shadow-sm`} />
      
      {/* Activity card with Magic Card effect */}
      <MagicCard 
        glowColor={activityGlowColors[activity.type as keyof typeof activityGlowColors] || activityGlowColors.other}
        className="rounded-lg"
      >
        <Card className="p-4 transition-shadow">
        <div className="flex items-start justify-between mb-2">
          <div className="flex items-start space-x-3">
            <div className={`p-2 ${colorClass} rounded-lg`}>
              <Icon className="h-5 w-5 text-white" />
            </div>
            <div className="flex-1">
              <h4 className="font-semibold text-gray-900">{activity.title}</h4>
              {activity.description && (
                <p className="text-sm text-gray-600 mt-1">{activity.description}</p>
              )}
            </div>
          </div>
          <Badge variant="outline" className="ml-2">
            {activity.type}
          </Badge>
        </div>

        {/* Activity details */}
        <div className="ml-11 space-y-1">
          {time && (
            <div className="flex items-center text-sm text-gray-600">
              <Clock className="h-3 w-3 mr-1" />
              {time}
              {duration && <span className="text-gray-400 ml-1">({duration})</span>}
            </div>
          )}

          {activity.location && (
            <div className="flex items-center text-sm text-gray-600">
              <MapPin className="h-3 w-3 mr-1" />
              {activity.location}
            </div>
          )}

          {activity.price && (
            <div className="flex items-center text-sm text-gray-600">
              <DollarSign className="h-3 w-3 mr-1" />
              {activity.price} {activity.currency}
            </div>
          )}

          {activity.booking_reference && (
            <div className="flex items-center text-sm text-gray-600">
              <FileText className="h-3 w-3 mr-1" />
              Booking: {activity.booking_reference}
            </div>
          )}

          {activity.booking_url && getSafeUrl(activity.booking_url) !== '#' && (
            <a
              href={getSafeUrl(activity.booking_url)}
              target="_blank"
              rel="noopener noreferrer"
              className="inline-flex items-center text-sm text-blue-600 hover:text-blue-800 mt-2"
              aria-label={`View booking for ${activity.title}`}
            >
              <Link className="h-3 w-3 mr-1" />
              View Booking
            </a>
          )}
        </div>

        {/* Notes */}
        {activity.notes && (
          <div className="ml-11 mt-3 p-3 bg-gray-50 rounded-lg">
            <p className="text-sm text-gray-700">{activity.notes}</p>
          </div>
        )}
      </Card>
      </MagicCard>
    </div>
  );
});

ActivityCard.displayName = 'ActivityCard';

const Row = memo(({ index, style, data }: {
  index: number;
  style: React.CSSProperties;
  data: ListItemData;
}) => {
  const item = data.items[index];

  if (item.type === 'header') {
    const { dayNumber, date } = item.data;
    return (
      <div style={style} className="flex items-center space-x-4 px-6">
        <div className="flex-shrink-0">
          {dayNumber > 0 ? (
            <div className="w-12 h-12 bg-orange-500 rounded-full flex items-center justify-center text-white font-bold">
              {dayNumber}
            </div>
          ) : (
            <div className="w-12 h-12 bg-gray-400 rounded-full flex items-center justify-center">
              <Calendar className="h-6 w-6 text-white" />
            </div>
          )}
        </div>
        <div>
          <h3 className="text-xl font-bold text-gray-900">
            {dayNumber > 0 ? `Day ${dayNumber}` : 'Unscheduled Activities'}
          </h3>
          <p className="text-gray-600">{date}</p>
        </div>
      </div>
    );
  }

  return (
    <div style={style}>
      <ActivityCard 
        activity={item.data} 
        formatTime={data.formatTime}
        formatDuration={data.formatDuration}
      />
    </div>
  );
});

Row.displayName = 'Row';

export default function VirtualizedTimeline({ 
  activities, 
  height = 600,
  width = '100%' 
}: VirtualizedTimelineProps) {
  const formatTime = useCallback((datetime: string | undefined) => {
    if (!datetime) return undefined;
    try {
      return format(parseISO(datetime), 'h:mm a');
    } catch {
      return undefined;
    }
  }, []);

  const formatDuration = useCallback((start: string | undefined, end: string | undefined) => {
    if (!start || !end) return undefined;
    try {
      const startTime = parseISO(start);
      const endTime = parseISO(end);
      const durationMs = endTime.getTime() - startTime.getTime();
      const hours = Math.floor(durationMs / (1000 * 60 * 60));
      const minutes = Math.floor((durationMs % (1000 * 60 * 60)) / (1000 * 60));
      
      if (hours > 0 && minutes > 0) return `${hours}h ${minutes}m`;
      if (hours > 0) return `${hours}h`;
      if (minutes > 0) return `${minutes}m`;
      return undefined;
    } catch {
      return undefined;
    }
  }, []);

  // Prepare flat list of items (headers + activities)
  const items = useMemo(() => {
    const flatItems: TimelineItem[] = [];
    let currentIndex = 0;

    // Group activities by day first
    const grouped = activities.reduce((acc, activity) => {
      const dateKey = activity.start_time 
        ? format(parseISO(activity.start_time), 'yyyy-MM-dd')
        : 'unscheduled';
      
      if (!acc[dateKey]) {
        acc[dateKey] = [];
      }
      acc[dateKey].push(activity);
      return acc;
    }, {} as Record<string, Activity[]>);

    // Sort dates
    const sortedDates = Object.keys(grouped).sort((a, b) => {
      if (a === 'unscheduled') return 1;
      if (b === 'unscheduled') return -1;
      return a.localeCompare(b);
    });

    let dayNumber = 1;
    sortedDates.forEach(dateKey => {
      const dayActivities = grouped[dateKey];
      const isUnscheduled = dateKey === 'unscheduled';
      
      // Add header
      flatItems.push({
        type: 'header',
        data: {
          dayNumber: isUnscheduled ? 0 : dayNumber++,
          date: isUnscheduled ? 'Unscheduled' : format(parseISO(dateKey), 'EEEE, MMMM d, yyyy')
        },
        index: currentIndex++
      });

      // Add activities
      dayActivities.forEach(activity => {
        flatItems.push({
          type: 'activity',
          data: activity,
          index: currentIndex++
        });
      });
    });

    return flatItems;
  }, [activities]);

  // Calculate item size
  const getItemSize = useCallback((index: number) => {
    const item = items[index];
    if (item.type === 'header') {
      return ITEM_SIZE_MAP.dayHeader;
    }
    
    const activity = item.data;
    if (activity.notes) {
      return ITEM_SIZE_MAP.withNotes;
    }
    if (activity.description) {
      return ITEM_SIZE_MAP.withDescription;
    }
    return ITEM_SIZE_MAP.basic;
  }, [items]);

  const itemData: ListItemData = {
    activities,
    items,
    formatTime,
    formatDuration
  };

  if (activities.length === 0) {
    return (
      <Card className="p-8 text-center">
        <Calendar className="h-12 w-12 text-gray-400 mx-auto mb-4" />
        <h3 className="text-lg font-semibold text-gray-900 mb-2">No Activities Yet</h3>
        <p className="text-gray-600">Start adding activities to see your timeline.</p>
      </Card>
    );
  }

  // For very large lists, use infinite loader
  const itemCount = items.length;
  const isItemLoaded = (index: number) => index < items.length;
  const loadMoreItems = () => Promise.resolve(); // All items are already loaded

  return (
    <div className="w-full" style={{ height, width }}>
      <InfiniteLoader
        isItemLoaded={isItemLoaded}
        itemCount={itemCount}
        loadMoreItems={loadMoreItems}
      >
        {({ onItemsRendered, ref }: { onItemsRendered: any; ref: any }) => (
          <List
            ref={ref}
            height={height}
            itemCount={itemCount}
            itemSize={getItemSize}
            itemData={itemData}
            onItemsRendered={onItemsRendered}
            width={typeof width === 'string' ? width : width || '100%'}
            overscanCount={3}
          >
            {Row}
          </List>
        )}
      </InfiniteLoader>
    </div>
  );
}