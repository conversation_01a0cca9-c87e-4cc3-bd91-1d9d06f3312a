"use client";

import Link from 'next/link';
import { ArrowLeft, Shield, FileText, Users, AlertCircle } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';

export default function TermsOfServicePage() {
  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <Link href="/" className="flex items-center space-x-2">
              <div className="p-2 bg-orange-500 rounded-lg">
                <FileText className="h-5 w-5 text-white" />
              </div>
              <span className="text-xl font-bold text-gray-900">TravelViz</span>
            </Link>
            <Link href="/">
              <Button variant="ghost" className="flex items-center">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Home
              </Button>
            </Link>
          </div>
        </div>
      </div>

      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8 sm:py-12">
        {/* Header Section */}
        <div className="text-center mb-8 sm:mb-12">
          <h1 className="text-3xl sm:text-4xl font-bold text-gray-900 mb-4">
            Terms of Service
          </h1>
          <p className="text-lg sm:text-xl text-gray-600 max-w-3xl mx-auto">
            Please read these terms carefully before using TravelViz
          </p>
          <div className="mt-4 text-sm text-gray-500">
            Last updated: December 2024
          </div>
        </div>

        {/* Quick Navigation */}
        <Card className="p-4 sm:p-6 mb-8">
          <h2 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
            <Shield className="h-5 w-5 text-orange-500 mr-2" />
            Quick Navigation
          </h2>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3">
            {[
              'Acceptance of Terms',
              'Description of Service',
              'User Accounts',
              'Subscription Plans',
              'User Content',
              'Privacy & Data',
              'Prohibited Uses',
              'Intellectual Property',
              'Limitation of Liability'
            ].map((section, index) => (
              <a
                key={index}
                href={`#section-${index + 1}`}
                className="text-sm text-orange-600 hover:text-orange-700 hover:underline transition-colors"
              >
                {section}
              </a>
            ))}
          </div>
        </Card>

        {/* Terms Content */}
        <div className="space-y-8">
          {/* Section 1 */}
          <section id="section-1" className="bg-white rounded-lg p-6 sm:p-8 shadow-sm">
            <h2 className="text-xl sm:text-2xl font-bold text-gray-900 mb-4">
              1. Acceptance of Terms
            </h2>
            <div className="prose prose-gray max-w-none">
              <p className="text-gray-700 leading-relaxed mb-4">
                By accessing and using TravelViz ("the Service"), you accept and agree to be bound by the terms and provision of this agreement. If you do not agree to abide by the above, please do not use this service.
              </p>
              <p className="text-gray-700 leading-relaxed">
                These Terms of Service ("Terms") govern your use of our website located at travelviz.com (the "Service") operated by TravelViz ("us", "we", or "our").
              </p>
            </div>
          </section>

          {/* Section 2 */}
          <section id="section-2" className="bg-white rounded-lg p-6 sm:p-8 shadow-sm">
            <h2 className="text-xl sm:text-2xl font-bold text-gray-900 mb-4">
              2. Description of Service
            </h2>
            <div className="prose prose-gray max-w-none">
              <p className="text-gray-700 leading-relaxed mb-4">
                TravelViz is a web-based travel planning platform that allows users to:
              </p>
              <ul className="list-disc list-inside text-gray-700 space-y-2 mb-4">
                <li>Create and organize travel itineraries</li>
                <li>Import travel plans from AI chatbots and documents</li>
                <li>Visualize trips on interactive maps</li>
                <li>Share travel plans with others</li>
                <li>Export itineraries in various formats</li>
              </ul>
              <p className="text-gray-700 leading-relaxed">
                We reserve the right to modify, suspend, or discontinue the Service at any time without notice.
              </p>
            </div>
          </section>

          {/* Section 3 */}
          <section id="section-3" className="bg-white rounded-lg p-6 sm:p-8 shadow-sm">
            <h2 className="text-xl sm:text-2xl font-bold text-gray-900 mb-4">
              3. User Accounts
            </h2>
            <div className="prose prose-gray max-w-none">
              <p className="text-gray-700 leading-relaxed mb-4">
                To access certain features of the Service, you must register for an account. You agree to:
              </p>
              <ul className="list-disc list-inside text-gray-700 space-y-2 mb-4">
                <li>Provide accurate, current, and complete information</li>
                <li>Maintain and update your account information</li>
                <li>Keep your password secure and confidential</li>
                <li>Accept responsibility for all activities under your account</li>
                <li>Notify us immediately of any unauthorized use</li>
              </ul>
              <p className="text-gray-700 leading-relaxed">
                We reserve the right to suspend or terminate accounts that violate these terms.
              </p>
            </div>
          </section>

          {/* Section 4 */}
          <section id="section-4" className="bg-white rounded-lg p-6 sm:p-8 shadow-sm">
            <h2 className="text-xl sm:text-2xl font-bold text-gray-900 mb-4">
              4. Subscription Plans and Billing
            </h2>
            <div className="prose prose-gray max-w-none">
              <p className="text-gray-700 leading-relaxed mb-4">
                TravelViz offers both free and paid subscription plans:
              </p>
              <div className="bg-gray-50 rounded-lg p-4 mb-4">
                <h4 className="font-semibold text-gray-900 mb-2">Free Plan</h4>
                <ul className="list-disc list-inside text-gray-700 space-y-1 text-sm">
                  <li>3 trips per month</li>
                  <li>10 AI generations per month</li>
                  <li>Watermarked PDF exports</li>
                  <li>Basic support</li>
                </ul>
              </div>
              <div className="bg-orange-50 rounded-lg p-4 mb-4">
                <h4 className="font-semibold text-gray-900 mb-2">Premium Plan ($6.99/month)</h4>
                <ul className="list-disc list-inside text-gray-700 space-y-1 text-sm">
                  <li>Unlimited trips and AI generations</li>
                  <li>Clean PDF exports</li>
                  <li>Priority support</li>
                  <li>Advanced features</li>
                </ul>
              </div>
              <p className="text-gray-700 leading-relaxed">
                Subscriptions automatically renew unless cancelled. You may cancel at any time through your account settings.
              </p>
            </div>
          </section>

          {/* Section 5 */}
          <section id="section-5" className="bg-white rounded-lg p-6 sm:p-8 shadow-sm">
            <h2 className="text-xl sm:text-2xl font-bold text-gray-900 mb-4">
              5. User Content and Data
            </h2>
            <div className="prose prose-gray max-w-none">
              <p className="text-gray-700 leading-relaxed mb-4">
                You retain ownership of all content you create or upload to TravelViz. By using our Service, you grant us a limited license to:
              </p>
              <ul className="list-disc list-inside text-gray-700 space-y-2 mb-4">
                <li>Store and process your travel plans and data</li>
                <li>Display your public itineraries to other users</li>
                <li>Use aggregated, anonymized data for service improvement</li>
              </ul>
              <p className="text-gray-700 leading-relaxed">
                You are responsible for ensuring you have the right to share any content you upload.
              </p>
            </div>
          </section>

          {/* Section 6 */}
          <section id="section-6" className="bg-white rounded-lg p-6 sm:p-8 shadow-sm">
            <h2 className="text-xl sm:text-2xl font-bold text-gray-900 mb-4">
              6. Privacy and Data Protection
            </h2>
            <div className="prose prose-gray max-w-none">
              <p className="text-gray-700 leading-relaxed mb-4">
                Your privacy is important to us. Our Privacy Policy explains how we collect, use, and protect your information. By using TravelViz, you agree to our data practices as described in our Privacy Policy.
              </p>
              <div className="bg-blue-50 rounded-lg p-4">
                <div className="flex items-start">
                  <AlertCircle className="h-5 w-5 text-blue-600 mt-0.5 mr-3 flex-shrink-0" />
                  <div>
                    <p className="text-sm text-blue-800 font-medium">Data Security</p>
                    <p className="text-sm text-blue-700 mt-1">
                      We use industry-standard security measures to protect your data, but no system is 100% secure.
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </section>

          {/* Section 7 */}
          <section id="section-7" className="bg-white rounded-lg p-6 sm:p-8 shadow-sm">
            <h2 className="text-xl sm:text-2xl font-bold text-gray-900 mb-4">
              7. Prohibited Uses
            </h2>
            <div className="prose prose-gray max-w-none">
              <p className="text-gray-700 leading-relaxed mb-4">
                You may not use TravelViz for any unlawful purpose or to solicit others to perform unlawful acts. Prohibited activities include:
              </p>
              <ul className="list-disc list-inside text-gray-700 space-y-2">
                <li>Violating any applicable laws or regulations</li>
                <li>Infringing on intellectual property rights</li>
                <li>Transmitting malicious code or viruses</li>
                <li>Attempting to gain unauthorized access to our systems</li>
                <li>Using the service to spam or harass other users</li>
                <li>Creating fake accounts or impersonating others</li>
              </ul>
            </div>
          </section>

          {/* Section 8 */}
          <section id="section-8" className="bg-white rounded-lg p-6 sm:p-8 shadow-sm">
            <h2 className="text-xl sm:text-2xl font-bold text-gray-900 mb-4">
              8. Intellectual Property
            </h2>
            <div className="prose prose-gray max-w-none">
              <p className="text-gray-700 leading-relaxed mb-4">
                The TravelViz service and its original content, features, and functionality are owned by TravelViz and are protected by international copyright, trademark, patent, trade secret, and other intellectual property laws.
              </p>
              <p className="text-gray-700 leading-relaxed">
                You may not reproduce, distribute, modify, create derivative works of, publicly display, publicly perform, republish, download, store, or transmit any of the material on our service without our prior written consent.
              </p>
            </div>
          </section>

          {/* Section 9 */}
          <section id="section-9" className="bg-white rounded-lg p-6 sm:p-8 shadow-sm">
            <h2 className="text-xl sm:text-2xl font-bold text-gray-900 mb-4">
              9. Limitation of Liability
            </h2>
            <div className="prose prose-gray max-w-none">
              <p className="text-gray-700 leading-relaxed mb-4">
                TravelViz is provided "as is" without warranties of any kind. We do not guarantee that the service will be uninterrupted, secure, or error-free.
              </p>
              <p className="text-gray-700 leading-relaxed mb-4">
                In no event shall TravelViz be liable for any indirect, incidental, special, consequential, or punitive damages, including without limitation, loss of profits, data, use, goodwill, or other intangible losses.
              </p>
              <div className="bg-yellow-50 rounded-lg p-4">
                <div className="flex items-start">
                  <AlertCircle className="h-5 w-5 text-yellow-600 mt-0.5 mr-3 flex-shrink-0" />
                  <div>
                    <p className="text-sm text-yellow-800 font-medium">Travel Disclaimer</p>
                    <p className="text-sm text-yellow-700 mt-1">
                      TravelViz is a planning tool only. We are not responsible for travel arrangements, bookings, or any issues that may arise during your travels.
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </section>

          {/* Contact Section */}
          <section className="bg-white rounded-lg p-6 sm:p-8 shadow-sm">
            <h2 className="text-xl sm:text-2xl font-bold text-gray-900 mb-4">
              Contact Information
            </h2>
            <div className="prose prose-gray max-w-none">
              <p className="text-gray-700 leading-relaxed mb-4">
                If you have any questions about these Terms of Service, please contact us:
              </p>
              <div className="bg-gray-50 rounded-lg p-4">
                <div className="space-y-2 text-sm">
                  <p><strong>Email:</strong> <EMAIL></p>
                  <p><strong>Address:</strong> TravelViz Legal Department</p>
                  <p><strong>Response Time:</strong> We aim to respond within 48 hours</p>
                </div>
              </div>
            </div>
          </section>
        </div>

        {/* Footer Navigation */}
        <div className="mt-12 pt-8 border-t border-gray-200">
          <div className="flex flex-col sm:flex-row items-center justify-between space-y-4 sm:space-y-0">
            <div className="flex items-center space-x-4 text-sm text-gray-600">
              <Link href="/privacy" className="hover:text-orange-600 transition-colors">
                Privacy Policy
              </Link>
              <span>•</span>
              <Link href="/about" className="hover:text-orange-600 transition-colors">
                About Us
              </Link>
              <span>•</span>
              <Link href="/contact" className="hover:text-orange-600 transition-colors">
                Contact
              </Link>
            </div>
            <Link href="/">
              <Button className="btn-primary">
                Start Planning Your Trip
              </Button>
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
}