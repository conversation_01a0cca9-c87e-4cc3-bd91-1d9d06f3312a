import { describe, it, expect, beforeAll } from 'vitest';
import request from 'supertest';
import path from 'path';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config({ path: path.resolve(__dirname, '../../../.env.local') });

console.log('ENV vars loaded:', {
  SUPABASE_URL: !!process.env.SUPABASE_URL,
  SUPABASE_ANON_KEY: !!process.env.SUPABASE_ANON_KEY,
  SUPABASE_SERVICE_ROLE_KEY: !!process.env.SUPABASE_SERVICE_ROLE_KEY,
});

import { getSupabaseClient } from '../../src/lib/supabase';
import { createServer } from '../../src/server';
import { Express } from 'express';
import { logger } from '../../src/utils/logger';

describe('Trip List Test', () => {
  let app: Express;
  let server: any;
  let authToken: string;

  beforeAll(async () => {
    // Enable debug logging
    (logger as any).logLevel = 0; // DEBUG = 0
    
    app = createServer();
    server = app.listen(0);

    // Get auth token
    const supabase = getSupabaseClient();
    const { data, error } = await supabase.auth.signInWithPassword({
      email: '<EMAIL>',
      password: 'Flaremmk123!'
    });

    if (error) {
      console.error('Auth error:', error);
      throw error;
    }

    authToken = data.session?.access_token || '';
    console.log('Got auth token');
  });

  it('should list trips', async () => {
    const response = await request(app)
      .get('/api/v1/trips')
      .set('Authorization', `Bearer ${authToken}`);

    console.log('Response status:', response.status);
    console.log('Response body:', JSON.stringify(response.body, null, 2));

    if (response.status !== 200) {
      // Try direct database query
      const supabase = getSupabaseClient();
      
      // First try a simple select without filters
      const { data: simpleData, error: simpleError } = await supabase
        .from('trips')
        .select('id')
        .limit(1);
      
      console.log('Simple query result:', { simpleData, simpleError });
      
      // Then try with user filter
      const { data, error, count } = await supabase
        .from('trips')
        .select('*', { count: 'exact', head: true })
        .eq('user_id', '697b40b3-42d7-4b32-ad49-0220c2313643');
      
      console.log('Direct query result:', { data, error, count });
    }

    expect(response.status).toBe(200);
  });
});