# Project Phoenix: A Roadmap to a Superior Travel Planning Experience

This document outlines a strategic plan to fundamentally enhance the Travelviz user experience. Our goal is to not only match the functionality of leading competitors like Wanderlog but to exceed them by creating a more intuitive, intelligent, and visually appealing platform.

The current user experience is hindered by three main issues:

1.  **Clunky Itinerary Management:** Lack of drag-and-drop makes reordering a chore.
2.  **Friction in Activity Creation:** The manual-input form is slow and cumbersome compared to competitors who use place-search APIs.
3.  **Confusing AI Implementation:** Exposing the choice of AI model to the user adds unnecessary complexity.

This plan addresses these issues head-on.

---

## Part 1: Foundational UX Enhancements (The Non-Negotiables)

Before adding new features, we must perfect the core experience.

### 1.1. Implement Seamless Drag-and-Drop Itinerary

**Goal:** Allow users to intuitively reorder activities and entire days within the timeline.

**Technical Plan:**

1.  **Library Integration:** Install and configure `@dnd-kit`, a modern and accessible drag-and-drop library for React.
2.  **Timeline Component (`TripTimeline.tsx`):**
    - Wrap the entire timeline view in a `<DndContext>` provider.
    - Create a new `SortableActivityItem` component that wraps the existing activity card and uses the `useSortable` hook from `@dnd-kit/sortable`.
    - Implement the `onDragEnd` event handler. This is the most critical piece:
      - It will detect if an activity has been moved.
      - It will call a new function in `useTripStore` (e.g., `reorderActivities`).
      - This store function will update the state optimistically for a snappy UI response, then make an API call to the backend to persist the new order.
3.  **Visual Feedback:** Provide clear visual cues during the drag operation, such as a "lifting" animation, drop shadows, and a placeholder for where the item will be dropped.

### 1.2. Fix and Supercharge the Map

**Goal:** Transform the map from a static view into a dynamic, interactive planning tool.

**Technical Plan:**

1.  **Immediate Fix:** Ensure the `NEXT_PUBLIC_MAPBOX_ACCESS_TOKEN` is correctly configured in a `.env.local` file to enable the map.
2.  **Real-time Sync:**
    - The map must listen for changes in the `useTripStore`.
    - When activities are reordered via drag-and-drop, the numbered pins on the map must update instantly to reflect the new sequence.
3.  **Draw Travel Paths:**
    - For each day, use the Mapbox API to draw a polyline (a line) connecting the activities in sequence. This will give users a visual representation of their daily route.
4.  **Timeline-to-Map Interaction:**
    - Implement a feature where clicking on an activity card in the timeline view automatically pans and zooms the map to focus on that activity's pin.

---

## Part 2: The "Magic" - AI-Powered Activity Addition

**Goal:** Make adding an activity as simple as typing its name. Eliminate the multi-field form for initial creation.

### 2.1. The "One-Box" Activity Input

**User Flow:**

1.  User clicks a single, prominent "Add Activity" button.
2.  An input field appears. The user starts typing "eiffel tow..."
3.  A dropdown of autocomplete suggestions appears, powered by a places API (e.g., Google Places), showing "Eiffel Tower, Paris, France".
4.  User clicks the suggestion.
5.  The activity is **instantly added** to their itinerary, pre-populated with the correct name, address, coordinates, and a short description.
6.  The user can _then_ click on the newly created activity card to add optional details like notes, budget, or booking confirmation numbers. This inverts the current workflow to be "add first, refine later."

**Technical Plan:**

1.  **Backend API:**
    - Create a new endpoint: `POST /api/activities/add-from-place`.
    - This endpoint will accept a `google_place_id` (or similar identifier).
    - It will then use the Google Places API (server-side) to fetch the full details (name, address, lat/lng, description, photo).
    - It will create a new activity in the database with this information and return the new activity object.
2.  **Frontend Component (`AddActivity.tsx`):**
    - Build a new component that uses an autocomplete library (like `react-select` or `downshift`).
    - This component will call a new backend endpoint (`GET /api/places/autocomplete?query=...`) as the user types.
    - When a user selects a place, the component will call the `POST /api/activities/add-from-place` endpoint with the selected `place_id`.
    - The `useTripStore` will be updated with the returned activity, and the UI will update automatically.
3.  **Configuration:** This will require a Google Maps Platform API key with the "Places API" enabled, stored securely in the backend environment variables.

---

## Part 3: Seamless, Invisible AI

**Goal:** The user should feel the power of AI without ever having to think about it. The AI should be a silent, competent assistant, not a feature to be configured.

### 3.1. Eliminate AI Model Selection

**The Problem:** Asking a user to choose between "Gemini," "Claude," etc., is like a car asking the driver which brand of spark plug to use. It's irrelevant technical detail that causes confusion.

**The Solution:**

1.  **Remove from UI:** Completely remove any dropdowns or settings that let the user pick an AI model.
2.  **Backend Intelligence:** Implement a "router" or "meta-model" on the backend.
    - This router will analyze the user's request (e.g., "create a 5-day trip to Tokyo" vs. "summarize this article: [URL]").
    - Based on the task, it will intelligently delegate the job to the best-suited model (e.g., Gemini for itinerary generation, a different model for summarization). This is an internal optimization, invisible to the user.

### 3.2. A Unified, AI-First Trip Creation Flow

**The Goal:** Simplify the creation of a new trip, whether from a prompt, a link, or a document.

**The New Flow:**

1.  Replace the separate "New Trip" and "Import" flows with a single, prominent "Create New Trip" button.
2.  This opens a clean modal with a single text area and a prompt: **"Describe your dream trip. You can type a prompt ('7 days in Costa Rica'), paste a link, or upload a file."**
3.  **Backend Intent Analysis:**
    - The backend receives this raw input.
    - It first determines the _intent_:
      - Is it a URL? Use a web-scraping tool to fetch the content.
      - Is it a PDF/DOCX? Use a document parser.
      - Is it plain text? Assume it's a prompt.
    - The extracted content is then passed to the AI itinerary generation service.
4.  The user is then taken directly to their newly created, pre-populated itinerary. The magic happens behind the scenes.

---

## Part 4: UI/UX Modernization

**Goal:** Create a visually stunning, modern, and intuitive interface.

**Inspiration:** Wanderlog, but also modern productivity apps like Notion and Linear.

**The Plan:**

1.  **New Layout:** Adopt a three-column layout for the main planning page:
    - **Left Column (Collapsible):** A simple list of the days in the trip (e.g., "Day 1: July 10", "Day 2: July 11"). Clicking a day smoothly scrolls the central timeline to that day.
    - **Center Column:** The main, drag-and-drop enabled `TripTimeline`.
    - **Right Column:** The interactive `TripMap`.
2.  **Visual Polish:**
    - **Reduce Clutter:** Increase whitespace and simplify card layouts to show only the most critical information at a glance. Secondary info can be revealed on click.
    - **Typography:** Adopt a modern, readable font stack and establish a strong visual hierarchy.
    - **Color Palette:** Use color more intentionally. A neutral palette (grays, whites) for the base UI, with the brand's primary color (e.g., orange) used for key actions like "Add Activity" and interactive highlights.
    - **Micro-interactions:** Use subtle `framer-motion` animations for feedback on actions like adding, deleting, and reordering items to make the UI feel alive and responsive.

By executing this plan, Travelviz can transform from a basic tool into a market-leading, AI-powered travel companion that users will love.

---

## Part 5: The Social & Collaborative Layer (Post-MVP)

**Goal:** Transform travel planning from a solo activity into a fun, shared experience, directly addressing the "Group Trip Coordinator" persona.

### 5.1. Real-time Collaborative Editing

- **User Story:** "As a group planner, I want to invite my friends to edit the itinerary with me, so we can build the perfect trip together in real-time."
- **Technical Plan:**
  - Integrate a real-time data synchronization library (e.g., Liveblocks, Supabase Realtime).
  - When one user drags an activity, adds a note, or changes a time, the change is instantly broadcast to all other users viewing the same trip.
  - Display avatars of currently active users in the header to create a sense of shared presence.

### 5.2. In-Line Commenting and Discussions

- **User Story:** "As a travel companion, I want to leave comments on specific activities, so I can ask questions or give feedback without messy email chains."
- **Technical Plan:**
  - Add a "comments" section to each activity card's detailed view.
  - Users can @-mention other trip members to notify them.
  - Implement a notification system (in-app and email) for new comments and replies.

### 5.3. Activity Voting System

- **User Story:** "As a group, we can't decide on a restaurant. I want to create a poll of three options so we can vote and easily make a decision."
- **Technical Plan:**
  - Allow users to add multiple "unconfirmed" activities to a day.
  - Add a "Vote" button that lets trip members upvote their preferred options.
  - The option with the most votes can then be "confirmed" to the main itinerary.

---

## Part 6: The Proactive, Money-Saving Assistant (Post-MVP)

**Goal:** Go beyond planning to actively save users money and time, delighting the "Budget Optimizer" persona.

### 6.1. Automated Price Tracking

- **User Story:** "I've added a flight to my trip. I want the app to automatically tell me when the price drops so I can book at the best time."
- **Technical Plan:**
  - **Backend:** Create a background worker (e.g., using BullMQ) that runs daily.
  - This worker will query the flight/hotel APIs (Duffel, Travelpayouts) for all tracked items in user itineraries.
  - It will store price history in the database to visualize trends.
  - **Frontend:** Display a small sparkline graph on the activity card showing the price history and a "Buy/Wait" recommendation.

### 6.2. Smart Alerts & Insights

- **User Story:** "I want to know more than just the price. Tell me _when_ to book."
- **Technical Plan:**
  - Move beyond simple price drop alerts to predictive insights.
  - The backend can analyze historical price data to generate recommendations like: "Prices for this route typically increase 21 days before departure."
  - Implement a notification system (email, push) to deliver these actionable insights.

### 6.3. Seamless Affiliate Booking (The "$10 Rule")

- **User Story:** "I trust this app to find me the best deals. I want to book directly from the itinerary with one click."
- **Technical Plan:**
  - When a user clicks "Book," the backend fetches prices from multiple sources (direct links and our affiliate partners).
  - It implements the "$10 Rule": If an affiliate link is within $10 of the absolute lowest price, present the affiliate link. Otherwise, present the cheapest non-affiliate link. This builds user trust.

---

## Part 7: The In-Travel, Offline-First Companion (Post-MVP)

**Goal:** Ensure Travelviz is an indispensable tool _during_ the trip, not just for planning. This is our key differentiator.

### 7.1. One-Click Offline Mode

- **User Story:** "I'm about to board my flight to Japan. I want to download my entire trip, including maps, so I can use it without expensive data."
- **Technical Plan:**
  - **Mapbox Offline:** Use the Mapbox GL JS SDK to allow users to download map tiles for specific geographic regions (the cities in their itinerary).
  - **Local Storage:** Use a robust client-side storage solution (like IndexedDB) to save all itinerary data (activities, notes, confirmations) to the user's device.
  - A simple toggle "Make available offline" will trigger the download process with a clear progress indicator.

### 7.2. AI Pocket Concierge

- **User Story:** "I'm in Paris and have a free afternoon. I want to ask the app, 'What's a cool museum near me that's open now?'"
- **Technical Plan:**
  - The app will use the device's GPS to get the user's current location.
  - This location, along with the query, is sent to our AI backend.
  - The AI can use its knowledge base, combined with the context of the user's existing itinerary, to provide a relevant, in-the-moment suggestion.

### 7.3. Centralized Document Hub

- **User Story:** "I want one place to keep my flight confirmation, hotel booking, and tour tickets, so I'm not searching my email at the airport."
- **Technical Plan:**
  - Allow users to upload PDF, PNG, or JPG files to each activity.
  - **AI Enhancement:** Use OCR (Optical Character Recognition) on the backend to automatically parse these documents, extracting key info like booking reference numbers, dates, and times, and adding it to the structured activity data.

By adding these strategic pillars, we move beyond a simple tool and create a true AI-powered travel companion that fulfills the ambitious vision of our product.

---

## Part 8: The Human-Centered UX Design Layer (Cross-Cutting Concern)

**Goal:** To ensure that beyond being functional, the application is intuitive, delightful, and emotionally resonant. This layer is not a phase, but a set of principles that must be woven into every part of the development process.

### 8.1. The First-Time User Experience (FTUE): Onboarding & The "Aha!" Moment

- **Problem:** A new user facing a blank canvas may feel intimidated and unsure where to start.
- **UX Strategy:** Guide the user to their "Aha!" moment (seeing their first beautiful itinerary) within the first 60 seconds.
  - **Interactive Onboarding:** A brief, dismissible tour on the first visit that highlights the 3 core actions: Import, Drag-and-Drop, and Map Interaction.
  - **Inspirational Templates:** Offer a gallery of pre-built, visually stunning trips. This immediately showcases the app's value and allows users to explore features in a populated environment.
  - **Pre-loaded Demo Trip:** For brand new accounts, pre-load a single, editable demo itinerary that they can manipulate and learn from.

### 8.2. Designing for Emotion: States, Feedback, and Delight

- **Problem:** Standard loading spinners and empty states are functional but emotionally barren. Travel planning should be exciting.
- **UX Strategy:** Treat every state as a touchpoint to engage and delight the user.
  - **Engaging Loading States:** Replace generic spinners with dynamic, anticipatory messages when the AI is working (e.g., "Sketching out your days in Tokyo...").
  - **Inspirational Empty States:** An empty timeline should be an invitation, not a void. Use beautiful imagery and encouraging copy (e.g., "Your next adventure begins here. Where are you dreaming of going?").
  - **Celebratory Feedback:** Use micro-interactions and subtle animations (like a confetti burst) to celebrate user accomplishments, such as creating their first trip or completing their bookings.

### 8.3. Information Hierarchy & Progressive Disclosure

- **Problem:** Displaying all activity information at once (title, time, notes, address, booking ref) creates cognitive overload.
- **UX Strategy:** Show only what's necessary. Reveal complexity as the user requests it.
  - **The "Glance" View:** An activity card in the timeline should, by default, only show the most critical info: Title, Time, and Type Icon.
  - **The "Expanded" View:** On click, the card should smoothly expand in-place to reveal details like notes, address, and booking links. This avoids the jarring context switch of a modal.
  - **Visual Prioritization:** Use size, color, and weight to guide the user's eye. The activity title should have the highest visual weight. Secondary details should be lighter and smaller.

### 8.4. The Human-AI Interaction Model

- **Problem:** A completely "invisible" AI is efficient, but can feel like an unfeeling black box. Users build more trust with conversational and collaborative systems.
- **UX Strategy:** Position the AI as a visible, helpful assistant, not just a hidden process.
  - **Proactive AI Suggestions:** Introduce a non-intrusive panel where the AI can offer contextual advice (e.g., "This day looks very busy. Would you like me to find a more relaxed alternative for the afternoon?").
  - **Conversational Refinement:** After generating an itinerary, allow users to make edits via a chat interface. Typing "Remove the museums on Tuesday and find a good coffee shop near my hotel instead" is a far more joyful and powerful interaction than manual clicking and deleting.
