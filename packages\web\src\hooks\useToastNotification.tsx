import { toast as sonnerToast } from 'sonner';

// Wrapper around sonner's toast to match our UI patterns
export function useToastNotification() {
  const toast = sonnerToast;

  return {
    success: (title: string, description?: string) => {
      toast.success(title, {
        description,
        duration: 5000,
      });
    },
    error: (title: string, description?: string) => {
      toast.error(title, {
        description,
        duration: 7000,
      });
    },
    warning: (title: string, description?: string) => {
      toast.warning(title, {
        description,
        duration: 5000,
      });
    },
    info: (title: string, description?: string) => {
      toast.info(title, {
        description,
        duration: 5000,
      });
    },
    loading: (title: string, description?: string) => {
      return toast.loading(title, {
        description,
      });
    },
    promise: <T,>(
      promise: Promise<T>,
      {
        loading,
        success,
        error,
      }: {
        loading: string;
        success: string | ((data: T) => string);
        error: string | ((error: any) => string);
      }
    ) => {
      return toast.promise(promise, {
        loading,
        success,
        error,
      });
    },
  };
}