#!/usr/bin/env tsx
/**
 * Script to verify RLS policies are properly configured
 * This checks the migration files and provides a summary
 */

import fs from 'fs';
import path from 'path';

const migrationsDir = path.join(__dirname, '../supabase/migrations');

interface PolicyCheck {
  table: string;
  policies: string[];
  rlsEnabled: boolean;
}

function analyzeMigration(content: string): PolicyCheck[] {
  const checks: PolicyCheck[] = [];
  const tables = new Set<string>();
  
  // Find all tables with RLS enabled
  const rlsMatches = content.matchAll(/ALTER TABLE\s+public\.(\w+)\s+ENABLE ROW LEVEL SECURITY/gi);
  for (const match of rlsMatches) {
    tables.add(match[1]);
  }
  
  // Find all policies
  const policyMatches = content.matchAll(/CREATE POLICY\s+"([^"]+)"\s+ON\s+public\.(\w+)/gi);
  const policiesByTable: Record<string, string[]> = {};
  
  for (const match of policyMatches) {
    const policyName = match[1];
    const tableName = match[2];
    if (!policiesByTable[tableName]) {
      policiesByTable[tableName] = [];
    }
    policiesByTable[tableName].push(policyName);
  }
  
  // Combine results
  for (const table of tables) {
    checks.push({
      table,
      policies: policiesByTable[table] || [],
      rlsEnabled: true
    });
  }
  
  return checks;
}

function main() {
  console.log('🔍 Verifying Row Level Security (RLS) Configuration\n');
  
  // Check if migrations directory exists
  if (!fs.existsSync(migrationsDir)) {
    console.error('❌ Migrations directory not found:', migrationsDir);
    process.exit(1);
  }
  
  // Read all SQL files
  const sqlFiles = fs.readdirSync(migrationsDir)
    .filter(f => f.endsWith('.sql'))
    .sort();
  
  console.log(`Found ${sqlFiles.length} migration files:\n`);
  
  const allChecks: PolicyCheck[] = [];
  
  for (const file of sqlFiles) {
    console.log(`📄 ${file}`);
    const content = fs.readFileSync(path.join(migrationsDir, file), 'utf8');
    const checks = analyzeMigration(content);
    
    if (checks.length > 0) {
      console.log('  RLS configurations found:');
      checks.forEach(check => {
        console.log(`  - Table: ${check.table}`);
        console.log(`    RLS Enabled: ✅`);
        console.log(`    Policies: ${check.policies.length}`);
        check.policies.forEach(policy => {
          console.log(`      • ${policy}`);
        });
      });
      allChecks.push(...checks);
    }
    console.log('');
  }
  
  // Summary
  console.log('📊 Summary:');
  console.log('─'.repeat(50));
  
  const tables = ['profiles', 'trips', 'activities', 'trip_shares', 'affiliate_clicks'];
  const secureTables = allChecks.filter(c => tables.includes(c.table));
  
  console.log(`Core tables with RLS: ${secureTables.length}/${tables.length}`);
  
  for (const table of tables) {
    const check = allChecks.find(c => c.table === table);
    if (check) {
      console.log(`✅ ${table}: ${check.policies.length} policies`);
    } else {
      console.log(`❌ ${table}: No RLS configured`);
    }
  }
  
  // Check for required policy types
  console.log('\n🔐 Policy Coverage:');
  console.log('─'.repeat(50));
  
  const requiredPolicies = {
    'profiles': ['view own', 'update own'],
    'trips': ['view own', 'view public', 'create own', 'update own', 'delete own'],
    'activities': ['view own', 'view public', 'create own', 'update own', 'delete own'],
    'trip_shares': ['view public', 'create own'],
    'affiliate_clicks': ['create', 'view own']
  };
  
  for (const [table, required] of Object.entries(requiredPolicies)) {
    const check = allChecks.find(c => c.table === table);
    if (check) {
      console.log(`\n${table}:`);
      for (const req of required) {
        const hasPolicy = check.policies.some(p => p.toLowerCase().includes(req));
        console.log(`  ${hasPolicy ? '✅' : '❌'} ${req}`);
      }
    }
  }
  
  console.log('\n✅ RLS verification complete!');
}

main();