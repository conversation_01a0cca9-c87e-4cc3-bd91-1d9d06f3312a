import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { ApiClient } from './api-client';

// Mock the auth store module
vi.mock('@/stores/auth.store', () => ({
  useAuthStore: {
    getState: vi.fn(() => ({
      accessToken: 'test-token',
      refreshSession: vi.fn(),
      clearAuth: vi.fn()
    }))
  }
}));

// Import after mocking
const { useAuthStore } = await import('@/stores/auth.store');

// Mock fetch
global.fetch = vi.fn();

describe('API Client - Circuit Breaker and Cascade Prevention', () => {
  let apiClient: ApiClient;
  let mockFetch: any;

  beforeEach(() => {
    mockFetch = global.fetch as any;
    vi.clearAllMocks();
    
    apiClient = new ApiClient({
      baseURL: 'http://localhost:3001',
      onUnauthorized: vi.fn(),
      onError: vi.fn()
    });
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  describe('Authentication Cascade Prevention', () => {
    it('should prevent infinite retry loops on 401 errors', async () => {
      const authStore = useAuthStore.getState();
      
      // Mock all requests to return 401
      mockFetch.mockResolvedValue({
        ok: false,
        status: 401,
        headers: new Headers({ 'content-type': 'application/json' }),
        json: async () => ({ error: 'Unauthorized' })
      });

      // Mock refresh to also fail
      (authStore.refreshSession as any).mockRejectedValue(new Error('Refresh failed'));

      // First request should trigger refresh attempt
      try {
        await apiClient.get('/api/test');
      } catch (error) {
        expect(error).toBeDefined();
      }

      // Verify only 2 fetch calls were made (1 original + 1 retry)
      expect(mockFetch).toHaveBeenCalledTimes(2);
      
      // Verify refresh was attempted only once
      expect(authStore.refreshSession).toHaveBeenCalledTimes(1);
      
      // Verify auth was cleared
      expect(authStore.clearAuth).toHaveBeenCalledTimes(1);
    });

    it('should handle successful refresh and retry', async () => {
      const authStore = useAuthStore.getState();
      
      // First call returns 401, second call succeeds
      mockFetch
        .mockResolvedValueOnce({
          ok: false,
          status: 401,
          headers: new Headers({ 'content-type': 'application/json' }),
          json: async () => ({ error: 'Unauthorized' })
        })
        .mockResolvedValueOnce({
          ok: true,
          status: 200,
          headers: new Headers({ 'content-type': 'application/json' }),
          json: async () => ({ success: true, data: { test: 'data' } })
        });

      // Mock successful refresh
      (authStore.refreshSession as any).mockResolvedValue(true);

      const result = await apiClient.get('/api/test');
      
      expect(result).toEqual({ test: 'data' });
      expect(mockFetch).toHaveBeenCalledTimes(2);
      expect(authStore.refreshSession).toHaveBeenCalledTimes(1);
      expect(authStore.clearAuth).not.toHaveBeenCalled();
    });

    it('should prevent concurrent refresh attempts', async () => {
      const authStore = useAuthStore.getState();
      
      // Mock all requests to return 401
      mockFetch.mockResolvedValue({
        ok: false,
        status: 401,
        headers: new Headers({ 'content-type': 'application/json' }),
        json: async () => ({ error: 'Unauthorized' })
      });

      // Mock refresh to take some time
      let refreshResolve: any;
      (authStore.refreshSession as any).mockReturnValue(
        new Promise((resolve) => {
          refreshResolve = resolve;
        })
      );

      // Make multiple concurrent requests
      const promise1 = apiClient.get('/api/test1').catch(() => {});
      const promise2 = apiClient.get('/api/test2').catch(() => {});
      const promise3 = apiClient.get('/api/test3').catch(() => {});

      // Wait a bit for all requests to hit the 401
      await new Promise(resolve => setTimeout(resolve, 10));

      // Verify refresh was only called once despite multiple 401s
      expect(authStore.refreshSession).toHaveBeenCalledTimes(1);

      // Resolve the refresh
      refreshResolve();

      // Wait for all promises to complete
      await Promise.all([promise1, promise2, promise3]);
    });
  });

  describe('Circuit Breaker', () => {
    it('should open circuit breaker after 3 failures', async () => {
      // Mock all requests to fail
      mockFetch.mockResolvedValue({
        ok: false,
        status: 500,
        headers: new Headers({ 'content-type': 'application/json' }),
        json: async () => ({ error: 'Server error' })
      });

      // Make 3 failed requests
      for (let i = 0; i < 3; i++) {
        try {
          await apiClient.get('/api/test');
        } catch (error) {
          // Expected to fail
        }
      }

      // Verify circuit breaker state
      const status = apiClient.getCircuitBreakerStatus();
      expect(status.isOpen).toBe(true);
      expect(status.failures).toBe(3);

      // 4th request should fail immediately due to circuit breaker
      try {
        await apiClient.get('/api/test');
      } catch (error: any) {
        expect(error.message).toBe('Service temporarily unavailable');
        expect(error.status).toBe(503);
      }

      // Verify fetch was only called 3 times (circuit breaker prevented 4th)
      expect(mockFetch).toHaveBeenCalledTimes(3);
    });

    it('should reset circuit breaker after successful request', async () => {
      // First, trigger some failures
      mockFetch.mockResolvedValue({
        ok: false,
        status: 500,
        headers: new Headers({ 'content-type': 'application/json' }),
        json: async () => ({ error: 'Server error' })
      });

      // Make 2 failed requests
      for (let i = 0; i < 2; i++) {
        try {
          await apiClient.get('/api/test');
        } catch (error) {
          // Expected to fail
        }
      }

      // Verify failures were recorded
      let status = apiClient.getCircuitBreakerStatus();
      expect(status.failures).toBe(2);

      // Now make a successful request
      mockFetch.mockResolvedValue({
        ok: true,
        status: 200,
        headers: new Headers({ 'content-type': 'application/json' }),
        json: async () => ({ success: true, data: { test: 'data' } })
      });

      const result = await apiClient.get('/api/test');
      expect(result).toEqual({ test: 'data' });

      // Verify circuit breaker was reset
      status = apiClient.getCircuitBreakerStatus();
      expect(status.failures).toBe(0);
      expect(status.isOpen).toBe(false);
    });

    it('should auto-reset circuit breaker after 60 seconds', async () => {
      // Open the circuit breaker
      mockFetch.mockResolvedValue({
        ok: false,
        status: 500,
        headers: new Headers({ 'content-type': 'application/json' }),
        json: async () => ({ error: 'Server error' })
      });

      for (let i = 0; i < 3; i++) {
        try {
          await apiClient.get('/api/test');
        } catch (error) {
          // Expected
        }
      }

      expect(apiClient.getCircuitBreakerStatus().isOpen).toBe(true);

      // Mock time passing (60+ seconds)
      const originalDateNow = Date.now;
      Date.now = vi.fn(() => originalDateNow() + 65000);

      // Circuit breaker should be closed now
      mockFetch.mockResolvedValue({
        ok: true,
        status: 200,
        headers: new Headers({ 'content-type': 'application/json' }),
        json: async () => ({ success: true, data: { test: 'data' } })
      });

      const result = await apiClient.get('/api/test');
      expect(result).toEqual({ test: 'data' });

      // Restore Date.now
      Date.now = originalDateNow;
    });
  });

  describe('Rate Limiting', () => {
    it('should handle 429 rate limiting gracefully', async () => {
      const onError = vi.fn();
      apiClient = new ApiClient({
        baseURL: 'http://localhost:3001',
        onError
      });

      // Mock rate limit response
      mockFetch.mockResolvedValue({
        ok: false,
        status: 429,
        headers: new Headers({ 
          'content-type': 'application/json',
          'Retry-After': '60'
        }),
        json: async () => ({ error: 'Rate limit exceeded' })
      });

      try {
        await apiClient.get('/api/test');
      } catch (error: any) {
        expect(error.message).toContain('Rate limit exceeded');
        expect(error.message).toContain('60 seconds');
        expect(error.status).toBe(429);
      }

      // Verify error handler was called
      expect(onError).toHaveBeenCalledWith(
        expect.objectContaining({
          status: 429,
          message: expect.stringContaining('Rate limit exceeded')
        })
      );

      // Verify circuit breaker state was updated
      const status = apiClient.getCircuitBreakerStatus();
      expect(status.failures).toBe(1);
    });
  });

  describe('Exponential Backoff', () => {
    it('should apply exponential backoff on retries', async () => {
      const authStore = useAuthStore.getState();
      
      // Mock successful refresh
      (authStore.refreshSession as any).mockResolvedValue(true);

      // First call returns 401, second call succeeds
      mockFetch
        .mockResolvedValueOnce({
          ok: false,
          status: 401,
          headers: new Headers({ 'content-type': 'application/json' }),
          json: async () => ({ error: 'Unauthorized' })
        })
        .mockResolvedValueOnce({
          ok: true,
          status: 200,
          headers: new Headers({ 'content-type': 'application/json' }),
          json: async () => ({ success: true, data: { test: 'data' } })
        });

      const startTime = Date.now();
      const result = await apiClient.get('/api/test');
      const endTime = Date.now();

      expect(result).toEqual({ test: 'data' });
      
      // Verify retry was delayed (at least 1000ms for first retry)
      expect(endTime - startTime).toBeGreaterThanOrEqual(1000);
    });

    it('should not retry more than maxRetries', async () => {
      const authStore = useAuthStore.getState();
      
      // Mock all requests to return 401
      mockFetch.mockResolvedValue({
        ok: false,
        status: 401,
        headers: new Headers({ 'content-type': 'application/json' }),
        json: async () => ({ error: 'Unauthorized' })
      });

      // Mock refresh to succeed but subsequent requests still fail
      (authStore.refreshSession as any).mockResolvedValue(true);

      try {
        await apiClient.get('/api/test');
      } catch (error: any) {
        expect(error.status).toBe(401);
      }

      // maxRetries is 1, so: original request + 1 retry = 2 total
      expect(mockFetch).toHaveBeenCalledTimes(2);
    });
  });

  describe('Error Handling', () => {
    it('should handle network errors', async () => {
      const onError = vi.fn();
      apiClient = new ApiClient({
        baseURL: 'http://localhost:3001',
        onError
      });

      // Mock network failure
      mockFetch.mockRejectedValue(new TypeError('Failed to fetch'));

      try {
        await apiClient.get('/api/test');
      } catch (error: any) {
        expect(error.message).toBe('Network error');
        expect(error.status).toBe(0);
      }

      expect(onError).toHaveBeenCalledWith(
        expect.objectContaining({
          status: 0,
          message: 'Network error'
        })
      );

      // Should update circuit breaker
      const status = apiClient.getCircuitBreakerStatus();
      expect(status.failures).toBe(1);
    });

    it('should handle timeout errors', async () => {
      const onError = vi.fn();
      apiClient = new ApiClient({
        baseURL: 'http://localhost:3001',
        timeout: 100,
        onError
      });

      // Mock slow response
      mockFetch.mockImplementation(() => 
        new Promise((resolve) => setTimeout(resolve, 200))
      );

      try {
        await apiClient.get('/api/test');
      } catch (error: any) {
        expect(error.message).toBe('Request timeout');
        expect(error.status).toBe(408);
      }

      expect(onError).toHaveBeenCalledWith(
        expect.objectContaining({
          status: 408,
          message: 'Request timeout'
        })
      );
    });
  });

  describe('HTTP Methods', () => {
    beforeEach(() => {
      mockFetch.mockResolvedValue({
        ok: true,
        status: 200,
        headers: new Headers({ 'content-type': 'application/json' }),
        json: async () => ({ success: true, data: { result: 'success' } })
      });
    });

    it('should support GET requests', async () => {
      const result = await apiClient.get('/api/test');
      
      expect(mockFetch).toHaveBeenCalledWith(
        'http://localhost:3001/api/test',
        expect.objectContaining({
          method: 'GET',
          headers: expect.objectContaining({
            'Authorization': 'Bearer test-token'
          })
        })
      );
      
      expect(result).toEqual({ result: 'success' });
    });

    it('should support POST requests', async () => {
      const data = { name: 'Test' };
      const result = await apiClient.post('/api/test', data);
      
      expect(mockFetch).toHaveBeenCalledWith(
        'http://localhost:3001/api/test',
        expect.objectContaining({
          method: 'POST',
          body: JSON.stringify(data),
          headers: expect.objectContaining({
            'Content-Type': 'application/json',
            'Authorization': 'Bearer test-token'
          })
        })
      );
      
      expect(result).toEqual({ result: 'success' });
    });

    it('should support file uploads', async () => {
      const formData = new FormData();
      formData.append('file', new Blob(['test']), 'test.txt');
      
      const result = await apiClient.upload('/api/upload', formData);
      
      expect(mockFetch).toHaveBeenCalledWith(
        'http://localhost:3001/api/upload',
        expect.objectContaining({
          method: 'POST',
          body: formData,
          headers: expect.objectContaining({
            'Authorization': 'Bearer test-token'
          })
        })
      );
      
      expect(result).toEqual({ result: 'success' });
    });
  });
});